# Work Order Automation - Sales Orders & Invoices

This document explains the automatic sales order and invoice creation feature when work orders are completed.

## Overview

When a work order is marked as "completed", the system will automatically:
1. **Create a Sales Order** containing all materials and labor from the work order
2. **Create an Invoice** from the sales order

This automation ensures that completed work is properly tracked for billing and revenue purposes.

## Features

### Automatic Sales Order Creation
- **Triggered when**: Work order status changes to "completed"
- **Contains**: All consumed materials and completed operations
- **Pricing**: Uses item cost prices for materials and default labor rates for operations
- **Customer**: Inherits from the work order
- **Service details**: Vehicle, technician, completion date, etc.

### Automatic Invoice Creation
- **Triggered when**: Sales order is created or confirmed
- **Status**: Automatically set to "issued"
- **Payment terms**: 30 days by default
- **Line items**: Mirror the sales order items

### Material Handling
- Only **consumed materials** are included in sales orders
- Pricing uses the item's `cost_price` field
- Quantity matches what was consumed in the work order

### Labor Handling
- Only **completed operations** are included in sales orders
- Creates or uses a generic "Labor - General Service" item
- Duration converted from minutes to hours
- Default labor rate: $75/hour (configurable in the Labor item)

## Configuration

### Environment Variables
Add these to your `.env` file to control the automation:

```bash
# Enable/disable automatic sales order creation (default: true)
WORK_ORDER_AUTO_CREATE_SALES_ORDER=true

# Enable/disable automatic invoice creation (default: true)
SALES_ORDER_AUTO_CREATE_INVOICE=true
```

### Disable Automation
To disable the automation, set the environment variables to `false`:

```bash
WORK_ORDER_AUTO_CREATE_SALES_ORDER=false
SALES_ORDER_AUTO_CREATE_INVOICE=false
```

## Processing Existing Work Orders

For work orders completed before this feature was enabled, use the management command:

### View what would be processed (dry run):
```bash
python manage.py create_sales_orders_from_completed_work_orders --dry-run
```

### Process all eligible work orders:
```bash
python manage.py create_sales_orders_from_completed_work_orders
```

### Process with limit:
```bash
python manage.py create_sales_orders_from_completed_work_orders --limit 50
```

### Process specific work order:
```bash
python manage.py create_sales_orders_from_completed_work_orders --work-order-number WO-12345
```

### Force processing (even if sales order exists):
```bash
python manage.py create_sales_orders_from_completed_work_orders --force
```

## Requirements for Automatic Processing

For a work order to be automatically processed, it must have:
- ✅ Status = "completed"
- ✅ Customer assigned
- ✅ At least one consumed material OR completed operation

If any of these requirements are missing, the work order will be skipped with a warning in the logs.

## Generated Numbers

### Sales Order Numbers
Format: `SO-{timestamp}-{random}`
Example: `SO-123456-7890`

### Invoice Numbers
Auto-generated by the billing system using existing logic.

## Logging

All automation activities are logged with INFO level:
- Work order completion triggers
- Sales order creation success/failure
- Invoice creation success/failure
- Configuration checks

Error conditions are logged with ERROR level:
- Missing customer
- Processing failures
- Database errors

## Workflow History

The work order history system tracks automation activities:
- Sales order creation
- Invoice creation
- Processing failures

View these in the Django admin under Work Order History.

## Data Relationships

```
WorkOrder (completed)
    ↓ (automatic)
SalesOrder
    ↓ (automatic)
Invoice
```

The relationships are maintained through foreign keys:
- `SalesOrder.work_order` → `WorkOrder`
- `Invoice.work_order` → `WorkOrder`
- `Invoice.sales_order` → `SalesOrder`

## Customization

### Labor Rates
Edit the "Labor - General Service" item in the inventory to change:
- Default hourly rate (`selling_price`)
- Cost rate (`cost_price`)
- Description

### Material Pricing
Material prices are taken from the inventory item's `cost_price` field.
To add markup, you can modify the `_create_sales_items_from_materials` method in `work_orders/services.py`.

### Invoice Terms
Default payment terms are 30 days. Modify in the `create_invoice_from_sales_order` method in `work_orders/services.py`.

## Troubleshooting

### Work Order Not Processing
1. Check if automation is enabled in settings
2. Verify work order has customer assigned
3. Check if work order has consumed materials or completed operations
4. Review logs for specific error messages

### Sales Order Not Creating Invoice
1. Check if invoice automation is enabled
2. Verify sales order status is 'confirmed', 'shipped', or 'delivered'
3. Check if invoice already exists

### Command Line Tool Issues
```bash
# Check what work orders would be processed
python manage.py create_sales_orders_from_completed_work_orders --dry-run

# Check logs for detailed error messages
tail -f /var/log/django.log
```

## Database Impact

The automation creates:
- 1 sales order per completed work order
- 1+ sales order items (for materials and operations)
- 1 invoice per sales order
- 1+ invoice items (mirroring sales order items)
- Work order history entries

Performance impact is minimal as processing happens individually per work order completion.

## Support

For issues or questions about this feature:
1. Check the logs first
2. Use the dry-run command to test
3. Review the work order requirements
4. Check configuration settings 