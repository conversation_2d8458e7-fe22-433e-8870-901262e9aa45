from rest_framework import serializers
from inventory.models import Item, Movement, ItemDocument
from reports.models import Report, ReportExecution, Dashboard, DashboardWidget


class ItemDocumentSerializer(serializers.ModelSerializer):
    """
    Serializer for item documents
    """
    file_url = serializers.SerializerMethodField()
    file_size_kb = serializers.SerializerMethodField()
    file_type = serializers.SerializerMethodField()
    
    class Meta:
        model = ItemDocument
        fields = ['id', 'title', 'document_type', 'description', 'is_public', 
                 'file', 'file_url', 'file_size_kb', 'file_type', 'created_at', 'item']
        read_only_fields = ['file_url', 'file_size_kb', 'file_type']
    
    def get_file_url(self, obj):
        request = self.context.get('request')
        if obj.file and request:
            return request.build_absolute_uri(obj.file.url)
        return None
    
    def get_file_size_kb(self, obj):
        if obj.file and hasattr(obj.file, 'size'):
            return round(obj.file.size / 1024, 1)
        return 0
    
    def get_file_type(self, obj):
        return obj.file_extension()


class ItemSerializer(serializers.ModelSerializer):
    """
    Serializer for inventory items with their attributes
    """
    is_low_stock = serializers.BooleanField(read_only=True)
    documents = ItemDocumentSerializer(many=True, read_only=True)
    
    class Meta:
        model = Item
        fields = '__all__'


class MovementSerializer(serializers.ModelSerializer):
    """
    Serializer for inventory stock movements
    """
    item_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Movement
        fields = '__all__'
    
    def get_item_name(self, obj):
        return obj.item.name if obj.item else None


# Report serializers
class ReportSerializer(serializers.ModelSerializer):
    """
    Serializer for reports
    """
    class Meta:
        model = Report
        fields = ['id', 'name', 'description', 'report_type', 'parameters', 'is_scheduled', 'schedule', 'created_at']


class ReportExecutionSerializer(serializers.ModelSerializer):
    """
    Serializer for report executions
    """
    report_name = serializers.SerializerMethodField()
    duration_seconds = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportExecution
        fields = ['id', 'report', 'report_name', 'parameters', 'status', 'start_time', 
                 'end_time', 'duration_seconds', 'result_file', 'error_message', 'created_at']
    
    def get_report_name(self, obj):
        return obj.report.name if obj.report else None
    
    def get_duration_seconds(self, obj):
        return obj.duration


class DashboardWidgetSerializer(serializers.ModelSerializer):
    """
    Serializer for dashboard widgets
    """
    report_name = serializers.SerializerMethodField()
    
    class Meta:
        model = DashboardWidget
        fields = ['id', 'dashboard', 'title', 'widget_type', 'config', 
                 'position', 'report', 'report_name']
    
    def get_report_name(self, obj):
        return obj.report.name if obj.report else None


class DashboardSerializer(serializers.ModelSerializer):
    """
    Serializer for dashboards
    """
    widgets = DashboardWidgetSerializer(many=True, read_only=True)
    
    class Meta:
        model = Dashboard
        fields = ['id', 'name', 'description', 'layout', 'is_default', 'widgets', 'created_at']
