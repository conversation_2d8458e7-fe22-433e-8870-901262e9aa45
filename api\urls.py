from django.urls import path, include
from rest_framework import routers
from rest_framework.authtoken.views import obtain_auth_token
from django.views.decorators.csrf import ensure_csrf_cookie
from django.http import JsonResponse
from django.middleware.csrf import get_token

from project.settings import DEBUG
from .views import (
    ItemViewSet, MovementViewSet, ItemDocumentViewSet,
    ReportViewSet, ReportExecutionViewSet, 
    DashboardViewSet, DashboardWidgetViewSet,
    # Dynamic pricing and inventory integration API endpoints
    PricingAPIViewSet, InventoryAPIViewSet, WorkOrderIntegrationAPIViewSet,
    # New supply chain API endpoints
    items_api, item_detail_api, movements_api, stock_adjustment_api,
    locations_api, location_detail_api, item_locations_api, transfers_api, pending_transfers_api,
    suppliers_api, purchase_orders_api, pending_orders_api, low_stock_items_api,
    supply_chain_stats_api,
    # Batch tracking API endpoints
    batches_api, batch_detail_api, batch_movement_api, expiring_batches_api,
    batch_allocations_api
)
from work_orders.api_spare_parts import api_get_spare_parts

router = routers.DefaultRouter()
router.register(r'items', ItemViewSet)
router.register(r'movements', MovementViewSet)
router.register(r'documents', ItemDocumentViewSet)
router.register(r'reports', ReportViewSet)
router.register(r'executions', ReportExecutionViewSet)
router.register(r'dashboards', DashboardViewSet)
router.register(r'widgets', DashboardWidgetViewSet)
# Dynamic pricing and inventory integration endpoints
router.register(r'pricing', PricingAPIViewSet, basename='pricing')
router.register(r'inventory', InventoryAPIViewSet, basename='inventory')
router.register(r'work-orders', WorkOrderIntegrationAPIViewSet, basename='work-orders')

@ensure_csrf_cookie
def csrf_token_view(request):
    """Endpoint to get CSRF token"""
    return JsonResponse({
        'csrfToken': get_token(request),
        'success': True
    })

urlpatterns = [
    path('v1/', include(router.urls)),
    path('token/', obtain_auth_token, name='api_token_auth'),
    path('work-orders/spare-parts/', api_get_spare_parts, name='api_get_spare_parts'),
    
    # Supply Chain API endpoints
    path('supply-chain/items/', items_api, name='api_items'),
    path('supply-chain/items/<uuid:item_id>/', item_detail_api, name='api_item_detail'),
    path('supply-chain/movements/', movements_api, name='api_movements'),
    path('supply-chain/stock-adjustment/', stock_adjustment_api, name='api_stock_adjustment'),
    path('supply-chain/locations/', locations_api, name='api_locations'),
    path('supply-chain/locations/<str:location_id>/', location_detail_api, name='api_location_detail'),
    path('supply-chain/item-locations/', item_locations_api, name='api_item_locations'),
    path('supply-chain/transfers/', transfers_api, name='api_transfers'),
    path('supply-chain/transfers/pending/', pending_transfers_api, name='api_pending_transfers'),
    path('supply-chain/suppliers/', suppliers_api, name='api_suppliers'),
    path('supply-chain/purchase-orders/', purchase_orders_api, name='api_purchase_orders'),
    path('supply-chain/purchase-orders/pending/', pending_orders_api, name='api_pending_orders'),
    path('supply-chain/low-stock/', low_stock_items_api, name='api_low_stock_items'),
    path('supply-chain/stats/', supply_chain_stats_api, name='api_supply_chain_stats'),
    
    # Batch tracking endpoints
    path('supply-chain/batches/', batches_api, name='api_batches'),
    path('supply-chain/batches/<uuid:batch_id>/', batch_detail_api, name='api_batch_detail'),
    path('supply-chain/batches/movements/', batch_movement_api, name='api_batch_movement'),
    path('supply-chain/batches/expiring/', expiring_batches_api, name='api_expiring_batches'),
    path('supply-chain/batches/allocations/', batch_allocations_api, name='api_batch_allocations'),
    
    # CSRF token endpoint
    path('csrf-token/', csrf_token_view, name='csrf_token'),
]

if DEBUG:
    urlpatterns += [path('api-auth/', include('rest_framework.urls'))]