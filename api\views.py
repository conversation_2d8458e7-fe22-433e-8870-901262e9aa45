from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from waffle.decorators import waffle_flag
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from rest_framework.parsers import <PERSON><PERSON>art<PERSON><PERSON><PERSON>, FormParser
from django.db.models import Q, Sum, Count, F
from django.utils import timezone
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
import json
from decimal import Decimal

from inventory.models import Item, Movement, ItemDocument, ItemBatch, BatchMovement
from warehouse.models import Location, TransferOrder, TransferOrderItem, LocationType, BinLocation, ItemLocation
from purchases.models import Supplier, PurchaseOrder, PurchaseOrderItem, PurchaseReceipt
from reports.models import Report, ReportExecution, Dashboard, DashboardWidget
from reports.services import execute_report, get_dashboard_data
from .serializers import (
    ItemSerializer, MovementSerializer, ItemDocumentSerializer,
    ReportSerializer, ReportExecutionSerializer, 
    DashboardSerializer, DashboardWidgetSerializer
)
from core.middleware import get_current_tenant_id
from inventory.services import DynamicPricingEngine, InventoryIntegrationService, AutomatedPartsRequestService, StockLevelOptimizationService
from work_orders.services import WorkOrderPricingService


class WaffleFlagProtectedViewSet(viewsets.ModelViewSet):
    """Base ViewSet that requires the 'api' waffle flag to be active"""
    
    @method_decorator(waffle_flag('api', redirect_to=None))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)


class ItemViewSet(WaffleFlagProtectedViewSet):
    """API endpoint for inventory items"""
    queryset = Item.objects.all()
    serializer_class = ItemSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['name', 'sku']
    search_fields = ['name', 'sku', 'description']

    @action(detail=True, methods=['get'])
    def movements(self, request, pk=None):
        """Get all movements for a specific item"""
        item = self.get_object()
        movements = Movement.objects.filter(item=item)
        serializer = MovementSerializer(movements, many=True)
        return Response(serializer.data)
        
    @action(detail=True, methods=['get'])
    def documents(self, request, pk=None):
        """Get all documents for a specific item"""
        item = self.get_object()
        
        # By default, only show public documents to regular users
        # Allow admins to see all documents
        if not request.user.is_staff:
            documents = item.documents.filter(is_public=True)
        else:
            documents = item.documents.all()
            
        serializer = ItemDocumentSerializer(documents, many=True, context={'request': request})
        return Response(serializer.data)


class MovementViewSet(WaffleFlagProtectedViewSet):
    """API endpoint for inventory movements"""
    queryset = Movement.objects.all()
    serializer_class = MovementSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['item', 'movement_type', 'created_at']


class ItemDocumentViewSet(WaffleFlagProtectedViewSet):
    """API endpoint for item documents"""
    queryset = ItemDocument.objects.all()
    serializer_class = ItemDocumentSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['item', 'document_type', 'is_public']
    parser_classes = [MultiPartParser, FormParser]
    
    def get_queryset(self):
        """
        Filter queryset based on user permissions.
        Staff users can see all documents, regular users only see public documents.
        """
        queryset = super().get_queryset()
        
        # Non-staff users can only see public documents
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_public=True)
            
        return queryset
    
    def get_serializer_context(self):
        """
        Add request to serializer context to generate absolute URLs
        """
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class AdvancedReportingViewSet(WaffleFlagProtectedViewSet):
    """Base ViewSet that requires both 'api' and 'advanced_reporting' waffle flags"""
    
    @method_decorator(waffle_flag('advanced_reporting', redirect_to=None))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)


class ReportViewSet(AdvancedReportingViewSet):
    """API endpoint for reports"""
    queryset = Report.objects.all()
    serializer_class = ReportSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['report_type', 'is_scheduled']
    search_fields = ['name', 'description']
    
    def get_queryset(self):
        """Filter reports by tenant"""
        return Report.objects.filter(tenant_id=self.request.tenant_id)
    
    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute a report with provided parameters"""
        report = self.get_object()
        
        try:
            # Get parameters from request data
            parameters = request.data.get('parameters', {})
            
            # Execute report
            execution, data = execute_report(
                report_id=report.id,
                parameters=parameters,
                tenant_id=request.tenant_id,
                user=request.user
            )
            
            # Return report data
            return Response({
                'execution_id': str(execution.id),
                'status': execution.status,
                'data': data
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class ReportExecutionViewSet(AdvancedReportingViewSet):
    """API endpoint for report executions"""
    queryset = ReportExecution.objects.all()
    serializer_class = ReportExecutionSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['report', 'status', 'created_at']
    
    def get_queryset(self):
        """Filter executions by tenant"""
        return ReportExecution.objects.filter(tenant_id=self.request.tenant_id)


class DashboardViewSet(AdvancedReportingViewSet):
    """API endpoint for dashboards"""
    queryset = Dashboard.objects.all()
    serializer_class = DashboardSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['is_default']
    search_fields = ['name', 'description']
    
    def get_queryset(self):
        """Filter dashboards by tenant"""
        return Dashboard.objects.filter(tenant_id=self.request.tenant_id)
    
    @action(detail=True, methods=['get'])
    def data(self, request, pk=None):
        """Get dashboard data with widget data"""
        dashboard = self.get_object()
        
        try:
            # Get dashboard data
            dashboard_data = get_dashboard_data(
                dashboard_id=dashboard.id,
                tenant_id=request.tenant_id
            )
            
            return Response(dashboard_data)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class DashboardWidgetViewSet(AdvancedReportingViewSet):
    """API endpoint for dashboard widgets"""
    queryset = DashboardWidget.objects.all()
    serializer_class = DashboardWidgetSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['dashboard', 'widget_type']
    
    def get_queryset(self):
        """Filter widgets by tenant through their dashboard"""
        return DashboardWidget.objects.filter(dashboard__tenant_id=self.request.tenant_id)
    
    @action(detail=True, methods=['get'])
    def data(self, request, pk=None):
        """Get widget data"""
        widget = self.get_object()
        
        try:
            # Execute the report for this widget
            if widget.report:
                params = widget.config.get('parameters', {})
                _, data = execute_report(
                    widget.report.id, 
                    parameters=params,
                    tenant_id=request.tenant_id
                )
                
                return Response({
                    'widget_id': str(widget.id),
                    'data': data
                })
            else:
                return Response({
                    'widget_id': str(widget.id),
                    'data': None,
                    'error': 'No report associated with this widget'
                }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'widget_id': str(widget.id),
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


# ============= SUPPLY CHAIN API ENDPOINTS =============

@api_view(['GET', 'POST'])
@permission_classes([])  # Override default DRF permissions
def items_api(request):
    """API for managing inventory items"""
    # Check if user is authenticated for session-based auth
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    tenant_id = get_current_tenant_id()
    
    if request.method == 'GET':
        items = Item.objects.filter(tenant_id=tenant_id)
        
        # Filter by search query
        search = request.GET.get('search', '')
        if search:
            items = items.filter(
                Q(name__icontains=search) | 
                Q(sku__icontains=search) |
                Q(description__icontains=search)
            )
        
        # Filter by category
        category = request.GET.get('category', '')
        if category:
            items = items.filter(category=category)
        
        # Filter by low stock
        low_stock = request.GET.get('low_stock', '')
        if low_stock == 'true':
            items = items.filter(quantity__lte=F('min_stock_level'))
        
        # Pagination
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        offset = (page - 1) * per_page
        
        total_count = items.count()
        items_page = items[offset:offset + per_page]
        
        items_data = []
        for item in items_page:
            items_data.append({
                'id': str(item.id),
                'sku': item.sku,
                'name': item.name,
                'description': item.description,
                'category': item.category,
                'quantity': float(item.quantity),
                'unit_price': float(item.unit_price),
                'min_stock_level': float(item.min_stock_level),
                'is_low_stock': item.is_low_stock,
                'created_at': item.created_at.isoformat(),
                'updated_at': item.updated_at.isoformat(),
            })
        
        return Response({
            'success': True,
            'data': items_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': (total_count + per_page - 1) // per_page
            }
        })
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Check if SKU already exists
            if Item.objects.filter(tenant_id=tenant_id, sku=data['sku']).exists():
                return Response({
                    'success': False,
                    'error': 'SKU already exists'
                }, status=400)
            
            item = Item.objects.create(
                tenant_id=tenant_id,
                sku=data['sku'],
                name=data['name'],
                description=data.get('description', ''),
                category=data.get('category', ''),
                quantity=data.get('quantity', 0),
                unit_price=data.get('unit_price', 0),
                min_stock_level=data.get('min_stock_level', 0),
            )
            
            return Response({
                'success': True,
                'data': {
                    'id': str(item.id),
                    'sku': item.sku,
                    'name': item.name,
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)

@api_view(['GET', 'PUT', 'DELETE'])
@login_required
def item_detail_api(request, item_id):
    """API for individual item operations"""
    tenant_id = get_current_tenant_id()
    
    try:
        item = Item.objects.get(id=item_id, tenant_id=tenant_id)
    except Item.DoesNotExist:
        return Response({'success': False, 'error': 'Item not found'}, status=404)
    
    if request.method == 'GET':
        return Response({
            'success': True,
            'data': {
                'id': str(item.id),
                'sku': item.sku,
                'name': item.name,
                'description': item.description,
                'category': item.category,
                'quantity': float(item.quantity),
                'unit_price': float(item.unit_price),
                'min_stock_level': float(item.min_stock_level),
                'is_low_stock': item.is_low_stock,
                'created_at': item.created_at.isoformat(),
                'updated_at': item.updated_at.isoformat(),
            }
        })
    
    elif request.method == 'PUT':
        try:
            data = json.loads(request.body)
            
            # Update fields
            for field in ['name', 'description', 'category', 'unit_price', 'min_stock_level']:
                if field in data:
                    setattr(item, field, data[field])
            
            item.save()
            
            return Response({
                'success': True,
                'data': {
                    'id': str(item.id),
                    'name': item.name,
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)
    
    elif request.method == 'DELETE':
        item.delete()
        return Response({'success': True})

@api_view(['GET', 'POST'])
def movements_api(request):
    """API for managing inventory movements"""
    # Check if user is authenticated for session-based auth
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    tenant_id = get_current_tenant_id()
    
    if request.method == 'GET':
        movements = Movement.objects.filter(tenant_id=tenant_id).select_related('item')
        
        # Filter by item
        item_id = request.GET.get('item_id', '')
        if item_id:
            movements = movements.filter(item_id=item_id)
        
        # Filter by date range
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')
        if date_from:
            movements = movements.filter(created_at__date__gte=date_from)
        if date_to:
            movements = movements.filter(created_at__date__lte=date_to)
        
        # Pagination
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        offset = (page - 1) * per_page
        
        total_count = movements.count()
        movements_page = movements.order_by('-created_at')[offset:offset + per_page]
        
        movements_data = []
        for movement in movements_page:
            movements_data.append({
                'id': str(movement.id),
                'item_id': str(movement.item.id),
                'item_name': movement.item.name,
                'item_sku': movement.item.sku,
                'quantity': float(movement.quantity),
                'movement_type': movement.get_movement_name(),
                'reference': movement.reference,
                'notes': movement.notes,
                'created_at': movement.created_at.isoformat(),
            })
        
        return Response({
            'success': True,
            'data': movements_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': (total_count + per_page - 1) // per_page
            }
        })
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            item = Item.objects.get(id=data['item_id'], tenant_id=tenant_id)
            
            movement = Movement.objects.create(
                tenant_id=tenant_id,
                item=item,
                quantity=data['quantity'],
                movement_type=data.get('movement_type', 'adjustment'),
                reference=data.get('reference', ''),
                notes=data.get('notes', ''),
            )
            
            return Response({
                'success': True,
                'data': {
                    'id': str(movement.id),
                    'item_name': item.name,
                    'quantity': float(movement.quantity),
                }
            })
            
        except Item.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Item not found'
            }, status=404)
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)

@api_view(['POST'])
@login_required
def stock_adjustment_api(request):
    """API for stock adjustments"""
    tenant_id = get_current_tenant_id()
    
    try:
        data = json.loads(request.body)
        
        item = Item.objects.get(id=data['item_id'], tenant_id=tenant_id)
        new_quantity = float(data['new_quantity'])
        reason = data.get('reason', '')
        
        # Calculate adjustment quantity
        adjustment_quantity = new_quantity - float(item.quantity)
        
        if adjustment_quantity != 0:
            # Create movement record
            Movement.objects.create(
                tenant_id=tenant_id,
                item=item,
                quantity=adjustment_quantity,
                movement_type='adjustment',
                reference=f"Stock Adjustment",
                notes=f"Adjusted to {new_quantity}. Reason: {reason}",
            )
            
            # Update item quantity
            item.quantity = new_quantity
            item.save()
        
        return Response({
            'success': True,
            'data': {
                'item_name': item.name,
                'old_quantity': float(item.quantity) - adjustment_quantity,
                'new_quantity': float(item.quantity),
                'adjustment': adjustment_quantity,
            }
        })
        
    except Item.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Item not found'
        }, status=404)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=400)

@api_view(['GET', 'POST'])
@permission_classes([])  # Override default DRF permissions
def locations_api(request):
    """API for managing warehouse locations"""
    # Check if user is authenticated for session-based auth
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    tenant_id = get_current_tenant_id()
    
    if request.method == 'GET':
        locations = Location.objects.filter(tenant_id=tenant_id)
        
        # Filter by ID if provided (for single location retrieval)
        location_id = request.GET.get('id', '')
        if location_id:
            locations = locations.filter(id=location_id)
        
        # Filter by search query
        search = request.GET.get('search', '')
        if search:
            locations = locations.filter(
                Q(name__icontains=search) | 
                Q(code__icontains=search)
            )
        
        locations_data = []
        for location in locations:
            storage_summary = location.get_storage_summary()
            
            # Format address
            address_parts = []
            if location.address:
                address_parts.append(location.address)
            if location.city:
                address_parts.append(location.city)
            if location.state:
                address_parts.append(location.state)
            formatted_address = '، '.join(address_parts) if address_parts else 'غير محدد'
            
            # Calculate area display
            area_display = f"{location.area_sqm} م²" if location.area_sqm else '-'
            
            # Items count display
            items_count = storage_summary['unique_items']
            items_display = f"{items_count} صنف" if items_count > 0 else '-'
            
            # Status display
            status_display = 'نشط' if location.is_active else 'غير نشط'
            status_class = 'active' if location.is_active else 'inactive'
            
            # Location type display
            location_type_display = location.location_type.name if location.location_type else '-'
            
            locations_data.append({
                'id': str(location.id),
                'name': location.name,
                'code': location.code,
                'address': location.address or '',
                'formatted_address': formatted_address,
                'city': location.city or '',
                'state': location.state or '',
                'country': location.country or '',
                'postal_code': location.postal_code or '',
                'area_sqm': float(location.area_sqm) if location.area_sqm else None,
                'area_display': area_display,
                'max_items': location.max_items,
                'max_volume': float(location.max_volume) if location.max_volume else None,
                'max_weight': float(location.max_weight) if location.max_weight else None,
                'contact_name': location.contact_name or '',
                'phone': location.phone or '',
                'email': location.email or '',
                'notes': location.notes or '',
                'is_active': location.is_active,
                'status_display': status_display,
                'status_class': status_class,
                'location_type': location_type_display,
                'location_type_id': str(location.location_type.id) if location.location_type else None,
                'items_count': items_count,
                'items_display': items_display,
                'total_quantity': storage_summary['total_quantity'],
                'storage_summary': storage_summary,
                'created_at': location.created_at.isoformat(),
                'updated_at': location.updated_at.isoformat(),
            })
        
        return Response({
            'success': True,
            'data': locations_data
        })
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            location = Location.objects.create(
                tenant_id=tenant_id,
                name=data['name'],
                code=data['code'],
                address=data.get('address', ''),
                city=data.get('city', ''),
                state=data.get('state', ''),
                country=data.get('country', ''),
                contact_name=data.get('contact_name', ''),
                phone=data.get('phone', ''),
                email=data.get('email', ''),
                notes=data.get('notes', ''),
            )
            
            return Response({
                'success': True,
                'data': {
                    'id': str(location.id),
                    'name': location.name,
                    'code': location.code,
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)

@api_view(['GET', 'PUT', 'DELETE'])
@login_required
def location_detail_api(request, location_id):
    """API for individual location operations"""
    tenant_id = get_current_tenant_id()
    
    try:
        location = Location.objects.get(id=location_id, tenant_id=tenant_id)
    except Location.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Location not found'
        }, status=404)
    
    if request.method == 'GET':
        storage_summary = location.get_storage_summary()
        
        # Format address
        address_parts = []
        if location.address:
            address_parts.append(location.address)
        if location.city:
            address_parts.append(location.city)
        if location.state:
            address_parts.append(location.state)
        formatted_address = '، '.join(address_parts) if address_parts else 'غير محدد'
        
        return Response({
            'success': True,
            'data': {
                'id': str(location.id),
                'name': location.name,
                'code': location.code,
                'address': location.address or '',
                'formatted_address': formatted_address,
                'city': location.city or '',
                'state': location.state or '',
                'country': location.country or '',
                'postal_code': location.postal_code or '',
                'area_sqm': float(location.area_sqm) if location.area_sqm else None,
                'area_display': f"{location.area_sqm} م²" if location.area_sqm else '-',
                'max_items': location.max_items,
                'max_volume': float(location.max_volume) if location.max_volume else None,
                'max_weight': float(location.max_weight) if location.max_weight else None,
                'contact_name': location.contact_name or '',
                'phone': location.phone or '',
                'email': location.email or '',
                'notes': location.notes or '',
                'is_active': location.is_active,
                'status_display': 'نشط' if location.is_active else 'غير نشط',
                'location_type': location.location_type.name if location.location_type else '-',
                'location_type_id': str(location.location_type.id) if location.location_type else None,
                'items_count': storage_summary['unique_items'],
                'items_display': f"{storage_summary['unique_items']} صنف" if storage_summary['unique_items'] > 0 else '-',
                'total_quantity': storage_summary['total_quantity'],
                'storage_summary': storage_summary,
                'created_at': location.created_at.isoformat(),
                'updated_at': location.updated_at.isoformat(),
            }
        })
    
    elif request.method == 'PUT':
        try:
            data = json.loads(request.body)
            
            # Update location fields
            if 'name' in data:
                location.name = data['name']
            if 'address' in data:
                location.address = data['address']
            if 'city' in data:
                location.city = data['city']
            if 'state' in data:
                location.state = data['state']
            if 'country' in data:
                location.country = data['country']
            if 'postal_code' in data:
                location.postal_code = data['postal_code']
            if 'contact_name' in data:
                location.contact_name = data['contact_name']
            if 'phone' in data:
                location.phone = data['phone']
            if 'email' in data:
                location.email = data['email']
            if 'notes' in data:
                location.notes = data['notes']
            if 'is_active' in data:
                location.is_active = data['is_active']
            if 'area_sqm' in data:
                location.area_sqm = data['area_sqm']
            if 'max_items' in data:
                location.max_items = data['max_items']
            if 'max_volume' in data:
                location.max_volume = data['max_volume']
            if 'max_weight' in data:
                location.max_weight = data['max_weight']
            
            location.save()
            
            return Response({
                'success': True,
                'message': 'Location updated successfully',
                'data': {
                    'id': str(location.id),
                    'name': location.name,
                    'is_active': location.is_active,
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)
    
    elif request.method == 'DELETE':
        try:
            # Check if location has items before deletion
            storage_summary = location.get_storage_summary()
            if storage_summary['unique_items'] > 0:
                return Response({
                    'success': False,
                    'error': f'Cannot delete location with {storage_summary["unique_items"]} items. Please move items first.'
                }, status=400)
            
            location_name = location.name
            location.delete()
            
            return Response({
                'success': True,
                'message': f'Location "{location_name}" deleted successfully'
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)

@api_view(['GET'])
@login_required
def item_locations_api(request):
    """API for tracking item positions across locations"""
    tenant_id = get_current_tenant_id()
    
    item_locations = ItemLocation.objects.filter(
        tenant_id=tenant_id
    ).select_related('item', 'location')
    
    # Filter by item
    item_id = request.GET.get('item_id', '')
    if item_id:
        item_locations = item_locations.filter(item_id=item_id)
    
    # Filter by location
    location_id = request.GET.get('location_id', '')
    if location_id:
        item_locations = item_locations.filter(location_id=location_id)
    
    data = []
    for item_loc in item_locations:
        data.append({
            'id': str(item_loc.id),
            'item_id': str(item_loc.item.id),
            'item_name': item_loc.item.name,
            'item_sku': item_loc.item.sku,
            'location_id': str(item_loc.location.id),
            'location_name': item_loc.location.name,
            'location_code': item_loc.location.code,
            'location_type': item_loc.location.location_type,
            'quantity': float(item_loc.quantity),
            'reorder_point': float(item_loc.reorder_point) if item_loc.reorder_point else 0,
            'max_stock': float(item_loc.max_stock) if item_loc.max_stock else 0,
            'is_low_stock': item_loc.is_low_stock,
            'updated_at': item_loc.updated_at.isoformat(),
        })
    
    return Response({
        'success': True,
        'data': data
    })

@api_view(['GET', 'POST'])
@login_required
def transfers_api(request):
    """API for managing transfers between locations"""
    tenant_id = get_current_tenant_id()
    
    if request.method == 'GET':
        transfers = TransferOrder.objects.filter(tenant_id=tenant_id).select_related(
            'source_location', 'destination_location'
        )
        
        # Filter by status
        status_filter = request.GET.get('status', '')
        if status_filter:
            transfers = transfers.filter(status=status_filter)
        
        transfers_data = []
        for transfer in transfers:
            transfers_data.append({
                'id': str(transfer.id),
                'reference': transfer.reference,
                'source_location': {
                    'id': str(transfer.source_location.id),
                    'name': transfer.source_location.name,
                    'code': transfer.source_location.code,
                },
                'destination_location': {
                    'id': str(transfer.destination_location.id),
                    'name': transfer.destination_location.name,
                    'code': transfer.destination_location.code,
                },
                'status': transfer.status,
                'items_count': transfer.items_count,
                'notes': transfer.notes,
                'created_at': transfer.created_at.isoformat(),
            })
        
        return Response({
            'success': True,
            'data': transfers_data
        })
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            source_location = Location.objects.get(
                id=data['source_location_id'], tenant_id=tenant_id
            )
            destination_location = Location.objects.get(
                id=data['destination_location_id'], tenant_id=tenant_id
            )
            
            # Generate reference number
            ref_count = TransferOrder.objects.filter(tenant_id=tenant_id).count() + 1
            reference = f"TRF-{ref_count:06d}"
            
            transfer = TransferOrder.objects.create(
                tenant_id=tenant_id,
                reference=reference,
                source_location=source_location,
                destination_location=destination_location,
                notes=data.get('notes', ''),
            )
            
            # Add items
            items_data = data.get('items', [])
            for item_data in items_data:
                item = Item.objects.get(id=item_data['item_id'], tenant_id=tenant_id)
                TransferOrderItem.objects.create(
                    tenant_id=tenant_id,
                    transfer_order=transfer,
                    item=item,
                    quantity=item_data['quantity'],
                    notes=item_data.get('notes', ''),
                )
            
            transfer.items_count = len(items_data)
            transfer.save()
            
            return Response({
                'success': True,
                'data': {
                    'id': str(transfer.id),
                    'reference': transfer.reference,
                }
            })
            
        except (Location.DoesNotExist, Item.DoesNotExist) as e:
            return Response({
                'success': False,
                'error': 'Location or Item not found'
            }, status=404)
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)

@api_view(['GET'])
@login_required
def pending_transfers_api(request):
    """API for getting pending transfers"""
    tenant_id = get_current_tenant_id()
    
    pending_transfers = TransferOrder.objects.filter(
        tenant_id=tenant_id,
        status__in=['draft', 'pending', 'in_transit']
    ).select_related('source_location', 'destination_location')
    
    data = []
    for transfer in pending_transfers:
        data.append({
            'id': str(transfer.id),
            'reference': transfer.reference,
            'source_location_name': transfer.source_location.name,
            'destination_location_name': transfer.destination_location.name,
            'status': transfer.status,
            'items_count': transfer.items_count,
            'created_at': transfer.created_at.isoformat(),
        })
    
    return Response({
        'success': True,
        'data': data
    })

@api_view(['GET', 'POST'])
@permission_classes([])  # Override default DRF permissions
def suppliers_api(request):
    """API for managing suppliers"""
    # Check if user is authenticated for session-based auth
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    tenant_id = get_current_tenant_id()
    
    if request.method == 'GET':
        suppliers = Supplier.objects.filter(tenant_id=tenant_id)
        
        # Filter by search query
        search = request.GET.get('search', '')
        if search:
            suppliers = suppliers.filter(
                Q(name__icontains=search) | 
                Q(email__icontains=search)
            )
        
        suppliers_data = []
        for supplier in suppliers:
            suppliers_data.append({
                'id': str(supplier.id),
                'name': supplier.name,
                'email': supplier.email,
                'phone': supplier.phone,
                'address': supplier.address,
                'is_active': supplier.is_active,
                'created_at': supplier.created_at.isoformat(),
            })
        
        return Response({
            'success': True,
            'data': suppliers_data
        })
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            supplier = Supplier.objects.create(
                tenant_id=tenant_id,
                name=data['name'],
                email=data.get('email', ''),
                phone=data.get('phone', ''),
                address=data.get('address', ''),
            )
            
            return Response({
                'success': True,
                'data': {
                    'id': str(supplier.id),
                    'name': supplier.name,
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)

@api_view(['GET', 'POST'])
@login_required
def purchase_orders_api(request):
    """API for managing purchase orders"""
    tenant_id = get_current_tenant_id()
    
    if request.method == 'GET':
        orders = PurchaseOrder.objects.filter(tenant_id=tenant_id).select_related('supplier')
        
        # Filter by status
        status_filter = request.GET.get('status', '')
        if status_filter:
            orders = orders.filter(status=status_filter)
        
        orders_data = []
        for order in orders:
            orders_data.append({
                'id': str(order.id),
                'order_number': order.order_number,
                'supplier': {
                    'id': str(order.supplier.id),
                    'name': order.supplier.name,
                },
                'order_date': order.order_date.isoformat(),
                'expected_delivery_date': order.expected_delivery_date.isoformat() if order.expected_delivery_date else None,
                'status': order.status,
                'total_amount': float(order.total_amount),
                'notes': order.notes,
                'created_at': order.created_at.isoformat(),
            })
        
        return Response({
            'success': True,
            'data': orders_data
        })
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            supplier = Supplier.objects.get(id=data['supplier_id'], tenant_id=tenant_id)
            
            # Generate order number
            order_count = PurchaseOrder.objects.filter(tenant_id=tenant_id).count() + 1
            order_number = f"PO-{order_count:06d}"
            
            order = PurchaseOrder.objects.create(
                tenant_id=tenant_id,
                order_number=order_number,
                supplier=supplier,
                order_date=data.get('order_date', timezone.now().date()),
                expected_delivery_date=data.get('expected_delivery_date'),
                notes=data.get('notes', ''),
            )
            
            # Add items
            items_data = data.get('items', [])
            for item_data in items_data:
                item = Item.objects.get(id=item_data['item_id'], tenant_id=tenant_id)
                PurchaseOrderItem.objects.create(
                    tenant_id=tenant_id,
                    purchase_order=order,
                    item=item,
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                )
            
            order.update_total_amount()
            
            return Response({
                'success': True,
                'data': {
                    'id': str(order.id),
                    'order_number': order.order_number,
                }
            })
            
        except (Supplier.DoesNotExist, Item.DoesNotExist) as e:
            return Response({
                'success': False,
                'error': 'Supplier or Item not found'
            }, status=404)
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=400)

@api_view(['GET'])
@login_required
def pending_orders_api(request):
    """API for getting pending purchase orders"""
    tenant_id = get_current_tenant_id()
    
    pending_orders = PurchaseOrder.objects.filter(
        tenant_id=tenant_id,
        status__in=['draft', 'sent', 'confirmed']
    ).select_related('supplier')
    
    data = []
    for order in pending_orders:
        data.append({
            'id': str(order.id),
            'order_number': order.order_number,
            'supplier_name': order.supplier.name,
            'status': order.status,
            'total_amount': float(order.total_amount),
            'order_date': order.order_date.isoformat(),
            'expected_delivery_date': order.expected_delivery_date.isoformat() if order.expected_delivery_date else None,
            'created_at': order.created_at.isoformat(),
        })
    
    return Response({
        'success': True,
        'data': data
    })

@api_view(['GET'])
def low_stock_items_api(request):
    """API for getting low stock items"""
    # Check if user is authenticated for session-based auth
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    tenant_id = get_current_tenant_id()
    
    low_stock_items = Item.objects.filter(
        tenant_id=tenant_id,
        quantity__lte=F('min_stock_level'),
        min_stock_level__gt=0
    )
    
    data = []
    for item in low_stock_items:
        data.append({
            'id': str(item.id),
            'sku': item.sku,
            'name': item.name,
            'current_quantity': float(item.quantity),
            'min_stock_level': float(item.min_stock_level),
            'shortage': float(item.min_stock_level - item.quantity),
        })
    
    return Response({
        'success': True,
        'data': data
    })

@api_view(['GET'])
def supply_chain_stats_api(request):
    """API for getting supply chain dashboard statistics"""
    # Check if user is authenticated for session-based auth
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    tenant_id = get_current_tenant_id()
    
    # Basic counts
    total_items = Item.objects.filter(tenant_id=tenant_id).count()
    total_suppliers = Supplier.objects.filter(tenant_id=tenant_id, is_active=True).count()
    total_locations = Location.objects.filter(tenant_id=tenant_id, is_active=True).count()
    
    # Low stock count
    low_stock_count = Item.objects.filter(
        tenant_id=tenant_id,
        quantity__lte=F('min_stock_level'),
        min_stock_level__gt=0
    ).count()
    
    # Recent movements count (last 24 hours)
    recent_movements = Movement.objects.filter(
        tenant_id=tenant_id,
        created_at__gte=timezone.now() - timedelta(days=1)
    ).count()
    
    # Pending transfers
    pending_transfers = TransferOrder.objects.filter(
        tenant_id=tenant_id,
        status__in=['draft', 'pending', 'in_transit']
    ).count()
    
    # Pending orders
    pending_orders = PurchaseOrder.objects.filter(
        tenant_id=tenant_id,
        status__in=['draft', 'sent', 'confirmed']
    ).count()
    
    # Average order value
    purchase_orders = PurchaseOrder.objects.filter(tenant_id=tenant_id)
    average_order_value = purchase_orders.aggregate(avg=Sum('total_amount'))['avg'] or 0
    
    # Active suppliers
    suppliers = Supplier.objects.filter(tenant_id=tenant_id)
    active_suppliers = suppliers.filter(is_active=True).count()
    
    stats = {
        'total_items': total_items,
        'total_suppliers': total_suppliers,
        'total_locations': total_locations,
        'low_stock_count': low_stock_count,
        'recent_movements': recent_movements,
        'pending_transfers': pending_transfers,
        'pending_orders': pending_orders,
        'average_order_value': average_order_value,
        'active_suppliers': active_suppliers,
    }
    
    return JsonResponse(stats)


# ========================
# BATCH TRACKING API ENDPOINTS
# ========================

@api_view(['GET', 'POST'])
@login_required
def batches_api(request):
    """API for managing item batches"""
    tenant_id = get_current_tenant_id()
    
    if request.method == 'GET':
        # Get query parameters
        item_id = request.GET.get('item_id')
        status = request.GET.get('status', 'active')
        expired_only = request.GET.get('expired_only', 'false').lower() == 'true'
        expiring_soon = request.GET.get('expiring_soon', 'false').lower() == 'true'
        
        # Build queryset
        batches = ItemBatch.objects.filter(tenant_id=tenant_id)
        
        if item_id:
            batches = batches.filter(item_id=item_id)
        
        if status and status != 'all':
            batches = batches.filter(status=status)
        
        if expired_only:
            batches = batches.filter(
                expiry_date__lt=timezone.now().date(),
                expiry_date__isnull=False
            )
        
        if expiring_soon:
            expiry_threshold = timezone.now().date() + timedelta(days=30)
            batches = batches.filter(
                expiry_date__lte=expiry_threshold,
                expiry_date__isnull=False
            )
        
        # Order by received date (newest first)
        batches = batches.order_by('-received_date')
        
        # Serialize data
        batch_data = []
        for batch in batches:
            batch_data.append({
                'id': str(batch.id),
                'batch_number': batch.batch_number,
                'item_id': str(batch.item.id),
                'item_name': batch.item.name,
                'item_sku': batch.item.sku,
                'status': batch.status,
                'initial_quantity': float(batch.initial_quantity),
                'current_quantity': float(batch.current_quantity),
                'reserved_quantity': float(batch.reserved_quantity),
                'available_quantity': float(batch.available_quantity),
                'purchase_price': float(batch.purchase_price) if batch.purchase_price else None,
                'selling_price': float(batch.selling_price) if batch.selling_price else None,
                'manufactured_date': batch.manufactured_date.isoformat() if batch.manufactured_date else None,
                'received_date': batch.received_date.isoformat(),
                'expiry_date': batch.expiry_date.isoformat() if batch.expiry_date else None,
                'days_until_expiry': batch.days_until_expiry,
                'is_expired': batch.is_expired,
                'expiry_status': batch.expiry_status,
                'supplier_batch_ref': batch.supplier_batch_ref,
                'purchase_order_ref': batch.purchase_order_ref,
                'warehouse_location': batch.warehouse_location,
                'notes': batch.notes,
                'created_at': batch.created_at.isoformat(),
                'updated_at': batch.updated_at.isoformat(),
            })
        
        return JsonResponse({
            'batches': batch_data,
            'count': len(batch_data)
        })
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Validate required fields
            required_fields = ['item_id', 'batch_number', 'initial_quantity', 'received_date']
            for field in required_fields:
                if field not in data or not data[field]:
                    return JsonResponse({'error': f'Field {field} is required'}, status=400)
            
            # Get item
            try:
                item = Item.objects.get(id=data['item_id'], tenant_id=tenant_id)
            except Item.DoesNotExist:
                return JsonResponse({'error': 'Item not found'}, status=404)
            
            # Auto-generate batch number if requested
            batch_number = data['batch_number']
            if batch_number == 'AUTO' and item.auto_generate_batch_number:
                batch_number = item.get_next_batch_number()
                if not batch_number:
                    return JsonResponse({'error': 'Could not generate batch number'}, status=400)
            
            # Check for duplicate batch number
            if ItemBatch.objects.filter(item=item, batch_number=batch_number, tenant_id=tenant_id).exists():
                return JsonResponse({'error': 'Batch number already exists for this item'}, status=400)
            
            # Parse dates
            received_date = datetime.strptime(data['received_date'], '%Y-%m-%d').date()
            manufactured_date = None
            if data.get('manufactured_date'):
                manufactured_date = datetime.strptime(data['manufactured_date'], '%Y-%m-%d').date()
            
            expiry_date = None
            if data.get('expiry_date'):
                expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
            elif item.default_shelf_life_days and manufactured_date:
                expiry_date = manufactured_date + timedelta(days=item.default_shelf_life_days)
            elif item.default_shelf_life_days:
                expiry_date = received_date + timedelta(days=item.default_shelf_life_days)
            
            # Create batch
            batch = ItemBatch.objects.create(
                tenant_id=tenant_id,
                item=item,
                batch_number=batch_number,
                status=data.get('status', 'active'),
                initial_quantity=data['initial_quantity'],
                current_quantity=data.get('current_quantity', data['initial_quantity']),
                reserved_quantity=data.get('reserved_quantity', 0),
                purchase_price=data.get('purchase_price'),
                selling_price=data.get('selling_price'),
                manufactured_date=manufactured_date,
                received_date=received_date,
                expiry_date=expiry_date,
                supplier_batch_ref=data.get('supplier_batch_ref', ''),
                purchase_order_ref=data.get('purchase_order_ref', ''),
                warehouse_location=data.get('warehouse_location', ''),
                notes=data.get('notes', ''),
                attributes=data.get('attributes', {})
            )
            
            # Create batch movement record
            BatchMovement.objects.create(
                tenant_id=tenant_id,
                batch=batch,
                quantity=batch.initial_quantity,
                movement_type='in',
                reference_number=data.get('purchase_order_ref', ''),
                notes=f"Initial batch receipt: {batch.batch_number}"
            )
            
            # Update item quantity if batch tracking is enabled
            if item.has_batch_tracking():
                item.update_quantity_from_batches()
            
            return JsonResponse({
                'message': 'Batch created successfully',
                'batch_id': str(batch.id),
                'batch_number': batch.batch_number
            })
            
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@api_view(['GET', 'PUT', 'DELETE'])
@login_required
def batch_detail_api(request, batch_id):
    """API for individual batch operations"""
    tenant_id = get_current_tenant_id()
    
    try:
        batch = ItemBatch.objects.get(id=batch_id, tenant_id=tenant_id)
    except ItemBatch.DoesNotExist:
        return JsonResponse({'error': 'Batch not found'}, status=404)
    
    if request.method == 'GET':
        # Get batch details with movement history
        movements = BatchMovement.objects.filter(batch=batch).order_by('-created_at')
        movement_data = []
        for movement in movements:
            movement_data.append({
                'id': str(movement.id),
                'quantity': float(movement.quantity),
                'movement_type': movement.movement_type,
                'reference_number': movement.reference_number,
                'notes': movement.notes,
                'created_at': movement.created_at.isoformat(),
            })
        
        return JsonResponse({
            'id': str(batch.id),
            'batch_number': batch.batch_number,
            'item_id': str(batch.item.id),
            'item_name': batch.item.name,
            'item_sku': batch.item.sku,
            'status': batch.status,
            'initial_quantity': float(batch.initial_quantity),
            'current_quantity': float(batch.current_quantity),
            'reserved_quantity': float(batch.reserved_quantity),
            'available_quantity': float(batch.available_quantity),
            'purchase_price': float(batch.purchase_price) if batch.purchase_price else None,
            'selling_price': float(batch.selling_price) if batch.selling_price else None,
            'manufactured_date': batch.manufactured_date.isoformat() if batch.manufactured_date else None,
            'received_date': batch.received_date.isoformat(),
            'expiry_date': batch.expiry_date.isoformat() if batch.expiry_date else None,
            'days_until_expiry': batch.days_until_expiry,
            'is_expired': batch.is_expired,
            'expiry_status': batch.expiry_status,
            'supplier_batch_ref': batch.supplier_batch_ref,
            'purchase_order_ref': batch.purchase_order_ref,
            'warehouse_location': batch.warehouse_location,
            'notes': batch.notes,
            'attributes': batch.attributes,
            'movements': movement_data,
            'created_at': batch.created_at.isoformat(),
            'updated_at': batch.updated_at.isoformat(),
        })
    
    elif request.method == 'PUT':
        try:
            data = json.loads(request.body)
            
            # Update allowed fields
            if 'status' in data:
                batch.status = data['status']
            if 'selling_price' in data:
                batch.selling_price = data['selling_price']
            if 'warehouse_location' in data:
                batch.warehouse_location = data['warehouse_location']
            if 'notes' in data:
                batch.notes = data['notes']
            if 'attributes' in data:
                batch.attributes = data['attributes']
            
            batch.save()
            
            return JsonResponse({'message': 'Batch updated successfully'})
            
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    elif request.method == 'DELETE':
        # Only allow deletion if batch has no movements and zero quantity
        if batch.current_quantity > 0:
            return JsonResponse({'error': 'Cannot delete batch with remaining quantity'}, status=400)
        
        movement_count = BatchMovement.objects.filter(batch=batch).count()
        if movement_count > 1:  # More than the initial receipt
            return JsonResponse({'error': 'Cannot delete batch with movement history'}, status=400)
        
        batch.delete()
        return JsonResponse({'message': 'Batch deleted successfully'})


@api_view(['POST'])
@login_required
def batch_movement_api(request):
    """API for creating batch movements (in/out/adjustment)"""
    tenant_id = get_current_tenant_id()
    
    try:
        data = json.loads(request.body)
        
        # Validate required fields
        required_fields = ['batch_id', 'quantity', 'movement_type']
        for field in required_fields:
            if field not in data:
                return JsonResponse({'error': f'Field {field} is required'}, status=400)
        
        # Get batch
        try:
            batch = ItemBatch.objects.get(id=data['batch_id'], tenant_id=tenant_id)
        except ItemBatch.DoesNotExist:
            return JsonResponse({'error': 'Batch not found'}, status=404)
        
        movement_type = data['movement_type']
        quantity = float(data['quantity'])
        
        # Validate movement type and quantity
        if movement_type == 'out' and quantity > batch.available_quantity:
            return JsonResponse({
                'error': f'Insufficient quantity. Available: {batch.available_quantity}'
            }, status=400)
        
        # Create batch movement
        batch_movement = BatchMovement.objects.create(
            tenant_id=tenant_id,
            batch=batch,
            quantity=quantity,
            movement_type=movement_type,
            reference_number=data.get('reference_number', ''),
            notes=data.get('notes', '')
        )
        
        # Update batch quantities
        if movement_type == 'in':
            batch.current_quantity += quantity
        elif movement_type == 'out':
            batch.current_quantity -= quantity
        elif movement_type == 'adjustment':
            batch.current_quantity = quantity
        elif movement_type == 'reservation':
            batch.reserved_quantity += quantity
        elif movement_type == 'release':
            batch.reserved_quantity = max(0, batch.reserved_quantity - quantity)
        
        batch.save()
        
        # Update item total quantity if batch tracking is enabled
        if batch.item.has_batch_tracking():
            batch.item.update_quantity_from_batches()
        
        return JsonResponse({
            'message': 'Batch movement created successfully',
            'movement_id': str(batch_movement.id),
            'new_quantity': float(batch.current_quantity),
            'available_quantity': float(batch.available_quantity)
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@api_view(['GET'])
@login_required
def expiring_batches_api(request):
    """API for getting batches that are expiring soon"""
    tenant_id = get_current_tenant_id()
    
    # Get query parameters
    days_ahead = int(request.GET.get('days_ahead', 30))
    include_expired = request.GET.get('include_expired', 'true').lower() == 'true'
    
    # Calculate expiry threshold
    expiry_threshold = timezone.now().date() + timedelta(days=days_ahead)
    
    # Build queryset
    batches = ItemBatch.objects.filter(
        tenant_id=tenant_id,
        status='active',
        expiry_date__lte=expiry_threshold,
        expiry_date__isnull=False,
        current_quantity__gt=0
    )
    
    if not include_expired:
        batches = batches.filter(expiry_date__gte=timezone.now().date())
    
    # Order by expiry date (soonest first)
    batches = batches.order_by('expiry_date')
    
    # Serialize data
    batch_data = []
    for batch in batches:
        batch_data.append({
            'id': str(batch.id),
            'batch_number': batch.batch_number,
            'item_id': str(batch.item.id),
            'item_name': batch.item.name,
            'item_sku': batch.item.sku,
            'current_quantity': float(batch.current_quantity),
            'expiry_date': batch.expiry_date.isoformat(),
            'days_until_expiry': batch.days_until_expiry,
            'is_expired': batch.is_expired,
            'expiry_status': batch.expiry_status,
            'warehouse_location': batch.warehouse_location,
        })
    
    return JsonResponse({
        'batches': batch_data,
        'count': len(batch_data),
        'days_ahead': days_ahead
    })


@api_view(['GET'])
@login_required
def batch_allocations_api(request):
    """API for getting available batch allocations for an item"""
    tenant_id = get_current_tenant_id()
    
    item_id = request.GET.get('item_id')
    quantity_needed = request.GET.get('quantity_needed')
    
    if not item_id:
        return JsonResponse({'error': 'item_id parameter is required'}, status=400)
    
    try:
        item = Item.objects.get(id=item_id, tenant_id=tenant_id)
    except Item.DoesNotExist:
        return JsonResponse({'error': 'Item not found'}, status=404)
    
    if not item.has_batch_tracking():
        return JsonResponse({'error': 'Item does not have batch tracking enabled'}, status=400)
    
    # Get available batches based on inventory method
    available_batches = item.get_available_batches()
    
    # If quantity is specified, get allocations
    allocations = []
    if quantity_needed:
        try:
            quantity_needed = float(quantity_needed)
            batch_allocations = item.allocate_batch_quantities(quantity_needed)
            
            for batch, allocated_qty in batch_allocations:
                allocations.append({
                    'batch_id': str(batch.id),
                    'batch_number': batch.batch_number,
                    'allocated_quantity': float(allocated_qty),
                    'available_quantity': float(batch.available_quantity),
                    'expiry_date': batch.expiry_date.isoformat() if batch.expiry_date else None,
                    'days_until_expiry': batch.days_until_expiry,
                })
        except (ValueError, TypeError):
            return JsonResponse({'error': 'Invalid quantity_needed value'}, status=400)
    
    # Get all available batches
    batch_data = []
    for batch in available_batches:
        batch_data.append({
            'id': str(batch.id),
            'batch_number': batch.batch_number,
            'available_quantity': float(batch.available_quantity),
            'expiry_date': batch.expiry_date.isoformat() if batch.expiry_date else None,
            'days_until_expiry': batch.days_until_expiry,
            'is_expired': batch.is_expired,
            'warehouse_location': batch.warehouse_location,
        })
    
    return JsonResponse({
        'item_id': str(item.id),
        'item_name': item.name,
        'inventory_method': item.inventory_method,
        'available_batches': batch_data,
        'allocations': allocations,
        'total_available': sum(batch.available_quantity for batch in available_batches)
    })


class PricingAPIViewSet(viewsets.ViewSet):
    """
    API endpoints for dynamic pricing calculations
    """
    
    def get_tenant_id(self):
        """Get tenant ID from request"""
        return getattr(self.request.user, 'tenant_id', None)
    
    @action(detail=False, methods=['post'])
    def calculate_service_price(self, request):
        """
        Calculate dynamic service price for a work order or operation
        
        POST /api/pricing/calculate_service_price/
        {
            "operation_type_id": "uuid",
            "vehicle_id": "uuid",
            "service_center_id": "uuid", 
            "customer_id": "uuid",
            "parts_list": [{"item_id": "uuid", "quantity": 2.5}],
            "pricing_strategy": "standard",
            "actual_labor_hours": 3.5,
            "target_date": "2025-01-15",
            "context": {"priority": "high"}
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get required parameters
            operation_type_id = request.data.get('operation_type_id')
            if not operation_type_id:
                return Response({'error': 'operation_type_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get optional parameters
            vehicle_id = request.data.get('vehicle_id')
            service_center_id = request.data.get('service_center_id')
            customer_id = request.data.get('customer_id')
            parts_list = request.data.get('parts_list', [])
            pricing_strategy = request.data.get('pricing_strategy', 'standard')
            actual_labor_hours = request.data.get('actual_labor_hours')
            target_date = request.data.get('target_date')
            context = request.data.get('context', {})
            
            # Parse target date
            if target_date:
                try:
                    from datetime import datetime
                    target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
                except ValueError:
                    return Response({'error': 'Invalid target_date format. Use YYYY-MM-DD'}, 
                                  status=status.HTTP_400_BAD_REQUEST)
            
            # Get related objects
            vehicle = None
            service_center = None
            customer = None
            
            if vehicle_id:
                try:
                    from setup.models import Vehicle
                    vehicle = Vehicle.objects.get(tenant_id=tenant_id, id=vehicle_id)
                except Vehicle.DoesNotExist:
                    return Response({'error': 'Vehicle not found'}, status=status.HTTP_404_NOT_FOUND)
            
            if service_center_id:
                try:
                    from setup.models import ServiceCenter
                    service_center = ServiceCenter.objects.get(tenant_id=tenant_id, id=service_center_id)
                except ServiceCenter.DoesNotExist:
                    return Response({'error': 'Service center not found'}, status=status.HTTP_404_NOT_FOUND)
            
            if customer_id:
                try:
                    from setup.models import Customer
                    customer = Customer.objects.get(tenant_id=tenant_id, id=customer_id)
                except Customer.DoesNotExist:
                    return Response({'error': 'Customer not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Calculate pricing using dynamic engine
            pricing_engine = DynamicPricingEngine(tenant_id)
            result = pricing_engine.calculate_service_price(
                operation_type_id=operation_type_id,
                vehicle=vehicle,
                service_center=service_center,
                customer=customer,
                parts_list=parts_list,
                pricing_strategy=pricing_strategy,
                actual_labor_hours=actual_labor_hours,
                target_date=target_date,
                context=context
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def calculate_work_order_pricing(self, request):
        """
        Calculate pricing for an existing work order
        
        POST /api/pricing/calculate_work_order_pricing/
        {
            "work_order_id": "uuid",
            "pricing_strategy": "premium",
            "force_recalculate": true
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            work_order_id = request.data.get('work_order_id')
            if not work_order_id:
                return Response({'error': 'work_order_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            pricing_strategy = request.data.get('pricing_strategy', 'standard')
            force_recalculate = request.data.get('force_recalculate', False)
            
            # Get work order
            try:
                from work_orders.models import WorkOrder
                work_order = WorkOrder.objects.get(tenant_id=tenant_id, id=work_order_id)
            except WorkOrder.DoesNotExist:
                return Response({'error': 'Work order not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Calculate pricing
            result = WorkOrderPricingService.calculate_work_order_estimate(
                work_order, pricing_strategy, force_recalculate
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def compare_pricing_strategies(self, request):
        """
        Compare pricing across different strategies
        
        POST /api/pricing/compare_pricing_strategies/
        {
            "work_order_id": "uuid"
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            work_order_id = request.data.get('work_order_id')
            if not work_order_id:
                return Response({'error': 'work_order_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get work order
            try:
                from work_orders.models import WorkOrder
                work_order = WorkOrder.objects.get(tenant_id=tenant_id, id=work_order_id)
            except WorkOrder.DoesNotExist:
                return Response({'error': 'Work order not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Get pricing comparison
            result = WorkOrderPricingService.get_pricing_comparison(work_order)
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def pricing_strategies(self, request):
        """
        Get available pricing strategies
        
        GET /api/pricing/pricing_strategies/
        """
        strategies = [
            {'key': 'standard', 'name': 'Standard Pricing', 'description': 'Regular service pricing'},
            {'key': 'premium', 'name': 'Premium Service', 'description': 'High-end service with premium rates'},
            {'key': 'economy', 'name': 'Economy Service', 'description': 'Budget-friendly service option'},
            {'key': 'express', 'name': 'Express/Rush Service', 'description': 'Fast service with urgency premium'},
            {'key': 'warranty', 'name': 'Warranty Work', 'description': 'Work covered under warranty'},
            {'key': 'insurance', 'name': 'Insurance Claim', 'description': 'Insurance-covered repairs'},
            {'key': 'fleet', 'name': 'Fleet Customer', 'description': 'Special pricing for fleet customers'},
            {'key': 'vip', 'name': 'VIP Customer', 'description': 'Premium customer pricing'}
        ]
        
        return Response({'strategies': strategies}, status=status.HTTP_200_OK)


class InventoryAPIViewSet(viewsets.ViewSet):
    """
    API endpoints for inventory integration and automation
    """
    
    def get_tenant_id(self):
        """Get tenant ID from request"""
        return getattr(self.request.user, 'tenant_id', None)
    
    @action(detail=False, methods=['post'])
    def validate_stock_availability(self, request):
        """
        Validate stock availability for a list of parts
        
        POST /api/inventory/validate_stock_availability/
        {
            "parts_list": [{"item_id": "uuid", "quantity": 2.5}],
            "warehouse_location_id": "uuid"
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            parts_list = request.data.get('parts_list', [])
            warehouse_location_id = request.data.get('warehouse_location_id')
            
            if not parts_list:
                return Response({'error': 'parts_list is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get warehouse location if provided
            warehouse_location = None
            if warehouse_location_id:
                try:
                    from setup.models import Warehouse
                    warehouse_location = Warehouse.objects.get(tenant_id=tenant_id, id=warehouse_location_id)
                except Warehouse.DoesNotExist:
                    return Response({'error': 'Warehouse not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Validate stock availability
            inventory_service = InventoryIntegrationService(tenant_id)
            result = inventory_service.validate_stock_availability(parts_list, warehouse_location)
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def create_parts_request(self, request):
        """
        Create automated parts request for missing items
        
        POST /api/inventory/create_parts_request/
        {
            "work_order_id": "uuid",
            "missing_items": [{"item_id": "uuid", "quantity": 2}],
            "priority": "high"
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            work_order_id = request.data.get('work_order_id')
            missing_items = request.data.get('missing_items', [])
            priority = request.data.get('priority', 'medium')
            
            if not work_order_id:
                return Response({'error': 'work_order_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            if not missing_items:
                return Response({'error': 'missing_items is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get work order
            try:
                from work_orders.models import WorkOrder
                work_order = WorkOrder.objects.get(tenant_id=tenant_id, id=work_order_id)
            except WorkOrder.DoesNotExist:
                return Response({'error': 'Work order not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Create parts request
            inventory_service = InventoryIntegrationService(tenant_id)
            result = inventory_service.create_parts_request(work_order, missing_items, priority)
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def run_automated_monitoring(self, request):
        """
        Run automated parts monitoring and request creation
        
        POST /api/inventory/run_automated_monitoring/
        {
            "service_center_id": "uuid",
            "check_low_stock": true,
            "check_expiring_items": true,
            "check_work_order_forecasts": true
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            service_center_id = request.data.get('service_center_id')
            check_low_stock = request.data.get('check_low_stock', True)
            check_expiring_items = request.data.get('check_expiring_items', True)
            check_work_order_forecasts = request.data.get('check_work_order_forecasts', True)
            
            # Get service center if provided
            service_center = None
            if service_center_id:
                try:
                    from setup.models import ServiceCenter
                    service_center = ServiceCenter.objects.get(tenant_id=tenant_id, id=service_center_id)
                except ServiceCenter.DoesNotExist:
                    return Response({'error': 'Service center not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Run automated monitoring
            monitoring_service = AutomatedPartsRequestService(tenant_id)
            result = monitoring_service.monitor_and_create_requests(
                service_center=service_center,
                check_low_stock=check_low_stock,
                check_expiring_items=check_expiring_items,
                check_work_order_forecasts=check_work_order_forecasts
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def optimize_stock_levels(self, request):
        """
        Optimize stock levels based on demand analysis
        
        POST /api/inventory/optimize_stock_levels/
        {
            "service_center_id": "uuid",
            "auto_apply": false
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            service_center_id = request.data.get('service_center_id')
            auto_apply = request.data.get('auto_apply', False)
            
            # Get service center if provided
            service_center = None
            if service_center_id:
                try:
                    from setup.models import ServiceCenter
                    service_center = ServiceCenter.objects.get(tenant_id=tenant_id, id=service_center_id)
                except ServiceCenter.DoesNotExist:
                    return Response({'error': 'Service center not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Run stock level optimization
            optimization_service = StockLevelOptimizationService(tenant_id)
            result = optimization_service.optimize_stock_levels_for_service_center(
                service_center=service_center,
                auto_apply=auto_apply
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def analyze_item_demand(self, request):
        """
        Analyze demand patterns for a specific item
        
        POST /api/inventory/analyze_item_demand/
        {
            "item_id": "uuid",
            "days_back": 90
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            item_id = request.data.get('item_id')
            days_back = request.data.get('days_back', 90)
            
            if not item_id:
                return Response({'error': 'item_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get item
            try:
                from inventory.models import Item
                item = Item.objects.get(tenant_id=tenant_id, id=item_id)
            except Item.DoesNotExist:
                return Response({'error': 'Item not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Analyze demand patterns
            optimization_service = StockLevelOptimizationService(tenant_id)
            result = optimization_service.analyze_demand_patterns(item, days_back)
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WorkOrderIntegrationAPIViewSet(viewsets.ViewSet):
    """
    API endpoints for work order integration with pricing and inventory
    """
    
    def get_tenant_id(self):
        """Get tenant ID from request"""
        return getattr(self.request.user, 'tenant_id', None)
    
    @action(detail=False, methods=['post'])
    def allocate_stock_for_work_order(self, request):
        """
        Allocate stock for work order with comprehensive validation
        
        POST /api/work-orders/allocate_stock_for_work_order/
        {
            "work_order_id": "uuid"
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            work_order_id = request.data.get('work_order_id')
            if not work_order_id:
                return Response({'error': 'work_order_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get work order
            try:
                from work_orders.models import WorkOrder
                work_order = WorkOrder.objects.get(tenant_id=tenant_id, id=work_order_id)
            except WorkOrder.DoesNotExist:
                return Response({'error': 'Work order not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Allocate stock
            from work_orders.services import WorkOrderAllocationService
            result = WorkOrderAllocationService.allocate_stock_for_work_order(
                work_order, user=request.user
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def validate_work_order_transition(self, request):
        """
        Validate if work order can transition to new status
        
        POST /api/work-orders/validate_work_order_transition/
        {
            "work_order_id": "uuid",
            "new_status": "in_progress"
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            work_order_id = request.data.get('work_order_id')
            new_status = request.data.get('new_status')
            
            if not work_order_id:
                return Response({'error': 'work_order_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            if not new_status:
                return Response({'error': 'new_status is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get work order
            try:
                from work_orders.models import WorkOrder
                work_order = WorkOrder.objects.get(tenant_id=tenant_id, id=work_order_id)
            except WorkOrder.DoesNotExist:
                return Response({'error': 'Work order not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Validate transition
            from work_orders.services import WorkOrderWorkflowService
            result = WorkOrderWorkflowService.can_transition_to_status(
                work_order, new_status, user=request.user
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def update_work_order_costs(self, request):
        """
        Update work order costs using dynamic pricing
        
        POST /api/work-orders/update_work_order_costs/
        {
            "work_order_id": "uuid",
            "pricing_strategy": "premium"
        }
        """
        try:
            tenant_id = self.get_tenant_id()
            if not tenant_id:
                return Response({'error': 'Tenant ID required'}, status=status.HTTP_400_BAD_REQUEST)
            
            work_order_id = request.data.get('work_order_id')
            pricing_strategy = request.data.get('pricing_strategy', 'standard')
            
            if not work_order_id:
                return Response({'error': 'work_order_id is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get work order
            try:
                from work_orders.models import WorkOrder
                work_order = WorkOrder.objects.get(tenant_id=tenant_id, id=work_order_id)
            except WorkOrder.DoesNotExist:
                return Response({'error': 'Work order not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Update costs
            result = WorkOrderPricingService.update_work_order_costs(work_order, pricing_strategy)
            
            # Add work order info to response
            result['work_order'] = {
                'id': str(work_order.id),
                'work_order_number': work_order.work_order_number,
                'estimated_cost': float(work_order.estimated_cost or 0),
                'actual_cost': float(work_order.actual_cost or 0),
                'status': work_order.status
            }
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
