from django.utils.translation import gettext_lazy as _
from app_settings.models import TenantSetting, SystemSetting


def get_tenant_setting(tenant_id, name, default=None):
    """
    Get a tenant setting by name
    
    Args:
        tenant_id (UUID): The tenant ID
        name (str): The setting name
        default (any, optional): Default value if setting doesn't exist
        
    Returns:
        dict: The setting value or default
    """
    try:
        setting = TenantSetting.objects.get(tenant_id=tenant_id, name=name)
        return setting.value
    except TenantSetting.DoesNotExist:
        return default or {}


def set_tenant_setting(tenant_id, name, value, description=None):
    """
    Set a tenant setting
    
    Args:
        tenant_id (UUID): The tenant ID
        name (str): The setting name
        value (dict): The setting value
        description (str, optional): The setting description
        
    Returns:
        TenantSetting: The created or updated setting
    """
    setting, created = TenantSetting.objects.update_or_create(
        tenant_id=tenant_id,
        name=name,
        defaults={
            'value': value,
            'description': description or ''
        }
    )
    
    return setting


def get_system_setting(name, default=None):
    """
    Get a system setting by name
    
    Args:
        name (str): The setting name
        default (any, optional): Default value if setting doesn't exist
        
    Returns:
        dict: The setting value or default
    """
    try:
        setting = SystemSetting.objects.get(name=name)
        return setting.value
    except SystemSetting.DoesNotExist:
        return default or {}


def set_system_setting(name, value, description=None):
    """
    Set a system setting
    
    Args:
        name (str): The setting name
        value (dict): The setting value
        description (str, optional): The setting description
        
    Returns:
        SystemSetting: The created or updated setting
    """
    setting, created = SystemSetting.objects.update_or_create(
        name=name,
        defaults={
            'value': value,
            'description': description or ''
        }
    )
    
    return setting 