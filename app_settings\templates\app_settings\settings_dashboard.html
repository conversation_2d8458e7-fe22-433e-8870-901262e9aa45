{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ title }} - {% trans "نظام ما بعد البيع" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-bold text-gray-900">
                <i class="fas fa-cogs text-blue-600 mr-2"></i>
                {{ page_title }}
            </h1>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- User Preferences -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-user-cog text-blue-600 text-2xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800">{% trans "تفضيلات المستخدم" %}</h3>
                </div>
                <p class="text-gray-600 mb-4">{% trans "إدارة الإعدادات الشخصية والتفضيلات" %}</p>
                <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                    {% trans "إدارة التفضيلات" %}
                </button>
            </div>

            <!-- System Settings -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-server text-green-600 text-2xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800">{% trans "إعدادات النظام" %}</h3>
                </div>
                <p class="text-gray-600 mb-4">{% trans "تكوين إعدادات النظام العامة" %}</p>
                <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                    {% trans "إعدادات النظام" %}
                </button>
            </div>

            <!-- Security Settings -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-shield-alt text-red-600 text-2xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800">{% trans "إعدادات الأمان" %}</h3>
                </div>
                <p class="text-gray-600 mb-4">{% trans "إدارة كلمات المرور والأمان" %}</p>
                <button class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors">
                    {% trans "إعدادات الأمان" %}
                </button>
            </div>

            <!-- Notification Settings -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-bell text-yellow-600 text-2xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800">{% trans "إعدادات الإشعارات" %}</h3>
                </div>
                <p class="text-gray-600 mb-4">{% trans "تخصيص تفضيلات الإشعارات" %}</p>
                <button class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 transition-colors">
                    {% trans "إدارة الإشعارات" %}
                </button>
            </div>

            <!-- Backup & Restore -->
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-database text-purple-600 text-2xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800">{% trans "النسخ الاحتياطي" %}</h3>
                </div>
                <p class="text-gray-600 mb-4">{% trans "إدارة النسخ الاحتياطي والاستعادة" %}</p>
                <button class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors">
                    {% trans "النسخ الاحتياطي" %}
                </button>
            </div>

            <!-- Integration Settings -->
            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-plug text-indigo-600 text-2xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-800">{% trans "التكاملات" %}</h3>
                </div>
                <p class="text-gray-600 mb-4">{% trans "إدارة التكاملات مع الأنظمة الخارجية" %}</p>
                <button class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition-colors">
                    {% trans "إعدادات التكامل" %}
                </button>
            </div>
        </div>

        <div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                {% trans "معلومات النظام" %}
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                    <span class="font-semibold text-gray-700">{% trans "إصدار النظام:" %}</span>
                    <span class="text-gray-600">v2.1.0</span>
                </div>
                <div>
                    <span class="font-semibold text-gray-700">{% trans "آخر تحديث:" %}</span>
                    <span class="text-gray-600">{% now "Y-m-d" %}</span>
                </div>
                <div>
                    <span class="font-semibold text-gray-700">{% trans "حالة النظام:" %}</span>
                    <span class="text-green-600 font-semibold">{% trans "متصل" %}</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 