from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _

# Create your views here.

@login_required
def settings_dashboard(request):
    """
    Basic settings dashboard view
    """
    context = {
        'title': _('Settings'),
        'page_title': _('Application Settings'),
    }
    return render(request, 'app_settings/settings_dashboard.html', context)
