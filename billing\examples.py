from .services import RuleEngine
from .models_rules import PromotionRule, RuleCondition, RuleEffect
from .models import Invoice, Customer
from django.utils import timezone
import uuid

"""
Example usage of the dynamic rule engine
"""

def example_create_rules(tenant_id):
    """Example of creating different types of discount rules"""
    
    # Example 1: 10% discount for Toyota vehicles
    toyota_rule = PromotionRule.objects.create(
        tenant_id=tenant_id,
        name="Toyota Vehicle Discount",
        description="10% discount on all services for Toyota vehicles",
        rule_type="discount",
        priority=10,
        start_date=timezone.now(),
        end_date=timezone.now() + timezone.timedelta(days=90),
        is_active=True
    )
    
    # Add the condition
    RuleCondition.objects.create(
        tenant_id=tenant_id,
        rule=toyota_rule,
        condition_type="vehicle_make",
        operator="eq",
        value="Toyota"
    )
    
    # Add the effect
    RuleEffect.objects.create(
        tenant_id=tenant_id,
        rule=toyota_rule,
        effect_type="order_discount_percentage",
        effect_value=10.00,
        apply_to="all",
        description="10% Toyota Owner Discount"
    )
    
    # Example 2: Special parts discount for VIP customers
    vip_parts_rule = PromotionRule.objects.create(
        tenant_id=tenant_id,
        name="VIP Parts Discount",
        description="15% off all parts for VIP customers",
        rule_type="discount",
        priority=20,
        start_date=timezone.now(),
        is_active=True
    )
    
    # Add the condition
    RuleCondition.objects.create(
        tenant_id=tenant_id,
        rule=vip_parts_rule,
        condition_type="customer_status",
        operator="eq",
        value="vip"
    )
    
    # Add the effect
    RuleEffect.objects.create(
        tenant_id=tenant_id,
        rule=vip_parts_rule,
        effect_type="item_discount_percentage",
        effect_value=15.00,
        apply_to="parts_only",
        description="15% VIP Parts Discount"
    )
    
    # Example 3: Summer service special (time-limited)
    summer_special = PromotionRule.objects.create(
        tenant_id=tenant_id,
        name="Summer Service Special",
        description="$50 off any service over $200 during summer months",
        rule_type="special",
        priority=5,
        start_date=timezone.datetime(2023, 6, 1, tzinfo=timezone.utc),
        end_date=timezone.datetime(2023, 8, 31, tzinfo=timezone.utc),
        is_active=True
    )
    
    # Add condition for order total
    RuleCondition.objects.create(
        tenant_id=tenant_id,
        rule=summer_special,
        condition_type="order_total",
        operator="gte",
        value="200"
    )
    
    # Add the effect
    RuleEffect.objects.create(
        tenant_id=tenant_id,
        rule=summer_special,
        effect_type="order_discount_fixed",
        effect_value=50.00,
        apply_to="all",
        description="$50 Summer Service Special"
    )
    
    # Example 4: Recall coverage for specific part
    recall_rule = PromotionRule.objects.create(
        tenant_id=tenant_id,
        name="Brake Sensor Recall",
        description="Full coverage for recalled brake sensors on Honda models 2019-2021",
        rule_type="recall",
        priority=100,  # High priority
        start_date=timezone.now(),
        is_active=True,
    )
    
    # Add vehicle make condition
    RuleCondition.objects.create(
        tenant_id=tenant_id,
        rule=recall_rule,
        condition_type="vehicle_make",
        operator="eq",
        value="Honda",
        group_id="vehicle"
    )
    
    # Add year range condition
    RuleCondition.objects.create(
        tenant_id=tenant_id,
        rule=recall_rule,
        condition_type="vehicle_year",
        operator="between",
        value="2019",
        value2="2021",
        group_id="vehicle"
    )
    
    # Add part condition 
    RuleCondition.objects.create(
        tenant_id=tenant_id,
        rule=recall_rule,
        condition_type="part_id",
        operator="eq",
        value="BRAKE-SENSOR-123",
        group_id="part"
    )
    
    # Add the effect - 100% coverage
    RuleEffect.objects.create(
        tenant_id=tenant_id,
        rule=recall_rule,
        effect_type="set_special_price",
        effect_value=0.00,  # Free
        apply_to="specific_items",
        item_filter={"part_ids": ["BRAKE-SENSOR-123"]},
        description="Recall: Brake Sensor Replacement (Honda 2019-2021)"
    )
    
    # Example 5: Corporate customer discount that requires approval
    corporate_rule = PromotionRule.objects.create(
        tenant_id=tenant_id,
        name="Corporate Customer Discount",
        description="20% discount for registered corporate customers, requires manager approval",
        rule_type="discount",
        priority=30,
        start_date=timezone.now(),
        is_active=True,
        requires_approval=True,
        approval_level="manager"
    )
    
    # Add corporate customer condition
    RuleCondition.objects.create(
        tenant_id=tenant_id,
        rule=corporate_rule,
        condition_type="customer_group",
        operator="eq",
        value="corporate"
    )
    
    # Add the effect
    RuleEffect.objects.create(
        tenant_id=tenant_id,
        rule=corporate_rule,
        effect_type="order_discount_percentage",
        effect_value=20.00,
        apply_to="all",
        max_amount=500.00,  # Cap the discount at $500
        description="20% Corporate Customer Discount (Max $500)"
    )
    
    return {
        "toyota_rule": toyota_rule,
        "vip_parts_rule": vip_parts_rule,
        "summer_special": summer_special,
        "recall_rule": recall_rule,
        "corporate_rule": corporate_rule
    }


def example_apply_rules(tenant_id, invoice_id):
    """Example of applying rules to an invoice"""
    try:
        invoice = Invoice.objects.get(id=invoice_id, tenant_id=tenant_id)
    except Invoice.DoesNotExist:
        print(f"Invoice {invoice_id} not found")
        return None
    
    # Initialize the rule engine
    engine = RuleEngine(tenant_id)
    
    # Apply all matching rules to the invoice
    result = engine.apply_rules_to_invoice(invoice)
    
    # Show results
    print(f"Applied {result['rule_count']} rules to invoice {invoice.invoice_number}")
    for rule in result['applied_rules']:
        print(f"- Applied rule: {rule.name}")
    
    print(f"Final invoice total: {invoice.total_amount}")
    
    return result 