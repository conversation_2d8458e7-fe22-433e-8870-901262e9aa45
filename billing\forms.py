from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from core.forms import BaseForm, BaseModelForm
from .models import (
    CustomerPreference, InsuranceCompany, InsurancePolicy, WarrantyType,
    VehicleWarranty, DiscountType, PaymentMethod, Invoice, InvoiceItem, Payment
)
from setup.models import Customer, Vehicle, ServiceCenter
from work_orders.models import WorkOrder

class CustomerPreferenceForm(BaseModelForm):
    """Form for creating and editing customer preferences"""
    
    class Meta:
        model = CustomerPreference
        fields = [
            'customer', 'status', 'payment_terms', 'credit_limit',
            'default_discount_percentage', 'send_sms_notifications',
            'send_email_notifications', 'special_instructions'
        ]
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        if user and tenant_id:
            self.fields['customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        self.fields['customer'].widget.attrs.update({'class': base_class})
        self.fields['status'].widget.attrs.update({'class': base_class})
        self.fields['payment_terms'].widget.attrs.update({'class': base_class})
        self.fields['credit_limit'].widget.attrs.update({
            'class': base_class,
            'step': '0.01',
            'placeholder': _('Enter credit limit...')
        })
        self.fields['default_discount_percentage'].widget.attrs.update({
            'class': base_class,
            'step': '0.01',
            'max': '100',
            'min': '0',
            'placeholder': _('Enter discount percentage...')
        })
        self.fields['special_instructions'].widget.attrs.update({
            'class': base_class,
            'rows': 3,
            'placeholder': _('Enter special instructions...')
        })

class InsuranceCompanyForm(BaseModelForm):
    """Form for creating and editing insurance companies"""
    
    class Meta:
        model = InsuranceCompany
        fields = [
            'name', 'code', 'contact_person', 'phone', 'email', 'address',
            'contract_number', 'contract_start_date', 'contract_end_date',
            'approval_required', 'standard_approval_time_hours', 'is_active', 'notes'
        ]
        widgets = {
            'contract_start_date': forms.DateInput(attrs={'type': 'date'}),
            'contract_end_date': forms.DateInput(attrs={'type': 'date'}),
            'address': forms.Textarea(attrs={'rows': 3}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['approval_required', 'is_active']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['name'].widget.attrs.update({
            'placeholder': _('Enter company name...')
        })
        self.fields['code'].widget.attrs.update({
            'placeholder': _('Enter company code...')
        })
        self.fields['contact_person'].widget.attrs.update({
            'placeholder': _('Enter contact person name...')
        })
        self.fields['phone'].widget.attrs.update({
            'placeholder': _('Enter phone number...')
        })
        self.fields['email'].widget.attrs.update({
            'placeholder': _('Enter email address...')
        })

class InsurancePolicyForm(BaseModelForm):
    """Form for creating and editing insurance policies"""
    
    class Meta:
        model = InsurancePolicy
        fields = [
            'insurance_company', 'vehicle', 'customer', 'policy_number',
            'policy_type', 'start_date', 'end_date', 'coverage_amount',
            'deductible', 'is_active', 'notes'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        if user and tenant_id:
            self.fields['insurance_company'].queryset = InsuranceCompany.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
            self.fields['vehicle'].queryset = Vehicle.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['is_active']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['policy_number'].widget.attrs.update({
            'placeholder': _('Enter policy number...')
        })
        self.fields['coverage_amount'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter coverage amount...')
        })
        self.fields['deductible'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter deductible amount...')
        })

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if start_date and end_date and start_date >= end_date:
            raise ValidationError(_('End date must be after start date.'))
        
        return cleaned_data

class WarrantyTypeForm(BaseModelForm):
    """Form for creating and editing warranty types"""
    
    class Meta:
        model = WarrantyType
        fields = [
            'name', 'description', 'parts_covered', 'labor_covered',
            'default_duration_months', 'default_mileage_limit', 'is_active'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'parts_covered': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['labor_covered', 'is_active']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['name'].widget.attrs.update({
            'placeholder': _('Enter warranty type name...')
        })
        self.fields['default_duration_months'].widget.attrs.update({
            'placeholder': _('Enter duration in months...')
        })
        self.fields['default_mileage_limit'].widget.attrs.update({
            'placeholder': _('Enter mileage limit...')
        })

class VehicleWarrantyForm(BaseModelForm):
    """Form for creating and editing vehicle warranties"""
    
    class Meta:
        model = VehicleWarranty
        fields = [
            'vehicle', 'warranty_type', 'warranty_number', 'provider',
            'provider_name', 'start_date', 'end_date', 'mileage_limit',
            'is_active', 'notes'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        if user and tenant_id:
            self.fields['vehicle'].queryset = Vehicle.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['warranty_type'].queryset = WarrantyType.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['is_active']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['warranty_number'].widget.attrs.update({
            'placeholder': _('Enter warranty number...')
        })
        self.fields['provider_name'].widget.attrs.update({
            'placeholder': _('Enter provider name...')
        })

class DiscountTypeForm(BaseModelForm):
    """Form for creating and editing discount types"""
    
    class Meta:
        model = DiscountType
        fields = [
            'name', 'description', 'discount_method', 'percentage', 'fixed_amount',
            'valid_from', 'valid_to', 'min_order_amount', 'max_discount_amount',
            'apply_to_parts', 'apply_to_labor', 'is_active'
        ]
        widgets = {
            'valid_from': forms.DateInput(attrs={'type': 'date'}),
            'valid_to': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['apply_to_parts', 'apply_to_labor', 'is_active']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['name'].widget.attrs.update({
            'placeholder': _('Enter discount name...')
        })
        self.fields['percentage'].widget.attrs.update({
            'step': '0.01',
            'max': '100',
            'min': '0',
            'placeholder': _('Enter percentage...')
        })
        self.fields['fixed_amount'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter fixed amount...')
        })

class PaymentMethodForm(BaseModelForm):
    """Form for creating and editing payment methods"""
    
    class Meta:
        model = PaymentMethod
        fields = [
            'name', 'payment_type', 'description', 'processing_fee_percentage',
            'processing_fee_fixed', 'requires_approval', 'is_active'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['requires_approval', 'is_active']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['name'].widget.attrs.update({
            'placeholder': _('Enter payment method name...')
        })
        self.fields['processing_fee_percentage'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter processing fee percentage...')
        })
        self.fields['processing_fee_fixed'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter fixed processing fee...')
        })

class InvoiceForm(BaseModelForm):
    """Form for creating and editing invoices"""
    
    class Meta:
        model = Invoice
        fields = [
            'work_order', 'customer', 'service_center', 'invoice_number',
            'invoice_date', 'due_date', 'status', 'insurance_policy',
            'vehicle_warranty', 'discount_type', 'discount_amount',
            'tax_method', 'tax_percentage', 'notes', 'terms_and_conditions'
        ]
        widgets = {
            'invoice_date': forms.DateInput(attrs={'type': 'date'}),
            'due_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
            'terms_and_conditions': forms.Textarea(attrs={'rows': 4}),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        if user and tenant_id:
            self.fields['work_order'].queryset = WorkOrder.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
            self.fields['service_center'].queryset = ServiceCenter.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
            self.fields['insurance_policy'].queryset = InsurancePolicy.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
            self.fields['vehicle_warranty'].queryset = VehicleWarranty.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
            self.fields['discount_type'].queryset = DiscountType.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': base_class})
        
        self.fields['invoice_number'].widget.attrs.update({
            'placeholder': _('Enter invoice number...')
        })
        self.fields['discount_amount'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter discount amount...')
        })
        self.fields['tax_percentage'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter tax percentage...')
        })

class InvoiceItemForm(BaseModelForm):
    """Form for creating and editing invoice items"""
    
    class Meta:
        model = InvoiceItem
        fields = [
            'invoice', 'item_type', 'description', 'quantity', 'unit_price',
            'part_id', 'discount_percentage', 'discount_amount',
            'tax_percentage', 'is_covered_by_insurance', 'is_covered_by_warranty',
            'coverage_percentage', 'notes'
        ]
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 2}),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        if user and tenant_id:
            self.fields['invoice'].queryset = Invoice.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['is_covered_by_insurance', 'is_covered_by_warranty']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['description'].widget.attrs.update({
            'placeholder': _('Enter item description...')
        })
        self.fields['quantity'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter quantity...')
        })
        self.fields['unit_price'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter unit price...')
        })

class PaymentForm(BaseModelForm):
    """Form for creating and editing payments"""
    
    class Meta:
        model = Payment
        fields = [
            'invoice', 'customer', 'payment_method', 'payment_date',
            'amount', 'status', 'reference_number', 'transaction_id',
            'card_last_four', 'card_type', 'bank_name', 'check_number',
            'insurance_claim_number', 'insurance_payment_date', 'notes'
        ]
        widgets = {
            'payment_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'insurance_payment_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        if user and tenant_id:
            self.fields['invoice'].queryset = Invoice.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
            self.fields['payment_method'].queryset = PaymentMethod.objects.filter(
                tenant_id=tenant_id, is_active=True
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': base_class})
        
        self.fields['amount'].widget.attrs.update({
            'step': '0.01',
            'placeholder': _('Enter payment amount...')
        })
        self.fields['reference_number'].widget.attrs.update({
            'placeholder': _('Enter reference number...')
        })
        self.fields['transaction_id'].widget.attrs.update({
            'placeholder': _('Enter transaction ID...')
        })

class BillingReportForm(BaseForm):
    """Form for billing reports"""
    
    REPORT_TYPE_CHOICES = [
        ('invoice_summary', _('Invoice Summary')),
        ('payment_summary', _('Payment Summary')),
        ('outstanding_invoices', _('Outstanding Invoices')),
        ('customer_statements', _('Customer Statements')),
        ('insurance_claims', _('Insurance Claims')),
        ('warranty_coverage', _('Warranty Coverage')),
    ]
    
    report_type = forms.ChoiceField(
        label=_("Report Type"),
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    date_from = forms.DateField(
        label=_("Date From"),
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    date_to = forms.DateField(
        label=_("Date To"),
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    customer = forms.ModelChoiceField(
        label=_("Customer"),
        queryset=Customer.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    status = forms.ChoiceField(
        label=_("Status"),
        choices=[('', _('All'))] + Invoice.INVOICE_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        if user and tenant_id:
            self.fields['customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id, is_active=True
            ) 