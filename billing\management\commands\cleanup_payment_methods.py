import os
import django
from django.core.management.base import BaseCommand
from django.db.models import Count
from billing.models import PaymentMethod, Payment

class Command(BaseCommand):
    help = 'Clean up duplicate payment methods'

    def handle(self, *args, **options):
        self.stdout.write('=== Cleaning up duplicate payment methods ===')

        # Find duplicates by name and tenant_id
        duplicates = PaymentMethod.objects.values('name', 'tenant_id').annotate(
            count=Count('id')
        ).filter(count__gt=1)

        for dup_info in duplicates:
            name = dup_info['name']
            tenant_id = dup_info['tenant_id']
            count = dup_info['count']
            
            self.stdout.write(f'Processing: {name} ({count} duplicates)')
            
            # Get all instances of this duplicate
            all_instances = PaymentMethod.objects.filter(
                name=name, 
                tenant_id=tenant_id
            ).order_by('created_at')
            
            # Keep the first (oldest) instance
            keep_instance = all_instances.first()
            self.stdout.write(f'  Keeping: {keep_instance.id}')
            
            # Get duplicates to remove (all except the first)
            to_remove = all_instances[1:]
            
            for duplicate in to_remove:
                # Check if this duplicate has any payments
                payments_count = Payment.objects.filter(payment_method=duplicate).count()
                
                if payments_count > 0:
                    self.stdout.write(f'    Moving {payments_count} payments to kept instance')
                    # Move payments to the kept instance
                    Payment.objects.filter(payment_method=duplicate).update(payment_method=keep_instance)
                
                self.stdout.write(f'    Deleting duplicate: {duplicate.id}')
                duplicate.delete()

        self.stdout.write('')
        self.stdout.write('=== Cleanup completed ===')

        # Show final state
        final_methods = PaymentMethod.objects.filter(is_active=True).order_by('name')
        self.stdout.write(f'Final payment methods count: {final_methods.count()}')
        for method in final_methods:
            payments_count = Payment.objects.filter(payment_method=method).count()
            self.stdout.write(f'  {method.name} - {payments_count} payments') 