from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from billing.models import Invoice, PaymentMethod
from setup.models import Customer, ServiceCenter
from work_orders.models import WorkOrder
from datetime import datetime, timedelta
import uuid

class Command(BaseCommand):
    help = 'Create demo invoices in the database for cashier dashboard testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating demo invoices in database...')
        
        # Get tenant_id from existing record or use default
        try:
            tenant_id = Customer.objects.first().tenant_id if Customer.objects.exists() else 'default_tenant'
            self.stdout.write(f'Using tenant_id: {tenant_id}')
        except Exception:
            tenant_id = 'default_tenant'
            self.stdout.write(f'Using default tenant_id: {tenant_id}')

        # Create required dependencies first
        from setup.models import ServiceCenterType, Franchise, Company
        
        # Create service center type
        center_type, created = ServiceCenterType.objects.get_or_create(
            name='مركز رئيسي',
            defaults={
                'description': 'مركز خدمة رئيسي شامل',
                'max_capacity': 50
            }
        )
        if created:
            self.stdout.write('✅ Created service center type')
        
        # Create franchise
        franchise, created = Franchise.objects.get_or_create(
            tenant_id=tenant_id,
            name='شركة الخدمة الرئيسية',
            defaults={
                'code': 'MAIN-FRANCHISE',
                'address': 'شارع النيل، القاهرة'
            }
        )
        if created:
            self.stdout.write('✅ Created franchise')
        
        # Create company  
        company, created = Company.objects.get_or_create(
            tenant_id=tenant_id,
            franchise=franchise,
            name='شركة الخدمة الرئيسية',
            defaults={
                'code': 'MAIN-COMPANY',
                'address': 'شارع النيل، القاهرة'
            }
        )
        if created:
            self.stdout.write('✅ Created company')

        # Create or get service center
        service_center, created = ServiceCenter.objects.get_or_create(
            tenant_id=tenant_id,
            name='مركز الخدمة الرئيسي',
            defaults={
                'company': company,
                'code': 'MAIN-SC',
                'center_type': center_type,
                'address': 'شارع الجامعة، مدينة نصر، القاهرة',
                'phone': '*********',
                'email': '<EMAIL>'
            }
        )
        if created:
            self.stdout.write('✅ Created service center')

        # Create customers with proper names
        customers_data = [
            ('أحمد', 'محمد علي', '01234567890', '<EMAIL>', 'شارع النيل، المعادي، القاهرة'),
            ('سارة', 'أحمد محمود', '01098765432', '<EMAIL>', 'شارع القصر العيني، وسط البلد، القاهرة'),
            ('محمد', 'حسن عبدالله', '01555666777', '<EMAIL>', 'شارع الهرم، الجيزة'),
            ('فاطمة', 'عبدالرحمن', '01777888999', '<EMAIL>', 'شارع مصر الجديدة، القاهرة الجديدة'),
            ('خالد', 'أحمد سالم', '01444333222', '<EMAIL>', 'شارع الملك فيصل، الدقي، الجيزة')
        ]
        
        customers = []
        for first_name, last_name, phone, email, address in customers_data:
            customer, created = Customer.objects.get_or_create(
                tenant_id=tenant_id,
                phone=phone,
                defaults={
                    'first_name': first_name,
                    'last_name': last_name,
                    'email': email,
                    'address': address
                }
            )
            customers.append(customer)
            if created:
                self.stdout.write(f'✅ Created customer: {customer.full_name}')

        # Create work orders first (required for invoices)
        work_orders = []
        work_order_data = [
            ('WO-2024-001', 'م ن ص 123 - تويوتا كامري (2019)', 'صيانة دورية'),
            ('WO-2024-002', 'أ ب ج 456 - هوندا أكورد (2020)', 'إصلاح عطل'),
            ('WO-2024-003', 'د هـ و 789 - نيسان ألتيما (2018)', 'صيانة كبرى'),
            ('WO-2024-004', 'م ن ص 321 - مرسيدس C-Class (2021)', 'صيانة كبرى شاملة'),
            ('WO-2024-005', 'ر ش ت 654 - فولكس واجن جيتا (2020)', 'صيانة دورية شاملة')
        ]

        for i, (work_order_number, vehicle_info, description) in enumerate(work_order_data):
            try:
                from work_orders.models import WorkOrder
                work_order, created = WorkOrder.objects.get_or_create(
                    tenant_id=tenant_id,
                    work_order_number=work_order_number,
                    defaults={
                        'customer': customers[i],
                        'service_center': service_center,
                        'description': description,
                        'status': 'completed',
                        'estimated_cost': Decimal('1500.00'),
                        'actual_cost': Decimal('1500.00'),
                        'created_at': timezone.now() - timedelta(days=i+1)
                    }
                )
                work_orders.append(work_order)
                if created:
                    self.stdout.write(f'✅ Created work order: {work_order_number}')
            except Exception as e:
                # If WorkOrder model has different fields, create a simple one
                self.stdout.write(f'⚠️ WorkOrder creation failed: {e}')
                # We'll use None for work_order and handle it in invoice creation
                work_orders.append(None)

        # Create invoices with demo data
        invoices_data = [
            {
                'invoice_number': 'INV-2024-001',
                'customer_idx': 0,
                'status': 'issued',
                'subtotal': Decimal('1315.79'),
                'tax_amount': Decimal('184.21'),
                'total_amount': Decimal('1500.00'),
                'amount_paid': Decimal('0.00'),
                'due_date': timezone.now().date() + timedelta(days=15),
                'notes': 'صيانة دورية شاملة'
            },
            {
                'invoice_number': 'INV-2024-002',
                'customer_idx': 1,
                'status': 'paid',
                'subtotal': Decimal('2193.68'),
                'tax_amount': Decimal('307.12'),
                'total_amount': Decimal('2500.80'),
                'amount_paid': Decimal('2500.80'),
                'due_date': timezone.now().date() + timedelta(days=30),
                'notes': 'إصلاح شامل ودهان'
            },
            {
                'invoice_number': 'INV-2024-003',
                'customer_idx': 2,
                'status': 'overdue',
                'subtotal': Decimal('701.75'),
                'tax_amount': Decimal('98.25'),
                'total_amount': Decimal('800.00'),
                'amount_paid': Decimal('400.00'),
                'due_date': timezone.now().date() - timedelta(days=5),
                'notes': 'فحص وإصلاح نظام الفرامل'
            },
            {
                'invoice_number': 'INV-2024-004',
                'customer_idx': 3,
                'status': 'draft',
                'subtotal': Decimal('2807.02'),
                'tax_amount': Decimal('392.98'),
                'total_amount': Decimal('3200.00'),
                'amount_paid': Decimal('0.00'),
                'due_date': timezone.now().date() + timedelta(days=31),
                'notes': 'فاتورة مسودة - خدمة صيانة كبرى'
            },
            {
                'invoice_number': 'INV-2024-005',
                'customer_idx': 4,
                'status': 'partially_paid',
                'subtotal': Decimal('1622.59'),
                'tax_amount': Decimal('227.16'),
                'total_amount': Decimal('1850.75'),
                'amount_paid': Decimal('1000.00'),
                'due_date': timezone.now().date() + timedelta(days=28),
                'notes': 'فاتورة صيانة دورية مع دفع جزئي'
            }
        ]

        created_invoices = []
        for i, invoice_data in enumerate(invoices_data):
            try:
                # Calculate amount_due
                amount_due = invoice_data['total_amount'] - invoice_data['amount_paid']
                
                # Create invoice (handle the case where work_order might be None)
                invoice_defaults = {
                    'customer': customers[invoice_data['customer_idx']],
                    'service_center': service_center,  # Add required service_center
                    'status': invoice_data['status'],
                    'subtotal': invoice_data['subtotal'],
                    'tax_amount': invoice_data['tax_amount'],
                    'tax_percentage': Decimal('14.0'),
                    'total_amount': invoice_data['total_amount'],
                    'amount_paid': invoice_data['amount_paid'],
                    'amount_due': amount_due,
                    'due_date': invoice_data['due_date'],
                    'notes': invoice_data['notes'],
                    'tenant_id': tenant_id,
                    'created_at': timezone.now() - timedelta(days=len(invoices_data)-i),
                }
                
                # Handle work_order field - add it if available, but allow invoices without work orders for demo
                if work_orders[i] is not None:
                    invoice_defaults['work_order'] = work_orders[i]
                
                invoice, created = Invoice.objects.get_or_create(
                    tenant_id=tenant_id,
                    invoice_number=invoice_data['invoice_number'],
                    defaults=invoice_defaults
                )
                
                created_invoices.append(invoice)
                if created:
                    self.stdout.write(f'✅ Created invoice: {invoice.invoice_number} - {invoice.customer.full_name} - {invoice.total_amount} ج.م')
                else:
                    self.stdout.write(f'ℹ️ Invoice already exists: {invoice.invoice_number}')
                    
            except Exception as e:
                self.stdout.write(f'❌ Error creating invoice {invoice_data["invoice_number"]}: {str(e)}')

        # Create some payment methods
        payment_methods_data = [
            ('نقدي', 'طريقة دفع نقدي'),
            ('فيزا', 'بطاقة ائتمان فيزا'),
            ('ماستر كارد', 'بطاقة ائتمان ماستركارد'),
            ('تحويل بنكي', 'تحويل بنكي'),
        ]
        
        for name, description in payment_methods_data:
            try:
                payment_method, created = PaymentMethod.objects.get_or_create(
                    tenant_id=tenant_id,
                    name=name,
                    defaults={
                        'description': description,
                        'is_active': True
                    }
                )
                if created:
                    self.stdout.write(f'✅ Created payment method: {name}')
                else:
                    self.stdout.write(f'ℹ️ Payment method already exists: {name}')
            except PaymentMethod.MultipleObjectsReturned:
                self.stdout.write(f'⚠️ Multiple payment methods found with name: {name}, skipping...')

        self.stdout.write(
            self.style.SUCCESS(
                f'✅ Successfully created {len(created_invoices)} demo invoices in database!'
            )
        )
        self.stdout.write('🎯 Now the cashier dashboard will use real database records for payment processing.') 