from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Demo data creation has been disabled - this command no longer creates sample invoices'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.WARNING(
                'Demo data creation has been disabled. '
                'This command no longer creates sample invoices. '
                'Please use the real system to create invoices from work orders.'
            )
        ) 