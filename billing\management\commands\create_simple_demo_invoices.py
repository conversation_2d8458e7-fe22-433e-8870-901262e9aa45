from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Demo data creation has been disabled - this command no longer creates simple demo invoices'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.WARNING(
                'Demo data creation has been disabled. '
                'This command no longer creates simple demo invoices. '
                'Please use the real system to create invoices from work orders.'
            )
        ) 