from django.core.management.base import BaseCommand
from django.utils import timezone
from billing.models import Invoice
import os


class Command(BaseCommand):
    help = 'Refresh invoice statuses based on current payments and due dates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            help='Update invoices for specific tenant only'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes'
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id')
        dry_run = options.get('dry_run', False)
        
        # Use tenant from environment if not provided
        if not tenant_id:
            tenant_id = os.environ.get('TENANT_ID')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        self.stdout.write('Refreshing invoice statuses...')
        
        # Get invoices to update
        queryset = Invoice.objects.all()
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
            self.stdout.write(f'Filtering by tenant: {tenant_id}')
        
        invoices = list(queryset.select_related('customer', 'work_order'))
        
        if not invoices:
            self.stdout.write(
                self.style.WARNING('No invoices found to update')
            )
            return
        
        self.stdout.write(f'Found {len(invoices)} invoices to check')
        
        # Track changes
        status_changes = {}
        updated_count = 0
        
        for invoice in invoices:
            old_status = invoice.status
            
            # Update the status
            new_status = invoice.update_status()
            
            if old_status != new_status:
                if new_status not in status_changes:
                    status_changes[new_status] = []
                status_changes[new_status].append({
                    'invoice': invoice,
                    'old_status': old_status,
                    'new_status': new_status
                })
                
                if not dry_run:
                    invoice.save(update_fields=['status', 'amount_due'])
                
                updated_count += 1
        
        # Display results
        if updated_count == 0:
            self.stdout.write(
                self.style.SUCCESS('All invoice statuses are already up to date')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Updated {updated_count} invoice statuses')
            )
            
            # Show status changes
            for new_status, changes in status_changes.items():
                self.stdout.write(f'\nChanged to {new_status.upper()}:')
                for change in changes:
                    invoice = change['invoice']
                    self.stdout.write(
                        f'  {invoice.invoice_number} ({change["old_status"]} → {change["new_status"]}) '
                        f'- {invoice.customer.full_name if invoice.customer else "No customer"} '
                        f'- Due: ${invoice.amount_due} - Due Date: {invoice.due_date}'
                    )
        
        # Summary by status
        self.stdout.write('\nCurrent status summary:')
        status_summary = {}
        for invoice in invoices:
            status = invoice.status
            if status not in status_summary:
                status_summary[status] = []
            status_summary[status].append(invoice)
        
        for status, invoice_list in status_summary.items():
            self.stdout.write(f'  {status.upper()}: {len(invoice_list)} invoices')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nDRY RUN completed - no changes were saved')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('\nInvoice status refresh completed!')
            ) 