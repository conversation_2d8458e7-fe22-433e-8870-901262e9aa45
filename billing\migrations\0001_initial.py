# Generated by Django 4.2.20 on 2025-05-08 13:46

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('work_orders', '0004_workorder_customer'),
        ('setup', '0003_remove_vehicle_owner_email_remove_vehicle_owner_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DiscountType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Discount Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('discount_method', models.CharField(choices=[('percentage', 'Percentage'), ('fixed', 'Fixed Amount')], max_length=20, verbose_name='Discount Method')),
                ('percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Percentage')),
                ('fixed_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Fixed Amount')),
                ('valid_from', models.DateField(blank=True, null=True, verbose_name='Valid From')),
                ('valid_to', models.DateField(blank=True, null=True, verbose_name='Valid To')),
                ('min_order_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Minimum Order Amount')),
                ('max_discount_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Maximum Discount Amount')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('apply_to_parts', models.BooleanField(default=True, verbose_name='Apply to Parts')),
                ('apply_to_labor', models.BooleanField(default=True, verbose_name='Apply to Labor')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Discount Type',
                'verbose_name_plural': 'Discount Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='InsuranceCompany',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Company Name')),
                ('code', models.CharField(max_length=50, verbose_name='Company Code')),
                ('contact_person', models.CharField(blank=True, max_length=255, verbose_name='Contact Person')),
                ('phone', models.CharField(blank=True, max_length=50, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('contract_number', models.CharField(blank=True, max_length=100, verbose_name='Contract Number')),
                ('contract_start_date', models.DateField(blank=True, null=True, verbose_name='Contract Start Date')),
                ('contract_end_date', models.DateField(blank=True, null=True, verbose_name='Contract End Date')),
                ('approval_required', models.BooleanField(default=True, verbose_name='Approval Required')),
                ('standard_approval_time_hours', models.PositiveIntegerField(default=24, verbose_name='Standard Approval Time (hours)')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Insurance Company',
                'verbose_name_plural': 'Insurance Companies',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='InsurancePolicy',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('policy_number', models.CharField(max_length=100, verbose_name='Policy Number')),
                ('policy_type', models.CharField(choices=[('comprehensive', 'Comprehensive'), ('third_party', 'Third Party'), ('partial', 'Partial Coverage')], max_length=20, verbose_name='Policy Type')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('coverage_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='Coverage Amount')),
                ('deductible', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Deductible')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='insurance_policies', to='setup.customer', verbose_name='Policy Holder')),
                ('insurance_company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='policies', to='billing.insurancecompany', verbose_name='Insurance Company')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='insurance_policies', to='setup.vehicle', verbose_name='Vehicle')),
            ],
            options={
                'verbose_name': 'Insurance Policy',
                'verbose_name_plural': 'Insurance Policies',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='Invoice #')),
                ('invoice_date', models.DateField(default=django.utils.timezone.now, verbose_name='Invoice Date')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('issued', 'Issued'), ('partially_paid', 'Partially Paid'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='Status')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Discount Amount')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Subtotal')),
                ('tax_method', models.CharField(choices=[('inclusive', 'Tax Inclusive'), ('exclusive', 'Tax Exclusive')], default='exclusive', max_length=20, verbose_name='Tax Method')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Tax Percentage')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Tax Amount')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Total Amount')),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Amount Paid')),
                ('amount_due', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Amount Due')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('terms_and_conditions', models.TextField(blank=True, verbose_name='Terms and Conditions')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='invoices', to='setup.customer', verbose_name='Customer')),
                ('discount_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices', to='billing.discounttype', verbose_name='Discount Type')),
                ('insurance_policy', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices', to='billing.insurancepolicy', verbose_name='Insurance Policy')),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='invoices', to='setup.servicecenter', verbose_name='Service Center')),
            ],
            options={
                'verbose_name': 'Invoice',
                'verbose_name_plural': 'Invoices',
                'ordering': ['-invoice_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Payment Method')),
                ('payment_type', models.CharField(choices=[('cash', 'Cash'), ('credit_card', 'Credit Card'), ('debit_card', 'Debit Card'), ('bank_transfer', 'Bank Transfer'), ('check', 'Check'), ('digital_wallet', 'Digital Wallet'), ('credit', 'Store Credit'), ('insurance', 'Insurance')], max_length=20, verbose_name='Payment Type')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('processing_fee_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Processing Fee %')),
                ('processing_fee_fixed', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Processing Fee Fixed')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='Requires Approval')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Payment Method',
                'verbose_name_plural': 'Payment Methods',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PromotionRule',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Rule Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('rule_type', models.CharField(choices=[('discount', 'Discount'), ('warranty', 'Extended Warranty'), ('insurance', 'Insurance Coverage'), ('recall', 'Recall'), ('special', 'Special Offer')], max_length=20, verbose_name='Rule Type')),
                ('priority', models.PositiveIntegerField(default=0, verbose_name='Priority')),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Start Date/Time')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='End Date/Time')),
                ('max_usages', models.PositiveIntegerField(blank=True, null=True, verbose_name='Maximum Uses')),
                ('current_usages', models.PositiveIntegerField(default=0, verbose_name='Current Uses')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='Requires Approval')),
                ('approval_level', models.CharField(choices=[('none', 'No Approval Needed'), ('manager', 'Manager Approval'), ('director', 'Director Approval'), ('executive', 'Executive Approval')], default='none', max_length=20, verbose_name='Approval Level')),
                ('created_by', models.UUIDField(blank=True, null=True, verbose_name='Created By User ID')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Promotion Rule',
                'verbose_name_plural': 'Promotion Rules',
                'ordering': ['-priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WarrantyType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Warranty Type')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('parts_covered', models.TextField(blank=True, verbose_name='Parts Covered')),
                ('labor_covered', models.BooleanField(default=False, verbose_name='Labor Covered')),
                ('default_duration_months', models.PositiveIntegerField(default=12, verbose_name='Default Duration (months)')),
                ('default_mileage_limit', models.PositiveIntegerField(blank=True, null=True, verbose_name='Default Mileage Limit')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Warranty Type',
                'verbose_name_plural': 'Warranty Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='VehicleWarranty',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('warranty_number', models.CharField(blank=True, max_length=100, verbose_name='Warranty Number')),
                ('provider', models.CharField(choices=[('manufacturer', 'Manufacturer'), ('dealer', 'Dealer'), ('third_party', 'Third Party'), ('extended', 'Extended')], max_length=20, verbose_name='Provider')),
                ('provider_name', models.CharField(blank=True, max_length=255, verbose_name='Provider Name')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='End Date')),
                ('mileage_limit', models.PositiveIntegerField(blank=True, null=True, verbose_name='Mileage Limit')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warranties', to='setup.vehicle', verbose_name='Vehicle')),
                ('warranty_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vehicle_warranties', to='billing.warrantytype', verbose_name='Warranty Type')),
            ],
            options={
                'verbose_name': 'Vehicle Warranty',
                'verbose_name_plural': 'Vehicle Warranties',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='RuleLog',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('effect_type', models.CharField(max_length=50, verbose_name='Effect Type')),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Amount')),
                ('required_approval', models.BooleanField(default=False, verbose_name='Required Approval')),
                ('approved', models.BooleanField(blank=True, null=True, verbose_name='Approved')),
                ('approved_by', models.UUIDField(blank=True, null=True, verbose_name='Approved By User ID')),
                ('approval_date', models.DateTimeField(blank=True, null=True, verbose_name='Approval Date')),
                ('approval_notes', models.TextField(blank=True, verbose_name='Approval Notes')),
                ('applied_items', models.JSONField(blank=True, default=list, help_text='List of items the rule was applied to', verbose_name='Applied Items')),
                ('rule_data', models.JSONField(blank=True, default=dict, help_text='Snapshot of rule at application time', verbose_name='Rule Data')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rule_logs', to='setup.customer', verbose_name='Customer')),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rule_logs', to='billing.invoice', verbose_name='Invoice')),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='logs', to='billing.promotionrule', verbose_name='Rule')),
                ('vehicle', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rule_logs', to='setup.vehicle', verbose_name='Vehicle')),
                ('work_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rule_logs', to='work_orders.workorder', verbose_name='Work Order')),
            ],
            options={
                'verbose_name': 'Rule Log',
                'verbose_name_plural': 'Rule Logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RuleEffect',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('effect_type', models.CharField(choices=[('item_discount_percentage', 'Item Discount %'), ('item_discount_fixed', 'Item Fixed Discount'), ('order_discount_percentage', 'Order Discount %'), ('order_discount_fixed', 'Order Fixed Discount'), ('free_item', 'Free Item'), ('buy_x_get_y', 'Buy X Get Y'), ('set_special_price', 'Set Special Price'), ('price_multiplier', 'Price Multiplier'), ('set_warranty_coverage', 'Set Warranty Coverage'), ('extend_warranty', 'Extend Warranty'), ('set_insurance_coverage', 'Set Insurance Coverage'), ('apply_service_package', 'Apply Service Package'), ('set_payment_terms', 'Set Payment Terms'), ('set_priority', 'Set Service Priority'), ('custom_effect', 'Custom Effect')], max_length=50, verbose_name='Effect Type')),
                ('effect_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Effect Value')),
                ('effect_data', models.JSONField(blank=True, default=dict, help_text='Additional data for complex effects', verbose_name='Effect Data')),
                ('apply_to', models.CharField(choices=[('all', 'Entire Order'), ('parts_only', 'Parts Only'), ('labor_only', 'Labor Only'), ('specific_items', 'Specific Items'), ('specific_categories', 'Specific Categories')], default='all', max_length=50, verbose_name='Apply To')),
                ('max_items', models.PositiveIntegerField(blank=True, help_text='Maximum number of items this effect applies to', null=True, verbose_name='Maximum Items')),
                ('max_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Maximum Amount')),
                ('item_filter', models.JSONField(blank=True, default=dict, help_text='Filter criteria for which items this applies to', verbose_name='Item Filter')),
                ('description', models.CharField(blank=True, help_text='Description to show on invoice', max_length=255, verbose_name='Description')),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='effects', to='billing.promotionrule', verbose_name='Rule')),
            ],
            options={
                'verbose_name': 'Rule Effect',
                'verbose_name_plural': 'Rule Effects',
            },
        ),
        migrations.CreateModel(
            name='RuleCondition',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('condition_type', models.CharField(choices=[('customer_id', 'Specific Customer'), ('customer_group', 'Customer Group'), ('customer_status', 'Customer Status'), ('customer_since', 'Customer Since'), ('customer_age', 'Customer Age'), ('customer_gender', 'Customer Gender'), ('customer_location', 'Customer Location'), ('vehicle_id', 'Specific Vehicle'), ('vehicle_make', 'Vehicle Make'), ('vehicle_model', 'Vehicle Model'), ('vehicle_year', 'Vehicle Year'), ('vehicle_type', 'Vehicle Type'), ('vehicle_age', 'Vehicle Age'), ('vehicle_mileage', 'Vehicle Mileage'), ('service_id', 'Specific Service'), ('service_type', 'Service Type'), ('service_category', 'Service Category'), ('part_id', 'Specific Part'), ('part_category', 'Part Category'), ('part_manufacturer', 'Part Manufacturer'), ('order_total', 'Order Total'), ('order_items', 'Number of Items'), ('order_date', 'Order Date'), ('service_center', 'Service Center'), ('day_of_week', 'Day of Week'), ('month', 'Month'), ('time_of_day', 'Time of Day'), ('special_day', 'Special Day'), ('custom', 'Custom Condition')], max_length=50, verbose_name='Condition Type')),
                ('operator', models.CharField(choices=[('eq', 'Equals'), ('neq', 'Not Equals'), ('gt', 'Greater Than'), ('gte', 'Greater Than or Equal'), ('lt', 'Less Than'), ('lte', 'Less Than or Equal'), ('contains', 'Contains'), ('not_contains', "Doesn't Contain"), ('starts_with', 'Starts With'), ('ends_with', 'Ends With'), ('in', 'In List'), ('not_in', 'Not In List'), ('between', 'Between'), ('not_between', 'Not Between')], max_length=20, verbose_name='Operator')),
                ('value', models.TextField(help_text='String, number, or JSON list/object as needed', verbose_name='Value')),
                ('value2', models.TextField(blank=True, help_text='For operators like "between" that need two values', verbose_name='Second Value')),
                ('group_id', models.CharField(blank=True, help_text='Group ID for AND/OR logic', max_length=50, verbose_name='Group ID')),
                ('is_or_condition', models.BooleanField(default=False, help_text='If true, this condition uses OR logic with the previous condition', verbose_name='Is OR Condition')),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conditions', to='billing.promotionrule', verbose_name='Rule')),
            ],
            options={
                'verbose_name': 'Rule Condition',
                'verbose_name_plural': 'Rule Conditions',
                'ordering': ['rule', 'group_id'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Payment Date')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='Amount')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Status')),
                ('reference_number', models.CharField(blank=True, max_length=100, verbose_name='Reference #')),
                ('transaction_id', models.CharField(blank=True, max_length=100, verbose_name='Transaction ID')),
                ('card_last_four', models.CharField(blank=True, max_length=4, verbose_name='Card Last 4 Digits')),
                ('card_type', models.CharField(blank=True, max_length=50, verbose_name='Card Type')),
                ('bank_name', models.CharField(blank=True, max_length=100, verbose_name='Bank Name')),
                ('check_number', models.CharField(blank=True, max_length=50, verbose_name='Check Number')),
                ('insurance_claim_number', models.CharField(blank=True, max_length=100, verbose_name='Insurance Claim #')),
                ('insurance_payment_date', models.DateField(blank=True, null=True, verbose_name='Insurance Payment Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payments', to='setup.customer', verbose_name='Customer')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payments', to='billing.invoice', verbose_name='Invoice')),
                ('payment_method', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payments', to='billing.paymentmethod', verbose_name='Payment Method')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('item_type', models.CharField(choices=[('part', 'Part'), ('labor', 'Labor'), ('service', 'Service'), ('fee', 'Fee'), ('tax', 'Tax'), ('other', 'Other')], max_length=20, verbose_name='Item Type')),
                ('description', models.CharField(max_length=255, verbose_name='Description')),
                ('quantity', models.DecimalField(decimal_places=2, default=1, max_digits=10, verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='Unit Price')),
                ('part_id', models.CharField(blank=True, max_length=100, verbose_name='Part ID/SKU')),
                ('work_order_material_id', models.UUIDField(blank=True, null=True, verbose_name='Work Order Material ID')),
                ('work_order_operation_id', models.UUIDField(blank=True, null=True, verbose_name='Work Order Operation ID')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Discount %')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Discount Amount')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Tax %')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Tax Amount')),
                ('line_total', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Line Total')),
                ('is_covered_by_insurance', models.BooleanField(default=False, verbose_name='Covered by Insurance')),
                ('is_covered_by_warranty', models.BooleanField(default=False, verbose_name='Covered by Warranty')),
                ('coverage_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Coverage %')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='billing.invoice', verbose_name='Invoice')),
            ],
            options={
                'verbose_name': 'Invoice Item',
                'verbose_name_plural': 'Invoice Items',
                'ordering': ['item_type', 'description'],
            },
        ),
        migrations.AddField(
            model_name='invoice',
            name='vehicle_warranty',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices', to='billing.vehiclewarranty', verbose_name='Vehicle Warranty'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='work_order',
            field=models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='invoice', to='work_orders.workorder', verbose_name='Work Order'),
        ),
        migrations.CreateModel(
            name='CustomerPreference',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('status', models.CharField(choices=[('regular', 'Regular'), ('vip', 'VIP'), ('gold', 'Gold Member'), ('platinum', 'Platinum Member')], default='regular', max_length=20, verbose_name='Customer Status')),
                ('payment_terms', models.CharField(choices=[('cash', 'Cash'), ('credit_30', '30 Day Credit'), ('credit_60', '60 Day Credit'), ('credit_90', '90 Day Credit')], default='cash', max_length=20, verbose_name='Payment Terms')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Credit Limit')),
                ('default_discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Default Discount %')),
                ('send_sms_notifications', models.BooleanField(default=True, verbose_name='Send SMS Notifications')),
                ('send_email_notifications', models.BooleanField(default=True, verbose_name='Send Email Notifications')),
                ('special_instructions', models.TextField(blank=True, verbose_name='Special Instructions')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('customer', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to='setup.customer', verbose_name='Customer')),
            ],
            options={
                'verbose_name': 'Customer Preference',
                'verbose_name_plural': 'Customer Preferences',
            },
        ),
    ]
