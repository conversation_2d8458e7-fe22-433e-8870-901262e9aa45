# Generated by Django 4.2.20 on 2025-05-08 14:55

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('setup', '0003_remove_vehicle_owner_email_remove_vehicle_owner_name_and_more'),
        ('billing', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerClassification',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Classification Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('level', models.PositiveIntegerField(default=0, help_text='Higher numbers represent better classifications', verbose_name='Level')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='Icon')),
                ('color', models.CharField(blank=True, max_length=20, verbose_name='Color')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Default Discount %')),
                ('service_priority', models.PositiveIntegerField(default=0, help_text='Higher numbers get higher priority', verbose_name='Service Priority')),
                ('credit_limit_multiplier', models.DecimalField(decimal_places=2, default=1.0, max_digits=5, verbose_name='Credit Limit Multiplier')),
                ('extended_payment_days', models.PositiveIntegerField(default=0, verbose_name='Extended Payment Days')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Customer Classification',
                'verbose_name_plural': 'Customer Classifications',
                'ordering': ['level'],
            },
        ),
        migrations.CreateModel(
            name='CustomerClassificationHistory',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('change_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Change Date')),
                ('automatic', models.BooleanField(default=True, verbose_name='Automatic Change')),
                ('changed_by', models.UUIDField(blank=True, null=True, verbose_name='Changed By User ID')),
                ('reason', models.TextField(blank=True, verbose_name='Reason for Change')),
                ('evaluation_data', models.JSONField(blank=True, default=dict, help_text='Details of the evaluation that led to this change', verbose_name='Evaluation Data')),
                ('notification_sent', models.BooleanField(default=False, verbose_name='Notification Sent')),
                ('notification_date', models.DateTimeField(blank=True, null=True, verbose_name='Notification Date')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='classification_history', to='setup.customer', verbose_name='Customer')),
                ('new_classification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='new_customers', to='billing.customerclassification', verbose_name='New Classification')),
                ('previous_classification', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='previous_customers', to='billing.customerclassification', verbose_name='Previous Classification')),
            ],
            options={
                'verbose_name': 'Customer Classification History',
                'verbose_name_plural': 'Customer Classification History',
                'ordering': ['-change_date'],
            },
        ),
        migrations.CreateModel(
            name='ClassificationCriteria',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('criteria_type', models.CharField(choices=[('spend_amount', 'Total Spend Amount'), ('spend_frequency', 'Spend Frequency'), ('visit_count', 'Visit Count'), ('average_spend', 'Average Spend'), ('payment_history', 'Payment History'), ('vehicle_count', 'Vehicle Count'), ('vehicle_value', 'Vehicle Value'), ('service_type_usage', 'Service Type Usage'), ('referrals', 'Referrals Made'), ('days_since_first', 'Days Since First Visit'), ('loyalty_duration', 'Loyalty Duration'), ('custom', 'Custom Criteria')], max_length=50, verbose_name='Criteria Type')),
                ('operator', models.CharField(choices=[('gt', 'Greater Than'), ('gte', 'Greater Than or Equal'), ('lt', 'Less Than'), ('lte', 'Less Than or Equal'), ('eq', 'Equal To'), ('between', 'Between')], max_length=20, verbose_name='Operator')),
                ('value', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='Value')),
                ('value2', models.DecimalField(blank=True, decimal_places=2, help_text='For "between" operator', max_digits=15, null=True, verbose_name='Second Value')),
                ('period', models.CharField(choices=[('all_time', 'All Time'), ('year', 'Last Year'), ('6_months', 'Last 6 Months'), ('3_months', 'Last 3 Months'), ('month', 'Last Month')], default='all_time', max_length=20, verbose_name='Time Period')),
                ('weight', models.DecimalField(decimal_places=2, default=1.0, help_text='Weight factor for this criteria', max_digits=5, verbose_name='Weight')),
                ('custom_evaluation', models.TextField(blank=True, help_text='JSON or description of custom evaluation logic', verbose_name='Custom Evaluation')),
                ('is_required', models.BooleanField(default=False, help_text='Customer must meet this criteria regardless of other criteria', verbose_name='Is Required')),
                ('description', models.CharField(blank=True, max_length=255, verbose_name='Description')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('classification', models.ForeignKey(help_text='Classification this criteria applies to', on_delete=django.db.models.deletion.CASCADE, related_name='criteria', to='billing.customerclassification', verbose_name='Classification')),
            ],
            options={
                'verbose_name': 'Classification Criteria',
                'verbose_name_plural': 'Classification Criteria',
                'ordering': ['classification__level', 'criteria_type'],
            },
        ),
    ]
