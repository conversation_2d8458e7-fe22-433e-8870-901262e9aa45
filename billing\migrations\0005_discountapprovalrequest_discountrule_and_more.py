# Generated by Django 4.2.20 on 2025-06-21 09:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("inventory", "0026_remove_batchmovement_destination_batch_and_more"),
        ("setup", "0016_userrole_userprofile_technicianspecialization_and_more"),
        ("sales", "0004_salesorder_labor_cost_salesorder_parts_cost_and_more"),
        ("billing", "0004_alter_classificationcriteria_tenant_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="DiscountApprovalRequest",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "discount_type",
                    models.CharField(
                        choices=[
                            ("manual", "Manual Discount"),
                            ("rule_based", "Rule Based"),
                            ("combined", "Combined"),
                        ],
                        max_length=20,
                        verbose_name="Discount Type",
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=5,
                        verbose_name="Discount Percentage",
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Discount Amount"
                    ),
                ),
                (
                    "original_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Original Amount"
                    ),
                ),
                (
                    "applicable_to",
                    models.CharField(
                        choices=[
                            ("single_part", "Single Part"),
                            ("all_parts", "All Parts"),
                            ("single_operation", "Single Operation"),
                            ("all_operations", "All Operations"),
                            ("full_invoice", "Full Invoice"),
                            ("category", "Category"),
                            ("customer_type", "Customer Type"),
                        ],
                        max_length=20,
                        verbose_name="Applicable To",
                    ),
                ),
                (
                    "target_items",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Target Items"
                    ),
                ),
                (
                    "reason",
                    models.TextField(
                        help_text="Justification for discount", verbose_name="Reason"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "approved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Approved At"
                    ),
                ),
                (
                    "rejection_reason",
                    models.TextField(blank=True, verbose_name="Rejection Reason"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Expires At"
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_discounts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approved By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Discount Approval Request",
                "verbose_name_plural": "Discount Approval Requests",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DiscountRule",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="Rule Name")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "rule_type",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage"),
                            ("fixed_amount", "Fixed Amount"),
                            ("buy_x_get_y", "Buy X Get Y"),
                            ("category_discount", "Category Discount"),
                            ("loyalty_discount", "Loyalty Discount"),
                        ],
                        max_length=20,
                        verbose_name="Rule Type",
                    ),
                ),
                (
                    "applicable_to",
                    models.CharField(
                        choices=[
                            ("single_part", "Single Part"),
                            ("all_parts", "All Parts"),
                            ("single_operation", "Single Operation"),
                            ("all_operations", "All Operations"),
                            ("full_invoice", "Full Invoice"),
                            ("category", "Category"),
                            ("customer_type", "Customer Type"),
                        ],
                        max_length=20,
                        verbose_name="Applicable To",
                    ),
                ),
                (
                    "percentage_discount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name="Percentage Discount",
                    ),
                ),
                (
                    "fixed_amount_discount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Fixed Amount Discount",
                    ),
                ),
                (
                    "buy_quantity",
                    models.PositiveIntegerField(default=1, verbose_name="Buy Quantity"),
                ),
                (
                    "get_quantity",
                    models.PositiveIntegerField(default=1, verbose_name="Get Quantity"),
                ),
                (
                    "get_discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=100,
                        max_digits=5,
                        verbose_name="Get Item Discount %",
                    ),
                ),
                (
                    "min_order_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Minimum Order Amount",
                    ),
                ),
                (
                    "max_discount_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=15,
                        null=True,
                        verbose_name="Maximum Discount Amount",
                    ),
                ),
                (
                    "valid_from",
                    models.DateField(blank=True, null=True, verbose_name="Valid From"),
                ),
                (
                    "valid_to",
                    models.DateField(blank=True, null=True, verbose_name="Valid To"),
                ),
                (
                    "target_categories",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Target Categories"
                    ),
                ),
                (
                    "customer_types",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Customer Types"
                    ),
                ),
                (
                    "priority",
                    models.IntegerField(
                        choices=[
                            (1, "Low"),
                            (2, "Medium"),
                            (3, "High"),
                            (4, "Critical"),
                        ],
                        default=2,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "can_combine_with_manual",
                    models.BooleanField(
                        default=False, verbose_name="Can Combine with Manual Discount"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "requires_approval",
                    models.BooleanField(
                        default=False, verbose_name="Requires Approval"
                    ),
                ),
                (
                    "specific_customers",
                    models.ManyToManyField(
                        blank=True,
                        to="setup.customer",
                        verbose_name="Specific Customers",
                    ),
                ),
                (
                    "target_items",
                    models.ManyToManyField(
                        blank=True, to="inventory.item", verbose_name="Target Items"
                    ),
                ),
            ],
            options={
                "verbose_name": "Discount Rule",
                "verbose_name_plural": "Discount Rules",
                "ordering": ["-priority", "name"],
            },
        ),
        migrations.AddField(
            model_name="invoice",
            name="sales_order",
            field=models.ForeignKey(
                blank=True,
                help_text="Sales order this invoice was generated from",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="invoices",
                to="sales.salesorder",
                verbose_name="Sales Order",
            ),
        ),
        migrations.CreateModel(
            name="InvoiceDiscount",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "discount_source",
                    models.CharField(
                        choices=[
                            ("manual", "Manual"),
                            ("rule", "Rule Based"),
                            ("loyalty", "Loyalty Program"),
                            ("promotion", "Promotion"),
                        ],
                        max_length=20,
                        verbose_name="Discount Source",
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name="Discount Percentage",
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Discount Amount"
                    ),
                ),
                (
                    "original_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Original Amount"
                    ),
                ),
                (
                    "applicable_to",
                    models.CharField(
                        choices=[
                            ("single_part", "Single Part"),
                            ("all_parts", "All Parts"),
                            ("single_operation", "Single Operation"),
                            ("all_operations", "All Operations"),
                            ("full_invoice", "Full Invoice"),
                            ("category", "Category"),
                            ("customer_type", "Customer Type"),
                        ],
                        max_length=20,
                        verbose_name="Applicable To",
                    ),
                ),
                (
                    "target_items",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Target Items"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "applied_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="applied_discounts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Applied By",
                    ),
                ),
                (
                    "approval_request",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="applied_discounts",
                        to="billing.discountapprovalrequest",
                        verbose_name="Approval Request",
                    ),
                ),
                (
                    "discount_rule",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="applied_discounts",
                        to="billing.discountrule",
                        verbose_name="Discount Rule",
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="applied_discounts",
                        to="billing.invoice",
                        verbose_name="Invoice",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice Discount",
                "verbose_name_plural": "Invoice Discounts",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddField(
            model_name="discountapprovalrequest",
            name="invoice",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="discount_requests",
                to="billing.invoice",
                verbose_name="Invoice",
            ),
        ),
        migrations.AddField(
            model_name="discountapprovalrequest",
            name="requested_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="discount_requests",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Requested By",
            ),
        ),
        migrations.CreateModel(
            name="CompanyDiscountLimit",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "cashier_max_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=5,
                        max_digits=5,
                        verbose_name="Cashier Max Percentage",
                    ),
                ),
                (
                    "cashier_max_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=1000,
                        max_digits=15,
                        verbose_name="Cashier Max Amount",
                    ),
                ),
                (
                    "manager_max_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=15,
                        max_digits=5,
                        verbose_name="Manager Max Percentage",
                    ),
                ),
                (
                    "manager_max_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=5000,
                        max_digits=15,
                        verbose_name="Manager Max Amount",
                    ),
                ),
                (
                    "company_manager_max_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=50,
                        max_digits=5,
                        verbose_name="Company Manager Max Percentage",
                    ),
                ),
                (
                    "company_manager_max_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=50000,
                        max_digits=15,
                        verbose_name="Company Manager Max Amount",
                    ),
                ),
                (
                    "company",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="discount_limit",
                        to="setup.company",
                        verbose_name="Company",
                    ),
                ),
            ],
            options={
                "verbose_name": "Company Discount Limit",
                "verbose_name_plural": "Company Discount Limits",
            },
        ),
    ]
