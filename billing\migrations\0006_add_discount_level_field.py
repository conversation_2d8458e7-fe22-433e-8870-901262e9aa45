# Generated by Django migration
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0005_discountapprovalrequest_discountrule_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoicediscount',
            name='discount_level',
            field=models.CharField(
                choices=[
                    ('invoice', 'Invoice Level'),
                    ('item', 'Item Level'),
                    ('category', 'Category Level'),
                ],
                default='invoice',
                max_length=20,
                verbose_name='Discount Level'
            ),
        ),
        migrations.AddField(
            model_name='invoicediscount',
            name='target_categories',
            field=models.JSONField(
                blank=True,
                default=list,
                verbose_name='Target Categories'
            ),
        ),
        migrations.AddField(
            model_name='invoicediscount',
            name='target_item_ids',
            field=models.JSONField(
                blank=True,
                default=list,
                verbose_name='Target Item IDs'
            ),
        ),
        migrations.AddField(
            model_name='invoicediscount',
            name='percentage',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=5,
                verbose_name='Percentage'
            ),
        ),
        migrations.AddField(
            model_name='invoicediscount',
            name='fixed_amount',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=15,
                verbose_name='Fixed Amount'
            ),
        ),
        migrations.AddField(
            model_name='invoicediscount',
            name='reason',
            field=models.CharField(
                blank=True,
                max_length=255,
                verbose_name='Reason'
            ),
        ),
    ] 