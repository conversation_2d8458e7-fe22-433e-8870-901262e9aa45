# Generated by Django 4.2.20 on 2025-06-22 11:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("billing", "0006_add_discount_level_field"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="invoicediscount",
            name="applicable_to",
        ),
        migrations.RemoveField(
            model_name="invoicediscount",
            name="approval_request",
        ),
        migrations.RemoveField(
            model_name="invoicediscount",
            name="discount_percentage",
        ),
        migrations.RemoveField(
            model_name="invoicediscount",
            name="notes",
        ),
        migrations.RemoveField(
            model_name="invoicediscount",
            name="target_items",
        ),
        migrations.AddField(
            model_name="invoicediscount",
            name="approved_at",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Approved At"
            ),
        ),
        migrations.AddField(
            model_name="invoicediscount",
            name="approved_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="approved_discounts",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Approved By",
            ),
        ),
        migrations.AddField(
            model_name="invoicediscount",
            name="discount_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="applied_discounts",
                to="billing.discounttype",
                verbose_name="Discount Type",
            ),
        ),
        migrations.AddField(
            model_name="invoicediscount",
            name="requires_approval",
            field=models.BooleanField(default=False, verbose_name="Requires Approval"),
        ),
        migrations.AlterField(
            model_name="discountapprovalrequest",
            name="approved_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="approved_discount_requests",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Approved By",
            ),
        ),
        migrations.AlterField(
            model_name="invoicediscount",
            name="applied_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="applied_discounts",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Applied By",
            ),
        ),
        migrations.AlterField(
            model_name="invoicediscount",
            name="discount_level",
            field=models.CharField(
                choices=[
                    ("invoice", "Invoice Level"),
                    ("item", "Item Level"),
                    ("category", "Category Level"),
                ],
                max_length=20,
                verbose_name="Discount Level",
            ),
        ),
        migrations.AlterField(
            model_name="invoicediscount",
            name="discount_source",
            field=models.CharField(
                choices=[
                    ("manual", "Manual"),
                    ("rule", "Automatic Rule"),
                    ("customer_preference", "Customer Preference"),
                    ("promotion", "Promotion"),
                ],
                max_length=20,
                verbose_name="Discount Source",
            ),
        ),
        migrations.CreateModel(
            name="InvoiceItemDiscount",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "original_line_total",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=15,
                        verbose_name="Original Line Total",
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Discount Amount"
                    ),
                ),
                (
                    "final_line_total",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Final Line Total"
                    ),
                ),
                (
                    "invoice_discount",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="item_applications",
                        to="billing.invoicediscount",
                        verbose_name="Invoice Discount",
                    ),
                ),
                (
                    "invoice_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="item_discounts",
                        to="billing.invoiceitem",
                        verbose_name="Invoice Item",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice Item Discount",
                "verbose_name_plural": "Invoice Item Discounts",
            },
        ),
        migrations.CreateModel(
            name="DiscountConflict",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "conflict_type",
                    models.CharField(
                        choices=[
                            ("overlapping_item", "Overlapping Item Discount"),
                            ("category_vs_item", "Category vs Item Conflict"),
                            ("invoice_vs_item", "Invoice vs Item Conflict"),
                            ("rule_combination", "Rule Combination Conflict"),
                        ],
                        max_length=30,
                        verbose_name="Conflict Type",
                    ),
                ),
                ("description", models.TextField(verbose_name="Conflict Description")),
                (
                    "conflicting_discounts",
                    models.JSONField(
                        default=list, verbose_name="Conflicting Discount IDs"
                    ),
                ),
                (
                    "resolution",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("override_allowed", "Override Allowed"),
                            ("highest_priority", "Highest Priority Applied"),
                            ("user_choice", "User Choice Applied"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Resolution",
                    ),
                ),
                (
                    "resolved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Resolved At"
                    ),
                ),
                (
                    "resolution_notes",
                    models.TextField(blank=True, verbose_name="Resolution Notes"),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="discount_conflicts",
                        to="billing.invoice",
                        verbose_name="Invoice",
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_conflicts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Resolved By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Discount Conflict",
                "verbose_name_plural": "Discount Conflicts",
                "ordering": ["-created_at"],
            },
        ),
    ]
