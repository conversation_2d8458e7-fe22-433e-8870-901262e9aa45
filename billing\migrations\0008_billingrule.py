# Generated by Django 4.2.20 on 2025-07-06 10:41

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("billing", "0007_remove_invoicediscount_applicable_to_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="BillingRule",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Rule Name")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "rule_type",
                    models.CharField(
                        choices=[
                            ("payment_terms", "Payment Terms"),
                            ("credit_limit", "Credit Limit"),
                            ("discount_auto", "Automatic Discount"),
                            ("tax_calculation", "Tax Calculation"),
                            ("late_fee", "Late Fee"),
                            ("early_payment", "Early Payment Discount"),
                        ],
                        max_length=20,
                        verbose_name="Rule Type",
                    ),
                ),
                (
                    "trigger",
                    models.CharField(
                        choices=[
                            ("invoice_creation", "Invoice Creation"),
                            ("payment_due", "Payment Due"),
                            ("payment_received", "Payment Received"),
                            ("overdue", "Overdue"),
                            ("customer_type", "Customer Type"),
                        ],
                        max_length=20,
                        verbose_name="Trigger",
                    ),
                ),
                (
                    "conditions",
                    models.JSONField(
                        default=dict,
                        help_text="JSON conditions for rule application",
                        verbose_name="Conditions",
                    ),
                ),
                (
                    "actions",
                    models.JSONField(
                        default=dict,
                        help_text="JSON actions to perform when rule matches",
                        verbose_name="Actions",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "priority",
                    models.IntegerField(
                        default=0,
                        help_text="Higher numbers have higher priority",
                        verbose_name="Priority",
                    ),
                ),
                (
                    "usage_count",
                    models.IntegerField(default=0, verbose_name="Usage Count"),
                ),
                (
                    "last_used",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Used"
                    ),
                ),
            ],
            options={
                "verbose_name": "Billing Rule",
                "verbose_name_plural": "Billing Rules",
                "ordering": ["-priority", "name"],
            },
        ),
    ]
