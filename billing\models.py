from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet
from django.utils import timezone
from setup.models import Customer, Vehicle, ServiceCenter
from work_orders.models import WorkOrder
from decimal import Decimal
import uuid

# Import classification models to make them available to Django
from .models_classification import CustomerClassification, ClassificationCriteria, CustomerClassificationHistory

# Import DiscountRule and BillingRule from models_rules
from .models_rules import DiscountRule, BillingRule


class CustomerPreference(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Customer preferences and special statuses
    """
    CUSTOMER_STATUS_CHOICES = [
        ('regular', _('Regular')),
        ('vip', _('VIP')),
        ('gold', _('Gold Member')),
        ('platinum', _('Platinum Member')),
    ]
    
    PAYMENT_TERMS_CHOICES = [
        ('cash', _('Cash')),
        ('credit_30', _('30 Day Credit')),
        ('credit_60', _('60 Day Credit')),
        ('credit_90', _('90 Day Credit')),
    ]
    
    customer = models.OneToOneField(
        Customer,
        on_delete=models.CASCADE,
        related_name='preferences',
        verbose_name=_('Customer')
    )
    
    # Customer Status
    status = models.CharField(
        _('Customer Status'),
        max_length=20,
        choices=CUSTOMER_STATUS_CHOICES,
        default='regular'
    )
    
    # Payment Preferences
    payment_terms = models.CharField(
        _('Payment Terms'),
        max_length=20,
        choices=PAYMENT_TERMS_CHOICES,
        default='cash'
    )
    
    credit_limit = models.DecimalField(
        _('Credit Limit'),
        max_digits=15, 
        decimal_places=2,
        default=0
    )
    
    # Discount Settings
    default_discount_percentage = models.DecimalField(
        _('Default Discount %'),
        max_digits=5,
        decimal_places=2,
        default=0
    )
    
    # Communication Preferences
    send_sms_notifications = models.BooleanField(_('Send SMS Notifications'), default=True)
    send_email_notifications = models.BooleanField(_('Send Email Notifications'), default=True)
    
    # Special Notes
    special_instructions = models.TextField(_('Special Instructions'), blank=True)
    
    # Other Settings
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Customer Preference')
        verbose_name_plural = _('Customer Preferences')
    
    def __str__(self):
        return f"{self.customer.full_name} - {self.get_status_display()}"


class InsuranceCompany(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Insurance company information
    """
    name = models.CharField(_('Company Name'), max_length=255)
    code = models.CharField(_('Company Code'), max_length=50)
    contact_person = models.CharField(_('Contact Person'), max_length=255, blank=True)
    phone = models.CharField(_('Phone'), max_length=50, blank=True)
    email = models.EmailField(_('Email'), blank=True)
    address = models.TextField(_('Address'), blank=True)
    
    # Contract Details
    contract_number = models.CharField(_('Contract Number'), max_length=100, blank=True)
    contract_start_date = models.DateField(_('Contract Start Date'), null=True, blank=True)
    contract_end_date = models.DateField(_('Contract End Date'), null=True, blank=True)
    
    # Processing Details
    approval_required = models.BooleanField(_('Approval Required'), default=True)
    standard_approval_time_hours = models.PositiveIntegerField(_('Standard Approval Time (hours)'), default=24)
    
    # Other Settings
    is_active = models.BooleanField(_('Is Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Insurance Company')
        verbose_name_plural = _('Insurance Companies')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class InsurancePolicy(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Vehicle insurance policy information
    """
    POLICY_TYPE_CHOICES = [
        ('comprehensive', _('Comprehensive')),
        ('third_party', _('Third Party')),
        ('partial', _('Partial Coverage')),
    ]
    
    insurance_company = models.ForeignKey(
        InsuranceCompany,
        on_delete=models.PROTECT,
        related_name='policies',
        verbose_name=_('Insurance Company')
    )
    
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.CASCADE,
        related_name='insurance_policies',
        verbose_name=_('Vehicle')
    )
    
    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='insurance_policies',
        verbose_name=_('Policy Holder')
    )
    
    # Policy Details
    policy_number = models.CharField(_('Policy Number'), max_length=100)
    policy_type = models.CharField(_('Policy Type'), max_length=20, choices=POLICY_TYPE_CHOICES)
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'))
    
    # Coverage Details
    coverage_amount = models.DecimalField(_('Coverage Amount'), max_digits=15, decimal_places=2)
    deductible = models.DecimalField(_('Deductible'), max_digits=15, decimal_places=2, default=0)
    
    # Status
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    # Other Settings
    notes = models.TextField(_('Notes'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Insurance Policy')
        verbose_name_plural = _('Insurance Policies')
        ordering = ['-start_date']
    
    def __str__(self):
        return f"{self.policy_number} - {self.vehicle}"
    
    @property
    def is_expired(self):
        return self.end_date < timezone.now().date() if self.end_date else False


class WarrantyType(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Types of warranties
    """
    name = models.CharField(_('Warranty Type'), max_length=100)
    description = models.TextField(_('Description'), blank=True)
    
    # Coverage Details
    parts_covered = models.TextField(_('Parts Covered'), blank=True)
    labor_covered = models.BooleanField(_('Labor Covered'), default=False)
    
    # Duration
    default_duration_months = models.PositiveIntegerField(_('Default Duration (months)'), default=12)
    default_mileage_limit = models.PositiveIntegerField(_('Default Mileage Limit'), null=True, blank=True)
    
    # Other Settings
    is_active = models.BooleanField(_('Is Active'), default=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Warranty Type')
        verbose_name_plural = _('Warranty Types')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class VehicleWarranty(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Vehicle warranty information
    """
    WARRANTY_PROVIDER_CHOICES = [
        ('manufacturer', _('Manufacturer')),
        ('dealer', _('Dealer')),
        ('third_party', _('Third Party')),
        ('extended', _('Extended')),
    ]
    
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.CASCADE,
        related_name='warranties',
        verbose_name=_('Vehicle')
    )
    
    warranty_type = models.ForeignKey(
        WarrantyType,
        on_delete=models.PROTECT,
        related_name='vehicle_warranties',
        verbose_name=_('Warranty Type')
    )
    
    # Warranty Details
    warranty_number = models.CharField(_('Warranty Number'), max_length=100, blank=True)
    provider = models.CharField(_('Provider'), max_length=20, choices=WARRANTY_PROVIDER_CHOICES)
    provider_name = models.CharField(_('Provider Name'), max_length=255, blank=True)
    
    # Coverage Period
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'), null=True, blank=True)
    mileage_limit = models.PositiveIntegerField(_('Mileage Limit'), null=True, blank=True)
    
    # Status
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    # Other Settings
    notes = models.TextField(_('Notes'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Vehicle Warranty')
        verbose_name_plural = _('Vehicle Warranties')
        ordering = ['-start_date']
    
    def __str__(self):
        return f"{self.vehicle} - {self.warranty_type}"
    
    @property
    def is_expired_by_date(self):
        return self.end_date < timezone.now().date() if self.end_date else False
    
    @property
    def is_expired_by_mileage(self):
        if not self.mileage_limit or not self.vehicle:
            return False
        
        # Get latest odometer reading
        latest_work_order = WorkOrder.objects.filter(
            vehicle=self.vehicle,
            current_odometer__isnull=False
        ).order_by('-created_at').first()
        
        if not latest_work_order:
            return False
            
        return latest_work_order.current_odometer > self.mileage_limit


class DiscountType(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Types of discounts
    """
    DISCOUNT_METHOD_CHOICES = [
        ('percentage', _('Percentage')),
        ('fixed', _('Fixed Amount')),
    ]
    
    name = models.CharField(_('Discount Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True)
    discount_method = models.CharField(_('Discount Method'), max_length=20, choices=DISCOUNT_METHOD_CHOICES)
    
    # Discount Value
    percentage = models.DecimalField(_('Percentage'), max_digits=5, decimal_places=2, default=0)
    fixed_amount = models.DecimalField(_('Fixed Amount'), max_digits=15, decimal_places=2, default=0)
    
    # Validity
    valid_from = models.DateField(_('Valid From'), null=True, blank=True)
    valid_to = models.DateField(_('Valid To'), null=True, blank=True)
    
    # Restrictions
    min_order_amount = models.DecimalField(_('Minimum Order Amount'), max_digits=15, decimal_places=2, default=0)
    max_discount_amount = models.DecimalField(_('Maximum Discount Amount'), max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Status
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    # Other Settings
    apply_to_parts = models.BooleanField(_('Apply to Parts'), default=True)
    apply_to_labor = models.BooleanField(_('Apply to Labor'), default=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Discount Type')
        verbose_name_plural = _('Discount Types')
        ordering = ['name']
    
    def __str__(self):
        if self.discount_method == 'percentage':
            return f"{self.name} ({self.percentage}%)"
        return f"{self.name} ({self.fixed_amount})"
    
    @property
    def is_valid(self):
        today = timezone.now().date()
        if self.valid_from and self.valid_from > today:
            return False
        if self.valid_to and self.valid_to < today:
            return False
        return self.is_active
    
    def calculate_discount(self, amount):
        """Calculate discount amount for a given value"""
        if not self.is_valid:
            return Decimal('0.00')
            
        if amount < self.min_order_amount:
            return Decimal('0.00')
            
        if self.discount_method == 'fixed':
            return min(self.fixed_amount, amount)
            
        # Percentage discount
        discount = (amount * self.percentage) / Decimal('100.00')
        
        # Apply maximum if set
        if self.max_discount_amount:
            discount = min(discount, self.max_discount_amount)
            
        return min(discount, amount)  # Discount can't be more than amount




class PaymentMethod(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Payment methods
    """
    PAYMENT_TYPE_CHOICES = [
        ('cash', _('Cash')),
        ('credit_card', _('Credit Card')),
        ('debit_card', _('Debit Card')),
        ('bank_transfer', _('Bank Transfer')),
        ('check', _('Check')),
        ('digital_wallet', _('Digital Wallet')),
        ('credit', _('Store Credit')),
        ('insurance', _('Insurance')),
    ]
    
    name = models.CharField(_('Payment Method'), max_length=100)
    payment_type = models.CharField(_('Payment Type'), max_length=20, choices=PAYMENT_TYPE_CHOICES)
    description = models.TextField(_('Description'), blank=True)
    
    # Processing Details
    processing_fee_percentage = models.DecimalField(_('Processing Fee %'), max_digits=5, decimal_places=2, default=0)
    processing_fee_fixed = models.DecimalField(_('Processing Fee Fixed'), max_digits=10, decimal_places=2, default=0)
    
    # Status
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    # Other Settings
    requires_approval = models.BooleanField(_('Requires Approval'), default=False)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Payment Method')
        verbose_name_plural = _('Payment Methods')
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def calculate_processing_fee(self, amount):
        """Calculate processing fee for a given amount"""
        percentage_fee = (amount * self.processing_fee_percentage) / Decimal('100.00')
        return percentage_fee + self.processing_fee_fixed


class Invoice(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Invoice for work orders
    """
    INVOICE_STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('issued', _('Issued')),
        ('partially_paid', _('Partially Paid')),
        ('paid', _('Paid')),
        ('overdue', _('Overdue')),
        ('cancelled', _('Cancelled')),
    ]
    
    TAX_METHOD_CHOICES = [
        ('inclusive', _('Tax Inclusive')),
        ('exclusive', _('Tax Exclusive')),
    ]
    
    # Relationships
    work_order = models.OneToOneField(
        WorkOrder,
        on_delete=models.PROTECT,
        related_name='invoice',
        verbose_name=_('Work Order')
    )
    
    # Sales Order Integration - NEW FIELD
    sales_order = models.ForeignKey(
        'sales.SalesOrder',
        on_delete=models.PROTECT,
        related_name='invoices',
        verbose_name=_('Sales Order'),
        null=True, blank=True,
        help_text=_('Sales order this invoice was generated from')
    )
    
    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='invoices',
        verbose_name=_('Customer')
    )
    
    service_center = models.ForeignKey(
        ServiceCenter,
        on_delete=models.PROTECT,
        related_name='invoices',
        verbose_name=_('Service Center')
    )
    
    # Basic Invoice Information
    invoice_number = models.CharField(_('Invoice #'), max_length=50, unique=True)
    invoice_date = models.DateField(_('Invoice Date'), default=timezone.now)
    due_date = models.DateField(_('Due Date'))
    status = models.CharField(_('Status'), max_length=20, choices=INVOICE_STATUS_CHOICES, default='draft')
    
    # Insurance and Warranty
    insurance_policy = models.ForeignKey(
        InsurancePolicy,
        on_delete=models.SET_NULL,
        related_name='invoices',
        verbose_name=_('Insurance Policy'),
        null=True, blank=True
    )
    
    vehicle_warranty = models.ForeignKey(
        VehicleWarranty,
        on_delete=models.SET_NULL,
        related_name='invoices',
        verbose_name=_('Vehicle Warranty'),
        null=True, blank=True
    )
    
    # Discounts
    discount_type = models.ForeignKey(
        DiscountType,
        on_delete=models.SET_NULL,
        related_name='invoices',
        verbose_name=_('Discount Type'),
        null=True, blank=True
    )
    
    discount_amount = models.DecimalField(_('Discount Amount'), max_digits=15, decimal_places=2, default=0)
    
    # Amount Fields
    subtotal = models.DecimalField(_('Subtotal'), max_digits=15, decimal_places=2, default=0)
    tax_method = models.CharField(_('Tax Method'), max_length=20, choices=TAX_METHOD_CHOICES, default='exclusive')
    tax_percentage = models.DecimalField(_('Tax Percentage'), max_digits=5, decimal_places=2, default=0)
    tax_amount = models.DecimalField(_('Tax Amount'), max_digits=15, decimal_places=2, default=0)
    total_amount = models.DecimalField(_('Total Amount'), max_digits=15, decimal_places=2, default=0)
    amount_paid = models.DecimalField(_('Amount Paid'), max_digits=15, decimal_places=2, default=0)
    amount_due = models.DecimalField(_('Amount Due'), max_digits=15, decimal_places=2, default=0)
    
    # Other Settings
    notes = models.TextField(_('Notes'), blank=True)
    terms_and_conditions = models.TextField(_('Terms and Conditions'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Invoice')
        verbose_name_plural = _('Invoices')
        ordering = ['-invoice_date', '-created_at']
    
    def __str__(self):
        return f"{self.invoice_number} - {self.customer.full_name}"
    
    def save(self, *args, **kwargs):
        # If no invoice number provided, generate one
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
            
        # Calculate totals
        self.calculate_totals()
        
        super().save(*args, **kwargs)
    
    def generate_invoice_number(self):
        """Generate a unique invoice number"""
        prefix = 'INV'
        year = timezone.now().strftime('%Y')
        month = timezone.now().strftime('%m')
        
        # Get the last invoice number for this tenant
        last_invoice = Invoice.objects.filter(
            tenant_id=self.tenant_id,
            invoice_number__startswith=f"{prefix}-{year}{month}"
        ).order_by('invoice_number').last()
        
        if last_invoice:
            # Extract the sequence number and increment
            try:
                seq = int(last_invoice.invoice_number.split('-')[-1])
                seq += 1
            except (IndexError, ValueError):
                seq = 1
        else:
            seq = 1
            
        return f"{prefix}-{year}{month}-{seq:04d}"
    
    def calculate_totals(self):
        """Calculate invoice totals"""
        # Calculate from invoice items if they exist
        if hasattr(self, 'items'):
            self.subtotal = sum(item.line_total for item in self.items.all())
        
        # Apply discount
        if self.discount_type and self.discount_amount:
            self.subtotal = max(self.subtotal - self.discount_amount, Decimal('0.00'))
        
        # Calculate tax
        if self.tax_method == 'exclusive':
            self.tax_amount = (self.subtotal * self.tax_percentage) / Decimal('100.00')
            self.total_amount = self.subtotal + self.tax_amount
        else:
            # For inclusive tax, subtotal already includes tax
            tax_divisor = Decimal('100.00') + self.tax_percentage
            self.tax_amount = self.subtotal * (self.tax_percentage / tax_divisor)
            self.total_amount = self.subtotal
        
        # Calculate amount due
        self.amount_due = self.total_amount - self.amount_paid
        
        # Update status based on payment
        if self.amount_due <= 0:
            self.status = 'paid'
        elif self.amount_paid > 0:
            self.status = 'partially_paid'
        elif self.status == 'draft':
            pass  # Leave as draft
        else:
            # Check if overdue
            if self.due_date and self.due_date < timezone.now().date():
                self.status = 'overdue'
            else:
                self.status = 'issued'
    
    def update_status(self):
        """
        Update invoice status based on current payments and due date
        """
        # Calculate amount due
        self.amount_due = self.total_amount - self.amount_paid
        
        # Update status based on payment and due date
        if self.amount_due <= 0:
            self.status = 'paid'
        elif self.amount_paid > 0:
            # Check if overdue
            if self.due_date and self.due_date < timezone.now().date():
                self.status = 'overdue'
            else:
                self.status = 'partially_paid'
        elif self.status == 'draft':
            pass  # Leave as draft
        else:
            # Check if overdue
            if self.due_date and self.due_date < timezone.now().date():
                self.status = 'overdue'
            else:
                self.status = 'issued'
        
        return self.status
    
    @classmethod
    def refresh_all_statuses(cls, tenant_id=None):
        """
        Refresh status for all invoices (or specific tenant)
        """
        queryset = cls.objects.all()
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
        
        updated_count = 0
        for invoice in queryset:
            old_status = invoice.status
            invoice.update_status()
            if old_status != invoice.status:
                invoice.save(update_fields=['status', 'amount_due'])
                updated_count += 1
        
        return updated_count

    @property
    def is_overdue(self):
        """Check if invoice is overdue"""
        return self.due_date < timezone.now().date() and self.amount_due > 0
    
    def can_apply_discount_type(self, discount_source):
        """
        Check if a discount type can be applied to this invoice.
        Implements the "one discount type per invoice" rule.
        """
        # Get currently applied discount types
        applied_types = self.get_applied_discount_types()
        
        # If no discounts applied yet, allow any type
        if not applied_types:
            return True
        
        # If the same type is already applied, allow it (for multiple discounts of same type)
        if discount_source in applied_types:
            return True
        
        # Otherwise, don't allow different types
        return False
    
    def get_applied_discount_types(self):
        """
        Get list of discount types currently applied to this invoice.
        """
        return list(self.applied_discounts.values_list('discount_source', flat=True).distinct())


class InvoiceItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Line items for invoices
    """
    ITEM_TYPE_CHOICES = [
        ('part', _('Part')),
        ('labor', _('Labor')),
        ('service', _('Service')),
        ('fee', _('Fee')),
        ('tax', _('Tax')),
        ('other', _('Other')),
    ]
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Invoice')
    )
    
    # Item Details
    item_type = models.CharField(_('Item Type'), max_length=20, choices=ITEM_TYPE_CHOICES)
    description = models.CharField(_('Description'), max_length=255)
    quantity = models.DecimalField(_('Quantity'), max_digits=10, decimal_places=2, default=1)
    unit_price = models.DecimalField(_('Unit Price'), max_digits=15, decimal_places=2)
    
    # Optional references
    part_id = models.CharField(_('Part ID/SKU'), max_length=100, blank=True)
    work_order_material_id = models.UUIDField(_('Work Order Material ID'), null=True, blank=True)
    work_order_operation_id = models.UUIDField(_('Work Order Operation ID'), null=True, blank=True)
    
    # Discount
    discount_percentage = models.DecimalField(_('Discount %'), max_digits=5, decimal_places=2, default=0)
    discount_amount = models.DecimalField(_('Discount Amount'), max_digits=15, decimal_places=2, default=0)
    
    # Tax
    tax_percentage = models.DecimalField(_('Tax %'), max_digits=5, decimal_places=2, default=0)
    tax_amount = models.DecimalField(_('Tax Amount'), max_digits=15, decimal_places=2, default=0)
    
    # Totals
    line_total = models.DecimalField(_('Line Total'), max_digits=15, decimal_places=2, default=0)
    
    # Insurance/Warranty Coverage
    is_covered_by_insurance = models.BooleanField(_('Covered by Insurance'), default=False)
    is_covered_by_warranty = models.BooleanField(_('Covered by Warranty'), default=False)
    coverage_percentage = models.DecimalField(_('Coverage %'), max_digits=5, decimal_places=2, default=0)
    
    # Other Settings
    notes = models.TextField(_('Notes'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Invoice Item')
        verbose_name_plural = _('Invoice Items')
        ordering = ['item_type', 'description']
    
    def __str__(self):
        return f"{self.description} ({self.quantity} x {self.unit_price})"
    
    def save(self, *args, **kwargs):
        # Calculate line total
        self.calculate_totals()
        super().save(*args, **kwargs)
        
        # Update parent invoice totals
        self.invoice.calculate_totals()
        self.invoice.save()
    
    def calculate_totals(self):
        """Calculate line item totals"""
        # Base amount
        base_amount = self.quantity * self.unit_price
        
        # Apply discount
        if self.discount_percentage > 0:
            self.discount_amount = (base_amount * self.discount_percentage) / Decimal('100.00')
        
        # Calculate amount after discount
        discounted_amount = base_amount - self.discount_amount
        
        # Apply tax
        if self.tax_percentage > 0:
            self.tax_amount = (discounted_amount * self.tax_percentage) / Decimal('100.00')
        
        # Calculate line total
        self.line_total = discounted_amount

    def apply_item_discount(self, discount_percentage=0, discount_amount=0, reason=''):
        """Apply discount to this specific item"""
        original_total = self.line_total
        
        if discount_percentage > 0:
            discount_amount = (original_total * discount_percentage) / Decimal('100.00')
        
        discount_amount = min(discount_amount, original_total)
        
        # Update item discount fields
        self.discount_percentage = discount_percentage
        self.discount_amount = discount_amount
        
        # Recalculate totals
        self.calculate_totals()
        self.save()
        
        return discount_amount


class Payment(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Payments for invoices
    """
    PAYMENT_STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
        ('refunded', _('Refunded')),
        ('cancelled', _('Cancelled')),
    ]
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.PROTECT,
        related_name='payments',
        verbose_name=_('Invoice')
    )
    
    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='payments',
        verbose_name=_('Customer')
    )
    
    payment_method = models.ForeignKey(
        PaymentMethod,
        on_delete=models.PROTECT,
        related_name='payments',
        verbose_name=_('Payment Method')
    )
    
    # Payment Details
    payment_date = models.DateTimeField(_('Payment Date'), default=timezone.now)
    amount = models.DecimalField(_('Amount'), max_digits=15, decimal_places=2)
    status = models.CharField(_('Status'), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    
    # Reference Information
    reference_number = models.CharField(_('Reference #'), max_length=100, blank=True)
    transaction_id = models.CharField(_('Transaction ID'), max_length=100, blank=True)
    
    # Bank/Card Information
    card_last_four = models.CharField(_('Card Last 4 Digits'), max_length=4, blank=True)
    card_type = models.CharField(_('Card Type'), max_length=50, blank=True)
    bank_name = models.CharField(_('Bank Name'), max_length=100, blank=True)
    check_number = models.CharField(_('Check Number'), max_length=50, blank=True)
    
    # Insurance Payment
    insurance_claim_number = models.CharField(_('Insurance Claim #'), max_length=100, blank=True)
    insurance_payment_date = models.DateField(_('Insurance Payment Date'), null=True, blank=True)
    
    # Other Settings
    notes = models.TextField(_('Notes'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-payment_date']
    
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.amount} ({self.get_status_display()})"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        
        # If payment is completed, update the invoice
        if self.status == 'completed':
            self.update_invoice_payment()
    
    def update_invoice_payment(self):
        """Update the invoice's payment information"""
        invoice = self.invoice
        
        # Calculate total payments
        completed_payments = Payment.objects.filter(
            invoice=invoice,
            status='completed'
        ).aggregate(total=models.Sum('amount'))
        
        # Update invoice
        total_paid = completed_payments.get('total') or Decimal('0.00')
        invoice.amount_paid = total_paid
        invoice.amount_due = invoice.total_amount - total_paid
        
        # Update status
        if invoice.amount_due <= 0:
            invoice.status = 'paid'
        elif invoice.amount_paid > 0:
            invoice.status = 'partially_paid'
        elif invoice.due_date < timezone.now().date():
            invoice.status = 'overdue'
        else:
            invoice.status = 'issued'
            
        invoice.save()


# ==========================================
# ENHANCED DISCOUNT MANAGEMENT MODELS
# ==========================================

class InvoiceDiscount(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Applied discounts on invoices with hierarchical support
    """
    DISCOUNT_LEVEL_CHOICES = [
        ('invoice', _('Invoice Level')),
        ('item', _('Item Level')),
        ('category', _('Category Level')),
    ]
    
    DISCOUNT_SOURCE_CHOICES = [
        ('manual', _('Manual')),
        ('rule', _('Automatic Rule')),
        ('customer_preference', _('Customer Preference')),
        ('promotion', _('Promotion')),
    ]
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='applied_discounts',
        verbose_name=_('Invoice')
    )
    
    # Discount Details
    discount_level = models.CharField(_('Discount Level'), max_length=20, choices=DISCOUNT_LEVEL_CHOICES)
    discount_source = models.CharField(_('Discount Source'), max_length=20, choices=DISCOUNT_SOURCE_CHOICES)
    
    # References
    discount_rule = models.ForeignKey(
        DiscountRule,
        on_delete=models.SET_NULL,
        related_name='applied_discounts',
        verbose_name=_('Discount Rule'),
        null=True, blank=True
    )
    
    discount_type = models.ForeignKey(
        DiscountType,
        on_delete=models.SET_NULL,
        related_name='applied_discounts',
        verbose_name=_('Discount Type'),
        null=True, blank=True
    )
    
    # Target (for item-level discounts)
    target_item_ids = models.JSONField(_('Target Item IDs'), default=list, blank=True)
    target_categories = models.JSONField(_('Target Categories'), default=list, blank=True)
    
    # Discount Values
    percentage = models.DecimalField(_('Percentage'), max_digits=5, decimal_places=2, default=0)
    fixed_amount = models.DecimalField(_('Fixed Amount'), max_digits=15, decimal_places=2, default=0)
    discount_amount = models.DecimalField(_('Discount Amount'), max_digits=15, decimal_places=2)
    
    # Applied to amounts
    original_amount = models.DecimalField(_('Original Amount'), max_digits=15, decimal_places=2)
    
    # Metadata
    reason = models.CharField(_('Reason'), max_length=255, blank=True)
    applied_by = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        related_name='applied_discounts',
        verbose_name=_('Applied By'),
        null=True, blank=True
    )
    
    # Approval (for high-value discounts)
    requires_approval = models.BooleanField(_('Requires Approval'), default=False)
    approved_by = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        related_name='approved_discounts',
        verbose_name=_('Approved By'),
        null=True, blank=True
    )
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Invoice Discount')
        verbose_name_plural = _('Invoice Discounts')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.discount_amount}"


class InvoiceItemDiscount(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Item-level discount tracking
    """
    invoice_item = models.ForeignKey(
        InvoiceItem,
        on_delete=models.CASCADE,
        related_name='item_discounts',
        verbose_name=_('Invoice Item')
    )
    
    invoice_discount = models.ForeignKey(
        InvoiceDiscount,
        on_delete=models.CASCADE,
        related_name='item_applications',
        verbose_name=_('Invoice Discount')
    )
    
    # Applied amounts
    original_line_total = models.DecimalField(_('Original Line Total'), max_digits=15, decimal_places=2)
    discount_amount = models.DecimalField(_('Discount Amount'), max_digits=15, decimal_places=2)
    final_line_total = models.DecimalField(_('Final Line Total'), max_digits=15, decimal_places=2)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Invoice Item Discount')
        verbose_name_plural = _('Invoice Item Discounts')
    
    def __str__(self):
        return f"{self.invoice_item.description} - {self.discount_amount}"


class DiscountConflict(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Track discount conflicts and resolutions
    """
    CONFLICT_TYPE_CHOICES = [
        ('overlapping_item', _('Overlapping Item Discount')),
        ('category_vs_item', _('Category vs Item Conflict')),
        ('invoice_vs_item', _('Invoice vs Item Conflict')),
        ('rule_combination', _('Rule Combination Conflict')),
    ]
    
    RESOLUTION_CHOICES = [
        ('pending', _('Pending')),
        ('override_allowed', _('Override Allowed')),
        ('highest_priority', _('Highest Priority Applied')),
        ('user_choice', _('User Choice Applied')),
        ('cancelled', _('Cancelled')),
    ]
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='discount_conflicts',
        verbose_name=_('Invoice')
    )
    
    conflict_type = models.CharField(_('Conflict Type'), max_length=30, choices=CONFLICT_TYPE_CHOICES)
    description = models.TextField(_('Conflict Description'))
    
    # Conflicting discounts
    conflicting_discounts = models.JSONField(_('Conflicting Discount IDs'), default=list)
    
    # Resolution
    resolution = models.CharField(_('Resolution'), max_length=20, choices=RESOLUTION_CHOICES, default='pending')
    resolved_by = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        related_name='resolved_conflicts',
        verbose_name=_('Resolved By'),
        null=True, blank=True
    )
    resolved_at = models.DateTimeField(_('Resolved At'), null=True, blank=True)
    resolution_notes = models.TextField(_('Resolution Notes'), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Discount Conflict')
        verbose_name_plural = _('Discount Conflicts')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.get_conflict_type_display()}"