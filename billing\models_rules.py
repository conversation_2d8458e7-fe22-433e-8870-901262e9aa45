from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet
from django.utils import timezone
from setup.models import Customer, Vehicle, ServiceCenter, Company
from decimal import Decimal
import uuid
import json

class PromotionRule(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Dynamic rule for promotions, discounts, and special pricing
    """
    RULE_TYPE_CHOICES = [
        ('discount', _('Discount')),
        ('warranty', _('Extended Warranty')),
        ('insurance', _('Insurance Coverage')),
        ('recall', _('Recall')),
        ('special', _('Special Offer')),
    ]
    
    APPROVAL_LEVEL_CHOICES = [
        ('none', _('No Approval Needed')),
        ('manager', _('Manager Approval')),
        ('director', _('Director Approval')),
        ('executive', _('Executive Approval')),
    ]
    
    name = models.CharField(_('Rule Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True)
    rule_type = models.CharField(_('Rule Type'), max_length=20, choices=RULE_TYPE_CHOICES)
    
    # Priority (higher number = higher priority when multiple rules match)
    priority = models.PositiveIntegerField(_('Priority'), default=0)
    
    # Validity period
    start_date = models.DateTimeField(_('Start Date/Time'), default=timezone.now)
    end_date = models.DateTimeField(_('End Date/Time'), null=True, blank=True)
    
    # Limit usage
    max_usages = models.PositiveIntegerField(_('Maximum Uses'), null=True, blank=True)
    current_usages = models.PositiveIntegerField(_('Current Uses'), default=0)
    
    # Status
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    # Approval settings
    requires_approval = models.BooleanField(_('Requires Approval'), default=False)
    approval_level = models.CharField(_('Approval Level'), max_length=20, choices=APPROVAL_LEVEL_CHOICES, default='none')
    
    # Other settings
    created_by = models.UUIDField(_('Created By User ID'), null=True, blank=True)
    notes = models.TextField(_('Notes'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Promotion Rule')
        verbose_name_plural = _('Promotion Rules')
        ordering = ['-priority', 'name']
    
    def __str__(self):
        return self.name
    
    @property
    def is_valid(self):
        now = timezone.now()
        if not self.is_active:
            return False
        if self.start_date and self.start_date > now:
            return False
        if self.end_date and self.end_date < now:
            return False
        if self.max_usages and self.current_usages >= self.max_usages:
            return False
        return True
    
    def increment_usage(self):
        self.current_usages += 1
        self.save(update_fields=['current_usages'])


class RuleCondition(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Conditions for when a promotion rule applies
    """
    CONDITION_TYPE_CHOICES = [
        # Customer related
        ('customer_id', _('Specific Customer')),
        ('customer_group', _('Customer Group')),
        ('customer_status', _('Customer Status')),
        ('customer_since', _('Customer Since')),
        ('customer_age', _('Customer Age')),
        ('customer_gender', _('Customer Gender')),
        ('customer_location', _('Customer Location')),
        
        # Vehicle related
        ('vehicle_id', _('Specific Vehicle')),
        ('vehicle_make', _('Vehicle Make')),
        ('vehicle_model', _('Vehicle Model')),
        ('vehicle_year', _('Vehicle Year')),
        ('vehicle_type', _('Vehicle Type')),
        ('vehicle_age', _('Vehicle Age')),
        ('vehicle_mileage', _('Vehicle Mileage')),
        
        # Service/Part related
        ('service_id', _('Specific Service')),
        ('service_type', _('Service Type')),
        ('service_category', _('Service Category')),
        ('part_id', _('Specific Part')),
        ('part_category', _('Part Category')),
        ('part_manufacturer', _('Part Manufacturer')),
        
        # Order related
        ('order_total', _('Order Total')),
        ('order_items', _('Number of Items')),
        ('order_date', _('Order Date')),
        ('service_center', _('Service Center')),
        
        # Time related
        ('day_of_week', _('Day of Week')),
        ('month', _('Month')),
        ('time_of_day', _('Time of Day')),
        ('special_day', _('Special Day')),
        
        # Other
        ('custom', _('Custom Condition')),
    ]
    
    OPERATOR_CHOICES = [
        ('eq', _('Equals')),
        ('neq', _('Not Equals')),
        ('gt', _('Greater Than')),
        ('gte', _('Greater Than or Equal')),
        ('lt', _('Less Than')),
        ('lte', _('Less Than or Equal')),
        ('contains', _('Contains')),
        ('not_contains', _('Doesn\'t Contain')),
        ('starts_with', _('Starts With')),
        ('ends_with', _('Ends With')),
        ('in', _('In List')),
        ('not_in', _('Not In List')),
        ('between', _('Between')),
        ('not_between', _('Not Between')),
    ]
    
    rule = models.ForeignKey(
        PromotionRule,
        on_delete=models.CASCADE,
        related_name='conditions',
        verbose_name=_('Rule')
    )
    
    # Condition details
    condition_type = models.CharField(_('Condition Type'), max_length=50, choices=CONDITION_TYPE_CHOICES)
    operator = models.CharField(_('Operator'), max_length=20, choices=OPERATOR_CHOICES)
    value = models.TextField(_('Value'), help_text=_('String, number, or JSON list/object as needed'))
    value2 = models.TextField(_('Second Value'), blank=True, help_text=_('For operators like "between" that need two values'))
    
    # Condition grouping
    group_id = models.CharField(_('Group ID'), max_length=50, blank=True, help_text=_('Group ID for AND/OR logic'))
    
    # Logic joining
    is_or_condition = models.BooleanField(_('Is OR Condition'), default=False, help_text=_('If true, this condition uses OR logic with the previous condition'))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Rule Condition')
        verbose_name_plural = _('Rule Conditions')
        ordering = ['rule', 'group_id']
    
    def __str__(self):
        return f"{self.get_condition_type_display()} {self.get_operator_display()} {self.value}"
    
    def get_value_as_type(self):
        """Convert stored value to appropriate type based on condition_type"""
        # Numeric types
        if self.condition_type in ('customer_age', 'vehicle_age', 'vehicle_mileage', 'order_total', 'order_items'):
            try:
                return Decimal(self.value)
            except:
                return 0
        
        # Date types
        elif self.condition_type in ('customer_since', 'order_date'):
            try:
                return timezone.datetime.fromisoformat(self.value)
            except:
                return None
        
        # List types
        elif self.operator in ('in', 'not_in'):
            try:
                return json.loads(self.value)
            except:
                return []
                
        # Default string
        return self.value


class RuleEffect(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Effects/outcomes when a rule matches (discounts, etc.)
    """
    EFFECT_TYPE_CHOICES = [
        # Discount types
        ('item_discount_percentage', _('Item Discount %')),
        ('item_discount_fixed', _('Item Fixed Discount')),
        ('order_discount_percentage', _('Order Discount %')),
        ('order_discount_fixed', _('Order Fixed Discount')),
        ('free_item', _('Free Item')),
        ('buy_x_get_y', _('Buy X Get Y')),
        
        # Price adjustments
        ('set_special_price', _('Set Special Price')),
        ('price_multiplier', _('Price Multiplier')),
        
        # Insurance/Warranty
        ('set_warranty_coverage', _('Set Warranty Coverage')),
        ('extend_warranty', _('Extend Warranty')),
        ('set_insurance_coverage', _('Set Insurance Coverage')),
        
        # Other effects
        ('apply_service_package', _('Apply Service Package')),
        ('set_payment_terms', _('Set Payment Terms')),
        ('set_priority', _('Set Service Priority')),
        ('custom_effect', _('Custom Effect')),
    ]
    
    APPLY_TO_CHOICES = [
        ('all', _('Entire Order')),
        ('parts_only', _('Parts Only')),
        ('labor_only', _('Labor Only')),
        ('specific_items', _('Specific Items')),
        ('specific_categories', _('Specific Categories')),
    ]
    
    rule = models.ForeignKey(
        PromotionRule,
        on_delete=models.CASCADE,
        related_name='effects',
        verbose_name=_('Rule')
    )
    
    # Effect details
    effect_type = models.CharField(_('Effect Type'), max_length=50, choices=EFFECT_TYPE_CHOICES)
    effect_value = models.DecimalField(_('Effect Value'), max_digits=15, decimal_places=2, default=0)
    effect_data = models.JSONField(_('Effect Data'), default=dict, blank=True, help_text=_('Additional data for complex effects'))
    
    # Application scope
    apply_to = models.CharField(_('Apply To'), max_length=50, choices=APPLY_TO_CHOICES, default='all')
    max_items = models.PositiveIntegerField(_('Maximum Items'), null=True, blank=True, help_text=_('Maximum number of items this effect applies to'))
    max_amount = models.DecimalField(_('Maximum Amount'), max_digits=15, decimal_places=2, null=True, blank=True)
    item_filter = models.JSONField(_('Item Filter'), default=dict, blank=True, help_text=_('Filter criteria for which items this applies to'))
    
    # Description for invoice
    description = models.CharField(_('Description'), max_length=255, blank=True, help_text=_('Description to show on invoice'))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Rule Effect')
        verbose_name_plural = _('Rule Effects')
    
    def __str__(self):
        return f"{self.get_effect_type_display()} ({self.effect_value})"


class RuleLog(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Log of rule applications for auditing and analytics
    """
    rule = models.ForeignKey(
        PromotionRule,
        on_delete=models.PROTECT,
        related_name='logs',
        verbose_name=_('Rule')
    )
    
    # Application context
    invoice = models.ForeignKey(
        'billing.Invoice',
        on_delete=models.SET_NULL,
        related_name='rule_logs',
        verbose_name=_('Invoice'),
        null=True, blank=True
    )
    
    work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        related_name='rule_logs',
        verbose_name=_('Work Order'),
        null=True, blank=True
    )
    
    customer = models.ForeignKey(
        Customer,
        on_delete=models.SET_NULL,
        related_name='rule_logs',
        verbose_name=_('Customer'),
        null=True, blank=True
    )
    
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.SET_NULL,
        related_name='rule_logs',
        verbose_name=_('Vehicle'),
        null=True, blank=True
    )
    
    # Effect details
    effect_type = models.CharField(_('Effect Type'), max_length=50)
    amount = models.DecimalField(_('Amount'), max_digits=15, decimal_places=2, default=0)
    
    # Approval information
    required_approval = models.BooleanField(_('Required Approval'), default=False)
    approved = models.BooleanField(_('Approved'), null=True, blank=True)
    approved_by = models.UUIDField(_('Approved By User ID'), null=True, blank=True)
    approval_date = models.DateTimeField(_('Approval Date'), null=True, blank=True)
    approval_notes = models.TextField(_('Approval Notes'), blank=True)
    
    # Detailed data
    applied_items = models.JSONField(_('Applied Items'), default=list, blank=True, help_text=_('List of items the rule was applied to'))
    rule_data = models.JSONField(_('Rule Data'), default=dict, blank=True, help_text=_('Snapshot of rule at application time'))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Rule Log')
        verbose_name_plural = _('Rule Logs')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.rule.name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}" 


class DiscountRule(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    العروض والقواعد - Discount rules and offers system
    """
    RULE_TYPE_CHOICES = [
        ('percentage', _('Percentage')),
        ('fixed_amount', _('Fixed Amount')),
        ('buy_x_get_y', _('Buy X Get Y')),
        ('category_discount', _('Category Discount')),
        ('loyalty_discount', _('Loyalty Discount')),
    ]
    
    APPLICABLE_TO_CHOICES = [
        ('single_part', _('Single Part')),
        ('all_parts', _('All Parts')),
        ('single_operation', _('Single Operation')),
        ('all_operations', _('All Operations')),
        ('full_invoice', _('Full Invoice')),
        ('category', _('Category')),
        ('customer_type', _('Customer Type')),
    ]
    
    PRIORITY_CHOICES = [
        (1, _('Low')),
        (2, _('Medium')), 
        (3, _('High')),
        (4, _('Critical')),
    ]
    
    name = models.CharField(_('Rule Name'), max_length=200)
    description = models.TextField(_('Description'), blank=True)
    rule_type = models.CharField(_('Rule Type'), max_length=20, choices=RULE_TYPE_CHOICES)
    applicable_to = models.CharField(_('Applicable To'), max_length=20, choices=APPLICABLE_TO_CHOICES)
    
    # Discount values
    percentage_discount = models.DecimalField(_('Percentage Discount'), max_digits=5, decimal_places=2, default=0)
    fixed_amount_discount = models.DecimalField(_('Fixed Amount Discount'), max_digits=15, decimal_places=2, default=0)
    
    # Buy X Get Y configuration
    buy_quantity = models.PositiveIntegerField(_('Buy Quantity'), default=1)
    get_quantity = models.PositiveIntegerField(_('Get Quantity'), default=1)
    get_discount_percentage = models.DecimalField(_('Get Item Discount %'), max_digits=5, decimal_places=2, default=100)
    
    # Conditions
    min_order_amount = models.DecimalField(_('Minimum Order Amount'), max_digits=15, decimal_places=2, default=0)
    max_discount_amount = models.DecimalField(_('Maximum Discount Amount'), max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Date restrictions
    valid_from = models.DateField(_('Valid From'), null=True, blank=True)
    valid_to = models.DateField(_('Valid To'), null=True, blank=True)
    
    # Target specific items/categories
    target_items = models.ManyToManyField('inventory.Item', blank=True, verbose_name=_('Target Items'))
    target_categories = models.JSONField(_('Target Categories'), default=list, blank=True)
    
    # Customer restrictions
    customer_types = models.JSONField(_('Customer Types'), default=list, blank=True)
    specific_customers = models.ManyToManyField('setup.Customer', blank=True, verbose_name=_('Specific Customers'))
    
    # Priority and combining rules
    priority = models.IntegerField(_('Priority'), choices=PRIORITY_CHOICES, default=2)
    can_combine_with_manual = models.BooleanField(_('Can Combine with Manual Discount'), default=False)
    
    # Status
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    # Approval requirement
    requires_approval = models.BooleanField(_('Requires Approval'), default=False)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Discount Rule')
        verbose_name_plural = _('Discount Rules')
        ordering = ['-priority', 'name']
    
    def __str__(self):
        return self.name
    
    def calculate_discount(self, amount, quantity=1, item=None, customer=None):
        """Calculate discount amount based on rule type"""
        discount_amount = Decimal('0')
        
        if self.rule_type == 'percentage':
            discount_amount = amount * (self.percentage_discount / 100)
        elif self.rule_type == 'fixed_amount':
            discount_amount = self.fixed_amount_discount
        elif self.rule_type == 'buy_x_get_y' and quantity >= self.buy_quantity:
            free_items = (quantity // self.buy_quantity) * self.get_quantity
            item_price = amount / quantity if quantity > 0 else 0
            discount_amount = free_items * item_price * (self.get_discount_percentage / 100)
        
        # Apply maximum discount limit
        if self.max_discount_amount and discount_amount > self.max_discount_amount:
            discount_amount = self.max_discount_amount
            
        return discount_amount
    
    def is_applicable(self, item=None, customer=None, order_amount=None, category=None):
        """Check if rule is applicable to given conditions"""
        if not self.is_active:
            return False
            
        # Check date validity
        today = timezone.now().date()
        if self.valid_from and today < self.valid_from:
            return False
        if self.valid_to and today > self.valid_to:
            return False
            
        # Check minimum order amount
        if order_amount and order_amount < self.min_order_amount:
            return False
            
        # Check item targeting
        if self.target_items.exists() and item and item not in self.target_items.all():
            return False
            
        # Check category targeting
        if self.target_categories and category and category not in self.target_categories:
            return False
            
        # Check customer targeting
        if self.specific_customers.exists() and customer and customer not in self.specific_customers.all():
            return False
            
        return True


class CompanyDiscountLimit(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Manual discount limits for each company
    """
    company = models.OneToOneField(
        Company,
        on_delete=models.CASCADE,
        related_name='discount_limit',
        verbose_name=_('Company')
    )
    
    # Cashier limits
    cashier_max_percentage = models.DecimalField(_('Cashier Max Percentage'), max_digits=5, decimal_places=2, default=5)
    cashier_max_amount = models.DecimalField(_('Cashier Max Amount'), max_digits=15, decimal_places=2, default=1000)
    
    # Service Center Manager limits
    manager_max_percentage = models.DecimalField(_('Manager Max Percentage'), max_digits=5, decimal_places=2, default=15)
    manager_max_amount = models.DecimalField(_('Manager Max Amount'), max_digits=15, decimal_places=2, default=5000)
    
    # Company Manager limits (unlimited by default)
    company_manager_max_percentage = models.DecimalField(_('Company Manager Max Percentage'), max_digits=5, decimal_places=2, default=50)
    company_manager_max_amount = models.DecimalField(_('Company Manager Max Amount'), max_digits=15, decimal_places=2, default=50000)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Company Discount Limit')
        verbose_name_plural = _('Company Discount Limits')
    
    def __str__(self):
        return f"Discount Limits - {self.company.name}"
    
    def get_user_limits(self, user_role):
        """Get discount limits based on user role"""
        if user_role in ['cashier', 'service_advisor']:
            return {
                'max_percentage': self.cashier_max_percentage,
                'max_amount': self.cashier_max_amount
            }
        elif user_role in ['service_center_manager', 'medium_center_manager']:
            return {
                'max_percentage': self.manager_max_percentage,
                'max_amount': self.manager_max_amount
            }
        elif user_role in ['company_manager', 'large_center_service_manager']:
            return {
                'max_percentage': self.company_manager_max_percentage,
                'max_amount': self.company_manager_max_amount
            }
        else:
            return {
                'max_percentage': Decimal('0'),
                'max_amount': Decimal('0')
            }


class DiscountApprovalRequest(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Approval requests for discounts exceeding limits
    """
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('expired', _('Expired')),
    ]
    
    DISCOUNT_TYPE_CHOICES = [
        ('manual', _('Manual Discount')),
        ('rule_based', _('Rule Based')),
        ('combined', _('Combined')),
    ]
    
    # Request details
    invoice = models.ForeignKey(
        'Invoice',
        on_delete=models.CASCADE,
        related_name='discount_requests',
        verbose_name=_('Invoice')
    )
    
    requested_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        related_name='discount_requests',
        verbose_name=_('Requested By')
    )
    
    # Discount details
    discount_type = models.CharField(_('Discount Type'), max_length=20, choices=DISCOUNT_TYPE_CHOICES)
    discount_percentage = models.DecimalField(_('Discount Percentage'), max_digits=5, decimal_places=2)
    discount_amount = models.DecimalField(_('Discount Amount'), max_digits=15, decimal_places=2)
    original_amount = models.DecimalField(_('Original Amount'), max_digits=15, decimal_places=2)
    
    # Applicable to
    applicable_to = models.CharField(_('Applicable To'), max_length=20, choices=DiscountRule.APPLICABLE_TO_CHOICES)
    target_items = models.JSONField(_('Target Items'), default=list, blank=True)
    
    # Justification
    reason = models.TextField(_('Reason'), help_text=_('Justification for discount'))
    
    # Approval workflow
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')
    approved_by = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='approved_discount_requests',
        verbose_name=_('Approved By')
    )
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    rejection_reason = models.TextField(_('Rejection Reason'), blank=True)
    
    # Expiry
    expires_at = models.DateTimeField(_('Expires At'), null=True, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Discount Approval Request')
        verbose_name_plural = _('Discount Approval Requests')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Discount Request - {self.invoice.invoice_number} - {self.discount_percentage}%"
    
    def is_expired(self):
        """Check if request has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False
    
    def approve(self, approved_by):
        """Approve the discount request"""
        self.status = 'approved'
        self.approved_by = approved_by
        self.approved_at = timezone.now()
        self.save()
    
    def reject(self, rejected_by, reason):
        """Reject the discount request"""
        self.status = 'rejected'
        self.approved_by = rejected_by
        self.rejection_reason = reason
        self.save()


