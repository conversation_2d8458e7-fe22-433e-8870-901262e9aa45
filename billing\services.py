from decimal import Decimal
from django.utils import timezone
from django.db.models import Q, Sum
import json
import logging
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _
from typing import Dict, List, Optional, Tuple

from .models import Invoice, InvoiceItem, InvoiceDiscount, InvoiceItemDiscount, DiscountType, CustomerPreference
from .models_rules import DiscountRule
from .models_rules import PromotionRule, RuleCondition, RuleEffect, RuleLog, CompanyDiscountLimit, DiscountApprovalRequest
from setup.models import Company

logger = logging.getLogger(__name__)

class RuleEngine:
    """
    Engine for processing and applying dynamic billing rules
    """
    
    def __init__(self, tenant_id):
        self.tenant_id = tenant_id
    
    def get_applicable_rules(self, context):
        """
        Find all rules that might apply to the current context
        
        Args:
            context: Dictionary with context info (customer, vehicle, items, etc)
        
        Returns:
            QuerySet of applicable PromotionRule objects
        """
        now = timezone.now()
        
        # Start with active rules for this tenant within date range
        rules = PromotionRule.objects.filter(
            tenant_id=self.tenant_id,
            is_active=True,
            start_date__lte=now
        ).filter(
            Q(end_date__isnull=True) | Q(end_date__gte=now)
        ).filter(
            Q(max_usages__isnull=True) | Q(current_usages__lt=models.F('max_usages'))
        ).order_by('-priority')
        
        return rules
    
    def evaluate_rule_conditions(self, rule, context):
        """
        Check if a rule's conditions match the current context
        
        Args:
            rule: PromotionRule object
            context: Dictionary with context info
        
        Returns:
            boolean: True if conditions match, False otherwise
        """
        conditions = rule.conditions.all()
        if not conditions:
            return True  # Rule with no conditions applies to everything
        
        # Group conditions by group_id
        condition_groups = {}
        for condition in conditions:
            group_id = condition.group_id or 'default'
            if group_id not in condition_groups:
                condition_groups[group_id] = []
            condition_groups[group_id].append(condition)
        
        # Each condition group is joined with AND
        # Within each group, conditions are joined with AND unless is_or_condition is True
        for group_id, group_conditions in condition_groups.items():
            group_result = False
            
            for i, condition in enumerate(group_conditions):
                condition_result = self._evaluate_single_condition(condition, context)
                
                if i == 0:
                    group_result = condition_result
                else:
                    if condition.is_or_condition:
                        group_result = group_result or condition_result
                    else:
                        group_result = group_result and condition_result
            
            if not group_result:
                return False  # If any group fails, the whole rule fails
        
        return True  # All condition groups passed
    
    def _evaluate_single_condition(self, condition, context):
        """
        Evaluate a single condition against the current context
        
        Args:
            condition: RuleCondition object
            context: Dictionary with context info
        
        Returns:
            boolean: True if condition matches, False otherwise
        """
        condition_type = condition.condition_type
        operator = condition.operator
        value = condition.get_value_as_type()
        value2 = condition.value2
        
        # Extract the target value from context based on condition type
        target_value = self._get_target_value(condition_type, context)
        if target_value is None:
            return False
        
        # Apply the operator
        return self._apply_operator(operator, target_value, value, value2)
    
    def _get_target_value(self, condition_type, context):
        """
        Extract the relevant value from context based on condition type
        """
        # Customer conditions
        if condition_type == 'customer_id':
            return context.get('customer', {}).get('id')
        elif condition_type == 'customer_group':
            return context.get('customer', {}).get('group')
        elif condition_type == 'customer_status':
            return context.get('customer', {}).get('status')
        elif condition_type == 'customer_since':
            return context.get('customer', {}).get('created_at')
        elif condition_type == 'customer_age':
            return context.get('customer', {}).get('age')
        elif condition_type == 'customer_gender':
            return context.get('customer', {}).get('gender')
        elif condition_type == 'customer_location':
            return context.get('customer', {}).get('location')
        
        # Vehicle conditions
        elif condition_type == 'vehicle_id':
            return context.get('vehicle', {}).get('id')
        elif condition_type == 'vehicle_make':
            return context.get('vehicle', {}).get('make')
        elif condition_type == 'vehicle_model':
            return context.get('vehicle', {}).get('model')
        elif condition_type == 'vehicle_year':
            return context.get('vehicle', {}).get('year')
        elif condition_type == 'vehicle_type':
            return context.get('vehicle', {}).get('type')
        elif condition_type == 'vehicle_age':
            return context.get('vehicle', {}).get('age')
        elif condition_type == 'vehicle_mileage':
            return context.get('vehicle', {}).get('mileage')
        
        # Order conditions
        elif condition_type == 'order_total':
            return context.get('order', {}).get('total')
        elif condition_type == 'order_items':
            return len(context.get('order', {}).get('items', []))
        elif condition_type == 'order_date':
            return context.get('order', {}).get('date')
        elif condition_type == 'service_center':
            return context.get('service_center', {}).get('id')
        
        # Time conditions
        elif condition_type == 'day_of_week':
            now = timezone.now()
            return now.strftime('%A').lower()
        elif condition_type == 'month':
            now = timezone.now()
            return now.strftime('%B').lower()
        elif condition_type == 'time_of_day':
            now = timezone.now()
            return now.strftime('%H:%M')
        
        # Service/Part conditions
        elif condition_type == 'service_id':
            services = [item.get('service_id') for item in context.get('order', {}).get('items', []) 
                      if item.get('type') == 'service']
            return services
        elif condition_type == 'part_id':
            parts = [item.get('part_id') for item in context.get('order', {}).get('items', []) 
                   if item.get('type') == 'part']
            return parts
        
        # If not found
        return None
    
    def _apply_operator(self, operator, target_value, condition_value, condition_value2=None):
        """
        Apply the operator to compare target_value and condition_value
        """
        # Handle list target values (like for parts)
        if isinstance(target_value, list):
            if operator == 'contains':
                return condition_value in target_value
            elif operator == 'not_contains':
                return condition_value not in target_value
            elif operator == 'eq':
                return condition_value == target_value
            elif operator == 'in':
                return any(val in condition_value for val in target_value)
            elif operator == 'not_in':
                return not any(val in condition_value for val in target_value)
            return False
        
        # Standard operators for scalar values
        if operator == 'eq':
            return target_value == condition_value
        elif operator == 'neq':
            return target_value != condition_value
        elif operator == 'gt':
            return target_value > condition_value
        elif operator == 'gte':
            return target_value >= condition_value
        elif operator == 'lt':
            return target_value < condition_value
        elif operator == 'lte':
            return target_value <= condition_value
        elif operator == 'contains':
            return str(condition_value) in str(target_value)
        elif operator == 'not_contains':
            return str(condition_value) not in str(target_value)
        elif operator == 'starts_with':
            return str(target_value).startswith(str(condition_value))
        elif operator == 'ends_with':
            return str(target_value).endswith(str(condition_value))
        elif operator == 'in':
            return target_value in condition_value
        elif operator == 'not_in':
            return target_value not in condition_value
        elif operator == 'between':
            if condition_value2:
                return condition_value <= target_value <= condition_value2
            return False
        elif operator == 'not_between':
            if condition_value2:
                return not (condition_value <= target_value <= condition_value2)
            return False
        
        return False
    
    def apply_rules_to_invoice(self, invoice, context=None):
        """
        Apply all applicable rules to an invoice
        
        Args:
            invoice: Invoice object
            context: Optional context dict (will be built if not provided)
            
        Returns:
            dict: Summary of applied rules and effects
        """
        if context is None:
            context = self._build_context(invoice)
        
        applied_rules = []
        rules = self.get_applicable_rules(context)
        
        for rule in rules:
            if self.evaluate_rule_conditions(rule, context):
                rule_applied = self._apply_rule_effects(rule, invoice, context)
                if rule_applied:
                    applied_rules.append(rule)
                    rule.increment_usage()
        
        # After all rules applied, recalculate invoice totals
        invoice.calculate_totals()
        invoice.save()
        
        return {
            'invoice': invoice,
            'applied_rules': applied_rules,
            'rule_count': len(applied_rules)
        }
    
    def _build_context(self, invoice):
        """
        Build a context dictionary from an invoice
        """
        context = {
            'customer': self._get_customer_context(invoice.customer),
            'vehicle': self._get_vehicle_context(invoice.work_order.vehicle),
            'service_center': {
                'id': invoice.service_center.id,
                'name': invoice.service_center.name
            },
            'order': {
                'id': invoice.id,
                'date': invoice.invoice_date,
                'total': invoice.subtotal,
                'items': self._get_items_context(invoice)
            }
        }
        return context
    
    def _get_customer_context(self, customer):
        """Extract customer data for context"""
        context = {
            'id': customer.id,
            'full_name': customer.full_name,
            'email': customer.email,
            'phone': customer.phone,
            'created_at': customer.created_at
        }
        
        # Try to get customer preferences
        try:
            preferences = customer.preferences
            context['status'] = preferences.status
            context['payment_terms'] = preferences.payment_terms
            context['default_discount'] = preferences.default_discount_percentage
        except:
            pass
            
        return context
    
    def _get_vehicle_context(self, vehicle):
        """Extract vehicle data for context"""
        if not vehicle:
            return {}
            
        today = timezone.now().date()
        manufacture_date = vehicle.manufacture_date or today
        age = (today.year - manufacture_date.year)
        
        return {
            'id': vehicle.id,
            'make': vehicle.make,
            'model': vehicle.model, 
            'year': vehicle.year,
            'mileage': vehicle.last_odometer_reading,
            'age': age,
            'vin': vehicle.vin,
            'license_plate': vehicle.license_plate
        }
    
    def _get_items_context(self, invoice):
        """Extract item data for context"""
        items = []
        for item in invoice.items.all():
            items.append({
                'id': item.id,
                'type': item.item_type,
                'description': item.description,
                'quantity': item.quantity,
                'unit_price': item.unit_price,
                'total': item.line_total,
                'part_id': item.part_id,
                'work_order_material_id': str(item.work_order_material_id) if item.work_order_material_id else None,
                'work_order_operation_id': str(item.work_order_operation_id) if item.work_order_operation_id else None
            })
        return items
    
    def _apply_rule_effects(self, rule, invoice, context):
        """
        Apply a rule's effects to an invoice
        
        Args:
            rule: PromotionRule object
            invoice: Invoice object
            context: Context dictionary
            
        Returns:
            boolean: True if any effects were applied
        """
        effects = rule.effects.all()
        if not effects:
            return False
            
        applied_effects = []
        items_modified = []
        
        for effect in effects:
            result = self._apply_single_effect(effect, invoice, context)
            if result['applied']:
                applied_effects.append(effect)
                items_modified.extend(result.get('items_modified', []))
                
                # Log the rule application
                self._log_rule_application(rule, effect, invoice, result)
        
        return len(applied_effects) > 0
    
    def _apply_single_effect(self, effect, invoice, context):
        """
        Apply a single rule effect to an invoice
        
        Args:
            effect: RuleEffect object
            invoice: Invoice object
            context: Context dictionary
            
        Returns:
            dict: Results of applying the effect
        """
        effect_type = effect.effect_type
        effect_value = effect.effect_value
        items_modified = []
        
        # Global discount effects
        if effect_type == 'order_discount_percentage':
            discount_amount = (invoice.subtotal * effect_value) / Decimal('100.0')
            if effect.max_amount:
                discount_amount = min(discount_amount, effect.max_amount)
                
            invoice.discount_amount = discount_amount
            
            return {
                'applied': True,
                'effect_type': effect_type,
                'amount': discount_amount,
                'description': effect.description or f"Order discount {effect_value}%"
            }
            
        elif effect_type == 'order_discount_fixed':
            discount_amount = min(effect_value, invoice.subtotal)
            invoice.discount_amount = discount_amount
            
            return {
                'applied': True,
                'effect_type': effect_type,
                'amount': discount_amount,
                'description': effect.description or f"Order discount {discount_amount}"
            }
        
        # Item-level discounts
        elif effect_type in ('item_discount_percentage', 'item_discount_fixed'):
            applicable_items = self._get_applicable_items(invoice, effect)
            
            for item in applicable_items:
                if effect_type == 'item_discount_percentage':
                    item_discount = (item.unit_price * effect_value) / Decimal('100.0')
                    item.discount_percentage = effect_value
                else:
                    item_discount = min(effect_value, item.unit_price)
                    
                item.discount_amount = item_discount * item.quantity
                item.save()
                items_modified.append(str(item.id))
            
            return {
                'applied': len(items_modified) > 0,
                'effect_type': effect_type,
                'items_modified': items_modified,
                'description': effect.description or f"Item discount applied to {len(items_modified)} items"
            }
        
        # Special price effects
        elif effect_type == 'set_special_price':
            applicable_items = self._get_applicable_items(invoice, effect)
            
            for item in applicable_items:
                original_price = item.unit_price
                item.unit_price = effect_value
                item.save()
                items_modified.append(str(item.id))
            
            return {
                'applied': len(items_modified) > 0,
                'effect_type': effect_type,
                'items_modified': items_modified,
                'description': effect.description or f"Special price applied to {len(items_modified)} items"
            }
        
        # Warranty/Insurance effects
        elif effect_type == 'set_warranty_coverage':
            applicable_items = self._get_applicable_items(invoice, effect)
            
            for item in applicable_items:
                item.is_covered_by_warranty = True
                item.coverage_percentage = effect_value
                item.save()
                items_modified.append(str(item.id))
            
            return {
                'applied': len(items_modified) > 0,
                'effect_type': effect_type,
                'items_modified': items_modified,
                'description': effect.description or f"Warranty coverage {effect_value}% applied to {len(items_modified)} items" 
            }
        
        elif effect_type == 'set_insurance_coverage':
            applicable_items = self._get_applicable_items(invoice, effect)
            
            for item in applicable_items:
                item.is_covered_by_insurance = True
                item.coverage_percentage = effect_value
                item.save()
                items_modified.append(str(item.id))
            
            return {
                'applied': len(items_modified) > 0,
                'effect_type': effect_type,
                'items_modified': items_modified,
                'description': effect.description or f"Insurance coverage {effect_value}% applied to {len(items_modified)} items"
            }
        
        # Buy X Get Y
        elif effect_type == 'buy_x_get_y':
            if not effect.effect_data:
                return {'applied': False}
                
            data = effect.effect_data
            buy_item_id = data.get('buy_item_id')
            get_item_id = data.get('get_item_id')
            buy_quantity = data.get('buy_quantity', 1)
            get_quantity = data.get('get_quantity', 1)
            
            # Find eligible buy items
            buy_items = invoice.items.filter(part_id=buy_item_id)
            if not buy_items.exists():
                return {'applied': False}
                
            # Calculate how many free items to add
            total_buy_qty = sum(item.quantity for item in buy_items)
            free_sets = int(total_buy_qty / buy_quantity)
            
            if free_sets > 0:
                # Apply discount to existing matching items or create new free item
                get_items = invoice.items.filter(part_id=get_item_id)
                if get_items.exists():
                    get_item = get_items.first()
                    get_item.discount_percentage = 100
                    get_item.save()
                    items_modified.append(str(get_item.id))
                else:
                    # We might need to create a new free item
                    # Logic would depend on how your system works
                    pass
                    
                return {
                    'applied': True,
                    'effect_type': effect_type,
                    'items_modified': items_modified,
                    'description': effect.description or f"Buy {buy_quantity} get {get_quantity} free"
                }
        
        # If we didn't apply anything
        return {'applied': False}
    
    def _get_applicable_items(self, invoice, effect):
        """
        Get the invoice items that this effect applies to
        """
        apply_to = effect.apply_to
        items = invoice.items.all()
        
        # Filter by type
        if apply_to == 'parts_only':
            items = items.filter(item_type='part')
        elif apply_to == 'labor_only':
            items = items.filter(item_type='labor')
        elif apply_to == 'specific_items':
            item_filter = effect.item_filter or {}
            item_ids = item_filter.get('item_ids', [])
            if item_ids:
                items = items.filter(id__in=item_ids)
        elif apply_to == 'specific_categories':
            item_filter = effect.item_filter or {}
            categories = item_filter.get('categories', [])
            if categories:
                # This would need to be customized based on how you track categories
                pass
        
        # Limit the number of items if specified
        if effect.max_items and effect.max_items > 0:
            items = items[:effect.max_items]
            
        return items
    
    def _log_rule_application(self, rule, effect, invoice, result):
        """
        Log that a rule was applied
        """
        customer = invoice.customer
        vehicle = invoice.work_order.vehicle if invoice.work_order else None
        
        log = RuleLog(
            tenant_id=self.tenant_id,
            rule=rule,
            invoice=invoice,
            work_order=invoice.work_order,
            customer=customer,
            vehicle=vehicle,
            effect_type=effect.effect_type,
            amount=result.get('amount', 0),
            required_approval=rule.requires_approval,
            applied_items=result.get('items_modified', []),
            rule_data={
                'rule_name': rule.name,
                'rule_type': rule.rule_type,
                'effect_description': result.get('description', '')
            }
        )
        log.save()
        
        return log 

class DiscountService:
    """
    Comprehensive discount management service with hierarchical logic
    """
    
    @staticmethod
    def detect_available_offers(invoice, customer=None):
        """
        Detect all available discount offers for an invoice
        """
        tenant_id = invoice.tenant_id
        today = timezone.now().date()
        
        # Get customer info
        if not customer:
            customer = invoice.customer
        
        # Get customer preferences
        customer_preference = None
        try:
            customer_preference = CustomerPreference.objects.get(
                customer=customer,
                tenant_id=tenant_id
            )
        except CustomerPreference.DoesNotExist:
            pass
        
        available_offers = {
            'automatic_rules': [],
            'customer_discounts': [],
            'bulk_discounts': [],
            'seasonal_offers': [],
            'loyalty_rewards': []
        }
        
        # 1. Get applicable automatic rules
        rules = DiscountRule.objects.filter(
            Q(valid_from__isnull=True) | Q(valid_from__lte=today),
            Q(valid_to__isnull=True) | Q(valid_to__gte=today),
            tenant_id=tenant_id,
            is_active=True
        ).order_by('-priority')
        
        for rule in rules:
            context = {
                'invoice': invoice,
                'customer': customer,
                'customer_preference': customer_preference
            }
            
            if DiscountService._is_rule_applicable(rule, context):
                discount_amount = rule.calculate_discount(invoice.subtotal, context)
                if discount_amount > 0:
                    available_offers['automatic_rules'].append({
                        'rule': rule,
                        'discount_amount': discount_amount,
                        'applicable_to': rule.applicable_to,
                        'can_combine': rule.can_combine_with_manual
                    })
        
        # 2. Customer-specific discounts
        if customer_preference and customer_preference.default_discount_percentage > 0:
            discount_amount = (invoice.subtotal * customer_preference.default_discount_percentage) / Decimal('100')
            available_offers['customer_discounts'].append({
                'type': 'customer_default',
                'percentage': customer_preference.default_discount_percentage,
                'discount_amount': discount_amount,
                'description': f'خصم العميل المميز ({customer_preference.default_discount_percentage}%)'
            })
        
        # 3. Bulk discounts
        parts_total = DiscountService._calculate_parts_total(invoice)
        operations_total = DiscountService._calculate_operations_total(invoice)
        
        if parts_total > 1000:  # Bulk parts discount
            discount_percentage = Decimal('5')  # 5% for parts over 1000
            discount_amount = (parts_total * discount_percentage) / Decimal('100')
            available_offers['bulk_discounts'].append({
                'type': 'bulk_parts',
                'percentage': discount_percentage,
                'discount_amount': discount_amount,
                'description': f'خصم كمية قطع الغيار ({discount_percentage}%)',
                'applicable_to': 'parts_only'
            })
        
        if operations_total > 500:  # Bulk operations discount
            discount_percentage = Decimal('10')  # 10% for operations over 500
            discount_amount = (operations_total * discount_percentage) / Decimal('100')
            available_offers['bulk_discounts'].append({
                'type': 'bulk_operations',
                'percentage': discount_percentage,
                'discount_amount': discount_amount,
                'description': f'خصم كمية العمليات ({discount_percentage}%)',
                'applicable_to': 'operations_only'
            })
        
        # 4. Seasonal offers (example)
        if today.month in [6, 7, 8]:  # Summer offers
            discount_percentage = Decimal('15')
            discount_amount = (invoice.subtotal * discount_percentage) / Decimal('100')
            available_offers['seasonal_offers'].append({
                'type': 'summer_offer',
                'percentage': discount_percentage,
                'discount_amount': discount_amount,
                'description': f'عرض الصيف ({discount_percentage}%)',
                'valid_until': '2024-08-31'
            })
        
        # 5. Loyalty rewards
        if customer:
            # Check customer's invoice history
            previous_invoices_count = Invoice.objects.filter(
                customer=customer,
                tenant_id=tenant_id,
                status='paid'
            ).count()
            
            if previous_invoices_count >= 5:  # Loyal customer
                discount_percentage = Decimal('12')
                discount_amount = (invoice.subtotal * discount_percentage) / Decimal('100')
                available_offers['loyalty_rewards'].append({
                    'type': 'loyalty_reward',
                    'percentage': discount_percentage,
                    'discount_amount': discount_amount,
                    'description': f'مكافأة العميل الدائم ({discount_percentage}%)',
                    'invoices_count': previous_invoices_count
                })
        
        return available_offers
    
    @staticmethod
    def apply_hierarchical_discounts(invoice, discount_requests):
        """
        Apply discounts with hierarchical logic to prevent conflicts
        
        discount_requests format:
        [
            {
                'level': 'invoice|item|category',
                'type': 'percentage|fixed',
                'value': 10.0,
                'target': 'all|parts|operations|specific_items',
                'target_items': [item_ids] (for specific items),
                'reason': 'Manual discount',
                'rule_id': None (for automatic rules)
            }
        ]
        """
        applied_discounts = []
        total_discount_amount = Decimal('0')
        errors = []
        
        # Sort by hierarchy: invoice level first, then category, then item
        hierarchy_order = {'invoice': 1, 'category': 2, 'item': 3}
        sorted_requests = sorted(discount_requests, key=lambda x: hierarchy_order.get(x.get('level', 'item'), 3))
        
        logger.info(f"Starting to apply {len(sorted_requests)} discount requests to invoice {invoice.id}")
        logger.info(f"Original invoice subtotal: {invoice.subtotal}")
        
        for i, request in enumerate(sorted_requests):
            try:
                logger.info(f"Processing discount request {i+1}/{len(sorted_requests)}: {request}")
                
                discount = DiscountService._apply_single_discount(invoice, request)
                if discount:
                    applied_discounts.append(discount)
                    total_discount_amount += discount.discount_amount
                    logger.info(f"Applied discount {discount.id} with amount {discount.discount_amount}")
                else:
                    logger.warning(f"Failed to apply discount request: {request}")
                    
            except Exception as e:
                error_msg = f"خطأ في تطبيق خصم {request.get('level', 'غير محدد')}: {str(e)}"
                errors.append(error_msg)
                logger.error(f"Error applying discount: {error_msg}", exc_info=True)
        
        if errors and not applied_discounts:
            logger.error(f"All discount applications failed: {errors}")
            return {
                'success': False,
                'error': f'فشل في تطبيق الخصومات: {"; ".join(errors)}',
                'applied_discounts': [],
                'total_discount_amount': 0
            }
        
        # Recalculate invoice totals ONCE after all discounts are applied
        original_total = invoice.total_amount
        logger.info(f"Before recalculation - Total: {original_total}, Amount Due: {invoice.amount_due}")
        
        invoice.calculate_totals()
        invoice.save()
        
        logger.info(f"After recalculation - Total: {invoice.total_amount}, Amount Due: {invoice.amount_due}")
        logger.info(f"Total discount amount applied: {total_discount_amount}")
        
        return {
            'success': True,
            'applied_discounts': applied_discounts,
            'total_discount_amount': total_discount_amount,
            'final_amount': invoice.total_amount,
            'final_amount_due': invoice.amount_due,
            'errors': errors if errors else []
        }
    
    @staticmethod
    def _apply_single_discount(invoice, request):
        """Apply a single discount request"""
        try:
            level = request['level']
            discount_type = request['type']
            value = Decimal(str(request['value']))
            target = request.get('target', 'all')
            target_items = request.get('target_items', [])
            reason = request.get('reason', '')
            rule_id = request.get('rule_id')
            
            logger.info(f"Applying discount: level={level}, type={discount_type}, value={value}, target={target}")
            
            # Determine discount source
            discount_source = 'manual' if not rule_id else 'rule'
            
            # Check if this discount type can be applied (one type per invoice rule)
            if not invoice.can_apply_discount_type(discount_source):
                existing_types = invoice.get_applied_discount_types()
                error_msg = f'لا يمكن تطبيق نوع خصم مختلف. الأنواع المطبقة حاليا: {", ".join(existing_types)}'
                logger.warning(f"Discount type restriction: {error_msg}")
                raise ValueError(error_msg)
            
            # Calculate original amount based on target
            if target == 'parts':
                original_amount = DiscountService._calculate_parts_total(invoice)
            elif target == 'operations':
                original_amount = DiscountService._calculate_operations_total(invoice)
            elif target == 'specific_items':
                original_amount = DiscountService._calculate_specific_items_total(invoice, target_items)
            else:
                original_amount = invoice.subtotal
            
            logger.info(f"Original amount calculated: {original_amount}")
            
            if original_amount <= 0:
                logger.warning(f"Original amount is zero or negative: {original_amount}")
                return None
            
            # Calculate discount amount
            if discount_type == 'percentage':
                discount_amount = (original_amount * value) / Decimal('100')
            else:  # fixed
                discount_amount = min(value, original_amount)
            
            logger.info(f"Discount amount calculated: {discount_amount}")
            
            # Create invoice discount record
            invoice_discount = InvoiceDiscount.objects.create(
                tenant_id=invoice.tenant_id,
                invoice=invoice,
                discount_level=level,
                discount_source=discount_source,
                discount_rule_id=rule_id,
                target_categories=[target] if target in ['parts', 'operations'] else [],
                target_item_ids=target_items if target == 'specific_items' else [],
                percentage=value if discount_type == 'percentage' else 0,
                fixed_amount=value if discount_type == 'fixed' else 0,
                discount_amount=discount_amount,
                original_amount=original_amount,
                reason=reason
            )
            
            logger.info(f"InvoiceDiscount created with ID: {invoice_discount.id}")
            
            # Apply to specific items if needed (but don't save invoice yet)
            if level == 'item' or target == 'specific_items':
                DiscountService._apply_to_items(invoice_discount, target, target_items)
            elif target == 'parts':
                DiscountService._apply_to_parts(invoice_discount)
            elif target == 'operations':
                DiscountService._apply_to_operations(invoice_discount)
            
            # NOTE: Don't calculate_totals here, it will be done in apply_hierarchical_discounts
            logger.info(f"Discount applied successfully to discount record: {invoice_discount.id}")
            
            return invoice_discount
            
        except Exception as e:
            logger.error(f"Error in _apply_single_discount: {str(e)}", exc_info=True)
            raise
    
    @staticmethod
    def _apply_to_items(invoice_discount, target, target_items):
        """Apply discount to specific items"""
        if target == 'specific_items' and target_items:
            items = InvoiceItem.objects.filter(
                invoice=invoice_discount.invoice,
                id__in=target_items
            )
        else:
            items = invoice_discount.invoice.items.all()
        
        for item in items:
            # Calculate item's share of the discount
            if invoice_discount.original_amount > 0:
                item_percentage = (item.line_total / invoice_discount.original_amount)
                item_discount_amount = invoice_discount.discount_amount * item_percentage
            else:
                item_discount_amount = Decimal('0')
            
            # Store original line total before discount
            original_line_total = item.line_total
            
            # Apply discount directly to item fields (without triggering save that recalculates invoice)
            if invoice_discount.percentage > 0:
                # Apply percentage discount
                item.discount_percentage = invoice_discount.percentage
                item.discount_amount = (original_line_total * invoice_discount.percentage) / Decimal('100.00')
            else:
                # Apply fixed discount amount proportionally
                item.discount_amount = item_discount_amount
                if original_line_total > 0:
                    item.discount_percentage = (item_discount_amount / original_line_total) * Decimal('100.00')
            
            # Recalculate item totals manually
            base_amount = item.quantity * item.unit_price
            discounted_amount = base_amount - item.discount_amount
            
            # Apply tax if needed
            if item.tax_percentage > 0:
                item.tax_amount = (discounted_amount * item.tax_percentage) / Decimal('100.00')
            
            # Update line total
            item.line_total = discounted_amount
            
            # Save item without triggering invoice recalculation
            super(InvoiceItem, item).save()
            
            # Create tracking record
            InvoiceItemDiscount.objects.create(
                tenant_id=invoice_discount.tenant_id,
                invoice_item=item,
                invoice_discount=invoice_discount,
                original_line_total=original_line_total,
                discount_amount=item_discount_amount,
                final_line_total=item.line_total
            )
    
    @staticmethod
    def _apply_to_parts(invoice_discount):
        """Apply discount to all parts"""
        parts_items = invoice_discount.invoice.items.filter(item_type='part')
        DiscountService._apply_to_items(invoice_discount, 'parts', [item.id for item in parts_items])
    
    @staticmethod
    def _apply_to_operations(invoice_discount):
        """Apply discount to all operations/labor"""
        operation_items = invoice_discount.invoice.items.filter(item_type__in=['labor', 'service'])
        DiscountService._apply_to_items(invoice_discount, 'operations', [item.id for item in operation_items])
    
    @staticmethod
    def _calculate_parts_total(invoice):
        """Calculate total amount for parts"""
        return invoice.items.filter(item_type='part').aggregate(
            total=Sum('line_total')
        )['total'] or Decimal('0')
    
    @staticmethod
    def _calculate_operations_total(invoice):
        """Calculate total amount for operations/labor"""
        return invoice.items.filter(item_type__in=['labor', 'service']).aggregate(
            total=Sum('line_total')
        )['total'] or Decimal('0')
    
    @staticmethod
    def _calculate_specific_items_total(invoice, item_ids):
        """Calculate total amount for specific items"""
        return invoice.items.filter(id__in=item_ids).aggregate(
            total=Sum('line_total')
        )['total'] or Decimal('0')
    
    @staticmethod
    def _is_rule_applicable(rule, context):
        """Check if a discount rule is applicable to the given context"""
        invoice = context['invoice']
        customer = context['customer']
        customer_preference = context.get('customer_preference')
        
        # Check minimum amount
        if invoice.subtotal < rule.min_amount:
            return False
        
        # Check minimum items count
        if invoice.items.count() < rule.min_items_count:
            return False
        
        # Check customer types
        if rule.customer_types and customer_preference:
            if customer_preference.status not in rule.customer_types:
                return False
        
        # Check service types (if work order exists)
        if rule.service_types and invoice.work_order:
            # This would need to be implemented based on your work order service types
            pass
        
        # Rule-specific checks
        if rule.rule_type == 'first_time_customer':
            # Check if this is customer's first invoice
            previous_invoices = Invoice.objects.filter(
                customer=customer,
                tenant_id=invoice.tenant_id
            ).exclude(id=invoice.id).count()
            if previous_invoices > 0:
                return False
        
        elif rule.rule_type == 'bulk_parts':
            parts_total = DiscountService._calculate_parts_total(invoice)
            if parts_total < rule.min_amount:
                return False
        
        elif rule.rule_type == 'bulk_operations':
            operations_total = DiscountService._calculate_operations_total(invoice)
            if operations_total < rule.min_amount:
                return False
        
        return True
    
    @staticmethod
    def get_discount_summary(invoice):
        """Get comprehensive discount summary for an invoice"""
        applied_discounts = InvoiceDiscount.objects.filter(
            invoice=invoice
        ).select_related('discount_rule', 'discount_type')
        
        summary = {
            'total_discount': Decimal('0'),
            'rule_based_discount': Decimal('0'),
            'manual_discount': Decimal('0'),
            'discounts_detail': []
        }
        
        for discount in applied_discounts:
            summary['total_discount'] += discount.discount_amount
            
            if discount.discount_source == 'rule':
                summary['rule_based_discount'] += discount.discount_amount
            else:
                summary['manual_discount'] += discount.discount_amount
            
            summary['discounts_detail'].append({
                'id': str(discount.id),
                'level': discount.discount_level,
                'source': discount.discount_source,
                'amount': discount.discount_amount,
                'percentage': discount.percentage,
                'reason': discount.reason,
                'rule_name': discount.discount_rule.name if discount.discount_rule else None,
                'target': discount.target_categories or discount.target_item_ids
            })
        
        return summary
    
    @staticmethod
    def remove_discount(invoice, discount_id):
        """Remove a specific discount from an invoice"""
        try:
            discount = InvoiceDiscount.objects.get(id=discount_id, invoice=invoice)
            
            # Remove item-level discount applications
            InvoiceItemDiscount.objects.filter(invoice_discount=discount).delete()
            
            # Remove the discount
            discount.delete()
            
            # Recalculate invoice totals to update amount_due
            invoice.calculate_totals()
            invoice.save()
            
            return True
            
        except InvoiceDiscount.DoesNotExist:
            return False
        except Exception as e:
            logger.error(f"Error removing discount {discount_id}: {e}")
            return False
    
    @staticmethod
    def validate_discount_conflicts(invoice, new_discount_request):
        """Validate if new discount conflicts with existing ones"""
        existing_discounts = InvoiceDiscount.objects.filter(invoice=invoice)
        
        conflicts = []
        
        for existing in existing_discounts:
            # Check for same level conflicts
            if (existing.discount_level == new_discount_request['level'] and 
                existing.target_categories == new_discount_request.get('target')):
                conflicts.append({
                    'type': 'same_level_conflict',
                    'existing_discount': existing,
                    'message': 'يوجد خصم مطبق بالفعل على نفس المستوى'
                })
        
        return conflicts

    # ==========================================
    # ENHANCED DISCOUNT MANAGEMENT METHODS
    # ==========================================

    @staticmethod
    def apply_hierarchical_discounts_enhanced(invoice_id, discount_requests, force_apply=False):
        """Apply multiple discounts with enhanced hierarchical conflict detection"""
        try:
            invoice = Invoice.objects.get(id=invoice_id)
            conflicts = []
            
            if not force_apply:
                conflicts = DiscountService._detect_enhanced_conflicts(invoice, discount_requests)
                if conflicts:
                    return {
                        'success': False,
                        'requires_confirmation': True,
                        'conflicts': conflicts
                    }
            
            # Apply discounts
            applied_discounts = []
            total_discount = Decimal('0.00')
            
            for request in discount_requests:
                discount = DiscountService._apply_single_discount_enhanced(invoice, request)
                if discount:
                    applied_discounts.append(discount)
                    total_discount += discount.discount_amount
            
            # Update invoice totals
            invoice.calculate_totals()
            invoice.save()
            
            return {
                'success': True,
                'applied_discounts': [str(d.id) for d in applied_discounts],
                'total_discount': float(total_discount),
                'new_invoice_total': float(invoice.total_amount)
            }
            
        except Exception as e:
            logger.error(f"Error applying hierarchical discounts: {e}")
            return {'success': False, 'error': str(e)}

    @staticmethod
    def _detect_enhanced_conflicts(invoice, discount_requests):
        """Enhanced conflict detection between discount requests and existing discounts"""
        conflicts = []
        existing_discounts = InvoiceDiscount.objects.filter(invoice=invoice)
        
        for request in discount_requests:
            level = request.get('level', 'invoice')
            target = request.get('target', 'all')
            
            for existing in existing_discounts:
                if DiscountService._discounts_conflict_enhanced(request, existing):
                    conflicts.append(f"تضارب مع خصم موجود: {existing.reason}")
        
        return conflicts

    @staticmethod
    def _discounts_conflict_enhanced(request, existing_discount):
        """Enhanced check if two discounts conflict"""
        request_level = request.get('level', 'invoice')
        request_target = request.get('target', 'all')
        
        # Invoice level conflicts with everything
        if request_level == 'invoice' or existing_discount.discount_level == 'invoice':
            return True
        
        # Category level conflicts with same category
        if (request_level == 'category' and existing_discount.discount_level == 'category' and
            request_target in existing_discount.target_categories):
            return True
        
        # Item level conflicts with same items
        if (request_level == 'item' and existing_discount.discount_level == 'item' and
            set(request.get('item_ids', [])) & set(existing_discount.target_item_ids)):
            return True
        
        return False

    @staticmethod
    def _apply_single_discount_enhanced(invoice, request):
        """Apply a single discount request with enhanced logic"""
        try:
            level = request.get('level', 'invoice')
            discount_type = request.get('type', 'percentage')
            value = Decimal(str(request.get('value', 0)))
            reason = request.get('reason', 'خصم يدوي')
            
            if level == 'invoice':
                return DiscountService._apply_invoice_discount_enhanced(invoice, discount_type, value, reason)
            elif level == 'category':
                target = request.get('target', 'all')
                return DiscountService._apply_category_discount_enhanced(invoice, target, discount_type, value, reason)
            elif level == 'item':
                item_ids = request.get('item_ids', [])
                return DiscountService._apply_item_discount_enhanced(invoice, item_ids, discount_type, value, reason)
            
        except Exception as e:
            logger.error(f"Error applying single discount: {e}")
            return None

    @staticmethod
    def _apply_invoice_discount_enhanced(invoice, discount_type, value, reason):
        """Apply discount to entire invoice with enhanced logic"""
        original_amount = invoice.subtotal
        
        if discount_type == 'percentage':
            discount_amount = (original_amount * value) / Decimal('100.00')
        else:
            discount_amount = min(value, original_amount)
        
        discount = InvoiceDiscount.objects.create(
            invoice=invoice,
            discount_level='invoice',
            discount_source='manual',
            percentage=value if discount_type == 'percentage' else 0,
            fixed_amount=value if discount_type == 'fixed' else 0,
            discount_amount=discount_amount,
            original_amount=original_amount,
            reason=reason,
            tenant_id=invoice.tenant_id
        )
        
        return discount

    @staticmethod
    def _apply_category_discount_enhanced(invoice, category, discount_type, value, reason):
        """Apply discount to category with enhanced logic"""
        if category == 'parts':
            items = invoice.items.filter(item_type='part')
        elif category == 'operations':
            items = invoice.items.filter(item_type__in=['labor', 'service'])
        else:
            items = invoice.items.all()
        
        category_total = sum(item.line_total for item in items)
        
        if discount_type == 'percentage':
            discount_amount = (category_total * value) / Decimal('100.00')
        else:
            discount_amount = min(value, category_total)
        
        discount = InvoiceDiscount.objects.create(
            invoice=invoice,
            discount_level='category',
            discount_source='manual',
            target_categories=[category],
            percentage=value if discount_type == 'percentage' else 0,
            fixed_amount=value if discount_type == 'fixed' else 0,
            discount_amount=discount_amount,
            original_amount=category_total,
            reason=reason,
            tenant_id=invoice.tenant_id
        )
        
        # Apply to individual items
        for item in items:
            if category_total > 0:
                item_discount_amount = (discount_amount * item.line_total) / category_total
                InvoiceItemDiscount.objects.create(
                    invoice_item=item,
                    invoice_discount=discount,
                    original_line_total=item.line_total,
                    discount_amount=item_discount_amount,
                    final_line_total=item.line_total - item_discount_amount,
                    tenant_id=invoice.tenant_id
                )
        
        return discount

    @staticmethod
    def _apply_item_discount_enhanced(invoice, item_ids, discount_type, value, reason):
        """Apply discount to specific items with enhanced logic"""
        items = invoice.items.filter(id__in=item_ids)
        items_total = sum(item.line_total for item in items)
        
        if discount_type == 'percentage':
            discount_amount = (items_total * value) / Decimal('100.00')
        else:
            discount_amount = min(value, items_total)
        
        discount = InvoiceDiscount.objects.create(
            invoice=invoice,
            discount_level='item',
            discount_source='manual',
            target_item_ids=list(item_ids),
            percentage=value if discount_type == 'percentage' else 0,
            fixed_amount=value if discount_type == 'fixed' else 0,
            discount_amount=discount_amount,
            original_amount=items_total,
            reason=reason,
            tenant_id=invoice.tenant_id
        )
        
        # Apply to individual items
        for item in items:
            if items_total > 0:
                item_discount_amount = (discount_amount * item.line_total) / items_total
                InvoiceItemDiscount.objects.create(
                    invoice_item=item,
                    invoice_discount=discount,
                    original_line_total=item.line_total,
                    discount_amount=item_discount_amount,
                    final_line_total=item.line_total - item_discount_amount,
                    tenant_id=invoice.tenant_id
                )
        
        return discount

    @staticmethod
    def get_enhanced_discount_summary(invoice):
        """Get enhanced comprehensive discount summary for an invoice"""
        discounts = InvoiceDiscount.objects.filter(invoice=invoice)
        
        summary = {
            'total_discount': sum(d.discount_amount for d in discounts),
            'discount_count': discounts.count(),
            'discounts_detail': []
        }
        
        for discount in discounts:
            summary['discounts_detail'].append({
                'id': str(discount.id),
                'level': discount.discount_level,
                'source': discount.discount_source,
                'amount': float(discount.discount_amount),
                'reason': discount.reason,
                'created_at': discount.created_at.isoformat()
            })
        
        return summary

    @staticmethod
    def remove_discount_enhanced(discount_id):
        """Remove a specific discount with enhanced logic"""
        try:
            discount = InvoiceDiscount.objects.get(id=discount_id)
            invoice = discount.invoice
            
            # Remove item-level applications
            InvoiceItemDiscount.objects.filter(invoice_discount=discount).delete()
            
            # Remove the discount
            discount.delete()
            
            # Recalculate invoice totals
            invoice.calculate_totals()
            invoice.save()
            
            return {
                'success': True,
                'message': 'تم إزالة الخصم بنجاح',
                'new_invoice_total': float(invoice.total_amount)
            }
            
        except InvoiceDiscount.DoesNotExist:
            return {'success': False, 'error': 'الخصم غير موجود'}
        except Exception as e:
            logger.error(f"Error removing discount: {e}")
            return {'success': False, 'error': str(e)}

    @staticmethod
    def apply_automatic_rules_enhanced(invoice_id, selected_rules):
        """Apply selected automatic rules with enhanced logic"""
        try:
            invoice = Invoice.objects.get(id=invoice_id)
            applied_rules = []
            total_discount = Decimal('0.00')
            
            for rule_id in selected_rules:
                try:
                    rule = DiscountRule.objects.get(id=rule_id, tenant_id=invoice.tenant_id)
                    if rule.is_valid_now():
                        discount_amount = rule.calculate_discount(invoice.subtotal)
                        if discount_amount > 0:
                            discount = InvoiceDiscount.objects.create(
                                invoice=invoice,
                                discount_level='invoice',
                                discount_source='rule',
                                discount_rule=rule,
                                percentage=rule.percentage_discount,
                                fixed_amount=rule.fixed_discount,
                                discount_amount=discount_amount,
                                original_amount=invoice.subtotal,
                                reason=f"قاعدة تلقائية: {rule.name}",
                                tenant_id=invoice.tenant_id
                            )
                            applied_rules.append(rule)
                            total_discount += discount_amount
                except DiscountRule.DoesNotExist:
                    continue
            
            # Update invoice totals
            invoice.calculate_totals()
            invoice.save()
            
            return {
                'success': True,
                'message': f'تم تطبيق {len(applied_rules)} قاعدة بنجاح',
                'applied_rules': [rule.name for rule in applied_rules],
                'total_discount': float(total_discount),
                'new_invoice_total': float(invoice.total_amount)
            }
            
        except Exception as e:
            logger.error(f"Error applying automatic rules: {e}")
            return {'success': False, 'error': str(e)}