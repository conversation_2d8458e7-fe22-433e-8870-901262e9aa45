from django.db.models import Sum, Avg, Count, F, Q
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from django.core.cache import cache
from functools import lru_cache

from setup.models import Customer
from billing.models import Invoice, Payment
from .models_classification import (
    CustomerClassification,
    ClassificationCriteria,
    CustomerClassificationHistory
)

import logging
logger = logging.getLogger(__name__)


class CustomerClassificationService:
    """
    Service for evaluating and managing customer classifications
    """
    
    def __init__(self, tenant_id):
        self.tenant_id = tenant_id
        # Cache timeout in seconds (1 hour)
        self.cache_timeout = 3600
    
    def evaluate_all_customers(self):
        """
        Evaluate all customers for possible classification changes
        """
        customers = Customer.objects.filter(tenant_id=self.tenant_id)
        
        results = {
            'total': customers.count(),
            'evaluated': 0,
            'changed': 0,
            'unchanged': 0,
            'errors': 0,
            'changes': []
        }
        
        for customer in customers:
            try:
                changed = self.evaluate_customer(customer)
                results['evaluated'] += 1
                
                if changed:
                    results['changed'] += 1
                    results['changes'].append({
                        'customer_id': str(customer.id),
                        'customer_name': customer.full_name,
                        'previous': customer.classification.name if customer.classification else 'None',
                        'new': changed.new_classification.name
                    })
                else:
                    results['unchanged'] += 1
                    
            except Exception as e:
                logger.error(f"Error evaluating customer {customer.id}: {str(e)}")
                results['errors'] += 1
        
        return results
    
    def evaluate_customer(self, customer):
        """
        Evaluate a single customer and update classification if needed
        
        Args:
            customer: Customer object to evaluate
            
        Returns:
            CustomerClassificationHistory object if classification changed, None otherwise
        """
        # Check cache
        cache_key = f"customer_classification_evaluation_{self.tenant_id}_{customer.id}"
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            return None if cached_result == "no_change" else cached_result
        
        # Get all active classifications ordered by level (highest first)
        classifications = CustomerClassification.objects.filter(
            tenant_id=self.tenant_id,
            is_active=True
        ).order_by('-level')
        
        if not classifications.exists():
            cache.set(cache_key, "no_change", self.cache_timeout)
            return None
        
        # Current classification
        current_classification = customer.classification
        
        # Try to qualify for each classification, starting with highest
        for classification in classifications:
            # Skip if customer already has this classification
            if current_classification and current_classification.id == classification.id:
                continue
                
            # Check if customer qualifies for this classification
            qualifies, evaluation_data = self._check_qualification(customer, classification)
            
            if qualifies:
                # Create history record
                history = CustomerClassificationHistory(
                    tenant_id=self.tenant_id,
                    customer=customer,
                    previous_classification=current_classification,
                    new_classification=classification,
                    automatic=True,
                    reason="Automatic evaluation",
                    evaluation_data=evaluation_data
                )
                history.save()
                
                # Update customer
                customer.classification = classification
                customer.save(update_fields=['classification'])
                
                # Update cache
                cache.set(cache_key, history, self.cache_timeout)
                
                return history
        
        # No changes made
        cache.set(cache_key, "no_change", self.cache_timeout)
        return None
    
    # Use LRU cache for criteria qualification checks
    @lru_cache(maxsize=128)
    def _check_qualification(self, customer, classification):
        """
        Check if a customer qualifies for a classification
        
        Args:
            customer: Customer to check
            classification: Classification to check against
            
        Returns:
            (bool, dict): (qualifies, evaluation_data)
        """
        criteria_set = classification.criteria.filter(tenant_id=self.tenant_id)
        
        if not criteria_set.exists():
            return False, {"error": "No criteria defined"}
        
        # Track required criteria results
        required_criteria = criteria_set.filter(is_required=True)
        required_passed = True
        
        # Track weighted criteria results
        weighted_criteria_count = criteria_set.filter(is_required=False).count()
        passing_score = 0
        criteria_results = {}
        
        # Check each criteria
        for criteria in criteria_set:
            result = self._evaluate_criteria(customer, criteria)
            criteria_results[str(criteria.id)] = {
                "type": criteria.criteria_type,
                "value": result["value"],
                "threshold": float(criteria.value),
                "passed": result["passed"],
                "details": result["details"]
            }
            
            # Handle required criteria
            if criteria.is_required and not result["passed"]:
                required_passed = False
            
            # Add to score for weighted criteria
            if not criteria.is_required and result["passed"]:
                passing_score += float(criteria.weight)
        
        # Calculate weighted percentage (if any weighted criteria exist)
        weighted_percentage = 0
        if weighted_criteria_count > 0:
            # Calculate total possible weight
            total_weight = sum(float(c.weight) for c in criteria_set.filter(is_required=False))
            weighted_percentage = (passing_score / total_weight) * 100 if total_weight > 0 else 0
        
        # Customer qualifies if all required criteria pass AND weighted score >= 60%
        qualifies = required_passed and weighted_percentage >= 60
        
        evaluation_data = {
            "criteria_results": criteria_results,
            "required_passed": required_passed,
            "weighted_percentage": weighted_percentage,
            "qualifies": qualifies
        }
        
        return qualifies, evaluation_data
    
    def _evaluate_criteria(self, customer, criteria):
        """
        Evaluate a single criteria for a customer
        
        Args:
            customer: Customer to evaluate
            criteria: ClassificationCriteria to evaluate against
            
        Returns:
            dict: Evaluation result containing value, passed status, and details
        """
        # Cache key for criteria evaluation
        cache_key = f"criteria_eval_{self.tenant_id}_{customer.id}_{criteria.id}"
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            return cached_result
        
        # Define evaluation period
        period_end = timezone.now()
        period_start = None
        
        if criteria.period == 'month':
            period_start = period_end - timedelta(days=30)
        elif criteria.period == '3_months':
            period_start = period_end - timedelta(days=90)
        elif criteria.period == '6_months':
            period_start = period_end - timedelta(days=180)
        elif criteria.period == 'year':
            period_start = period_end - timedelta(days=365)
        
        # Evaluate based on criteria type
        if criteria.criteria_type == 'spend_amount':
            result = self._evaluate_spend_amount(customer, criteria, period_start, period_end)
        elif criteria.criteria_type == 'spend_frequency':
            result = self._evaluate_spend_frequency(customer, criteria, period_start, period_end)
        elif criteria.criteria_type == 'visit_count':
            result = self._evaluate_visit_count(customer, criteria, period_start, period_end)
        elif criteria.criteria_type == 'average_spend':
            result = self._evaluate_average_spend(customer, criteria, period_start, period_end)
        elif criteria.criteria_type == 'payment_history':
            result = self._evaluate_payment_history(customer, criteria, period_start, period_end)
        elif criteria.criteria_type == 'vehicle_count':
            result = self._evaluate_vehicle_count(customer, criteria)
        elif criteria.criteria_type == 'vehicle_value':
            result = self._evaluate_vehicle_value(customer, criteria)
        elif criteria.criteria_type == 'service_type_usage':
            result = self._evaluate_service_type_usage(customer, criteria, period_start, period_end)
        elif criteria.criteria_type == 'referrals':
            result = self._evaluate_referrals(customer, criteria, period_start, period_end)
        elif criteria.criteria_type == 'days_since_first':
            result = self._evaluate_days_since_first(customer, criteria)
        elif criteria.criteria_type == 'loyalty_duration':
            result = self._evaluate_loyalty_duration(customer, criteria)
        else:
            # Default for unimplemented criteria types
            result = {
                "value": 0,
                "passed": False,
                "details": f"Criteria type '{criteria.criteria_type}' not implemented"
            }
        
        # Cache the result
        cache.set(cache_key, result, self.cache_timeout)
        
        return result
    
    def _evaluate_spend_amount(self, customer, criteria, period_start, period_end):
        """
        Evaluate total spend amount criteria
        """
        query = Q(customer=customer, tenant_id=self.tenant_id, status__in=['paid', 'partially_paid'])
        
        if period_start:
            query &= Q(invoice_date__gte=period_start, invoice_date__lte=period_end)
        
        total_spend = Invoice.objects.filter(query).aggregate(
            total=Sum('total_amount')
        )['total'] or Decimal('0.00')
        
        # Compare based on operator
        passed = self._compare_value(total_spend, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": float(total_spend),
            "passed": passed,
            "details": {
                "total_spend": float(total_spend),
                "period_start": period_start.isoformat() if period_start else None,
                "period_end": period_end.isoformat()
            }
        }
    
    def _evaluate_spend_frequency(self, customer, criteria, period_start, period_end):
        """
        Evaluate spend frequency (invoices per month)
        """
        query = Q(customer=customer, tenant_id=self.tenant_id)
        
        if period_start:
            query &= Q(invoice_date__gte=period_start, invoice_date__lte=period_end)
        
        invoice_count = Invoice.objects.filter(query).count()
        
        # Calculate months in period
        if period_start:
            months = (period_end.year - period_start.year) * 12 + (period_end.month - period_start.month)
            months = max(1, months)  # At least 1 month
        else:
            # Get date of first invoice
            first_invoice = Invoice.objects.filter(
                customer=customer, 
                tenant_id=self.tenant_id
            ).order_by('invoice_date').first()
            
            if first_invoice:
                months = (period_end.year - first_invoice.invoice_date.year) * 12 + (period_end.month - first_invoice.invoice_date.month)
                months = max(1, months)
            else:
                months = 1
        
        frequency = invoice_count / months
        
        # Compare based on operator
        passed = self._compare_value(frequency, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": frequency,
            "passed": passed,
            "details": {
                "invoice_count": invoice_count,
                "months": months,
                "frequency": frequency,
                "period_start": period_start.isoformat() if period_start else None,
                "period_end": period_end.isoformat()
            }
        }
    
    def _evaluate_visit_count(self, customer, criteria, period_start, period_end):
        """
        Evaluate total number of visits (work orders)
        """
        from work_orders.models import WorkOrder
        
        query = Q(customer=customer, tenant_id=self.tenant_id)
        
        if period_start:
            query &= Q(created_at__gte=period_start, created_at__lte=period_end)
        
        visit_count = WorkOrder.objects.filter(query).count()
        
        # Compare based on operator
        passed = self._compare_value(visit_count, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": visit_count,
            "passed": passed,
            "details": {
                "visit_count": visit_count,
                "period_start": period_start.isoformat() if period_start else None,
                "period_end": period_end.isoformat()
            }
        }
    
    def _evaluate_average_spend(self, customer, criteria, period_start, period_end):
        """
        Evaluate average spend per invoice
        """
        query = Q(customer=customer, tenant_id=self.tenant_id, status__in=['paid', 'partially_paid'])
        
        if period_start:
            query &= Q(invoice_date__gte=period_start, invoice_date__lte=period_end)
        
        average = Invoice.objects.filter(query).aggregate(
            avg=Avg('total_amount')
        )['avg'] or Decimal('0.00')
        
        # Compare based on operator
        passed = self._compare_value(average, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": float(average),
            "passed": passed,
            "details": {
                "average_spend": float(average),
                "period_start": period_start.isoformat() if period_start else None,
                "period_end": period_end.isoformat()
            }
        }
    
    def _evaluate_payment_history(self, customer, criteria, period_start, period_end):
        """
        Evaluate payment history (percentage of invoices paid on time)
        """
        query = Q(customer=customer, tenant_id=self.tenant_id)
        
        if period_start:
            query &= Q(invoice_date__gte=period_start, invoice_date__lte=period_end)
        
        invoices = Invoice.objects.filter(query)
        total_count = invoices.count()
        
        if total_count == 0:
            return {
                "value": 0,
                "passed": False,
                "details": {
                    "total_invoices": 0,
                    "on_time_count": 0,
                    "on_time_percentage": 0
                }
            }
        
        # Count invoices paid on time
        on_time_count = 0
        for invoice in invoices:
            payments = Payment.objects.filter(
                invoice=invoice,
                tenant_id=self.tenant_id,
                status='completed'
            ).order_by('payment_date')
            
            if payments.exists() and payments.first().payment_date <= invoice.due_date:
                on_time_count += 1
        
        on_time_percentage = (on_time_count / total_count) * 100
        
        # Compare based on operator
        passed = self._compare_value(on_time_percentage, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": on_time_percentage,
            "passed": passed,
            "details": {
                "total_invoices": total_count,
                "on_time_count": on_time_count,
                "on_time_percentage": on_time_percentage,
                "period_start": period_start.isoformat() if period_start else None,
                "period_end": period_end.isoformat()
            }
        }
    
    def _evaluate_vehicle_count(self, customer, criteria):
        """
        Evaluate number of vehicles registered to customer
        """
        vehicle_count = customer.owned_vehicles.filter(tenant_id=self.tenant_id).count()
        
        # Compare based on operator
        passed = self._compare_value(vehicle_count, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": vehicle_count,
            "passed": passed,
            "details": {
                "vehicle_count": vehicle_count
            }
        }
    
    def _evaluate_vehicle_value(self, customer, criteria):
        """
        Evaluate total value of vehicles owned by customer
        """
        from setup.models import Vehicle
        
        vehicles = Vehicle.objects.filter(
            owner=customer,
            tenant_id=self.tenant_id
        )
        
        # Sum the value from attributes (if exists)
        total_value = Decimal('0.00')
        for vehicle in vehicles:
            if 'estimated_value' in vehicle.attributes:
                try:
                    total_value += Decimal(str(vehicle.attributes['estimated_value']))
                except (ValueError, TypeError, decimal.InvalidOperation):
                    # Skip if value can't be converted to Decimal
                    pass
        
        # Compare based on operator
        passed = self._compare_value(total_value, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": float(total_value),
            "passed": passed,
            "details": {
                "vehicle_count": vehicles.count(),
                "total_value": float(total_value)
            }
        }
    
    def _evaluate_service_type_usage(self, customer, criteria, period_start, period_end):
        """
        Evaluate usage of premium service types
        """
        from work_orders.models import WorkOrder, ServiceItem
        
        # Get all work orders for this customer
        query = Q(customer=customer, tenant_id=self.tenant_id)
        
        if period_start:
            query &= Q(created_at__gte=period_start, created_at__lte=period_end)
        
        work_orders = WorkOrder.objects.filter(query)
        
        # Count premium service items
        premium_count = 0
        total_count = 0
        
        for work_order in work_orders:
            service_items = ServiceItem.objects.filter(
                work_order=work_order,
                tenant_id=self.tenant_id
            )
            
            for item in service_items:
                total_count += 1
                if item.service and getattr(item.service, 'is_premium', False):
                    premium_count += 1
        
        # Calculate percentage
        premium_percentage = (premium_count / total_count) * 100 if total_count > 0 else 0
        
        # Compare based on operator
        passed = self._compare_value(premium_percentage, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": premium_percentage,
            "passed": passed,
            "details": {
                "premium_services": premium_count,
                "total_services": total_count,
                "premium_percentage": premium_percentage,
                "period_start": period_start.isoformat() if period_start else None,
                "period_end": period_end.isoformat()
            }
        }
    
    def _evaluate_referrals(self, customer, criteria, period_start, period_end):
        """
        Evaluate number of successful referrals
        """
        # Assuming there's a Referral model that tracks referrals
        from referrals.models import Referral
        
        query = Q(
            referrer=customer,
            status='completed',
            tenant_id=self.tenant_id
        )
        
        if period_start:
            query &= Q(created_at__gte=period_start, created_at__lte=period_end)
        
        referral_count = Referral.objects.filter(query).count()
        
        # Compare based on operator
        passed = self._compare_value(referral_count, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": referral_count,
            "passed": passed,
            "details": {
                "referral_count": referral_count,
                "period_start": period_start.isoformat() if period_start else None,
                "period_end": period_end.isoformat()
            }
        }
    
    def _evaluate_days_since_first(self, customer, criteria):
        """
        Evaluate days since first visit/interaction
        """
        from work_orders.models import WorkOrder
        
        # Find earliest work order
        first_work_order = WorkOrder.objects.filter(
            customer=customer,
            tenant_id=self.tenant_id
        ).order_by('created_at').first()
        
        if not first_work_order:
            return {
                "value": 0,
                "passed": False,
                "details": {
                    "days_since_first": 0,
                    "first_date": None
                }
            }
        
        # Calculate days
        days_since = (timezone.now().date() - first_work_order.created_at.date()).days
        
        # Compare based on operator
        passed = self._compare_value(days_since, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": days_since,
            "passed": passed,
            "details": {
                "days_since_first": days_since,
                "first_date": first_work_order.created_at.isoformat()
            }
        }
    
    def _evaluate_loyalty_duration(self, customer, criteria):
        """
        Evaluate loyalty duration (how long the customer has been active)
        """
        # Calculate days since customer creation
        days_active = (timezone.now().date() - customer.created_at.date()).days
        
        # Compare based on operator
        passed = self._compare_value(days_active, criteria.operator, criteria.value, criteria.value2)
        
        return {
            "value": days_active,
            "passed": passed,
            "details": {
                "days_active": days_active,
                "customer_since": customer.created_at.isoformat()
            }
        }
    
    def _compare_value(self, actual, operator, threshold, threshold2=None):
        """
        Compare a value against threshold(s) using the given operator
        """
        if operator == 'gt':
            return actual > float(threshold)
        elif operator == 'gte':
            return actual >= float(threshold)
        elif operator == 'lt':
            return actual < float(threshold)
        elif operator == 'lte':
            return actual <= float(threshold)
        elif operator == 'eq':
            return actual == float(threshold)
        elif operator == 'between' and threshold2 is not None:
            return float(threshold) <= actual <= float(threshold2)
        
        return False
    
    def manually_change_classification(self, customer_id, classification_id, reason, user_id=None):
        """
        Manually change a customer's classification
        
        Args:
            customer_id: UUID of customer
            classification_id: UUID of new classification
            reason: Text reason for change
            user_id: UUID of user making the change
            
        Returns:
            CustomerClassificationHistory object if successful, None if error
        """
        try:
            customer = Customer.objects.get(id=customer_id, tenant_id=self.tenant_id)
            new_classification = CustomerClassification.objects.get(id=classification_id, tenant_id=self.tenant_id)
            
            # Create history record
            history = CustomerClassificationHistory(
                tenant_id=self.tenant_id,
                customer=customer,
                previous_classification=customer.classification,
                new_classification=new_classification,
                automatic=False,
                changed_by=user_id,
                reason=reason
            )
            history.save()
            
            # Update customer
            customer.classification = new_classification
            customer.save(update_fields=['classification'])
            
            # Invalidate cache
            cache_key = f"customer_classification_evaluation_{self.tenant_id}_{customer.id}"
            cache.delete(cache_key)
            
            # Send notification
            self.send_classification_change_notification(history)
            
            return history
            
        except (Customer.DoesNotExist, CustomerClassification.DoesNotExist) as e:
            logger.error(f"Error manually changing classification: {str(e)}")
            return None
    
    def send_classification_change_notification(self, history):
        """
        Send notification about classification change
        
        Args:
            history: CustomerClassificationHistory object
        """
        try:
            # Skip if notification already sent
            if history.notification_sent:
                return
            
            # Implement notification logic here (email, SMS, in-app, etc.)
            # For example:
            # from notifications.services import NotificationService
            # notification_service = NotificationService(self.tenant_id)
            # notification_service.send_classification_change_notification(history)
            
            # Update history record
            history.notification_sent = True
            history.notification_date = timezone.now()
            history.save(update_fields=['notification_sent', 'notification_date'])
            
            logger.info(f"Sent classification change notification for customer {history.customer.id}")
            
        except Exception as e:
            logger.error(f"Error sending classification change notification: {str(e)}")
    
    def check_for_downgrades(self):
        """
        Check for customers that no longer meet their current classification
        and should be downgraded
        
        Returns:
            dict: Results of the downgrade operation
        """
        customers = Customer.objects.filter(
            tenant_id=self.tenant_id,
            classification__isnull=False
        )
        
        results = {
            'total': customers.count(),
            'evaluated': 0,
            'downgraded': 0,
            'maintained': 0,
            'errors': 0,
            'changes': []
        }
        
        for customer in customers:
            try:
                current_classification = customer.classification
                
                # Skip if no classification
                if not current_classification:
                    continue
                
                # Check if customer still qualifies for current classification
                qualifies, _ = self._check_qualification(customer, current_classification)
                
                if not qualifies:
                    # Find next highest classification they qualify for
                    new_classification = self._find_appropriate_classification(customer, current_classification.level)
                    
                    if new_classification and new_classification.id != current_classification.id:
                        # Create history record
                        history = CustomerClassificationHistory(
                            tenant_id=self.tenant_id,
                            customer=customer,
                            previous_classification=current_classification,
                            new_classification=new_classification,
                            automatic=True,
                            reason="Automatic downgrade - no longer meets criteria"
                        )
                        history.save()
                        
                        # Update customer
                        customer.classification = new_classification
                        customer.save(update_fields=['classification'])
                        
                        # Invalidate cache
                        cache_key = f"customer_classification_evaluation_{self.tenant_id}_{customer.id}"
                        cache.delete(cache_key)
                        
                        # Send notification
                        self.send_classification_change_notification(history)
                        
                        results['downgraded'] += 1
                        results['changes'].append({
                            'customer_id': str(customer.id),
                            'customer_name': customer.full_name,
                            'previous': current_classification.name,
                            'new': new_classification.name
                        })
                    else:
                        results['maintained'] += 1
                else:
                    results['maintained'] += 1
                
                results['evaluated'] += 1
                    
            except Exception as e:
                logger.error(f"Error checking for downgrade for customer {customer.id}: {str(e)}")
                results['errors'] += 1
        
        return results
    
    def _find_appropriate_classification(self, customer, current_level):
        """
        Find the highest classification level the customer qualifies for
        that is below their current level
        
        Args:
            customer: Customer object
            current_level: Current classification level
            
        Returns:
            CustomerClassification object or None
        """
        # Get all active classifications ordered by level (highest first)
        classifications = CustomerClassification.objects.filter(
            tenant_id=self.tenant_id,
            is_active=True,
            level__lt=current_level  # Only levels below current
        ).order_by('-level')
        
        # Try each classification
        for classification in classifications:
            qualifies, _ = self._check_qualification(customer, classification)
            if qualifies:
                return classification
        
        # If no suitable classification found, return the lowest level
        lowest = CustomerClassification.objects.filter(
            tenant_id=self.tenant_id,
            is_active=True
        ).order_by('level').first()
        
        return lowest 