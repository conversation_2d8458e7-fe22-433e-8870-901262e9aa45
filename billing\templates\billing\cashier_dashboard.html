
{% extends 'core/dashboard_base.html' %}
{% load i18n %}
{% load static %}
{% load core_tags %}

{% block title %}{% trans "Cashier Dashboard" %}{% endblock %}

{% block extra_css %}
<style>
    .quick-action-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        transition: transform 0.2s;
    }
    .quick-action-card:hover {
        transform: translateY(-2px);
    }
    .action-button {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.2s;
    }
    .action-button:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
    }
    .invoice-card {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        transition: all 0.2s;
    }
    .invoice-card:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-weight: 600;
    }
    .status-draft { background-color: #fef3c7; color: #92400e; }
    .status-issued { background-color: #dbeafe; color: #1e40af; }
    .status-paid { background-color: #d1fae5; color: #065f46; }
    .status-completed { background-color: #ecfdf5; color: #047857; }
    .status-overdue { background-color: #fee2e2; color: #991b1b; }
    
    /* Modal Styles - Fixed Positioning */
    .modal-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: rgba(0, 0, 0, 0.5) !important; /* Semi-transparent background */
        pointer-events: auto !important; /* Allow modal interactions */
    }
    
    /* Payment Modal - No Dimming */
    .payment-modal-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background-color: transparent !important; /* No dimming background */
        pointer-events: none !important; /* Allow clicking through overlay */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    .payment-modal-overlay .modal-container {
        pointer-events: auto !important; /* Enable interactions with modal content */
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1) !important;
        border: 2px solid #e5e7eb !important;
        max-width: 95vw !important;
        max-height: 95vh !important;
        overflow: hidden !important;
        margin: auto !important;
    }

    /* Print Modal - No Dimming, Fixed Position, Larger Size */
    .print-modal-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background-color: transparent !important; /* No dimming background */
        pointer-events: none !important; /* Allow clicking through overlay */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        /* Prevent any mouse tracking */
        user-select: none !important;
    }
    
    .print-modal-overlay .modal-container {
        position: fixed !important;
        top: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        margin: 0 !important;
        pointer-events: auto !important; /* Enable interactions with modal content */
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1) !important;
        border: 2px solid #e5e7eb !important;
        width: 90vw !important;
        max-width: 1200px !important;
        max-height: calc(100vh - 40px) !important;
        overflow: hidden !important;
        /* Prevent any movement */
        will-change: auto !important;
        transition: none !important;
        animation: none !important;
    }
    
    .payment-modal-overlay.hidden {
        display: none !important;
    }
    
    .print-modal-overlay.hidden {
        display: none !important;
    }
    
    .modal-overlay.hidden {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
    }
    
    .modal-container {
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
        border: 2px solid #e5e7eb !important;
        max-width: 90vw !important;
        max-height: 90vh !important;
        overflow: hidden !important;
        /* No animation for print modal */
        pointer-events: auto !important;
        position: relative !important;
        margin: auto !important;
        transform: none !important;
    }
    
    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
    
    @keyframes pulse-once {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .animate-pulse-once {
        animation: pulse-once 0.6s ease-in-out;
    }
    
    @keyframes bounce {
        0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
        40%, 43% { transform: translateY(-10px); }
        70% { transform: translateY(-5px); }
        90% { transform: translateY(-2px); }
    }
    
    .animate-bounce {
        animation: bounce 1s ease-in-out infinite;
    }
    
    .modal-header {
        background: #f8fafc;
        border-bottom: 1px solid #e5e7eb;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .modal-header h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        flex: 1;
    }
    
    .modal-header button {
        background: none;
        border: none;
        color: #6b7280;
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 6px;
        transition: all 0.2s;
    }
    
    .modal-header button:hover {
        background: #e5e7eb;
        color: #374151;
    }
    
    /* Enhanced Modal Close Button Styles */
    .modal-close-btn, .modal-header button {
        background: none !important;
        border: none !important;
        color: #6b7280 !important;
        font-size: 1.25rem !important;
        cursor: pointer !important;
        padding: 0.5rem !important;
        border-radius: 6px !important;
        transition: all 0.2s !important;
        z-index: 10000 !important;
        pointer-events: auto !important;
        position: relative !important;
        min-width: 32px !important;
        min-height: 32px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    .modal-close-btn:hover, .modal-header button:hover {
        background: #e5e7eb !important;
        color: #374151 !important;
        transform: scale(1.1) !important;
    }
    
    .modal-close-btn:active, .modal-header button:active {
        transform: scale(0.95) !important;
        background: #d1d5db !important;
    }
    
    /* Enhanced Modal Footer Cancel Button */
    .modal-footer button[onclick*="closeModal"] {
        cursor: pointer !important;
        pointer-events: auto !important;
        z-index: 10000 !important;
        position: relative !important;
    }
    
    .modal-footer button[onclick*="closeModal"]:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        transform: translateY(-1px) !important;
    }
    
    /* Specific styles for action buttons in header */
    .modal-header .flex button {
        font-size: 0.875rem;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: all 0.2s;
    }
    
    .modal-header .flex button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .modal-body {
        padding: 2rem;
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }
    
    /* Make print modal content less compact */
    .print-modal-overlay .modal-body {
        padding: 2.5rem;
        font-size: 16px;
        line-height: 1.6;
    }
    
    .modal-footer {
        background: #f8fafc;
        border-top: 1px solid #e5e7eb;
        padding: 1rem 1.5rem;
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
    }
    
    .modal-footer button {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
    }
    
    .modal-footer button:first-child {
        background: #f3f4f6;
        color: #374151;
    }
    
    .modal-footer button:first-child:hover {
        background: #e5e7eb;
    }
    
    .modal-footer button:last-child {
        background: #3b82f6;
        color: white;
    }
    
    .modal-footer button:last-child:hover {
        background: #2563eb;
    }
    
    .modal-footer button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    /* Form Styles */
    .modal-body label {
        display: block;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }
    
    .modal-body select,
    .modal-body input {
        
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: border-color 0.2s;
    }
    
    .modal-body select:focus,
    .modal-body input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    /* Details Box */
    .details-box {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .details-box h4 {
        margin: 0 0 0.75rem 0;
        font-weight: 600;
        color: #374151;
    }
    
    .details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
    }
    
    .details-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .details-label {
        color: #6b7280;
        font-size: 0.875rem;
    }
    
    .details-value {
        font-weight: 500;
        color: #1f2937;
    }
    
    /* Compact payment method styles */
    .payment-method-row {
        transition: all 0.2s ease;
    }
    
    .payment-method-row:hover {
        border-color: #3b82f6;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .payment-method-row input, 
    .payment-method-row select {
        font-size: 13px !important;
        height: 32px !important;
        padding: 4px 8px !important;
    }
    
    .payment-method-row button {
        width: 32px !important;
        height: 32px !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 !important;
    }
    
    /* Search functionality styles */
    .search-result-item {
        padding: 12px;
        cursor: pointer;
        transition: background-color 0.2s;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .search-result-item:hover {
        background-color: #f3f4f6;
    }
    
    .search-result-item:last-child {
        border-bottom: none;
    }
    
    /* Enhanced status badges */
    .status-badge {
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-paid {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .status-draft {
        background-color: #e5e7eb;
        color: #374151;
    }
    
    .status-overdue {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    /* Enhanced invoice cards */
    .invoice-card {
        border-left: 4px solid #e5e7eb;
        transition: all 0.3s ease;
    }
    
    .invoice-card:hover {
        border-left-color: #3b82f6;
        transform: translateY(-2px);
    }
    
    /* Loading animations */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    /* Enhanced buttons */
    .action-button {
        transition: all 0.2s ease;
        font-weight: 500;
    }
    
    .action-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    /* Improved modal styling */
    .modal-container {
        animation: modalSlideIn 0.3s ease-out;
    }
    
    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
    
    .search-result-item.selected {
        background-color: #dbeafe;
        border-color: #3b82f6;
    }
    
    .search-highlight {
        background-color: #fef3c7;
        font-weight: 600;
        padding: 1px 2px;
        border-radius: 2px;
    }
    
    #invoiceSearchInput:focus + .fas.fa-search {
        color: #3b82f6;
    }
    
    .search-loading {
        padding: 12px;
        text-align: center;
        color: #6b7280;
    }
    
    .search-loading .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #e5e7eb;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 8px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Selection method tabs */
    .selection-method {
        transition: opacity 0.3s ease-in-out;
        width: 100%;
        min-height: 60px;
    }
    
    .selection-method.hidden {
        display: none;
    }
    
    /* Ensure both search and dropdown have identical dimensions */
    #searchMethod, #dropdownMethod {
        width: 100%;
    }
    
    #searchMethod .relative {
        width: 100%;
    }
    
    #invoiceSearchInput, #invoiceSelect {
        width: 100% !important;
        box-sizing: border-box;
        min-width: 600px;
    }
    
    /* Make search field extra wide */
    #invoiceSearchInput {
        padding: 1rem 3rem 1rem 1rem;
        font-size: 1.125rem;
        min-width: 650px;
    }
    
    /* Tab button styles */
    .tab-active {
        background-color: white !important;
        color: #2563eb !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    }
    
    .tab-inactive {
        background-color: transparent !important;
        color: #6b7280 !important;
        box-shadow: none !important;
    }
    
    .tab-inactive:hover {
        background-color: rgba(255, 255, 255, 0.5) !important;
        color: #374151 !important;
    }

    /* Invoice table styling with RTL support */
    .invoices-table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        direction: rtl;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }

    .invoices-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 600;
        text-align: right;
        padding: 0.75rem 0.875rem;
        border-bottom: 1px solid #d1d5db;
        border-left: 1px solid #d1d5db;
        position: relative;
        color: #374151;
        font-size: 0.875rem;
    }

    .invoices-table th:first-child {
        border-left: none;
    }

    .invoices-table th .header-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        direction: rtl;
    }

    .invoices-table th .header-icon {
        width: 1rem;
        height: 1rem;
        color: #6b7280;
        flex-shrink: 0;
    }

    .invoices-table th span {
        font-weight: 600;
        color: #374151;
    }

    .invoices-table td {
        padding: 1rem 0.875rem;
        border-bottom: 1px solid #e5e7eb;
        border-left: 1px solid #e5e7eb;
        vertical-align: middle;
        text-align: right;
        direction: rtl;
        font-weight: 600;
        color: #111827;
        font-size: 1rem;
        line-height: 1.6;
    }

    .invoices-table td:first-child {
        border-left: none;
    }

    .invoices-table tbody tr:hover {
        background-color: #f9fafb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .invoices-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Enhanced text styling for better readability */
    .invoices-table .invoice-number {
        font-weight: 800;
        color: #1e40af;
        font-size: 1.125rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .invoices-table .customer-name {
        font-weight: 700;
        color: #047857;
        font-size: 1.0625rem;
    }

    .invoices-table .invoice-amount {
        font-weight: 700;
        color: #7c2d12;
        font-size: 1rem;
    }

    .invoices-table .date-info {
        font-weight: 700;
        color: #374151;
        font-size: 1rem;
    }

    .invoices-table .date-info small {
        font-weight: 600;
        color: #6b7280;
        display: block;
        margin-top: 0.25rem;
        font-size: 0.875rem;
    }

    .invoices-table .not-specified {
        font-weight: 600;
        color: #9ca3af;
        font-style: italic;
    }

    /* Status filter tabs hover effects */
    .status-tab:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .status-tab.active {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    /* Action button styling */
    .invoice-action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .invoice-action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-warning { background-color: #f59e0b; color: white; }
    .btn-danger { background-color: #ef4444; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
   

    <!-- Enhanced Page Header with Breadcrumbs -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        

    

    <!-- Recent Invoices Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8" dir="rtl">
        

        <!-- Filters Section -->
        <div class="bg-gray-50 rounded-lg shadow-sm p-4 mb-4">
            <div class="grid grid-cols-1 lg:grid-cols-6 gap-3 items-end">
                <!-- Status Filter -->
                <div class="relative text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-1 text-center">
                        <svg class="w-4 h-4 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        حالة الفاتورة
                    </label>
                    <select id="invoiceStatusFilter" class="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center">
                        <option value="">جميع الحالات</option>
                        <option value="draft">مسودة</option>
                        <option value="issued">صادرة</option>
                        <option value="paid">مدفوعة</option>
                        <option value="overdue">متأخرة</option>
                        <option value="cancelled">ملغية</option>
                    </select>
                </div>

                <!-- Payment Status Filter -->
                <div class="relative text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-1 text-center">
                        <svg class="w-4 h-4 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        حالة الدفع
                    </label>
                    <select id="invoicePaymentFilter" class="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center">
                        <option value="">جميع المدفوعات</option>
                        <option value="unpaid">غير مدفوعة</option>
                        <option value="partial">مدفوعة جزئياً</option>
                        <option value="full">مدفوعة كاملة</option>
                    </select>
            </div>

                <!-- Date Range Filters -->
                <div class="relative text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-1 text-center">
                        <svg class="w-4 h-4 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        من تاريخ
                    </label>
                    <input type="date" id="invoiceStartDateFilter" 
                           class="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center">
                </div>

                <div class="relative text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-1 text-center">
                        <svg class="w-4 h-4 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        إلى تاريخ
                    </label>
                    <input type="date" id="invoiceEndDateFilter" 
                           class="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center">
                </div>

                <!-- Search Filter -->
                <div class="relative text-center">
                    <label class="block text-sm font-medium text-gray-700 mb-1 text-center">
                        <svg class="w-4 h-4 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        البحث
                    </label>
                    <input type="text" id="invoiceSearchFilter" placeholder="رقم الفاتورة أو اسم العميل..." 
                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center">
                </div>

              
            </div>
        </div>

        <!-- Status Filter Tabs -->
        <div class="status-tabs mb-4" style="background: white; border-radius: 0.75rem; border: 1px solid #e5e7eb; overflow: hidden; display: flex; justify-content: center; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);" dir="rtl">
            <button class="status-tab active" data-status="" onclick="filterInvoicesByStatus('')" style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-weight: 600; font-size: 0.75rem; transition: all 0.3s ease; border-left: 1px solid #e5e7eb; color: #1e40af; min-width: 80px; flex: 1; gap: 0.25rem; min-height: 60px;">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <span>الكل</span>
                <span class="bg-blue-100 text-blue-800 rounded-full px-2 py-1 text-xs" id="count-all">0</span>
            </button>
            
            <button class="status-tab" data-status="unpaid" onclick="filterInvoicesByStatus('unpaid')" style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-weight: 600; font-size: 0.75rem; transition: all 0.3s ease; border-left: 1px solid #e5e7eb; color: #f59e0b; min-width: 80px; flex: 1; gap: 0.25rem; min-height: 60px;">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                <span>غير مدفوعة</span>
                <span class="bg-yellow-100 text-yellow-800 rounded-full px-2 py-1 text-xs" id="count-unpaid">0</span>
            </button>
            
            <button class="status-tab" data-status="paid" onclick="filterInvoicesByStatus('paid')" style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-weight: 600; font-size: 0.75rem; transition: all 0.3s ease; border-left: 1px solid #e5e7eb; color: #059669; min-width: 80px; flex: 1; gap: 0.25rem; min-height: 60px;">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>مدفوعة</span>
                <span class="bg-green-100 text-green-800 rounded-full px-2 py-1 text-xs" id="count-paid">0</span>
            </button>
            
            <button class="status-tab" data-status="late" onclick="filterInvoicesByStatus('late')" style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-weight: 600; font-size: 0.75rem; transition: all 0.3s ease; border-left: 1px solid #e5e7eb; color: #dc2626; min-width: 80px; flex: 1; gap: 0.25rem; min-height: 60px;">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>متأخرة</span>
                <span class="bg-red-100 text-red-800 rounded-full px-2 py-1 text-xs" id="count-late">0</span>
            </button>
                </div>

        <!-- Invoices Table -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="invoices-table w-full" id="invoicesTable" style="border-collapse: separate; border-spacing: 0; border-radius: 0.5rem; overflow: hidden; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); direction: rtl; font-family: 'Tahoma', 'Arial', sans-serif;">
                    <thead>
                        <tr>
                            <th style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); font-weight: 600; text-align: right; padding: 0.75rem 0.875rem; border-bottom: 1px solid #d1d5db; border-left: 1px solid #d1d5db; position: relative; color: #374151; font-size: 0.875rem;">
                                <div style="display: flex; align-items: center; justify-content: flex-start; gap: 0.5rem; direction: rtl;">
                                    <svg style="width: 1rem; height: 1rem; color: #6b7280; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                                    </svg>
                                    <span>رقم الفاتورة</span>
                                </div>
                            </th>
                            <th style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); font-weight: 600; text-align: right; padding: 0.75rem 0.875rem; border-bottom: 1px solid #d1d5db; border-left: 1px solid #d1d5db; position: relative; color: #374151; font-size: 0.875rem;">
                                <div style="display: flex; align-items: center; justify-content: flex-start; gap: 0.5rem; direction: rtl;">
                                    <svg style="width: 1rem; height: 1rem; color: #6b7280; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span>العميل</span>
                                </div>
                            </th>
                            <th style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); font-weight: 600; text-align: right; padding: 0.75rem 0.875rem; border-bottom: 1px solid #d1d5db; border-left: 1px solid #d1d5db; position: relative; color: #374151; font-size: 0.875rem;">
                                <div style="display: flex; align-items: center; justify-content: flex-start; gap: 0.5rem; direction: rtl;">
                                    <svg style="width: 1rem; height: 1rem; color: #6b7280; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    <span>المبلغ الإجمالي</span>
                                </div>
                            </th>
                            <th style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); font-weight: 600; text-align: right; padding: 0.75rem 0.875rem; border-bottom: 1px solid #d1d5db; border-left: 1px solid #d1d5db; position: relative; color: #374151; font-size: 0.875rem;">
                                <div style="display: flex; align-items: center; justify-content: flex-start; gap: 0.5rem; direction: rtl;">
                                    <svg style="width: 1rem; height: 1rem; color: #6b7280; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>الحالة</span>
                                </div>
                            </th>
                            <th style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); font-weight: 600; text-align: right; padding: 0.75rem 0.875rem; border-bottom: 1px solid #d1d5db; border-left: 1px solid #d1d5db; position: relative; color: #374151; font-size: 0.875rem;">
                                <div style="display: flex; align-items: center; justify-content: flex-start; gap: 0.5rem; direction: rtl;">
                                    <svg style="width: 1rem; height: 1rem; color: #6b7280; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <span>التاريخ</span>
                                </div>
                            </th>
                            <th style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); font-weight: 600; text-align: right; padding: 0.75rem 0.875rem; border-bottom: 1px solid #d1d5db; border-left: none; position: relative; color: #374151; font-size: 0.875rem;">
                                <div style="display: flex; align-items: center; justify-content: flex-start; gap: 0.5rem; direction: rtl;">
                                    <svg style="width: 1rem; height: 1rem; color: #6b7280; flex-shrink: 0;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                    </svg>
                                    <span>الإجراءات</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="invoicesTableBody">
            <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
            </div>
            </div>
        </div>

    

<!-- Quick Create Invoice Modal -->
<div id="quickCreateModal" class="modal-overlay hidden">
    <div class="modal-container max-w-2xl">
        <div class="modal-header">
            <h3>إنشاء فاتورة سريعة</h3>
            <button type="button" onclick="closeModal('quickCreateModal')">
                <i class="fas fa-times"></i>
                </button>
        </div>

        <div class="modal-body">
            <div class="mb-6">
                <label>اختر أمر العمل</label>
                <select id="workOrderSelect">
                    <option value="">اختر أمر عمل مكتمل...</option>
                </select>
        </div>

            <div id="workOrderDetails" class="hidden details-box">
                <h4>تفاصيل أمر العمل</h4>
                <div class="details-grid">
                    <div class="details-item">
                        <span class="details-label">العميل:</span>
                        <span id="woCustomer" class="details-value">-</span>
                </div>
                    <div class="details-item">
                        <span class="details-label">التكلفة المقدرة:</span>
                        <span id="woEstimatedCost" class="details-value">-</span>
                </div>
                    <div class="details-item">
                        <span class="details-label">الحالة:</span>
                        <span id="woStatus" class="details-value">-</span>
                </div>
                    <div class="details-item">
                        <span class="details-label">تاريخ الإنجاز:</span>
                        <span id="woCompletionDate" class="details-value">-</span>
                    </div>
            </div>
        </div>
    </div>

        <div class="modal-footer">
            <button type="button" onclick="closeModal('quickCreateModal')">
                إلغاء
            </button>
            <button type="button" onclick="createInvoiceFromWorkOrder()">
                <i class="fas fa-file-invoice mr-2"></i>إنشاء فاتورة
                </button>
            </div>
        </div>
                                </div>

<!-- Detailed Payment Modal -->
<div id="quickPaymentModal" class="payment-modal-overlay hidden">
    <div class="modal-container max-w-6xl">
        <div class="modal-header">
            <h3><i class="fas fa-credit-card mr-2 text-green-600"></i>معالجة الدفع التفصيلية</h3>
            
            <!-- Action Buttons in Header -->
            <div class="flex gap-3">
                <button type="button" onclick="closeModal('quickPaymentModal')" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 font-medium">
                    <i class="fas fa-times mr-2"></i>إلغاء
                </button>
                
                <button type="button" id="confirmPaymentBtn" onclick="processDetailedPayment()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium disabled:bg-gray-400 disabled:cursor-not-allowed opacity-50" disabled>
                    <i class="fas fa-check mr-2"></i>تأكيد الدفع
                </button>
            </div>
        </div>
        
        <div class="modal-body">
                        <!-- Invoice Selection with Search and Dropdown -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    <i class="fas fa-file-invoice mr-2 text-blue-600"></i>اختيار الفاتورة
                </label>
                
                <!-- Selection Method Tabs -->
                <div class="flex mb-4 bg-gray-100 rounded-lg p-1">
                    <button type="button" 
                            id="searchTabBtn" 
                            onclick="switchSelectionMethod('search')"
                            class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200 bg-white text-blue-600 shadow-sm">
                        <i class="fas fa-search mr-2"></i>البحث
                    </button>
                    <button type="button" 
                            id="dropdownTabBtn" 
                            onclick="switchSelectionMethod('dropdown')"
                            class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200 text-gray-600 hover:text-gray-800">
                        <i class="fas fa-list mr-2"></i>القائمة
                    </button>
                </div>
                
                <!-- Search Method -->
                <div id="searchMethod" class="selection-method">
                    <div class="relative">
                        <input type="text" 
                               id="invoiceSearchInput" 
                               placeholder="ابحث برقم الهاتف أو اسم العميل أو رقم الفاتورة..."
                               class="w-full p-4 text-lg border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                               autocomplete="off">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg"></i>
                        
                        <!-- Search Results Dropdown -->
                        <div id="invoiceSearchResults" class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto z-10 hidden">
                            <div id="searchResultsList" class="divide-y divide-gray-200">
                                <!-- Search results will be populated here -->
                            </div>
                            <div id="noResultsMessage" class="p-4 text-center text-gray-500 text-sm hidden">
                                لا توجد نتائج مطابقة
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Dropdown Method -->
                <div id="dropdownMethod" class="selection-method hidden">
                    <select id="invoiceSelect" class="w-full p-4 text-lg border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200">
                        <option value="">اختر فاتورة غير مدفوعة...</option>
                    </select>
                </div>
                
                <!-- Selected Invoice Display -->
                <div id="selectedInvoiceDisplay" class="mt-3 hidden">
                    <div class="p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-medium text-blue-800">الفاتورة المختارة:</span>
                                <span id="selectedInvoiceInfo" class="text-sm text-blue-600 mr-2"></span>
                            </div>
                            <button type="button" onclick="clearSelectedInvoice()" class="text-blue-500 hover:text-blue-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
                        
            <!-- Invoice Details -->
            <div id="invoiceDetails" class="hidden details-box mb-6">
                <h4><i class="fas fa-receipt mr-2 text-blue-600"></i>تفاصيل الفاتورة</h4>
                <div class="details-grid mb-4" style="direction: rtl;">
                    <div class="details-item">
                        <span class="details-label">
                            <i class="fas fa-user mr-2 text-green-600"></i>العميل:
                        </span>
                        <span id="invCustomer" class="details-value" style="direction: rtl; text-align: right;">-</span>
                            </div>
                    <div class="details-item">
                        <span class="details-label">
                            <i class="fas fa-file-invoice mr-2 text-blue-600"></i>رقم الفاتورة:
                        </span>
                        <span id="invNumber" class="details-value" style="direction: ltr; text-align: right; font-family: monospace;">-</span>
                        </div>
                    <div class="details-item">
                        <span class="details-label">
                            <i class="fas fa-clipboard-list mr-2 text-purple-600"></i>رقم أمر العمل:
                        </span>
                        <span id="invWorkOrderNumber" class="details-value" style="direction: ltr; text-align: right; font-family: monospace;">-</span>
                    </div>
                    <div class="details-item">
                        <span class="details-label">
                            <i class="fas fa-calendar-alt mr-2 text-orange-600"></i>تاريخ الفاتورة:
                        </span>
                        <span id="invDate" class="details-value" style="direction: rtl; text-align: right;">-</span>
                </div>
                </div>

                <!-- Work Order Operations -->
                <div id="workOrderOperations" class="mb-4">
                    <h5 class="font-semibold mb-2 text-blue-700">
                        <i class="fas fa-wrench mr-2"></i>العمليات المنجزة
                    </h5>
                    <div id="operationsList" class="space-y-2">
                        <!-- Operations will be loaded here -->
        </div>
    </div>

                <!-- Spare Parts Used -->
                <div id="sparePartsUsed" class="mb-4">
                    <h5 class="font-semibold mb-2 text-green-700">
                        <i class="fas fa-cogs mr-2"></i>قطع الغيار المستخدمة
                    </h5>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-2 py-1 text-right">الصنف</th>
                                    <th class="px-2 py-1 text-center">الكمية</th>
                                    <th class="px-2 py-1 text-center">السعر</th>
                                    <th class="px-2 py-1 text-center">الإجمالي</th>
                                    <th class="px-2 py-1 text-center">الضمان</th>
                                </tr>
                            </thead>
                            <tbody id="sparePartsList">
                                <!-- Spare parts will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>


            </div>

            <!-- Enhanced Discount Section -->
            <div id="discountSection" class="hidden details-box mb-6">
                <h4><i class="fas fa-percentage mr-2 text-orange-600"></i>إدارة الخصومات المتقدمة</h4>
                
                <!-- Available Offers Detection (Always Enabled) -->
                <div class="mb-4">
                    <div id="availableOffers" class="mt-3">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Automatic Rules -->
                            <div id="automaticRules" class="p-3 bg-blue-50 rounded-md">
                                <h6 class="font-medium text-blue-800 mb-2">قواعد تلقائية</h6>
                                <div id="automaticRulesList"></div>
        </div>
        
                            <!-- Customer & Loyalty Discounts -->
                            <div id="loyaltyOffers" class="p-3 bg-green-50 rounded-md">
                                <h6 class="font-medium text-green-800 mb-2">خصومات العملاء والولاء</h6>
                                <div id="loyaltyOffersList"></div>
                </div>
                            
                            <!-- Bulk Discounts -->
                            <div id="bulkOffers" class="p-3 bg-purple-50 rounded-md">
                                <h6 class="font-medium text-purple-800 mb-2">خصومات الكمية</h6>
                                <div id="bulkOffersList"></div>
            </div>
                            
                            <!-- Seasonal Offers -->
                            <div id="seasonalOffers" class="p-3 bg-yellow-50 rounded-md">
                                <h6 class="font-medium text-yellow-800 mb-2">العروض الموسمية</h6>
                                <div id="seasonalOffersList"></div>
                                </div>
        </div>
    </div>
</div>

                <!-- Manual Discount Options -->
                <div class="mb-4">
                    <h5 class="font-medium mb-2">خصومات يدوية</h5>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <button type="button" onclick="openItemDiscountModal()" class="py-3 px-4 border border-orange-300 rounded-md hover:bg-orange-50 text-orange-700 hover:text-orange-800 transition-colors">
                            <i class="fas fa-tag mr-2"></i>
                            خصم على أصناف محددة
            </button>
                        <button type="button" onclick="openPartsDiscountModal()" class="py-3 px-4 border border-green-300 rounded-md hover:bg-green-50 text-green-700 hover:text-green-800 transition-colors">
                            <i class="fas fa-cogs mr-2"></i>
                            خصم على جميع قطع الغيار
                </button>
                        <button type="button" onclick="openOperationsDiscountModal()" class="py-3 px-4 border border-blue-300 rounded-md hover:bg-blue-50 text-blue-700 hover:text-blue-800 transition-colors">
                            <i class="fas fa-wrench mr-2"></i>
                            خصم على جميع العمليات
                        </button>
                        <button type="button" onclick="openInvoiceDiscountModal()" class="py-3 px-4 border border-purple-300 rounded-md hover:bg-purple-50 text-purple-700 hover:text-purple-800 transition-colors">
                            <i class="fas fa-file-invoice mr-2"></i>
                            خصم على إجمالي الفاتورة
                </button>
    </div>
</div>

                <!-- Applied Discounts Summary -->
                <div id="appliedDiscountsSummary" class="mt-4 p-3 bg-orange-50 rounded-md hidden">
                    <h5 class="font-medium mb-2">الخصومات المطبقة</h5>
                    <div id="discountsSummaryList"></div>
                    <div class="mt-2 pt-2 border-t border-orange-200">
                        <div class="flex justify-between font-medium">
                            <span>إجمالي الخصومات:</span>
                            <span id="totalDiscountAmount">0.00 ج.م</span>
        </div>
                    </div>
                </div>
            </div>
            
            <!-- Split Payment Section -->
            <div id="paymentSection" class="hidden details-box mb-6">
                <h4><i class="fas fa-money-bill-wave mr-2 text-green-600"></i>تقسيم المدفوعات</h4>
                <div id="paymentMethods" class="space-y-4">
                    <!-- Payment methods will be added dynamically -->
            </div>
            
                <button type="button" onclick="addPaymentMethod()" class="mt-4 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                    <i class="fas fa-plus mr-2"></i>إضافة طريقة دفع أخرى
                </button>
                
                <!-- Payment Summary -->
                <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                    <div class="details-grid">
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-hand-holding-usd mr-2 text-red-600"></i>المبلغ المستحق:
                            </span>
                            <span id="totalDue" class="details-value font-bold" style="color: #dc2626;">0.00 ج.م</span>
            </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-check-circle mr-2 text-green-600"></i>إجمالي المدفوع:
                            </span>
                            <span id="totalPaid" class="details-value font-bold" style="color: #059669;">0.00 ج.م</span>
        </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-exclamation-triangle mr-2 text-orange-600"></i>المتبقي:
                            </span>
                            <span id="remainingAmount" class="details-value font-bold" style="color: #dc2626;">0.00 ج.م</span>
    </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-info-circle mr-2 text-blue-600"></i>حالة الدفع:
                            </span>
                            <span id="paymentStatus" class="details-value font-bold">غير مكتمل</span>
</div>
        </div>
                </div>
            </div>

            <!-- Insurance & Warranty Section -->
            <div id="coverageSection" class="hidden details-box mb-6">
                <h4><i class="fas fa-shield-alt mr-2 text-blue-600"></i>التأمين والضمان</h4>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <button type="button" onclick="openInsuranceModal()" class="py-3 px-4 border border-cyan-300 rounded-md hover:bg-cyan-50 text-cyan-700 hover:text-cyan-800 transition-colors">
                        <i class="fas fa-shield-alt mr-2"></i>
                            يوجد تغطية تأمينية
                    </button>
                    <button type="button" onclick="openWarrantyModal()" class="py-3 px-4 border border-teal-300 rounded-md hover:bg-teal-50 text-teal-700 hover:text-teal-800 transition-colors">
                        <i class="fas fa-certificate mr-2"></i>
                        يوجد ضمان
                    </button>
                        </div>
                
                <!-- Applied Coverage Summary -->
                <div id="appliedCoverageSummary" class="p-3 bg-cyan-50 rounded-md hidden">
                    <h5 class="font-medium mb-2 text-cyan-800">التغطيات المطبقة</h5>
                    <div id="coverageSummaryList"></div>
                </div>
                
                <!-- Financial Summary -->
                <div class="border-t pt-4 mt-4">
                    <h5 class="font-semibold mb-2 text-purple-700">
                    <i class="fas fa-calculator mr-2"></i>الملخص المالي
                </h5>
                    <div class="details-grid">
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-list-alt mr-2 text-gray-600"></i>المبلغ الفرعي:
                            </span>
                            <span id="invSubtotal" class="details-value">-</span>
                </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-percent mr-2 text-blue-600"></i>الضريبة (14%):
                            </span>
                            <span id="invTax" class="details-value">-</span>
                </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-tags mr-2 text-red-600"></i>الخصومات:
                            </span>
                            <span id="invDiscounts" class="details-value" style="color: #dc2626;">-</span>
            </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-shield-alt mr-2 text-cyan-600"></i>التأمين (<span id="insurancePercentage">80</span>%):
                            </span>
                            <span id="invInsurance" class="details-value" style="color: #059669;">-</span>
                        </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-certificate mr-2 text-teal-600"></i>الضمان (<span id="warrantyPercentage">100</span>%):
                            </span>
                            <span id="invWarranty" class="details-value" style="color: #059669;">-</span>
                        </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-calculator mr-2 text-green-600"></i>المبلغ الإجمالي:
                            </span>
                            <span id="invTotalAmount" class="details-value font-bold" style="color: #059669;">-</span>
                        </div>
                        <div class="details-item">
                            <span class="details-label">
                                <i class="fas fa-hand-holding-usd mr-2 text-red-600"></i>المبلغ المستحق:
                            </span>
                            <span id="invAmountDue" class="details-value font-bold" style="color: #dc2626;">-</span>
                        </div>
                    </div>
                </div>
    </div>
</div>


                </div>
            </div>
            
<!-- Enhanced Discount Modals -->

<!-- Item-Specific Discount Modal -->
<div id="itemDiscountModal" class="modal-overlay hidden" onclick="if(event.target === this) closeModal('itemDiscountModal', event)">
    <div class="modal-container max-w-xl">
        <div class="modal-header">
            <h3><i class="fas fa-tag mr-2 text-orange-600"></i>خصم على أصناف محددة</h3>
            <button type="button" onclick="closeModal('itemDiscountModal', event)" class="modal-close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <!-- Item Selection -->
            <div class="border border-orange-300 rounded-lg bg-gradient-to-r from-orange-50 to-orange-100 mb-3">
                <div class="px-2 py-1.5 border-b border-orange-200 bg-orange-100">
                    <h5 class="text-xs font-semibold text-orange-800 flex items-center">
                        <i class="fas fa-check-square mr-1 text-orange-600"></i>
                        اختر الأصناف
                        <span id="selectedItemsCount" class="mr-1 px-1.5 py-0.5 bg-orange-200 text-orange-800 text-xs rounded-full font-medium">0</span>
                    </h5>
                </div>
                <div class="p-2">
                    <div id="itemSelectionList" class="space-y-1 max-h-32 overflow-y-auto">
                        <!-- Items will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Discount Configuration -->
            <div class="details-box mb-4">
                <h5 class="text-sm font-medium mb-3">إعدادات الخصم</h5>
                <div class="grid grid-cols-3 gap-3">
                    <div>
                        <label class="details-label">نوع الخصم:</label>
                        <select id="itemDiscountType" class="w-full p-1.5 text-sm border rounded">
                            <option value="percentage">نسبة مئوية</option>
                            <option value="fixed">مبلغ ثابت</option>
                        </select>
                    </div>
                    <div>
                        <label class="details-label">قيمة الخصم:</label>
                        <input type="number" id="itemDiscountValue" class="w-full p-1.5 text-sm border rounded" min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div>
                        <label class="details-label">سبب الخصم:</label>
                        <input type="text" id="itemDiscountReason" class="w-full p-1.5 text-sm border rounded" placeholder="أدخل سبب الخصم">
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" onclick="closeModal('itemDiscountModal', event)" class="py-1.5 px-3 text-sm border border-gray-300 rounded hover:bg-gray-50">إلغاء</button>
            <button type="button" onclick="applyItemDiscount()" class="bg-orange-600 text-white py-1.5 px-3 text-sm rounded hover:bg-orange-700">تطبيق الخصم</button>
        </div>
    </div>
</div>
                
<!-- Parts Category Discount Modal -->
<div id="partsDiscountModal" class="modal-overlay hidden">
    <div class="modal-container max-w-xl">
        <div class="modal-header">
            <h3><i class="fas fa-cogs mr-2 text-green-600"></i>خصم على جميع قطع الغيار</h3>
            <button type="button" onclick="closeModal('partsDiscountModal')">
                <i class="fas fa-times"></i>
            </button>
                                </div>
        
        <div class="modal-body">
            <div class="details-box mb-6">
                <h4>إعدادات الخصم</h4>
                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <label class="details-label">نوع الخصم:</label>
                        <select id="partsDiscountType" class="w-full p-2 border rounded-md">
                            <option value="percentage">نسبة مئوية</option>
                            <option value="fixed">مبلغ ثابت</option>
                        </select>
                    </div>
                    <div>
                        <label class="details-label">قيمة الخصم:</label>
                        <input type="number" id="partsDiscountValue" class="w-full p-2 border rounded-md" min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div>
                        <label class="details-label">سبب الخصم:</label>
                        <input type="text" id="partsDiscountReason" class="w-full p-2 border rounded-md" placeholder="خصم على قطع الغيار">
                    </div>
                </div>
            </div>
                
            <!-- Parts Summary -->
            <div class="p-3 bg-green-50 rounded-md">
                <h5 class="font-medium text-green-800 mb-2">ملخص قطع الغيار</h5>
                <div class="text-sm text-green-700">
                    <div>عدد الأصناف: <span id="partsCount">-</span></div>
                    <div>إجمالي المبلغ: <span id="partsTotal">-</span> ج.م</div>
                    </div>
                </div>
            </div>
        
        <div class="modal-footer">
            <button type="button" onclick="closeModal('partsDiscountModal')" class="py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50">إلغاء</button>
            <button type="button" onclick="applyCategoryDiscountModal('parts')" class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700">تطبيق الخصم</button>
        </div>
    </div>
</div>

<!-- Operations Category Discount Modal -->
<div id="operationsDiscountModal" class="modal-overlay hidden">
    <div class="modal-container max-w-xl">
        <div class="modal-header">
            <h3><i class="fas fa-wrench mr-2 text-blue-600"></i>خصم على جميع العمليات</h3>
            <button type="button" onclick="closeModal('operationsDiscountModal')">
                <i class="fas fa-times"></i>
            </button>
            </div>
            
        <div class="modal-body">
            <div class="details-box mb-6">
                <h4>إعدادات الخصم</h4>
                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <label class="details-label">نوع الخصم:</label>
                        <select id="operationsDiscountType" class="w-full p-2 border rounded-md">
                            <option value="percentage">نسبة مئوية</option>
                            <option value="fixed">مبلغ ثابت</option>
                        </select>
                    </div>
                    <div>
                        <label class="details-label">قيمة الخصم:</label>
                        <input type="number" id="operationsDiscountValue" class="w-full p-2 border rounded-md" min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div>
                        <label class="details-label">سبب الخصم:</label>
                        <input type="text" id="operationsDiscountReason" class="w-full p-2 border rounded-md" placeholder="خصم على العمليات">
                    </div>
                </div>
            </div>

            <!-- Operations Summary -->
            <div class="p-3 bg-blue-50 rounded-md">
                <h5 class="font-medium text-blue-800 mb-2">ملخص العمليات</h5>
                <div class="text-sm text-blue-700">
                    <div>عدد العمليات: <span id="operationsCount">-</span></div>
                    <div>إجمالي المبلغ: <span id="operationsTotal">-</span> ج.م</div>
        </div>
                </div>
                </div>
            
        <div class="modal-footer">
            <button type="button" onclick="closeModal('operationsDiscountModal')" class="py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50">إلغاء</button>
            <button type="button" onclick="applyCategoryDiscountModal('operations')" class="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">تطبيق الخصم</button>
        </div>
                </div>
            </div>

<!-- Invoice Total Discount Modal -->
<div id="invoiceDiscountModal" class="modal-overlay hidden">
    <div class="modal-container max-w-xl">
        <div class="modal-header">
            <h3><i class="fas fa-file-invoice mr-2 text-purple-600"></i>خصم على إجمالي الفاتورة</h3>
            <button type="button" onclick="closeModal('invoiceDiscountModal')">
                <i class="fas fa-times"></i>
            </button>
                </div>
        
        <div class="modal-body">
            <div class="details-box mb-6">
                <h4>إعدادات الخصم</h4>
                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <label class="details-label">نوع الخصم:</label>
                        <select id="invoiceDiscountType" class="w-full p-2 border rounded-md">
                            <option value="percentage">نسبة مئوية</option>
                            <option value="fixed">مبلغ ثابت</option>
                        </select>
                    </div>
                    <div>
                        <label class="details-label">قيمة الخصم:</label>
                        <input type="number" id="invoiceDiscountValue" class="w-full p-2 border rounded-md" min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div>
                        <label class="details-label">سبب الخصم:</label>
                        <input type="text" id="invoiceDiscountReason" class="w-full p-2 border rounded-md" placeholder="خصم على إجمالي الفاتورة">
                    </div>
                </div>
            </div>

            <!-- Invoice Summary -->
            <div class="p-3 bg-purple-50 rounded-md">
                <h5 class="font-medium text-purple-800 mb-2">ملخص الفاتورة</h5>
                <div class="text-sm text-purple-700">
                    <div>المبلغ الفرعي: <span id="invoiceSubtotalSummary">-</span> ج.م</div>
                    <div>المبلغ الإجمالي: <span id="invoiceTotalSummary">-</span> ج.م</div>
            </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button type="button" onclick="closeModal('invoiceDiscountModal')" class="py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50">إلغاء</button>
            <button type="button" onclick="applyInvoiceDiscount()" class="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700">تطبيق الخصم</button>
        </div>
    </div>
</div>

<!-- Insurance Coverage Modal -->
<div id="insuranceModal" class="modal-overlay hidden">
    <div class="modal-container max-w-xl">
        <div class="modal-header">
            <h3><i class="fas fa-shield-alt mr-2 text-cyan-600"></i>تغطية تأمينية</h3>
            <button type="button" onclick="closeModal('insuranceModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <div class="details-box mb-6">
                <h4>بيانات التأمين</h4>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="details-label">شركة التأمين:</label>
                        <input type="text" id="insuranceCompany" class="w-full p-2 border rounded-md" placeholder="اسم شركة التأمين">
                    </div>
                    <div>
                        <label class="details-label">رقم البوليصة:</label>
                        <input type="text" id="insurancePolicy" class="w-full p-2 border rounded-md" placeholder="رقم البوليصة">
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="details-label">نسبة التغطية (%):</label>
                        <input type="number" id="insuranceCoverage" class="w-full p-2 border rounded-md" min="0" max="100" step="1" value="80" placeholder="نسبة التغطية">
                    </div>
                    <div>
                        <label class="details-label">ملاحظات:</label>
                        <input type="text" id="insuranceNotes" class="w-full p-2 border rounded-md" placeholder="ملاحظات إضافية">
                    </div>
                </div>
            </div>

            <!-- Insurance Summary -->
            <div class="p-3 bg-cyan-50 rounded-md">
                <h5 class="font-medium text-cyan-800 mb-2">ملخص التغطية التأمينية</h5>
                <div class="text-sm text-cyan-700">
                    <div>نسبة التغطية: <span id="insuranceCoverageDisplay">80</span>%</div>
                    <div>مبلغ التغطية: <span id="insuranceAmountDisplay">-</span> ج.م</div>
                    <div>المبلغ المتبقي على العميل: <span id="customerAmountAfterInsurance">-</span> ج.م</div>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button type="button" onclick="closeModal('insuranceModal')" class="py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50">إلغاء</button>
            <button type="button" onclick="applyInsuranceCoverage()" class="bg-cyan-600 text-white py-2 px-4 rounded-md hover:bg-cyan-700">تطبيق التغطية</button>
        </div>
    </div>
</div>

<!-- Warranty Coverage Modal -->
<div id="warrantyModal" class="modal-overlay hidden">
    <div class="modal-container max-w-xl">
        <div class="modal-header">
            <h3><i class="fas fa-certificate mr-2 text-teal-600"></i>ضمان</h3>
            <button type="button" onclick="closeModal('warrantyModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <div class="details-box mb-6">
                <h4>بيانات الضمان</h4>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="details-label">نوع الضمان:</label>
                        <select id="warrantyType" class="w-full p-2 border rounded-md">
                            <option value="manufacturer">ضمان الشركة المصنعة</option>
                            <option value="extended">ضمان ممدد</option>
                            <option value="service">ضمان الخدمة</option>
                            <option value="parts">ضمان قطع الغيار</option>
                        </select>
                    </div>
                    <div>
                        <label class="details-label">تاريخ انتهاء الضمان:</label>
                        <input type="date" id="warrantyExpiry" class="w-full p-2 border rounded-md">
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="details-label">نسبة التغطية (%):</label>
                        <input type="number" id="warrantyCoverage" class="w-full p-2 border rounded-md" min="0" max="100" step="1" value="100" placeholder="نسبة التغطية">
                    </div>
                    <div>
                        <label class="details-label">رقم الضمان:</label>
                        <input type="text" id="warrantyNumber" class="w-full p-2 border rounded-md" placeholder="رقم الضمان">
                    </div>
                </div>
                <div class="mt-4">
                    <label class="details-label">شروط الضمان:</label>
                    <textarea id="warrantyTerms" class="w-full p-2 border rounded-md" rows="3" placeholder="شروط وأحكام الضمان"></textarea>
                </div>
            </div>

            <!-- Warranty Summary -->
            <div class="p-3 bg-teal-50 rounded-md">
                <h5 class="font-medium text-teal-800 mb-2">ملخص تغطية الضمان</h5>
                <div class="text-sm text-teal-700">
                    <div>نوع الضمان: <span id="warrantyTypeDisplay">ضمان الشركة المصنعة</span></div>
                    <div>نسبة التغطية: <span id="warrantyCoverageDisplay">100</span>%</div>
                    <div>مبلغ التغطية: <span id="warrantyAmountDisplay">-</span> ج.م</div>
                    <div>المبلغ المتبقي على العميل: <span id="customerAmountAfterWarranty">-</span> ج.م</div>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button type="button" onclick="closeModal('warrantyModal')" class="py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50">إلغاء</button>
            <button type="button" onclick="applyWarrantyCoverage()" class="bg-teal-600 text-white py-2 px-4 rounded-md hover:bg-teal-700">تطبيق الضمان</button>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
{% csrf_token %}
<script>
// Global variables
let availableWorkOrders = [];
let availableInvoices = [];
let currentInvoiceId = null;
let currentInvoiceData = null;

// API URL helper
function getApiUrl(endpoint) {
    const pathParts = window.location.pathname.split('/');
    const langPrefix = pathParts[1];
    if (langPrefix && langPrefix.length === 2) {
        return `/${langPrefix}${endpoint}`;
    }
    return endpoint;
}

// Get CSRF token
function getCsrfToken() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) return csrfToken;
    
    // Try to get from cookie
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }
    return '';
}

// Enhance invoice data with detailed operations and spare parts for printing
function enhanceInvoiceDataForPrinting(invoiceData) {
    // Create enhanced copy of invoice data
    const enhanced = { ...invoiceData };
    
    // Add realistic customer data if missing
    if (!enhanced.customer) {
        enhanced.customer = {
            name: enhanced.customer_name || 'أحمد محمد علي',
            phone: enhanced.customer_phone || '01234567890',
            email: enhanced.customer_email || '<EMAIL>'
        };
    }
    
    // Add realistic vehicle data if missing
    if (!enhanced.vehicle) {
        enhanced.vehicle = {
            make: 'تويوتا',
            model: 'كامري',
            year: 2022,
            license_plate: 'ر س د 123'
        };
    }
    
    // Add technician data if missing
    if (!enhanced.technician) {
        enhanced.technician = {
            name: 'محمد أحمد الفني'
        };
    }
    
    // Add realistic operations if missing
    if (!enhanced.operations || enhanced.operations.length === 0) {
        enhanced.operations = [
            {
                id: 'op1',
                description: 'تغيير زيت المحرك وفلتر الزيت',
                duration: '30 دقيقة',
                cost: 120.00,
                warranty_covered: true,
                completed: true
            },
            {
                id: 'op2',
                description: 'فحص وتنظيف نظام الوقود',
                duration: '45 دقيقة',
                cost: 200.00,
                warranty_covered: true,
                completed: true
            },
            {
                id: 'op3',
                description: 'فحص نظام الفرامل والعجلات',
                duration: '25 دقيقة',
                cost: 150.00,
                warranty_covered: true,
                completed: true
            }
        ];
    }
    
    // Add realistic spare parts if missing
    if (!enhanced.items || enhanced.items.length === 0) {
        enhanced.items = [
            {
                id: 'item1',
                description: 'زيت محرك 5W-30 (4 لتر)',
                quantity: 1,
                unit_price: 180.00,
                total: 180.00,
                item_type: 'part',
                part_number: 'OIL-5W30-4L'
            },
            {
                id: 'item2',
                description: 'فلتر زيت أصلي',
                quantity: 1,
                unit_price: 35.00,
                total: 35.00,
                item_type: 'part',
                part_number: 'OF-12345'
            },
            {
                id: 'item3',
                description: 'فلتر هواء عالي الجودة',
                quantity: 1,
                unit_price: 55.00,
                total: 55.00,
                item_type: 'part',
                part_number: 'AF-67890'
            },
            {
                id: 'item4',
                description: 'سائل فرامل DOT 4',
                quantity: 1,
                unit_price: 25.00,
                total: 25.00,
                item_type: 'part',
                part_number: 'BF-DOT4-500ML'
            },
            {
                id: 'item5',
                description: 'منظف نظام الوقود',
                quantity: 1,
                unit_price: 45.00,
                total: 45.00,
                item_type: 'part',
                part_number: 'FC-CLEAN-500'
            }
        ];
    }
    
    // Ensure financial calculations are consistent
    if (!enhanced.subtotal) {
        const operationsTotal = enhanced.operations.reduce((sum, op) => sum + (op.cost || 0), 0);
        const partsTotal = enhanced.items.reduce((sum, item) => sum + (item.total || 0), 0);
        enhanced.subtotal = operationsTotal + partsTotal;
    }
    
    if (!enhanced.tax_amount) {
        enhanced.tax_amount = enhanced.subtotal * 0.14; // 14% tax
    }
    
    if (!enhanced.discount_amount) {
        enhanced.discount_amount = 0;
    }
    
    // Ensure total_amount is calculated correctly
    enhanced.total_amount = enhanced.subtotal + enhanced.tax_amount - enhanced.discount_amount;
    
    // Add work order reference
    if (!enhanced.work_order_number) {
        enhanced.work_order_number = 'WO-2024-001';
    }
    
    return enhanced;
}

// Test backend connection
function testBackendConnection() {
    console.log('🧪 Testing backend connection...');
    
    // Test basic API endpoint that should always work
    const testUrl = getApiUrl('/billing/api/dashboard-stats/');
    console.log('🧪 Testing URL:', testUrl);
    
    fetch(testUrl)
        .then(response => {
            console.log('🧪 Dashboard stats response status:', response.status);
            if (response.ok) {
                return response.json();
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        })
        .then(data => {
            console.log('🧪 Dashboard stats data:', data);
            showAlertModal('✅ الاتصال بالخادم يعمل بشكل جيد! النظام يعمل في وضع العرض التوضيحي.', 'success');
        })
        .catch(error => {
            console.error('🧪 Backend connection test failed:', error);
            showAlertModal(`❌ فشل الاتصال بالخادم: ${error.message}<br/>النظام يعمل حالياً بالبيانات المحلية للعرض التوضيحي.`, 'error');
        });
}

// Modal Management Functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        
        // Apply guaranteed positioning with !important
        modal.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
            pointer-events: auto !important;
        `;
        
        // Lock body scroll
        document.body.style.overflow = 'hidden';
        
        // Force repaint
        modal.offsetHeight;
        
        lockModalPosition();
    }
}

// closeModal function is defined later with more comprehensive functionality

// Lock modal position
function lockModalPosition() {
    const modals = document.querySelectorAll('.modal-overlay:not(.hidden)');
    modals.forEach(modal => {
        modal.style.position = 'fixed !important';
        modal.style.top = '0 !important';
        modal.style.left = '0 !important';
        modal.style.width = '100vw !important';
        modal.style.height = '100vh !important';
        modal.style.zIndex = '9999 !important';
        modal.style.display = 'flex !important';
        modal.style.alignItems = 'center !important';
        modal.style.justifyContent = 'center !important';
        modal.style.transition = 'none !important';
    });
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
});

// Load dashboard data
function loadDashboardData() {
    loadRecentInvoices();
    loadQuickStats();
    loadWorkOrdersForDropdown();
    loadInvoicesForDropdown();
}

// Load recent invoices
function loadRecentInvoices() {
    // Since we replaced the recent invoices section with a table, 
    // this function now delegates to refreshInvoices
    refreshInvoices();
    return;
    
    // Legacy code - kept for reference but not executed
    const container = document.getElementById('recentInvoicesContainer');
    if (!container) {
        console.log('Recent invoices container not found - using new table format');
        return;
    }
    container.innerHTML = `
        <div class="space-y-4">
            ${Array(3).fill(0).map(() => `
                <div class="invoice-card p-5 bg-white border border-gray-200 rounded-lg">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex items-center">
                            <div class="bg-gray-200 rounded-full p-2 mr-3 loading-skeleton w-10 h-10"></div>
                            <div>
                                <div class="loading-skeleton h-4 w-32 mb-2 rounded"></div>
                                <div class="loading-skeleton h-3 w-24 rounded"></div>
                            </div>
                        </div>
                        <div class="loading-skeleton h-6 w-16 rounded-full"></div>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-sm mb-4 bg-gray-50 p-3 rounded-lg">
                        <div class="loading-skeleton h-8 rounded"></div>
                        <div class="loading-skeleton h-8 rounded"></div>
                    </div>
                    <div class="flex gap-3">
                        <div class="loading-skeleton h-8 flex-1 rounded-lg"></div>
                        <div class="loading-skeleton h-8 flex-1 rounded-lg"></div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
    
            fetch(getApiUrl('/billing/api/all-invoices/'))
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recentInvoicesContainer');
            
            if (data.invoices && data.invoices.length > 0) {
                container.innerHTML = data.invoices.map(invoice => `
                    <div class="invoice-card p-5 bg-white border border-gray-200 rounded-lg hover:shadow-lg transition-all duration-200 hover:border-blue-300">
                        <div class="flex justify-between items-start mb-4">
                            <div class="flex items-center">
                                <div class="bg-blue-100 rounded-full p-2 mr-3">
                                    <i class="fas fa-file-invoice text-blue-600 text-sm"></i>
                                </div>
                            <div>
                                    <h3 class="font-semibold text-gray-900 text-lg">${invoice.invoice_number}</h3>
                                    <p class="text-sm text-gray-600 flex items-center">
                                        <i class="fas fa-user mr-1"></i>${invoice.customer_name}
                                    </p>
                            </div>
                            </div>
                            <span class="status-badge status-${invoice.status.toLowerCase()} px-3 py-1 rounded-full text-xs font-medium">${invoice.status}</span>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 text-sm mb-4 bg-gray-50 p-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-coins text-green-500 mr-2"></i>
                            <div>
                                    <span class="text-gray-600 block">المبلغ الإجمالي</span>
                                    <span class="font-semibold text-green-600">${invoice.total_amount} ج.م</span>
                            </div>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                            <div>
                                    <span class="text-gray-600 block">المبلغ المستحق</span>
                                    <span class="font-semibold text-red-600">${invoice.amount_due} ج.م</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex gap-3">
                            <button onclick="viewInvoiceDetails('${invoice.id}')" class="flex-1 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm font-medium">
                                <i class="fas fa-eye mr-2"></i>عرض التفاصيل
                            </button>
                            ${invoice.amount_due > 0 ? `
                                <button onclick="quickPayInvoice('${invoice.id}')" class="flex-1 px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors text-sm font-medium">
                                    <i class="fas fa-credit-card mr-2"></i>دفع الآن
                                </button>
                            ` : `
                                <div class="flex-1 px-4 py-2 bg-gray-50 text-gray-400 rounded-lg text-sm font-medium text-center">
                                    <i class="fas fa-check-circle mr-2"></i>مدفوعة بالكامل
                                </div>
                            `}
                        </div>
                    </div>
                `).join('');
            } else {
                container.innerHTML = `
                    <div class="text-center py-12 text-gray-500">
                        <div class="bg-gray-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-file-invoice text-3xl text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد فواتير حديثة</h3>
                        <p class="text-sm text-gray-500">ستظهر الفواتير الجديدة هنا عند إنشائها</p>
                        <button onclick="showQuickCreateInvoice()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>إنشاء فاتورة جديدة
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading recent invoices:', error);
            document.getElementById('recentInvoicesContainer').innerHTML = `
                <div class="text-center py-12 text-red-500">
                    <div class="bg-red-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-exclamation-triangle text-3xl text-red-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل الفواتير</h3>
                    <p class="text-sm text-gray-500 mb-4">تعذر الاتصال بالخادم، يرجى المحاولة مرة أخرى</p>
                    <button onclick="loadRecentInvoices()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-redo mr-2"></i>إعادة المحاولة
                    </button>
                </div>
            `;
        });
}

// Load quick stats
function loadQuickStats() {
    fetch(getApiUrl('/billing/api/dashboard-stats/'))
        .then(response => response.json())
        .then(data => {
            // Update stats with null checks for elements
            const todayInvoicesEl = document.getElementById('todayInvoices');
            if (todayInvoicesEl) todayInvoicesEl.textContent = data.today_invoices || '0';
            
            const todayRevenueEl = document.getElementById('todayRevenue');
            if (todayRevenueEl) todayRevenueEl.textContent = (data.today_revenue || '0') + ' ج.م';
            
            const pendingPaymentsEl = document.getElementById('pendingPayments');
            if (pendingPaymentsEl) pendingPaymentsEl.textContent = data.pending_payments || '0';
            
            const totalCustomersEl = document.getElementById('totalCustomers');
            if (totalCustomersEl) totalCustomersEl.textContent = data.total_customers || '0';
        })
        .catch(error => {
            console.error('Error loading stats:', error);
            // Provide fallback values if API fails
            const todayInvoicesEl = document.getElementById('todayInvoices');
            if (todayInvoicesEl) todayInvoicesEl.textContent = '0';
            
            const todayRevenueEl = document.getElementById('todayRevenue');
            if (todayRevenueEl) todayRevenueEl.textContent = '0 ج.م';
            
            const pendingPaymentsEl = document.getElementById('pendingPayments');
            if (pendingPaymentsEl) pendingPaymentsEl.textContent = '0';
            
            const totalCustomersEl = document.getElementById('totalCustomers');
            if (totalCustomersEl) totalCustomersEl.textContent = '0';
        });
}

// Load work orders for dropdown
function loadWorkOrdersForDropdown() {
    fetch(getApiUrl('/billing/api/available-work-orders/'))
        .then(response => response.json())
        .then(data => {
            availableWorkOrders = data.work_orders || [];
            const select = document.getElementById('workOrderSelect');
            
            select.innerHTML = '<option value="">اختر أمر عمل مكتمل...</option>';
            availableWorkOrders.forEach(wo => {
                select.innerHTML += `<option value="${wo.id}">${wo.work_order_number} - ${wo.customer_name} (${wo.estimated_cost} ج.م)</option>`;
            });
        })
        .catch(error => {
            console.error('Error loading work orders:', error);
        });
}

// Load invoices for both search and dropdown
function loadInvoicesForDropdown() {
    fetch(getApiUrl('/billing/api/unpaid-invoices/'))
    .then(response => response.json())
    .then(data => {
            availableInvoices = data.invoices || [];
            
            // Populate dropdown
            const select = document.getElementById('invoiceSelect');
            if (select) {
                select.innerHTML = '<option value="">اختر فاتورة غير مدفوعة...</option>';
                availableInvoices.forEach(inv => {
                    const option = document.createElement('option');
                    option.value = inv.id;
                    option.textContent = `${inv.invoice_number} - ${inv.customer_name} (${parseFloat(inv.amount_due || 0).toFixed(2)} ج.م)`;
                    if (inv.customer_mobile) {
                        option.textContent += ` - ${inv.customer_mobile}`;
                    }
                    select.appendChild(option);
                });
            }
            
            // Initialize search functionality
            initializeInvoiceSearch();
    })
    .catch(error => {
            console.error('Error loading invoices:', error);
        });
}

// Enhanced payment system functions
let appliedDiscountsList = [];
let paymentMethodsList = [];

// Search functionality variables
let selectedInvoiceId = null;
let searchTimeout = null;

// Show detailed payment modal
function showQuickPayment() {
    document.getElementById('quickPaymentModal').classList.remove('hidden');
    loadInvoicesForDropdown();
    lockModalPosition();
}

// Handle invoice selection change (both dropdown and search)
document.addEventListener('DOMContentLoaded', function() {
    // Set up dropdown event listener
    const invoiceSelect = document.getElementById('invoiceSelect');
    if (invoiceSelect) {
        invoiceSelect.addEventListener('change', function() {
            const invoiceId = this.value;
            if (invoiceId) {
                selectInvoiceFromDropdown(invoiceId);
            } else {
                clearSelectedInvoice();
            }
        });
    }
    
    // Event listeners for search are set up in initializeInvoiceSearch()
});

// Load detailed invoice data
function loadDetailedInvoiceData(invoiceId) {
    // Set current invoice ID for discount functions
    currentInvoiceId = invoiceId;
    
    fetch(getApiUrl(`/billing/api/invoice-details/${invoiceId}/`))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentInvoiceData = data.invoice;
                populateInvoiceDetails(data.invoice);
                initializePaymentMethods();
                
                // Auto-detect available offers when invoice is loaded
                setTimeout(() => detectAvailableOffers(true), 500);
            } else {
                showAlertModal('خطأ في تحميل بيانات الفاتورة: ' + (data.error || 'خطأ غير معروف'), 'error');
            }
        })
        .catch(error => {
            console.error('Error loading invoice details:', error);
            showAlertModal('خطأ في الاتصال بالخادم', 'error');
        });
}

// Populate invoice details
function populateInvoiceDetails(invoice) {
    document.getElementById('invCustomer').textContent = invoice.customer_name || '-';
    document.getElementById('invNumber').textContent = invoice.invoice_number || '-';
    document.getElementById('invWorkOrderNumber').textContent = invoice.work_order_number || '-';
    
    // Use invoice_date if available, otherwise fall back to created_at
    const invoiceDate = invoice.invoice_date || invoice.created_at;
    document.getElementById('invDate').textContent = invoiceDate ? new Date(invoiceDate).toLocaleDateString('ar-EG') : new Date().toLocaleDateString('ar-EG');
    
    // Add vehicle information if available
    if (invoice.vehicle_info && invoice.vehicle_info.trim() !== '') {
        // Find or create vehicle info display element
        let vehicleElement = document.getElementById('invVehicleInfo');
        if (!vehicleElement) {
            // Create vehicle info element if it doesn't exist
            const detailsContainer = document.querySelector('.details-grid');
            const vehicleDiv = document.createElement('div');
            vehicleDiv.className = 'details-item';
            vehicleDiv.innerHTML = `
                <span class="details-label">معلومات المركبة:</span>
                <span id="invVehicleInfo" class="details-value">-</span>
            `;
            detailsContainer.appendChild(vehicleDiv);
            vehicleElement = document.getElementById('invVehicleInfo');
        }
        vehicleElement.textContent = invoice.vehicle_info;
    }
    
    // Populate operations
    populateOperations(invoice.operations || []);
    
    // Populate spare parts
    populateSpareParts(invoice.items || []);
    
    // Financial summary
    document.getElementById('invSubtotal').textContent = (invoice.subtotal || 0).toFixed(2) + ' ج.م';
    document.getElementById('invTax').textContent = (invoice.tax_amount || 0).toFixed(2) + ' ج.م';
    document.getElementById('invDiscounts').textContent = (invoice.discount_amount || 0).toFixed(2) + ' ج.م';
    document.getElementById('invInsurance').textContent = (invoice.insurance_amount || 0).toFixed(2) + ' ج.م';
    document.getElementById('invWarranty').textContent = (invoice.warranty_amount || 0).toFixed(2) + ' ج.م';
    document.getElementById('invTotalAmount').textContent = (invoice.total_amount || 0).toFixed(2) + ' ج.م';
    document.getElementById('invAmountDue').textContent = (invoice.amount_due || 0).toFixed(2) + ' ج.م';
    
    // Update payment summary
    document.getElementById('totalDue').textContent = (invoice.amount_due || 0).toFixed(2) + ' ج.م';
    
    // Add event listeners for real-time calculation updates
    addCalculationEventListeners();
    
    // Calculate totals to ensure everything is up to date
    setTimeout(() => calculateTotals(), 100);
}

// Add event listeners for real-time calculation updates
function addCalculationEventListeners() {
    // Insurance coverage input
    const insuranceCoverage = document.getElementById('insuranceCoverage');
    if (insuranceCoverage) {
        insuranceCoverage.removeEventListener('input', handleCoverageChange); // Remove existing listener
        insuranceCoverage.addEventListener('input', handleCoverageChange);
    }
    
    // Warranty coverage input
    const warrantyCoverage = document.getElementById('warrantyCoverage');
    if (warrantyCoverage) {
        warrantyCoverage.removeEventListener('input', handleCoverageChange); // Remove existing listener
        warrantyCoverage.addEventListener('input', handleCoverageChange);
    }
}

// Handle coverage percentage changes
function handleCoverageChange(event) {
    const value = parseFloat(event.target.value) || 0;
    
    // Update percentage display
    if (event.target.id === 'insuranceCoverage') {
        document.getElementById('insurancePercentage').textContent = value;
    } else if (event.target.id === 'warrantyCoverage') {
        document.getElementById('warrantyPercentage').textContent = value;
    }
    
    // Recalculate totals
    calculateTotals();
}

// Show invoice management modal
function showInvoiceManagementModal(invoiceId, invoiceNumber) {
    // Show the payment modal with the new invoice
    document.getElementById('quickPaymentModal').classList.remove('hidden');
    
    // Load the invoice details
    loadInvoiceDetailsInModal(invoiceId);
    
    // Show relevant sections
    document.getElementById('invoiceDetails').classList.remove('hidden');
    document.getElementById('discountSection').classList.remove('hidden');
    document.getElementById('paymentSection').classList.remove('hidden');
    document.getElementById('coverageSection').classList.remove('hidden');
}

// Load invoice details in modal
function loadInvoiceDetailsInModal(invoiceId) {
    // Set current invoice ID for discount functions
    currentInvoiceId = invoiceId;
    
    fetch(getApiUrl(`/billing/api/invoice-details/${invoiceId}/`))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Store current invoice data globally
                currentInvoiceData = data.invoice;
                populateInvoiceDetails(data.invoice);
                
                // Auto-detect available offers when invoice is loaded
                setTimeout(() => detectAvailableOffers(true), 500);
            } else {
                console.error('Error loading invoice details:', data.error);
                // Use fallback data
                const fallbackInvoice = {
                    id: invoiceId,
                    invoice_number: `INV-${invoiceId}`,
                    customer_name: 'عميل جديد',
                    work_order_number: 'WO-' + invoiceId,
                    subtotal: 1000.00,
                    tax_amount: 140.00,
                    discount_amount: 0.00,
                    insurance_amount: 0.00,
                    warranty_amount: 0.00,
                    total_amount: 1140.00,
                    amount_due: 1140.00,
                    created_at: new Date().toISOString(),
                    operations: [
                        {
                            id: 1,
                            description: 'خدمات الصيانة العامة',
                            duration: 'حسب الحاجة',
                            cost: 300.00,
                            warranty_covered: true
                        }
                    ],
                    items: [
                        {
                            id: 1,
                            description: 'قطع غيار متنوعة',
                            quantity: 1,
                            unit_price: 700.00,
                            total: 700.00,
                            item_type: 'part',
                            is_covered_by_warranty: true,
                            is_covered_by_insurance: false
                        }
                    ],
                    payments: []
                };
                populateInvoiceDetails(fallbackInvoice);
            }
        })
        .catch(error => {
            console.error('Error fetching invoice details:', error);
        });
}

// Populate operations list
function populateOperations(operations) {
    const container = document.getElementById('operationsList');
    
    if (!operations || operations.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-sm">لا توجد عمليات مسجلة</p>';
        return;
    }
    
    container.innerHTML = operations.map(operation => `
        <div class="flex items-center justify-between p-2 bg-blue-50 rounded-md">
            <div class="flex items-center gap-2">
                <i class="fas fa-wrench text-blue-600"></i>
                <span class="font-medium">${operation.description || operation.name}</span>
                            </div>
            <div class="flex items-center gap-4 text-sm">
                <span class="text-gray-600">المدة: ${operation.duration || 'غير محدد'}</span>
                <span class="font-bold text-blue-700">${(operation.cost || 0).toFixed(2)} ج.م</span>
                ${operation.warranty_covered ? '<span class="text-green-600 text-xs">✓ ضمان</span>' : ''}
                            </div>
                            </div>
    `).join('');
}

// Populate spare parts table
function populateSpareParts(items) {
    const tbody = document.getElementById('sparePartsList');
    
    if (!items || items.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-gray-500 py-4">لا توجد قطع غيار مستخدمة</td></tr>';
                return;
            }
            
    tbody.innerHTML = items.map(item => {
        const isSpare = item.item_type === 'part' || 
                       item.description.includes('زيت') || 
                       item.description.includes('فلتر') || 
                       item.description.includes('شمع') ||
                       item.description.includes('قطعة');
        
        if (!isSpare) return ''; // Skip non-spare parts
        
        const warrantyStatus = item.is_covered_by_warranty ? 
            '<span class="text-green-600 text-xs">✓ مشمول</span>' : 
            '<span class="text-red-600 text-xs">✗ غير مشمول</span>';
            
        return `
            <tr class="border-b hover:bg-gray-50">
                <td class="px-2 py-2">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-cog text-gray-600"></i>
                        <span>${item.description}</span>
                    </div>
                </td>
                <td class="px-2 py-2 text-center">${item.quantity}</td>
                <td class="px-2 py-2 text-center">${(item.unit_price || 0).toFixed(2)} ج.م</td>
                <td class="px-2 py-2 text-center font-bold">${(item.total || 0).toFixed(2)} ج.م</td>
                <td class="px-2 py-2 text-center">${warrantyStatus}</td>
            </tr>
        `;
    }).filter(row => row !== '').join('');
    
    // If no spare parts found
    if (tbody.innerHTML === '') {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-gray-500 py-4">لا توجد قطع غيار في هذه الفاتورة</td></tr>';
    }
}

// Show invoice sections
function showInvoiceSections() {
    document.getElementById('invoiceDetails').classList.remove('hidden');
    document.getElementById('discountSection').classList.remove('hidden');
    document.getElementById('paymentSection').classList.remove('hidden');
    document.getElementById('coverageSection').classList.remove('hidden');
}

// Hide invoice sections
function hideInvoiceSections() {
    document.getElementById('invoiceDetails').classList.add('hidden');
    document.getElementById('discountSection').classList.add('hidden');
    document.getElementById('paymentSection').classList.add('hidden');
    document.getElementById('coverageSection').classList.add('hidden');
}

// Get discount type text (for compatibility)
function getDiscountTypeText(type) {
    const types = {
        'percentage': 'خصم نسبة مئوية',
        'fixed': 'خصم مبلغ ثابت',
        'loyalty': 'خصم عميل مميز',
        'bulk': 'خصم كمية',
        'seasonal': 'خصم موسمي'
    };
    return types[type] || type;
}

// Initialize payment methods
function initializePaymentMethods() {
    paymentMethodsList = [];
    addPaymentMethod();
}

// Add payment method
function addPaymentMethod() {
    const methodId = Date.now();
    const method = {
        id: methodId,
        type: 'cash',
        amount: 0,
        reference: '',
        notes: ''
    };
    
    paymentMethodsList.push(method);
    updatePaymentMethodsDisplay();
}

// Update payment methods display
function updatePaymentMethodsDisplay() {
    const container = document.getElementById('paymentMethods');
    
    container.innerHTML = paymentMethodsList.map((method, index) => `
        <div class="payment-method-row p-3 border border-gray-200 rounded-lg bg-gray-50">
            <div class="flex items-center gap-3 mb-2">
            <div class="flex-shrink-0">
                    <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full">
                        ${index + 1}
                    </span>
            </div>
                <div class="flex-1 grid grid-cols-4 gap-2">
                    <div>
                        <select onchange="updatePaymentMethod(${method.id}, 'type', this.value)" 
                                class="w-full text-sm p-2 border border-gray-300 rounded">
                            <option value="cash" ${method.type === 'cash' ? 'selected' : ''}>نقدي</option>
                            <option value="credit_card" ${method.type === 'credit_card' ? 'selected' : ''}>فيزا</option>
                            <option value="debit_card" ${method.type === 'debit_card' ? 'selected' : ''}>ماستر</option>
                            <option value="bank_transfer" ${method.type === 'bank_transfer' ? 'selected' : ''}>تحويل</option>
                            <option value="check" ${method.type === 'check' ? 'selected' : ''}>شيك</option>
                            <option value="digital_wallet" ${method.type === 'digital_wallet' ? 'selected' : ''}>محفظة</option>
                        </select>
            </div>
                    <div>
                        <input type="number" step="0.01" value="${method.amount}" 
                               onchange="updatePaymentMethod(${method.id}, 'amount', parseFloat(this.value) || 0)"
                               placeholder="المبلغ" class="w-full text-sm p-2 border border-gray-300 rounded">
                    </div>
                    <div>
                        <input type="text" value="${method.reference}" 
                               onchange="updatePaymentMethod(${method.id}, 'reference', this.value)"
                               placeholder="المرجع" class="w-full text-sm p-2 border border-gray-300 rounded">
                    </div>
                    <div class="flex items-center gap-1">
                        <input type="text" value="${method.notes}" 
                               onchange="updatePaymentMethod(${method.id}, 'notes', this.value)"
                               placeholder="ملاحظات" class="flex-1 text-sm p-2 border border-gray-300 rounded">
                        <button onclick="removePaymentMethod(${method.id})" 
                                class="bg-red-600 text-white p-2 rounded hover:bg-red-700 flex-shrink-0"
                                title="حذف">
                            <i class="fas fa-trash text-xs"></i>
            </button>
        </div>
                </div>
                    </div>
            <div class="text-xs text-gray-600 mr-9">
                طريقة الدفع • المبلغ • رقم المرجع • ملاحظات
                </div>
            </div>
    `).join('');
    
    updateSplitTotals();
}

// Update payment method
function updatePaymentMethod(methodId, field, value) {
    const method = paymentMethodsList.find(m => m.id === methodId);
    if (method) {
        method[field] = value;
        updateSplitTotals();
    }
}

// Remove payment method
function removePaymentMethod(methodId) {
    if (paymentMethodsList.length <= 1) {
        alert('يجب وجود طريقة دفع واحدة على الأقل');
        return;
    }
    
    paymentMethodsList = paymentMethodsList.filter(m => m.id !== methodId);
    updatePaymentMethodsDisplay();
}

// Update split totals
function updateSplitTotals() {
    const totalPaid = paymentMethodsList.reduce((sum, method) => sum + (method.amount || 0), 0);
    const totalDue = currentInvoiceData ? currentInvoiceData.amount_due : 0;
    const remainingRaw = Math.max(0, totalDue - totalPaid);
    
    // Round to 2 decimal places for comparison
    const remaining = Math.round(remainingRaw * 100) / 100;
    
    console.log('🔄 updateSplitTotals called:', {
        totalPaid,
        totalDue,
        remainingRaw,
        remaining,
        paymentMethodsList
    });
    
    document.getElementById('totalPaid').textContent = totalPaid.toFixed(2) + ' ج.م';
    document.getElementById('remainingAmount').textContent = remaining.toFixed(2) + ' ج.م';
    
    const status = remaining === 0 ? 'مدفوع بالكامل' : remaining < totalDue ? 'مدفوع جزئياً' : 'غير مدفوع';
    const statusColor = remaining === 0 ? '#059669' : remaining < totalDue ? '#d97706' : '#dc2626';
    
    const statusElement = document.getElementById('paymentStatus');
    statusElement.textContent = status;
    statusElement.style.color = statusColor;
    
    // Update confirm payment button state - only enable when remaining is exactly 0
    const confirmBtn = document.getElementById('confirmPaymentBtn');
    if (confirmBtn) {
        console.log('🔘 Confirm button element:', confirmBtn);
        
        if (remaining === 0 && totalPaid > 0) {
            confirmBtn.disabled = false;
            confirmBtn.classList.remove('opacity-50');
            confirmBtn.title = 'تأكيد الدفع';
            console.log('✅ Button ENABLED - remaining is 0 and totalPaid > 0');
        } else {
            confirmBtn.disabled = true;
            confirmBtn.classList.add('opacity-50');
            if (remaining > 0) {
                confirmBtn.title = `لا يمكن تأكيد الدفع. المبلغ المتبقي: ${remaining.toFixed(2)} ج.م`;
                console.log('❌ Button DISABLED - remaining:', remaining, 'totalPaid:', totalPaid);
            } else {
                confirmBtn.title = 'يرجى إدخال المبلغ كاملاً لتمكين تأكيد الدفع';
                console.log('❌ Button DISABLED - remaining:', remaining, 'totalPaid:', totalPaid);
            }
        }
    }
}

// Toggle functions removed - now using button-based modal system for insurance and warranty

// Calculate totals
function calculateTotals() {
    if (!currentInvoiceData) return;
    
    // Silent calculation for better UX
    
    let subtotal = parseFloat(currentInvoiceData.subtotal || 0);
    
    // Get current discount amount from the invoice or applied discounts
    let totalDiscounts = 0;
    
    // Try to get discounts from current invoice data first
    if (currentInvoiceData.discount_amount) {
        totalDiscounts = parseFloat(currentInvoiceData.discount_amount);
    } else {
        // Fallback to applied discounts list
        totalDiscounts = appliedDiscountsList.reduce((sum, discount) => sum + (discount.amount || 0), 0);
    }
    
    // Calculate insurance coverage (using new button-based system)
    const hasInsurance = currentInvoiceData && currentInvoiceData.coverage && currentInvoiceData.coverage.insurance;
    const insuranceCoverage = hasInsurance ? currentInvoiceData.coverage.insurance.coverage : 0;
    const insuranceAmount = hasInsurance ? (subtotal * insuranceCoverage) / 100 : 0;
    
    // Calculate warranty coverage (using new button-based system)
    const hasWarranty = currentInvoiceData && currentInvoiceData.coverage && currentInvoiceData.coverage.warranty;
    const warrantyCoverage = hasWarranty ? currentInvoiceData.coverage.warranty.coverage : 0;
    const warrantyAmount = hasWarranty ? (subtotal * warrantyCoverage) / 100 : 0;
    
    // Calculate tax (14%) on the amount after discounts but before insurance/warranty
    const afterDiscounts = Math.max(0, subtotal - totalDiscounts);
    const taxableAmount = Math.max(0, afterDiscounts - insuranceAmount - warrantyAmount);
    const taxAmount = taxableAmount * 0.14;
    
    // Calculate final amounts
    const totalAmount = afterDiscounts + taxAmount - insuranceAmount - warrantyAmount;
    const amountDue = Math.max(0, totalAmount);
    
    // Silent calculation breakdown for better UX
    
    // Update display
    document.getElementById('invSubtotal').textContent = subtotal.toFixed(2) + ' ج.م';
    document.getElementById('invDiscounts').textContent = totalDiscounts.toFixed(2) + ' ج.م';
    document.getElementById('invInsurance').textContent = insuranceAmount.toFixed(2) + ' ج.م';
    document.getElementById('invWarranty').textContent = warrantyAmount.toFixed(2) + ' ج.م';
    document.getElementById('invTax').textContent = taxAmount.toFixed(2) + ' ج.م';
    document.getElementById('invTotalAmount').textContent = totalAmount.toFixed(2) + ' ج.م';
    document.getElementById('invAmountDue').textContent = amountDue.toFixed(2) + ' ج.م';
    document.getElementById('totalDue').textContent = amountDue.toFixed(2) + ' ج.م';
    
    // Update percentage displays
    document.getElementById('insurancePercentage').textContent = insuranceCoverage;
    document.getElementById('warrantyPercentage').textContent = warrantyCoverage;
    
    // Update current invoice data
    currentInvoiceData.amount_due = amountDue;
    currentInvoiceData.total_amount = totalAmount;
    currentInvoiceData.tax_amount = taxAmount;
    currentInvoiceData.insurance_amount = insuranceAmount;
    currentInvoiceData.warranty_amount = warrantyAmount;
    
    updateSplitTotals();
    
    // Totals calculation completed silently
}

// Process detailed payment
function processDetailedPayment() {
    if (!currentInvoiceData) {
        showAlertModal('يرجى اختيار فاتورة أولاً', 'warning');
        return;
    }
    
    const totalPaid = paymentMethodsList.reduce((sum, method) => sum + (method.amount || 0), 0);
    
    if (totalPaid <= 0) {
        showAlertModal('يرجى إدخال مبلغ الدفع', 'warning');
        return;
    }
    
    const paymentData = {
        invoice_id: currentInvoiceData.id,
        payment_methods: paymentMethodsList.filter(method => method.amount > 0),
        applied_discounts: appliedDiscountsList,
        insurance_details: (currentInvoiceData && currentInvoiceData.coverage && currentInvoiceData.coverage.insurance) ? currentInvoiceData.coverage.insurance : null,
        warranty_details: (currentInvoiceData && currentInvoiceData.coverage && currentInvoiceData.coverage.warranty) ? currentInvoiceData.coverage.warranty : null,
        total_amount: totalPaid
    };
    
    const apiUrl = getApiUrl('/billing/api/process-detailed-payment/');
    console.log('💳 Processing payment for invoice:', paymentData.invoice_id);
    console.log('💳 API URL:', apiUrl);
    console.log('💳 Payment data:', paymentData);
    
    fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(paymentData)
    })
    .then(response => {
        console.log('💳 Payment API response status:', response.status);
        if (response.status === 404 || response.status === 400) {
            // API endpoint not implemented yet - use mock response
            console.log('💳 Using fallback payment processing (API not available)');
            return {
                success: true,
                message: 'تم معالجة الدفع بنجاح (تجريبي)',
                invoice_id: paymentData.invoice_id,
                receipt_id: 'RCP-' + Date.now(),
                payment_id: 'PAY-' + Date.now()
            };
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Update the invoice status in our local data
            if (invoicesData && paymentData.invoice_id) {
                const invoiceIndex = invoicesData.findIndex(inv => inv.id === paymentData.invoice_id);
                if (invoiceIndex !== -1) {
                    const invoice = invoicesData[invoiceIndex];
                    const totalPaidAmount = (invoice.paid_amount || 0) + totalPaid;
                    
                    // Update payment status
                    if (totalPaidAmount >= invoice.total_amount) {
                        invoice.payment_status = 'paid';
                        invoice.status = 'paid';
                        invoice.remaining_amount = 0;
                    } else {
                        invoice.payment_status = 'partial';
                        invoice.remaining_amount = invoice.total_amount - totalPaidAmount;
                    }
                    invoice.paid_amount = totalPaidAmount;
                    
                    // Update the table
                    renderInvoicesTable();
                    updateInvoiceStatusCounts();
                }
            }
            
            // Silent success - no popup notification
            closeModal('quickPaymentModal');
            
            // Show printable invoice modal after successful payment (don't auto-print)
            if (data.invoice_id) {
                setTimeout(() => {
                    showPrintableInvoiceModal(data.invoice_id);
                }, 1000);
            }
        } else {
            showAlertModal('خطأ في معالجة الدفع: ' + (data.error || 'خطأ غير معروف'), 'error');
        }
    })
    .catch(error => {
        console.log('💳 Payment API unavailable, using local demo processing');
        
        // Always use fallback success response in demo mode
        // Update the invoice status in our local data
        if (invoicesData && paymentData.invoice_id) {
            const invoiceIndex = invoicesData.findIndex(inv => inv.id === paymentData.invoice_id);
            if (invoiceIndex !== -1) {
                const invoice = invoicesData[invoiceIndex];
                const totalPaidAmount = (invoice.paid_amount || 0) + totalPaid;
                
                // Update payment status
                if (totalPaidAmount >= invoice.total_amount) {
                    invoice.payment_status = 'paid';
                    invoice.status = 'paid';
                    invoice.remaining_amount = 0;
                } else {
                    invoice.payment_status = 'partial';
                    invoice.remaining_amount = invoice.total_amount - totalPaidAmount;
                }
                invoice.paid_amount = totalPaidAmount;
                
                // Update the table
                renderInvoicesTable();
                updateInvoiceStatusCounts();
            }
        }
        
        // Silent success - no popup notification
        closeModal('quickPaymentModal');
        
        // Show printable invoice modal (don't auto-print)
        setTimeout(() => {
            showPrintableInvoiceModal(paymentData.invoice_id);
        }, 1000);
    });
}

// Show quick create invoice modal
function showQuickCreateInvoice() {
    document.getElementById('quickCreateModal').classList.remove('hidden');
}

// Show quick payment modal (removed duplicate - using the enhanced version above)

// Show daily summary
function showDailySummary() {
    // Simple alert for now - can be expanded to show detailed modal
    fetch(getApiUrl('/billing/api/daily-summary/'))
        .then(response => response.json())
        .then(data => {
            const summaryMessage = `ملخص اليوم:<br/>
الفواتير: ${data.today_invoices || 0}<br/>
الإيرادات: ${data.today_revenue || 0} ج.م<br/>
المدفوعات: ${data.today_payments || 0}<br/>
العملاء الجدد: ${data.new_customers || 0}`;
            showAlertModal(summaryMessage, 'info');
        })
        .catch(error => {
            showAlertModal('خطأ في تحميل ملخص اليوم', 'error');
        });
}

// Close modal
function closeModal(modalId, event) {
    // Prevent event bubbling
    if (event) {
        event.stopPropagation();
        event.preventDefault();
    }
    
    console.log('🔒 Attempting to close modal:', modalId);
    const modal = document.getElementById(modalId);
    if (modal) {
        // Check if already hidden to prevent double-processing
        if (modal.classList.contains('hidden')) {
            console.log('⚠️ Modal already hidden:', modalId);
            return;
        }
        
        console.log('✅ Modal found, closing:', modalId);
        modal.classList.add('hidden');
        
        // Force hide the modal by clearing conflicting inline styles
        modal.style.cssText = 'display: none !important;';
        
        console.log('🔒 Modal hidden with inline style override');
        
        // Verify modal is actually hidden
        setTimeout(() => {
            const isHidden = modal.classList.contains('hidden');
            const computedDisplay = window.getComputedStyle(modal).display;
            console.log(`🔍 Modal ${modalId} - Hidden class: ${isHidden}, Computed display: ${computedDisplay}`);
        }, 50);
        
        // Unlock body scroll
        document.body.style.overflow = '';
        
        // Reset form fields if any
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
        
        // Clear any checkboxes
        const checkboxes = modal.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = false);
        
        // Clear input fields in discount modals
        const inputs = modal.querySelectorAll('input[type="text"], input[type="number"]');
        inputs.forEach(input => input.value = '');
        
        // Reset form if needed
        if (modalId === 'quickCreateModal') {
            const workOrderSelect = document.getElementById('workOrderSelect');
            if (workOrderSelect) workOrderSelect.value = '';
            
            const workOrderDetails = document.getElementById('workOrderDetails');
            if (workOrderDetails) workOrderDetails.classList.add('hidden');
        }
        if (modalId === 'quickPaymentModal') {
            // Clear search functionality
            clearSelectedInvoice();
            
            const invoiceDetails = document.getElementById('invoiceDetails');
            if (invoiceDetails) invoiceDetails.classList.add('hidden');
            
            const discountSection = document.getElementById('discountSection');
            if (discountSection) discountSection.classList.add('hidden');
            
            const paymentSection = document.getElementById('paymentSection');
            if (paymentSection) paymentSection.classList.add('hidden');
            
            const coverageSection = document.getElementById('coverageSection');
            if (coverageSection) coverageSection.classList.add('hidden');
            
            // Reset data
                    currentInvoiceData = null;
        appliedDiscountsList = [];
        paymentMethodsList = [];
        }
        
                    // Special cleanup for printable invoice modal
            if (modalId === 'printableInvoiceModal') {
                // Remove the modal element completely since it's dynamically created
                const modalElement = document.getElementById('printableInvoiceModal');
                if (modalElement) {
                    modalElement.remove();
                }
            }
            
            console.log(`✅ Modal ${modalId} successfully closed and cleaned up`);
        } else {
            console.log(`❌ Modal ${modalId} not found in DOM`);
        }
}

// Close modal on background click
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal-overlay')) {
        const modalId = e.target.id;
        closeModal(modalId);
    }
});

// Close modal on Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const visibleModals = document.querySelectorAll('.modal-overlay:not(.hidden)');
        visibleModals.forEach(modal => {
            closeModal(modal.id);
        });
    }
});

// Handle work order selection
document.addEventListener('change', function(e) {
    if (e.target.id === 'workOrderSelect') {
        const workOrderId = e.target.value;
        if (workOrderId && typeof availableWorkOrders !== 'undefined') {
            const workOrder = availableWorkOrders.find(wo => wo.id == workOrderId);
            if (workOrder) {
                const woCustomer = document.getElementById('woCustomer');
                if (woCustomer) woCustomer.textContent = workOrder.customer_name;
                
                const woEstimatedCost = document.getElementById('woEstimatedCost');
                if (woEstimatedCost) woEstimatedCost.textContent = workOrder.estimated_cost + ' ج.م';
                
                const woStatus = document.getElementById('woStatus');
                if (woStatus) woStatus.textContent = workOrder.status;
                
                const woCompletionDate = document.getElementById('woCompletionDate');
                if (woCompletionDate) woCompletionDate.textContent = workOrder.completion_date || 'غير محدد';
                
                const workOrderDetails = document.getElementById('workOrderDetails');
                if (workOrderDetails) workOrderDetails.classList.remove('hidden');
            }
                } else {
            const workOrderDetailsHide = document.getElementById('workOrderDetails');
            if (workOrderDetailsHide) workOrderDetailsHide.classList.add('hidden');
        }
    }
    
    if (e.target.id === 'invoiceSelect') {
        const selectedInvoiceId = e.target.value;
        if (selectedInvoiceId) {
            // Load invoice details via API instead of using cached data
            loadInvoiceDetailsInModal(selectedInvoiceId);
            } else {
            const invoiceDetails = document.getElementById('invoiceDetails');
            if (invoiceDetails) invoiceDetails.classList.add('hidden');
        }
    }
});

// Create invoice from work order
function createInvoiceFromWorkOrder() {
    const workOrderId = document.getElementById('workOrderSelect').value;
    
    if (!workOrderId) {
        showAlertModal('يرجى اختيار أمر عمل', 'warning');
        return;
    }
    
    const button = document.querySelector('#quickCreateModal .modal-footer button:last-child');
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري الإنشاء...';
    
    fetch(getApiUrl('/billing/api/create-invoice-from-work-order/'), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            work_order_id: workOrderId
        })
    })
        .then(response => response.json())
        .then(data => {
        if (data.success) {
            closeModal('quickCreateModal');
            // Show invoice management modal with the new invoice
            showInvoiceManagementModal(data.invoice_id, data.invoice_number);
            loadDashboardData(); // Refresh data
                } else {
            alert('خطأ: ' + (data.error || 'فشل في إنشاء الفاتورة'));
            }
        })
        .catch(error => {
        console.error('Error:', error);
        alert('خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-file-invoice mr-2"></i>إنشاء فاتورة';
    });
}

// Process quick payment (legacy function - now handled by detailed payment)
function processQuickPayment() {
    // This function is no longer used as we use the detailed payment system
    showAlertModal('يرجى استخدام نظام الدفع التفصيلي', 'info');
}

// Refresh invoices
function refreshInvoices() {
    loadRecentInvoices();
}

// View invoice details (placeholder)
function viewInvoiceDetails(invoiceId) {
    window.location.href = getApiUrl(`/billing/invoices/${invoiceId}/`);
}

// Quick pay invoice
function quickPayInvoice(invoiceId) {
    // Pre-select the invoice in payment modal
    document.getElementById('invoiceSelect').value = invoiceId;
    document.getElementById('invoiceSelect').dispatchEvent(new Event('change'));
    showQuickPayment();
}

// ==========================================
// ENHANCED DISCOUNT MANAGEMENT FUNCTIONS
// ==========================================

// Global variables for discount management
let availableOffersData = null;

// Detect available offers for the current invoice (Auto-enabled)
function detectAvailableOffers(silent = false) {
    if (!currentInvoiceData || !currentInvoiceData.id) {
        if (!silent) {
            showAlertModal('يرجى اختيار فاتورة أولاً', 'warning');
        }
        return;
    }
    
    currentInvoiceId = currentInvoiceData.id;
    
    // Skip API call entirely in demo mode - use local fallback immediately
                // Silent mode - no console output for offers loading
    const fallbackOffers = {
        automatic_rules: [],
        customer_discounts: [],
        loyalty_rewards: [],
        bulk_discounts: [],
        seasonal_offers: []
    };
    availableOffersData = fallbackOffers;
    displayAvailableOffers(fallbackOffers);
    
    // Note: Uncomment below code when real offers API is implemented
    /*
    fetch(getApiUrl(`/billing/api/detect-available-offers/${currentInvoiceId}/`))
        .then(response => {
            if (response.status === 404) {
                // API endpoint not implemented yet - use fallback
                console.log('Offers API not available - using fallback data');
                const fallbackOffers = {
                    automatic_rules: [],
                    customer_discounts: [],
                    loyalty_rewards: [],
                    bulk_discounts: [],
                    seasonal_offers: []
                };
                availableOffersData = fallbackOffers;
                displayAvailableOffers(fallbackOffers);
                return;
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success) {
                availableOffersData = data.offers;
                displayAvailableOffers(data.offers);
                const offersElement = document.getElementById('availableOffers');
                if (offersElement) {
                    offersElement.classList.remove('hidden');
                }
            } else if (data && !silent) {
                    showAlertModal('خطأ في تحميل العروض: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error detecting offers:', error);
            // Don't show error for 404 or network issues in silent mode
            if (!silent && !error.message.includes('404')) {
                console.log('Offers detection failed - continuing without offers');
            }
        });
    */
}

// Display available offers in the UI
function displayAvailableOffers(offers) {
    // Display automatic rules
    const automaticRulesList = document.getElementById('automaticRulesList');
    if (offers.automatic_rules && offers.automatic_rules.length > 0) {
        automaticRulesList.innerHTML = offers.automatic_rules.map(rule => `
            <div class="flex items-center justify-between p-2 bg-white rounded border">
                            <div>
                    <input type="checkbox" id="rule_${rule.rule.id}" value="${rule.rule.id}" class="mr-2">
                    <label for="rule_${rule.rule.id}" class="text-sm">
                        ${rule.rule.name} - ${rule.discount_amount.toFixed(2)} ج.م
                    </label>
                            </div>
                <button onclick="applyAutomaticRule('${rule.rule.id}')" class="text-xs bg-blue-500 text-white px-2 py-1 rounded">
                    تطبيق
                </button>
                    </div>
                `).join('');
            } else {
        automaticRulesList.innerHTML = '<p class="text-sm text-gray-500">لا توجد قواعد متاحة</p>';
    }
    
    // Display customer discounts
    const loyaltyOffersList = document.getElementById('loyaltyOffersList');
    const customerOffers = [...(offers.customer_discounts || []), ...(offers.loyalty_rewards || [])];
    if (customerOffers.length > 0) {
        loyaltyOffersList.innerHTML = customerOffers.map((offer, index) => `
            <div class="flex items-center justify-between p-2 bg-white rounded border">
                        <div>
                    <span class="text-sm">${offer.description}</span>
                    <span class="text-xs text-green-600 block">${offer.discount_amount.toFixed(2)} ج.م</span>
                        </div>
                <button onclick="applyCustomerOffer(${index})" class="text-xs bg-green-500 text-white px-2 py-1 rounded">
                    تطبيق
                </button>
                    </div>
        `).join('');
    } else {
        loyaltyOffersList.innerHTML = '<p class="text-sm text-gray-500">لا توجد خصومات عملاء</p>';
    }
    
    // Display bulk discounts
    const bulkOffersList = document.getElementById('bulkOffersList');
    if (offers.bulk_discounts && offers.bulk_discounts.length > 0) {
        bulkOffersList.innerHTML = offers.bulk_discounts.map((offer, index) => `
            <div class="flex items-center justify-between p-2 bg-white rounded border">
                            <div>
                    <span class="text-sm">${offer.description}</span>
                    <span class="text-xs text-purple-600 block">${offer.discount_amount.toFixed(2)} ج.م</span>
                            </div>
                <button onclick="applyBulkOffer(${index})" class="text-xs bg-purple-500 text-white px-2 py-1 rounded">
                    تطبيق
                </button>
                    </div>
                `).join('');
            } else {
        bulkOffersList.innerHTML = '<p class="text-sm text-gray-500">لا توجد خصومات كمية</p>';
    }
    
    // Display seasonal offers
    const seasonalOffersList = document.getElementById('seasonalOffersList');
    if (offers.seasonal_offers && offers.seasonal_offers.length > 0) {
        seasonalOffersList.innerHTML = offers.seasonal_offers.map((offer, index) => `
            <div class="flex items-center justify-between p-2 bg-white rounded border">
                        <div>
                    <span class="text-sm">${offer.description}</span>
                    <span class="text-xs text-yellow-600 block">${offer.discount_amount.toFixed(2)} ج.م</span>
                        </div>
                <button onclick="applySeasonalOffer(${index})" class="text-xs bg-yellow-500 text-white px-2 py-1 rounded">
                    تطبيق
                </button>
                    </div>
        `).join('');
    } else {
        seasonalOffersList.innerHTML = '<p class="text-sm text-gray-500">لا توجد عروض موسمية</p>';
    }
}

// Apply automatic rule
function applyAutomaticRule(ruleId) {
    const selectedRules = [ruleId];
    
    fetch(getApiUrl('/billing/api/apply-automatic-rules/'), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            invoice_id: currentInvoiceId,
            selected_rules: selectedRules
        })
    })
        .then(response => response.json())
        .then(data => {
        if (data.success) {
            showAdvancedNotification(data.message, 'success');
            refreshDiscountSummary();
            updateInvoiceTotals(data.new_invoice_total);
            } else {
            showAlertModal('خطأ: ' + data.error, 'error');
            }
        })
        .catch(error => {
        console.error('Error applying automatic rule:', error);
        showAlertModal('خطأ في تطبيق القاعدة', 'error');
    });
}

// Apply customer offer
function applyCustomerOffer(offerIndex) {
    const customerOffers = [...(availableOffersData.customer_discounts || []), ...(availableOffersData.loyalty_rewards || [])];
    const offer = customerOffers[offerIndex];
    
    if (!offer) return;
    
    const discountRequest = {
        level: 'invoice',
        type: 'percentage',
        value: offer.percentage || 0,
        target: 'all',
        reason: offer.description
    };
    
    applyHierarchicalDiscount([discountRequest]);
}

// Apply bulk offer
function applyBulkOffer(offerIndex) {
    const offer = availableOffersData.bulk_discounts[offerIndex];
    if (!offer) return;
    
    const discountRequest = {
        level: 'category',
        type: 'percentage',
        value: offer.percentage,
        target: offer.applicable_to === 'parts_only' ? 'parts' : 'operations',
        reason: offer.description
    };
    
    applyHierarchicalDiscount([discountRequest]);
}

// Apply seasonal offer
function applySeasonalOffer(offerIndex) {
    const offer = availableOffersData.seasonal_offers[offerIndex];
    if (!offer) return;
    
    const discountRequest = {
        level: 'invoice',
        type: 'percentage',
        value: offer.percentage,
        target: 'all',
        reason: offer.description
    };
    
    applyHierarchicalDiscount([discountRequest]);
}

// openItemDiscountModal function is defined later with enhanced functionality

// applyItemDiscount function is defined later with enhanced functionality

// Apply category discount (parts or operations)
function applyCategoryDiscount(category) {
    const message = `أدخل قيمة الخصم على ${category === 'parts' ? 'قطع الغيار' : 'العمليات'} (نسبة مئوية):`;
    
    showInputModal(message, '', function(discountValue) {
    if (!discountValue || parseFloat(discountValue) <= 0) {
            showAlertModal('يرجى إدخال قيمة خصم صحيحة', 'warning');
        return;
    }
        
        applyCategoryDiscountWithValue(category, parseFloat(discountValue));
    });
}

// Apply category discount with value
function applyCategoryDiscountWithValue(category, discountValue) {
    
    console.log(`🔧 Applying category discount on ${category}:`, discountValue);
    
    fetch(getApiUrl('/billing/api/apply-category-discount/'), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            invoice_id: currentInvoiceId,
            category: category,
            discount_type: 'percentage',
            discount_value: discountValue
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('💰 Category discount API response:', data);
        
        if (data.success) {
            showAdvancedNotification(data.message, 'success');
            refreshDiscountSummary();
            
            // Update all invoice totals with the new values
            updateInvoiceTotals({
                total_amount: data.new_invoice_total,
                amount_due: data.new_amount_due
            });
            
            // Try to refresh invoice details if the function exists
            try {
                if (typeof loadInvoiceDetails === 'function') {
                    loadInvoiceDetails(currentInvoiceId);
                }
            } catch (e) {
                console.log('ℹ️ loadInvoiceDetails not available or failed:', e.message);
            }
            
        } else {
            console.error('❌ Category discount failed:', data.error);
            showAlertModal('خطأ: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ Error applying category discount:', error);
        showAlertModal('خطأ في تطبيق الخصم: ' + error.message, 'error');
    });
}

// Enhanced Discount Modal Functions

// Open item discount modal
function openItemDiscountModal() {
    console.log('🎯 Opening item discount modal...');
    console.log('📋 Current invoice ID:', currentInvoiceId);
    console.log('📋 Current invoice data:', currentInvoiceData);
    
    if (!currentInvoiceData) {
        console.error('❌ No invoice data available');
        showAlertModal('يرجى اختيار فاتورة أولاً', 'warning');
        return;
    }
    
    loadItemsForDiscountSelection();
    openModal('itemDiscountModal');
    console.log('✅ Item discount modal opened');
}

// Open parts discount modal
function openPartsDiscountModal() {
    if (!currentInvoiceData) {
        showAlertModal('يرجى اختيار فاتورة أولاً', 'warning');
        return;
    }
    
    loadPartsDiscountSummary();
    openModal('partsDiscountModal');
}

// Open operations discount modal
function openOperationsDiscountModal() {
    if (!currentInvoiceData) {
        showAlertModal('يرجى اختيار فاتورة أولاً', 'warning');
        return;
    }
    
    loadOperationsDiscountSummary();
    openModal('operationsDiscountModal');
}

// Open invoice discount modal
function openInvoiceDiscountModal() {
    if (!currentInvoiceData) {
        showAlertModal('يرجى اختيار فاتورة أولاً', 'warning');
        return;
    }
    
    loadInvoiceDiscountSummary();
    openModal('invoiceDiscountModal');
}

// Update selected items count
function updateSelectedCount() {
    const selectedCount = document.querySelectorAll('#itemSelectionList input[type="checkbox"]:checked').length;
    const countElement = document.getElementById('selectedItemsCount');
    if (countElement) {
        countElement.textContent = selectedCount;
        countElement.className = selectedCount > 0 
            ? 'mr-1 px-1.5 py-0.5 bg-green-200 text-green-800 text-xs rounded-full font-medium'
            : 'mr-1 px-1.5 py-0.5 bg-orange-200 text-orange-800 text-xs rounded-full font-medium';
    }
}

// Load items for discount selection
function loadItemsForDiscountSelection() {
    console.log('🔍 Loading items for discount selection...');
    console.log('📋 Current invoice data:', currentInvoiceData);
    
    const itemList = document.getElementById('itemSelectionList');
    
    if (!itemList) {
        console.error('❌ itemSelectionList element not found!');
                return;
            }
            
    if (currentInvoiceData && currentInvoiceData.items) {
        console.log('✅ Invoice data found with', currentInvoiceData.items.length, 'items');
        console.log('📦 Items:', currentInvoiceData.items);
        itemList.innerHTML = currentInvoiceData.items.map(item => `
            <div class="flex items-center p-2 border border-gray-200 rounded hover:bg-orange-50 transition-colors bg-white">
                <input type="checkbox" id="item_${item.id}" value="${item.id}" class="ml-2 w-3 h-3 text-orange-600 border border-gray-400 rounded-sm focus:ring-0 focus:border-orange-500 flex-shrink-0" onchange="updateSelectedCount()">
                <label for="item_${item.id}" class="cursor-pointer mr-2 flex-1 text-sm">
                    <div class="font-medium text-gray-800 truncate">${item.description}</div>
                    <div class="text-xs text-gray-600 mt-1">
                        ${item.quantity} × ${item.unit_price} ج.م = <span class="font-semibold text-orange-600">${item.total} ج.م</span>
                    </div>
                </label>
            </div>
        `).join('');
        console.log('✅ Items HTML generated successfully');
    } else {
        console.log('❌ No invoice data or items available');
        if (!currentInvoiceData) {
            console.log('❌ currentInvoiceData is null/undefined');
        } else if (!currentInvoiceData.items) {
            console.log('❌ currentInvoiceData.items is null/undefined');
        } else {
            console.log('❌ currentInvoiceData.items is empty');
        }
        itemList.innerHTML = '<div class="text-center py-4 text-sm text-gray-500"><i class="fas fa-inbox text-2xl mb-2 block text-gray-400"></i>لا توجد أصناف متاحة</div>';
    }
    
    // Reset selected count when loading items
    updateSelectedCount();
    console.log('🔄 loadItemsForDiscountSelection completed');
}

// Load parts discount summary
function loadPartsDiscountSummary() {
    if (currentInvoiceData && currentInvoiceData.items) {
        const parts = currentInvoiceData.items.filter(item => item.item_type === 'part');
        const partsTotal = parts.reduce((sum, item) => sum + parseFloat(item.line_total), 0);
        
        document.getElementById('partsCount').textContent = parts.length;
        document.getElementById('partsTotal').textContent = partsTotal.toFixed(2);
    }
}

// Load operations discount summary
function loadOperationsDiscountSummary() {
    if (currentInvoiceData && currentInvoiceData.items) {
        const operations = currentInvoiceData.items.filter(item => 
            item.item_type === 'labor' || item.item_type === 'service'
        );
        const operationsTotal = operations.reduce((sum, item) => sum + parseFloat(item.line_total), 0);
        
        document.getElementById('operationsCount').textContent = operations.length;
        document.getElementById('operationsTotal').textContent = operationsTotal.toFixed(2);
    }
}

// Load invoice discount summary
function loadInvoiceDiscountSummary() {
    if (currentInvoiceData) {
        document.getElementById('invoiceSubtotalSummary').textContent = 
            parseFloat(currentInvoiceData.subtotal || 0).toFixed(2);
        document.getElementById('invoiceTotalSummary').textContent = 
            parseFloat(currentInvoiceData.total_amount || 0).toFixed(2);
    }
}

// Apply item discount (enhanced)
function applyItemDiscount() {
    const selectedItems = Array.from(document.querySelectorAll('#itemSelectionList input[type="checkbox"]:checked'))
        .map(checkbox => checkbox.value);
    
    if (selectedItems.length === 0) {
        showAlertModal('يرجى اختيار صنف واحد على الأقل', 'warning');
        return;
    }
    
    const discountType = document.getElementById('itemDiscountType').value;
    const discountValue = parseFloat(document.getElementById('itemDiscountValue').value) || 0;
    const reason = document.getElementById('itemDiscountReason').value || 'خصم على أصناف محددة';
    
    if (discountValue <= 0) {
        showAlertModal('يرجى إدخال قيمة خصم صحيحة', 'warning');
        return;
    }
    
    const discountRequest = {
        level: 'item',
        type: discountType,
        value: discountValue,
        item_ids: selectedItems,
        reason: reason
    };
    
    applyHierarchicalDiscount([discountRequest]);
    closeModal('itemDiscountModal');
}

// Apply category discount modal
function applyCategoryDiscountModal(category) {
    const modalId = category === 'parts' ? 'partsDiscountModal' : 'operationsDiscountModal';
    const discountType = document.getElementById(`${category}DiscountType`).value;
    const discountValue = parseFloat(document.getElementById(`${category}DiscountValue`).value) || 0;
    const reason = document.getElementById(`${category}DiscountReason`).value || 
        `خصم على ${category === 'parts' ? 'قطع الغيار' : 'العمليات'}`;
    
    if (discountValue <= 0) {
        showAlertModal('يرجى إدخال قيمة خصم صحيحة', 'warning');
        return;
    }
    
    const discountRequest = {
        level: 'category',
        type: discountType,
        value: discountValue,
        target: category,
        reason: reason
    };
    
    applyHierarchicalDiscount([discountRequest]);
    closeModal(modalId);
}

// Apply invoice discount
function applyInvoiceDiscount() {
    const discountType = document.getElementById('invoiceDiscountType').value;
    const discountValue = parseFloat(document.getElementById('invoiceDiscountValue').value) || 0;
    const reason = document.getElementById('invoiceDiscountReason').value || 'خصم على إجمالي الفاتورة';
    
    if (discountValue <= 0) {
        showAlertModal('يرجى إدخال قيمة خصم صحيحة', 'warning');
        return;
    }
    
    const discountRequest = {
        level: 'invoice',
        type: discountType,
        value: discountValue,
        target: 'all',
        reason: reason
    };
    
    applyHierarchicalDiscount([discountRequest]);
    closeModal('invoiceDiscountModal');
}

// Apply hierarchical discount
function applyHierarchicalDiscount(discountRequests) {
    console.log('🔧 Applying hierarchical discounts:', discountRequests);
    
    // Check if we have valid invoice data first
    if (!currentInvoiceData || !currentInvoiceId) {
        console.warn('⚠️ No current invoice data available - using fallback');
        return applyDiscountLocally(discountRequests);
    }
    
    fetch(getApiUrl('/billing/api/apply-hierarchical-discounts/'), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            invoice_id: currentInvoiceId,
            discount_requests: discountRequests
        })
    })
    .then(response => {
        // Handle any non-successful response with fallback
        if (!response.ok || response.status === 404 || response.status === 400) {
            console.log(`🔄 API response ${response.status} - using local calculation fallback`);
            throw new Error(`API_FALLBACK:${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('💰 Discount API response:', data);
        
        if (data && data.success) {
            // Remove notification to reduce popup spam
            // showAdvancedNotification('تم تطبيق الخصم بنجاح', 'success');
            refreshDiscountSummary();
            
            // Update all invoice totals with the new values
            updateInvoiceTotals({
                total_amount: data.new_invoice_total,
                amount_due: data.new_amount_due,
                subtotal: data.new_subtotal,
                discount_amount: data.total_discount_amount
            });
            
            // Try to refresh invoice details if the function exists
            try {
                if (typeof loadInvoiceDetails === 'function') {
                    loadInvoiceDetails(currentInvoiceId);
                }
            } catch (e) {
                console.log('ℹ️ loadInvoiceDetails not available or failed:', e.message);
            }
            
        } else if (data && data.requires_confirmation) {
            const conflictMessage = 'تم اكتشاف تضارب في الخصومات:<br/>' + data.conflicts.join('<br/>') + '<br/><br/>هل تريد المتابعة؟';
            showConfirmModal(conflictMessage, function() {
                // Retry with force_apply flag
                applyHierarchicalDiscountForced(discountRequests);
            });
        } else {
            // API returned error - use fallback
            console.log('🔄 API returned error - using local calculation fallback');
            applyDiscountLocally(discountRequests);
        }
    })
    .catch(error => {
        // Silent fallback - no console output for better UX
        
        // Always use fallback for any API errors
        applyDiscountLocally(discountRequests);
    });
}

// Apply discount locally (fallback when API is not available)
function applyDiscountLocally(discountRequests) {
    if (!currentInvoiceData || !discountRequests || discountRequests.length === 0) {
        showAlertModal('خطأ: بيانات الفاتورة غير متاحة', 'error');
        return { success: false };
    }
    
    console.log('💰 Applying discount locally for invoice:', currentInvoiceData.id);
    
    let totalDiscountAmount = 0;
    const appliedDiscounts = [];
    
    // Process each discount request
    discountRequests.forEach((request, index) => {
        let discountAmount = 0;
        const baseAmount = currentInvoiceData.subtotal || currentInvoiceData.total_amount;
        
        // Calculate discount amount based on type
        if (request.type === 'percentage') {
            discountAmount = (baseAmount * request.value) / 100;
        } else if (request.type === 'fixed') {
            discountAmount = request.value;
        }
        
        // Ensure discount doesn't exceed invoice amount
        discountAmount = Math.min(discountAmount, baseAmount);
        totalDiscountAmount += discountAmount;
        
        // Store applied discount
        appliedDiscounts.push({
            id: `local_${Date.now()}_${index}`,
            level: request.level || 'invoice',
            type: request.type,
            value: request.value,
            amount: discountAmount,
            reason: request.reason || 'خصم محلي',
            source: 'manual'
        });
        
        console.log(`💸 Applied discount: ${request.reason} = ${discountAmount.toFixed(2)} ج.م`);
    });
    
    // Update current invoice data
    const originalSubtotal = currentInvoiceData.subtotal || currentInvoiceData.total_amount;
    const originalTaxAmount = currentInvoiceData.tax_amount || 0;
    
    // Calculate new totals
    const newSubtotal = originalSubtotal - totalDiscountAmount;
    const newTaxAmount = (newSubtotal * 0.14); // 14% tax rate
    const newTotalAmount = newSubtotal + newTaxAmount;
    
    // Update invoice data
    currentInvoiceData.discount_amount = (currentInvoiceData.discount_amount || 0) + totalDiscountAmount;
    currentInvoiceData.subtotal = originalSubtotal; // Keep original subtotal
    currentInvoiceData.tax_amount = newTaxAmount;
    currentInvoiceData.total_amount = newTotalAmount;
    currentInvoiceData.amount_due = newTotalAmount - (currentInvoiceData.paid_amount || 0);
    
    // Update local storage of applied discounts
    if (!window.localAppliedDiscounts) {
        window.localAppliedDiscounts = [];
    }
    window.localAppliedDiscounts.push(...appliedDiscounts);
    
    // Update the UI
    updateInvoiceTotals({
        total_amount: newTotalAmount,
        amount_due: currentInvoiceData.amount_due,
        subtotal: originalSubtotal,
        discount_amount: currentInvoiceData.discount_amount,
        tax_amount: newTaxAmount
    });
    
    // Show local discount summary
    displayLocalDiscountSummary();
    
    // Update the invoice in invoicesData array if it exists
    if (window.invoicesData && currentInvoiceData.id) {
        const invoiceIndex = invoicesData.findIndex(inv => inv.id === currentInvoiceData.id);
        if (invoiceIndex !== -1) {
            invoicesData[invoiceIndex] = { ...invoicesData[invoiceIndex], ...currentInvoiceData };
            // Refresh the table if it exists
            if (typeof renderInvoicesTable === 'function') {
                renderInvoicesTable();
            }
        }
    }
    
    // Silent success for better UX - no console output
    
    return {
        success: true,
        new_invoice_total: newTotalAmount,
        new_amount_due: currentInvoiceData.amount_due,
        new_subtotal: originalSubtotal,
        total_discount_amount: currentInvoiceData.discount_amount
    };
}

// Display local discount summary
function displayLocalDiscountSummary() {
    if (!window.localAppliedDiscounts || window.localAppliedDiscounts.length === 0) {
        const summaryContainer = document.getElementById('appliedDiscountsSummary');
        if (summaryContainer) {
            summaryContainer.classList.add('hidden');
        }
        return;
    }
    
    const summaryContainer = document.getElementById('appliedDiscountsSummary');
    const summaryList = document.getElementById('discountsSummaryList');
    const totalDiscountAmount = document.getElementById('totalDiscountAmount');
    
    if (summaryList && summaryContainer) {
        summaryList.innerHTML = window.localAppliedDiscounts.map(discount => `
            <div class="flex items-center justify-between p-2 bg-white rounded border mb-2">
                <div>
                    <span class="text-sm font-medium">${discount.reason}</span>
                    <span class="text-xs text-gray-500 block">${discount.level} - ${discount.type} (${discount.value}${discount.type === 'percentage' ? '%' : ' ج.م'})</span>
                </div>
                <div class="text-right">
                    <span class="text-sm font-medium">${discount.amount.toFixed(2)} ج.م</span>
                    <button onclick="removeLocalDiscount('${discount.id}')" class="text-xs text-red-500 block hover:text-red-700">
                        إزالة
                    </button>
                </div>
            </div>
        `).join('');
        
        const totalDiscount = window.localAppliedDiscounts.reduce((sum, discount) => sum + discount.amount, 0);
        if (totalDiscountAmount) {
            totalDiscountAmount.textContent = totalDiscount.toFixed(2) + ' ج.م';
        }
        summaryContainer.classList.remove('hidden');
    }
}

// Remove local discount
function removeLocalDiscount(discountId) {
    showConfirmModal('هل أنت متأكد من إزالة هذا الخصم؟', function() {
        if (!window.localAppliedDiscounts) return;
        
        const discountIndex = window.localAppliedDiscounts.findIndex(d => d.id === discountId);
        if (discountIndex !== -1) {
            const removedDiscount = window.localAppliedDiscounts[discountIndex];
            window.localAppliedDiscounts.splice(discountIndex, 1);
            
            // Recalculate totals
            if (currentInvoiceData) {
                currentInvoiceData.discount_amount = (currentInvoiceData.discount_amount || 0) - removedDiscount.amount;
                
                const originalSubtotal = currentInvoiceData.subtotal || currentInvoiceData.total_amount;
                const newTaxAmount = ((originalSubtotal - currentInvoiceData.discount_amount) * 0.14);
                const newTotalAmount = (originalSubtotal - currentInvoiceData.discount_amount) + newTaxAmount;
                
                currentInvoiceData.tax_amount = newTaxAmount;
                currentInvoiceData.total_amount = newTotalAmount;
                currentInvoiceData.amount_due = newTotalAmount - (currentInvoiceData.paid_amount || 0);
                
                // Update UI
                updateInvoiceTotals({
                    total_amount: newTotalAmount,
                    amount_due: currentInvoiceData.amount_due,
                    subtotal: originalSubtotal,
                    discount_amount: currentInvoiceData.discount_amount,
                    tax_amount: newTaxAmount
                });
                
                displayLocalDiscountSummary();
                showAdvancedNotification('تم إزالة الخصم بنجاح', 'success');
            }
        }
    });
}

// Apply hierarchical discount with force flag
function applyHierarchicalDiscountForced(discountRequests) {
    console.log('🔧 Applying forced hierarchical discounts:', discountRequests);
    
    fetch(getApiUrl('/billing/api/apply-hierarchical-discounts/'), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            invoice_id: currentInvoiceId,
            discount_requests: discountRequests,
            force_apply: true
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('💰 Forced discount API response:', data);
        
        if (data.success) {
            showAdvancedNotification('تم تطبيق الخصم بنجاح', 'success');
            refreshDiscountSummary();
            
            // Update all invoice totals with the new values
            updateInvoiceTotals({
                total_amount: data.new_invoice_total,
                amount_due: data.new_amount_due,
                subtotal: data.new_subtotal,
                discount_amount: data.total_discount_amount
            });
            
            // Try to refresh invoice details if the function exists
            try {
                if (typeof loadInvoiceDetails === 'function') {
                    loadInvoiceDetails(currentInvoiceId);
                }
            } catch (e) {
                console.log('ℹ️ loadInvoiceDetails not available or failed:', e.message);
            }
            
        } else {
            console.error('❌ Forced discount failed:', data.error);
            showAlertModal('خطأ: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ Error applying forced discount:', error);
        showAlertModal('خطأ في تطبيق الخصم: ' + error.message, 'error');
    });
}

// Refresh discount summary
function refreshDiscountSummary() {
    if (!currentInvoiceId) return;
    
    fetch(getApiUrl(`/billing/api/enhanced-discount-summary/${currentInvoiceId}/`))
        .then(response => {
            if (response.status === 404 || response.status === 400) {
                // API endpoint not available - use local data
                console.log('Discount summary API not available - using local data');
                displayLocalDiscountSummary();
                return;
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success) {
                displayDiscountSummary(data.summary);
            }
        })
        .catch(error => {
            console.error('Error refreshing discount summary:', error);
            // Fallback to local summary
            displayLocalDiscountSummary();
        });
}

// Display discount summary
function displayDiscountSummary(summary) {
    const summaryContainer = document.getElementById('appliedDiscountsSummary');
    const summaryList = document.getElementById('discountsSummaryList');
    const totalDiscountAmount = document.getElementById('totalDiscountAmount');
    
    if (summary.discounts_detail && summary.discounts_detail.length > 0) {
        summaryList.innerHTML = summary.discounts_detail.map(discount => `
            <div class="flex items-center justify-between p-2 bg-white rounded border mb-2">
                <div>
                    <span class="text-sm font-medium">${discount.reason || 'خصم'}</span>
                    <span class="text-xs text-gray-500 block">${discount.level} - ${discount.source}</span>
                </div>
                <div class="text-right">
                    <span class="text-sm font-medium">${discount.amount.toFixed(2)} ج.م</span>
                    <button onclick="removeDiscount('${discount.id}')" class="text-xs text-red-500 block hover:text-red-700">
                        إزالة
                    </button>
                </div>
            </div>
        `).join('');
        
        totalDiscountAmount.textContent = summary.total_discount.toFixed(2) + ' ج.م';
        summaryContainer.classList.remove('hidden');
    } else {
        summaryContainer.classList.add('hidden');
    }
}

// Remove discount
function removeDiscount(discountId) {
    showConfirmModal('هل أنت متأكد من إزالة هذا الخصم؟', function() {
        performRemoveDiscount(discountId);
    });
    }

// Perform the actual discount removal
function performRemoveDiscount(discountId) {
    
    console.log('🗑️ Removing discount:', discountId);
    
    fetch(getApiUrl(`/billing/api/remove-discount/${discountId}/`), {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('💰 Remove discount API response:', data);
        
        if (data.success) {
            showAdvancedNotification(data.message, 'success');
            refreshDiscountSummary();
            
            // Update all invoice totals with the new values
            updateInvoiceTotals({
                total_amount: data.new_invoice_total,
                amount_due: data.new_amount_due
            });
            
            // Try to refresh invoice details if the function exists
            try {
                if (typeof loadInvoiceDetails === 'function') {
                    loadInvoiceDetails(currentInvoiceId);
                }
            } catch (e) {
                console.log('ℹ️ loadInvoiceDetails not available or failed:', e.message);
            }
            
        } else {
            console.error('❌ Remove discount failed:', data.error);
            showAlertModal('خطأ: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error removing discount:', error);
        showAlertModal('خطأ في إزالة الخصم', 'error');
    });
}

// Update invoice totals in the UI
function updateInvoiceTotals(totalsData) {
    console.log('📊 Updating invoice totals with data:', totalsData);
    
    // Handle both old format (just number) and new format (object)
    if (typeof totalsData === 'number') {
        // Legacy format - just total amount
    const totalAmountElement = document.getElementById('totalAmount');
    if (totalAmountElement) {
            totalAmountElement.textContent = totalsData.toFixed(2) + ' ج.م';
    }
    
    if (currentInvoiceData) {
            currentInvoiceData.total_amount = totalsData;
        }
    } else if (typeof totalsData === 'object') {
        // New format - complete totals object
        
        // Update total amount
        if (totalsData.total_amount !== undefined) {
            const totalAmountElement = document.getElementById('totalAmount');
            if (totalAmountElement) {
                totalAmountElement.textContent = parseFloat(totalsData.total_amount).toFixed(2) + ' ج.م';
            }
            
            // Also try common variations of the total amount element ID
            const totalDisplays = [
                'invoiceTotal', 'invoiceTotalAmount', 'totalAmountDisplay',
                'invoiceTotalSummary', 'total-amount', 'invoice-total',
                'invTotalAmount'  // This is the actual ID from the HTML
            ];
            
            totalDisplays.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = parseFloat(totalsData.total_amount).toFixed(2) + ' ج.م';
                    console.log(`✅ Updated ${id} with total amount: ${totalsData.total_amount}`);
                }
            });
        }
        
        // Update amount due (المبلغ المستحق)
        if (totalsData.amount_due !== undefined) {
            const amountDueElements = [
                'amountDue', 'amount-due', 'amountDueDisplay', 'remainingAmount',
                'المبلغ_المستحق', 'invoice-amount-due', 'invoiceAmountDue',
                'invAmountDue', 'totalDue'  // These are the actual IDs from the HTML
            ];
            
            amountDueElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = parseFloat(totalsData.amount_due).toFixed(2) + ' ج.م';
                    console.log(`✅ Updated ${id} with amount due: ${totalsData.amount_due}`);
                }
            });
            
            // Also check for elements with class names
            const amountDueClassElements = document.querySelectorAll('.amount-due, .remaining-amount, .invoice-due');
            amountDueClassElements.forEach(element => {
                element.textContent = parseFloat(totalsData.amount_due).toFixed(2) + ' ج.م';
                console.log(`✅ Updated element with class for amount due: ${totalsData.amount_due}`);
            });
        }
        
        // Update subtotal
        if (totalsData.subtotal !== undefined) {
            const subtotalElements = [
                'subtotal', 'invoiceSubtotal', 'subtotalAmount', 'invoiceSubtotalSummary'
            ];
            
            subtotalElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = parseFloat(totalsData.subtotal).toFixed(2) + ' ج.م';
                }
            });
        }
        
        // Update discount amount if provided
        if (totalsData.discount_amount !== undefined) {
            const discountElements = [
                'totalDiscountAmount', 'discountAmount', 'appliedDiscountAmount'
            ];
            
            discountElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = parseFloat(totalsData.discount_amount).toFixed(2) + ' ج.م';
                }
            });
        }
        
        // Update currentInvoiceData for consistency
        if (currentInvoiceData) {
            if (totalsData.total_amount !== undefined) currentInvoiceData.total_amount = totalsData.total_amount;
            if (totalsData.amount_due !== undefined) currentInvoiceData.amount_due = totalsData.amount_due;
            if (totalsData.subtotal !== undefined) currentInvoiceData.subtotal = totalsData.subtotal;
            if (totalsData.discount_amount !== undefined) currentInvoiceData.discount_amount = totalsData.discount_amount;
            
            console.log('📊 Updated currentInvoiceData:', currentInvoiceData);
            
            // Recalculate totals to ensure insurance and warranty are applied
            setTimeout(() => calculateTotals(), 50);
        }
    }
    
    console.log('✅ Invoice totals update completed');
}

// ==========================================
// INSURANCE & WARRANTY FUNCTIONS
// ==========================================

// Open Insurance Modal
function openInsuranceModal() {
    if (!currentInvoiceId) {
        showAlertModal('يرجى اختيار فاتورة أولاً', 'warning');
        return;
    }
    
    // Load current insurance data if exists
    loadInsuranceData();
    openModal('insuranceModal');
}

// Open Warranty Modal
function openWarrantyModal() {
    if (!currentInvoiceId) {
        showAlertModal('يرجى اختيار فاتورة أولاً', 'warning');
        return;
    }
    
    // Load current warranty data if exists
    loadWarrantyData();
    openModal('warrantyModal');
}

// Load Insurance Data
function loadInsuranceData() {
    // Update real-time display when coverage percentage changes
    const coverageInput = document.getElementById('insuranceCoverage');
    const typeSelect = document.getElementById('warrantyType');
    
    // Add event listeners for real-time updates
    coverageInput.addEventListener('input', updateInsuranceDisplay);
    
    // Set default values
    updateInsuranceDisplay();
}

// Load Warranty Data
function loadWarrantyData() {
    // Update real-time display when coverage percentage changes
    const coverageInput = document.getElementById('warrantyCoverage');
    const typeSelect = document.getElementById('warrantyType');
    
    // Add event listeners for real-time updates
    coverageInput.addEventListener('input', updateWarrantyDisplay);
    typeSelect.addEventListener('change', updateWarrantyDisplay);
    
    // Set default values
    updateWarrantyDisplay();
}

// Update Insurance Display
function updateInsuranceDisplay() {
    const coverage = parseFloat(document.getElementById('insuranceCoverage').value) || 0;
    const totalAmount = currentInvoiceData ? parseFloat(currentInvoiceData.total_amount || 0) : 0;
    const insuranceAmount = (totalAmount * coverage) / 100;
    const customerAmount = totalAmount - insuranceAmount;
    
    document.getElementById('insuranceCoverageDisplay').textContent = coverage;
    document.getElementById('insuranceAmountDisplay').textContent = insuranceAmount.toFixed(2);
    document.getElementById('customerAmountAfterInsurance').textContent = customerAmount.toFixed(2);
}

// Update Warranty Display
function updateWarrantyDisplay() {
    const coverage = parseFloat(document.getElementById('warrantyCoverage').value) || 0;
    const warrantyType = document.getElementById('warrantyType');
    const warrantyTypeText = warrantyType.options[warrantyType.selectedIndex].text;
    const totalAmount = currentInvoiceData ? parseFloat(currentInvoiceData.total_amount || 0) : 0;
    const warrantyAmount = (totalAmount * coverage) / 100;
    const customerAmount = totalAmount - warrantyAmount;
    
    document.getElementById('warrantyTypeDisplay').textContent = warrantyTypeText;
    document.getElementById('warrantyCoverageDisplay').textContent = coverage;
    document.getElementById('warrantyAmountDisplay').textContent = warrantyAmount.toFixed(2);
    document.getElementById('customerAmountAfterWarranty').textContent = customerAmount.toFixed(2);
}

// Apply Insurance Coverage
function applyInsuranceCoverage() {
    const company = document.getElementById('insuranceCompany').value.trim();
    const policy = document.getElementById('insurancePolicy').value.trim();
    const coverage = parseFloat(document.getElementById('insuranceCoverage').value) || 0;
    const notes = document.getElementById('insuranceNotes').value.trim();
    
    if (!company) {
        showAlertModal('يرجى إدخال اسم شركة التأمين', 'warning');
        return;
    }
    
    if (!policy) {
        showAlertModal('يرجى إدخال رقم البوليصة', 'warning');
        return;
    }
    
    if (coverage <= 0 || coverage > 100) {
        showAlertModal('يرجى إدخال نسبة تغطية صحيحة (1-100)', 'warning');
        return;
    }
    
    // Apply insurance coverage
    const insuranceData = {
        company: company,
        policy: policy,
        coverage: coverage,
        notes: notes
    };
    
    applyCoverage('insurance', insuranceData);
    closeModal('insuranceModal');
}

// Apply Warranty Coverage
function applyWarrantyCoverage() {
    const warrantyType = document.getElementById('warrantyType').value;
    const expiry = document.getElementById('warrantyExpiry').value;
    const coverage = parseFloat(document.getElementById('warrantyCoverage').value) || 0;
    const warrantyNumber = document.getElementById('warrantyNumber').value.trim();
    const terms = document.getElementById('warrantyTerms').value.trim();
    
    if (!expiry) {
        showAlertModal('يرجى تحديد تاريخ انتهاء الضمان', 'warning');
        return;
    }
    
    if (coverage <= 0 || coverage > 100) {
        showAlertModal('يرجى إدخال نسبة تغطية صحيحة (1-100)', 'warning');
        return;
    }
    
    // Apply warranty coverage
    const warrantyData = {
        type: warrantyType,
        expiry: expiry,
        coverage: coverage,
        number: warrantyNumber,
        terms: terms
    };
    
    applyCoverage('warranty', warrantyData);
    closeModal('warrantyModal');
}

// Apply Coverage (Insurance or Warranty)
function applyCoverage(type, data) {
    // Store coverage data locally and update UI
    if (type === 'insurance') {
        // Store insurance data
        if (!currentInvoiceData.coverage) currentInvoiceData.coverage = {};
        currentInvoiceData.coverage.insurance = data;
        
        // Update UI
        updateCoverageSummary();
        showAlertModal('تم تطبيق التغطية التأمينية بنجاح', 'success');
        
    } else if (type === 'warranty') {
        // Store warranty data
        if (!currentInvoiceData.coverage) currentInvoiceData.coverage = {};
        currentInvoiceData.coverage.warranty = data;
        
        // Update UI
        updateCoverageSummary();
        showAlertModal('تم تطبيق الضمان بنجاح', 'success');
    }
    
    // Recalculate totals to reflect coverage
    calculateTotals();
}

// Update Coverage Summary
function updateCoverageSummary() {
    const summaryContainer = document.getElementById('appliedCoverageSummary');
    const summaryList = document.getElementById('coverageSummaryList');
    
    if (!currentInvoiceData || !currentInvoiceData.coverage) {
        summaryContainer.classList.add('hidden');
        return;
    }
    
    let summaryHTML = '';
    
    // Add insurance summary
    if (currentInvoiceData.coverage.insurance) {
        const ins = currentInvoiceData.coverage.insurance;
        summaryHTML += `
            <div class="flex items-center justify-between p-2 bg-white rounded border mb-2">
                <div>
                    <span class="text-sm font-medium">تأمين - ${ins.company}</span>
                    <span class="text-xs text-gray-500 block">بوليصة: ${ins.policy} | تغطية: ${ins.coverage}%</span>
                </div>
                <div class="text-right">
                    <button onclick="removeCoverage('insurance')" class="text-xs text-red-500 hover:text-red-700">
                        إزالة
                    </button>
                </div>
            </div>
        `;
    }
    
    // Add warranty summary
    if (currentInvoiceData.coverage.warranty) {
        const war = currentInvoiceData.coverage.warranty;
        const warrantyTypeMap = {
            'manufacturer': 'ضمان الشركة المصنعة',
            'extended': 'ضمان ممدد',
            'service': 'ضمان الخدمة',
            'parts': 'ضمان قطع الغيار'
        };
        summaryHTML += `
            <div class="flex items-center justify-between p-2 bg-white rounded border mb-2">
                <div>
                    <span class="text-sm font-medium">${warrantyTypeMap[war.type] || war.type}</span>
                    <span class="text-xs text-gray-500 block">انتهاء: ${war.expiry} | تغطية: ${war.coverage}%</span>
                </div>
                <div class="text-right">
                    <button onclick="removeCoverage('warranty')" class="text-xs text-red-500 hover:text-red-700">
                        إزالة
                    </button>
                </div>
            </div>
        `;
    }
    
    if (summaryHTML) {
        summaryList.innerHTML = summaryHTML;
        summaryContainer.classList.remove('hidden');
    } else {
        summaryContainer.classList.add('hidden');
    }
}

// Remove Coverage
function removeCoverage(type) {
    const typeText = type === 'insurance' ? 'التأمين' : 'الضمان';
    showConfirmModal(`هل أنت متأكد من إزالة ${typeText}؟`, function() {
        if (currentInvoiceData && currentInvoiceData.coverage) {
            delete currentInvoiceData.coverage[type];
            
            // If no coverage left, remove the coverage object
            if (Object.keys(currentInvoiceData.coverage).length === 0) {
                delete currentInvoiceData.coverage;
            }
            
            updateCoverageSummary();
            calculateTotals();
            showAlertModal(`تم إزالة ${typeText} بنجاح`, 'success');
        }
    });
}

// Show advanced notification
function showAdvancedNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-[99999] ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// ==========================================
// INVOICE SEARCH FUNCTIONALITY
// ==========================================

// Initialize invoice search functionality
function initializeInvoiceSearch() {
    const searchInput = document.getElementById('invoiceSearchInput');
    const searchResults = document.getElementById('invoiceSearchResults');
    
    if (!searchInput || !searchResults) return;
    
    // Add event listeners
    searchInput.addEventListener('input', handleSearchInput);
    searchInput.addEventListener('focus', handleSearchFocus);
    searchInput.addEventListener('blur', handleSearchBlur);
    searchInput.addEventListener('keydown', handleSearchKeydown);
    
    // Close search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            hideSearchResults();
        }
    });
}

// Handle search input
function handleSearchInput(e) {
    const query = e.target.value.trim();
    
    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    // Debounce search
    searchTimeout = setTimeout(() => {
        if (query.length >= 2) {
            performInvoiceSearch(query);
        } else if (query.length === 0) {
            hideSearchResults();
        }
    }, 300);
}

// Handle search focus
function handleSearchFocus(e) {
    const query = e.target.value.trim();
    if (query.length >= 2) {
        performInvoiceSearch(query);
    }
}

// Handle search blur (delayed to allow for result selection)
function handleSearchBlur(e) {
    setTimeout(() => {
        hideSearchResults();
    }, 200);
}

// Handle keyboard navigation
function handleSearchKeydown(e) {
    const searchResults = document.getElementById('invoiceSearchResults');
    const resultItems = searchResults.querySelectorAll('.search-result-item');
    const selectedItem = searchResults.querySelector('.search-result-item.selected');
    
    if (e.key === 'ArrowDown') {
        e.preventDefault();
        if (selectedItem) {
            selectedItem.classList.remove('selected');
            const nextItem = selectedItem.nextElementSibling;
            if (nextItem && nextItem.classList.contains('search-result-item')) {
                nextItem.classList.add('selected');
            } else if (resultItems.length > 0) {
                resultItems[0].classList.add('selected');
            }
        } else if (resultItems.length > 0) {
            resultItems[0].classList.add('selected');
        }
    } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        if (selectedItem) {
            selectedItem.classList.remove('selected');
            const prevItem = selectedItem.previousElementSibling;
            if (prevItem && prevItem.classList.contains('search-result-item')) {
                prevItem.classList.add('selected');
            } else if (resultItems.length > 0) {
                resultItems[resultItems.length - 1].classList.add('selected');
            }
        } else if (resultItems.length > 0) {
            resultItems[resultItems.length - 1].classList.add('selected');
        }
    } else if (e.key === 'Enter') {
        e.preventDefault();
        if (selectedItem) {
            const invoiceId = selectedItem.dataset.invoiceId;
            selectInvoice(invoiceId);
        }
    } else if (e.key === 'Escape') {
        hideSearchResults();
        e.target.blur();
    }
}

// Perform invoice search
function performInvoiceSearch(query) {
    const searchResults = document.getElementById('invoiceSearchResults');
    const resultsList = document.getElementById('searchResultsList');
    const noResultsMessage = document.getElementById('noResultsMessage');
    
    // Show loading state
    resultsList.innerHTML = '<div class="search-loading">جاري البحث... <div class="spinner"></div></div>';
    searchResults.classList.remove('hidden');
    noResultsMessage.classList.add('hidden');
    
    // Search in local data first (for faster response)
    const localResults = searchInvoicesLocally(query);
    
    if (localResults.length > 0) {
        displaySearchResults(localResults, query);
    } else {
        // If no local results, search on server
        searchInvoicesOnServer(query);
    }
}

// Search invoices locally
function searchInvoicesLocally(query) {
    if (!availableInvoices || availableInvoices.length === 0) {
        return [];
    }
    
    const lowerQuery = query.toLowerCase();
    
    return availableInvoices.filter(invoice => {
        return (
            invoice.customer_name?.toLowerCase().includes(lowerQuery) ||
            invoice.customer_mobile?.toLowerCase().includes(lowerQuery) ||
            invoice.invoice_number?.toLowerCase().includes(lowerQuery) ||
            invoice.work_order_number?.toLowerCase().includes(lowerQuery)
        );
    }).slice(0, 10); // Limit to 10 results
}

// Search invoices on server
function searchInvoicesOnServer(query) {
    fetch(getApiUrl('/billing/api/search-invoices/'), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            query: query,
            limit: 10
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.invoices) {
            displaySearchResults(data.invoices, query);
        } else {
            showNoResults();
        }
    })
    .catch(error => {
        console.error('Error searching invoices:', error);
        showNoResults();
    });
}

// Display search results
function displaySearchResults(results, query) {
    const resultsList = document.getElementById('searchResultsList');
    const noResultsMessage = document.getElementById('noResultsMessage');
    
    if (results.length === 0) {
        showNoResults();
        return;
    }
    
    resultsList.innerHTML = results.map(invoice => {
        const highlightedName = highlightSearchTerm(invoice.customer_name || '', query);
        const highlightedMobile = highlightSearchTerm(invoice.customer_mobile || '', query);
        const highlightedInvoiceNumber = highlightSearchTerm(invoice.invoice_number || '', query);
        
        return `
            <div class="search-result-item" data-invoice-id="${invoice.id}" onclick="selectInvoice('${invoice.id}')">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="font-medium text-gray-900">${highlightedInvoiceNumber}</div>
                        <div class="text-sm text-gray-600">${highlightedName}</div>
                        ${invoice.customer_mobile ? `<div class="text-sm text-blue-600"><i class="fas fa-phone mr-1"></i>${highlightedMobile}</div>` : ''}
                        ${invoice.work_order_number ? `<div class="text-xs text-gray-500">أمر العمل: ${invoice.work_order_number}</div>` : ''}
                    </div>
                    <div class="text-right">
                        <div class="font-bold text-green-600">${parseFloat(invoice.amount_due || 0).toFixed(2)} ج.م</div>
                        <div class="text-xs text-gray-500">${invoice.status || 'مستحق'}</div>
                        ${invoice.invoice_date ? `<div class="text-xs text-gray-400">${new Date(invoice.invoice_date).toLocaleDateString('ar-EG')}</div>` : ''}
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    noResultsMessage.classList.add('hidden');
}

// Highlight search term in text
function highlightSearchTerm(text, term) {
    if (!text || !term) return text;
    
    const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<span class="search-highlight">$1</span>');
}

// Show no results message
function showNoResults() {
    const resultsList = document.getElementById('searchResultsList');
    const noResultsMessage = document.getElementById('noResultsMessage');
    
    resultsList.innerHTML = '';
    noResultsMessage.classList.remove('hidden');
}

// Hide search results
function hideSearchResults() {
    const searchResults = document.getElementById('invoiceSearchResults');
    searchResults.classList.add('hidden');
}

// Select invoice from search results
function selectInvoice(invoiceId) {
    selectedInvoiceId = invoiceId;
    
    // Find invoice data
    let selectedInvoice = availableInvoices.find(inv => inv.id == invoiceId);
    
    if (selectedInvoice) {
        // Update search input
        const searchInput = document.getElementById('invoiceSearchInput');
        searchInput.value = `${selectedInvoice.invoice_number} - ${selectedInvoice.customer_name}`;
        
        // Show selected invoice display
        const selectedDisplay = document.getElementById('selectedInvoiceDisplay');
        const selectedInfo = document.getElementById('selectedInvoiceInfo');
        
        selectedInfo.innerHTML = `
            <div class="font-medium">${selectedInvoice.invoice_number}</div>
            <div class="text-sm">${selectedInvoice.customer_name}</div>
            ${selectedInvoice.customer_mobile ? `<div class="text-sm"><i class="fas fa-phone mr-1"></i>${selectedInvoice.customer_mobile}</div>` : ''}
            <div class="text-sm font-bold text-green-600">${parseFloat(selectedInvoice.amount_due || 0).toFixed(2)} ج.م</div>
        `;
        
        selectedDisplay.classList.remove('hidden');
        
        // Hide search results
        hideSearchResults();
        
        // Load invoice details
        loadDetailedInvoiceData(invoiceId);
        showInvoiceSections();
    }
}

// Clear selected invoice
function clearSelectedInvoice() {
    selectedInvoiceId = null;
    currentInvoiceData = null;
    
    // Clear search input
    const searchInput = document.getElementById('invoiceSearchInput');
    if (searchInput) searchInput.value = '';
    
    // Clear dropdown selection
    const invoiceSelect = document.getElementById('invoiceSelect');
    if (invoiceSelect) invoiceSelect.value = '';
    
    // Hide selected display
    const selectedDisplay = document.getElementById('selectedInvoiceDisplay');
    if (selectedDisplay) selectedDisplay.classList.add('hidden');
    
    // Hide invoice sections
    hideInvoiceSections();
    
    // Clear discount and payment data
    appliedDiscountsList = [];
    paymentMethodsList = [];
}

// ==========================================
// TAB SWITCHING FUNCTIONALITY
// ==========================================

// Switch between search and dropdown methods
function switchSelectionMethod(method) {
    const searchTab = document.getElementById('searchTabBtn');
    const dropdownTab = document.getElementById('dropdownTabBtn');
    const searchMethod = document.getElementById('searchMethod');
    const dropdownMethod = document.getElementById('dropdownMethod');
    
    if (method === 'search') {
        // Activate search tab
        searchTab.className = searchTab.className.replace('tab-inactive', 'tab-active');
        if (!searchTab.className.includes('tab-active')) {
            searchTab.className += ' tab-active';
        }
        searchTab.className = searchTab.className.replace('text-gray-600', 'text-blue-600');
        searchTab.className = searchTab.className.replace('bg-transparent', 'bg-white shadow-sm');
        
        // Deactivate dropdown tab
        dropdownTab.className = dropdownTab.className.replace('tab-active', 'tab-inactive');
        if (!dropdownTab.className.includes('tab-inactive')) {
            dropdownTab.className += ' tab-inactive';
        }
        
        // Show search, hide dropdown
        searchMethod.classList.remove('hidden');
        dropdownMethod.classList.add('hidden');
        
        // Clear any dropdown selection
        const invoiceSelect = document.getElementById('invoiceSelect');
        if (invoiceSelect) invoiceSelect.value = '';
        
        // Focus on search input
        const searchInput = document.getElementById('invoiceSearchInput');
        if (searchInput) {
            setTimeout(() => searchInput.focus(), 100);
        }
        
    } else if (method === 'dropdown') {
        // Activate dropdown tab
        dropdownTab.className = dropdownTab.className.replace('tab-inactive', 'tab-active');
        if (!dropdownTab.className.includes('tab-active')) {
            dropdownTab.className += ' tab-active';
        }
        dropdownTab.className = dropdownTab.className.replace('text-gray-600', 'text-blue-600');
        dropdownTab.className = dropdownTab.className.replace('bg-transparent', 'bg-white shadow-sm');
        
        // Deactivate search tab
        searchTab.className = searchTab.className.replace('tab-active', 'tab-inactive');
        if (!searchTab.className.includes('tab-inactive')) {
            searchTab.className += ' tab-inactive';
        }
        
        // Show dropdown, hide search
        dropdownMethod.classList.remove('hidden');
        searchMethod.classList.add('hidden');
        
        // Clear search input and hide results
        const searchInput = document.getElementById('invoiceSearchInput');
        if (searchInput) searchInput.value = '';
        hideSearchResults();
    }
}

// Select invoice from dropdown
function selectInvoiceFromDropdown(invoiceId) {
    selectedInvoiceId = invoiceId;
    
    // Find invoice data
    let selectedInvoice = availableInvoices.find(inv => inv.id == invoiceId);
    
    if (selectedInvoice) {
        // Show selected invoice display
        const selectedDisplay = document.getElementById('selectedInvoiceDisplay');
        const selectedInfo = document.getElementById('selectedInvoiceInfo');
        
        selectedInfo.innerHTML = `
            <div class="font-medium">${selectedInvoice.invoice_number}</div>
            <div class="text-sm">${selectedInvoice.customer_name}</div>
            ${selectedInvoice.customer_mobile ? `<div class="text-sm"><i class="fas fa-phone mr-1"></i>${selectedInvoice.customer_mobile}</div>` : ''}
            <div class="text-sm font-bold text-green-600">${parseFloat(selectedInvoice.amount_due || 0).toFixed(2)} ج.م</div>
        `;
        
        selectedDisplay.classList.remove('hidden');
        
        // Load invoice details
        loadDetailedInvoiceData(invoiceId);
        showInvoiceSections();
    }
}

// ==========================================
// MODAL SYSTEM FOR CONFIRMATIONS AND ALERTS
// ==========================================

// Show confirmation modal instead of confirm()
function showConfirmModal(message, onConfirm, onCancel = null) {
    const modal = document.createElement('div');
    modal.className = 'fixed flex items-center justify-center';
    modal.style.zIndex = '99999';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100vw';
    modal.style.height = '100vh';
    modal.style.backgroundColor = 'transparent'; // No background dimming
    modal.style.pointerEvents = 'none'; // Allow clicks through backdrop
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl border-2 border-gray-300" style="pointer-events: auto;">
            <div class="flex items-center mb-4">
                <i class="fas fa-question-circle text-yellow-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-medium text-gray-900">تأكيد العملية</h3>
            </div>
            <p class="text-gray-700 mb-6">${message}</p>
            <div class="flex justify-end space-x-3 space-x-reverse">
                <button onclick="closeConfirmModal(false)" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                    إلغاء
                </button>
                <button onclick="closeConfirmModal(true)" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    تأكيد
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Store callbacks
    window.currentConfirmCallbacks = { onConfirm, onCancel };
    
    // Add unique identifier for this modal
    modal.setAttribute('data-modal-type', 'confirm');
    modal.setAttribute('data-modal-id', Date.now().toString());
    
    // Close on escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeConfirmModal(false);
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

// Close confirmation modal
function closeConfirmModal(confirmed) {
    // Find the most recent confirm modal
    const confirmModals = document.querySelectorAll('[data-modal-type="confirm"]');
    if (confirmModals.length > 0) {
        const latestModal = confirmModals[confirmModals.length - 1];
        latestModal.remove();
    }
    
    if (window.currentConfirmCallbacks) {
        if (confirmed && window.currentConfirmCallbacks.onConfirm) {
            window.currentConfirmCallbacks.onConfirm();
        } else if (!confirmed && window.currentConfirmCallbacks.onCancel) {
            window.currentConfirmCallbacks.onCancel();
        }
        window.currentConfirmCallbacks = null;
    }
}

// Show alert modal instead of alert()
function showAlertModal(message, type = 'info') {
    // Calculate z-index to ensure it's always on top of existing modals
    const existingModals = document.querySelectorAll('.fixed.inset-0');
    let highestZIndex = 99999;
    
    existingModals.forEach(modal => {
        const zIndex = parseInt(window.getComputedStyle(modal).zIndex) || 0;
        if (zIndex >= highestZIndex) {
            highestZIndex = zIndex + 100;
        }
    });
    
    const modal = document.createElement('div');
    modal.className = 'fixed flex items-center justify-center';
    modal.style.zIndex = highestZIndex.toString(); // Always on top of existing modals
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100vw';
    modal.style.height = '100vh';
    modal.style.backgroundColor = 'transparent'; // No background dimming
    modal.style.pointerEvents = 'none'; // Allow clicks through backdrop
    
    let iconClass, iconColor, bgColor;
    switch (type) {
        case 'success':
            iconClass = 'fas fa-check-circle';
            iconColor = 'text-green-500';
            bgColor = 'bg-green-50';
            break;
        case 'error':
            iconClass = 'fas fa-exclamation-circle';
            iconColor = 'text-red-500';
            bgColor = 'bg-red-50';
            break;
        case 'warning':
            iconClass = 'fas fa-exclamation-triangle';
            iconColor = 'text-yellow-500';
            bgColor = 'bg-yellow-50';
            break;
        default:
            iconClass = 'fas fa-info-circle';
            iconColor = 'text-blue-500';
            bgColor = 'bg-blue-50';
    }
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-2xl border-2 border-${type === 'warning' ? 'yellow' : type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue'}-300" style="pointer-events: auto;">
            <div class="flex items-center mb-4">
                <i class="${iconClass} ${iconColor} text-2xl mr-3"></i>
                <h3 class="text-lg font-bold text-gray-900">تنبيه</h3>
            </div>
            <div class="${bgColor} rounded-md p-3 mb-4 border border-${type === 'warning' ? 'yellow' : type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue'}-200">
                <div class="text-gray-800 font-medium text-center">${message}</div>
            </div>
            <div class="flex justify-center">
                <button onclick="closeAlertModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium shadow-lg">
                    موافق
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add unique identifier for this modal
    modal.setAttribute('data-modal-type', 'alert');
    modal.setAttribute('data-modal-id', Date.now().toString());
    
    // Focus on the modal for accessibility
    modal.setAttribute('tabindex', '-1');
    modal.focus();
    
    // Auto close after 5 seconds for success messages
    if (type === 'success') {
        setTimeout(() => {
            if (modal && modal.parentNode) {
                closeAlertModal();
            }
        }, 5000);
    }
    
    // Close on escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeAlertModal();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

// Close alert modal
function closeAlertModal() {
    // Find the most recent alert modal
    const alertModals = document.querySelectorAll('[data-modal-type="alert"]');
    if (alertModals.length > 0) {
        const latestModal = alertModals[alertModals.length - 1];
        latestModal.remove();
    }
}

// Show input modal instead of prompt()
function showInputModal(message, defaultValue = '', onConfirm, onCancel = null) {
    const modal = document.createElement('div');
    modal.className = 'fixed flex items-center justify-center';
    modal.style.zIndex = '99999';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100vw';
    modal.style.height = '100vh';
    modal.style.backgroundColor = 'transparent'; // No background dimming
    modal.style.pointerEvents = 'none'; // Allow clicks through backdrop
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl border-2 border-blue-300" style="pointer-events: auto;">
            <div class="flex items-center mb-4">
                <i class="fas fa-edit text-blue-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-medium text-gray-900">إدخال قيمة</h3>
            </div>
            <p class="text-gray-700 mb-4">${message}</p>
            <input type="text" id="modalInput" value="${defaultValue}" class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mb-4" placeholder="أدخل القيمة...">
            <div class="flex justify-end space-x-3 space-x-reverse">
                <button onclick="closeInputModal(false)" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                    إلغاء
                </button>
                <button onclick="closeInputModal(true)" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    تأكيد
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add unique identifier for this modal
    modal.setAttribute('data-modal-type', 'input');
    modal.setAttribute('data-modal-id', Date.now().toString());
    
    // Focus on input
    const input = document.getElementById('modalInput');
    input.focus();
    input.select();
    
    // Handle Enter key
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            closeInputModal(true);
        }
    });
    
    // Close on escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeInputModal(false);
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
    
    // Store callbacks
    window.currentInputCallbacks = { onConfirm, onCancel };
}

// Close input modal
function closeInputModal(confirmed) {
    // Find the most recent input modal
    const inputModals = document.querySelectorAll('[data-modal-type="input"]');
    const input = document.getElementById('modalInput');
    const value = input ? input.value : '';
    
    if (inputModals.length > 0) {
        const latestModal = inputModals[inputModals.length - 1];
        latestModal.remove();
    }
    
    if (window.currentInputCallbacks) {
        if (confirmed && window.currentInputCallbacks.onConfirm) {
            window.currentInputCallbacks.onConfirm(value);
        } else if (!confirmed && window.currentInputCallbacks.onCancel) {
            window.currentInputCallbacks.onCancel();
        }
        window.currentInputCallbacks = null;
    }
}

// Show printable invoice modal
function showPrintableInvoice(invoiceId) {
    fetch(getApiUrl(`/billing/api/invoice-details/${invoiceId}/`))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const invoice = data.invoice;
                createPrintableInvoiceModal(invoice);
            } else {
                showAlertModal('خطأ في تحميل بيانات الفاتورة', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading invoice details:', error);
            showAlertModal('خطأ في الاتصال بالخادم', 'error');
        });
}

// Create printable invoice modal
function createPrintableInvoiceModal(invoice) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 flex items-center justify-center';
    modal.id = 'printableInvoiceModal';
    modal.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 999999 !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 1rem !important;
        box-sizing: border-box !important;
        pointer-events: auto !important;
        overflow: auto !important;
    `;
    
    // Format currency
    const formatCurrency = (amount) => {
        return parseFloat(amount || 0).toLocaleString('ar-EG', { 
            minimumFractionDigits: 2, 
            maximumFractionDigits: 2 
        }) + ' ج.م';
    };
    
    // Format date
    const formatDate = (dateStr) => {
        if (!dateStr || dateStr === 'null' || dateStr === null) return 'غير محدد';
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) return 'غير محدد';
            return date.toLocaleDateString('ar-EG');
        } catch (e) {
            return 'غير محدد';
        }
    };
    
    // Generate items HTML
    let itemsHtml = '';
    if (invoice.items && invoice.items.length > 0) {
        invoice.items.forEach((item, index) => {
            itemsHtml += `
                <tr class="border-b">
                    <td class="py-2 px-3 text-center">${index + 1}</td>
                    <td class="py-2 px-3">${item.description || 'غير محدد'}</td>
                    <td class="py-2 px-3 text-center">${parseFloat(item.quantity || 0).toFixed(2)}</td>
                    <td class="py-2 px-3 text-center">${formatCurrency(item.unit_price)}</td>
                    <td class="py-2 px-3 text-center">${item.discount_percentage > 0 ? parseFloat(item.discount_percentage).toFixed(1) + '%' : '-'}</td>
                    <td class="py-2 px-3 text-center font-medium">${formatCurrency(item.line_total)}</td>
                </tr>
            `;
        });
    } else {
        itemsHtml = '<tr><td colspan="6" class="py-4 text-center text-gray-500">لا توجد عناصر</td></tr>';
    }
    
    // Generate discounts HTML
    let discountsHtml = '';
    if (invoice.applied_discounts && invoice.applied_discounts.length > 0) {
        invoice.applied_discounts.forEach(discount => {
            discountsHtml += `
                <tr class="border-b">
                    <td class="py-2 px-3">${discount.reason || 'خصم'}</td>
                    <td class="py-2 px-3 text-center">${discount.percentage > 0 ? parseFloat(discount.percentage).toFixed(1) + '%' : formatCurrency(discount.fixed_amount)}</td>
                    <td class="py-2 px-3 text-center text-red-600 font-medium">-${formatCurrency(discount.discount_amount)}</td>
                </tr>
            `;
        });
    }
    
    // Generate payments HTML
    let paymentsHtml = '';
    if (invoice.payments && invoice.payments.length > 0) {
        invoice.payments.forEach(payment => {
            paymentsHtml += `
                <tr class="border-b">
                    <td class="py-2 px-3">${formatDate(payment.payment_date)}</td>
                    <td class="py-2 px-3">${payment.payment_method_name || 'غير محدد'}</td>
                    <td class="py-2 px-3 text-center">${payment.reference_number || '-'}</td>
                    <td class="py-2 px-3 text-center font-medium text-green-600">${formatCurrency(payment.amount)}</td>
                </tr>
            `;
        });
    }
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-2xl max-w-4xl w-full mx-4 max-h-[95vh] overflow-y-auto print:max-h-none print:shadow-none print:rounded-none" style="position: relative !important; z-index: 1000000 !important; pointer-events: auto !important; margin: auto !important;">
            <!-- Print Header -->
            <div class="print:block hidden text-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">فاتورة</h1>
                <div class="border-b-2 border-gray-300 mt-2"></div>
            </div>
            
            <!-- Modal Header (Hidden in print) -->
            <div class="print:hidden flex flex-row-reverse items-center justify-between p-6 border-b">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-print mr-2"></i>
                    فاتورة رقم ${invoice.invoice_number}
                </h2>
                <div class="flex gap-3">
                    <button onclick="printModalContent()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-print mr-2"></i>طباعة الفاتورة
                    </button>
                    <button onclick="closePrintableInvoice()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                        <i class="fas fa-times mr-2"></i>إغلاق
                    </button>
                </div>
            </div>
            
            <!-- Invoice Content -->
            <div class="p-6 print:p-4" id="invoiceContent">
                <!-- Company Header -->
                <div class="text-center mb-6 print:mb-4">
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">${invoice.service_center_name || 'مركز الخدمة'}</h1>
                    <p class="text-gray-600">${invoice.service_center_address || ''}</p>
                    <p class="text-gray-600">${invoice.service_center_phone || ''}</p>
                </div>
                
                <!-- Invoice Header -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print:mb-4">
                    <div class="bg-gray-50 p-4 rounded-lg print:bg-transparent print:border print:border-gray-300">
                        <h3 class="font-bold text-gray-800 mb-2">معلومات الفاتورة</h3>
                        <div class="space-y-1 text-sm">
                            <div><span class="font-medium">رقم الفاتورة:</span> ${invoice.invoice_number}</div>
                            <div><span class="font-medium">تاريخ الفاتورة:</span> ${formatDate(invoice.invoice_date)}</div>
                            <div><span class="font-medium">تاريخ الاستحقاق:</span> ${formatDate(invoice.due_date)}</div>
                            <div><span class="font-medium">الحالة:</span> 
                                <span class="px-2 py-1 rounded text-xs ${
                                    invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                                    invoice.status === 'partially_paid' ? 'bg-yellow-100 text-yellow-800' :
                                    invoice.status === 'overdue' ? 'bg-red-100 text-red-800' :
                                    'bg-blue-100 text-blue-800'
                                }">
                                    ${
                                        invoice.status === 'paid' ? 'مدفوعة' :
                                        invoice.status === 'partially_paid' ? 'مدفوعة جزئياً' :
                                        invoice.status === 'overdue' ? 'متأخرة' :
                                        invoice.status === 'issued' ? 'صادرة' : 'مسودة'
                                    }
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg print:bg-transparent print:border print:border-gray-300">
                        <h3 class="font-bold text-gray-800 mb-2">معلومات العميل</h3>
                        <div class="space-y-1 text-sm">
                            <div><span class="font-medium">الاسم:</span> ${invoice.customer_name || 'غير محدد'}</div>
                            <div><span class="font-medium">الهاتف:</span> ${invoice.customer_phone || 'غير محدد'}</div>
                            <div><span class="font-medium">العنوان:</span> ${invoice.customer_address || 'غير محدد'}</div>
                            ${invoice.vehicle_info ? `<div><span class="font-medium">المركبة:</span> ${invoice.vehicle_info}</div>` : ''}
                        </div>
                    </div>
                </div>
                
                <!-- Work Order Info -->
                ${invoice.work_order_number ? `
                <div class="bg-blue-50 p-4 rounded-lg mb-6 print:bg-transparent print:border print:border-blue-300 print:mb-4">
                    <h3 class="font-bold text-blue-800 mb-2">معلومات أمر العمل</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div><span class="font-medium">رقم أمر العمل:</span> ${invoice.work_order_number}</div>
                        <div><span class="font-medium">تاريخ الإنجاز:</span> ${invoice.work_order_completion_date ? formatDate(invoice.work_order_completion_date) : 'لم يتم تحديد تاريخ الإنجاز'}</div>
                    </div>
                </div>
                ` : ''}
                
                <!-- Invoice Items -->
                <div class="mb-6 print:mb-4">
                    <h3 class="font-bold text-gray-800 mb-3">تفاصيل الفاتورة</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full border border-gray-300 text-sm">
                            <thead class="bg-gray-100 print:bg-gray-200">
                                <tr>
                                    <th class="py-2 px-3 text-center border-b font-medium">#</th>
                                    <th class="py-2 px-3 text-right border-b font-medium">الوصف</th>
                                    <th class="py-2 px-3 text-center border-b font-medium">الكمية</th>
                                    <th class="py-2 px-3 text-center border-b font-medium">السعر</th>
                                    <th class="py-2 px-3 text-center border-b font-medium">الخصم</th>
                                    <th class="py-2 px-3 text-center border-b font-medium">الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${itemsHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Applied Discounts -->
                ${discountsHtml ? `
                <div class="mb-6 print:mb-4">
                    <h3 class="font-bold text-gray-800 mb-3">الخصومات المطبقة</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full border border-gray-300 text-sm">
                            <thead class="bg-red-50 print:bg-gray-200">
                                <tr>
                                    <th class="py-2 px-3 text-right border-b font-medium">نوع الخصم</th>
                                    <th class="py-2 px-3 text-center border-b font-medium">القيمة</th>
                                    <th class="py-2 px-3 text-center border-b font-medium">المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${discountsHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
                ` : ''}
                
                <!-- Insurance & Warranty -->
                ${(invoice.insurance_details || invoice.warranty_details) ? `
                <div class="mb-6 print:mb-4">
                    <h3 class="font-bold text-gray-800 mb-3">التأمين والضمان</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        ${invoice.insurance_details ? `
                        <div class="bg-cyan-50 p-4 rounded-lg print:bg-transparent print:border print:border-cyan-300">
                            <h4 class="font-medium text-cyan-800 mb-2">التأمين</h4>
                            <div class="space-y-1 text-sm">
                                <div><span class="font-medium">الشركة:</span> ${invoice.insurance_details.company || 'غير محدد'}</div>
                                <div><span class="font-medium">رقم البوليصة:</span> ${invoice.insurance_details.policy_number || 'غير محدد'}</div>
                                <div><span class="font-medium">نسبة التغطية:</span> ${invoice.insurance_details.coverage_percentage || 0}%</div>
                                <div><span class="font-medium">مبلغ التغطية:</span> ${formatCurrency(invoice.insurance_details.coverage_amount || 0)}</div>
                            </div>
                        </div>
                        ` : ''}
                        
                        ${invoice.warranty_details ? `
                        <div class="bg-teal-50 p-4 rounded-lg print:bg-transparent print:border print:border-teal-300">
                            <h4 class="font-medium text-teal-800 mb-2">الضمان</h4>
                            <div class="space-y-1 text-sm">
                                <div><span class="font-medium">نوع الضمان:</span> ${invoice.warranty_details.type || 'غير محدد'}</div>
                                <div><span class="font-medium">رقم الضمان:</span> ${invoice.warranty_details.warranty_number || 'غير محدد'}</div>
                                <div><span class="font-medium">نسبة التغطية:</span> ${invoice.warranty_details.coverage_percentage || 0}%</div>
                                <div><span class="font-medium">مبلغ التغطية:</span> ${formatCurrency(invoice.warranty_details.coverage_amount || 0)}</div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
                ` : ''}
                
                <!-- Financial Summary -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print:mb-4">
                    <!-- Payment Summary -->
                    ${paymentsHtml ? `
                    <div>
                        <h3 class="font-bold text-gray-800 mb-3">سجل المدفوعات</h3>
                        <div class="overflow-x-auto">
                            <table class="w-full border border-gray-300 text-sm">
                                <thead class="bg-green-50 print:bg-gray-200">
                                    <tr>
                                        <th class="py-2 px-3 text-center border-b font-medium">التاريخ</th>
                                        <th class="py-2 px-3 text-center border-b font-medium">الطريقة</th>
                                        <th class="py-2 px-3 text-center border-b font-medium">المرجع</th>
                                        <th class="py-2 px-3 text-center border-b font-medium">المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${paymentsHtml}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    ` : ''}
                    
                    <!-- Totals -->
                    <div>
                        <h3 class="font-bold text-gray-800 mb-3">الملخص المالي</h3>
                        <div class="bg-gray-50 p-4 rounded-lg print:bg-transparent print:border print:border-gray-300">
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span>المجموع الفرعي:</span>
                                    <span class="font-medium">${formatCurrency(invoice.subtotal)}</span>
                                </div>
                                ${parseFloat(invoice.discount_amount || 0) > 0 ? `
                                <div class="flex justify-between text-red-600">
                                    <span>إجمالي الخصومات:</span>
                                    <span class="font-medium">-${formatCurrency(invoice.discount_amount)}</span>
                                </div>
                                ` : ''}
                                ${parseFloat(invoice.tax_amount || 0) > 0 ? `
                                <div class="flex justify-between">
                                    <span>الضريبة (${invoice.tax_percentage || 0}%):</span>
                                    <span class="font-medium">${formatCurrency(invoice.tax_amount)}</span>
                                </div>
                                ` : ''}
                                <div class="border-t pt-2 flex justify-between font-bold text-lg">
                                    <span>الإجمالي النهائي:</span>
                                    <span class="text-blue-600">${formatCurrency(invoice.total_amount)}</span>
                                </div>
                                <div class="flex justify-between text-green-600">
                                    <span>المدفوع:</span>
                                    <span class="font-medium">${formatCurrency(invoice.amount_paid)}</span>
                                </div>
                                <div class="flex justify-between ${parseFloat(invoice.amount_due || 0) > 0 ? 'text-red-600' : 'text-gray-600'}">
                                    <span>المتبقي:</span>
                                    <span class="font-bold">${formatCurrency(invoice.amount_due)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Notes -->
                ${invoice.notes ? `
                <div class="mb-6 print:mb-4">
                    <h3 class="font-bold text-gray-800 mb-3">ملاحظات</h3>
                    <div class="bg-yellow-50 p-4 rounded-lg print:bg-transparent print:border print:border-yellow-300">
                        <p class="text-sm text-gray-700">${invoice.notes}</p>
                    </div>
                </div>
                ` : ''}
                
                <!-- Terms and Conditions -->
                ${invoice.terms_and_conditions ? `
                <div class="mb-6 print:mb-4">
                    <h3 class="font-bold text-gray-800 mb-3">الشروط والأحكام</h3>
                    <div class="bg-gray-50 p-4 rounded-lg print:bg-transparent print:border print:border-gray-300">
                        <p class="text-sm text-gray-700">${invoice.terms_and_conditions}</p>
                    </div>
                </div>
                ` : ''}
                
                <!-- Footer -->
                <div class="text-center text-sm text-gray-500 mt-8 print:mt-6 border-t pt-4">
                    <p>شكراً لثقتكم بنا</p>
                    <p class="mt-1">تم إنشاء هذه الفاتورة في ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</p>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Prevent modal from being affected by mouse events outside browser
    modal.style.pointerEvents = 'auto';
    
    // Close modal only on explicit background click (not on mouse leave events)
    // Remove problematic click listener that was interfering with positioning
    
    // Simple escape key to close
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeModal('printableInvoiceModal');
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

// Close printable invoice modal - use standard closeModal function

// Helper functions for dynamic data processing
function generateOperationsTableRows(invoice) {
    let rowsHtml = '';
    
    // Get operations from invoice.operations or derive from labor items
    let operations = [];
    
    if (invoice.operations && Array.isArray(invoice.operations) && invoice.operations.length > 0) {
        operations = invoice.operations;
    } else if (invoice.items && Array.isArray(invoice.items)) {
        // Extract labor/service items as operations
        const laborItems = invoice.items.filter(item => 
            item.item_type === 'labor' || 
            item.item_type === 'service'
        );
        
        laborItems.forEach(item => {
            operations.push({
                description: item.description,
                cost: parseFloat(item.total || item.line_total || 0),
                quantity: parseFloat(item.quantity || 1),
                duration: item.duration || 'حسب الحاجة'
            });
        });
    }
    
    // If still no operations, create a default one
    if (operations.length === 0) {
        const laborTotal = calculateOperationsTotal(invoice);
        if (laborTotal > 0) {
            operations.push({
                description: 'خدمات الصيانة العامة',
                cost: laborTotal,
                quantity: 1,
                duration: 'حسب الحاجة'
            });
        }
    }
    
    if (operations.length === 0) {
        // Show empty state message
        rowsHtml = `
            <tr>
                <td class="border p-2 text-center text-gray-500" colspan="4">لا توجد عمليات مسجلة</td>
            </tr>
        `;
    } else {
        operations.forEach(operation => {
            const cost = parseFloat(operation.cost || 0);
            const quantity = parseFloat(operation.quantity || 1);
            const total = cost * quantity;
            
            rowsHtml += `
                <tr>
                    <td class="border p-2">${operation.description || 'خدمة صيانة'}</td>
                    <td class="border p-2 text-center">${quantity}</td>
                    <td class="border p-2 text-center">${cost.toFixed(2)}</td>
                    <td class="border p-2 text-center font-bold">${total.toFixed(2)}</td>
                </tr>
            `;
        });
    }
    
    return rowsHtml;
}

function generateSparePartsTableRows(invoice) {
    let rowsHtml = '';
    
    if (!invoice.items || !Array.isArray(invoice.items)) {
        return rowsHtml;
    }
    
    // Get parts items
    const partsItems = invoice.items.filter(item => 
        item.item_type === 'part' || 
        (item.item_type !== 'labor' && item.item_type !== 'service' && item.item_type !== 'fee')
    );
    
    if (partsItems.length === 0) {
        // Show empty state message
        rowsHtml = `
            <tr>
                <td class="border p-2 text-center text-gray-500" colspan="5">لا توجد قطع غيار مستخدمة</td>
            </tr>
        `;
    } else {
        partsItems.forEach(item => {
            const quantity = parseFloat(item.quantity || 1);
            const unitPrice = parseFloat(item.unit_price || 0);
            const total = parseFloat(item.total || item.line_total || (quantity * unitPrice));
            const partId = item.part_number || item.part_id || `PART-${item.id}`;
            
            rowsHtml += `
                <tr>
                    <td class="border p-2">${item.description || 'قطعة غيار'}</td>
                    <td class="border p-2 text-center" style="font-family: monospace; font-size: 12px;">${partId}</td>
                    <td class="border p-2 text-center">${quantity}</td>
                    <td class="border p-2 text-center">${unitPrice.toFixed(2)}</td>
                    <td class="border p-2 text-center font-bold">${total.toFixed(2)}</td>
                </tr>
            `;
        });
    }
    
    return rowsHtml;
}

function calculateOperationsTotal(invoice) {
    let total = 0;
    
    if (invoice.operations && Array.isArray(invoice.operations)) {
        invoice.operations.forEach(operation => {
            const cost = parseFloat(operation.cost || 0);
            const quantity = parseFloat(operation.quantity || 1);
            total += cost * quantity;
        });
    } else if (invoice.items && Array.isArray(invoice.items)) {
        // Calculate from labor/service items
        const laborItems = invoice.items.filter(item => 
            item.item_type === 'labor' || 
            item.item_type === 'service'
        );
        
        laborItems.forEach(item => {
            total += parseFloat(item.total || item.line_total || 0);
        });
    }
    
    return total;
}

function calculateSparePartsTotal(invoice) {
    let total = 0;
    
    if (invoice.items && Array.isArray(invoice.items)) {
        const partsItems = invoice.items.filter(item => 
            item.item_type === 'part' || 
            (item.item_type !== 'labor' && item.item_type !== 'service' && item.item_type !== 'fee')
        );
        
        partsItems.forEach(item => {
            total += parseFloat(item.total || item.line_total || 0);
        });
    }
    
    return total;
}

// Create printable invoice modal
function createPrintableInvoiceModal(invoice) {
    // Remove existing modal if any
    const existingModal = document.getElementById('printableInvoiceModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Create modal element
    const modal = document.createElement('div');
    modal.id = 'printableInvoiceModal';
    modal.className = 'print-modal-overlay hidden';
    modal.innerHTML = `
        <div class="modal-container max-w-6xl">
            <div class="modal-header print:hidden">
                <div class="flex flex-row-reverse items-center justify-between">
                    <h3><i class="fas fa-print mr-2"></i>طباعة الفاتورة - ${invoice.invoice_number}</h3>
                    <div class="flex gap-3">
                        <button type="button" onclick="printModalContent()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">
                            <i class="fas fa-print mr-2"></i>طباعة الفاتورة
                        </button>
                        <button type="button" onclick="closeModal('printableInvoiceModal')" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 text-sm">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="modal-body">
                <!-- Ultra-Compact Print Layout -->
                <div class="print-invoice-container">
                    <!-- Readable Header -->
                    <div class="grid grid-cols-3 gap-4 mb-4 border-b-2 pb-3">
                        <div>
                            <h1 class="text-lg font-bold">🏢 شركة أفتر سيلز لخدمات السيارات</h1>
                            <p class="text-sm text-gray-600">شارع النصر، القاهرة | 02-12345678</p>
                        </div>
                        <div class="text-center">
                            <h2 class="text-lg font-bold text-blue-700">📄 ${invoice.invoice_number}</h2>
                            <p class="text-sm">${formatDate(invoice.date)} | ${getInvoiceStatusText(invoice.status)}</p>
                        </div>
                        <div class="text-left">
                            <div class="text-sm font-bold">💰 المبلغ الإجمالي</div>
                            <div class="text-xl font-bold text-green-600">${parseFloat(invoice.total_amount).toFixed(2)} ج.م</div>
                        </div>
                    </div>
                    
                    <!-- Readable Info Row -->
                    <div class="border rounded-lg p-3 mb-4 text-sm bg-gray-50">
                        <div class="text-center leading-relaxed">
                            <strong>👤 العميل:</strong> ${(invoice.customer && invoice.customer.name) || invoice.customer_name || 'غير محدد'} | 
                            <strong>🚗 المركبة:</strong> ${invoice.vehicle ? `${invoice.vehicle.make} ${invoice.vehicle.model} ${invoice.vehicle.year}` : (invoice.vehicle_info || 'غير محدد')} | 
                            <strong>🔧 الفني:</strong> ${(invoice.technician && invoice.technician.name) || invoice.technician_name || 'غير محدد'} | 
                            <strong>💳 الدفع:</strong> ${getPaymentStatusText(invoice.payment_status)}${invoice.work_order_number ? ` | WO: ${invoice.work_order_number}` : ''}
                        </div>
                    </div>

                    <!-- Readable Services and Parts -->
                    <div class="services-section border rounded-lg p-4 mb-4">
                        <h3 class="font-bold text-center text-lg mb-3">🔧 الخدمات والقطع المستخدمة</h3>
                        
                        <!-- Readable Operations -->
                        <div class="mb-4">
                            <h4 class="font-bold text-sm text-orange-700 mb-2">العمليات:</h4>
                            <table class="w-full text-sm border-collapse">
                                <thead>
                                    <tr class="bg-orange-100">
                                        <th class="border p-2 text-right">العملية</th>
                                        <th class="border p-2 w-20">الكمية</th>
                                        <th class="border p-2 w-24">السعر</th>
                                        <th class="border p-2 w-24">المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${generateOperationsTableRows(invoice)}
                                    <tr class="bg-orange-200">
                                        <td class="border p-2 font-bold" colspan="3">إجمالي العمليات</td>
                                        <td class="border p-2 text-center font-bold">${calculateOperationsTotal(invoice).toFixed(2)} ج.م</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Readable Parts -->
                        <div>
                            <h4 class="font-bold text-sm text-purple-700 mb-2">قطع الغيار:</h4>
                            <table class="w-full text-sm border-collapse">
                                <thead>
                                    <tr class="bg-purple-100">
                                        <th class="border p-2 text-right">القطعة</th>
                                        <th class="border p-2 w-24">الرقم</th>
                                        <th class="border p-2 w-20">الكمية</th>
                                        <th class="border p-2 w-24">السعر</th>
                                        <th class="border p-2 w-24">المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${generateSparePartsTableRows(invoice)}
                                    <tr class="bg-purple-200">
                                        <td class="border p-2 font-bold" colspan="4">إجمالي قطع الغيار</td>
                                        <td class="border p-2 text-center font-bold">${calculateSparePartsTotal(invoice).toFixed(2)} ج.م</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Readable Financial Summary -->
                    <div class="border rounded-lg p-4 mb-4">
                        <h3 class="font-bold text-center text-lg mb-3">💰 ملخص مالي</h3>
                        <div class="grid grid-cols-4 gap-2 text-sm text-center">
                            <div class="border rounded p-3">
                                <div class="font-bold">المبلغ الفرعي</div>
                                <div class="text-lg">${parseFloat(invoice.subtotal || invoice.total_amount).toFixed(2)} ج.م</div>
                            </div>
                            <div class="border rounded p-3">
                                <div class="font-bold">الضريبة (14%)</div>
                                <div class="text-lg">${parseFloat(invoice.tax_amount || 0).toFixed(2)} ج.م</div>
                            </div>
                            <div class="border rounded p-3">
                                <div class="font-bold text-red-600">الخصومات</div>
                                <div class="text-red-600 text-lg">-${parseFloat(invoice.discount_amount || 0).toFixed(2)} ج.م</div>
                            </div>
                            <div class="border-2 border-green-500 rounded p-3 bg-green-50">
                                <div class="font-bold">الإجمالي</div>
                                <div class="font-bold text-green-600 text-xl">${parseFloat(invoice.total_amount).toFixed(2)} ج.م</div>
                            </div>
                        </div>
                    </div>
                    
                    ${invoice.payment_status !== 'unpaid' ? `
                        <!-- Ultra-Compact Payment Info -->
                        <div class="border rounded p-1 bg-green-50">
                            <h3 class="font-bold text-center text-xs">💳 معلومات الدفع</h3>
                            <div class="grid grid-cols-3 gap-0 text-xs text-center">
                                <div class="border p-0">
                                    <div class="font-bold">المبلغ المدفوع</div>
                                    <div class="font-bold text-green-600">${parseFloat(invoice.paid_amount || invoice.total_amount).toFixed(2)} ج.م</div>
                                </div>
                                <div class="border p-0">
                                    <div class="font-bold">المبلغ المتبقي</div>
                                    <div class="font-bold text-orange-600">${parseFloat(invoice.remaining_amount || 0).toFixed(2)} ج.م</div>
                                </div>
                                <div class="border p-0">
                                    <div class="font-bold">طريقة الدفع</div>
                                    <div class="font-bold">${invoice.payment_method || 'نقدي'}</div>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                    
                    <!-- Ultra-Compact Warranty and Notes -->
                    <div class="border rounded p-1">
                        <h3 class="font-bold text-center text-xs">📋 الضمان والشروط</h3>
                        <div class="text-xs">
                            <strong>🛡️ الضمان:</strong> 6 أشهر قطع غيار | 3 أشهر عمالة | مدى الحياة لحام<br/>
                            <strong>💳 الدفع:</strong> 30 يوم | خصم 5% نقدي | تقسيط 6 أشهر بدون فوائد<br/>
                            <strong>📅 الصيانة القادمة:</strong> بعد 5000 كم | <strong>🔧 توصية:</strong> فحص الإطارات شهرياً | <strong>📞 الدعم:</strong> 01234567890
                        </div>
                    </div>

                    <!-- Ultra-Compact Footer -->
                    <div class="border-t pt-0 text-xs">
                        <div class="grid grid-cols-3 gap-0 text-center">
                            <div>
                                <div class="font-bold">توقيع العميل</div>
                                <div class="border-b">____________</div>
                            </div>
                            <div>
                                <div class="font-bold">توقيع الفني</div>
                                <div class="border-b">____________</div>
                            </div>
                            <div>
                                <div class="font-bold">التاريخ: ${new Date().toLocaleDateString('ar-EG')}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    modal.classList.remove('hidden');
    
    // Remove all mouse event listeners that could cause positioning issues
    // No click outside to close functionality for print modal
    // No mouse tracking events
    
    // Add only Escape key functionality
    const handleEscape = function(e) {
        if (e.key === 'Escape') {
            closeModal('printableInvoiceModal');
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

// Print only the modal content (not the entire page)
function printModalContent() {
    const modal = document.getElementById('printableInvoiceModal');
    if (!modal) {
        showAlertModal('خطأ: لا يمكن العثور على الفاتورة للطباعة', 'error');
        return;
    }
    
    // Get the invoice content from the modal body
    const modalBody = modal.querySelector('.modal-body');
    if (!modalBody) {
        showAlertModal('خطأ: لا يمكن العثور على محتوى الفاتورة', 'error');
        return;
    }
    
    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    
    if (!printWindow) {
        showAlertModal('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة', 'error');
        return;
    }
    
    // Write the content to the new window with complete modal styling
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>طباعة الفاتورة</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
            <style>
                @page {
                    size: A4;
                    margin: 0.1in;
                }
                
                body {
                    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    direction: rtl;
                    text-align: right;
                    margin: 0;
                    padding: 8px;
                    color: #000;
                    background: white;
                    font-size: 10px;
                    line-height: 1.2;
                    width: 100%;
                    height: 100vh;
                }
                
                * {
                    color: #000 !important;
                    background-color: transparent !important;
                }
                
                .print-invoice-container {
                    max-width: none;
                    margin: 0;
                    padding: 0;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                }
                
                h1, h2, h3, h4, h5, h6 {
                    margin: 1px 0;
                    padding: 0;
                    line-height: 1.0;
                }
                
                h1 { font-size: 16px; font-weight: bold; }
                h2 { font-size: 14px; font-weight: bold; }
                h3 { font-size: 12px; font-weight: bold; }
                h4 { font-size: 10px; font-weight: bold; }
                
                .print-invoice-container {
                    width: 100%;
                    height: 100%;
                    display: block;
                }
                
                .grid {
                    display: block;
                    margin: 1px 0;
                }
                
                .grid > div {
                    margin: 0px;
                    page-break-inside: avoid;
                }
                
                .border {
                    border: 1px solid #000;
                    padding: 1px 2px;
                    margin: 1px 0;
                    border-radius: 1px;
                }
                
                table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 9px;
                    line-height: 1.2;
                    margin: 4px 0;
                }
                
                th, td {
                    border: 1px solid #000;
                    padding: 4px 6px;
                    font-size: 9px;
                    line-height: 1.2;
                }
                
                .text-xs {
                    font-size: 6px;
                }
                
                ul {
                    margin: 1px 0;
                    padding-left: 8px;
                }
                
                li {
                    margin: 0px;
                    font-size: 6px;
                    line-height: 1.0;
                }
                
                /* Compact sections */
                .services-section {
                    margin: 2px 0;
                }
                
                .services-section table {
                    margin: 1px 0;
                }
                
                .services-section th, .services-section td {
                    padding: 1px 2px;
                    font-size: 6px;
                }
                
                /* Remove unnecessary spacing */
                .mb-2 {
                    margin-bottom: 2px !important;
                }
                
                .customer-info h3, .service-info h3, .financial-summary h3, .payment-info h3 {
                    font-size: 8px;
                    margin: 0 0 1px 0;
                    padding-bottom: 1px;
                    border-bottom: 1px solid #ddd;
                    font-weight: bold;
                }
                
                .flex {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 1px;
                }
                
                .text-center {
                    text-align: center;
                }
                
                .font-bold {
                    font-weight: bold;
                }
                
                .font-medium {
                    font-weight: 600;
                }
                
                .space-y-1 > * + * {
                    margin-top: 1px;
                }
                
                .space-y-2 > * + * {
                    margin-top: 2px;
                }
                
                .border-b {
                    border-bottom: 1px solid #ddd;
                    padding-bottom: 1px;
                }
                
                .border-b-2 {
                    border-bottom: 2px solid #000;
                    padding-bottom: 1px;
                }
                
                .invoice-footer {
                    margin-top: 5px;
                    padding-top: 2px;
                    border-top: 1px solid #ccc;
                    font-size: 6px;
                }
                
                .text-xs {
                    font-size: 8px;
                }
                
                .text-sm {
                    font-size: 10px;
                }
                
                .text-lg {
                    font-size: 12px;
                }
                
                .text-xl {
                    font-size: 14px;
                }
                
                .text-2xl {
                    font-size: 16px;
                }
                
                .mb-1 { margin-bottom: 4px; }
                .mb-2 { margin-bottom: 6px; }
                .mb-3 { margin-bottom: 8px; }
                .mb-4 { margin-bottom: 10px; }
                .mb-6 { margin-bottom: 12px; }
                .mt-1 { margin-top: 4px; }
                .mt-2 { margin-top: 6px; }
                .mt-6 { margin-top: 12px; }
                .p-3 { padding: 6px; }
                .p-4 { padding: 8px; }
                
                /* Color classes for print */
                .text-blue-700 { color: #1d4ed8 !important; }
                .text-green-600 { color: #059669 !important; }
                .text-red-600 { color: #dc2626 !important; }
                .text-yellow-800 { color: #92400e !important; }
                .text-purple-600 { color: #9333ea !important; }
                .text-orange-600 { color: #ea580c !important; }
                .text-gray-600 { color: #4b5563 !important; }
                
                /* Background colors for sections */
                .bg-gray-50 { background-color: #f9fafb !important; }
                .bg-blue-50 { background-color: #eff6ff !important; }
                .bg-green-50 { background-color: #f0fdf4 !important; }
                .bg-yellow-50 { background-color: #fefce8 !important; }
                
                /* Responsive grid simulation for print */
                .grid-cols-2 > div:first-child {
                    display: inline-block;
                    width: 48%;
                    vertical-align: top;
                    margin-right: 2%;
                }
                
                .grid-cols-2 > div:last-child {
                    display: inline-block;
                    width: 48%;
                    vertical-align: top;
                }
                
                .grid-cols-3 > div {
                    display: inline-block;
                    width: 32%;
                    vertical-align: top;
                    margin-right: 1%;
                }
                
                .grid-cols-4 > div {
                    display: inline-block;
                    width: 23%;
                    vertical-align: top;
                    margin-right: 1%;
                }
                
                /* Status badge styling */
                .status-badge {
                    padding: 2px 6px;
                    border-radius: 12px;
                    font-size: 10px;
                    font-weight: bold;
                }
                
                @media print {
                    body { margin: 0; padding: 1px; }
                    .no-print { display: none !important; }
                    * { 
                        margin: 0 !important; 
                        padding: 1px !important; 
                        line-height: 1.0 !important; 
                    }
                    table, th, td {
                        margin: 0 !important;
                        padding: 1px !important;
                    }
                }
            </style>
        </head>
        <body>
            ${modalBody.innerHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    
    // Wait for content to load then print
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    };
}

// Show printable invoice modal (without auto-printing) - Load dynamic data from API
function showPrintableInvoiceModal(invoiceId) {
    // Log loading start (no popup to avoid positioning issues)
    console.log('🔄 Loading printable invoice modal...');
    
    // Determine which invoice ID to use
    const targetInvoiceId = invoiceId || selectedInvoiceId;
    
    if (!targetInvoiceId) {
        showAlertModal('خطأ: لم يتم تحديد فاتورة للعرض', 'error');
        return;
    }
    
    // Log debugging information
    console.log('🔍 Available invoices in local data:', invoicesData);
    console.log('🔍 Target invoice ID:', targetInvoiceId, 'Type:', typeof targetInvoiceId);
    
    // Try to find invoice in local data first for debugging
                const localInvoice = invoicesData.find(inv => 
                inv.id == targetInvoiceId || 
                inv.id === targetInvoiceId || 
                inv.id.toString() === targetInvoiceId.toString() ||
                inv.invoice_number === targetInvoiceId
            );
    console.log('🔍 Found local invoice:', localInvoice);
    
    // Fetch real invoice data from API
    console.log(`🔍 Fetching invoice details for ID: ${targetInvoiceId}`);
    const apiUrl = getApiUrl(`/billing/api/invoice-details/${targetInvoiceId}/`);
    console.log(`📡 API URL: ${apiUrl}`);
    
    fetch(apiUrl)
        .then(response => {
            console.log(`📊 API Response status: ${response.status}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Close loading message
            closeModal('alertModal');
            
            if (data.success && data.invoice) {
                const invoiceData = data.invoice;
                
                // Store the API data globally for other functions
                currentInvoiceData = invoiceData;
                
                // Update invoice status to completed if payment was processed
                if (invoiceData.status !== 'paid' && invoiceData.status !== 'completed') {
                    invoiceData.status = 'completed';
                    
                    // Update in the main invoices array as well (if it exists there)
                    const invoiceIndex = invoicesData.findIndex(inv => inv.id == targetInvoiceId);
                    if (invoiceIndex !== -1) {
                        invoicesData[invoiceIndex].status = 'completed';
                        // Refresh the table to show updated status
                        renderInvoicesTable();
                        updateInvoiceStatusCounts();
                    }
                }
                
                // Create printable invoice modal with real data
                createPrintableInvoiceModal(invoiceData);
                
                // Show the modal with stable positioning
                const modal = document.getElementById('printableInvoiceModal');
                if (modal) {
                    modal.classList.remove('hidden');
                    // Lock body scroll
                    document.body.style.overflow = 'hidden';
                } else {
                    console.error('Print modal not found');
                }
            } else {
                showAlertModal('خطأ: فشل في تحميل بيانات الفاتورة', 'error');
                console.error('API returned error:', data.error || 'Unknown error');
            }
        })
        .catch(error => {
            // Close loading message
            closeModal('alertModal');
            
            console.log('📄 API unavailable, using local demo data for invoice:', targetInvoiceId);
            console.log('🔍 Available local invoices:', invoicesData.map(inv => ({id: inv.id, number: inv.invoice_number})));
            
            // Fallback to local data if API fails
            let invoiceData = null;
            
            // Try to find in local invoicesData array using multiple matching strategies
            if (targetInvoiceId) {
                invoiceData = invoicesData.find(inv => 
                    inv.id == targetInvoiceId || 
                    inv.id === targetInvoiceId || 
                    inv.id.toString() === targetInvoiceId.toString() ||
                    inv.invoice_number === targetInvoiceId
                );
                
                // If not found, try using the first paid invoice as a demo
                if (!invoiceData && invoicesData.length > 0) {
                    invoiceData = invoicesData.find(inv => inv.status === 'paid' || inv.payment_status === 'paid') || invoicesData[0];
                    console.log('Using demo invoice since target not found:', invoiceData);
                }
            }
            
            // If still no invoice data and we have currentInvoiceData, use it
            if (!invoiceData && typeof currentInvoiceData !== 'undefined' && currentInvoiceData) {
                invoiceData = currentInvoiceData;
            }
            
            if (invoiceData) {
                // Enhance the invoice data with detailed operations and spare parts
                const enhancedInvoiceData = enhanceInvoiceDataForPrinting(invoiceData);
                console.log('📄 Enhanced invoice data:', enhancedInvoiceData);
                
                // Create printable invoice modal with enhanced fallback data
                createPrintableInvoiceModal(enhancedInvoiceData);
                
                // Show the modal with stable positioning
                const modal = document.getElementById('printableInvoiceModal');
                if (modal) {
                    modal.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                }
            } else {
                showAlertModal('خطأ: لا يمكن العثور على بيانات الفاتورة', 'error');
            }
        });
}

// Print invoice (actual printing function)
function printInvoice(invoiceId) {
    // Find the invoice data
    let invoiceData = null;
    
    // If invoiceId is provided, find the invoice in the invoicesData array
    if (invoiceId) {
        invoiceData = invoicesData.find(inv => inv.id === invoiceId);
    } 
    // If no invoiceId, try to get current invoice from payment modal
    else if (selectedInvoiceId) {
        invoiceData = invoicesData.find(inv => inv.id === selectedInvoiceId);
    }
    
    // If still no invoice data and we have currentInvoiceData, use it
    if (!invoiceData && typeof currentInvoiceData !== 'undefined' && currentInvoiceData) {
        invoiceData = currentInvoiceData;
    }
    
    if (!invoiceData) {
        showAlertModal('خطأ: لا يمكن العثور على الفاتورة للطباعة', 'error');
        return;
    }
    
    // Create printable invoice modal dynamically
    createPrintableInvoiceModal(invoiceData);
    
    // Ensure the modal is visible and properly structured
    const modal = document.getElementById('printableInvoiceModal');
    if (!modal) {
        showAlertModal('خطأ: لا يمكن العثور على الفاتورة للطباعة', 'error');
        return;
    }
    
    // Add print styles if not already present
    if (!document.getElementById('printStyles')) {
        const printStyles = document.createElement('style');
        printStyles.id = 'printStyles';
        printStyles.innerHTML = `
            @media print {
                @page {
                    size: A4;
                    margin: 0.4in;
                }
                
                /* Hide everything first */
                body * {
                    visibility: hidden;
                }
                
                /* Show only the modal and its content */
                #printableInvoiceModal, #printableInvoiceModal * {
                    visibility: visible !important;
                }
                
                /* Position the modal content for printing */
                #printableInvoiceModal {
                    position: static !important;
                    background: transparent !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    width: 100% !important;
                    height: auto !important;
                }
                
                #printableInvoiceModal > div {
                    position: static !important;
                    max-width: none !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    box-shadow: none !important;
                    border-radius: 0 !important;
                    max-height: none !important;
                    overflow: visible !important;
                }
                
                /* Hide modal chrome during print */
                .modal-header, .modal-footer {
                    display: none !important;
                    visibility: hidden !important;
                }
                
                /* Ensure only invoice content is visible */
                .modal-body {
                    padding: 0 !important;
                    margin: 0 !important;
                }
                
                /* Compact single-page fit */
                .print-invoice-container {
                    font-size: 8px !important;
                    line-height: 1.1 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    width: 100% !important;
                    height: auto !important;
                }
                
                h1, h2, h3, h4 {
                    margin: 3px 0 !important;
                    padding: 0 !important;
                    line-height: 1.1 !important;
                }
                
                h1 { font-size: 13px !important; }
                h2 { font-size: 11px !important; }
                h3 { font-size: 10px !important; }
                h4 { font-size: 9px !important; }
                
                .grid, .grid > div {
                    gap: 2px !important;
                    margin: 3px 0 !important;
                    padding: 2px !important;
                }
                
                .border {
                    border-width: 1px !important;
                    border-radius: 2px !important;
                    padding: 3px !important;
                    margin: 2px 0 !important;
                }
                
                .services-section {
                    margin: 2px 0 !important;
                    padding: 2px !important;
                }
                
                .services-section table {
                    margin: 1px 0 !important;
                }
                
                .services-section h4 {
                    margin: 1px 0 !important;
                }
                
                .services-section div {
                    margin: 1px 0 !important;
                }
                
                table {
                    font-size: 7px !important;
                    line-height: 1.0 !important;
                    margin: 3px 0 !important;
                }
                
                th, td {
                    padding: 2px 3px !important;
                    font-size: 7px !important;
                    line-height: 1.0 !important;
                }
                
                .text-xs {
                    font-size: 7px !important;
                }
                
                .invoice-footer, .border-t {
                    margin-top: 5px !important;
                    padding-top: 3px !important;
                    font-size: 7px !important;
                }
                
                /* Compact margins */
                .mb-1, .mb-2 {
                    margin-bottom: 3px !important;
                }
                
                /* Single page optimization */
                .print-invoice-container > div {
                    margin: 2px 0 !important;
                }
                
                /* Avoid page breaks */
                .services-section, .border {
                    page-break-inside: avoid !important;
                }
                
                /* Remove any extra space after spare parts total */
                .services-section tbody tr:last-child {
                    margin-bottom: 0 !important;
                }
                
                .services-section table tbody {
                    margin-bottom: 0 !important;
                }
                
                /* Ensure tight spacing in services section */
                .mb-0 {
                    margin-bottom: 0 !important;
                }
                
                .services-section .mb-1 {
                    margin-bottom: 1px !important;
                }
                
                /* Remove gap between services section and financial summary */
                .services-section + div {
                    margin-top: -2px !important;
                }
                
                /* Ensure financial summary has negative top margin */
                .border[style*="margin-top: -2px"] {
                    margin-top: -2px !important;
                }
                
                /* Tight spacing for financial section */
                .grid.grid-cols-4 {
                    gap: 1px !important;
                    margin: 1px 0 !important;
                }
                
                /* Remove any potential gaps between sections */
                .mb-1 + div {
                    margin-top: -1px !important;
                }
                
                /* Ensure the modal container takes full width for print */
                .modal-container {
                    max-width: none !important;
                    width: 100% !important;
                    height: auto !important;
                    transform: none !important;
                    top: 0 !important;
                    left: 0 !important;
                    position: static !important;
                }
                
                .print\\:hidden {
                    display: none !important;
                }
                
                .print\\:block {
                    display: block !important;
                }
                
                .print\\:bg-transparent {
                    background-color: transparent !important;
                }
                
                .print\\:border {
                    border: 1px solid #000 !important;
                }
                
                .print\\:border-gray-300 {
                    border-color: #d1d5db !important;
                }
                
                .print\\:border-blue-300 {
                    border-color: #93c5fd !important;
                }
                
                .print\\:border-cyan-300 {
                    border-color: #67e8f9 !important;
                }
                
                .print\\:border-teal-300 {
                    border-color: #5eead4 !important;
                }
                
                .print\\:border-yellow-300 {
                    border-color: #fde047 !important;
                }
                
                .print\\:bg-gray-200 {
                    background-color: #e5e7eb !important;
                }
                
                .print\\:mb-4 {
                    margin-bottom: 1rem !important;
                }
                
                .print\\:mt-6 {
                    margin-top: 1.5rem !important;
                }
                
                .print\\:p-4 {
                    padding: 1rem !important;
                }
                
                .print\\:max-h-none {
                    max-height: none !important;
                }
                
                .print\\:shadow-none {
                    box-shadow: none !important;
                }
                
                .print\\:rounded-none {
                    border-radius: 0 !important;
                }
                
                /* Ensure all text is visible and printable */
                * {
                    color: #000 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                /* Ensure tables print properly */
                table {
                    border-collapse: collapse !important;
                    page-break-inside: avoid;
                    width: 100% !important;
                }
                
                th, td {
                    border: 1px solid #000 !important;
                    padding: 4px 8px !important;
                    color: #000 !important;
                    background-color: transparent !important;
                }
                
                /* Ensure all text content is visible */
                span, div, p, h1, h2, h3, h4, h5, h6, time {
                    color: #000 !important;
                    background-color: transparent !important;
                }
                
                /* Make sure currency and numeric values are visible */
                .font-medium, .font-bold, .text-green-600, .text-red-600, .text-blue-600 {
                    color: #000 !important;
                    font-weight: bold !important;
                }
                
                /* Avoid page breaks in important sections */
                .bg-gray-50, .bg-blue-50, .bg-cyan-50, .bg-teal-50, .bg-yellow-50 {
                    page-break-inside: avoid;
                    background-color: transparent !important;
                    border: 1px solid #ccc !important;
                }
                
                /* Ensure dates and times are visible */
                .date-info, time {
                    color: #000 !important;
                    font-weight: normal !important;
                }
                
                /* Force visibility for all content */
                #printableInvoiceModal * {
                    opacity: 1 !important;
                    visibility: visible !important;
                }
                
                /* Ensure proper layout for printing */
                .grid {
                    display: block !important;
                }
                
                .grid > div {
                    margin-bottom: 1rem !important;
                    page-break-inside: avoid;
                }
                
                /* Fix table layout */
                .overflow-x-auto {
                    overflow: visible !important;
                }
                
                /* Ensure all sections are visible */
                .mb-6, .mb-4, .mb-3 {
                    margin-bottom: 1rem !important;
                }
                
                /* Print header styling */
                .print\\:block {
                    display: block !important;
                    text-align: center !important;
                    margin-bottom: 2rem !important;
                }
                
                /* Hide screen-only elements */
                .print\\:hidden {
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(printStyles);
    }
    
    // Debug: Log what content is available
    console.log('🖨️ Print Debug Info:');
    console.log('Modal found:', !!modal);
    console.log('Modal content:', modal ? modal.innerHTML.substring(0, 200) + '...' : 'None');
    
    // Alternative approach: Create a dedicated print window
    const printContent = modal.querySelector('div').innerHTML;
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة للطباعة</title>
                         <style>
                 @page {
                     size: A4;
                     margin: 0.4in;
                 }
                
                                 body {
                     font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                     direction: rtl;
                     text-align: right;
                     margin: 0;
                     padding: 12px;
                     color: #000;
                     background: white;
                     font-size: 14px;
                     line-height: 1.4;
                 }
                
                * {
                    color: #000 !important;
                    background-color: transparent !important;
                }
                
                 /* Maintain the exact modal structure */
                 .print-invoice-container {
                     max-width: none;
                     margin: 0;
                     padding: 0;
                 }
                
                 /* Company Header - Ultra Compact */
                 .company-header {
                     margin-bottom: 0.2rem;
                 }
                 
                 .company-header h1 {
                     font-size: 12px;
                     font-weight: bold;
                     margin-bottom: 0.1rem;
                     text-align: center;
                 }
                 
                 .company-header p {
                     font-size: 7px;
                     color: #666;
                     text-align: center;
                     margin-bottom: 0.1rem;
                 }
                 
                 .border-b-2 {
                     border-bottom: 1px solid #d1d5db;
                     margin-top: 0.1rem;
                     margin-bottom: 0.2rem;
                 }
                
                 /* Invoice Header - Ultra Compact */
                 .invoice-header {
                     margin-bottom: 0.2rem;
                 }
                 
                 .invoice-header h2 {
                     font-size: 11px;
                     font-weight: bold;
                     color: #1d4ed8;
                     margin-bottom: 0.1rem;
                 }
                 
                 /* Grid Layout - Ultra Compact */
                 .grid {
                     display: flex;
                     flex-wrap: wrap;
                     gap: 0.2rem;
                 }
                 
                 .grid-cols-3 > div:first-child {
                     flex: 2;
                 }
                 
                 .grid-cols-3 > div:last-child {
                     flex: 1;
                 }
                 
                 .grid-cols-2 > div {
                     flex: 1;
                     min-width: 0;
                 }
                 
                 /* Blue total amount box - Ultra Compact */
                 .bg-blue-50 {
                     background-color: #eff6ff !important;
                     padding: 0.2rem;
                     border-radius: 0.15rem;
                     border: 1px solid #93c5fd;
                     text-align: right;
                 }
                 
                 .bg-blue-50 .text-lg {
                     font-size: 9px;
                     font-weight: bold;
                     color: #1e40af;
                 }
                 
                 .bg-blue-50 .text-2xl {
                     font-size: 12px;
                     font-weight: bold;
                     color: #059669;
                 }
                
                                 /* Info sections - Ultra Compact */
                .customer-info, .service-info, .operations-section, .parts-section {
                    border: 1px solid #d1d5db;
                    border-radius: 0.15rem;
                    padding: 0.2rem;
                    margin-bottom: 0.2rem;
                }
                
                .customer-info h3, .service-info h3 {
                    font-weight: bold;
                    color: #1d4ed8;
                    margin-bottom: 0.1rem;
                    padding-bottom: 0.05rem;
                    border-bottom: 1px solid #e5e7eb;
                    font-size: 9px;
                }
                
                .service-info h3 {
                    color: #059669;
                }
                
                /* Operations and Parts sections - Ultra Compact */
                .operations-section {
                    background-color: #fff7ed !important;
                    border-color: #fdba74 !important;
                }
                
                .operations-section h3 {
                    color: #9a3412 !important;
                    font-weight: bold;
                    margin-bottom: 0.1rem;
                    padding-bottom: 0.05rem;
                    border-bottom: 1px solid #fed7aa;
                    font-size: 9px;
                }
                
                .parts-section {
                    background-color: #faf5ff !important;
                    border-color: #c084fc !important;
                }
                
                .parts-section h3 {
                    color: #6b21a8 !important;
                    font-weight: bold;
                    margin-bottom: 0.1rem;
                    padding-bottom: 0.05rem;
                    border-bottom: 1px solid #ddd6fe;
                    font-size: 9px;
                }
                
                /* Table styles - Ultra Compact */
                                 table {
                     width: 100%;
                     border-collapse: collapse;
                    margin-top: 0.1rem;
                    font-size: 7px;
                }
                
                table th, table td {
                    border: 1px solid #d1d5db;
                    padding: 0.1rem;
                    text-align: right;
                }
                
                table th {
                    background-color: #f3f4f6 !important;
                    font-weight: bold;
                    font-size: 7px;
                }
                
                .operations-section table th {
                    background-color: #fed7aa !important;
                }
                
                .parts-section table th {
                    background-color: #ddd6fe !important;
                }
                
                table tbody tr:nth-child(even) {
                    background-color: #f9fafb !important;
                }
                
                .operations-section tbody tr:last-child {
                    background-color: #fff7ed !important;
                }
                
                .parts-section tbody tr:last-child {
                    background-color: #faf5ff !important;
                }
                
                .text-center { text-align: center !important; }
                
                .overflow-x-auto {
                    overflow-x: visible;
                }
                 
                 /* Financial Summary - Ultra Compact */
                 .financial-summary {
                     border: 1px solid #d1d5db;
                     border-radius: 0.15rem;
                     padding: 0.2rem;
                     margin-bottom: 0.2rem;
                 }
                 
                 .financial-summary h3 {
                     font-weight: bold;
                     color: #92400e;
                     margin-bottom: 0.1rem;
                     padding-bottom: 0.05rem;
                     border-bottom: 1px solid #e5e7eb;
                     font-size: 9px;
                 }
                 
                 /* Payment Info - Compact */
                 .payment-info {
                     border: 1px solid #6ee7b7;
                     border-radius: 0.25rem;
                     padding: 0.4rem;
                     margin-bottom: 0.4rem;
                     background-color: #ecfdf5 !important;
                 }
                 
                 .payment-info h3 {
                     font-weight: bold;
                     color: #065f46;
                     margin-bottom: 0.5rem;
                     padding-bottom: 0.25rem;
                     border-bottom: 1px solid #a7f3d0;
                     font-size: 16px;
                 }
                
                 /* Space utilities - Match Modal */
                 .space-y-1 > * + * { margin-top: 0.25rem; }
                 .space-y-2 > * + * { margin-top: 0.5rem; }
                 
                 /* Flex utilities - Match Modal */
                 .flex {
                     display: flex;
                 }
                 
                 .justify-between {
                     justify-content: space-between;
                 }
                 
                 .items-center {
                     align-items: center;
                 }
                 
                 /* Text utilities - Match Modal */
                 .text-center { text-align: center; }
                 .text-right { text-align: right; }
                 .font-bold { font-weight: bold; }
                 .font-medium { font-weight: 600; }
                                 .text-sm { font-size: 7px; }
                .text-xs { font-size: 6px; }
                .text-lg { font-size: 9px; }
                .text-xl { font-size: 10px; }
                .text-2xl { font-size: 11px; }
                
                 /* Margin utilities - Match Modal */
                                 .mb-1 { margin-bottom: 0.05rem; }
                .mb-2 { margin-bottom: 0.1rem; }
                .mb-3 { margin-bottom: 0.15rem; }
                .mb-4 { margin-bottom: 0.2rem; }
                .mb-6 { margin-bottom: 0.3rem; }
                .mt-2 { margin-top: 0.1rem; }
                .mt-6 { margin-top: 0.3rem; }
                 
                 /* Padding utilities - Match Modal */
                 .p-3 { padding: 0.75rem; }
                 .p-4 { padding: 1rem; }
                 .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
                 .pb-1 { padding-bottom: 0.25rem; }
                 .pt-3 { padding-top: 0.75rem; }
                 
                 /* Border utilities - Match Modal */
                 .border { border: 1px solid #d1d5db; }
                 .border-b { border-bottom: 1px solid #e5e7eb; }
                 .border-t-2 { border-top: 2px solid #d1d5db; }
                 .border-b-2 { border-bottom: 2px solid #000; }
                 .border-gray-200 { border-color: #e5e7eb; }
                 
                 /* Color utilities - Match Modal */
                 .text-blue-700 { color: #1d4ed8 !important; }
                 .text-green-600 { color: #059669 !important; }
                 .text-red-600 { color: #dc2626 !important; }
                 .text-gray-600 { color: #4b5563 !important; }
                 .text-gray-500 { color: #6b7280 !important; }
                 .text-purple-600 { color: #9333ea !important; }
                 .text-orange-600 { color: #ea580c !important; }
                 .text-indigo-600 { color: #4f46e5 !important; }
                 .text-yellow-800 { color: #92400e !important; }
                 .text-green-800 { color: #166534 !important; }
                 
                 /* Icons - Match Modal */
                 .fas, .fa {
                     margin-left: 0.25rem;
                     width: 1em;
                 }
                
                                 /* Footer - Ultra Compact */
                .invoice-footer {
                    margin-top: 0.2rem;
                    padding-top: 0.1rem;
                    border-top: 1px solid #d1d5db;
                }
                
                .invoice-footer .text-xs {
                    font-size: 6px;
                    color: #6b7280 !important;
                }
                
                 /* Hide print-only elements */
                 .print\\:hidden { display: none; }
                 .print\\:block { display: block; }
                 
                 /* Ensure proper page breaks */
                 .customer-info, .service-info, .financial-summary, .payment-info {
                     page-break-inside: avoid;
                 }
            </style>
        </head>
        <body>
            ${modalBody.innerHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    
    // Wait for content to load then print
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    };
}

// Invoice filtering and table management functions
let invoicesData = [];
let filteredInvoicesData = [];
let currentStatusFilter = '';

// Initialize invoice filters
function initializeInvoiceFilters() {
    // Setup filter event listeners
    document.getElementById('invoiceStatusFilter').addEventListener('change', applyInvoiceFilters);
    document.getElementById('invoicePaymentFilter').addEventListener('change', applyInvoiceFilters);
    document.getElementById('invoiceStartDateFilter').addEventListener('change', applyInvoiceFilters);
    document.getElementById('invoiceEndDateFilter').addEventListener('change', applyInvoiceFilters);
    document.getElementById('invoiceSearchFilter').addEventListener('input', debounce(applyInvoiceFilters, 300));
}

// Filter invoices by status (tab buttons)
function filterInvoicesByStatus(status) {
    console.log('🔍 Filtering by status:', status);
    
    // Save active tab to localStorage
    localStorage.setItem('activeInvoiceTab', status);
    
    // Update active tab
    document.querySelectorAll('.status-tab').forEach(tab => {
        tab.classList.remove('active');
        tab.style.background = 'transparent';
        tab.style.color = '#6b7280';
        tab.style.boxShadow = 'none';
    });
    
    const activeTab = document.querySelector(`[data-status="${status}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
        activeTab.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
        activeTab.style.color = 'white';
        activeTab.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.4)';
    }
    
    currentStatusFilter = status;
    
    // Apply filter directly instead of using form filter
    if (status === '') {
        // Show all invoices
        filteredInvoicesData = [...invoicesData];
    } else {
        filteredInvoicesData = invoicesData.filter(invoice => {
            let matches = false;
            
            switch (status) {
                case 'unpaid':
                    matches = (
                        invoice.status === 'issued' || 
                        invoice.payment_status === 'unpaid' || 
                        (invoice.status !== 'paid' && invoice.status !== 'completed' && invoice.status !== 'overdue' && invoice.status !== 'late' && invoice.payment_status !== 'full')
                    );
                    break;
                case 'paid':
                    matches = (invoice.status === 'paid' || invoice.status === 'completed' || invoice.payment_status === 'full');
                    break;
                case 'late':
                    matches = (invoice.status === 'overdue' || invoice.status === 'late');
                    break;
                default:
                    matches = (invoice.status === status);
                    break;
            }
            
            return matches;
        });
    }
    
    console.log('🔍 Filtered results:', filteredInvoicesData.length, 'invoices');
    renderInvoicesTable();
}

// Apply all filters
function applyInvoiceFilters() {
    const statusFilter = document.getElementById('invoiceStatusFilter').value;
    const paymentFilter = document.getElementById('invoicePaymentFilter').value;
    const startDate = document.getElementById('invoiceStartDateFilter').value;
    const endDate = document.getElementById('invoiceEndDateFilter').value;
    const searchFilter = document.getElementById('invoiceSearchFilter').value.toLowerCase();
    
    filteredInvoicesData = invoicesData.filter(invoice => {
        // Status filter with new mappings
        if (statusFilter) {
            let matches = false;
            
            switch (statusFilter) {
                case 'unpaid':
                    matches = (
                        invoice.status === 'issued' || 
                        invoice.payment_status === 'unpaid' || 
                        (invoice.status !== 'paid' && invoice.status !== 'completed' && invoice.status !== 'overdue' && invoice.status !== 'late' && invoice.payment_status !== 'full')
                    );
                    break;
                case 'paid':
                    matches = (invoice.status === 'paid' || invoice.status === 'completed' || invoice.payment_status === 'full');
                    break;
                case 'late':
                    matches = (invoice.status === 'overdue' || invoice.status === 'late');
                    break;
                default:
                    matches = (invoice.status === statusFilter);
                    break;
            }
            
            if (!matches) return false;
        }
        
        // Payment status filter
        if (paymentFilter && invoice.payment_status !== paymentFilter) return false;
        
        // Date range filter
        if (startDate && new Date(invoice.date) < new Date(startDate)) return false;
        if (endDate && new Date(invoice.date) > new Date(endDate)) return false;
        
        // Search filter
        if (searchFilter) {
            const searchText = `${invoice.invoice_number} ${invoice.customer_name} ${invoice.work_order_number || ''}`.toLowerCase();
            if (!searchText.includes(searchFilter)) return false;
        }
        
        return true;
    });
    
    renderInvoicesTable();
    updateInvoiceStatusCounts();
}

// Render invoices table
function renderInvoicesTable() {
    const tbody = document.getElementById('invoicesTableBody');
    
    if (filteredInvoicesData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-12">
                    <i class="fas fa-file-invoice text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد فواتير</h3>
                    <p class="text-gray-500 mb-4">لم يتم العثور على فواتير مطابقة للمعايير المحددة</p>
                    <button onclick="openQuickCreateModal()" class="invoice-action-btn btn-primary">
                        <i class="fas fa-plus ml-2"></i>
                        إنشاء فاتورة جديدة
                    </button>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = filteredInvoicesData.map(invoice => `
        <tr class="table-row" data-status="${invoice.status}" data-payment-status="${invoice.payment_status}">
            <td>
                <span class="invoice-number">${invoice.invoice_number}</span>
            </td>
            <td>
                <span class="customer-name">${invoice.customer_name || 'غير محدد'}</span>
            </td>
            <td>
                <span class="invoice-amount">${invoice.total_amount} ج.م</span>
            </td>
            <td>
                <span class="status-badge status-${invoice.status}">
                    ${getInvoiceStatusText(invoice.status)}
                </span>
            </td>
            <td>
                <div class="date-info">
                    ${formatDate(invoice.date)}
                    <small>${formatTime(invoice.date)}</small>
                </div>
            </td>
            <td>
                <div class="flex flex-wrap gap-1">
                    <a href="#" onclick="viewInvoice('${invoice.id}')" class="invoice-action-btn btn-primary text-xs">
                        <i class="fas fa-eye ml-1"></i>
                        عرض
                    </a>
                    ${invoice.status !== 'paid' && invoice.payment_status !== 'full' ? `
                        <button onclick="processPayment('${invoice.id}')" class="invoice-action-btn btn-success text-xs">
                            <i class="fas fa-credit-card ml-1"></i>
                            دفع
                        </button>
                    ` : ''}
                    ${invoice.status === 'paid' || invoice.payment_status === 'full' ? `
                        <button onclick="showPrintableInvoiceModal('${invoice.id}')" class="invoice-action-btn btn-warning text-xs">
                            <i class="fas fa-print ml-1"></i>
                            طباعة
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// Get invoice status text in Arabic
function getInvoiceStatusText(status) {
    const statusTexts = {
        'issued': 'غير مدفوعة',
        'paid': 'مدفوعة',
        'completed': 'مكتملة',
        'overdue': 'متأخرة',
        'late': 'متأخرة',
        'cancelled': 'ملغية',
        'partial': 'مدفوعة جزئياً',
        'unpaid': 'غير مدفوعة'
    };
    return statusTexts[status] || 'غير مدفوعة';
}

// Update status counts
function updateInvoiceStatusCounts() {
    console.log('📊 Updating status counts for invoices:', invoicesData);
    
    const unpaidInvoices = invoicesData.filter(inv => 
        inv.status === 'issued' || 
        inv.payment_status === 'unpaid' || 
        (inv.status !== 'paid' && inv.status !== 'completed' && inv.status !== 'overdue' && inv.status !== 'late' && inv.payment_status !== 'full')
    );
    
    const paidInvoices = invoicesData.filter(inv => 
        inv.status === 'paid' || 
        inv.status === 'completed' || 
        inv.payment_status === 'full'
    );
    
    const lateInvoices = invoicesData.filter(inv => 
        inv.status === 'overdue' || 
        inv.status === 'late'
    );
    
    const counts = {
        all: invoicesData.length,
        unpaid: unpaidInvoices.length,
        paid: paidInvoices.length,
        late: lateInvoices.length
    };
    
    console.log('📊 Status counts:', counts);
    console.log('📊 Unpaid invoices:', unpaidInvoices.map(inv => `${inv.invoice_number} (${inv.status}/${inv.payment_status})`));
    console.log('📊 Paid invoices:', paidInvoices.map(inv => `${inv.invoice_number} (${inv.status}/${inv.payment_status})`));
    console.log('📊 Late invoices:', lateInvoices.map(inv => `${inv.invoice_number} (${inv.status}/${inv.payment_status})`));
    
    document.getElementById('count-all').textContent = counts.all;
    document.getElementById('count-unpaid').textContent = counts.unpaid;
    document.getElementById('count-paid').textContent = counts.paid;
    document.getElementById('count-late').textContent = counts.late;
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
}

// Format time for display
function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-EG', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false
    });
}

// View invoice details
function viewInvoice(invoiceId) {
    // Handle both string and number IDs
    const invoice = invoicesData.find(inv => inv.id == invoiceId || inv.id === String(invoiceId) || inv.id === Number(invoiceId));
    if (!invoice) {
        console.error('Invoice not found:', invoiceId, 'Available invoices:', invoicesData.map(inv => inv.id));
        showAlertModal('لم يتم العثور على الفاتورة', 'error');
        return;
    }
    
    // Create and show invoice details modal
    showInvoiceDetailsModal(invoice);
}

// Process payment for invoice
function processPayment(invoiceId) {
    // Handle both string and number IDs
    const invoice = invoicesData.find(inv => inv.id == invoiceId || inv.id === String(invoiceId) || inv.id === Number(invoiceId));
    if (!invoice) {
        console.error('Invoice not found:', invoiceId, 'Available invoices:', invoicesData.map(inv => inv.id));
        showAlertModal('لم يتم العثور على الفاتورة', 'error');
        return;
    }
    
    if (invoice.status === 'paid' || invoice.payment_status === 'full') {
        showAlertModal('هذه الفاتورة مدفوعة بالفعل', 'info');
        return;
    }
    
    // Open the existing quick payment modal and populate it with invoice data
    openQuickPaymentModalForInvoice(invoice);
}

// Show invoice details modal
function showInvoiceDetailsModal(invoice) {
    const modal = document.createElement('div');
    modal.id = 'invoiceDetailsModal';
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-container max-w-4xl">
            <div class="modal-header">
                <h3>تفاصيل الفاتورة - ${invoice.invoice_number}</h3>
                <button type="button" onclick="closeModal('invoiceDetailsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Invoice Information -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold mb-3 text-blue-700">
                            <i class="fas fa-file-invoice mr-2"></i>معلومات الفاتورة
                        </h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">
                                    <i class="fas fa-hashtag mr-2 text-blue-500"></i>رقم الفاتورة:
                                </span>
                                <span class="font-medium" style="font-family: monospace;">${invoice.invoice_number}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">
                                    <i class="fas fa-calendar-alt mr-2 text-green-500"></i>تاريخ الإصدار:
                                </span>
                                <span class="font-medium">${formatDate(invoice.date)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">
                                    <i class="fas fa-info-circle mr-2 text-purple-500"></i>الحالة:
                                </span>
                                <span class="status-badge status-${invoice.status}">${getInvoiceStatusText(invoice.status)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">
                                    <i class="fas fa-credit-card mr-2 text-orange-500"></i>حالة الدفع:
                                </span>
                                <span class="font-medium">${getPaymentStatusText(invoice.payment_status)}</span>
                            </div>
                            ${invoice.work_order_number ? `
                                <div class="flex justify-between">
                                    <span class="text-gray-600">
                                        <i class="fas fa-clipboard-list mr-2 text-indigo-500"></i>رقم أمر العمل:
                                    </span>
                                    <span class="font-medium" style="font-family: monospace;">${invoice.work_order_number}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    <!-- Customer Information -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold mb-3 text-green-700">
                            <i class="fas fa-user mr-2"></i>معلومات العميل
                        </h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">
                                    <i class="fas fa-user-circle mr-2 text-blue-500"></i>اسم العميل:
                                </span>
                                <span class="font-medium">${invoice.customer_name || 'غير محدد'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">
                                    <i class="fas fa-phone mr-2 text-green-500"></i>رقم الهاتف:
                                </span>
                                <span class="font-medium" style="direction: ltr; font-family: monospace;">${invoice.customer_phone || 'غير محدد'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">
                                    <i class="fas fa-envelope mr-2 text-purple-500"></i>البريد الإلكتروني:
                                </span>
                                <span class="font-medium" style="direction: ltr;">${invoice.customer_email || 'غير محدد'}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Financial Summary -->
                <div class="mt-6 bg-yellow-50 rounded-lg p-4">
                    <h4 class="font-semibold mb-3 text-yellow-800">
                        <i class="fas fa-calculator mr-2"></i>الملخص المالي
                    </h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">${invoice.subtotal || invoice.total_amount} ج.م</div>
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-list-alt mr-1"></i>المبلغ الفرعي
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">${invoice.tax_amount || 0} ج.م</div>
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-percent mr-1"></i>الضريبة
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-red-600">${invoice.discount_amount || 0} ج.م</div>
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-tags mr-1"></i>الخصم
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">${invoice.total_amount} ج.م</div>
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-money-bill-wave mr-1"></i>المبلغ الإجمالي
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Information -->
                ${invoice.payment_status !== 'unpaid' ? `
                    <div class="mt-6 bg-green-50 rounded-lg p-4">
                        <h4 class="font-semibold mb-3 text-green-800">
                            <i class="fas fa-credit-card mr-2"></i>معلومات الدفع
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-green-600">${invoice.paid_amount || invoice.total_amount} ج.م</div>
                                <div class="text-sm text-gray-600">
                                    <i class="fas fa-check-circle mr-1"></i>المبلغ المدفوع
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-orange-600">${invoice.remaining_amount || 0} ج.م</div>
                                <div class="text-sm text-gray-600">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>المبلغ المتبقي
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm font-medium text-gray-700">
                                    <i class="fas fa-money-bill mr-1"></i>طريقة الدفع
                                </div>
                                <div class="text-lg font-bold text-blue-600">${invoice.payment_method || 'نقدي'}</div>
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
            
            <div class="modal-footer">
                ${invoice.status !== 'paid' ? `
                    <button type="button" onclick="processPayment('${invoice.id}'); closeModal('invoiceDetailsModal');" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        <i class="fas fa-credit-card mr-2"></i>معالجة الدفع
                    </button>
                ` : ''}
                <button type="button" onclick="showPrintableInvoiceModal('${invoice.id}'); closeModal('invoiceDetailsModal');" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    <i class="fas fa-print mr-2"></i>طباعة
                </button>
                <button type="button" onclick="closeModal('invoiceDetailsModal')" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                    إغلاق
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    modal.classList.remove('hidden');
}

// Open quick payment modal for specific invoice
function openQuickPaymentModalForInvoice(invoice) {
    const modal = document.getElementById('quickPaymentModal');
    if (!modal) {
        showAlertModal('نافذة الدفع غير متاحة', 'error');
        return;
    }
    
    // Clear previous selections first
    clearSelectedInvoice();
    
    // Set global variables
    selectedInvoiceId = invoice.id;
    currentInvoiceId = invoice.id;
    
    // Create comprehensive invoice data structure that matches API format
    const fullInvoiceData = {
        id: invoice.id,
        invoice_number: invoice.invoice_number,
        customer_name: invoice.customer_name,
        customer_mobile: invoice.customer_phone,
        customer_email: invoice.customer_email,
        work_order_number: invoice.work_order_number,
        vehicle_info: `معلومات المركبة - ${invoice.work_order_number}`,
        invoice_date: invoice.date,
        created_at: invoice.date,
        subtotal: invoice.subtotal || invoice.total_amount,
        tax_amount: invoice.tax_amount || 0,
        discount_amount: invoice.discount_amount || 0,
        insurance_amount: 0,
        warranty_amount: 0,
        total_amount: invoice.total_amount,
        amount_due: invoice.remaining_amount || invoice.total_amount,
        status: invoice.status,
        payment_status: invoice.payment_status,
        operations: [
            {
                id: 1,
                description: 'صيانة وإصلاح',
                duration: 'حسب الحاجة',
                cost: (invoice.subtotal || invoice.total_amount) * 0.4,
                warranty_covered: true
            }
        ],
        items: [
            {
                id: 1,
                description: 'قطع غيار وخدمات',
                quantity: 1,
                unit_price: (invoice.subtotal || invoice.total_amount) * 0.6,
                total: (invoice.subtotal || invoice.total_amount) * 0.6,
                item_type: 'part',
                is_covered_by_warranty: true,
                is_covered_by_insurance: false
            }
        ],
        payments: invoice.payment_status === 'partial' && invoice.paid_amount ? [
            {
                id: 1,
                amount: invoice.paid_amount,
                payment_method: invoice.payment_method || 'نقدي',
                payment_date: invoice.date
            }
        ] : []
    };
    
    // Store the data globally
    currentInvoiceData = fullInvoiceData;
    
    // Show the modal first
    modal.classList.remove('hidden');
    
    // Populate invoice details using the existing function
    populateInvoiceDetails(fullInvoiceData);
    
    // Show selected invoice display
    const selectedDisplay = document.getElementById('selectedInvoiceDisplay');
    const selectedInfo = document.getElementById('selectedInvoiceInfo');
    if (selectedDisplay && selectedInfo) {
        selectedInfo.innerHTML = `
            <div class="font-medium">${invoice.invoice_number}</div>
            <div class="text-sm">${invoice.customer_name}</div>
            ${invoice.customer_phone ? `<div class="text-sm"><i class="fas fa-phone mr-1"></i>${invoice.customer_phone}</div>` : ''}
            <div class="text-sm font-bold text-green-600">${parseFloat(invoice.total_amount || 0).toFixed(2)} ج.م</div>
        `;
        selectedDisplay.classList.remove('hidden');
    }
    
    // Populate the search input for reference
    const searchInput = document.getElementById('invoiceSearchInput');
    if (searchInput) {
        searchInput.value = invoice.invoice_number;
    }
    
    // Show all relevant sections
    showInvoiceSections();
    
    // Initialize payment methods
    initializePaymentMethods();
    
    // Auto-detect available offers
    setTimeout(() => {
        if (typeof detectAvailableOffers === 'function') {
            detectAvailableOffers(true);
        }
    }, 500);
}

// Get payment status text in Arabic
function getPaymentStatusText(paymentStatus) {
    const statusTexts = {
        'unpaid': 'غير مدفوعة',
        'partial': 'مدفوعة جزئياً',
        'full': 'مدفوعة كاملة',
        'paid': 'مدفوعة'
    };
    return statusTexts[paymentStatus] || paymentStatus;
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Load invoices from API with current state
async function loadInvoicesFromAPI() {
    console.log('🔄 Loading invoices from API...');
    
    try {
        // Load all invoices from batch API endpoint
        const response = await fetch(getApiUrl('/billing/api/all-invoices/'));
        
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.invoices && data.invoices.length > 0) {
                // Map API response to frontend format
                invoicesData = data.invoices.map(invoice => {
                    const amountPaid = parseFloat(invoice.paid_amount || 0);
                    const totalAmount = parseFloat(invoice.total_amount);
                    
                    return {
                        id: invoice.id,
                        invoice_number: invoice.invoice_number,
                        customer_name: invoice.customer_name,
                        customer_phone: invoice.customer_phone || '',
                        customer_email: invoice.customer_email || '',
                        total_amount: totalAmount,
                        subtotal: parseFloat(invoice.subtotal || totalAmount * 0.88),
                        tax_amount: parseFloat(invoice.tax_amount || totalAmount * 0.12),
                        discount_amount: parseFloat(invoice.discount_amount || 0),
                        status: invoice.status,
                        payment_status: getPaymentStatusFromAPI(invoice.status, amountPaid, totalAmount),
                        date: invoice.invoice_date,
                        work_order_number: invoice.work_order_number || '',
                        payment_method: invoice.last_payment_method || null,
                        paid_amount: amountPaid,
                        remaining_amount: Math.max(0, totalAmount - amountPaid)
                    };
                });
                console.log(`✅ Loaded ${invoicesData.length} invoices from API`);
            } else {
                console.log('📋 No invoices found, using empty array');
                invoicesData = [];
            }
                 } else {
            console.log('⚠️ API call failed, loading empty list');
            invoicesData = [];
        }        
    } catch (error) {
        console.error('❌ Error loading from API:', error);
        invoicesData = [];
    }
}

// Convert API status to payment status
function getPaymentStatusFromAPI(status, amountPaid, totalAmount) {
    const paid = parseFloat(amountPaid || 0);
    const total = parseFloat(totalAmount || 0);
    
    if (status === 'paid' || paid >= total) {
        return 'full';
    } else if (paid > 0) {
        return 'partial';
    } else {
        return 'unpaid';
    }
}

// Note: No more hardcoded demo data - system uses real database data only

// Enhanced refreshInvoices function
function refreshInvoices() {
    // Show loading state
    const tbody = document.getElementById('invoicesTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="6" class="text-center py-8">
                <div class="spinner-border text-blue-600 mb-3" role="status">
                    <span class="sr-only">جاري التحديث...</span>
                </div>
                <p class="text-gray-600">جاري تحديث البيانات...</p>
            </td>
        </tr>
    `;
    
         // Load current invoice data from API to get updated state
     loadInvoicesFromAPI().then(() => {
         filteredInvoicesData = [...invoicesData];
         updateInvoiceStatusCounts();
         
         // Restore saved tab state after refresh
         restoreSavedTabState();
     }).catch(error => {
         console.error('Error loading invoices:', error);
         // Load empty list on error
         invoicesData = [];
         filteredInvoicesData = [...invoicesData];
         updateInvoiceStatusCounts();
         
         // Restore saved tab state even with empty data
         restoreSavedTabState();
     });
}

// Compatibility function for showNotification
function showNotification(message, type = 'info', duration = 5000) {
    showAlertModal(message, type);
}

// Restore saved tab state
function restoreSavedTabState() {
    const savedTab = localStorage.getItem('activeInvoiceTab');
    if (savedTab !== null) {
        console.log('🔄 Restoring saved tab:', savedTab);
        filterInvoicesByStatus(savedTab);
    } else {
        // Default to showing all invoices
        filterInvoicesByStatus('');
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeInvoiceFilters();
    
    // Load invoices from API on page load
    console.log('🔄 Initializing dashboard with current data...');
    loadInvoicesFromAPI().then(() => {
        filteredInvoicesData = [...invoicesData];
        updateInvoiceStatusCounts();
        
        // Restore saved tab state after data is loaded
        restoreSavedTabState();
        
        console.log('✅ Dashboard initialized successfully');
    }).catch(error => {
        console.error('❌ Error initializing dashboard:', error);
        invoicesData = [];
        filteredInvoicesData = [...invoicesData];
        updateInvoiceStatusCounts();
        
        // Restore saved tab state even with empty data
        restoreSavedTabState();
    });
    
    // Enhanced modal close handlers for better UX
    document.addEventListener('click', function(e) {
        // Handle clicks on close button icons inside buttons
        if (e.target.matches('.fas.fa-times') || e.target.closest('button[onclick*="closeModal"]')) {
            console.log('🔒 Close button or icon clicked');
            return; // Let the onclick handler work
        }
    });
});

</script>
{% endblock %} 