{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تفضيلات العملاء" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-heart text-red-500 mr-3"></i>
                    {% trans "تفضيلات العملاء" %}
                </h1>
                <p class="text-gray-600 mt-2">{% trans "إدارة تفضيلات وخيارات العملاء" %}</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'billing:customer_preference_create' %}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    {% trans "إضافة تفضيل جديد" %}
                </a>
                <a href="{% url 'sales:dashboard' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Customer Preferences List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-list text-red-500 mr-2"></i>
                قائمة تفضيلات العملاء
            </h3>
        </div>
        <div class="p-6">
            {% if object_list %}
                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">نوع التفضيل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القيمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">شروط الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for preference in object_list %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-user text-gray-400 mr-2"></i>
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ preference.customer|default:"غير محدد" }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ preference.preference_type|default:"عام" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        {% if preference.attributes %}
                                            {{ preference.attributes|truncatewords:5 }}
                                        {% else %}
                                            لا توجد تفاصيل
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        {% if preference.payment_terms %}
                                            {% if preference.payment_terms == 'cash' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">نقداً</span>
                                            {% elif preference.payment_terms == 'credit_30' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">آجل 30 يوم</span>
                                            {% elif preference.payment_terms == 'credit_60' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">آجل 60 يوم</span>
                                            {% else %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">{{ preference.payment_terms }}</span>
                                            {% endif %}
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ preference.created_at|date:"Y/m/d"|default:"غير محدد" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex gap-2">
                                        <a href="{% url 'billing:customer_preference_detail' preference.pk %}" class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'billing:customer_preference_update' preference.pk %}" class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <div class="flex justify-center mt-8">
                    <nav class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?page=1" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">الأولى</a>
                            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">السابق</a>
                        {% endif %}
                        
                        <span class="px-3 py-2 text-sm text-gray-700">
                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                        </span>
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">التالي</a>
                            <a href="?page={{ page_obj.paginator.num_pages }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">الأخيرة</a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-heart text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد تفضيلات عملاء</h3>
                    <p class="text-gray-500 mb-6">ابدأ بإضافة تفضيلات العملاء لتخصيص الخدمات</p>
                    <a href="{% url 'billing:customer_preference_create' %}" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة تفضيل عميل
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 