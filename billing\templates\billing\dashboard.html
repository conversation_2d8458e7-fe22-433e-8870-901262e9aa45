{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load humanize %}

{% block title %}{% trans "لوحة تحكم الفواتير" %}{% endblock %}

{% block extra_css %}
<style>
    .invoice-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .payment-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    .revenue-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    .outstanding-card {
        background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
        color: white;
    }
    .chart-container {
        height: 300px;
    }
    .status-badge {
        @apply px-2 py-1 text-xs font-semibold rounded-full;
    }
    .status-draft { @apply bg-gray-100 text-gray-800; }
    .status-issued { @apply bg-blue-100 text-blue-800; }
    .status-paid { @apply bg-green-100 text-green-800; }
    .status-overdue { @apply bg-red-100 text-red-800; }
    .status-partially_paid { @apply bg-yellow-100 text-yellow-800; }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">{% trans "لوحة تحكم الفواتير" %}</h1>
            <p class="text-gray-600 mt-2">{% trans "إدارة شاملة للفواتير والمدفوعات" %}</p>
        </div>
        <div class="flex space-x-3 space-x-reverse">
         
            <a href="{% url 'billing:cashier_dashboard' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-list ml-2"></i>
                {% trans "جميع الفواتير" %}
            </a>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Revenue -->
        <div class="revenue-card rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{% trans "إجمالي الإيرادات" %}</h3>
                    <p class="text-3xl font-bold">{{ total_revenue|default:"0"|floatformat:2 }} {% trans "جنيه" %}</p>
                    <p class="text-sm opacity-90 mt-1">{% trans "جميع الفواتير المدفوعة" %}</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>

        <!-- Monthly Revenue -->
        <div class="invoice-card rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{% trans "إيرادات الشهر" %}</h3>
                    <p class="text-3xl font-bold">{{ monthly_revenue|default:"0"|floatformat:2 }} {% trans "جنيه" %}</p>
                    <p class="text-sm opacity-90 mt-1">{% trans "آخر 30 يوم" %}</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-calendar-alt"></i>
                </div>
            </div>
        </div>

        <!-- Outstanding Amount -->
        <div class="outstanding-card rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{% trans "المبالغ المستحقة" %}</h3>
                    <p class="text-3xl font-bold">{{ outstanding_amount|default:"0"|floatformat:2 }} {% trans "جنيه" %}</p>
                    <p class="text-sm opacity-90 mt-1">{% trans "فواتير غير مدفوعة" %}</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>

        <!-- Total Invoices -->
        <div class="payment-card rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{% trans "إجمالي الفواتير" %}</h3>
                    <p class="text-3xl font-bold">{{ total_invoices|default:"0" }}</p>
                    <p class="text-sm opacity-90 mt-1">{% trans "جميع الفواتير" %}</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-file-invoice"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Pending Invoices -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-500">{% trans "فواتير قيد الانتظار" %}</h4>
                    <p class="text-2xl font-bold text-gray-900">{{ pending_invoices|default:"0" }}</p>
                </div>
                <div class="p-3 bg-yellow-100 rounded-full">
                    <i class="fas fa-clock text-yellow-600"></i>
                </div>
            </div>
        </div>

        <!-- Overdue Invoices -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-500">{% trans "فواتير متأخرة" %}</h4>
                    <p class="text-2xl font-bold text-red-600">{{ overdue_invoices|default:"0" }}</p>
                </div>
                <div class="p-3 bg-red-100 rounded-full">
                    <i class="fas fa-exclamation-circle text-red-600"></i>
                </div>
            </div>
        </div>

        <!-- Average Invoice Value -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-500">{% trans "متوسط قيمة الفاتورة" %}</h4>
                    <p class="text-2xl font-bold text-gray-900">
                        {{ average_invoice_value|floatformat:2 }} {% trans "جنيه" %}
                    </p>
                </div>
                <div class="p-3 bg-blue-100 rounded-full">
                    <i class="fas fa-calculator text-blue-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Data -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        
        <!-- Monthly Revenue Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-4">{% trans "الإيرادات الشهرية" %}</h3>
            <div class="chart-container">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- Invoice Status Distribution -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-4">{% trans "توزيع حالات الفواتير" %}</h3>
            <div class="chart-container">
                <canvas id="statusChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Recent Invoices -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold">{% trans "الفواتير الحديثة" %}</h3>
                    <a href="{% url 'billing:cashier_dashboard' %}" class="text-blue-500 hover:text-blue-600 text-sm">
                        {% trans "عرض الكل" %}
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_invoices %}
                    <div class="space-y-4">
                        {% for invoice in recent_invoices %}
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg ml-3">
                                    <i class="fas fa-file-invoice text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">{{ invoice.invoice_number }}</p>
                                    <p class="text-sm text-gray-500">{{ invoice.customer.full_name }}</p>
                                </div>
                            </div>
                            <div class="text-left">
                                <p class="font-medium">{{ invoice.total_amount|floatformat:2 }} {% trans "جنيه" %}</p>
                                <span class="status-badge status-{{ invoice.status }}">
                                    {{ invoice.get_status_display }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i class="fas fa-file-invoice text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500">{% trans "لا توجد فواتير حديثة" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold">{% trans "المدفوعات الحديثة" %}</h3>
                    <a href="{% url 'billing:payment_list' %}" class="text-blue-500 hover:text-blue-600 text-sm">
                        {% trans "عرض الكل" %}
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_payments %}
                    <div class="space-y-4">
                        {% for payment in recent_payments %}
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-100 rounded-lg ml-3">
                                    <i class="fas fa-money-bill text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">{{ payment.invoice.invoice_number }}</p>
                                    <p class="text-sm text-gray-500">{{ payment.payment_method.name }}</p>
                                </div>
                            </div>
                            <div class="text-left">
                                <p class="font-medium text-green-600">{{ payment.amount|floatformat:2 }} {% trans "جنيه" %}</p>
                                <p class="text-sm text-gray-500">{{ payment.payment_date|date:"d/m/Y" }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i class="fas fa-money-bill text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500">{% trans "لا توجد مدفوعات حديثة" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">{% trans "إجراءات سريعة" %}</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="{% url 'billing:invoice_create' %}" class="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 text-center transition-colors">
                <i class="fas fa-plus text-blue-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium">{% trans "فاتورة جديدة" %}</p>
            </a>
            <a href="#" class="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 text-center transition-colors">
                <i class="fas fa-search text-green-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium">{% trans "البحث عن فاتورة" %}</p>
            </a>
            <a href="{% url 'billing:payment_create' %}" class="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 text-center transition-colors">
                <i class="fas fa-money-bill text-purple-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium">{% trans "تسجيل دفعة" %}</p>
            </a>
            <a href="{% url 'billing:reports' %}" class="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 text-center transition-colors">
                <i class="fas fa-chart-bar text-orange-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium">{% trans "التقارير" %}</p>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // Monthly Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: [
                {% for month in monthly_data %}
                    '{{ month.month }}'{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: 'الإيرادات (جنيه)',
                data: [
                    {% for month in monthly_data %}
                        {{ month.revenue }}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' جنيه';
                        }
                    }
                }
            }
        }
    });

    // Invoice Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['مسودة', 'مُصدرة', 'مدفوعة', 'متأخرة', 'مدفوعة جزئياً'],
            datasets: [{
                data: [
                    {{ pending_invoices|default:0 }},
                    {{ total_invoices|default:0 }},
                    0, // This should be calculated from actual paid invoices
                    {{ overdue_invoices|default:0 }},
                    0  // This should be calculated from partially paid invoices
                ],
                backgroundColor: [
                    '#9CA3AF',
                    '#3B82F6',
                    '#10B981',
                    '#EF4444',
                    '#F59E0B'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
{% endblock %} 