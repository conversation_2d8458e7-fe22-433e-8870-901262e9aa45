{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "شركات التأمين" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-building text-blue-500 mr-3"></i>
                    {% trans "شركات التأمين" %}
                </h1>
                <p class="text-gray-600 mt-2">{% trans "إدارة شركات التأمين وبياناتها" %}</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'billing:insurance_company_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    {% trans "إضافة شركة تأمين" %}
                </a>
                <a href="{% url 'sales:dashboard' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-blue-50 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-building text-2xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-blue-600 text-sm font-medium">إجمالي الشركات</p>
                    <p class="text-3xl font-bold text-blue-800">{{ object_list|length }}</p>
                    <p class="text-xs text-blue-500">شركة</p>
                </div>
            </div>
        </div>

        <div class="bg-green-50 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-2xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-green-600 text-sm font-medium">شركات نشطة</p>
                    <p class="text-3xl font-bold text-green-800">{{ object_list|length }}</p>
                    <p class="text-xs text-green-500">نشطة</p>
                </div>
            </div>
        </div>

        <div class="bg-purple-50 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-file-contract text-2xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-purple-600 text-sm font-medium">بوالص التأمين</p>
                    <p class="text-3xl font-bold text-purple-800">0</p>
                    <p class="text-xs text-purple-500">بوليصة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Insurance Companies List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-list text-blue-500 mr-2"></i>
                قائمة شركات التأمين
            </h3>
        </div>
        <div class="p-6">
            {% if object_list %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for company in object_list %}
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <i class="fas fa-building text-blue-500 text-2xl mr-3"></i>
                                <div>
                                    <div class="text-sm font-medium">{{ company.name }}</div>
                                    <div class="text-xs text-gray-500">{{ company.code|default:"لا يوجد كود" }}</div>
                                </div>
                            </div>
                            {% if company.is_active %}
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">نشط</span>
                            {% else %}
                                <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">غير نشط</span>
                            {% endif %}
                        </div>
                        {% if company.description %}
                            <div class="text-xs text-gray-600 mb-3">{{ company.description|truncatewords:10 }}</div>
                        {% endif %}
                        {% if company.contact_email %}
                            <div class="text-xs text-gray-600 mb-2">
                                <i class="fas fa-envelope mr-1"></i>
                                {{ company.contact_email }}
                            </div>
                        {% endif %}
                        {% if company.contact_phone %}
                            <div class="text-xs text-gray-600 mb-3">
                                <i class="fas fa-phone mr-1"></i>
                                {{ company.contact_phone }}
                            </div>
                        {% endif %}
                        <div class="flex gap-2 mt-3">
                            <a href="{% url 'billing:insurance_company_detail' company.pk %}" class="flex-1 text-center bg-blue-50 text-blue-600 px-3 py-2 rounded text-xs hover:bg-blue-100">
                                <i class="fas fa-eye mr-1"></i>عرض
                            </a>
                            <a href="{% url 'billing:insurance_company_update' company.pk %}" class="flex-1 text-center bg-green-50 text-green-600 px-3 py-2 rounded text-xs hover:bg-green-100">
                                <i class="fas fa-edit mr-1"></i>تعديل
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <div class="flex justify-center mt-8">
                    <nav class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?page=1" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">الأولى</a>
                            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">السابق</a>
                        {% endif %}
                        
                        <span class="px-3 py-2 text-sm text-gray-700">
                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                        </span>
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">التالي</a>
                            <a href="?page={{ page_obj.paginator.num_pages }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">الأخيرة</a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-building text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد شركات تأمين</h3>
                    <p class="text-gray-500 mb-6">ابدأ بإضافة شركة تأمين جديدة لإدارة بوالص التأمين</p>
                    <a href="{% url 'billing:insurance_company_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة شركة تأمين
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 