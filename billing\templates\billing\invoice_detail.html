{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load humanize %}

{% block title %}{% trans "فاتورة" %} {{ invoice.invoice_number }}{% endblock %}

{% block extra_css %}
<style>
    /* Comprehensive A4 Print Styles for Invoice Detail */
    @media print {
        /* Page Setup for A4 */
        @page {
            size: A4;
            margin: 10mm 8mm;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        
        /* Hide non-essential elements */
        .no-print { display: none !important; }
        
        /* Body and container adjustments */
        body {
            font-size: 10px !important;
            line-height: 1.2 !important;
            color: #000 !important;
            background: white !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        .container {
            max-width: none !important;
            padding: 0 !important;
            margin: 0 !important;
            width: 100% !important;
        }
        
        .invoice-container { 
            box-shadow: none !important; 
            border: none !important;
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        .print-full-width { 
            width: 100% !important; 
            margin: 0 !important; 
        }
        
        /* Header section adjustments */
        .bg-gradient-to-l {
            background: #2563eb !important;
            color: white !important;
        }
        
        .relative.z-10 {
            padding: 15px !important;
        }
        
        .flex.justify-between.items-start {
            display: block !important;
        }
        
        .flex-1 {
            margin-bottom: 10px !important;
        }
        
        .text-2xl {
            font-size: 16px !important;
            margin-bottom: 5px !important;
        }
        
        .text-3xl {
            font-size: 18px !important;
            margin-bottom: 8px !important;
        }
        
        .company-logo {
            max-height: 40px !important;
            max-width: 120px !important;
            margin-bottom: 8px !important;
        }
        
        .text-blue-100 {
            color: #e0e7ff !important;
            font-size: 9px !important;
        }
        
        .text-blue-100 p {
            margin-bottom: 2px !important;
        }
        
        /* Invoice info box */
        .bg-white.bg-opacity-20 {
            background: rgba(255, 255, 255, 0.2) !important;
            padding: 8px !important;
            border-radius: 4px !important;
            font-size: 9px !important;
        }
        
        .bg-white.bg-opacity-20 .flex {
            display: flex !important;
            justify-content: space-between !important;
            margin-bottom: 3px !important;
        }
        
        /* Watermark adjustments */
        .watermark {
            font-size: 3rem !important;
            color: rgba(0, 0, 0, 0.03) !important;
        }
        
        /* Main content padding */
        .p-8 {
            padding: 12px !important;
        }
        
        /* Grid adjustments */
        .grid {
            display: block !important;
        }
        
        .grid > div {
            margin-bottom: 15px !important;
        }
        
        /* Section headers */
        .text-lg.font-semibold {
            font-size: 12px !important;
            margin-bottom: 6px !important;
            color: #000 !important;
        }
        
        .border-b-2 {
            border-bottom: 1px solid #000 !important;
            padding-bottom: 2px !important;
        }
        
        /* Customer info */
        .bg-gray-50 {
            background: #f9f9f9 !important;
            padding: 8px !important;
            border: 1px solid #ddd !important;
            border-radius: 3px !important;
        }
        
        .font-semibold.text-lg {
            font-size: 11px !important;
            font-weight: bold !important;
            margin-bottom: 4px !important;
        }
        
        .text-gray-600 {
            color: #666 !important;
            font-size: 9px !important;
            margin-bottom: 2px !important;
        }
        
        /* QR code and additional info */
        .qr-code {
            width: 60px !important;
            height: 60px !important;
            border: 1px solid #ccc !important;
            margin: 0 auto 5px !important;
        }
        
        .text-xs {
            font-size: 8px !important;
        }
        
        /* Invoice items table */
        .invoice-table {
            width: 100% !important;
            border-collapse: collapse !important;
            font-size: 9px !important;
            margin: 10px 0 !important;
        }
        
        .invoice-table th,
        .invoice-table td {
            padding: 4px 6px !important;
            border: 1px solid #333 !important;
            text-align: right !important;
            font-size: 9px !important;
        }
        
        .invoice-table th {
            background-color: #f0f0f0 !important;
            font-weight: bold !important;
            color: #000 !important;
        }
        
        .invoice-table .font-medium {
            font-weight: bold !important;
        }
        
        .invoice-table .text-sm {
            font-size: 8px !important;
        }
        
        .invoice-table .text-center {
            text-align: center !important;
        }
        
        /* Totals section */
        .flex.justify-end {
            display: block !important;
        }
        
        .w-full {
            width: 100% !important;
            max-width: 300px !important;
            margin-left: auto !important;
        }
        
        .space-y-3 > * + * {
            margin-top: 4px !important;
        }
        
        .flex.justify-between {
            display: flex !important;
            justify-content: space-between !important;
            font-size: 9px !important;
        }
        
        .font-bold.text-lg {
            font-size: 11px !important;
            font-weight: bold !important;
        }
        
        .text-blue-600 {
            color: #2563eb !important;
            font-weight: bold !important;
        }
        
        /* Payment information table */
        .overflow-x-auto table {
            width: 100% !important;
            border-collapse: collapse !important;
            font-size: 9px !important;
        }
        
        .overflow-x-auto th,
        .overflow-x-auto td {
            padding: 3px 4px !important;
            border: 1px solid #ccc !important;
            font-size: 9px !important;
        }
        
        .bg-green-50 {
            background: #f0f9ff !important;
        }
        
        /* Notes and terms section - handled by general grid rule above */
        
        .bg-orange-50,
        .bg-gray-50 {
            background: #f9f9f9 !important;
            border: 1px solid #ddd !important;
            padding: 6px !important;
            border-radius: 3px !important;
        }
        
        .text-gray-700 {
            color: #000 !important;
            font-size: 9px !important;
            line-height: 1.2 !important;
        }
        
        .whitespace-pre-line {
            white-space: pre-line !important;
        }
        
        /* Status badge adjustments */
        .status-badge {
            font-size: 8px !important;
            padding: 2px 4px !important;
            border: 1px solid #333 !important;
            border-radius: 2px !important;
            background: white !important;
            color: #000 !important;
            font-weight: bold !important;
        }
        
        /* Footer section */
        .border-t.border-gray-200 {
            border-top: 1px solid #ccc !important;
            margin-top: 15px !important;
            padding-top: 8px !important;
        }
        
        .text-center.text-gray-500 {
            color: #666 !important;
            font-size: 8px !important;
            text-align: center !important;
        }
        
        /* Utility classes */
        .mb-8 {
            margin-bottom: 10px !important;
        }
        
        .mb-6 {
            margin-bottom: 8px !important;
        }
        
        .mb-4 {
            margin-bottom: 6px !important;
        }
        
        .gap-8 {
            gap: 10px !important;
        }
        
        /* Hide elements that take too much space */
        .fas {
            font-size: 10px !important;
        }
        
        /* Prevent page breaks in important sections */
        .invoice-table,
        .bg-gray-50,
        .invoice-container {
            page-break-inside: avoid;
        }
        
        /* Ensure RTL text direction */
        [dir="rtl"] {
            direction: rtl !important;
        }
        
        /* Color adjustments for print */
        .text-green-600 {
            color: #059669 !important;
        }
        
        .text-red-600 {
            color: #dc2626 !important;
        }
        
        .text-yellow-600 {
            color: #d97706 !important;
        }
    }
    
    .invoice-container {
        background: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-draft { background-color: #f3f4f6; color: #374151; }
    .status-issued { background-color: #dbeafe; color: #1e40af; }
    .status-paid { background-color: #d1fae5; color: #065f46; }
    .status-overdue { background-color: #fee2e2; color: #991b1b; }
    .status-partially_paid { background-color: #fef3c7; color: #92400e; }
    
    .watermark {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: 6rem;
        color: rgba(0, 0, 0, 0.05);
        z-index: 0;
        pointer-events: none;
    }
    
    .qr-code {
        width: 100px;
        height: 100px;
        border: 2px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9fafb;
    }
    
    .invoice-table th,
    .invoice-table td {
        padding: 0.75rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .invoice-table th {
        background-color: #f9fafb;
        font-weight: 600;
        text-align: right;
    }
    
    .company-logo {
        max-height: 80px;
        max-width: 200px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    
    <!-- Action Buttons -->
    <div class="mb-6 no-print flex justify-between items-center">
        <div>
            <a href="{% url 'billing:invoice_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right ml-2"></i>
                {% trans "العودة للقائمة" %}
            </a>
        </div>
        <div class="flex space-x-3 space-x-reverse">
            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print ml-2"></i>
                {% trans "طباعة" %}
            </button>
            <a href="#" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download ml-2"></i>
                {% trans "تحميل PDF" %}
            </a>
            {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                <a href="{% url 'billing:invoice_update' invoice.pk %}" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-edit ml-2"></i>
                    {% trans "تعديل" %}
                </a>
            {% endif %}
        </div>
    </div>

    <!-- Invoice Container -->
    <div class="invoice-container print-full-width relative overflow-hidden">
        
        <!-- Watermark -->
        {% if invoice.status == 'paid' %}
            <div class="watermark">{% trans "مدفوعة" %}</div>
        {% elif invoice.status == 'overdue' %}
            <div class="watermark text-red-200">{% trans "متأخرة" %}</div>
        {% elif invoice.status == 'cancelled' %}
            <div class="watermark text-red-200">{% trans "ملغاة" %}</div>
        {% endif %}
        
        <!-- Header -->
        <div class="relative z-10 bg-gradient-to-l from-blue-600 to-blue-800 text-white p-8">
            <div class="flex justify-between items-start">
                
                <!-- Company Info -->
                <div class="flex-1">
                    {% if invoice.service_center.company.logo %}
                        <img src="{{ invoice.service_center.company.logo.url }}" alt="{{ invoice.service_center.company.name }}" class="company-logo mb-4">
                    {% endif %}
                    <h1 class="text-2xl font-bold mb-2">{{ invoice.service_center.company.name }}</h1>
                    <div class="text-blue-100 space-y-1">
                        {% if invoice.service_center.address %}
                            <p><i class="fas fa-map-marker-alt ml-2"></i>{{ invoice.service_center.address }}</p>
                        {% endif %}
                        {% if invoice.service_center.phone %}
                            <p><i class="fas fa-phone ml-2"></i>{{ invoice.service_center.phone }}</p>
                        {% endif %}
                        {% if invoice.service_center.email %}
                            <p><i class="fas fa-envelope ml-2"></i>{{ invoice.service_center.email }}</p>
                        {% endif %}
                        {% if invoice.service_center.company.tax_id %}
                            <p><i class="fas fa-receipt ml-2"></i>{% trans "الرقم الضريبي:" %} {{ invoice.service_center.company.tax_id }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Invoice Info -->
                <div class="text-left">
                    <h2 class="text-3xl font-bold mb-4">{% trans "فـــاتـــورة" %}</h2>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 space-y-2">
                        <div class="flex justify-between">
                            <span class="font-semibold">{% trans "رقم الفاتورة:" %}</span>
                            <span>{{ invoice.invoice_number }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-semibold">{% trans "تاريخ الإصدار:" %}</span>
                            <span>{{ invoice.invoice_date|date:"d/m/Y" }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-semibold">{% trans "تاريخ الاستحقاق:" %}</span>
                            <span>{{ invoice.due_date|date:"d/m/Y" }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-semibold">{% trans "الحالة:" %}</span>
                            <span class="status-badge status-{{ invoice.status }} text-gray-800">
                                {{ invoice.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer and Invoice Details -->
        <div class="p-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                
                <!-- Bill To -->
                <div class="lg:col-span-2">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 border-b-2 border-blue-500 pb-2">
                        {% trans "فاتورة إلى:" %}
                    </h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-lg mb-2">
                            {% if invoice.customer.customer_type == 'corporate' %}
                                {{ invoice.customer.company_name }}
                            {% else %}
                                {{ invoice.customer.full_name }}
                            {% endif %}
                        </h4>
                        {% if invoice.customer.customer_type == 'corporate' and invoice.customer.full_name %}
                            <p class="text-gray-600 mb-1">{% trans "جهة الاتصال:" %} {{ invoice.customer.full_name }}</p>
                        {% endif %}
                        {% if invoice.customer.email %}
                            <p class="text-gray-600 mb-1"><i class="fas fa-envelope ml-2"></i>{{ invoice.customer.email }}</p>
                        {% endif %}
                        {% if invoice.customer.phone %}
                            <p class="text-gray-600 mb-1"><i class="fas fa-phone ml-2"></i>{{ invoice.customer.phone }}</p>
                        {% endif %}
                        {% if invoice.customer.address %}
                            <p class="text-gray-600"><i class="fas fa-map-marker-alt ml-2"></i>{{ invoice.customer.address }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- QR Code and Additional Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 border-b-2 border-blue-500 pb-2">
                        {% trans "معلومات إضافية" %}
                    </h3>
                    <div class="space-y-4">
                        <!-- QR Code -->
                        <div class="text-center">
                            <div class="qr-code mx-auto mb-2">
                                <i class="fas fa-qrcode text-gray-400 text-2xl"></i>
                            </div>
                            <p class="text-xs text-gray-500">{% trans "رمز QR للتحقق" %}</p>
                        </div>
                        
                        <!-- Work Order Info -->
                        {% if invoice.work_order %}
                        <div class="bg-blue-50 rounded-lg p-3">
                            <h5 class="font-semibold text-blue-800 mb-2">{% trans "أمر العمل" %}</h5>
                            <p class="text-sm text-blue-600">{{ invoice.work_order.work_order_number }}</p>
                        </div>
                        {% endif %}
                        
                        <!-- Payment Terms -->
                        <div class="bg-yellow-50 rounded-lg p-3">
                            <h5 class="font-semibold text-yellow-800 mb-2">{% trans "شروط الدفع" %}</h5>
                            <p class="text-sm text-yellow-600">{% trans "الدفع خلال 30 يوم من تاريخ الإصدار" %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Items -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4 text-gray-800 border-b-2 border-blue-500 pb-2">
                    {% trans "بنود الفاتورة" %}
                </h3>
                <div class="overflow-x-auto">
                    <table class="invoice-table w-full">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="text-right">{% trans "الوصف" %}</th>
                                <th class="text-center">{% trans "الكمية" %}</th>
                                <th class="text-center">{% trans "سعر الوحدة" %}</th>
                                <th class="text-center">{% trans "الخصم" %}</th>
                                <th class="text-center">{% trans "الضريبة" %}</th>
                                <th class="text-center">{% trans "الإجمالي" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in invoice_items %}
                            <tr>
                                <td>
                                    <div>
                                        <p class="font-medium">{{ item.description }}</p>
                                        <p class="text-sm text-gray-500">{{ item.get_item_type_display }}</p>
                                        {% if item.part_id %}
                                            <p class="text-xs text-gray-400">{% trans "كود القطعة:" %} {{ item.part_id }}</p>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="text-center">{{ item.quantity|floatformat:2 }}</td>
                                <td class="text-center">{{ item.unit_price|floatformat:2 }} ج.م</td>
                                <td class="text-center">
                                    {% if item.discount_amount > 0 %}
                                        {{ item.discount_amount|floatformat:2 }} ج.م
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if item.tax_amount > 0 %}
                                        {{ item.tax_amount|floatformat:2 }} {% trans "ر.س" %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-center font-semibold">{{ item.line_total|floatformat:2 }} {% trans "ر.س" %}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center py-8 text-gray-500">
                                    {% trans "لا توجد بنود في هذه الفاتورة" %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Invoice Totals -->
            <div class="flex justify-end mb-8">
                <div class="w-full lg:w-1/2">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">{% trans "المجموع الفرعي:" %}</span>
                                <span class="font-medium">{{ invoice.subtotal|floatformat:2 }} {% trans "ر.س" %}</span>
                            </div>
                            
                            {% if invoice.discount_amount > 0 %}
                            <div class="flex justify-between text-green-600">
                                <span>{% trans "الخصم:" %}</span>
                                <span>-{{ invoice.discount_amount|floatformat:2 }} {% trans "ر.س" %}</span>
                            </div>
                            {% endif %}
                            
                            {% if invoice.tax_amount > 0 %}
                            <div class="flex justify-between">
                                <span class="text-gray-600">{% trans "الضريبة" %} ({{ invoice.tax_percentage }}%):</span>
                                <span class="font-medium">{{ invoice.tax_amount|floatformat:2 }} {% trans "ر.س" %}</span>
                            </div>
                            {% endif %}
                            
                            <hr class="my-3">
                            
                            <div class="flex justify-between text-lg font-bold">
                                <span>{% trans "الإجمالي:" %}</span>
                                <span class="text-blue-600">{{ invoice.total_amount|floatformat:2 }} {% trans "ر.س" %}</span>
                            </div>
                            
                            {% if invoice.amount_paid > 0 %}
                            <div class="flex justify-between text-green-600">
                                <span>{% trans "المبلغ المدفوع:" %}</span>
                                <span>{{ invoice.amount_paid|floatformat:2 }} {% trans "ر.س" %}</span>
                            </div>
                            {% endif %}
                            
                            {% if invoice.amount_due > 0 %}
                            <div class="flex justify-between text-red-600 font-semibold">
                                <span>{% trans "المبلغ المستحق:" %}</span>
                                <span>{{ invoice.amount_due|floatformat:2 }} {% trans "ر.س" %}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            {% if payments %}
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4 text-gray-800 border-b-2 border-green-500 pb-2">
                    {% trans "تفاصيل المدفوعات" %}
                </h3>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-green-50">
                            <tr>
                                <th class="px-4 py-3 text-right">{% trans "تاريخ الدفع" %}</th>
                                <th class="px-4 py-3 text-center">{% trans "طريقة الدفع" %}</th>
                                <th class="px-4 py-3 text-center">{% trans "المبلغ" %}</th>
                                <th class="px-4 py-3 text-center">{% trans "المرجع" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr class="border-b">
                                <td class="px-4 py-3">{{ payment.payment_date|date:"d/m/Y" }}</td>
                                <td class="px-4 py-3 text-center">{{ payment.payment_method.name }}</td>
                                <td class="px-4 py-3 text-center text-green-600 font-semibold">{{ payment.amount|floatformat:2 }} {% trans "ر.س" %}</td>
                                <td class="px-4 py-3 text-center">{{ payment.reference_number|default:"-" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- Notes and Terms -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Notes -->
                {% if invoice.notes %}
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 border-b-2 border-orange-500 pb-2">
                        {% trans "ملاحظات" %}
                    </h3>
                    <div class="bg-orange-50 rounded-lg p-4">
                        <p class="text-gray-700 whitespace-pre-line">{{ invoice.notes }}</p>
                    </div>
                </div>
                {% endif %}
                
                <!-- Terms and Conditions -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 border-b-2 border-gray-500 pb-2">
                        {% trans "الشروط والأحكام" %}
                    </h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        {% if invoice.terms_and_conditions %}
                            <p class="text-gray-700 whitespace-pre-line">{{ invoice.terms_and_conditions }}</p>
                        {% else %}
                            <div class="text-sm text-gray-600 space-y-2">
                                <p>• {% trans "جميع الأسعار تشمل ضريبة القيمة المضافة" %}</p>
                                <p>• {% trans "يرجى الدفع خلال 30 يوم من تاريخ الإصدار" %}</p>
                                <p>• {% trans "في حالة التأخير في الدفع، ستطبق رسوم إضافية" %}</p>
                                <p>• {% trans "جميع المنتجات تحت الضمان حسب الشروط المحددة" %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-8 pt-6 border-t border-gray-200 text-center text-gray-500 text-sm">
                <p>{% trans "شكراً لتعاملكم معنا" %} | {{ invoice.service_center.company.name }}</p>
                <p class="mt-2">{% trans "تم إنشاء هذه الفاتورة بواسطة نظام ما بعد البيع" %} - {{ invoice.created_at|date:"d/m/Y H:i" }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-focus print dialog on Ctrl+P
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        window.print();
    }
});

// Generate QR Code (placeholder - integrate with actual QR library)
document.addEventListener('DOMContentLoaded', function() {
    // You can integrate with QR.js or similar library here
    // const qrData = `INV-${invoice.invoice_number}-${invoice.total_amount}`;
    // Generate QR code with invoice verification data
});
</script>
{% endblock %} 