{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load humanize %}

{% block title %}{% trans "قائمة الفواتير" %}{% endblock %}

{% block extra_css %}
<style>
    .invoice-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
    }
    
    .invoice-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .status-paid { background-color: #dcfce7; color: #166534; }
    .status-pending { background-color: #fef3c7; color: #92400e; }
    .status-overdue { background-color: #fee2e2; color: #dc2626; }
    .status-draft { background-color: #f3f4f6; color: #374151; }

    /* Print Specific Styles for A4 Invoice List */
    @media print {
        /* Page Setup */
        @page {
            size: A4;
            margin: 15mm 10mm;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        
        /* Hide elements not needed in print */
        .no-print,
        button,
        .bg-gray-600,
        .bg-blue-600,
        form,
        .flex.gap-3,
        .flex.gap-2,
        .pagination,
        .flex.justify-end,
        .flex.justify-between.items-center.mt-6,
        .fixed {
            display: none !important;
        }
        
        /* Body and container adjustments */
        body {
            font-size: 11px !important;
            line-height: 1.2 !important;
            color: #000 !important;
            background: white !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        .container {
            max-width: none !important;
            padding: 0 !important;
            margin: 0 !important;
            width: 100% !important;
        }
        
        .min-h-screen {
            min-height: auto !important;
            background: white !important;
        }
        
        /* Header adjustments */
        h1 {
            font-size: 16px !important;
            margin-bottom: 8px !important;
            color: #000 !important;
            text-align: center !important;
        }
        
        .text-3xl {
            font-size: 16px !important;
        }
        
        .text-gray-600 {
            font-size: 9px !important;
            color: #666 !important;
            text-align: center !important;
        }
        
        /* Table adjustments */
        .bg-white {
            background: white !important;
            box-shadow: none !important;
            border: none !important;
        }
        
        table {
            width: 100% !important;
            border-collapse: collapse !important;
            font-size: 10px !important;
            margin-top: 10px !important;
        }
        
        th, td {
            padding: 3px 5px !important;
            border: 1px solid #333 !important;
            text-align: right !important;
            font-size: 10px !important;
            line-height: 1.1 !important;
        }
        
        th {
            background-color: #f0f0f0 !important;
            font-weight: bold !important;
            font-size: 10px !important;
            color: #000 !important;
        }
        
        /* Status badges for print */
        .status-badge {
            font-size: 8px !important;
            padding: 1px 4px !important;
            border: 1px solid #333 !important;
            border-radius: 2px !important;
            background-color: white !important;
            color: #000 !important;
            font-weight: bold !important;
        }
        
        /* Remove shadows and backgrounds */
        .shadow-sm,
        .border,
        .rounded-lg,
        .hover\:bg-gray-50 {
            background: transparent !important;
            box-shadow: none !important;
            border: none !important;
            border-radius: 0 !important;
        }
        
        /* Ensure proper spacing */
        .mb-6 {
            margin-bottom: 8px !important;
        }
        
        .p-6 {
            padding: 0 !important;
        }
        
        .px-4 {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
        
        .py-6 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }
        
        /* Font weights and colors */
        .font-medium,
        .font-bold {
            font-weight: bold !important;
            color: #000 !important;
        }
        
        .text-blue-600,
        .text-gray-900,
        .text-gray-500,
        .text-gray-700 {
            color: #000 !important;
        }
        
        /* Responsive table for print */
        .overflow-x-auto {
            overflow: visible !important;
        }
        
        /* Fix RTL direction for print */
        [dir="rtl"] table {
            direction: rtl !important;
        }
        
        /* Empty state adjustments */
        .fas {
            font-size: 12px !important;
        }
        
        .text-4xl {
            font-size: 16px !important;
        }
        
        .text-lg {
            font-size: 12px !important;
        }
        
        .text-sm {
            font-size: 9px !important;
        }
        
        /* Print header */
        .print-header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #000;
            padding-bottom: 8px;
        }
        
        .print-header h1 {
            font-size: 18px !important;
            font-weight: bold !important;
            margin: 0 !important;
        }
        
        .print-date {
            font-size: 9px;
            text-align: left;
            margin-bottom: 10px;
            color: #333;
        }
        
        /* Hide actions column header and data */
        th:last-child,
        td:last-child {
            display: none !important;
        }
        
        /* Adjust email text size */
        .text-sm.text-gray-500 {
            font-size: 8px !important;
        }
        
        /* Better spacing for invoice rows */
        tbody tr {
            page-break-inside: avoid;
        }
        
        /* Ensure table fits on page */
        .w-full {
            width: 100% !important;
        }
    }
    
    /* Print button styling */
    .print-btn {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1000;
        background: #059669;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 12px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .print-btn:hover {
        background: #047857;
    }
    
    @media print {
        .print-btn {
            display: none !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Print Button -->
<button onclick="window.print()" class="print-btn no-print">
    <i class="fas fa-print"></i> طباعة
</button>

<!-- Print Date Info -->
<div class="print-date" style="display: none;">
    تاريخ الطباعة: <span id="printDate"></span>
</div>

<div class="min-h-screen bg-gray-50" dir="rtl">
    <div class="container mx-auto px-4 py-6">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-file-invoice text-blue-600"></i>
                    {% trans "قائمة الفواتير" %}
                </h1>
                <p class="text-gray-600 mt-2">{% trans "إدارة وعرض جميع الفواتير" %}</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'billing:cashier_dashboard' %}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2">
                    <i class="fas fa-arrow-right"></i>
                    {% trans "العودة للكاشير" %}
                </a>
                <button onclick="openModal('createInvoiceModal')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                    <i class="fas fa-plus"></i>
                    {% trans "فاتورة جديدة" %}
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 no-print">
            <form method="get" id="filterForm">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "البحث" %}</label>
                        <input type="text" id="searchInput" name="search" value="{{ request.GET.search }}" placeholder="{% trans 'رقم الفاتورة أو العميل' %}" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "الحالة" %}</label>
                        <select id="statusFilter" name="status" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="paid" {% if request.GET.status == 'paid' %}selected{% endif %}>{% trans "مدفوعة" %}</option>
                            <option value="issued" {% if request.GET.status == 'issued' %}selected{% endif %}>{% trans "معلقة" %}</option>
                            <option value="overdue" {% if request.GET.status == 'overdue' %}selected{% endif %}>{% trans "متأخرة" %}</option>
                            <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>{% trans "مسودة" %}</option>
                            <option value="partially_paid" {% if request.GET.status == 'partially_paid' %}selected{% endif %}>{% trans "مدفوعة جزئياً" %}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "من تاريخ" %}</label>
                        <input type="date" id="dateFrom" name="date_from" value="{{ request.GET.date_from }}" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "إلى تاريخ" %}</label>
                        <input type="date" id="dateTo" name="date_to" value="{{ request.GET.date_to }}" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="mt-4 flex gap-2">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                        <i class="fas fa-search"></i>
                        {% trans "بحث" %}
                    </button>
                    <a href="{% url 'billing:invoice_list' %}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2">
                        <i class="fas fa-times"></i>
                        {% trans "مسح الفلاتر" %}
                    </a>
                </div>
            </form>
        </div>

        <!-- Invoices List -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="w-full table-auto">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">{% trans "رقم الفاتورة" %}</th>
                                <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">{% trans "العميل" %}</th>
                                <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">{% trans "التاريخ" %}</th>
                                <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">{% trans "المبلغ" %}</th>
                                <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">{% trans "الحالة" %}</th>
                                <th class="px-4 py-3 text-right text-sm font-medium text-gray-700 no-print">{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody id="invoicesTable">
                            {% for invoice in invoices %}
                            <tr class="border-t border-gray-200 hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <span class="font-medium text-blue-600">#{{ invoice.invoice_number }}</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div>
                                        <div class="font-medium text-gray-900">{{ invoice.customer.full_name }}</div>
                                        {% if invoice.customer.email %}
                                        <div class="text-sm text-gray-500">{{ invoice.customer.email }}</div>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-gray-900">{{ invoice.invoice_date|date:"Y/m/d" }}</td>
                                <td class="px-4 py-3">
                                    <span class="font-medium text-gray-900">{{ invoice.total_amount|floatformat:2 }} جنيه</span>
                                </td>
                                <td class="px-4 py-3">
                                    {% if invoice.status == 'paid' %}
                                        <span class="status-badge status-paid">{% trans "مدفوعة" %}</span>
                                    {% elif invoice.status == 'draft' %}
                                        <span class="status-badge status-draft">{% trans "مسودة" %}</span>
                                    {% elif invoice.status == 'overdue' %}
                                        <span class="status-badge status-overdue">{% trans "متأخرة" %}</span>
                                    {% elif invoice.status == 'partially_paid' %}
                                        <span class="status-badge status-pending">{% trans "مدفوعة جزئياً" %}</span>
                                    {% else %}
                                        <span class="status-badge status-pending">{% trans "معلقة" %}</span>
                                    {% endif %}
                                </td>
                                <td class="px-4 py-3 no-print">
                                    <div class="flex gap-2">
                                        <button onclick="viewInvoice('{{ invoice.invoice_number }}')" class="text-blue-600 hover:text-blue-800" title="{% trans 'عرض' %}">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="printInvoice('{{ invoice.invoice_number }}')" class="text-green-600 hover:text-green-800" title="{% trans 'طباعة' %}">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button onclick="editInvoice('{{ invoice.invoice_number }}')" class="text-yellow-600 hover:text-yellow-800" title="{% trans 'تعديل' %}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                                    <div class="flex flex-col items-center">
                                        <i class="fas fa-file-invoice text-4xl text-gray-300 mb-4"></i>
                                        <p class="text-lg font-medium">{% trans "لا توجد فواتير" %}</p>
                                        <p class="text-sm">{% trans "لم يتم العثور على أي فواتير مطابقة للبحث" %}</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="flex justify-between items-center mt-6 px-6 py-4 no-print">
                <div class="text-sm text-gray-700">
                    {% trans "عرض" %} {{ page_obj.start_index }} - {{ page_obj.end_index }} {% trans "من" %} {{ page_obj.paginator.count }} {% trans "فاتورة" %}
                </div>
                <div class="flex gap-2">
                    {% if page_obj.has_previous %}
                        <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-lg">{{ num }}</span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" 
                               class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50">{{ num }}</a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Create Invoice Modal -->
<div id="createInvoiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50 no-print" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto">
        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-plus"></i>
                {% trans "إنشاء فاتورة جديدة" %}
            </h3>
            <button onclick="closeModal('createInvoiceModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <p class="text-gray-600 mb-4">{% trans "سيتم إضافة نموذج إنشاء الفاتورة هنا" %}</p>
            <div class="flex justify-end">
                <button onclick="closeModal('createInvoiceModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    {% trans "إغلاق" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Modal functions (reused from supply chain dashboard)
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// Invoice actions
function viewInvoice(invoiceId) {
    console.log('View invoice:', invoiceId);
    // Implement view logic
}

function printInvoice(invoiceId) {
    console.log('Print invoice:', invoiceId);
    // Implement print logic
}

function editInvoice(invoiceId) {
    console.log('Edit invoice:', invoiceId);
    // Implement edit logic
}

// Auto-submit form on filter changes
document.addEventListener('DOMContentLoaded', function() {
    const statusFilter = document.getElementById('statusFilter');
    const dateFrom = document.getElementById('dateFrom');
    const dateTo = document.getElementById('dateTo');
    const filterForm = document.getElementById('filterForm');
    
    // Set print date
    const printDate = document.getElementById('printDate');
    if (printDate) {
        const now = new Date();
        const dateStr = now.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'long', 
            day: 'numeric'
        });
        const timeStr = now.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit'
        });
        printDate.textContent = dateStr + ' - ' + timeStr;
    }
    
    // Show/hide print date
    window.addEventListener('beforeprint', function() {
        const printDateEl = document.querySelector('.print-date');
        if (printDateEl) {
            printDateEl.style.display = 'block';
        }
    });
    
    window.addEventListener('afterprint', function() {
        const printDateEl = document.querySelector('.print-date');
        if (printDateEl) {
            printDateEl.style.display = 'none';
        }
    });
    
    // Auto-submit on status change
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            filterForm.submit();
        });
    }
    
    // Auto-submit on date changes
    if (dateFrom) {
        dateFrom.addEventListener('change', function() {
            filterForm.submit();
        });
    }
    
    if (dateTo) {
        dateTo.addEventListener('change', function() {
            filterForm.submit();
        });
    }
    
    // Submit on Enter key for search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                filterForm.submit();
            }
        });
    }
});
</script>
{% endblock %} 