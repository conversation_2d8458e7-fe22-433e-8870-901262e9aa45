{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "طرق الدفع" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-credit-card text-teal-500 mr-3"></i>
                    {% trans "طرق الدفع" %}
                </h1>
                <p class="text-gray-600 mt-2">{% trans "إدارة طرق الدفع المتاحة" %}</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'billing:payment_method_create' %}" class="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    {% trans "إضافة طريقة دفع" %}
                </a>
                <a href="{% url 'sales:dashboard' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Payment Methods List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-list text-teal-500 mr-2"></i>
                قائمة طرق الدفع
            </h3>
        </div>
        <div class="p-6">
            {% if object_list %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for method in object_list %}
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                {% if 'نقد' in method.name or 'cash' in method.name|lower %}
                                    <i class="fas fa-money-bill-wave text-green-500 text-2xl mr-3"></i>
                                {% elif 'فيزا' in method.name or 'visa' in method.name|lower %}
                                    <i class="fab fa-cc-visa text-blue-500 text-2xl mr-3"></i>
                                {% elif 'ماستر' in method.name or 'master' in method.name|lower %}
                                    <i class="fab fa-cc-mastercard text-red-500 text-2xl mr-3"></i>
                                {% elif 'تحويل' in method.name or 'transfer' in method.name|lower %}
                                    <i class="fas fa-exchange-alt text-purple-500 text-2xl mr-3"></i>
                                {% else %}
                                    <i class="fas fa-credit-card text-teal-500 text-2xl mr-3"></i>
                                {% endif %}
                                <div>
                                    <div class="text-sm font-medium">{{ method.name }}</div>
                                    <div class="text-xs text-gray-500">{{ method.code|default:"لا يوجد كود" }}</div>
                                </div>
                            </div>
                            {% if method.is_active %}
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">متاح</span>
                            {% else %}
                                <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">غير متاح</span>
                            {% endif %}
                        </div>
                        
                        {% if method.description %}
                            <div class="text-xs text-gray-600 mb-3">{{ method.description|truncatewords:10 }}</div>
                        {% endif %}
                        
                        <div class="space-y-1 mb-3">
                            {% if method.fees %}
                                <div class="text-xs text-gray-600">
                                    <i class="fas fa-percentage mr-1"></i>
                                    الرسوم: {{ method.fees }}%
                                </div>
                            {% endif %}
                            {% if method.processing_time %}
                                <div class="text-xs text-gray-600">
                                    <i class="fas fa-clock mr-1"></i>
                                    وقت المعالجة: {{ method.processing_time }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="flex gap-2">
                            <a href="{% url 'billing:payment_method_detail' method.pk %}" class="flex-1 text-center bg-blue-50 text-blue-600 px-3 py-2 rounded text-xs hover:bg-blue-100">
                                <i class="fas fa-eye mr-1"></i>عرض
                            </a>
                            <a href="{% url 'billing:payment_method_update' method.pk %}" class="flex-1 text-center bg-green-50 text-green-600 px-3 py-2 rounded text-xs hover:bg-green-100">
                                <i class="fas fa-edit mr-1"></i>تعديل
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <div class="flex justify-center mt-8">
                    <nav class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?page=1" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">الأولى</a>
                            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">السابق</a>
                        {% endif %}
                        
                        <span class="px-3 py-2 text-sm text-gray-700">
                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                        </span>
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">التالي</a>
                            <a href="?page={{ page_obj.paginator.num_pages }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">الأخيرة</a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-credit-card text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد طرق دفع</h3>
                    <p class="text-gray-500 mb-6">ابدأ بإضافة طريقة دفع جديدة لقبول المدفوعات</p>
                    <a href="{% url 'billing:payment_method_create' %}" class="bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-lg">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة طريقة دفع
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 