{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "أنواع الضمان" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-tags text-orange-500 mr-3"></i>
                    {% trans "أنواع الضمان" %}
                </h1>
                <p class="text-gray-600 mt-2">{% trans "إدارة أنواع الضمان المختلفة" %}</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'billing:warranty_type_create' %}" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    {% trans "إضافة نوع ضمان" %}
                </a>
                <a href="{% url 'sales:dashboard' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Warranty Types List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-list text-orange-500 mr-2"></i>
                قائمة أنواع الضمان
            </h3>
        </div>
        <div class="p-6">
            {% if object_list %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for warranty_type in object_list %}
                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-tag text-orange-500 text-2xl mr-3"></i>
                                <div>
                                    <div class="text-lg font-medium">{{ warranty_type.name }}</div>
                                    <div class="text-xs text-gray-500">{{ warranty_type.code|default:"لا يوجد كود" }}</div>
                                </div>
                            </div>
                            {% if warranty_type.is_active %}
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">متاح</span>
                            {% else %}
                                <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">غير متاح</span>
                            {% endif %}
                        </div>
                        
                        {% if warranty_type.description %}
                            <div class="text-sm text-gray-600 mb-4">{{ warranty_type.description|truncatewords:15 }}</div>
                        {% endif %}
                        
                        <div class="space-y-2 mb-4">
                            {% if warranty_type.duration %}
                                <div class="text-sm text-gray-600">
                                    <i class="fas fa-calendar-alt mr-2"></i>
                                    المدة: {{ warranty_type.duration }} شهر
                                </div>
                            {% endif %}
                            {% if warranty_type.coverage_limit %}
                                <div class="text-sm text-gray-600">
                                    <i class="fas fa-dollar-sign mr-2"></i>
                                    حد التغطية: {{ warranty_type.coverage_limit }} ج.م
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="flex gap-2">
                            <a href="{% url 'billing:warranty_type_detail' warranty_type.pk %}" class="flex-1 text-center bg-blue-50 text-blue-600 px-3 py-2 rounded text-sm hover:bg-blue-100">
                                <i class="fas fa-eye mr-1"></i>عرض
                            </a>
                            <a href="{% url 'billing:warranty_type_update' warranty_type.pk %}" class="flex-1 text-center bg-green-50 text-green-600 px-3 py-2 rounded text-sm hover:bg-green-100">
                                <i class="fas fa-edit mr-1"></i>تعديل
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <div class="flex justify-center mt-8">
                    <nav class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?page=1" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">الأولى</a>
                            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">السابق</a>
                        {% endif %}
                        
                        <span class="px-3 py-2 text-sm text-gray-700">
                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                        </span>
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">التالي</a>
                            <a href="?page={{ page_obj.paginator.num_pages }}" class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">الأخيرة</a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-tags text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أنواع ضمان</h3>
                    <p class="text-gray-500 mb-6">ابدأ بإضافة نوع ضمان جديد لتصنيف الضمانات</p>
                    <a href="{% url 'billing:warranty_type_create' %}" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة نوع ضمان
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 