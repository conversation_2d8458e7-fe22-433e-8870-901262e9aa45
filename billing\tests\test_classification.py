from django.test import TestCase
from django.utils import timezone
from decimal import Decimal
import uuid
from unittest.mock import patch, MagicMock

from setup.models import Customer
from billing.models_classification import CustomerClassification, ClassificationCriteria
from billing.services_classification import CustomerClassificationService


class CustomerClassificationServiceTests(TestCase):
    """
    Tests for the CustomerClassificationService
    """
    
    def setUp(self):
        """Set up test data"""
        self.tenant_id = uuid.uuid4()
        
        # Create test classifications
        self.platinum = CustomerClassification.objects.create(
            tenant_id=self.tenant_id,
            name="Platinum",
            level=30,
            discount_percentage=Decimal("20.00"),
            is_active=True
        )
        
        self.gold = CustomerClassification.objects.create(
            tenant_id=self.tenant_id,
            name="Gold",
            level=20,
            discount_percentage=Decimal("10.00"),
            is_active=True
        )
        
        self.silver = CustomerClassification.objects.create(
            tenant_id=self.tenant_id,
            name="Silver",
            level=10,
            discount_percentage=Decimal("5.00"),
            is_active=True
        )
        
        self.regular = CustomerClassification.objects.create(
            tenant_id=self.tenant_id,
            name="Regular",
            level=0,
            discount_percentage=Decimal("0.00"),
            is_active=True
        )
        
        # Create test customers
        self.customer = Customer.objects.create(
            tenant_id=self.tenant_id,
            first_name="Test",
            last_name="Customer",
            phone="1234567890",
            classification=self.regular
        )
        
        # Create criteria
        self.spend_criteria = ClassificationCriteria.objects.create(
            tenant_id=self.tenant_id,
            classification=self.gold,
            criteria_type="spend_amount",
            operator="gte",
            value=Decimal("5000.00"),
            period="year",
            is_required=True
        )
        
    def test_check_qualification_no_criteria(self):
        """Test qualification check with no criteria defined"""
        service = CustomerClassificationService(self.tenant_id)
        
        # Remove all criteria
        ClassificationCriteria.objects.filter(tenant_id=self.tenant_id).delete()
        
        qualifies, data = service._check_qualification(self.customer, self.gold)
        
        self.assertFalse(qualifies)
        self.assertEqual(data["error"], "No criteria defined")
    
    @patch('billing.services_classification.Invoice.objects')
    def test_evaluate_spend_amount(self, mock_invoice):
        """Test spend amount evaluation"""
        service = CustomerClassificationService(self.tenant_id)
        
        # Mock the invoice query
        mock_invoice.filter.return_value.aggregate.return_value = {'total': Decimal("6000.00")}
        
        result = service._evaluate_spend_amount(
            self.customer, 
            self.spend_criteria,
            timezone.now() - timezone.timedelta(days=365),
            timezone.now()
        )
        
        self.assertTrue(result["passed"])
        self.assertEqual(result["value"], float(6000.00))
    
    @patch('billing.services_classification.CustomerClassificationService._check_qualification')
    def test_evaluate_customer_no_change(self, mock_check):
        """Test customer evaluation with no classification change"""
        service = CustomerClassificationService(self.tenant_id)
        
        # Mock qualification check to return False for all classifications
        mock_check.return_value = (False, {})
        
        result = service.evaluate_customer(self.customer)
        
        self.assertIsNone(result)
        self.assertEqual(self.customer.classification, self.regular)
    
    @patch('billing.services_classification.CustomerClassificationService._check_qualification')
    def test_evaluate_customer_upgrade(self, mock_check):
        """Test customer evaluation with classification upgrade"""
        service = CustomerClassificationService(self.tenant_id)
        
        # Mock qualification check to return True for gold
        def check_qualification_side_effect(customer, classification):
            if classification == self.gold:
                return True, {}
            return False, {}
            
        mock_check.side_effect = check_qualification_side_effect
        
        result = service.evaluate_customer(self.customer)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.previous_classification, self.regular)
        self.assertEqual(result.new_classification, self.gold)
        
        # Refresh customer from DB to check if classification was updated
        self.customer.refresh_from_db()
        self.assertEqual(self.customer.classification, self.gold)
    
    @patch('billing.services_classification.CustomerClassificationService._check_qualification')
    def test_downgrade_customer(self, mock_check):
        """Test downgrading a customer who no longer meets criteria"""
        # First upgrade customer to gold
        self.customer.classification = self.gold
        self.customer.save()
        
        service = CustomerClassificationService(self.tenant_id)
        
        # Mock qualification check to return False for gold, True for silver
        def check_qualification_side_effect(customer, classification):
            if classification == self.gold:
                return False, {}
            if classification == self.silver:
                return True, {}
            return False, {}
            
        mock_check.side_effect = check_qualification_side_effect
        
        # Make a private function accessible for testing
        service._find_appropriate_classification = lambda customer, level: (
            self.silver if level == self.gold.level else self.regular
        )
        
        # Call the downgrade function directly for testing
        with patch('billing.services_classification.CustomerClassificationService.send_classification_change_notification'):
            results = service.check_for_downgrades()
        
        # Refresh customer from DB to check if classification was updated
        self.customer.refresh_from_db()
        self.assertEqual(self.customer.classification, self.silver)
        self.assertEqual(results['downgraded'], 1) 