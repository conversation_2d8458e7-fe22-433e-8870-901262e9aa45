from django.urls import path, include
from . import views

app_name = 'billing'

urlpatterns = [
    # Dashboard
    path('', views.billing_dashboard, name='dashboard'),
    path('dashboard/', views.billing_dashboard, name='dashboard'),
    path('cashier/', views.cashier_dashboard, name='cashier_dashboard'),
    
    # Customer Preferences
    path('preferences/', views.CustomerPreferenceListView.as_view(), name='customer_preference_list'),
    path('preferences/create/', views.CustomerPreferenceCreateView.as_view(), name='customer_preference_create'),
    path('preferences/<uuid:pk>/', views.CustomerPreferenceDetailView.as_view(), name='customer_preference_detail'),
    path('preferences/<uuid:pk>/edit/', views.CustomerPreferenceUpdateView.as_view(), name='customer_preference_update'),
    
    # Insurance Companies
    path('insurance-companies/', views.InsuranceCompanyListView.as_view(), name='insurance_company_list'),
    path('insurance-companies/create/', views.InsuranceCompanyCreateView.as_view(), name='insurance_company_create'),
    path('insurance-companies/<uuid:pk>/', views.InsuranceCompanyDetailView.as_view(), name='insurance_company_detail'),
    path('insurance-companies/<uuid:pk>/edit/', views.InsuranceCompanyUpdateView.as_view(), name='insurance_company_update'),
    
    # Insurance Policies
    path('insurance-policies/', views.InsurancePolicyListView.as_view(), name='insurance_policy_list'),
    path('insurance-policies/create/', views.InsurancePolicyCreateView.as_view(), name='insurance_policy_create'),
    path('insurance-policies/<uuid:pk>/', views.InsurancePolicyDetailView.as_view(), name='insurance_policy_detail'),
    path('insurance-policies/<uuid:pk>/edit/', views.InsurancePolicyUpdateView.as_view(), name='insurance_policy_update'),
    
    # Warranty Types
    path('warranty-types/', views.WarrantyTypeListView.as_view(), name='warranty_type_list'),
    path('warranty-types/create/', views.WarrantyTypeCreateView.as_view(), name='warranty_type_create'),
    path('warranty-types/<uuid:pk>/', views.WarrantyTypeDetailView.as_view(), name='warranty_type_detail'),
    path('warranty-types/<uuid:pk>/edit/', views.WarrantyTypeUpdateView.as_view(), name='warranty_type_update'),
    
    # Vehicle Warranties
    path('vehicle-warranties/', views.VehicleWarrantyListView.as_view(), name='vehicle_warranty_list'),
    path('vehicle-warranties/create/', views.VehicleWarrantyCreateView.as_view(), name='vehicle_warranty_create'),
    path('vehicle-warranties/<uuid:pk>/', views.VehicleWarrantyDetailView.as_view(), name='vehicle_warranty_detail'),
    path('vehicle-warranties/<uuid:pk>/edit/', views.VehicleWarrantyUpdateView.as_view(), name='vehicle_warranty_update'),
    
    # Discount Types
    path('discount-types/', views.DiscountTypeListView.as_view(), name='discount_type_list'),
    path('discount-types/create/', views.DiscountTypeCreateView.as_view(), name='discount_type_create'),
    path('discount-types/<uuid:pk>/', views.DiscountTypeDetailView.as_view(), name='discount_type_detail'),
    path('discount-types/<uuid:pk>/edit/', views.DiscountTypeUpdateView.as_view(), name='discount_type_update'),
    
    # Payment Methods
    path('payment-methods/', views.PaymentMethodListView.as_view(), name='payment_method_list'),
    path('payment-methods/create/', views.PaymentMethodCreateView.as_view(), name='payment_method_create'),
    path('payment-methods/<uuid:pk>/', views.PaymentMethodDetailView.as_view(), name='payment_method_detail'),
    path('payment-methods/<uuid:pk>/edit/', views.PaymentMethodUpdateView.as_view(), name='payment_method_update'),
    
    # Invoices
    path('invoices/', views.InvoiceListView.as_view(), name='invoice_list'),
    path('invoices/create/', views.InvoiceCreateView.as_view(), name='invoice_create'),
    path('invoices/<uuid:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail'),
    path('invoices/<uuid:pk>/edit/', views.InvoiceUpdateView.as_view(), name='invoice_update'),
    # Alternative URL pattern for invoice detail (singular form)
    path('invoice/<uuid:pk>/', views.InvoiceDetailView.as_view(), name='invoice_detail_singular'),
    
    # Payments
    path('payments/', views.PaymentListView.as_view(), name='payment_list'),
    path('payments/create/', views.PaymentCreateView.as_view(), name='payment_create'),
    path('payments/<uuid:pk>/', views.PaymentDetailView.as_view(), name='payment_detail'),
    path('payments/<uuid:pk>/edit/', views.PaymentUpdateView.as_view(), name='payment_update'),
    
    # Reports
    path('reports/', views.billing_reports, name='reports'),
    
    # API Endpoints
    path('api/', include([
        path('invoice-stats/', views.api_invoice_stats, name='api_invoice_stats'),
        path('payment-methods/', views.api_payment_methods, name='api_payment_methods'),
        path('calculate-discount/', views.api_calculate_discount, name='api_calculate_discount'),
        path('customer-credit/<uuid:customer_id>/', views.api_customer_credit_info, name='api_customer_credit_info'),
        # Cashier API endpoints
        path('inventory-available/', views.api_inventory_available, name='api_inventory_available'),
        path('inventory-low-stock/', views.api_inventory_low_stock, name='api_inventory_low_stock'),
        path('work-orders-ready/', views.api_work_orders_ready, name='api_work_orders_ready'),
        path('pending-invoices/', views.api_pending_invoices, name='api_pending_invoices'),
        path('printable-invoices/', views.api_printable_invoices, name='api_printable_invoices'),
        path('supply-report/', views.api_supply_report, name='api_supply_report'),
        path('work-orders/<uuid:work_order_id>/', views.api_work_order_details, name='api_work_order_details'),
        path('work-orders/<uuid:work_order_id>/create-invoice/', views.api_create_invoice_from_work_order, name='api_create_invoice_from_work_order'),
        path('create-invoice-from-work-order/', views.api_create_invoice_from_work_order_generic, name='api_create_invoice_from_work_order_generic'),
        path('invoice-details/<uuid:invoice_id>/', views.api_invoice_details, name='api_invoice_details'),
        path('invoice-details/<int:invoice_id>/', views.api_invoice_details_int, name='api_invoice_details_int'),
        path('auto-complete-work-orders/', views.api_auto_complete_work_orders, name='api_auto_complete_work_orders'),
        # New API endpoints for full cashier functionality
        path('process-payment/', views.api_process_payment, name='api_process_payment'),
        path('customer-history/<uuid:customer_id>/', views.api_customer_history, name='api_customer_history'),
        path('request-reorder/<uuid:item_id>/', views.api_request_reorder, name='api_request_reorder'),
        # Advanced Discount System APIs
        path('discount-rules/<uuid:invoice_id>/', views.api_available_discount_rules, name='api_available_discount_rules'),
        path('calculate-discount/', views.api_calculate_discount, name='api_calculate_discount'),
        path('check-discount-auth/', views.api_check_discount_authorization, name='api_check_discount_authorization'),
        path('request-discount-approval/', views.api_request_discount_approval, name='api_request_discount_approval'),
        path('approve-discount/<uuid:approval_id>/', views.api_approve_discount, name='api_approve_discount'),
        path('reject-discount/<uuid:approval_id>/', views.api_reject_discount, name='api_reject_discount'),
        path('discount-summary/<uuid:invoice_id>/', views.api_discount_summary, name='api_discount_summary'),
        path('pending-discount-approvals/', views.api_pending_discount_approvals, name='api_pending_discount_approvals'),
        
        # Sales Order Integration APIs
        path('create-invoice-from-sales-order/', views.api_create_invoice_from_sales_order, name='api_create_invoice_from_sales_order'),
        path('sales-orders-ready-for-invoicing/', views.api_sales_orders_ready_for_invoicing, name='api_sales_orders_ready_for_invoicing'),
        
        # Simplified Cashier Dashboard APIs
        path('recent-invoices/', views.api_recent_invoices, name='api_recent_invoices'),
        path('dashboard-stats/', views.api_dashboard_stats, name='api_dashboard_stats'),
        path('available-work-orders/', views.api_available_work_orders, name='api_available_work_orders'),
        path('unpaid-invoices/', views.api_unpaid_invoices, name='api_unpaid_invoices'),
        path('daily-summary/', views.api_daily_summary, name='api_daily_summary'),
        path('process-detailed-payment/', views.api_process_detailed_payment, name='api_process_detailed_payment'),
        # Enhanced Discount System APIs
        path('detect-available-offers/<uuid:invoice_id>/', views.api_detect_available_offers, name='api_detect_available_offers'),
        path('apply-hierarchical-discounts/', views.api_apply_hierarchical_discounts, name='api_apply_hierarchical_discounts'),
        path('apply-item-discount/', views.api_apply_item_discount, name='api_apply_item_discount'),
        path('apply-category-discount/', views.api_apply_category_discount, name='api_apply_category_discount'),
        path('apply-automatic-rules/', views.api_apply_automatic_rules, name='api_apply_automatic_rules'),
        path('remove-discount/<uuid:discount_id>/', views.api_remove_discount, name='api_remove_discount'),
        path('enhanced-discount-summary/<uuid:invoice_id>/', views.api_enhanced_discount_summary, name='api_enhanced_discount_summary'),
        # All invoices endpoint
        path('all-invoices/', views.api_all_invoices, name='api_all_invoices'),
    ])),
] 