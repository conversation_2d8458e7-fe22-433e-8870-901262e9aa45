from django.shortcuts import render, get_object_or_404, redirect
from core.middleware import get_current_tenant_id
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg
from django.http import JsonResponse
from core.middleware import get_current_tenant_id
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta
import json

from core.views import TenantCreateView, TenantListView, TenantDetailView, TenantUpdateView
from core.middleware import get_current_tenant_id
from .models import (
    CustomerPreference, InsuranceCompany, InsurancePolicy, WarrantyType,
    VehicleWarranty, DiscountType, PaymentMethod, Invoice, InvoiceItem, Payment
)
from .forms import (
    CustomerPreferenceForm, InsuranceCompanyForm, InsurancePolicyForm, WarrantyTypeForm,
    VehicleWarrantyForm, DiscountTypeForm, PaymentMethodForm, InvoiceForm, 
    InvoiceItemForm, PaymentForm, BillingReportForm
)

# Utility functions for dynamic configuration
def get_default_payment_terms(customer=None, tenant_id=None):
    """Get default payment terms in days"""
    if customer and hasattr(customer, 'preferences'):
        try:
            preferences = customer.preferences
            terms_mapping = {
                'cash': 0,
                'credit_30': 30,
                'credit_60': 60,
                'credit_90': 90,
            }
            return terms_mapping.get(preferences.payment_terms, 30)
        except:
            pass
    return 30  # Default fallback

def get_default_tax_rate(tenant_id=None):
    """Get default tax rate as decimal (e.g., 0.14 for 14%)"""
    try:
        # Try to get from tenant settings or app settings
        from app_settings.models import TenantProfile
        tenant_profile = TenantProfile.objects.filter(tenant_id=tenant_id).first()
        if tenant_profile and hasattr(tenant_profile, 'default_tax_rate'):
            return Decimal(str(tenant_profile.default_tax_rate / 100))
    except:
        pass
    return Decimal('0.14')  # 14% VAT default for Egypt

def get_labor_percentage(service_center=None):
    """Get labor cost percentage from service center or defaults"""
    if service_center and hasattr(service_center, 'attributes'):
        return service_center.attributes.get('labor_percentage', 60) / 100
    return 0.6  # 60% default

def get_insurance_coverage_rate(customer=None):
    """Get default insurance coverage rate"""
    if customer and hasattr(customer, 'preferences'):
        try:
            return customer.preferences.attributes.get('insurance_coverage', 80)
        except:
            pass
    return 80  # 80% default

def get_warranty_coverage_rate():
    """Get default warranty coverage rate"""
    return 100  # 100% default for warranty

@login_required
def billing_dashboard(request):
    """Billing dashboard with key metrics and recent activity"""
    
    # Get tenant_id properly
    tenant_id = get_current_tenant_id()
    
    # Get date ranges
    today = timezone.now().date()
    thirty_days_ago = today - timedelta(days=30)
    
    # Revenue metrics
    total_revenue = Invoice.objects.filter(
        tenant_id=tenant_id,
        status='paid'
    ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')
    
    monthly_revenue = Invoice.objects.filter(
        tenant_id=tenant_id,
        status='paid',
        invoice_date__gte=thirty_days_ago
    ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')
    
    # Outstanding amounts
    outstanding_amount = Invoice.objects.filter(
        tenant_id=tenant_id,
        status__in=['issued', 'partially_paid', 'overdue']
    ).aggregate(total=Sum('amount_due'))['total'] or Decimal('0')
    
    # Invoice counts
    total_invoices = Invoice.objects.filter(tenant_id=tenant_id).count()
    pending_invoices = Invoice.objects.filter(
        tenant_id=tenant_id,
        status='draft'
    ).count()
    overdue_invoices = Invoice.objects.filter(
        tenant_id=tenant_id,
        status='overdue'
    ).count()
    
    # Recent activity
    recent_invoices = Invoice.objects.filter(
        tenant_id=tenant_id
    ).order_by('-created_at')[:10]
    
    recent_payments = Payment.objects.filter(
        tenant_id=tenant_id
    ).order_by('-payment_date')[:10]
    
    # Monthly revenue chart data
    monthly_data = []
    for i in range(12):
        month_start = today.replace(day=1) - timedelta(days=30*i)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        
        month_revenue = Invoice.objects.filter(
            tenant_id=tenant_id,
            status='paid',
            invoice_date__range=[month_start, month_end]
        ).aggregate(total=Sum('total_amount'))['total'] or Decimal('0')
        
        monthly_data.append({
            'month': month_start.strftime('%B %Y'),
            'revenue': float(month_revenue)
        })
    
    monthly_data.reverse()
    
    # Calculate average invoice value
    average_invoice_value = Decimal('0.00')
    if total_invoices > 0:
        average_invoice_value = total_revenue / total_invoices
    
    context = {
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'outstanding_amount': outstanding_amount,
        'total_invoices': total_invoices,
        'pending_invoices': pending_invoices,
        'overdue_invoices': overdue_invoices,
        'recent_invoices': recent_invoices,
        'recent_payments': recent_payments,
        'monthly_data': monthly_data,
        'average_invoice_value': average_invoice_value,
    }
    
    return render(request, 'billing/dashboard.html', context)


@login_required
def cashier_dashboard(request):
    """Cashier dashboard for handling completed work orders and billing"""
    
    # Get tenant_id properly
    tenant_id = get_current_tenant_id()
    
    # Get date ranges
    today = timezone.now().date()
    
    # Work order statistics
    try:
        from work_orders.models import WorkOrder, WorkOrderMaterial
        
        # Get completed work orders that haven't been invoiced yet
        completed_work_orders_list = WorkOrder.objects.filter(
            tenant_id=tenant_id,
            status='completed',
            invoice__isnull=True
        ).select_related('customer', 'vehicle').prefetch_related('materials__item')[:20]
        
        completed_work_orders_count = WorkOrder.objects.filter(
            tenant_id=tenant_id,
            status='completed',
            invoice__isnull=True
        ).count()
        
        # Get parts used today from work orders
        parts_used_today = WorkOrderMaterial.objects.filter(
            work_order__tenant_id=tenant_id,
            work_order__actual_end_date__date=today
        ).count()
        
    except ImportError:
        completed_work_orders_list = []
        completed_work_orders_count = 0
        parts_used_today = 0
    
    # Invoice statistics
    pending_invoices_count = Invoice.objects.filter(
        tenant_id=tenant_id,
        status__in=['draft', 'issued']
    ).count()
    
    # Today's revenue from payments
    today_revenue = Payment.objects.filter(
        tenant_id=tenant_id,
        payment_date__date=today
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
    
    # Customer statistics for today
    customers_today = Invoice.objects.filter(
        tenant_id=tenant_id,
        invoice_date=today
    ).values('customer').distinct().count()
    
    services_completed = 0
    try:
        from work_orders.models import WorkOrder
        services_completed = WorkOrder.objects.filter(
            tenant_id=tenant_id,
            status='completed',
            actual_end_date__date=today
        ).count()
    except ImportError:
        pass
    
    # Average service value
    avg_service_value = Invoice.objects.filter(
        tenant_id=tenant_id,
        invoice_date=today,
        work_order__isnull=False
    ).aggregate(avg=Avg('total_amount'))['avg'] or Decimal('0')
    
    # All invoices for cashier (not just recent)
    all_invoices = Invoice.objects.filter(
        tenant_id=tenant_id
    ).select_related('customer', 'work_order').order_by('-created_at')
    
    # Recent activity (limited for dashboard display)
    recent_invoices = all_invoices[:10]
    
    recent_payments = Payment.objects.filter(
        tenant_id=tenant_id
    ).select_related('customer', 'payment_method', 'invoice').order_by('-payment_date')[:10]
    
    context = {
        'completed_work_orders': completed_work_orders_count,
        'completed_work_orders_list': completed_work_orders_list,
        'pending_invoices': pending_invoices_count,
        'today_revenue': today_revenue,
        'parts_used_today': parts_used_today,
        'customers_today': customers_today,
        'services_completed': services_completed,
        'avg_service_value': avg_service_value,
        'recent_invoices': recent_invoices,
        'all_invoices': all_invoices,
        'recent_payments': recent_payments,
    }
    
    return render(request, 'billing/cashier_dashboard.html', context)


# Customer Preference Views
class CustomerPreferenceCreateView(TenantCreateView):
    model = CustomerPreference
    form_class = CustomerPreferenceForm
    template_name = 'billing/customer_preference_form.html'
    success_url = '/billing/preferences/'

class CustomerPreferenceListView(TenantListView):
    model = CustomerPreference
    template_name = 'billing/customer_preference_list.html'
    context_object_name = 'preferences'
    paginate_by = 20

class CustomerPreferenceDetailView(TenantDetailView):
    model = CustomerPreference
    template_name = 'billing/customer_preference_detail.html'
    context_object_name = 'preference'

class CustomerPreferenceUpdateView(TenantUpdateView):
    model = CustomerPreference
    form_class = CustomerPreferenceForm
    template_name = 'billing/customer_preference_form.html'
    success_url = '/billing/preferences/'

# Insurance Company Views
class InsuranceCompanyCreateView(TenantCreateView):
    model = InsuranceCompany
    form_class = InsuranceCompanyForm
    template_name = 'billing/insurance_company_form.html'
    success_url = '/billing/insurance-companies/'

class InsuranceCompanyListView(TenantListView):
    model = InsuranceCompany
    template_name = 'billing/insurance_company_list.html'
    context_object_name = 'companies'
    paginate_by = 20

class InsuranceCompanyDetailView(TenantDetailView):
    model = InsuranceCompany
    template_name = 'billing/insurance_company_detail.html'
    context_object_name = 'company'

class InsuranceCompanyUpdateView(TenantUpdateView):
    model = InsuranceCompany
    form_class = InsuranceCompanyForm
    template_name = 'billing/insurance_company_form.html'
    success_url = '/billing/insurance-companies/'

# Insurance Policy Views
class InsurancePolicyCreateView(TenantCreateView):
    model = InsurancePolicy
    form_class = InsurancePolicyForm
    template_name = 'billing/insurance_policy_form.html'
    success_url = '/billing/insurance-policies/'

class InsurancePolicyListView(TenantListView):
    model = InsurancePolicy
    template_name = 'billing/insurance_policy_list.html'
    context_object_name = 'policies'
    paginate_by = 20

class InsurancePolicyDetailView(TenantDetailView):
    model = InsurancePolicy
    template_name = 'billing/insurance_policy_detail.html'
    context_object_name = 'policy'

class InsurancePolicyUpdateView(TenantUpdateView):
    model = InsurancePolicy
    form_class = InsurancePolicyForm
    template_name = 'billing/insurance_policy_form.html'
    success_url = '/billing/insurance-policies/'

# Warranty Type Views
class WarrantyTypeCreateView(TenantCreateView):
    model = WarrantyType
    form_class = WarrantyTypeForm
    template_name = 'billing/warranty_type_form.html'
    success_url = '/billing/warranty-types/'

class WarrantyTypeListView(TenantListView):
    model = WarrantyType
    template_name = 'billing/warranty_type_list.html'
    context_object_name = 'warranty_types'
    paginate_by = 20

class WarrantyTypeDetailView(TenantDetailView):
    model = WarrantyType
    template_name = 'billing/warranty_type_detail.html'
    context_object_name = 'warranty_type'

class WarrantyTypeUpdateView(TenantUpdateView):
    model = WarrantyType
    form_class = WarrantyTypeForm
    template_name = 'billing/warranty_type_form.html'
    success_url = '/billing/warranty-types/'

# Vehicle Warranty Views
class VehicleWarrantyCreateView(TenantCreateView):
    model = VehicleWarranty
    form_class = VehicleWarrantyForm
    template_name = 'billing/vehicle_warranty_form.html'
    success_url = '/billing/vehicle-warranties/'

class VehicleWarrantyListView(TenantListView):
    model = VehicleWarranty
    template_name = 'billing/vehicle_warranty_list.html'
    context_object_name = 'warranties'
    paginate_by = 20

class VehicleWarrantyDetailView(TenantDetailView):
    model = VehicleWarranty
    template_name = 'billing/vehicle_warranty_detail.html'
    context_object_name = 'warranty'

class VehicleWarrantyUpdateView(TenantUpdateView):
    model = VehicleWarranty
    form_class = VehicleWarrantyForm
    template_name = 'billing/vehicle_warranty_form.html'
    success_url = '/billing/vehicle-warranties/'

# Discount Type Views
class DiscountTypeCreateView(TenantCreateView):
    model = DiscountType
    form_class = DiscountTypeForm
    template_name = 'billing/discount_type_form.html'
    success_url = '/billing/discount-types/'

class DiscountTypeListView(TenantListView):
    model = DiscountType
    template_name = 'billing/discount_type_list.html'
    context_object_name = 'discount_types'
    paginate_by = 20

class DiscountTypeDetailView(TenantDetailView):
    model = DiscountType
    template_name = 'billing/discount_type_detail.html'
    context_object_name = 'discount_type'

class DiscountTypeUpdateView(TenantUpdateView):
    model = DiscountType
    form_class = DiscountTypeForm
    template_name = 'billing/discount_type_form.html'
    success_url = '/billing/discount-types/'

# Payment Method Views
class PaymentMethodCreateView(TenantCreateView):
    model = PaymentMethod
    form_class = PaymentMethodForm
    template_name = 'billing/payment_method_form.html'
    success_url = '/billing/payment-methods/'

class PaymentMethodListView(TenantListView):
    model = PaymentMethod
    template_name = 'billing/payment_method_list.html'
    context_object_name = 'payment_methods'
    paginate_by = 20

class PaymentMethodDetailView(TenantDetailView):
    model = PaymentMethod
    template_name = 'billing/payment_method_detail.html'
    context_object_name = 'payment_method'

class PaymentMethodUpdateView(TenantUpdateView):
    model = PaymentMethod
    form_class = PaymentMethodForm
    template_name = 'billing/payment_method_form.html'
    success_url = '/billing/payment-methods/'

# Invoice Views
class InvoiceCreateView(TenantCreateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'billing/invoice_form.html'
    success_url = '/billing/invoices/'

class InvoiceListView(TenantListView):
    model = Invoice
    template_name = 'billing/invoice_list.html'
    context_object_name = 'invoices'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search_query) |
                Q(customer__first_name__icontains=search_query) |
                Q(customer__last_name__icontains=search_query) |
                Q(customer__email__icontains=search_query) |
                Q(work_order__work_order_number__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status:
            queryset = queryset.filter(status=status)
        
        # Date filters
        date_from = self.request.GET.get('date_from', '')
        if date_from:
            queryset = queryset.filter(invoice_date__gte=date_from)
        
        date_to = self.request.GET.get('date_to', '')
        if date_to:
            queryset = queryset.filter(invoice_date__lte=date_to)
        
        return queryset.select_related('customer', 'work_order')

class InvoiceDetailView(TenantDetailView):
    model = Invoice
    template_name = 'billing/invoice_detail.html'
    context_object_name = 'invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        invoice = self.get_object()
        context['invoice_items'] = invoice.items.all()
        context['payments'] = invoice.payments.all()
        return context

class InvoiceUpdateView(TenantUpdateView):
    model = Invoice
    form_class = InvoiceForm
    template_name = 'billing/invoice_form.html'
    success_url = '/billing/invoices/'

# Payment Views
class PaymentCreateView(TenantCreateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'billing/payment_form.html'
    success_url = '/billing/payments/'

class PaymentListView(TenantListView):
    model = Payment
    template_name = 'billing/payment_list.html'
    context_object_name = 'payments'
    paginate_by = 20

class PaymentDetailView(TenantDetailView):
    model = Payment
    template_name = 'billing/payment_detail.html'
    context_object_name = 'payment'

class PaymentUpdateView(TenantUpdateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'billing/payment_form.html'
    success_url = '/billing/payments/'

# Report Views
@login_required
def billing_reports(request):
    """Billing reports page"""
    form = BillingReportForm(user=request.user)
    
    if request.method == 'POST':
        form = BillingReportForm(request.POST, user=request.user)
        if form.is_valid():
            # Process report generation
            report_type = form.cleaned_data['report_type']
            date_from = form.cleaned_data.get('date_from')
            date_to = form.cleaned_data.get('date_to')
            customer = form.cleaned_data.get('customer')
            status = form.cleaned_data.get('status')
            
            # Generate report data based on type
            context = generate_billing_report(
                tenant_id, report_type, 
                date_from, date_to, customer, status
            )
            context['form'] = form
            context['report_generated'] = True
            
            return render(request, 'billing/reports.html', context)
    
    return render(request, 'billing/reports.html', {'form': form})

def generate_billing_report(tenant_id, report_type, date_from=None, date_to=None, customer=None, status=None):
    """Generate billing report data"""
    
    base_queryset = Invoice.objects.filter(tenant_id=get_current_tenant_id())
    
    # Apply filters
    if date_from:
        base_queryset = base_queryset.filter(invoice_date__gte=date_from)
    if date_to:
        base_queryset = base_queryset.filter(invoice_date__lte=date_to)
    if customer:
        base_queryset = base_queryset.filter(customer=customer)
    if status:
        base_queryset = base_queryset.filter(status=status)
    
    context = {'report_type': report_type}
    
    if report_type == 'invoice_summary':
        context.update({
            'invoices': base_queryset.select_related('customer', 'work_order'),
            'total_amount': base_queryset.aggregate(total=Sum('total_amount'))['total'] or Decimal('0'),
            'count': base_queryset.count()
        })
    
    elif report_type == 'payment_summary':
        payments = Payment.objects.filter(
            tenant_id=get_current_tenant_id(),
            invoice__in=base_queryset
        ).select_related('invoice', 'customer', 'payment_method')
        
        context.update({
            'payments': payments,
            'total_amount': payments.aggregate(total=Sum('amount'))['total'] or Decimal('0'),
            'count': payments.count()
        })
    
    elif report_type == 'outstanding_invoices':
        outstanding = base_queryset.filter(
            status__in=['issued', 'partially_paid', 'overdue']
        ).select_related('customer', 'work_order')
        
        context.update({
            'invoices': outstanding,
            'total_outstanding': outstanding.aggregate(total=Sum('amount_due'))['total'] or Decimal('0'),
            'count': outstanding.count()
        })
    
    return context

# API Views
@login_required
@require_http_methods(["GET"])
def api_invoice_stats(request):
    """API endpoint for invoice statistics"""
    tenant_id = get_current_tenant_id()
    
    # Get date range
    days = int(request.GET.get('days', 30))
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=days)
    
    # Calculate stats
    stats = Invoice.objects.filter(
        tenant_id=tenant_id,
        invoice_date__range=[start_date, end_date]
    ).aggregate(
        total_amount=Sum('total_amount'),
        total_paid=Sum('amount_paid'),
        total_outstanding=Sum('amount_due'),
        count=Count('id')
    )
    
    # Status breakdown
    status_breakdown = Invoice.objects.filter(
        tenant_id=tenant_id,
        invoice_date__range=[start_date, end_date]
    ).values('status').annotate(
        count=Count('id'),
        total=Sum('total_amount')
    )
    
    return JsonResponse({
        'stats': {
            'total_amount': float(stats['total_amount'] or 0),
            'total_paid': float(stats['total_paid'] or 0),
            'total_outstanding': float(stats['total_outstanding'] or 0),
            'count': stats['count']
        },
        'status_breakdown': list(status_breakdown)
    })

@login_required
@require_http_methods(["GET"])
def api_payment_methods(request):
    """API endpoint for payment methods"""
    tenant_id = get_current_tenant_id()
    
    payment_methods = PaymentMethod.objects.filter(
        tenant_id=tenant_id,
        is_active=True
    ).values('id', 'name', 'payment_type')
    
    return JsonResponse({'payment_methods': list(payment_methods)})

@login_required
@require_http_methods(["POST"])
def api_calculate_discount(request):
    """API endpoint to calculate discount amount"""
    tenant_id = get_current_tenant_id()
    
    discount_id = request.POST.get('discount_id')
    amount = Decimal(request.POST.get('amount', '0'))
    
    if not discount_id:
        return JsonResponse({'error': 'Discount ID required'}, status=400)
    
    try:
        discount = DiscountType.objects.get(
            id=discount_id,
            tenant_id=tenant_id
        )
        
        discount_amount = discount.calculate_discount(amount)
        
        return JsonResponse({
            'discount_amount': float(discount_amount),
            'final_amount': float(amount - discount_amount)
        })
        
    except DiscountType.DoesNotExist:
        return JsonResponse({'error': 'Discount not found'}, status=404)

@login_required
@require_http_methods(["GET"])
def api_customer_credit_info(request, customer_id):
    """API endpoint for customer credit information"""
    tenant_id = get_current_tenant_id()
    
    try:
        preference = CustomerPreference.objects.get(
            customer_id=customer_id,
            tenant_id=tenant_id
        )
        
        # Calculate outstanding balance
        outstanding = Invoice.objects.filter(
            customer_id=customer_id,
            tenant_id=tenant_id,
            status__in=['issued', 'partially_paid', 'overdue']
        ).aggregate(total=Sum('amount_due'))['total'] or Decimal('0')
        
        available_credit = preference.credit_limit - outstanding
        
        return JsonResponse({
            'credit_limit': float(preference.credit_limit),
            'outstanding_balance': float(outstanding),
            'available_credit': float(available_credit),
            'payment_terms': preference.payment_terms,
            'default_discount': float(preference.default_discount_percentage)
        })
        
    except CustomerPreference.DoesNotExist:
        return JsonResponse({
            'credit_limit': 0,
            'outstanding_balance': 0,
            'available_credit': 0,
            'payment_terms': 'cash',
            'default_discount': 0
        })


@login_required
@require_http_methods(["GET"])
def api_inventory_available(request):
    """API endpoint for available inventory for supply chain integration"""
    tenant_id = get_current_tenant_id()
    
    try:
        from inventory.models import Item
        
        items = Item.objects.filter(
            tenant_id=tenant_id,
            quantity__gt=0
        ).values('id', 'name', 'sku', 'quantity', 'unit')[:50]
        
        return JsonResponse({
            'items': list(items)
        })
    except ImportError:
        return JsonResponse({
            'items': [],
            'error': 'Inventory module not available'
        })


@login_required
@require_http_methods(["GET"])
def api_inventory_low_stock(request):
    """API endpoint for low stock alerts"""
    tenant_id = get_current_tenant_id()
    
    try:
        from inventory.models import Item
        from django.db.models import F
        
        items = Item.objects.filter(
            tenant_id=tenant_id,
            quantity__lte=F('min_stock_level')
        ).values('id', 'name', 'sku', 'quantity', 'min_stock_level', 'unit')[:20]
        
        return JsonResponse({
            'items': [{
                **item,
                'min_level': item['min_stock_level']
            } for item in items]
        })
    except ImportError:
        return JsonResponse({
            'items': [],
            'error': 'Inventory module not available'
        })


@login_required  
@require_http_methods(["GET"])
def api_work_order_details(request, work_order_id):
    """API endpoint for work order details"""
    tenant_id = get_current_tenant_id()
    
    try:
        from work_orders.models import WorkOrder
        
        work_order = WorkOrder.objects.get(
            id=work_order_id,
            tenant_id=tenant_id
        )
        
        materials = []
        try:
            for material in work_order.materials.all():
                materials.append({
                    'item_name': material.item.name,
                    'quantity': float(material.quantity),
                    'unit_price': float(getattr(material, 'unit_price', None) or material.item.unit_price or 0),
                    'total_price': float(material.quantity) * float(getattr(material, 'unit_price', None) or material.item.unit_price or 0)
                })
        except Exception as e:
            print(f"Error loading materials: {e}")
            pass
        
        return JsonResponse({
            'success': True,
            'work_order': {
                'id': str(work_order.id),
                'work_order_number': work_order.work_order_number,
                'customer_name': work_order.customer.full_name if work_order.customer else work_order.customer_name,
                'customer_phone': work_order.customer.phone if work_order.customer else work_order.customer_phone,
                'vehicle_info': f"{work_order.vehicle.license_plate} - {work_order.vehicle.make} {work_order.vehicle.model}" if work_order.vehicle else '',
                'status': work_order.status,
                'estimated_cost': float(work_order.estimated_cost or 0),
                'actual_cost': float(work_order.actual_cost or 0),
                'materials': materials,
                'created_at': work_order.created_at.isoformat() if work_order.created_at else None,
                'description': work_order.description or ''
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=404)


@login_required
@require_http_methods(["POST"])
def api_create_invoice_from_work_order(request, work_order_id):
    """API endpoint to create invoice from work order"""
    tenant_id = get_current_tenant_id()
    
    try:
        from work_orders.models import WorkOrder
        import json
        
        work_order = WorkOrder.objects.get(
            id=work_order_id,
            tenant_id=tenant_id,
            status='completed'
        )
        
        if hasattr(work_order, 'invoice') and work_order.invoice:
            return JsonResponse({
                'success': False,
                'error': 'Work order already has an invoice'
            })
        
        # Create invoice
        payment_days = get_default_payment_terms(work_order.customer, tenant_id)
        invoice = Invoice.objects.create(
            tenant_id=tenant_id,
            customer=work_order.customer,
            service_center=work_order.service_center,
            work_order=work_order,
            invoice_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=payment_days),
            status='draft',
            subtotal=work_order.actual_cost or work_order.estimated_cost,
            total_amount=work_order.actual_cost or work_order.estimated_cost,
            amount_due=work_order.actual_cost or work_order.estimated_cost
        )
        
        # Create invoice items from work order materials
        try:
            for material in work_order.materials.all():
                InvoiceItem.objects.create(
                    tenant_id=tenant_id,
                    invoice=invoice,
                    item=material.item,
                    description=f"{material.item.name} - Work Order {work_order.work_order_number}",
                    quantity=material.quantity,
                    unit_price=material.unit_price or material.item.unit_price,
                    line_total=material.quantity * (material.unit_price or material.item.unit_price or 0)
                )
        except:
            pass
        
        return JsonResponse({
            'success': True,
            'invoice_number': invoice.invoice_number,
            'invoice_id': str(invoice.id)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["POST"])
def api_create_invoice_from_work_order_generic(request):
    """API endpoint to create invoice from work order (body parameter)"""
    tenant_id = get_current_tenant_id()
    
    try:
        from work_orders.models import WorkOrder
        import json
        
        data = json.loads(request.body)
        work_order_id = data.get('work_order_id')
        
        if not work_order_id:
            return JsonResponse({
                'success': False,
                'error': 'work_order_id is required'
            })
        
        work_order = WorkOrder.objects.get(
            id=work_order_id,
            tenant_id=tenant_id,
            status='completed'
        )
        
        if hasattr(work_order, 'invoice') and work_order.invoice:
            return JsonResponse({
                'success': False,
                'error': 'Work order already has an invoice'
            })
        
        # Create invoice
        payment_days = get_default_payment_terms(work_order.customer, tenant_id)
        invoice = Invoice.objects.create(
            tenant_id=tenant_id,
            customer=work_order.customer,
            service_center=work_order.service_center,
            work_order=work_order,
            invoice_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=payment_days),
            status='draft',
            subtotal=work_order.actual_cost or work_order.estimated_cost,
            total_amount=work_order.actual_cost or work_order.estimated_cost,
            amount_due=work_order.actual_cost or work_order.estimated_cost
        )
        
        # Create invoice items from work order operations first
        try:
            # Add operations as labor/service items
            for operation in work_order.operations.all():
                # Calculate labor cost using dynamic percentage
                labor_percentage = get_labor_percentage(work_order.service_center)
                labor_cost = float(work_order.estimated_cost or 100) * labor_percentage / max(1, work_order.operations.count())
                
                InvoiceItem.objects.create(
                    tenant_id=tenant_id,
                    invoice=invoice,
                    item_type='labor',
                    description=f"عمالة: {operation.description or operation.name}",
                    quantity=1,
                    unit_price=labor_cost,
                    line_total=labor_cost,
                    work_order_operation_id=operation.id
                )
        except Exception as e:
            print(f"Error creating operation items: {e}")
            pass
        
        # Create invoice items from work order materials
        try:
            for material in work_order.materials.all():
                unit_price = float(getattr(material.item, 'unit_price', 0) or 0)
                InvoiceItem.objects.create(
                    tenant_id=tenant_id,
                    invoice=invoice,
                    item_type='part',
                    description=f"{material.item.name} - Work Order {work_order.work_order_number}",
                    quantity=material.quantity,
                    unit_price=unit_price,
                    line_total=material.quantity * unit_price,
                    work_order_material_id=material.id
                )
        except Exception as e:
            print(f"Error creating material items: {e}")
            pass
        
        return JsonResponse({
            'success': True,
            'invoice_number': invoice.invoice_number,
            'invoice_id': str(invoice.id)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["POST"])
def api_auto_complete_work_orders(request):
    """API endpoint to auto complete all ready work orders"""
    tenant_id = get_current_tenant_id()
    
    try:
        from work_orders.models import WorkOrder
        import json
        
        completed_work_orders = WorkOrder.objects.filter(
            tenant_id=tenant_id,
            status='completed',
            invoice__isnull=True
        )
        
        completed_count = 0
        invoices_created = 0
        
        for work_order in completed_work_orders:
            try:
                # Create invoice
                payment_days = get_default_payment_terms(work_order.customer, tenant_id)
                invoice = Invoice.objects.create(
                    tenant_id=tenant_id,
                    customer=work_order.customer,
                    service_center=work_order.service_center,
                    work_order=work_order,
                    invoice_date=timezone.now().date(),
                    due_date=timezone.now().date() + timedelta(days=payment_days),
                    status='issued',
                    subtotal=work_order.actual_cost or work_order.estimated_cost,
                    total_amount=work_order.actual_cost or work_order.estimated_cost,
                    amount_due=work_order.actual_cost or work_order.estimated_cost
                )
                
                # Create invoice items from work order materials
                try:
                    for material in work_order.materials.all():
                        InvoiceItem.objects.create(
                            tenant_id=tenant_id,
                            invoice=invoice,
                            item=material.item,
                            description=f"{material.item.name} - Work Order {work_order.work_order_number}",
                            quantity=material.quantity,
                            unit_price=material.unit_price or material.item.unit_price,
                            line_total=material.quantity * (material.unit_price or material.item.unit_price or 0)
                        )
                except:
                    pass
                
                completed_count += 1
                invoices_created += 1
                
            except Exception as e:
                continue
        
        return JsonResponse({
            'completed': completed_count,
            'invoices_created': invoices_created
        })
        
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
def api_process_payment(request):
    """Process payment for an invoice"""
    tenant_id = get_current_tenant_id()
    
    try:
        data = json.loads(request.body)
        invoice_id = data.get('invoice_id')
        amount = Decimal(str(data.get('amount', 0)))
        payment_method_id = data.get('payment_method_id')
        payment_method_name = data.get('payment_method', '')
        reference_number = data.get('reference_number', '')
        notes = data.get('notes', '')
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Handle payment method - try to find by ID first, then by name/create if needed
        payment_method = None
        if payment_method_id:
            try:
                payment_method = PaymentMethod.objects.get(id=payment_method_id, tenant_id=tenant_id)
            except PaymentMethod.DoesNotExist:
                pass
        
        if not payment_method and payment_method_name:
            # Try to find by name or create new one
            payment_method, created = PaymentMethod.objects.get_or_create(
                name=payment_method_name,
                tenant_id=tenant_id,
                defaults={'description': f'Auto-created payment method: {payment_method_name}'}
            )
        
        if not payment_method:
            # Create default cash payment method
            payment_method, created = PaymentMethod.objects.get_or_create(
                name='نقدي',
                tenant_id=tenant_id,
                defaults={'description': 'Cash payment method'}
            )
        
        # Create payment record
        payment = Payment.objects.create(
            invoice=invoice,
            customer=invoice.customer,
            payment_method=payment_method,
            amount=amount,
            reference_number=reference_number,
            notes=notes,
            status='completed',
            tenant_id=tenant_id
        )
        
        # Update invoice payment status
        payment.update_invoice_payment()
        
        # Send notification
        try:
            from notifications.services import NotificationService
            NotificationService.create_notification(
                recipient=request.user,
                notification_type_code='payment_received',
                title=f"Payment Received - Invoice #{invoice.invoice_number}",
                message=f"Payment of {amount} EGP received for invoice #{invoice.invoice_number}",
                priority='medium',
                tenant_id=get_current_tenant_id()
            )
        except ImportError:
            pass
        
        return JsonResponse({
            'success': True,
            'message': 'Payment processed successfully',
            'payment_id': str(payment.id),
            'invoice_status': invoice.status
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)


@login_required
@require_http_methods(["GET"])
def api_customer_history(request, customer_id):
    """Get customer transaction history"""
    tenant_id = get_current_tenant_id()
    
    try:
        from setup.models import Customer
        customer = get_object_or_404(Customer, id=customer_id, tenant_id=tenant_id)
        
        # Get customer invoices
        invoices = Invoice.objects.filter(
            customer=customer,
            tenant_id=tenant_id
        ).order_by('-invoice_date')[:10]
        
        # Get payments
        payments = Payment.objects.filter(
            customer=customer,
            tenant_id=tenant_id
        ).order_by('-payment_date')[:10]
        
        # Get work orders
        work_orders = []
        try:
            from work_orders.models import WorkOrder
            work_orders = WorkOrder.objects.filter(
                customer=customer,
                tenant_id=tenant_id
            ).order_by('-created_at')[:10]
        except ImportError:
            pass
        
        return JsonResponse({
            'success': True,
            'customer': {
                'id': str(customer.id),
                'name': customer.full_name,
                'phone': customer.phone,
                'email': customer.email
            },
            'invoices': [{
                'id': str(inv.id),
                'number': inv.invoice_number,
                'date': inv.invoice_date.isoformat(),
                'amount': float(inv.total_amount),
                'status': inv.status
            } for inv in invoices],
            'payments': [{
                'id': str(pay.id),
                'date': pay.payment_date.isoformat() if pay.payment_date else None,
                'amount': float(pay.amount),
                'method': pay.payment_method.name
            } for pay in payments],
            'work_orders': [{
                'id': str(wo.id),
                'number': wo.work_order_number,
                'status': wo.status,
                'created': wo.created_at.isoformat()
            } for wo in work_orders]
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)


@login_required  
@require_http_methods(["POST"])
def api_request_reorder(request, item_id):
    """Request reorder for low stock item"""
    tenant_id = get_current_tenant_id()
    
    try:
        from inventory.models import Item
        item = get_object_or_404(Item, id=item_id, tenant_id=tenant_id)
        
        # Create purchase request notification
        try:
            from notifications.services import NotificationService
            # Get purchase managers
            purchase_managers = NotificationService.get_users_by_role(['purchase_manager'], tenant_id)
            
            for manager in purchase_managers:
                NotificationService.create_action_item(
                    assigned_to=manager,
                    action_type='approve_purchase',
                    title=f"Reorder Request - {item.name}",
                    description=f"Low stock alert: {item.name} needs reordering. Current stock: {item.quantity}",
                    priority='high',
                    related_object_type='item',
                    related_object_id=str(item.id),
                    tenant_id=tenant_id
                )
        except ImportError:
            pass
        
        return JsonResponse({
            'success': True,
            'message': f'Reorder request sent for {item.name}'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)

@login_required
@require_http_methods(["GET"])
def api_available_discount_rules(request, invoice_id):
    """Get available discount rules for an invoice"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        customer = invoice.customer
        
        # Get applicable discount rules
        rules = DiscountService.get_applicable_discount_rules(invoice, customer)
        
        rules_data = []
        for rule_data in rules:
            rule = rule_data['rule']
            rules_data.append({
                'id': str(rule.id),
                'name': rule.name,
                'description': rule.description,
                'rule_type': rule.rule_type,
                'applicable_to': rule.applicable_to,
                'discount_amount': float(rule_data['discount_amount']),
                'percentage_discount': float(rule.percentage_discount),
                'priority': rule.priority,
                'can_combine_with_manual': rule.can_combine_with_manual
            })
        
        return JsonResponse({
            'success': True,
            'rules': rules_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["POST"])
def api_calculate_discount(request):
    """Calculate discount with hierarchical logic"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        import json
        
        data = json.loads(request.body)
        invoice_id = data.get('invoice_id')
        manual_discounts = data.get('manual_discounts', [])
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Get available rules
        rules = DiscountService.get_applicable_discount_rules(invoice, invoice.customer)
        
        # Calculate hierarchical discount
        discount_result = DiscountService.calculate_hierarchical_discount(
            invoice, rules, manual_discounts
        )
        
        return JsonResponse({
            'success': True,
            'total_discount': float(discount_result['total_discount']),
            'final_amount': float(discount_result['final_amount']),
            'applied_discounts': discount_result['applied_discounts']
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["POST"])
def api_check_discount_authorization(request):
    """Check if user can apply discount or needs approval"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        from setup.models import Company
        import json
        
        data = json.loads(request.body)
        discount_amount = Decimal(str(data.get('discount_amount', 0)))
        discount_percentage = Decimal(str(data.get('discount_percentage', 0)))
        original_amount = Decimal(str(data.get('original_amount', 0)))
        
        # Get user's company (assuming from service center)
        try:
            from setup.models import ServiceCenter
            service_center = ServiceCenter.objects.filter(tenant_id=tenant_id).first()
            company = service_center.company if service_center else None
        except:
            company = Company.objects.filter(tenant_id=tenant_id).first()
        
        if not company:
            return JsonResponse({
                'success': False,
                'error': 'Company not found'
            })
        
        # Check authorization
        auth_result = DiscountService.check_discount_authorization(
            request.user, company, discount_amount, discount_percentage, original_amount
        )
        
        return JsonResponse({
            'success': True,
            'is_authorized': auth_result['is_authorized'],
            'requires_approval': auth_result['requires_approval'],
            'approval_level': auth_result['approval_level'],
            'user_limits': {
                'max_percentage': float(auth_result['user_limits']['max_percentage']),
                'max_amount': float(auth_result['user_limits']['max_amount'])
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["POST"])
def api_request_discount_approval(request):
    """Request approval for discount exceeding limits"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        import json
        
        data = json.loads(request.body)
        invoice_id = data.get('invoice_id')
        discount_data = data.get('discount_data')
        reason = data.get('reason', '')
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Create approval request
        approval_request = DiscountService.create_discount_approval_request(
            invoice, request.user, discount_data, reason
        )
        
        return JsonResponse({
            'success': True,
            'approval_request_id': str(approval_request.id),
            'message': 'تم إرسال طلب الموافقة بنجاح',
            'expires_at': approval_request.expires_at.isoformat()
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["POST"])
def api_approve_discount(request, approval_id):
    """Approve discount request"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        from .models_rules import DiscountApprovalRequest
        
        approval_request = get_object_or_404(
            DiscountApprovalRequest, 
            id=approval_id, 
            tenant_id=tenant_id,
            status='pending'
        )
        
        # Check if user has permission to approve
        user_role = DiscountService._get_user_role(request.user)
        if user_role not in ['service_center_manager', 'medium_center_manager', 'company_manager', 'large_center_service_manager']:
            return JsonResponse({
                'success': False,
                'error': 'غير مصرح لك بالموافقة على الخصومات'
            })
        
        # Approve the request
        approval_request.approve(request.user)
        
        # Apply the discount
        invoice_discount = DiscountService.apply_approved_discount(approval_request)
        
        return JsonResponse({
            'success': True,
            'message': 'تمت الموافقة على الخصم وتطبيقه',
            'discount_amount': float(invoice_discount.discount_amount)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["POST"])
def api_reject_discount(request, approval_id):
    """Reject discount request"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .models_rules import DiscountApprovalRequest
        import json
        
        data = json.loads(request.body)
        reason = data.get('reason', 'No reason provided')
        
        approval_request = get_object_or_404(
            DiscountApprovalRequest, 
            id=approval_id, 
            tenant_id=tenant_id,
            status='pending'
        )
        
        # Check if user has permission to reject
        from .services import DiscountService
        user_role = DiscountService._get_user_role(request.user)
        if user_role not in ['service_center_manager', 'medium_center_manager', 'company_manager', 'large_center_service_manager']:
            return JsonResponse({
                'success': False,
                'error': 'غير مصرح لك برفض الخصومات'
            })
        
        # Reject the request
        approval_request.reject(request.user, reason)
        
        # Send notification to requester
        try:
            from notifications.services import NotificationService
            NotificationService.create_notification(
                recipient=approval_request.requested_by,
                notification_type_code='discount_rejected',
                title=f"تم رفض طلب الخصم - فاتورة {approval_request.invoice.invoice_number}",
                message=f"تم رفض طلب الخصم. السبب: {reason}",
                tenant_id=approval_request.tenant_id
            )
        except:
            pass
        
        return JsonResponse({
            'success': True,
            'message': 'تم رفض طلب الخصم'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["GET"])
def api_discount_summary(request, invoice_id):
    """Get comprehensive discount summary for invoice"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        summary = DiscountService.get_discount_summary(invoice)
        
        # Convert Decimal to float for JSON serialization
        summary['total_discount'] = float(summary['total_discount'])
        summary['rule_based_discount'] = float(summary['rule_based_discount'])
        summary['manual_discount'] = float(summary['manual_discount'])
        
        for detail in summary['discounts_detail']:
            detail['amount'] = float(detail['amount'])
            detail['percentage'] = float(detail['percentage'])
        
        return JsonResponse({
            'success': True,
            'summary': summary
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["GET"])
def api_pending_discount_approvals(request):
    """Get pending discount approvals for managers"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .models_rules import DiscountApprovalRequest
        from .services import DiscountService
        
        # Check if user can approve discounts
        user_role = DiscountService._get_user_role(request.user)
        if user_role not in ['service_center_manager', 'medium_center_manager', 'company_manager', 'large_center_service_manager']:
            return JsonResponse({
                'success': False,
                'error': 'غير مصرح لك بمشاهدة طلبات الموافقة'
            })
        
        # Get pending approvals
        pending_approvals = DiscountApprovalRequest.objects.filter(
            tenant_id=tenant_id,
            status='pending'
        ).select_related('invoice', 'requested_by')
        
        approvals_data = []
        for approval in pending_approvals:
            approvals_data.append({
                'id': str(approval.id),
                'invoice_number': approval.invoice.invoice_number,
                'customer_name': approval.invoice.customer.full_name if approval.invoice.customer else 'Unknown',
                'discount_amount': float(approval.discount_amount),
                'discount_percentage': float(approval.discount_percentage),
                'original_amount': float(approval.original_amount),
                'requested_by': approval.requested_by.get_full_name(),
                'reason': approval.reason,
                'created_at': approval.created_at.isoformat(),
                'expires_at': approval.expires_at.isoformat() if approval.expires_at else None,
                'applicable_to': approval.applicable_to
            })
        
        return JsonResponse({
            'success': True,
            'approvals': approvals_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["GET"])
def api_invoice_details(request, invoice_id):
    """API endpoint to get invoice details"""
    tenant_id = get_current_tenant_id()
    
    try:
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Get invoice items
        items = []
        for item in invoice.items.all():
            # Determine item type based on work order details
            item_type = item.item_type if hasattr(item, 'item_type') and item.item_type else 'service'  # Use stored type or default
            is_covered_by_warranty = False
            is_covered_by_insurance = False
            
            # If no stored item_type, determine from description
            if not hasattr(item, 'item_type') or not item.item_type:
                # Check if this is a spare part or labor
                if any(keyword in item.description for keyword in ['زيت', 'فلتر', 'شمع', 'قطعة', 'جزء', 'مكون']):
                    item_type = 'part'
                elif any(keyword in item.description for keyword in ['تغيير', 'إصلاح', 'عمالة', 'خدمة', 'صيانة', 'تركيب']):
                    item_type = 'labor'
                    is_covered_by_warranty = True  # Labor often covered by warranty
                elif any(keyword in item.description for keyword in ['فحص', 'تشخيص', 'استشارة']):
                    item_type = 'service'
                    is_covered_by_insurance = True  # Diagnostic services often covered by insurance
                else:
                    # If description doesn't contain clear indicators, check if it has an associated item
                    if hasattr(item, 'item') and item.item:
                        item_type = 'part'  # Has physical item = part
                    else:
                        item_type = 'labor'  # No physical item = service/labor
            
            # Check for warranty coverage (items under 6 months old or specific parts)
            if item_type == 'part' and ('فلتر' in item.description or 'زيت' in item.description):
                is_covered_by_warranty = False  # Consumables not covered
            elif item_type == 'part':
                is_covered_by_warranty = True  # Other parts might be covered
                
            items.append({
                'id': item.id,
                'description': item.description,
                'quantity': float(item.quantity),
                'unit_price': float(item.unit_price),
                'total': float(item.line_total),
                'line_total': float(item.line_total),
                'discount_amount': float(getattr(item, 'discount_amount', 0)),
                'discount_percentage': float(getattr(item, 'discount_percentage', 0)),
                'item_type': item_type,
                'is_covered_by_warranty': is_covered_by_warranty,
                'is_covered_by_insurance': is_covered_by_insurance,
                'warranty_period': '12 شهر' if is_covered_by_warranty else None,
                'insurance_coverage_percent': get_insurance_coverage_rate(invoice.customer) if is_covered_by_insurance else 0
            })

        # Get work order operations if available
        operations = []
        vehicle_info = ''
        if invoice.work_order:
            try:
                # Get vehicle information
                if invoice.work_order.vehicle:
                    vehicle = invoice.work_order.vehicle
                    vehicle_info = f"{vehicle.license_plate} - {vehicle.make} {vehicle.model} ({vehicle.year or 'غير محدد'})"
                
                # Get actual work order operations first
                for operation in invoice.work_order.operations.all():
                    operations.append({
                        'id': operation.id,
                        'description': operation.description or operation.name,
                        'duration': f"{operation.duration_minutes or 0} دقيقة",
                        'cost': float(invoice.subtotal * get_labor_percentage(invoice.service_center) / max(1, invoice.work_order.operations.count())),  # Distribute labor percentage to operations
                        'warranty_covered': True,
                        'completed': operation.is_completed
                    })
                
                # If no operations, check work order materials for service descriptions
                if not operations:
                    for material in invoice.work_order.materials.all():
                        if any(keyword in (material.item.name or '') for keyword in ['عمالة', 'تغيير', 'إصلاح', 'خدمة', 'صيانة']):
                            unit_price = 0
                            try:
                                unit_price = float(material.item.unit_price or 0)
                            except (AttributeError, ValueError):
                                unit_price = 0
                            
                            operations.append({
                                'id': material.id,
                                'description': f"تطبيق {material.item.name}",
                                'duration': f"{float(material.quantity)} وحدة",
                                'cost': unit_price * float(material.quantity),
                                'warranty_covered': True,
                                'completed': True
                            })
                
                # If still no operations, try to derive from invoice items
                if not operations:
                    # Check if invoice has service/labor items
                    service_items = [item for item in items if item.get('item_type') in ['labor', 'service']]
                    if service_items:
                        for service_item in service_items:
                            operations.append({
                                'id': f"service_{service_item['id']}",
                                'description': service_item['description'],
                                'duration': 'حسب الحاجة',
                                'cost': service_item['total'],
                                'warranty_covered': service_item.get('is_covered_by_warranty', True),
                                'completed': True
                            })
                    else:
                        # Calculate labor cost from the difference between total and parts
                        parts_total = sum(item['total'] for item in items if item.get('item_type') == 'part')
                        labor_cost = float(invoice.subtotal) - parts_total
                        
                        if labor_cost > 0:
                            operations.append({
                                'id': 'calculated_labor',
                                'description': 'خدمات الصيانة والعمالة',
                                'duration': 'حسب الحاجة',
                                'cost': labor_cost,
                                'warranty_covered': True,
                                'completed': True
                            })
                        else:
                            # Fallback to percentage-based calculation
                            operations.append({
                                'id': 'general',
                                'description': invoice.work_order.description or 'خدمات الصيانة العامة',
                                'duration': 'حسب الحاجة',
                                'cost': float(invoice.subtotal * get_labor_percentage(invoice.service_center)),  # Dynamic labor percentage
                                'warranty_covered': True,
                                'completed': True
                            })
            except Exception as e:
                print(f"Error loading operations: {e}")
                pass
        
        # If still no operations found (even without work order), calculate from invoice items
        if not operations:
            # Check if invoice has service/labor items
            service_items = [item for item in items if item.get('item_type') in ['labor', 'service']]
            if service_items:
                for service_item in service_items:
                    operations.append({
                        'id': f"invoice_service_{service_item['id']}",
                        'description': service_item['description'],
                        'duration': 'حسب الحاجة',
                        'cost': service_item['total'],
                        'warranty_covered': service_item.get('is_covered_by_warranty', True),
                        'completed': True
                    })
            else:
                # Calculate operations from total minus parts
                parts_total = sum(item['total'] for item in items if item.get('item_type') == 'part')
                labor_cost = float(invoice.subtotal) - parts_total
                
                if labor_cost > 0:
                    operations.append({
                        'id': 'calculated_operations',
                        'description': f'خدمات الصيانة والعمالة (المحسوبة: {labor_cost:.2f} ج.م)',
                        'duration': 'حسب الحاجة',
                        'cost': labor_cost,
                        'warranty_covered': True,
                        'completed': True
                    })
        
        # Get payments
        payments = []
        for payment in invoice.payments.all():
            payments.append({
                'id': payment.id,
                'amount': float(payment.amount),
                'payment_date': payment.payment_date.strftime('%Y-%m-%d %H:%M'),
                'payment_method': payment.payment_method.name if payment.payment_method else 'نقدي',
                'payment_method_name': payment.payment_method.name if payment.payment_method else 'نقدي',
                'reference_number': payment.reference_number or ''
            })
        
        # Get applied discounts
        applied_discounts = []
        for discount in invoice.applied_discounts.all():
            applied_discounts.append({
                'id': discount.id,
                'reason': discount.reason or 'خصم',
                'percentage': float(discount.percentage) if discount.percentage else 0,
                'fixed_amount': float(discount.fixed_amount) if discount.fixed_amount else 0,
                'discount_amount': float(discount.discount_amount),
                'discount_source': discount.get_discount_source_display() if hasattr(discount, 'get_discount_source_display') else discount.discount_source
            })
        
        # Get service center information
        service_center_name = ''
        service_center_address = ''
        service_center_phone = ''
        if invoice.service_center:
            service_center_name = invoice.service_center.name
            service_center_address = getattr(invoice.service_center, 'address', '')
            service_center_phone = getattr(invoice.service_center, 'phone', '')
        
        # Get customer address
        customer_address = ''
        if invoice.customer:
            customer_address = getattr(invoice.customer, 'address', '') or getattr(invoice.customer, 'billing_address', '')
        
        # Get work order completion date - try multiple sources
        work_order_completion_date = None
        if invoice.work_order:
            # Try actual_end_date first
            if hasattr(invoice.work_order, 'actual_end_date') and invoice.work_order.actual_end_date:
                work_order_completion_date = invoice.work_order.actual_end_date
            # Fall back to completed_at if available
            elif hasattr(invoice.work_order, 'completed_at') and invoice.work_order.completed_at:
                work_order_completion_date = invoice.work_order.completed_at
            # Fall back to updated_at if work order is completed
            elif invoice.work_order.status == 'completed' and hasattr(invoice.work_order, 'updated_at'):
                work_order_completion_date = invoice.work_order.updated_at
            # Fall back to created_at if work order is completed
            elif invoice.work_order.status == 'completed' and hasattr(invoice.work_order, 'created_at'):
                work_order_completion_date = invoice.work_order.created_at
        
        # Get insurance and warranty details
        insurance_details = None
        warranty_details = None
        
        # Check if there are any coverage details stored in invoice attributes
        if hasattr(invoice, 'attributes') and invoice.attributes:
            coverage = invoice.attributes.get('coverage', {})
            if coverage.get('insurance'):
                insurance_details = coverage['insurance']
            if coverage.get('warranty'):
                warranty_details = coverage['warranty']
        
        # Calculate insurance and warranty amounts
        insurance_amount = sum(item['total'] * (item['insurance_coverage_percent'] / 100) for item in items if item['is_covered_by_insurance'])
        warranty_amount = sum(item['total'] for item in items if item['is_covered_by_warranty'])
        
        invoice_data = {
            'id': invoice.id,
            'invoice_number': invoice.invoice_number,
            'customer_name': invoice.customer.full_name if invoice.customer else 'عميل غير محدد',
            'customer_phone': invoice.customer.phone if invoice.customer else '',
            'customer_address': customer_address,
            'invoice_date': invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else timezone.now().date().strftime('%Y-%m-%d'),
            'due_date': invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else None,
            'status': invoice.status,
            'subtotal': float(invoice.subtotal),
            'tax_amount': float(invoice.tax_amount),
            'tax_percentage': float(invoice.tax_percentage) if invoice.tax_percentage else 0,
            'discount_amount': float(invoice.discount_amount),
            'insurance_amount': float(insurance_amount),
            'warranty_amount': float(warranty_amount),
            'total_amount': float(invoice.total_amount),
            'amount_paid': float(invoice.amount_paid),
            'amount_due': float(invoice.amount_due),
            'work_order_id': invoice.work_order.id if invoice.work_order else None,
            'work_order_number': invoice.work_order.work_order_number if invoice.work_order else None,
            'work_order_completion_date': work_order_completion_date.strftime('%Y-%m-%d') if work_order_completion_date else None,
            'vehicle_info': vehicle_info,
            'service_center_name': service_center_name,
            'service_center_address': service_center_address,
            'service_center_phone': service_center_phone,
            'insurance_details': insurance_details,
            'warranty_details': warranty_details,
            'applied_discounts': applied_discounts,
            'items': items,
            'operations': operations,
            'payments': payments,
            'notes': invoice.notes or '',
            'terms_and_conditions': getattr(invoice, 'terms_and_conditions', '') or '',
            'processing_fees': 0.0,  # Can be calculated based on payment methods
            'net_amount_due': float(invoice.amount_due) - insurance_amount - warranty_amount
        }
        
        return JsonResponse({
            'success': True,
            'invoice': invoice_data
        })
        
    except Invoice.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Invoice not found'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_http_methods(["GET"])
def api_work_orders_ready(request):
    """Get work orders ready for invoicing"""
    tenant_id = get_current_tenant_id()
    
    try:
        from work_orders.models import WorkOrder
        
        work_orders = WorkOrder.objects.filter(
            tenant_id=tenant_id,
            status='completed',
            invoice__isnull=True
        ).select_related('customer', 'vehicle')[:20]
        
        work_orders_data = []
        for wo in work_orders:
            work_orders_data.append({
                'id': str(wo.id),
                'work_order_number': wo.work_order_number,
                'customer_name': wo.customer.full_name if wo.customer else wo.customer_name,
                'customer_phone': wo.customer.phone if wo.customer else wo.customer_phone,
                'vehicle_info': f"{wo.vehicle.license_plate} - {wo.vehicle.make} {wo.vehicle.model}" if wo.vehicle else '',
                'estimated_cost': float(wo.estimated_cost or 0),
                'actual_cost': float(wo.actual_cost or 0),
                'created_at': wo.created_at.isoformat() if wo.created_at else None
            })
        
        return JsonResponse({
            'success': True,
            'work_orders': work_orders_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'work_orders': []
        })

@login_required
@require_http_methods(["GET"])
def api_pending_invoices(request):
    """Get pending invoices for payment processing"""
    tenant_id = get_current_tenant_id()
    
    try:
        pending_invoices = Invoice.objects.filter(
            tenant_id=tenant_id,
            status__in=['draft', 'issued'],
            amount_due__gt=0
        ).select_related('customer')[:20]
        
        invoices_data = []
        for invoice in pending_invoices:
            invoices_data.append({
                'id': str(invoice.id),
                'invoice_number': invoice.invoice_number,
                'customer_name': invoice.customer.full_name if invoice.customer else 'Unknown',
                'customer_phone': invoice.customer.phone if invoice.customer else '',
                'total_amount': float(invoice.total_amount or 0),
                'amount_due': float(invoice.amount_due or 0),
                'invoice_date': invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                'due_date': invoice.due_date.isoformat() if invoice.due_date else None
            })
        
        return JsonResponse({
            'success': True,
            'invoices': invoices_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'invoices': []
        })

@login_required
@require_http_methods(["GET"])
def api_printable_invoices(request):
    """Get invoices available for printing"""
    tenant_id = get_current_tenant_id()
    
    try:
        printable_invoices = Invoice.objects.filter(
            tenant_id=tenant_id,
            status__in=['issued', 'paid', 'partial_paid']
        ).select_related('customer').order_by('-created_at')[:50]
        
        invoices_data = []
        for invoice in printable_invoices:
            invoices_data.append({
                'id': str(invoice.id),
                'invoice_number': invoice.invoice_number,
                'customer_name': invoice.customer.full_name if invoice.customer else 'Unknown',
                'total_amount': float(invoice.total_amount or 0),
                'status': invoice.status,
                'invoice_date': invoice.invoice_date.isoformat() if invoice.invoice_date else None
            })
        
        return JsonResponse({
            'success': True,
            'invoices': invoices_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'invoices': []
        })

@login_required
@require_http_methods(["GET", "POST"])
def api_supply_report(request):
    """Get supply chain report data"""
    tenant_id = get_current_tenant_id()
    
    if request.method == 'GET':
        # Get real data from inventory
        try:
            from inventory.models import Item
            from purchases.models import PurchaseOrder
            from django.db.models import F
            
            # Get low stock items
            low_stock_items = Item.objects.filter(
                tenant_id=tenant_id,
                quantity__lte=F('min_stock_level')
            ).values('name', 'quantity', 'min_stock_level')[:10]
            
            low_stock_list = [{
                'name': item['name'],
                'current_stock': item['quantity'],
                'min_stock': item['min_stock_level']
            } for item in low_stock_items]
            
            # Get pending purchase orders
            pending_orders = PurchaseOrder.objects.filter(
                tenant_id=tenant_id,
                status__in=['draft', 'sent', 'approved']
            ).count()
            
            # Get total active suppliers
            from setup.models import Supplier
            total_suppliers = Supplier.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).count()
            
            # Get items that need reordering
            items_need_reorder = Item.objects.filter(
                tenant_id=tenant_id,
                quantity__lte=F('min_stock_level')
            ).count()
            
        except ImportError:
            # Fallback to minimal data if modules not available
            low_stock_list = []
            pending_orders = 0
            total_suppliers = 0
            items_need_reorder = 0
        
        return JsonResponse({
            'success': True,
            'report': {
                'low_stock_items': low_stock_list,
                'pending_orders': pending_orders,
                'total_suppliers': total_suppliers,
                'items_need_reorder': items_need_reorder
            }
        })
    
    # Handle POST request for generating report
    try:
        import json
        data = json.loads(request.body)
        report_type = data.get('report_type', 'summary')
        
        # Generate different types of reports based on request
        if report_type == 'detailed':
            return JsonResponse({
                'success': True,
                'message': 'تم إنشاء التقرير المفصل بنجاح',
                'report_file': '/reports/supply_detailed_report.pdf'
            })
        
        return JsonResponse({
            'success': True,
            'message': 'تم إنشاء التقرير بنجاح',
            'report_file': '/reports/supply_summary_report.pdf'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'خطأ في إنشاء التقرير: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def api_create_invoice_from_sales_order(request):
    """Create invoice from confirmed sales order"""
    tenant_id = get_current_tenant_id()
    
    try:
        import json
        from sales.models import SalesOrder, SalesOrderItem
        from datetime import date, timedelta
        
        data = json.loads(request.body)
        sales_order_id = data.get('sales_order_id')
        
        if not sales_order_id:
            return JsonResponse({
                'success': False,
                'error': 'sales_order_id is required'
            })
        
        # Get sales order
        sales_order = get_object_or_404(
            SalesOrder,
            id=sales_order_id,
            tenant_id=tenant_id,
            status='confirmed'
        )
        
        # Check if invoice already exists for this sales order
        existing_invoice = Invoice.objects.filter(
            sales_order=sales_order,
            tenant_id=tenant_id
        ).first()
        
        if existing_invoice:
            return JsonResponse({
                'success': False,
                'error': f'Invoice already exists: {existing_invoice.invoice_number}'
            })
        
        # Create invoice from sales order
        invoice = Invoice.objects.create(
            tenant_id=tenant_id,
            work_order=sales_order.work_order,  # Link to original work order
            sales_order=sales_order,  # NEW: Link to sales order
            customer=sales_order.customer,
            service_center=sales_order.service_center,
            invoice_date=date.today(),
            due_date=date.today() + timedelta(days=get_default_payment_terms(sales_order.customer, tenant_id)),
            status='issued',
            subtotal=sales_order.total_amount,
            tax_percentage=float(get_default_tax_rate(tenant_id)) * 100,  # Convert to percentage
            total_amount=sales_order.total_amount * (1 + get_default_tax_rate(tenant_id)),  # Add dynamic tax
            amount_due=sales_order.total_amount * (1 + get_default_tax_rate(tenant_id)),
            notes=f"Generated from Sales Order: {sales_order.order_number}"
        )
        
        # Create invoice items from sales order items
        for so_item in sales_order.items.all():
            item_type = 'part' if so_item.item_type == 'parts' else 'labor'
            
            invoice_item = InvoiceItem.objects.create(
                tenant_id=tenant_id,
                invoice=invoice,
                item_type=item_type,
                description=so_item.item.name if so_item.item else so_item.operation_description,
                quantity=so_item.quantity,
                unit_price=so_item.unit_price,
                discount_amount=so_item.discount,
                tax_percentage=float(get_default_tax_rate(tenant_id)) * 100,  # Dynamic VAT
                part_id=so_item.item.sku if so_item.item and hasattr(so_item.item, 'sku') else '',
                work_order_material_id=so_item.work_order_material_id,
                work_order_operation_id=so_item.work_order_operation_id
            )
            
            # Calculate totals for this item
            invoice_item.calculate_totals()
        
        # Recalculate invoice totals
        invoice.calculate_totals()
        
        # Update sales order status to delivered
        sales_order.status = 'delivered'
        sales_order.save()
        
        return JsonResponse({
            'success': True,
            'invoice_number': invoice.invoice_number,
            'invoice_id': str(invoice.id),
            'total_amount': float(invoice.total_amount),
            'subtotal': float(invoice.subtotal),
            'tax_amount': float(invoice.tax_amount)
        })
        
    except Exception as e:
        print(f"Error creating invoice from sales order: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'خطأ في إنشاء الفاتورة: {str(e)}'
        })


@login_required
@require_http_methods(["GET"])
def api_sales_orders_ready_for_invoicing(request):
    """Get confirmed sales orders ready for invoicing"""
    tenant_id = get_current_tenant_id()
    
    try:
        from sales.models import SalesOrder
        
        # Get confirmed sales orders that don't have invoices yet
        ready_sales_orders = SalesOrder.objects.filter(
            tenant_id=tenant_id,
            status='confirmed'
        ).select_related(
            'customer', 'work_order', 'vehicle', 'service_center'
        ).prefetch_related('items__item')
        
        # Filter out those that already have invoices
        invoiced_sales_order_ids = Invoice.objects.filter(
            tenant_id=tenant_id,
            sales_order__isnull=False
        ).values_list('sales_order_id', flat=True)
        
        ready_sales_orders = ready_sales_orders.exclude(
            id__in=invoiced_sales_order_ids
        ).order_by('-order_date')[:20]
        
        orders_data = []
        for order in ready_sales_orders:
            # Count items by type
            labor_items = order.items.filter(item_type='labor').count()
            parts_items = order.items.filter(item_type='parts').count()
            
            orders_data.append({
                'id': str(order.id),
                'order_number': order.order_number,
                'customer_name': f"{order.customer.first_name} {order.customer.last_name}" if order.customer else 'غير محدد',
                'customer_phone': order.customer.phone if order.customer else '',
                'order_date': order.order_date.isoformat(),
                'total_amount': float(order.total_amount),
                'labor_cost': float(order.labor_cost),
                'parts_cost': float(order.parts_cost),
                'work_order_number': order.work_order_number,
                'vehicle_info': f"{order.vehicle.make} {order.vehicle.model}" if order.vehicle else 'غير محدد',
                'service_type': order.get_service_type_display() if order.service_type else 'خدمة عامة',
                'service_center': order.service_center.name if order.service_center else 'غير محدد',
                'items_count': {
                    'labor': labor_items,
                    'parts': parts_items,
                    'total': labor_items + parts_items
                }
            })
        
        return JsonResponse({
            'success': True,
            'sales_orders': orders_data
        })
        
    except Exception as e:
        print(f"Error fetching sales orders ready for invoicing: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'خطأ في تحميل طلبات المبيعات الجاهزة للفوترة'
        })


# ==========================================
# SIMPLIFIED CASHIER DASHBOARD API ENDPOINTS
# ==========================================

@login_required
@require_http_methods(["GET"])
def api_recent_invoices(request):
    """Get recent invoices for dashboard"""
    tenant_id = get_current_tenant_id()
    
    try:
        # Handle case where tenant_id is None (development/testing scenario)
        if not tenant_id:
            # Get any tenant_id from existing invoices as fallback
            first_invoice = Invoice.objects.first()
            if first_invoice:
                tenant_id = first_invoice.tenant_id
        
        if tenant_id:
            invoices = Invoice.objects.filter(
                tenant_id=tenant_id
            ).select_related('customer').order_by('-created_at')
        else:
            # If still no tenant_id, return all invoices (for development)
            invoices = Invoice.objects.select_related('customer').order_by('-created_at')
        
        invoice_data = []
        for invoice in invoices:
            status = 'paid' if invoice.amount_due <= 0 else ('overdue' if invoice.status == 'overdue' else 'issued')
            
            invoice_data.append({
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'customer_name': invoice.customer.full_name if invoice.customer else 'عميل غير محدد',
                'total_amount': float(invoice.total_amount),
                'amount_due': float(invoice.amount_due),
                'status': status,
                'invoice_date': invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else '',
                'created_at': invoice.created_at.strftime('%Y-%m-%d %H:%M')
            })
        
        return JsonResponse({
            'success': True,
            'invoices': invoice_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["GET"])
def api_dashboard_stats(request):
    """Get dashboard statistics"""
    tenant_id = get_current_tenant_id()
    today = timezone.now().date()
    
    # Today's invoices
    today_invoices = Invoice.objects.filter(
        tenant_id=tenant_id,
        invoice_date=today
    ).count()
    
    # Today's revenue
    today_revenue = Payment.objects.filter(
        tenant_id=tenant_id,
        payment_date__date=today
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
    
    # Pending payments
    pending_payments = Invoice.objects.filter(
        tenant_id=tenant_id,
        amount_due__gt=0
    ).count()
    
    # Total customers
    total_customers = Invoice.objects.filter(
        tenant_id=tenant_id
    ).values('customer').distinct().count()
    
    return JsonResponse({
        'success': True,
        'today_invoices': today_invoices,
        'today_revenue': float(today_revenue),
        'pending_payments': pending_payments,
        'total_customers': total_customers
    })
    

@login_required
@require_http_methods(["GET"])
def api_available_work_orders(request):
    """Get available work orders for invoice creation"""
    tenant_id = get_current_tenant_id()
    
    try:
        # Import work orders
        from work_orders.models import WorkOrder
        
        work_orders = WorkOrder.objects.filter(
            tenant_id=tenant_id,
            status='completed',
            invoice__isnull=True
        ).select_related('customer')[:20]
        
        work_order_data = []
        for wo in work_orders:
            work_order_data.append({
                'id': wo.id,
                'work_order_number': wo.work_order_number,
                'customer_name': wo.customer.full_name if wo.customer else wo.customer_name,
                'estimated_cost': float(wo.estimated_cost or 0),
                'status': wo.status,
                'completion_date': wo.actual_end_date.strftime('%Y-%m-%d') if wo.actual_end_date else None
            })
        
        return JsonResponse({
            'success': True,
            'work_orders': work_order_data
        })
        
    except ImportError:
        return JsonResponse({
            'success': True,
            'work_orders': []
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["GET"])
def api_unpaid_invoices(request):
    """Get unpaid invoices for payment processing"""
    tenant_id = get_current_tenant_id()
    
    try:
        invoices = Invoice.objects.filter(
            tenant_id=tenant_id,
            amount_due__gt=0
        ).select_related('customer').order_by('-created_at')[:20]
        
        invoice_data = []
        for invoice in invoices:
            invoice_data.append({
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'customer_name': invoice.customer.full_name if invoice.customer else 'عميل غير محدد',
                'total_amount': float(invoice.total_amount),
                'amount_due': float(invoice.amount_due),
                'invoice_date': invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else ''
            })
        
        return JsonResponse({
            'success': True,
            'invoices': invoice_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["GET"])
def api_daily_summary(request):
    """Get daily summary for dashboard"""
    tenant_id = get_current_tenant_id()
    today = timezone.now().date()
    
    # Today's statistics
    today_invoices = Invoice.objects.filter(
        tenant_id=tenant_id,
        invoice_date=today
    ).count()
    
    today_revenue = Payment.objects.filter(
        tenant_id=tenant_id,
        payment_date__date=today
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
    
    today_payments = Payment.objects.filter(
        tenant_id=tenant_id,
        payment_date__date=today
    ).count()
    
    new_customers = Invoice.objects.filter(
        tenant_id=tenant_id,
        invoice_date=today
    ).values('customer').distinct().count()
    
    return JsonResponse({
        'success': True,
        'today_invoices': today_invoices,
        'today_revenue': float(today_revenue),
        'today_payments': today_payments,
        'new_customers': new_customers
    })
    

@csrf_exempt
@require_http_methods(["POST"])
def api_process_detailed_payment(request):
    """Process detailed payment with split methods, discounts, insurance, and warranty"""
    tenant_id = get_current_tenant_id()
    
    try:
        data = json.loads(request.body)
        invoice_id = data.get('invoice_id')
        payment_methods = data.get('payment_methods', [])
        applied_discounts = data.get('applied_discounts', [])
        insurance_details = data.get('insurance_details')
        warranty_details = data.get('warranty_details')
        total_amount = Decimal(str(data.get('total_amount', 0)))
        
        print(f"🔄 Processing payment for invoice {invoice_id}")
        print(f"💳 Payment methods: {len(payment_methods)}")
        print(f"💰 Total amount: {total_amount}")
        print(f"📋 Full payment data received: {data}")
        
        # Debug validation conditions
        print(f"🔍 Validation checks:")
        print(f"   - invoice_id exists: {bool(invoice_id)}")
        print(f"   - payment_methods not empty: {bool(payment_methods)}")
        print(f"   - total_amount > 0: {total_amount > Decimal('0')} (value: {total_amount})")
        
        if not invoice_id:
            print("❌ Validation failed: Missing invoice_id")
            return JsonResponse({
                'success': False,
                'error': 'معرف الفاتورة مطلوب'
            }, status=400)
            
        if not payment_methods:
            print("❌ Validation failed: Missing payment_methods")
            return JsonResponse({
                'success': False,
                'error': 'طرق الدفع مطلوبة'
            }, status=400)
            
        # Allow 0 total_amount for full payment scenarios
        if total_amount < Decimal('0'):
            print("❌ Validation failed: Negative total_amount")
            return JsonResponse({
                'success': False,
                'error': 'مبلغ الدفع لا يمكن أن يكون سالباً'
            }, status=400)
        
        # Handle demo data (integer IDs) vs real database data (UUIDs)
        try:
            # Try to get from database first
            tenant_id = get_current_tenant_id()
            try:
                invoice = Invoice.objects.get(id=invoice_id, tenant_id=tenant_id)
            except Invoice.DoesNotExist:
                # This will trigger the fallback to demo mode
                raise ValueError("Demo mode - invoice not in database")
            
            # Process real database invoice
            # Process each payment method
            payment_records = []
            for method_data in payment_methods:
                if Decimal(str(method_data.get('amount', 0))) > Decimal('0'):
                    # Get or create payment method
                    method_type = method_data.get('type', 'cash')
                    
                    # Get payment method mappings from database or use defaults
                    def get_payment_method_mapping():
                        try:
                            # Try to get mappings from tenant settings
                            from app_settings.models import TenantProfile
                            tenant_profile = TenantProfile.objects.filter(tenant_id=tenant_id).first()
                            if tenant_profile and hasattr(tenant_profile, 'payment_method_mappings'):
                                return tenant_profile.payment_method_mappings
                        except:
                            pass
                        # Default mappings
                        return {
                            'cash': 'نقدي',
                            'credit_card': 'فيزا',
                            'debit_card': 'ماستر كارد',
                            'bank_transfer': 'تحويل بنكي',
                            'check': 'شيك',
                            'digital_wallet': 'محفظة إلكترونية'
                        }
                    
                    type_mapping = get_payment_method_mapping()
                    
                    method_name = type_mapping.get(method_type, method_type)
                    
                    # Try to find existing payment method, create if not found
                    try:
                        payment_method = PaymentMethod.objects.filter(
                            tenant_id=tenant_id,
                            name=method_name,
                            is_active=True
                        ).first()
                        
                        if not payment_method:
                            payment_method = PaymentMethod.objects.create(
                                tenant_id=tenant_id,
                                name=method_name,
                                payment_type=method_type,
                                is_active=True,
                                description=f'طريقة دفع {method_name}'
                            )
                            print(f"✅ Created new payment method: {method_name}")
                        else:
                            print(f"✅ Using existing payment method: {method_name}")
                    except Exception as e:
                        print(f"⚠️ Payment method error: {e}")
                        # Fallback: create a default cash payment method
                        payment_method = PaymentMethod.objects.filter(
                            tenant_id=tenant_id,
                            payment_type='cash',
                            is_active=True
                        ).first()
                        
                        if not payment_method:
                            payment_method = PaymentMethod.objects.create(
                                tenant_id=tenant_id,
                                name='نقدي',
                                payment_type='cash',
                                is_active=True,
                                description='طريقة دفع نقدي'
                            )
                    
                    # Create payment record
                    payment = Payment.objects.create(
                        invoice=invoice,
                        customer=invoice.customer,
                        amount=Decimal(str(method_data['amount'])),
                        payment_method=payment_method,
                        reference_number=method_data.get('reference', ''),
                        notes=method_data.get('notes', ''),
                        status='completed',  # Set status to completed
                        tenant_id=tenant_id
                    )
                    payment_records.append(payment)
            
            # Apply discounts
            total_discount_amount = Decimal('0')
            for discount_data in applied_discounts:
                total_discount_amount += Decimal(str(discount_data.get('amount', 0)))
            
            # Update invoice with new amounts
            invoice.discount_amount = (invoice.discount_amount or Decimal('0')) + total_discount_amount
            
            # Calculate insurance and warranty coverage
            insurance_amount = Decimal('0')
            warranty_amount = Decimal('0')
            
            if insurance_details:
                default_coverage = get_insurance_coverage_rate(invoice.customer)
                coverage_percentage = Decimal(str(insurance_details.get('coverage', default_coverage))) / Decimal('100')
                insurance_amount = invoice.subtotal * coverage_percentage
            
            if warranty_details:
                default_warranty = get_warranty_coverage_rate()
                coverage_percentage = Decimal(str(warranty_details.get('coverage', default_warranty))) / Decimal('100')
                warranty_amount = invoice.subtotal * coverage_percentage
            
            # Update invoice amounts
            total_paid = sum(Decimal(str(p.amount)) for p in payment_records)
            print(f"💵 Total paid: {total_paid} (type: {type(total_paid)})")
            print(f"📄 Invoice amount_paid before: {invoice.amount_paid} (type: {type(invoice.amount_paid)})")
            invoice.amount_paid = (invoice.amount_paid or Decimal('0')) + total_paid
            print(f"📄 Invoice amount_paid after: {invoice.amount_paid} (type: {type(invoice.amount_paid)})")
            
            # Recalculate total with discounts and coverage
            print(f"🧮 Calculation inputs:")
            print(f"   Subtotal: {invoice.subtotal} (type: {type(invoice.subtotal)})")
            print(f"   Discount: {total_discount_amount} (type: {type(total_discount_amount)})")
            print(f"   Insurance: {insurance_amount} (type: {type(insurance_amount)})")
            print(f"   Warranty: {warranty_amount} (type: {type(warranty_amount)})")
            
            adjusted_total = invoice.subtotal - total_discount_amount - insurance_amount - warranty_amount
            tax_rate = get_default_tax_rate(tenant_id)
            tax_amount = adjusted_total * tax_rate
            invoice.tax_amount = tax_amount
            invoice.total_amount = adjusted_total + tax_amount
            invoice.amount_due = max(Decimal('0'), invoice.total_amount - invoice.amount_paid)
            
            print(f"✅ Calculation results:")
            print(f"   Adjusted total: {adjusted_total}")
            print(f"   Tax amount: {tax_amount}")
            print(f"   Total amount: {invoice.total_amount}")
            print(f"   Amount due: {invoice.amount_due}")
            
            # Update status
            if invoice.amount_due <= Decimal('0'):
                invoice.status = 'paid'
            elif invoice.amount_paid > Decimal('0'):
                invoice.status = 'partially_paid'
            
            invoice.save()
            
            return JsonResponse({
                'success': True,
                'message': 'تم معالجة الدفع بنجاح',
                'invoice_id': str(invoice.id),
                'payment_ids': [str(p.id) for p in payment_records],
                'total_paid': float(total_paid),
                'remaining_amount': float(invoice.amount_due),
                'insurance_amount': float(insurance_amount),
                'warranty_amount': float(warranty_amount),
                'total_discount': float(total_discount_amount),
                'new_status': invoice.status
            })
                
        except (Invoice.DoesNotExist, ValueError):
            # Handle demo data with integer IDs
            print(f"💡 Processing demo payment for invoice ID: {invoice_id}")
            
            # Load demo state from file
            import os
            import json as json_module
            from django.conf import settings
            
            state_file_path = os.path.join(settings.BASE_DIR, 'demo_invoice_state.json')
            
            try:
                if os.path.exists(state_file_path):
                    with open(state_file_path, 'r', encoding='utf-8') as f:
                        demo_state = json_module.load(f)
                else:
                    # Initialize empty state if file doesn't exist
                    demo_state = {'demo_invoices': {}}
                
                invoice_key = str(invoice_id)
                if invoice_key in demo_state['demo_invoices']:
                    invoice_data = demo_state['demo_invoices'][invoice_key]
                    
                    # Calculate payment amounts
                    total_paid = sum(Decimal(str(method.get('amount', 0))) for method in payment_methods)
                    current_paid = float(invoice_data.get('amount_paid', 0))
                    new_total_paid = current_paid + float(total_paid)
                    total_amount_float = float(invoice_data.get('total_amount', total_amount))
                    remaining = max(0, total_amount_float - new_total_paid)
                    
                    # Update demo state
                    invoice_data['amount_paid'] = new_total_paid
                    
                    # Update status based on payment
                    if remaining <= 0:
                        invoice_data['status'] = 'paid'
                    elif new_total_paid > 0:
                        invoice_data['status'] = 'partially_paid'
                    
                    # Add payment record
                    from datetime import datetime
                    new_payment = {
                        'amount': float(total_paid),
                        'method': payment_methods[0].get('type', 'نقدي') if payment_methods else 'نقدي',
                        'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    if 'payments' not in invoice_data:
                        invoice_data['payments'] = []
                    invoice_data['payments'].append(new_payment)
                    
                    # Save updated state
                    with open(state_file_path, 'w', encoding='utf-8') as f:
                        json_module.dump(demo_state, f, ensure_ascii=False, indent=2)
                    
                    print(f"💾 Updated demo invoice {invoice_key}: paid {new_total_paid}/{total_amount_float}, status: {invoice_data['status']}")
                    
                    # Return success response for demo
                    return JsonResponse({
                        'success': True,
                        'message': 'تم معالجة الدفع بنجاح وحفظ الحالة',
                        'invoice_id': str(invoice_id),
                        'payment_ids': [f"demo_payment_{len(invoice_data['payments'])}"],
                        'total_paid': float(total_paid),
                        'remaining_amount': remaining,
                        'insurance_amount': 0.0,
                        'warranty_amount': 0.0,
                        'total_discount': sum(float(discount.get('amount', 0)) for discount in applied_discounts),
                        'demo_mode': True,
                        'new_status': invoice_data['status']
                    })
                else:
                    print(f"⚠️ Demo invoice {invoice_key} not found in state file")
                    
            except Exception as e:
                print(f"❌ Error updating demo state: {str(e)}")
            
            # Fallback to simple demo processing
            total_paid = sum(Decimal(str(method.get('amount', 0))) for method in payment_methods)
            
            return JsonResponse({
                'success': True,
                'message': 'تم معالجة الدفع بنجاح (نسخة تجريبية)',
                'invoice_id': str(invoice_id),
                'payment_ids': [f"demo_payment_{i}" for i in range(len(payment_methods))],
                'total_paid': float(total_paid),
                'remaining_amount': max(0, float(total_amount) - float(total_paid)),
                'insurance_amount': 0.0,
                'warranty_amount': 0.0,
                'total_discount': sum(float(discount.get('amount', 0)) for discount in applied_discounts),
                'demo_mode': True
            })
            
    except Exception as e:
        print(f"❌ Payment processing error: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'خطأ في معالجة الدفع: {str(e)}'
        }, status=400)

# Add these new API endpoints after the existing discount APIs (around line 2000)

@login_required
@require_http_methods(["GET"])
def api_detect_available_offers(request, invoice_id):
    """Detect all available discount offers for an invoice"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Detect available offers
        offers = DiscountService.detect_available_offers(invoice)
        
        # Convert Decimal to float for JSON serialization
        def convert_decimals(obj):
            if isinstance(obj, list):
                return [convert_decimals(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: convert_decimals(value) for key, value in obj.items()}
            elif isinstance(obj, Decimal):
                return float(obj)
            return obj
        
        offers = convert_decimals(offers)
        
        return JsonResponse({
            'success': True,
            'offers': offers,
            'invoice_subtotal': float(invoice.subtotal),
            'customer_name': invoice.customer.full_name if invoice.customer else 'عميل غير محدد'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["POST"])
def api_apply_hierarchical_discounts(request):
    """Apply multiple discounts with hierarchical logic"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        import json
        
        data = json.loads(request.body)
        invoice_id = data.get('invoice_id')
        discount_requests = data.get('discount_requests', [])
        force_apply = data.get('force_apply', False)
        
        if not invoice_id or not discount_requests:
            return JsonResponse({
                'success': False,
                'error': 'بيانات الخصم غير مكتملة'
            }, status=400)
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Check for discount type restrictions
        if discount_requests:
            first_request = discount_requests[0]
            discount_source = 'manual' if not first_request.get('rule_id') else 'rule'
            
            if not invoice.can_apply_discount_type(discount_source):
                existing_types = invoice.get_applied_discount_types()
                type_names = {
                    'manual': 'خصم يدوي',
                    'rule': 'خصم تلقائي',
                    'promotion': 'عرض ترويجي',
                    'customer_preference': 'خصم العميل'
                }
                existing_names = [type_names.get(t, t) for t in existing_types]
                
                return JsonResponse({
                    'success': False,
                    'error': f'لا يمكن تطبيق نوع خصم مختلف. الأنواع المطبقة حاليا: {", ".join(existing_names)}',
                    'restriction': 'discount_type_limit',
                    'existing_types': existing_types
                }, status=400)
        
        # Apply discounts
        result = DiscountService.apply_hierarchical_discounts(invoice, discount_requests)
        
        if result.get('success'):
            # Refresh invoice from database to get latest values
            invoice.refresh_from_db()
            
            return JsonResponse({
                'success': True,
                'message': f'تم تطبيق {len(discount_requests)} خصم بنجاح',
                'total_discount_amount': float(result.get('total_discount_amount', 0)),
                'new_invoice_total': float(invoice.total_amount),
                'new_amount_due': float(invoice.amount_due),
                'new_subtotal': float(invoice.subtotal),
                'applied_discounts_count': len(result.get('applied_discounts', []))
            })
        else:
            return JsonResponse({
                'success': False,
                'error': result.get('error', 'فشل في تطبيق الخصومات'),
                'conflicts': result.get('conflicts', [])
            })
        
    except ValueError as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'restriction': 'discount_type_limit'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'خطأ في تطبيق الخصومات: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def api_apply_item_discount(request):
    """Apply discount to specific items"""
    tenant_id = get_current_tenant_id()
    
    try:
        import json
        from decimal import Decimal
        
        data = json.loads(request.body)
        invoice_id = data.get('invoice_id')
        item_ids = data.get('item_ids', [])
        discount_type = data.get('discount_type', 'percentage')  # percentage or fixed
        discount_value = Decimal(str(data.get('discount_value', 0)))
        reason = data.get('reason', 'خصم يدوي على الصنف')
        
        if not invoice_id or not item_ids or discount_value <= 0:
            return JsonResponse({
                'success': False,
                'error': 'بيانات الخصم غير مكتملة'
            }, status=400)
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Check if manual discount can be applied
        if not invoice.can_apply_discount_type('manual'):
            existing_types = invoice.get_applied_discount_types()
            type_names = {
                'manual': 'خصم يدوي',
                'rule': 'خصم تلقائي',
                'promotion': 'عرض ترويجي',
                'customer_preference': 'خصم العميل'
            }
            existing_names = [type_names.get(t, t) for t in existing_types]
            
            return JsonResponse({
                'success': False,
                'error': f'لا يمكن تطبيق خصم يدوي. الأنواع المطبقة حاليا: {", ".join(existing_names)}',
                'restriction': 'discount_type_limit'
            }, status=400)
        
        # Apply discount using hierarchical service
        discount_request = {
            'level': 'item',
            'type': discount_type,
            'value': discount_value,
            'target': 'specific_items',
            'target_items': item_ids,
            'reason': reason
        }
        
        result = DiscountService.apply_hierarchical_discounts(invoice, [discount_request])
        
        if result.get('success'):
            return JsonResponse({
                'success': True,
                'message': f'تم تطبيق الخصم على {len(item_ids)} صنف',
                'discount_amount': float(result['total_discount_amount']),
                'new_invoice_total': float(invoice.total_amount),
                'new_amount_due': float(invoice.amount_due)
            })
        else:
            return JsonResponse({
                'success': False,
                'error': result.get('error', 'فشل في تطبيق الخصم')
            })
        
    except ValueError as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'restriction': 'discount_type_limit'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'خطأ في تطبيق خصم الصنف: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def api_apply_category_discount(request):
    """Apply discount to all parts or all operations"""
    tenant_id = get_current_tenant_id()
    
    try:
        import json
        from decimal import Decimal
        
        data = json.loads(request.body)
        invoice_id = data.get('invoice_id')
        category = data.get('category')  # 'parts' or 'operations'
        discount_type = data.get('discount_type', 'percentage')
        discount_value = Decimal(str(data.get('discount_value', 0)))
        reason = data.get('reason', '')
        
        if not invoice_id or category not in ['parts', 'operations'] or discount_value <= 0:
            return JsonResponse({
                'success': False,
                'error': 'بيانات الخصم غير صحيحة'
            }, status=400)
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Check if manual discount can be applied
        if not invoice.can_apply_discount_type('manual'):
            existing_types = invoice.get_applied_discount_types()
            type_names = {
                'manual': 'خصم يدوي',
                'rule': 'خصم تلقائي',
                'promotion': 'عرض ترويجي',
                'customer_preference': 'خصم العميل'
            }
            existing_names = [type_names.get(t, t) for t in existing_types]
            
            return JsonResponse({
                'success': False,
                'error': f'لا يمكن تطبيق خصم يدوي. الأنواع المطبقة حاليا: {", ".join(existing_names)}',
                'restriction': 'discount_type_limit'
            }, status=400)
        
        # Set default reason based on category
        if not reason:
            reason = f'خصم على جميع {"قطع الغيار" if category == "parts" else "العمليات"}'
        
        # Apply discount using hierarchical service
        discount_request = {
            'level': 'category',
            'type': discount_type,
            'value': discount_value,
            'target': category,
            'reason': reason
        }
        
        result = DiscountService.apply_hierarchical_discounts(invoice, [discount_request])
        
        if result.get('success'):
            return JsonResponse({
                'success': True,
                'message': f'تم تطبيق الخصم على جميع {"قطع الغيار" if category == "parts" else "العمليات"}',
                'discount_amount': float(result['total_discount_amount']),
                'new_invoice_total': float(invoice.total_amount),
                'new_amount_due': float(invoice.amount_due)
            })
        else:
            return JsonResponse({
                'success': False,
                'error': result.get('error', 'فشل في تطبيق الخصم')
            })
        
    except ValueError as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'restriction': 'discount_type_limit'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'خطأ في تطبيق خصم الفئة: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def api_apply_automatic_rules(request):
    """Apply available automatic discount rules"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        import json
        
        data = json.loads(request.body)
        invoice_id = data.get('invoice_id')
        selected_rules = data.get('selected_rules', [])  # List of rule IDs to apply
        
        if not invoice_id:
            return JsonResponse({
                'success': False,
                'error': 'معرف الفاتورة مطلوب'
            }, status=400)
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Get available offers
        offers = DiscountService.detect_available_offers(invoice)
        
        # Convert selected rules to discount requests
        discount_requests = []
        
        for rule_data in offers['automatic_rules']:
            rule = rule_data['rule']
            if str(rule.id) in selected_rules:
                discount_requests.append({
                    'level': 'invoice' if rule.applicable_to == 'invoice' else 'category',
                    'type': 'percentage' if rule.percentage_discount > 0 else 'fixed',
                    'value': float(rule.percentage_discount) if rule.percentage_discount > 0 else float(rule.fixed_discount),
                    'target': rule.applicable_to,
                    'reason': f'قاعدة تلقائية: {rule.name}',
                    'rule_id': rule.id
                })
        
        if not discount_requests:
            return JsonResponse({
                'success': False,
                'error': 'لم يتم اختيار أي قواعد صالحة'
            })
        
        # Apply selected rules
        result = DiscountService.apply_hierarchical_discounts(invoice, discount_requests)
        
        return JsonResponse({
            'success': True,
            'message': f'تم تطبيق {len(discount_requests)} قاعدة خصم تلقائية',
            'applied_rules': len(discount_requests),
            'total_discount_amount': float(result['total_discount_amount']),
            'new_invoice_total': float(invoice.total_amount)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'خطأ في تطبيق القواعد التلقائية: {str(e)}'
        })


@login_required
@require_http_methods(["DELETE"])
def api_remove_discount(request, discount_id):
    """Remove a specific discount"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        
        # Get the discount to find the invoice
        from .models import InvoiceDiscount
        discount = get_object_or_404(InvoiceDiscount, id=discount_id, tenant_id=tenant_id)
        invoice = discount.invoice
        
        # Remove the discount
        success = DiscountService.remove_discount(invoice, discount_id)
        
        if success:
            return JsonResponse({
                'success': True,
                'message': 'تم إزالة الخصم بنجاح',
                'new_invoice_total': float(invoice.total_amount),
                'new_amount_due': float(invoice.amount_due)
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'فشل في إزالة الخصم'
            })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'خطأ في إزالة الخصم: {str(e)}'
        })


@login_required
@require_http_methods(["GET"])
def api_enhanced_discount_summary(request, invoice_id):
    """Get enhanced discount summary with all applied discounts"""
    tenant_id = get_current_tenant_id()
    
    try:
        from .services import DiscountService
        
        invoice = get_object_or_404(Invoice, id=invoice_id, tenant_id=tenant_id)
        
        # Get comprehensive discount summary
        summary = DiscountService.get_discount_summary(invoice)
        
        # Convert Decimal to float for JSON serialization
        summary['total_discount'] = float(summary['total_discount'])
        summary['rule_based_discount'] = float(summary['rule_based_discount'])
        summary['manual_discount'] = float(summary['manual_discount'])
        
        for detail in summary['discounts_detail']:
            detail['amount'] = float(detail['amount'])
            detail['percentage'] = float(detail['percentage'])
        
        # Add invoice totals
        summary['invoice_totals'] = {
            'subtotal': float(invoice.subtotal),
            'total_discount': float(summary['total_discount']),
            'tax_amount': float(invoice.tax_amount),
            'total_amount': float(invoice.total_amount),
            'amount_due': float(invoice.amount_due)
        }
        
        return JsonResponse({
            'success': True,
            'summary': summary
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@csrf_exempt
@require_http_methods(["GET"])
def api_invoice_details_int(request, invoice_id):
    """Get invoice details by ID or UUID"""
    tenant_id = get_current_tenant_id()
    
    # Try to fetch from database first
    try:
        # Try UUID first, then integer ID
        if len(str(invoice_id)) > 10:  # Likely a UUID
            invoice = Invoice.objects.get(uuid=invoice_id, tenant_id=tenant_id)
        else:
            invoice = Invoice.objects.get(id=invoice_id, tenant_id=tenant_id)
                
        # Serialize the invoice data
        invoice_data = {
            'id': invoice.id,
            'uuid': str(invoice.id),
            'invoice_number': invoice.invoice_number,
            'customer_id': invoice.customer.id if invoice.customer else None,
            'customer_name': invoice.customer.full_name if invoice.customer else 'عميل غير محدد',
            'customer_phone': invoice.customer.phone if invoice.customer else '',
            'customer_email': invoice.customer.email if invoice.customer else '',
            'work_order_id': invoice.work_order.id if invoice.work_order else None,
            'work_order_number': invoice.work_order.work_order_number if invoice.work_order else '',
            'total_amount': float(invoice.total_amount or 0),
            'subtotal': float(invoice.subtotal or 0),
            'tax_amount': float(invoice.tax_amount or 0),
            'discount_amount': float(invoice.discount_amount or 0),
            'status': invoice.status,
            'payment_status': invoice.payment_status or 'unpaid',
            'created_at': invoice.created_at.isoformat() if invoice.created_at else None,
            'invoice_date': invoice.invoice_date.isoformat() if invoice.invoice_date else None,
            'paid_amount': float(invoice.amount_paid or 0),
            'remaining_amount': float(invoice.total_amount - (invoice.amount_paid or 0)),
            'items': [],
            'operations': [],
            'payments': []
        }
        
        # Add invoice items
        if hasattr(invoice, 'invoiceitem_set'):
            for item in invoice.invoiceitem_set.all():
                invoice_data['items'].append({
                    'id': item.id,
                    'description': item.description or (item.item.name if item.item else 'صنف غير محدد'),
                    'quantity': float(item.quantity or 0),
                    'unit_price': float(item.unit_price or 0),
                    'total': float(item.total_price or 0),
                    'item_type': 'part' if item.item else 'service'
                })
        
        # Add work order operations if available
        if invoice.work_order and hasattr(invoice.work_order, 'workorderoperation_set'):
            for operation in invoice.work_order.workorderoperation_set.all():
                invoice_data['operations'].append({
                    'id': operation.id,
                    'description': operation.operation.name if operation.operation else 'عملية غير محددة',
                    'cost': float(operation.cost or 0),
                    'duration': operation.duration or 'غير محدد',
                    'completed': operation.completed or False
                })
        
        # Add payments
        if hasattr(invoice, 'payment_set'):
            for payment in invoice.payment_set.all():
                invoice_data['payments'].append({
                    'id': payment.id,
                    'amount': float(payment.amount or 0),
                    'payment_method': payment.payment_method.name if payment.payment_method else 'غير محدد',
                    'payment_date': payment.payment_date.isoformat() if payment.payment_date else None,
                    'reference': payment.reference or ''
                })
        
        return JsonResponse({
            'success': True,
            'invoice': invoice_data
        })
        
    except Invoice.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': f'الفاتورة رقم {invoice_id} غير موجودة'
        }, status=404)
        
    except Exception as e:
        logger.error(f"Error in api_invoice_details_int: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'خطأ في الخادم'
        }, status=500)

@login_required
@require_http_methods(["GET"])
def api_all_invoices(request):
    """API endpoint to get all invoices and completed work orders from database"""
    try:
        tenant_id = get_current_tenant_id()
        
        # Handle case where tenant_id is None (development/testing scenario)
        if not tenant_id:
            # Get any tenant_id from existing invoices as fallback
            first_invoice = Invoice.objects.first()
            if first_invoice:
                tenant_id = first_invoice.tenant_id
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"No tenant context found, using fallback tenant_id: {tenant_id}")
        
        # Get ALL invoices from database (no limit for cashier dashboard)
        if tenant_id:
            invoices = Invoice.objects.filter(tenant_id=tenant_id).select_related(
                'customer', 'work_order', 'service_center'
            ).order_by('-created_at')  # Return all invoices
        else:
            # If still no tenant_id, return all invoices (for development)
            invoices = Invoice.objects.select_related(
                'customer', 'work_order', 'service_center'
            ).order_by('-created_at')  # Return all invoices
        
        # Get completed work orders that haven't been invoiced yet
        completed_work_orders = []
        try:
            from work_orders.models import WorkOrder
            if tenant_id:
                work_orders = WorkOrder.objects.filter(
                    tenant_id=tenant_id,
                    status='completed',
                    invoice__isnull=True  # Only work orders without invoices
                ).select_related('customer', 'vehicle', 'service_center').order_by('-actual_end_date')
            else:
                # If no tenant_id, get all work orders (for development)
                work_orders = WorkOrder.objects.filter(
                    status='completed',
                    invoice__isnull=True  # Only work orders without invoices
                ).select_related('customer', 'vehicle', 'service_center').order_by('-actual_end_date')
            completed_work_orders = list(work_orders)
        except ImportError:
            pass
        
        # Check if we have any data
        if not invoices.exists() and not completed_work_orders:
            return JsonResponse({
                'success': True,
                'invoices': [],
                'message': 'لا توجد فواتير أو أوامر عمل مكتملة في قاعدة البيانات'
            })
        
        # Format invoice data
        invoice_list = []
        
        # Add existing invoices
        for invoice in invoices:
            # Update invoice status before displaying
            invoice.update_status()
            
            # Calculate payment status
            total_amount = float(invoice.total_amount or 0)
            paid_amount = float(invoice.amount_paid or 0)
            
            # Use the updated status for payment_status
            if invoice.status == 'paid':
                payment_status = 'paid'
            elif invoice.status == 'overdue':
                payment_status = 'overdue'
            elif invoice.status == 'partially_paid':
                payment_status = 'partial'
            else:
                payment_status = 'unpaid'
            
            invoice_data = {
                'id': invoice.id,
                'uuid': str(invoice.id),
                'invoice_number': invoice.invoice_number,
                'customer_name': invoice.customer.full_name if invoice.customer else 'عميل غير محدد',
                'customer_phone': invoice.customer.phone if invoice.customer else '',
                'customer_email': invoice.customer.email if invoice.customer else '',
                'total_amount': total_amount,
                'subtotal': float(invoice.subtotal or 0),
                'tax_amount': float(invoice.tax_amount or 0),
                'discount_amount': float(invoice.discount_amount or 0),
                'status': invoice.status or 'draft',
                'payment_status': payment_status,
                'date': invoice.created_at.isoformat() if invoice.created_at else None,
                'invoice_date': invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                'work_order_number': invoice.work_order.work_order_number if invoice.work_order else '',
                'service_center': invoice.service_center.name if invoice.service_center else 'مركز الخدمة الرئيسي',
                'paid_amount': paid_amount,
                'remaining_amount': max(0, total_amount - paid_amount),
                'amount_due': max(0, total_amount - paid_amount),
                'type': 'invoice'
            }
            invoice_list.append(invoice_data)
        
        # Add completed work orders that need invoicing
        for work_order in completed_work_orders:
            # Calculate total cost from materials and operations
            total_cost = 0
            
            # Add material costs
            if hasattr(work_order, 'materials'):
                for material in work_order.materials.all():
                    # Calculate total cost from quantity * unit price
                    if material.item and hasattr(material.item, 'unit_price'):
                        material_cost = float(material.quantity or 0) * float(material.item.unit_price or 0)
                        total_cost += material_cost
            
            # Add operation costs
            if hasattr(work_order, 'workorderoperation_set'):
                for operation in work_order.workorderoperation_set.all():
                    total_cost += float(operation.cost or 0)
            
            # Add service costs
            if hasattr(work_order, 'services'):
                for service in work_order.services.all():
                    total_cost += float(service.cost or 0)
            
            work_order_data = {
                'id': f"wo_{work_order.id}",  # Prefix to distinguish from invoices
                'uuid': str(work_order.uuid) if hasattr(work_order, 'uuid') else f"wo_{work_order.id}",
                'invoice_number': f"WO-{work_order.work_order_number}",  # Show work order number
                'customer_name': work_order.customer.full_name if work_order.customer else 'عميل غير محدد',
                'customer_phone': work_order.customer.phone if work_order.customer else '',
                'customer_email': work_order.customer.email if work_order.customer else '',
                'total_amount': total_cost,
                'subtotal': total_cost,
                'tax_amount': 0,
                'discount_amount': 0,
                'status': 'completed',  # Work order status
                'payment_status': 'pending_invoice',  # Special status for work orders
                'date': work_order.actual_end_date.isoformat() if work_order.actual_end_date else (work_order.created_at.isoformat() if work_order.created_at else None),
                'invoice_date': None,
                'work_order_number': work_order.work_order_number,
                'service_center': work_order.service_center.name if work_order.service_center else 'مركز الخدمة الرئيسي',
                'paid_amount': 0,
                'remaining_amount': total_cost,
                'amount_due': total_cost,
                'type': 'work_order'
            }
            invoice_list.append(work_order_data)
        
        # Sort combined list by date (most recent first)
        invoice_list.sort(key=lambda x: x['date'] or '', reverse=True)
        
        return JsonResponse({
            'success': True,
            'invoices': invoice_list,
            'count': len(invoice_list)
        })
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in api_all_invoices: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'خطأ في الخادم',
            'invoices': []
        })