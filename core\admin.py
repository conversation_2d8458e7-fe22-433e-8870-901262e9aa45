from django.contrib import admin
from django_otp.admin import OTPAdminSite
from project.settings import DEBUG
from core.middleware import get_current_tenant_id

admin.site.site_title = 'Project (Test)'
admin.site.site_header = 'Project (Test)'
admin.site.index_title = 'Administration'

if not DEBUG:
  admin.site.site_title = 'Project'
  admin.site.site_header = 'Project'
  # admin.site.__class__ = OTPAdminSite #TODO: Enable in production

class TenantAdminMixin:
    """
    Admin mixin that filters querysets by the current tenant ID
    """
    
    def get_queryset(self, request):
        """
        Filter the queryset by the current tenant ID
        """
        queryset = super().get_queryset(request)
        tenant_id = get_current_tenant_id()
        
        # Only filter if tenant_id exists and model has tenant_id field
        if tenant_id and hasattr(self.model, 'tenant_id'):
            queryset = queryset.filter(tenant_id=tenant_id)
            
        return queryset
        
    def save_model(self, request, obj, form, change):
        """
        Set tenant_id on the object before saving
        """
        if not change and hasattr(obj, 'tenant_id') and not obj.tenant_id:
            tenant_id = get_current_tenant_id()
            if tenant_id:
                obj.tenant_id = tenant_id
                
        super().save_model(request, obj, form, change)



