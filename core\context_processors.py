from django.contrib import admin
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from setup.models import Franchise, Company, ServiceCenter
from urllib.parse import urlencode
from core.utils import get_user_role_info

# Import the new dynamic permission services
from user_roles.services import PermissionService, NavigationService, DataFilterService


def admin_header_processor(request):
    site_header = getattr(admin.site, 'site_header')
    return {"site_header": site_header}

def language_context(request):
    """
    Adds language-related context variables to templates
    For now, always returning Arabic and RTL setup
    """
    return {
        'current_language': 'ar',
        'available_languages': settings.LANGUAGES,
        'is_rtl': True,  # Always RTL for Arabic-only interface
        'dir': 'rtl',    # Add explicit direction
        'text_direction': 'rtl',
    }

def user_roles_context(request):
    """
    Adds the user's primary role to the template context
    """
    primary_role = None
    
    if request.user.is_authenticated and hasattr(request.user, 'user_roles'):
        # Get all active roles for the user
        user_roles = request.user.user_roles.filter(is_active=True)
        
        # First, try to find the primary role
        for user_role in user_roles:
            if user_role.is_primary:
                primary_role = user_role.role
                break
                
        # If no primary role is found, use the first active role
        if not primary_role and user_roles.exists():
            primary_role = user_roles.first().role
    
    return {
        'primary_role': primary_role
    }

def user_permissions(request):
    """
    Enhanced context processor for dynamic user permissions.
    Provides both legacy permissions and new dynamic tab permissions.
    """
    permissions = {}
    primary_role = None
    dynamic_permissions = {}
    accessible_tabs = []
    navigation_menu = []
    
    if hasattr(request, 'user') and request.user.is_authenticated:
        # Get legacy permissions for backward compatibility
        primary_role, permissions = get_user_role_info(request.user)
        
        # Get current context (franchise, company, service_center)
        context = _get_permission_context(request)
        
        # Get dynamic permissions for all tabs
        dynamic_permissions = PermissionService.get_user_permissions(request.user, context)
        
        # Get accessible tabs for navigation
        accessible_tabs = PermissionService.get_accessible_tabs(request.user, context)
        
        # Build navigation menu
        navigation_menu = NavigationService.build_main_navigation(request.user, context)
    
    return {
        'user_permissions': permissions,  # Legacy permissions
        'user_primary_role': primary_role,
        'dynamic_permissions': dynamic_permissions,  # New dynamic permissions
        'accessible_tabs': accessible_tabs,
        'navigation_menu': navigation_menu,
    }

def dynamic_navigation_context(request):
    """
    Context processor specifically for dynamic navigation features
    """
    breadcrumbs = []
    current_tab_code = None
    user_data_scope = {}
    
    if hasattr(request, 'user') and request.user.is_authenticated:
        # Get current context
        context = _get_permission_context(request)
        
        # Get user's data scope
        user_data_scope = DataFilterService.get_user_data_scope(request.user, context)
        
        # Try to determine current tab from URL
        current_tab_code = _determine_current_tab(request)
        
        if current_tab_code:
            breadcrumbs = NavigationService.get_breadcrumb_path(
                request.user, 
                current_tab_code, 
                context
            )
    
    return {
        'breadcrumbs': breadcrumbs,
        'current_tab_code': current_tab_code,
        'user_data_scope': user_data_scope,
    }

def _get_permission_context(request):
    """
    Helper function to extract permission context from request
    """
    context = {}
    
    # Try to get context from URL parameters
    if hasattr(request, 'GET'):
        franchise_id = request.GET.get('franchise_id')
        company_id = request.GET.get('company_id')
        service_center_id = request.GET.get('service_center_id')
        
        try:
            if service_center_id:
                context['service_center'] = ServiceCenter.objects.get(id=service_center_id)
            if company_id:
                context['company'] = Company.objects.get(id=company_id)
            if franchise_id:
                context['franchise'] = Franchise.objects.get(id=franchise_id)
        except (ServiceCenter.DoesNotExist, Company.DoesNotExist, Franchise.DoesNotExist, ValueError):
            pass
    
    # If no explicit context, try to infer from user's primary role
    if not context and request.user.is_authenticated:
        try:
            primary_user_role = request.user.user_roles.filter(is_primary=True, is_active=True).first()
            if not primary_user_role:
                primary_user_role = request.user.user_roles.filter(is_active=True).first()
            
            if primary_user_role:
                if primary_user_role.service_center:
                    context['service_center'] = primary_user_role.service_center
                    context['company'] = primary_user_role.service_center.company
                    if primary_user_role.service_center.company.franchise:
                        context['franchise'] = primary_user_role.service_center.company.franchise
                elif primary_user_role.company:
                    context['company'] = primary_user_role.company
                    if primary_user_role.company.franchise:
                        context['franchise'] = primary_user_role.company.franchise
                elif primary_user_role.franchise:
                    context['franchise'] = primary_user_role.franchise
        except Exception:
            pass
    
    return context

def _determine_current_tab(request):
    """
    Helper function to determine current tab from URL patterns
    """
    # URL pattern to tab code mapping
    url_tab_mapping = {
        'core:main_dashboard': 'dashboard',
        'core:dashboard': 'dashboard',
        'inventory:': 'inventory',
        'warehouse:': 'warehouse',
        'sales:': 'sales',
        'purchases:': 'purchases',
        'work_orders:': 'work_orders',
        'billing:': 'billing',
        'reports:': 'reports',
        'setup:': 'setup',
    }
    
    # Get the current URL name
    if hasattr(request, 'resolver_match') and request.resolver_match:
        url_name = request.resolver_match.url_name
        namespace = request.resolver_match.namespace
        
        full_url_name = f"{namespace}:{url_name}" if namespace else url_name
        
        # Check for exact matches first
        if full_url_name in url_tab_mapping:
            return url_tab_mapping[full_url_name]
        
        # Check for namespace matches
        if namespace:
            for pattern, tab_code in url_tab_mapping.items():
                if pattern.endswith(':') and namespace == pattern[:-1]:
                    return tab_code
    
    # Fallback: analyze the URL path
    if hasattr(request, 'path'):
        path = request.path.strip('/')
        path_segments = path.split('/')
        
        # Common path patterns
        if 'setup' in path_segments:
            return 'setup'
        elif 'inventory' in path_segments:
            return 'inventory'
        elif 'warehouse' in path_segments:
            return 'warehouse'
        elif 'sales' in path_segments:
            return 'sales'
        elif 'purchases' in path_segments:
            return 'purchases'
        elif 'work_orders' in path_segments or 'work-orders' in path_segments:
            return 'work_orders'
        elif 'billing' in path_segments or 'cashier' in path_segments:
            return 'billing'
        elif 'reports' in path_segments:
            return 'reports'
    
    # Default to dashboard
    return 'dashboard'

def entity_filters(request):
    """
    Context processor to provide entity filter data (franchise, company, service center)
    for use in templates across the system
    """
    # Default: Get all entities ordered by name
    franchises = Franchise.objects.filter(is_active=True).order_by('name')
    companies = Company.objects.filter(is_active=True).order_by('name')
    service_centers = ServiceCenter.objects.filter(is_active=True).order_by('name')
    
    # Get selected entities from request parameters
    selected_franchise = None
    selected_company = None  
    selected_service_center = None
    
    franchise_id = request.GET.get('franchise_id')
    company_id = request.GET.get('company_id')
    service_center_id = request.GET.get('service_center_id')
    
    # First, filter the available options based on user role scope
    if request.user.is_authenticated and hasattr(request.user, 'user_roles') and not request.user.is_superuser:
        primary_role = None
        user_roles = request.user.user_roles.filter(is_active=True)
        
        # Find primary role
        for user_role in user_roles:
            if user_role.is_primary:
                primary_role = user_role
                break
                
        # If no primary role is found, use the first active role
        if not primary_role and user_roles.exists():
            primary_role = user_roles.first()
        
        # Filter available entities based on role scope
        if primary_role:
            if primary_role.service_center:
                # Service center role only sees their service center and its hierarchy
                service_centers = ServiceCenter.objects.filter(id=primary_role.service_center.id, is_active=True)
                companies = Company.objects.filter(id=primary_role.service_center.company_id, is_active=True)
                if primary_role.service_center.company.franchise:
                    franchises = Franchise.objects.filter(id=primary_role.service_center.company.franchise_id, is_active=True)
                else:
                    franchises = Franchise.objects.none()
            elif primary_role.company:
                # Company role only sees their company and its service centers
                companies = Company.objects.filter(id=primary_role.company.id, is_active=True)
                service_centers = ServiceCenter.objects.filter(company_id=primary_role.company.id, is_active=True)
                if primary_role.company.franchise:
                    franchises = Franchise.objects.filter(id=primary_role.company.franchise_id, is_active=True)
                else:
                    franchises = Franchise.objects.none()
            elif primary_role.franchise:
                # Franchise role only sees their franchise and its companies/service centers
                franchises = Franchise.objects.filter(id=primary_role.franchise.id, is_active=True)
                companies = Company.objects.filter(franchise_id=primary_role.franchise.id, is_active=True)
                service_centers = ServiceCenter.objects.filter(company__franchise_id=primary_role.franchise.id, is_active=True)
    
    # Then, if no filter is explicitly set in GET parameters, try to auto-select based on user role
    if request.user.is_authenticated and not (franchise_id or company_id or service_center_id):
        if hasattr(request.user, 'user_roles'):
            primary_role = None
            user_roles = request.user.user_roles.filter(is_active=True)
            
            # Find primary role
            for user_role in user_roles:
                if user_role.is_primary:
                    primary_role = user_role
                    break
                    
            # If no primary role is found, use the first active role
            if not primary_role and user_roles.exists():
                primary_role = user_roles.first()
            
            # Set filters based on role scope
            if primary_role:
                if primary_role.service_center:
                    selected_service_center = primary_role.service_center
                    service_center_id = selected_service_center.id
                    selected_company = primary_role.service_center.company
                    company_id = selected_company.id
                    if selected_company.franchise:
                        selected_franchise = selected_company.franchise
                        franchise_id = selected_franchise.id
                elif primary_role.company:
                    selected_company = primary_role.company
                    company_id = selected_company.id
                    if selected_company.franchise:
                        selected_franchise = selected_company.franchise
                        franchise_id = selected_franchise.id
                elif primary_role.franchise:
                    selected_franchise = primary_role.franchise
                    franchise_id = selected_franchise.id
    
    # Process explicit filter selections from GET parameters
    if franchise_id:
        try:
            selected_franchise = Franchise.objects.get(id=franchise_id)
            # Filter companies by franchise
            companies = companies.filter(franchise_id=franchise_id)
            # Also filter service centers to be from this franchise
            service_centers = service_centers.filter(company__franchise_id=franchise_id)
        except (Franchise.DoesNotExist, ValueError):
            pass
    
    if company_id:
        try:
            selected_company = Company.objects.get(id=company_id)
            # Filter service centers by company
            service_centers = service_centers.filter(company_id=company_id)
        except (Company.DoesNotExist, ValueError):
            pass
    
    if service_center_id:
        try:
            selected_service_center = ServiceCenter.objects.get(id=service_center_id)
        except (ServiceCenter.DoesNotExist, ValueError):
            pass
    
    # Create a preserved query string for links that need to maintain filter state but reset entity filters
    preserved_params = {k: v for k, v in request.GET.items() 
                        if k not in ['franchise_id', 'company_id', 'service_center_id']}
    preserved_query = urlencode(preserved_params)
    
    return {
        'franchises': franchises,
        'companies': companies,
        'service_centers': service_centers,
        'selected_franchise': selected_franchise,
        'selected_company': selected_company,
        'selected_service_center': selected_service_center,
        'preserved_query': preserved_query,
    }
