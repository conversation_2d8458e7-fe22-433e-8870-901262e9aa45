from django import forms
from django.utils.translation import gettext_lazy as _


class BaseForm(forms.Form):
    """Base form class with common functionality and styling"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Apply base styling to all fields
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        for field_name, field in self.fields.items():
            # Skip checkbox fields
            if not isinstance(field.widget, forms.CheckboxInput):
                if 'class' not in field.widget.attrs:
                    field.widget.attrs['class'] = base_class
                else:
                    field.widget.attrs['class'] += ' ' + base_class


class BaseModelForm(forms.ModelForm):
    """Base model form class with common functionality and styling"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Apply base styling to all fields
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        for field_name, field in self.fields.items():
            # Skip checkbox fields
            if not isinstance(field.widget, forms.CheckboxInput):
                if 'class' not in field.widget.attrs:
                    field.widget.attrs['class'] = base_class
                else:
                    field.widget.attrs['class'] += ' ' + base_class
    
    def save(self, commit=True):
        """Override save to add any common functionality"""
        instance = super().save(commit=False)
        
        # Add any common save logic here
        
        if commit:
            instance.save()
        return instance 