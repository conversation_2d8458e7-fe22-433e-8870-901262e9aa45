from django.core.management.base import BaseCommand
from django.db import connection
from django.apps import apps


class Command(BaseCommand):
    help = 'Adds tenant_id field to existing models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant_id',
            type=int,
            help='Tenant ID to use for existing records',
            required=True
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Only show which tables would be modified without making changes'
        )

    def handle(self, *args, **options):
        tenant_id = options['tenant_id']
        dry_run = options['dry_run']
        
        # Get all model classes from installed apps
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                # Skip models that already have tenant_id
                if hasattr(model, 'tenant_id'):
                    continue
                    
                # Get table name
                table_name = model._meta.db_table
                
                # Check if table exists
                cursor = connection.cursor()
                cursor.execute(
                    "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=%s",
                    [table_name]
                )
                if cursor.fetchone()[0] == 0:
                    self.stdout.write(f"Table {table_name} does not exist, skipping")
                    continue
                
                # Check if tenant_id field already exists
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                
                if 'tenant_id' in columns:
                    self.stdout.write(f"Table {table_name} already has tenant_id field")
                    continue
                
                # Show what would be modified
                self.stdout.write(f"Adding tenant_id to {table_name}")
                
                if not dry_run:
                    # Add tenant_id column
                    cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN tenant_id INTEGER")
                    
                    # Set default tenant_id value
                    cursor.execute(f"UPDATE {table_name} SET tenant_id = %s", [tenant_id])
                    
                    # Create index
                    cursor.execute(f"CREATE INDEX idx_{table_name}_tenant_id ON {table_name} (tenant_id)")
                    
                    self.stdout.write(self.style.SUCCESS(f"Successfully added tenant_id to {table_name}"))
                
        if dry_run:
            self.stdout.write(self.style.WARNING("Dry run completed. No changes were made."))
        else:
            self.stdout.write(self.style.SUCCESS("Successfully added tenant_id to all tables.")) 