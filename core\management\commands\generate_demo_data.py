from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth.models import User
from django.utils import timezone
import random
from datetime import timedelta
import uuid

# Import models from all apps
from inventory.models import Item, UnitOfMeasurement, ItemClassification, Movement, MovementType
from setup.models import Customer, ServiceCenter, Vehicle, Franchise, Company, ServiceCenterType, ServiceLevel
from warehouse.models import Location, WarehouseTransfer
from work_orders.models import WorkOrder, WorkOrderType, Operation
from billing.models import Invoice, CustomerClassification
from reports.models import Report, Dashboard

# Egyptian specific data
EGYPTIAN_MALE_FIRST_NAMES = [
    "محمد", "أحمد", "محمود", "مصطفى", "عبدالله", "عمر", "علي", "إبراهيم", "عبدالرحمن", "خالد",
    "يوسف", "حسن", "سعيد", "هيثم", "طارق", "ماجد", "هشام", "سامح", "نادر", "وليد"
]

EGYPTIAN_FEMALE_FIRST_NAMES = [
    "نور", "سارة", "فاطمة", "مريم", "آية", "هدى", "منى", "ياسمين", "دينا", "ريم",
    "هبة", "هناء", "سلمى", "رنا", "نورهان", "إيمان", "ليلى", "سمر", "أمل", "ندى"
]

EGYPTIAN_LAST_NAMES = [
    "محمد", "أحمد", "محمود", "مصطفى", "السيد", "علي", "إبراهيم", "عبدالرحمن", "خالد", "يوسف",
    "حسن", "صلاح", "عبدالعزيز", "حسين", "عثمان", "عمر", "عادل", "سعيد", "عبدالله", "فهمي"
]

EGYPTIAN_CITIES = [
    "القاهرة", "الإسكندرية", "الجيزة", "شرم الشيخ", "الغردقة", "المنصورة", "طنطا", "أسيوط", "الزقازيق", "الإسماعيلية",
    "بورسعيد", "السويس", "الأقصر", "أسوان", "المنيا", "سوهاج", "بني سويف", "دمياط", "مرسى مطروح", "الفيوم"
]

EGYPTIAN_VEHICLE_MAKES = [
    "مرسيدس", "بي إم دبليو", "أودي", "كيا", "هيونداي", "نيسان", "تويوتا", "فولكس فاجن", "سكودا", "شيفروليه",
    "فيات", "بيجو", "رينو", "مازدا", "سوزوكي", "ميتسوبيشي", "جيلي", "بريليانس", "سنجراي", "بي واي دي"
]

VEHICLE_MODELS = {
    "مرسيدس": ["C180", "C200", "E200", "E300", "S400", "GLA", "GLC", "GLE"],
    "بي إم دبليو": ["316i", "318i", "320i", "520i", "X1", "X3", "X5", "X6"],
    "أودي": ["A3", "A4", "A6", "Q3", "Q5", "Q7", "TT"],
    "كيا": ["سيراتو", "سبورتاج", "بيكانتو", "سول", "ريو", "سورينتو"],
    "هيونداي": ["إلنترا", "أكسنت", "توسان", "سنتافي", "i10", "i20", "i30"],
    "نيسان": ["صني", "سنترا", "قشقاي", "جوك", "باترول"],
    "تويوتا": ["كورولا", "يارس", "كامري", "لاند كروزر", "فورتشنر", "راف 4"],
    "فولكس فاجن": ["جولف", "باسات", "تيجوان", "جيتا", "بولو"],
    "شيفروليه": ["أفيو", "كروز", "أوبترا", "لانوس", "كابتيفا"],
    "فيات": ["تيبو", "500", "بونتو", "لينيا"]
}

PART_CATEGORIES = [
    "زيت المحرك", "فلتر زيت", "فلتر هواء", "شمعات الإحتراق", "فلتر وقود", "بطارية", "مساحات", "لمبات إضاءة", 
    "ممتص صدمات", "وسادة فرامل", "اسطوانة فرامل", "سير التوقيت", "سير المروحة", "منظم حرارة", "رادياتير",
    "طقم دبرياج", "عمود توازن", "بلف هواء", "أقمشة فرامل", "حساس أكسجين"
]

class Command(BaseCommand):
    help = 'Generate demo data for all apps with Egyptian market specifics'

    def add_arguments(self, parser):
        parser.add_argument(
            '--customers',
            type=int,
            default=50,
            help='Number of customers to generate'
        )
        parser.add_argument(
            '--vehicles',
            type=int,
            default=100,
            help='Number of vehicles to generate'
        )
        parser.add_argument(
            '--items',
            type=int,
            default=200,
            help='Number of inventory items to generate'
        )
        parser.add_argument(
            '--work-orders',
            type=int,
            default=150,
            help='Number of work orders to generate'
        )
        parser.add_argument(
            '--delete',
            action='store_true',
            help='Delete all existing data before generating new data'
        )

    @transaction.atomic
    def handle(self, *args, **options):
        if options['delete']:
            self.stdout.write('Deleting existing data...')
            self.delete_existing_data()

        self.stdout.write('Generating base data...')
        # Generate a tenant ID for all records
        self.tenant_id = str(uuid.uuid4())
        
        # Create admin user if it doesn't exist
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin')
            self.stdout.write(self.style.SUCCESS('Created admin user'))
        
        # Step 1: Generate base data (units, service levels, etc.)
        self.generate_base_data()
        
        # Step 2: Generate franchise hierarchy
        self.generate_franchise_structure()
        
        # Step 3: Generate inventory data
        self.generate_inventory_data(options['items'])
        
        # Step 4: Generate customers
        self.generate_customers(options['customers'])
        
        # Step 5: Generate vehicles
        self.generate_vehicles(options['vehicles'])
        
        # Step 6: Generate work orders
        self.generate_work_orders(options['work_orders'])
        
        # Step 7: Generate billing data
        self.generate_billing_data()
        
        self.stdout.write(self.style.SUCCESS('Successfully generated demo data!'))

    def delete_existing_data(self):
        # Delete data from each app in reverse order of dependencies
        Invoice.objects.all().delete()
        WorkOrder.objects.all().delete()
        Vehicle.objects.all().delete()
        Customer.objects.all().delete()
        Item.objects.all().delete()
        Movement.objects.all().delete()
        ServiceCenter.objects.all().delete()
        Company.objects.all().delete()
        Franchise.objects.all().delete()
        
        self.stdout.write(self.style.SUCCESS('Deleted existing data'))

    def generate_base_data(self):
        # Create units of measurement
        units = [
            {'name': 'قطعة', 'symbol': 'قطعة', 'is_base_unit': True},
            {'name': 'لتر', 'symbol': 'لتر', 'is_base_unit': True},
            {'name': 'كيلوجرام', 'symbol': 'كجم', 'is_base_unit': True},
            {'name': 'متر', 'symbol': 'م', 'is_base_unit': True},
            {'name': 'صندوق', 'symbol': 'صندوق', 'is_base_unit': False},
            {'name': 'عبوة', 'symbol': 'عبوة', 'is_base_unit': False}
        ]
        
        for unit_data in units:
            UnitOfMeasurement.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=unit_data['name'],
                defaults={
                    'symbol': unit_data['symbol'],
                    'is_base_unit': unit_data['is_base_unit']
                }
            )
        self.stdout.write(self.style.SUCCESS('Created units of measurement'))
        
        # Create item classifications
        classifications = [
            {'name': 'قطع غيار أصلية', 'code': 'OEM', 'level': 0},
            {'name': 'قطع غيار متوافقة', 'code': 'COMPAT', 'level': 0},
            {'name': 'سوائل', 'code': 'FLUID', 'level': 0},
            {'name': 'إطارات', 'code': 'TIRE', 'level': 0},
            {'name': 'بطاريات', 'code': 'BATT', 'level': 0},
            {'name': 'إكسسوارات', 'code': 'ACC', 'level': 0},
        ]
        
        for class_data in classifications:
            ItemClassification.objects.get_or_create(
                tenant_id=self.tenant_id,
                code=class_data['code'],
                defaults={
                    'name': class_data['name'],
                    'level': class_data['level']
                }
            )
        self.stdout.write(self.style.SUCCESS('Created item classifications'))
        
        # Create service levels
        service_levels = [
            {'name': 'بلاتيني', 'description': 'أعلى مستوى خدمة مع استجابة فورية', 'priority': 1},
            {'name': 'ذهبي', 'description': 'مستوى خدمة ممتاز مع استجابة سريعة', 'priority': 2},
            {'name': 'فضي', 'description': 'مستوى خدمة جيد مع استجابة معقولة', 'priority': 3},
            {'name': 'برونزي', 'description': 'مستوى خدمة أساسي', 'priority': 4},
        ]
        
        for level_data in service_levels:
            ServiceLevel.objects.get_or_create(
                name=level_data['name'],
                defaults={
                    'description': level_data['description'],
                    'priority': level_data['priority']
                }
            )
        self.stdout.write(self.style.SUCCESS('Created service levels'))
        
        # Create service center types
        center_types = [
            {'name': 'مركز رئيسي', 'description': 'جميع الخدمات متوفرة', 'max_capacity': 50},
            {'name': 'مركز صيانة سريعة', 'description': 'صيانة سريعة وخدمات بسيطة', 'max_capacity': 30},
            {'name': 'مركز صيانة متخصص', 'description': 'صيانة متخصصة لأنواع معينة من السيارات', 'max_capacity': 20},
        ]
        
        for type_data in center_types:
            ServiceCenterType.objects.get_or_create(
                name=type_data['name'],
                defaults={
                    'description': type_data['description'],
                    'max_capacity': type_data['max_capacity']
                }
            )
        self.stdout.write(self.style.SUCCESS('Created service center types'))
        
        # Create work order types
        work_order_types = [
            {'name': 'صيانة دورية', 'description': 'خدمات الصيانة الدورية'},
            {'name': 'إصلاح أعطال', 'description': 'إصلاح الأعطال والمشاكل'},
            {'name': 'صيانة وقائية', 'description': 'الصيانة الوقائية والفحص'},
            {'name': 'إصلاح حوادث', 'description': 'إصلاح أضرار الحوادث'},
            {'name': 'ضبط وموازنة', 'description': 'ضبط وموازنة السيارة'}
        ]
        
        for wo_type_data in work_order_types:
            WorkOrderType.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=wo_type_data['name'],
                defaults={
                    'description': wo_type_data['description']
                }
            )
        self.stdout.write(self.style.SUCCESS('Created work order types'))
        
        # Create customer classifications
        customer_classes = [
            {'name': 'VIP', 'discount_percentage': 15, 'min_spend': 50000},
            {'name': 'ذهبي', 'discount_percentage': 10, 'min_spend': 25000},
            {'name': 'فضي', 'discount_percentage': 5, 'min_spend': 10000},
            {'name': 'عادي', 'discount_percentage': 0, 'min_spend': 0},
        ]
        
        for class_data in customer_classes:
            CustomerClassification.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=class_data['name'],
                defaults={
                    'discount_percentage': class_data['discount_percentage'],
                    'min_spend': class_data['min_spend']
                }
            )
        self.stdout.write(self.style.SUCCESS('Created customer classifications'))

    def generate_franchise_structure(self):
        # Create a main franchise
        franchise, created = Franchise.objects.get_or_create(
            name='شبكة مراكز صيانة أفتر سيلز',
            code='AFTERSAILS',
            defaults={
                'address': 'القاهرة الجديدة، التجمع الخامس',
                'city': 'القاهرة',
                'country': 'مصر',
                'phone': '+20 1000000000',
                'email': '<EMAIL>'
            }
        )
        
        # Create companies under the franchise
        companies = [
            {
                'name': 'أفترسيلز القاهرة',
                'code': 'AS-CAIRO',
                'city': 'القاهرة'
            },
            {
                'name': 'أفترسيلز الإسكندرية',
                'code': 'AS-ALEX',
                'city': 'الإسكندرية'
            },
            {
                'name': 'أفترسيلز الدلتا',
                'code': 'AS-DELTA',
                'city': 'المنصورة'
            }
        ]
        
        company_objects = []
        service_levels = list(ServiceLevel.objects.all())
        
        for company_data in companies:
            company, created = Company.objects.get_or_create(
                franchise=franchise,
                code=company_data['code'],
                defaults={
                    'name': company_data['name'],
                    'city': company_data['city'],
                    'country': 'مصر',
                    'service_level': random.choice(service_levels)
                }
            )
            company_objects.append(company)
        
        self.stdout.write(self.style.SUCCESS('Created franchise and companies'))
        
        # Create service centers
        service_center_types = list(ServiceCenterType.objects.all())
        
        for company in company_objects:
            # Create 2-4 service centers per company
            num_centers = random.randint(2, 4)
            
            for i in range(num_centers):
                city = company.city if i == 0 else random.choice(EGYPTIAN_CITIES)
                
                ServiceCenter.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    company=company,
                    code=f"{company.code}-SC{i+1}",
                    defaults={
                        'name': f"مركز {company.name} - {city}",
                        'center_type': random.choice(service_center_types),
                        'address': f"عنوان عشوائي في {city}",
                        'city': city,
                        'country': 'مصر',
                        'phone': f"+20 10{random.randint(10000000, 99999999)}",
                        'email': f"sc{i+1}@{company.code.lower()}.aftersails.eg",
                        'capacity': random.randint(10, 50),
                        'is_active': True
                    }
                )
        
        self.stdout.write(self.style.SUCCESS('Created service centers'))

    def generate_inventory_data(self, num_items):
        units = list(UnitOfMeasurement.objects.filter(tenant_id=self.tenant_id))
        classifications = list(ItemClassification.objects.filter(tenant_id=self.tenant_id))
        
        # Create inventory items
        for i in range(num_items):
            category = random.choice(PART_CATEGORIES)
            make = random.choice(EGYPTIAN_VEHICLE_MAKES)
            
            item, created = Item.objects.get_or_create(
                tenant_id=self.tenant_id,
                sku=f"P{str(i+1).zfill(5)}",
                defaults={
                    'name': f"{category} - {make}",
                    'description': f"وصف {category} - {make}",
                    'quantity': random.randint(5, 100),
                    'unit_of_measurement': random.choice(units),
                    'unit_price': random.randint(50, 5000),
                    'min_stock_level': random.randint(2, 10),
                    'classification': random.choice(classifications),
                    'category': 'part'
                }
            )
            
            # Create some movements for this item
            if created:
                movement_types = ['purchase', 'adjustment']
                
                for _ in range(random.randint(1, 3)):
                    movement_type = random.choice(movement_types)
                    quantity = random.randint(1, 20)
                    
                    Movement.objects.create(
                        tenant_id=self.tenant_id,
                        item=item,
                        quantity=quantity,
                        movement_type=movement_type,
                        reference=f"REF-{uuid.uuid4().hex[:8].upper()}",
                        unit_of_measurement=item.unit_of_measurement
                    )
        
        self.stdout.write(self.style.SUCCESS(f'Created {num_items} inventory items with movements'))

    def generate_customers(self, num_customers):
        service_centers = list(ServiceCenter.objects.filter(tenant_id=self.tenant_id))
        classifications = list(CustomerClassification.objects.filter(tenant_id=self.tenant_id))
        
        # Create customers
        for i in range(num_customers):
            gender = random.choice(['male', 'female'])
            first_name = random.choice(EGYPTIAN_MALE_FIRST_NAMES if gender == 'male' else EGYPTIAN_FEMALE_FIRST_NAMES)
            last_name = random.choice(EGYPTIAN_LAST_NAMES)
            
            Customer.objects.get_or_create(
                tenant_id=self.tenant_id,
                first_name=first_name,
                last_name=last_name,
                defaults={
                    'email': f"{first_name.lower()}.{last_name.lower()}{random.randint(1, 999)}@example.com",
                    'phone': f"+20 10{random.randint(10000000, 99999999)}",
                    'gender': gender,
                    'city': random.choice(EGYPTIAN_CITIES),
                    'country': 'مصر',
                    'service_center': random.choice(service_centers),
                    'classification': random.choice(classifications),
                    'customer_type': 'individual',
                    'is_active': True
                }
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {num_customers} customers'))

    def generate_vehicles(self, num_vehicles):
        customers = list(Customer.objects.filter(tenant_id=self.tenant_id))
        service_centers = list(ServiceCenter.objects.filter(tenant_id=self.tenant_id))
        
        # Create vehicles
        for i in range(num_vehicles):
            make = random.choice(EGYPTIAN_VEHICLE_MAKES)
            model = random.choice(VEHICLE_MODELS.get(make, ["موديل غير معروف"]))
            year = random.randint(2010, 2023)
            
            Vehicle.objects.get_or_create(
                tenant_id=self.tenant_id,
                vin=f"VIN{str(i+1).zfill(6)}",
                defaults={
                    'make': make,
                    'model': model,
                    'year': year,
                    'license_plate': f"{random.randint(1, 999)}-{random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهو')}-{random.randint(1, 9999)}",
                    'color': random.choice(['أبيض', 'أسود', 'رمادي', 'فضي', 'أحمر', 'أزرق']),
                    'owner': random.choice(customers),
                    'service_center': random.choice(service_centers),
                    'purchase_date': timezone.now().date() - timedelta(days=random.randint(30, 1500)),
                    'warranty_end_date': timezone.now().date() + timedelta(days=random.randint(-500, 500))
                }
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {num_vehicles} vehicles'))

    def generate_work_orders(self, num_work_orders):
        vehicles = list(Vehicle.objects.filter(tenant_id=self.tenant_id))
        service_centers = list(ServiceCenter.objects.filter(tenant_id=self.tenant_id))
        work_order_types = list(WorkOrderType.objects.filter(tenant_id=self.tenant_id))
        items = list(Item.objects.filter(tenant_id=self.tenant_id))
        
        STATUS_CHOICES = ['draft', 'scheduled', 'in_progress', 'completed', 'cancelled']
        
        # Create work orders
        for i in range(num_work_orders):
            vehicle = random.choice(vehicles)
            status = random.choice(STATUS_CHOICES)
            created_date = timezone.now() - timedelta(days=random.randint(1, 180))
            
            if status in ['completed', 'cancelled']:
                completed_date = created_date + timedelta(days=random.randint(1, 10))
            else:
                completed_date = None
            
            work_order = WorkOrder.objects.create(
                tenant_id=self.tenant_id,
                number=f"WO-{str(i+1).zfill(6)}",
                vehicle=vehicle,
                customer=vehicle.owner,
                service_center=vehicle.service_center or random.choice(service_centers),
                work_order_type=random.choice(work_order_types),
                status=status,
                description=f"وصف أمر العمل رقم {i+1}",
                estimated_completion=created_date + timedelta(days=random.randint(1, 5)),
                created_at=created_date,
                completed_at=completed_date
            )
            
            # Add operations and items to the work order
            num_operations = random.randint(1, 5)
            
            for j in range(num_operations):
                operation = Operation.objects.create(
                    tenant_id=self.tenant_id,
                    work_order=work_order,
                    name=f"عملية {j+1} لأمر العمل {work_order.number}",
                    description=f"وصف العملية {j+1}",
                    duration_minutes=random.randint(15, 180)
                )
                
                # Add items to the operation
                num_items = random.randint(0, 3)
                for _ in range(num_items):
                    item = random.choice(items)
                    operation.items.add(item, through_defaults={
                        'tenant_id': self.tenant_id,
                        'quantity': random.randint(1, 3),
                        'unit_price': item.unit_price
                    })
        
        self.stdout.write(self.style.SUCCESS(f'Created {num_work_orders} work orders with operations and items'))

    def generate_billing_data(self):
        # Create invoices for completed work orders
        completed_work_orders = WorkOrder.objects.filter(tenant_id=self.tenant_id, status='completed')
        
        for work_order in completed_work_orders:
            # Calculate total of operation times
            operations = Operation.objects.filter(work_order=work_order)
            
            # Calculate total
            operation_cost = sum(op.duration_minutes * 5 for op in operations)  # 5 per minute rate
            
            # Get items used in operations
            items_cost = 0
            for operation in operations:
                for item_op in operation.items.through.objects.filter(operation=operation):
                    items_cost += item_op.quantity * item_op.unit_price
            
            # Apply classification discount
            discount = 0
            if work_order.customer and work_order.customer.classification:
                discount = work_order.customer.classification.discount_percentage / 100 * (operation_cost + items_cost)
            
            total = operation_cost + items_cost - discount
            
            # Create invoice
            Invoice.objects.create(
                tenant_id=self.tenant_id,
                work_order=work_order,
                customer=work_order.customer,
                invoice_date=work_order.completed_at or timezone.now(),
                due_date=(work_order.completed_at or timezone.now()) + timedelta(days=15),
                subtotal=operation_cost + items_cost,
                discount=discount,
                tax=total * 0.14,  # Egyptian VAT rate
                total=total * 1.14,  # Including VAT
                status=random.choice(['draft', 'issued', 'paid']),
                payment_method=random.choice(['cash', 'credit_card', 'bank_transfer'])
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {completed_work_orders.count()} invoices for completed work orders')) 