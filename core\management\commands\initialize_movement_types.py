from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from inventory.models import MovementType
import uuid


class Command(BaseCommand):
    help = 'Initialize default movement types for inventory management'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            help='The tenant ID to initialize movement types for (required)',
            required=True
        )

    def handle(self, *args, **options):
        tenant_id = options['tenant_id']
        try:
            tenant_uuid = uuid.UUID(tenant_id)
        except ValueError:
            self.stderr.write(self.style.ERROR(f'Invalid tenant ID: {tenant_id}'))
            return
            
        self.stdout.write(self.style.SUCCESS(f'Initializing movement types for tenant: {tenant_id}'))
        
        # Define default movement types
        default_types = [
            # Inbound types
            {
                'code': 'purchase',
                'name': _('Purchase'),
                'description': _('Items purchased from suppliers'),
                'is_inbound': True,
                'is_outbound': False,
                'icon': 'shopping-cart',
                'color': '#28a745',
                'requires_reference': True,
                'sequence': 10,
            },
            {
                'code': 'return_in',
                'name': _('Customer Return'),
                'description': _('Items returned by customers'),
                'is_inbound': True,
                'is_outbound': False,
                'icon': 'reply',
                'color': '#17a2b8',
                'requires_reference': True,
                'sequence': 20,
            },
            {
                'code': 'adjustment_in',
                'name': _('Positive Adjustment'),
                'description': _('Manual stock increase'),
                'is_inbound': True,
                'is_outbound': False,
                'icon': 'plus-circle',
                'color': '#28a745',
                'requires_reference': False,
                'sequence': 30,
            },
            {
                'code': 'production',
                'name': _('Production'),
                'description': _('Items produced internally'),
                'is_inbound': True,
                'is_outbound': False,
                'icon': 'industry',
                'color': '#6f42c1',
                'requires_reference': True,
                'sequence': 40,
            },
            
            # Outbound types
            {
                'code': 'sale',
                'name': _('Sale'),
                'description': _('Items sold to customers'),
                'is_inbound': False,
                'is_outbound': True,
                'icon': 'tag',
                'color': '#007bff',
                'requires_reference': True,
                'sequence': 50,
            },
            {
                'code': 'return_out',
                'name': _('Supplier Return'),
                'description': _('Items returned to suppliers'),
                'is_inbound': False,
                'is_outbound': True,
                'icon': 'share',
                'color': '#fd7e14',
                'requires_reference': True,
                'sequence': 60,
            },
            {
                'code': 'adjustment_out',
                'name': _('Negative Adjustment'),
                'description': _('Manual stock decrease'),
                'is_inbound': False,
                'is_outbound': True,
                'icon': 'minus-circle',
                'color': '#dc3545',
                'requires_reference': False,
                'sequence': 70,
            },
            {
                'code': 'wastage',
                'name': _('Wastage'),
                'description': _('Items damaged, expired or discarded'),
                'is_inbound': False,
                'is_outbound': True,
                'icon': 'trash',
                'color': '#6c757d',
                'requires_reference': False,
                'sequence': 80,
            },
            
            # Transfer types (both inbound and outbound)
            {
                'code': 'transfer',
                'name': _('Location Transfer'),
                'description': _('Items transferred between locations'),
                'is_inbound': True,
                'is_outbound': True,
                'icon': 'exchange-alt',
                'color': '#6c757d',
                'requires_reference': True,
                'sequence': 90,
            },
        ]
        
        # Create or update each type
        created_count = 0
        updated_count = 0
        
        for type_data in default_types:
            try:
                obj, created = MovementType.objects.update_or_create(
                    tenant_id=tenant_uuid,
                    code=type_data['code'],
                    defaults={
                        'name': type_data['name'],
                        'description': type_data['description'],
                        'is_inbound': type_data['is_inbound'],
                        'is_outbound': type_data['is_outbound'],
                        'icon': type_data['icon'],
                        'color': type_data['color'],
                        'requires_reference': type_data['requires_reference'],
                        'sequence': type_data['sequence'],
                        'is_active': True,
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f"  + Created: {type_data['name']}")
                else:
                    updated_count += 1
                    self.stdout.write(f"  ~ Updated: {type_data['name']}")
                    
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Error creating {type_data['code']}: {str(e)}"))
        
        self.stdout.write(self.style.SUCCESS(
            f'Finished! Created {created_count} types, updated {updated_count} types'
        )) 