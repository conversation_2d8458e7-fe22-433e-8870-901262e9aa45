import os
import sys
from django.core.management.base import BaseCommand
from django.db import connections, transaction
from django.apps import apps
from django.conf import settings
import sqlite3
import psycopg2
from psycopg2.extras import RealDictCursor
from django.db.models import ForeignKey, ManyToManyField


class Command(BaseCommand):
    help = 'Migrate data from SQLite to PostgreSQL'

    def add_arguments(self, parser):
        parser.add_argument(
            '--sqlite-path',
            type=str,
            default='db.sqlite3',
            help='Path to SQLite database file'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without actually doing it'
        )
        parser.add_argument(
            '--exclude-apps',
            nargs='*',
            default=['contenttypes', 'auth', 'sessions', 'admin', 'messages'],
            help='Apps to exclude from migration'
        )

    def handle(self, *args, **options):
        sqlite_path = options['sqlite_path']
        dry_run = options['dry_run']
        exclude_apps = options['exclude_apps']

        # Check if SQLite file exists
        if not os.path.exists(sqlite_path):
            self.stdout.write(
                self.style.ERROR(f'SQLite file not found: {sqlite_path}')
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f'Starting migration from {sqlite_path} to PostgreSQL')
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No data will be actually migrated')
            )

        try:
            # Setup database connections
            sqlite_conn = sqlite3.connect(sqlite_path)
            sqlite_conn.row_factory = sqlite3.Row

            # Get PostgreSQL connection details from settings
            pg_config = settings.DATABASES['default']
            pg_conn = psycopg2.connect(
                host=pg_config.get('HOST', 'localhost'),
                port=pg_config.get('PORT', 5432),
                database=pg_config.get('NAME', 'postgres'),
                user=pg_config.get('USER', 'postgres'),
                password=pg_config.get('PASSWORD', '')
            )

            # Get all Django models
            all_models = []
            for app_config in apps.get_app_configs():
                if app_config.label not in exclude_apps:
                    for model in app_config.get_models():
                        all_models.append(model)

            # Sort models by dependencies (models with no foreign keys first)
            sorted_models = self._sort_models_by_dependencies(all_models)

            total_records = 0
            migrated_records = 0

            for model in sorted_models:
                table_name = model._meta.db_table
                
                # Check if table exists in SQLite
                cursor = sqlite_conn.cursor()
                cursor.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    (table_name,)
                )
                
                if not cursor.fetchone():
                    self.stdout.write(
                        self.style.WARNING(f'Table {table_name} not found in SQLite, skipping...')
                    )
                    continue

                # Get record count
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                record_count = cursor.fetchone()[0]
                total_records += record_count

                if record_count == 0:
                    self.stdout.write(f'Table {table_name}: 0 records - skipping')
                    continue

                self.stdout.write(f'Migrating {table_name}: {record_count} records...')

                if not dry_run:
                    try:
                        with transaction.atomic():
                            # Clear existing data in PostgreSQL table
                            with pg_conn.cursor() as pg_cursor:
                                pg_cursor.execute(f'TRUNCATE TABLE "{table_name}" RESTART IDENTITY CASCADE')
                            
                            # Get all records from SQLite
                            cursor.execute(f"SELECT * FROM {table_name}")
                            records = cursor.fetchall()
                            
                            if records:
                                # Get column names
                                columns = [description[0] for description in cursor.description]
                                placeholders = ', '.join(['%s'] * len(columns))
                                columns_str = ', '.join([f'"{col}"' for col in columns])
                                
                                # Insert records into PostgreSQL
                                with pg_conn.cursor() as pg_cursor:
                                    insert_query = f'INSERT INTO "{table_name}" ({columns_str}) VALUES ({placeholders})'
                                    
                                    for record in records:
                                        try:
                                            # Convert SQLite row to tuple
                                            values = tuple(record)
                                            pg_cursor.execute(insert_query, values)
                                        except Exception as e:
                                            self.stdout.write(
                                                self.style.ERROR(f'Error inserting record in {table_name}: {e}')
                                            )
                                            continue
                                
                                pg_conn.commit()
                                migrated_records += record_count
                                self.stdout.write(
                                    self.style.SUCCESS(f'✓ Migrated {record_count} records from {table_name}')
                                )
                    
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error migrating {table_name}: {e}')
                        )
                        pg_conn.rollback()
                        continue
                else:
                    self.stdout.write(f'Would migrate {record_count} records from {table_name}')

            # Update PostgreSQL sequences for auto-increment fields
            if not dry_run:
                self._update_sequences(pg_conn, sorted_models)

            sqlite_conn.close()
            pg_conn.close()

            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f'DRY RUN COMPLETE: Would migrate {total_records} total records')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'Migration completed: {migrated_records}/{total_records} records migrated')
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Migration failed: {e}')
            )
            sys.exit(1)

    def _sort_models_by_dependencies(self, models):
        """Sort models by their foreign key dependencies"""
        sorted_models = []
        remaining_models = models.copy()
        
        while remaining_models:
            # Find models with no unresolved foreign key dependencies
            models_to_add = []
            
            for model in remaining_models:
                has_unresolved_deps = False
                
                for field in model._meta.get_fields():
                    if isinstance(field, ForeignKey):
                        related_model = field.related_model
                        if related_model in remaining_models and related_model != model:
                            has_unresolved_deps = True
                            break
                
                if not has_unresolved_deps:
                    models_to_add.append(model)
            
            if not models_to_add:
                # Break circular dependencies by adding remaining models
                models_to_add = remaining_models
            
            sorted_models.extend(models_to_add)
            for model in models_to_add:
                remaining_models.remove(model)
        
        return sorted_models

    def _update_sequences(self, pg_conn, models):
        """Update PostgreSQL sequences for auto-increment fields"""
        self.stdout.write('Updating PostgreSQL sequences...')
        
        with pg_conn.cursor() as cursor:
            for model in models:
                table_name = model._meta.db_table
                pk_field = model._meta.pk
                
                if pk_field and hasattr(pk_field, 'get_sequence_name'):
                    sequence_name = pk_field.get_sequence_name()
                    try:
                        cursor.execute(f"""
                            SELECT setval(pg_get_serial_sequence('"{table_name}"', '{pk_field.column}'), 
                                         COALESCE(MAX({pk_field.column}), 1))
                            FROM "{table_name}"
                        """)
                        pg_conn.commit()
                    except Exception as e:
                        self.stdout.write(
                            self.style.WARNING(f'Could not update sequence for {table_name}: {e}')
                        )
        
        self.stdout.write(self.style.SUCCESS('Sequences updated')) 