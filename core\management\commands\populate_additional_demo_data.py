from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group, Permission
from django.utils import timezone
from decimal import Decimal
import random
from datetime import date, timedelta

# Import additional models
from admins.models import AdminProfile
from app_settings.models import AppSetting
from feature_flags.models import FeatureFlag
from notifications.models import Notification, NotificationTemplate
from manufacturing.models import ProductionLine, ProductionOrder, ProductionOrderItem
from warehouse.models import Location, StockTransfer, StockTransferItem
from api.models import APIKey, APILog


class Command(BaseCommand):
    help = 'Populate additional demo data for remaining apps in Egypt market'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            default='1',
            help='Tenant ID to use for demo data'
        )

    def handle(self, *args, **options):
        self.tenant_id = options['tenant_id']
        
        self.stdout.write(self.style.SUCCESS('Starting additional Egypt demo data population...'))
        
        # Populate data for remaining apps
        self.create_admin_profiles()
        self.create_app_settings()
        self.create_feature_flags()
        self.create_notifications()
        self.create_manufacturing_data()
        self.create_warehouse_data()
        self.create_api_data()
        
        self.stdout.write(self.style.SUCCESS('Additional Egypt demo data population completed!'))

    def create_admin_profiles(self):
        """Create admin profiles for existing users"""
        self.stdout.write('Creating admin profiles...')
        
        # Get existing users from notes.md
        admin_users = [
            'system_admin', 'franchise_admin', 'company_admin', 
            'center_manager', 'service_advisor', 'technician'
        ]
        
        for username in admin_users:
            try:
                user = User.objects.get(username=username)
                if hasattr(AdminProfile, 'objects'):  # Check if AdminProfile model exists
                    profile, created = AdminProfile.objects.get_or_create(
                        user=user,
                        defaults={
                            'phone': f'+20-10-{random.randint(10000000, 99999999)}',
                            'address': f'القاهرة، مصر',
                            'department': random.choice(['IT', 'Operations', 'Management', 'Service']),
                            'is_active': True
                        }
                    )
                    if created:
                        self.stdout.write(f'  ✓ Created admin profile for {username}')
            except User.DoesNotExist:
                continue
            except Exception as e:
                self.stdout.write(f'  ⚠ Error creating admin profile for {username}: {e}')

    def create_app_settings(self):
        """Create application settings for Egypt market"""
        self.stdout.write('Creating app settings...')
        
        settings_data = [
            {
                'key': 'company_name',
                'value': 'خدمات السيارات المصرية',
                'description': 'اسم الشركة',
                'category': 'general'
            },
            {
                'key': 'default_currency',
                'value': 'EGP',
                'description': 'العملة الافتراضية',
                'category': 'financial'
            },
            {
                'key': 'tax_rate',
                'value': '14.0',
                'description': 'معدل الضريبة المضافة (%)',
                'category': 'financial'
            },
            {
                'key': 'working_hours_start',
                'value': '08:00',
                'description': 'بداية ساعات العمل',
                'category': 'operations'
            },
            {
                'key': 'working_hours_end',
                'value': '17:00',
                'description': 'نهاية ساعات العمل',
                'category': 'operations'
            },
            {
                'key': 'notification_email',
                'value': '<EMAIL>',
                'description': 'بريد إلكتروني للإشعارات',
                'category': 'notifications'
            },
            {
                'key': 'low_stock_threshold',
                'value': '10',
                'description': 'حد التنبيه للمخزون المنخفض',
                'category': 'inventory'
            },
            {
                'key': 'default_payment_terms',
                'value': 'cash',
                'description': 'شروط الدفع الافتراضية',
                'category': 'financial'
            }
        ]
        
        if hasattr(AppSetting, 'objects'):  # Check if AppSetting model exists
            for setting_data in settings_data:
                try:
                    AppSetting.objects.get_or_create(
                        key=setting_data['key'],
                        defaults=setting_data
                    )
                except Exception as e:
                    self.stdout.write(f'  ⚠ Error creating setting {setting_data["key"]}: {e}')

    def create_feature_flags(self):
        """Create feature flags for different modules"""
        self.stdout.write('Creating feature flags...')
        
        flags_data = [
            {
                'name': 'advanced_inventory',
                'description': 'تفعيل إدارة المخزون المتقدمة',
                'is_active': True
            },
            {
                'name': 'multi_location',
                'description': 'تفعيل إدارة المواقع المتعددة',
                'is_active': True
            },
            {
                'name': 'sms_notifications',
                'description': 'تفعيل إشعارات الرسائل النصية',
                'is_active': False
            },
            {
                'name': 'mobile_app',
                'description': 'تفعيل تطبيق الهاتف المحمول',
                'is_active': True
            },
            {
                'name': 'online_booking',
                'description': 'تفعيل الحجز الإلكتروني',
                'is_active': True
            },
            {
                'name': 'fleet_management',
                'description': 'تفعيل إدارة الأساطيل',
                'is_active': False
            }
        ]
        
        if hasattr(FeatureFlag, 'objects'):  # Check if FeatureFlag model exists
            for flag_data in flags_data:
                try:
                    FeatureFlag.objects.get_or_create(
                        name=flag_data['name'],
                        defaults=flag_data
                    )
                except Exception as e:
                    self.stdout.write(f'  ⚠ Error creating feature flag {flag_data["name"]}: {e}')

    def create_notifications(self):
        """Create notification templates and sample notifications"""
        self.stdout.write('Creating notifications...')
        
        # Create notification templates
        templates_data = [
            {
                'name': 'work_order_completed',
                'title': 'تم إنجاز أمر العمل',
                'message': 'تم إنجاز أمر العمل رقم {work_order_number} بنجاح',
                'notification_type': 'work_order'
            },
            {
                'name': 'low_stock_alert',
                'title': 'تنبيه مخزون منخفض',
                'message': 'المنتج {item_name} وصل إلى الحد الأدنى للمخزون',
                'notification_type': 'inventory'
            },
            {
                'name': 'customer_appointment',
                'title': 'موعد عميل جديد',
                'message': 'لديك موعد جديد مع العميل {customer_name} في {appointment_date}',
                'notification_type': 'appointment'
            },
            {
                'name': 'payment_received',
                'title': 'تم استلام دفعة',
                'message': 'تم استلام دفعة بقيمة {amount} EGP من العميل {customer_name}',
                'notification_type': 'payment'
            }
        ]
        
        if hasattr(NotificationTemplate, 'objects'):  # Check if NotificationTemplate model exists
            for template_data in templates_data:
                try:
                    NotificationTemplate.objects.get_or_create(
                        name=template_data['name'],
                        defaults=template_data
                    )
                except Exception as e:
                    self.stdout.write(f'  ⚠ Error creating notification template {template_data["name"]}: {e}')
        
        # Create sample notifications
        if hasattr(Notification, 'objects'):  # Check if Notification model exists
            try:
                users = User.objects.all()[:5]  # Get first 5 users
                for user in users:
                    for i in range(3):  # Create 3 notifications per user
                        Notification.objects.create(
                            user=user,
                            title=f'إشعار تجريبي {i+1}',
                            message=f'هذا إشعار تجريبي رقم {i+1} للمستخدم {user.username}',
                            notification_type='system',
                            is_read=random.choice([True, False]),
                            created_at=timezone.now() - timedelta(days=random.randint(0, 30))
                        )
            except Exception as e:
                self.stdout.write(f'  ⚠ Error creating notifications: {e}')

    def create_manufacturing_data(self):
        """Create manufacturing production lines and orders"""
        self.stdout.write('Creating manufacturing data...')
        
        # Create production lines
        production_lines_data = [
            {
                'name': 'خط إنتاج قطع الغيار',
                'description': 'خط إنتاج قطع غيار السيارات',
                'capacity_per_hour': Decimal('50.0'),
                'location': 'المصنع الرئيسي - القاهرة'
            },
            {
                'name': 'خط تجميع الفلاتر',
                'description': 'خط تجميع فلاتر الهواء والزيت',
                'capacity_per_hour': Decimal('100.0'),
                'location': 'مصنع الإسكندرية'
            },
            {
                'name': 'خط إنتاج الزيوت',
                'description': 'خط تعبئة وتغليف الزيوت',
                'capacity_per_hour': Decimal('200.0'),
                'location': 'مصنع الجيزة'
            }
        ]
        
        for line_data in production_lines_data:
            try:
                ProductionLine.objects.get_or_create(
                    name=line_data['name'],
                    tenant_id=self.tenant_id,
                    defaults=line_data
                )
            except Exception as e:
                self.stdout.write(f'  ⚠ Error creating production line {line_data["name"]}: {e}')
        
        # Create production orders
        try:
            from inventory.models import Item
            production_lines = ProductionLine.objects.filter(tenant_id=self.tenant_id)
            items = Item.objects.filter(tenant_id=self.tenant_id)[:5]  # Get first 5 items
            
            for i in range(8):  # Create 8 production orders
                if production_lines.exists() and items.exists():
                    production_line = random.choice(production_lines)
                    order = ProductionOrder.objects.create(
                        order_number=f'PROD-{str(i+1).zfill(5)}',
                        production_line=production_line,
                        order_date=date.today() - timedelta(days=random.randint(0, 30)),
                        planned_start_date=date.today() + timedelta(days=random.randint(1, 10)),
                        planned_end_date=date.today() + timedelta(days=random.randint(11, 20)),
                        status=random.choice(['planned', 'in_progress', 'completed']),
                        priority=random.choice(['low', 'medium', 'high']),
                        tenant_id=self.tenant_id
                    )
                    
                    # Add items to production order
                    selected_items = random.sample(list(items), random.randint(1, 3))
                    for item in selected_items:
                        ProductionOrderItem.objects.create(
                            production_order=order,
                            item=item,
                            quantity=random.randint(10, 100),
                            tenant_id=self.tenant_id
                        )
        except Exception as e:
            self.stdout.write(f'  ⚠ Error creating production orders: {e}')

    def create_warehouse_data(self):
        """Create warehouse locations and stock transfers"""
        self.stdout.write('Creating warehouse data...')
        
        # Create warehouse locations
        locations_data = [
            {
                'name': 'المخزن الرئيسي',
                'code': 'MAIN-WH',
                'location_type': 'warehouse',
                'address': 'القاهرة الجديدة، القاهرة'
            },
            {
                'name': 'مخزن قطع الغيار',
                'code': 'PARTS-WH',
                'location_type': 'warehouse',
                'address': 'مدينة نصر، القاهرة'
            },
            {
                'name': 'مخزن الزيوت',
                'code': 'OILS-WH',
                'location_type': 'warehouse',
                'address': 'الإسكندرية'
            },
            {
                'name': 'نقطة البيع - النزهة',
                'code': 'POS-01',
                'location_type': 'retail',
                'address': 'النزهة، القاهرة'
            }
        ]
        
        for location_data in locations_data:
            try:
                Location.objects.get_or_create(
                    code=location_data['code'],
                    tenant_id=self.tenant_id,
                    defaults=location_data
                )
            except Exception as e:
                self.stdout.write(f'  ⚠ Error creating location {location_data["code"]}: {e}')
        
        # Create stock transfers
        try:
            from inventory.models import Item
            locations = Location.objects.filter(tenant_id=self.tenant_id)
            items = Item.objects.filter(tenant_id=self.tenant_id)[:10]
            
            if locations.count() >= 2 and items.exists():
                for i in range(6):  # Create 6 stock transfers
                    from_location = random.choice(locations)
                    to_location = random.choice(locations.exclude(id=from_location.id))
                    
                    transfer = StockTransfer.objects.create(
                        transfer_number=f'ST-{str(i+1).zfill(5)}',
                        from_location=from_location,
                        to_location=to_location,
                        transfer_date=date.today() - timedelta(days=random.randint(0, 15)),
                        status=random.choice(['pending', 'in_transit', 'completed']),
                        notes=f'نقل مخزون من {from_location.name} إلى {to_location.name}',
                        tenant_id=self.tenant_id
                    )
                    
                    # Add items to transfer
                    selected_items = random.sample(list(items), random.randint(2, 4))
                    for item in selected_items:
                        StockTransferItem.objects.create(
                            stock_transfer=transfer,
                            item=item,
                            quantity=random.randint(5, 25),
                            tenant_id=self.tenant_id
                        )
        except Exception as e:
            self.stdout.write(f'  ⚠ Error creating stock transfers: {e}')

    def create_api_data(self):
        """Create API keys and sample API logs"""
        self.stdout.write('Creating API data...')
        
        # Create API keys
        api_keys_data = [
            {
                'name': 'Mobile App API Key',
                'description': 'مفتاح API لتطبيق الهاتف المحمول',
                'is_active': True
            },
            {
                'name': 'Integration API Key',
                'description': 'مفتاح API للتكامل مع الأنظمة الخارجية',
                'is_active': True
            },
            {
                'name': 'Analytics API Key',
                'description': 'مفتاح API لأدوات التحليل',
                'is_active': False
            }
        ]
        
        if hasattr(APIKey, 'objects'):  # Check if APIKey model exists
            for key_data in api_keys_data:
                try:
                    APIKey.objects.get_or_create(
                        name=key_data['name'],
                        defaults={
                            **key_data,
                            'key': f'egypt_auto_{random.randint(100000, 999999)}',
                            'created_by': User.objects.filter(is_superuser=True).first()
                        }
                    )
                except Exception as e:
                    self.stdout.write(f'  ⚠ Error creating API key {key_data["name"]}: {e}')
        
        # Create sample API logs
        if hasattr(APILog, 'objects'):  # Check if APILog model exists
            try:
                api_endpoints = [
                    '/api/v1/customers/',
                    '/api/v1/vehicles/',
                    '/api/v1/work-orders/',
                    '/api/v1/inventory/',
                    '/api/v1/sales/'
                ]
                
                for i in range(20):  # Create 20 API log entries
                    APILog.objects.create(
                        endpoint=random.choice(api_endpoints),
                        method=random.choice(['GET', 'POST', 'PUT', 'DELETE']),
                        status_code=random.choice([200, 201, 400, 404, 500]),
                        response_time=random.randint(100, 2000),
                        ip_address=f'192.168.1.{random.randint(1, 254)}',
                        user_agent='Mobile App Egypt Auto v1.0',
                        created_at=timezone.now() - timedelta(days=random.randint(0, 7))
                    )
            except Exception as e:
                self.stdout.write(f'  ⚠ Error creating API logs: {e}')
        
        self.stdout.write(self.style.SUCCESS('✓ Additional demo data created successfully!'))
        self.stdout.write(self.style.SUCCESS('Additional data includes:'))
        self.stdout.write(f'  - Admin profiles for existing users')
        self.stdout.write(f'  - Application settings for Egypt market')
        self.stdout.write(f'  - Feature flags for module control')
        self.stdout.write(f'  - Notification templates and sample notifications')
        self.stdout.write(f'  - Manufacturing production lines and orders')
        self.stdout.write(f'  - Warehouse locations and stock transfers')
        self.stdout.write(f'  - API keys and usage logs') 