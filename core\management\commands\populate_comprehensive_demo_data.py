from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
import random
from datetime import date, timedelta
import uuid

# Import all necessary models
from setup.models import (
    ServiceLevel, Franchise, Company, ServiceCenter, Customer, 
    VehicleMake, VehicleModel, Vehicle
)
from inventory.models import (
    ItemClassification, UnitOfMeasurement, Item, 
    MovementType, Movement, OperationCompatibility
)
from work_orders.models import (
    WorkOrderType, MaintenanceSchedule, ScheduleOperation, OperationPart,
    BillOfMaterials, BOMItem, WorkOrder, WorkOrderOperation, WorkOrderMaterial
)
from sales.models import Customer as SalesCustomer, SalesOrder, SalesOrderItem
from purchases.models import Supplier, PurchaseOrder, PurchaseOrderItem, PurchaseReceipt, PurchaseReceiptItem
from warehouse.models import Location, LocationType, Transfer, TransferOrder, TransferOrderItem


class Command(BaseCommand):
    help = 'Populate comprehensive demo data with interconnected inventory, spare parts, and operations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            default=None,
            help='Tenant ID to use for demo data (will get from existing El Mikaneeky franchise)'
        )

    def handle(self, *args, **options):
        # Get El Mikaneeky franchise tenant_id
        try:
            el_mikaneeky = Franchise.objects.filter(name__icontains="mikaneeky").first()
            if el_mikaneeky:
                self.tenant_id = el_mikaneeky.tenant_id
                self.stdout.write(self.style.SUCCESS(f'Using El Mikaneeky tenant ID: {self.tenant_id}'))
            else:
                self.stdout.write(self.style.ERROR('El Mikaneeky franchise not found. Please run basic demo data first.'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error finding franchise: {e}'))
            return
        
        self.stdout.write(self.style.SUCCESS('Starting comprehensive demo data population...'))
        
        # Populate comprehensive data
        self.create_comprehensive_inventory()
        self.create_maintenance_operations()
        self.create_bills_of_materials()
        self.create_comprehensive_work_orders()
        self.create_warehouse_operations()
        self.create_interconnected_sales()
        self.create_purchase_workflows()
        self.create_inventory_movements()
        
        self.stdout.write(self.style.SUCCESS('Comprehensive demo data population completed!'))

    def create_comprehensive_inventory(self):
        """Create comprehensive inventory with spare parts and consumables"""
        self.stdout.write('Creating comprehensive inventory...')
        
        # Get or create additional units
        additional_units = [
            {'name': 'مجموعة', 'symbol': 'مجموعة'},
            {'name': 'رول', 'symbol': 'رول'},
            {'name': 'غالون', 'symbol': 'غالون'},
            {'name': 'مليلتر', 'symbol': 'مل'},
            {'name': 'جرام', 'symbol': 'جم'}
        ]
        
        for unit_data in additional_units:
            UnitOfMeasurement.objects.get_or_create(
                name=unit_data['name'],
                tenant_id=self.tenant_id,
                defaults=unit_data
            )
        
        # Get units
        piece_unit = UnitOfMeasurement.objects.filter(name='قطعة', tenant_id=self.tenant_id).first()
        liter_unit = UnitOfMeasurement.objects.filter(name='لتر', tenant_id=self.tenant_id).first()
        kg_unit = UnitOfMeasurement.objects.filter(name='كيلوجرام', tenant_id=self.tenant_id).first()
        
        # Create additional classifications
        additional_classifications = [
            {'name': 'نظام التعليق', 'code': 'SUS', 'description': 'قطع غيار نظام التعليق والممتصات'},
            {'name': 'نظام التبريد', 'code': 'COL', 'description': 'قطع غيار نظام التبريد والمبردات'},
            {'name': 'نظام الكهرباء', 'code': 'ELE', 'description': 'قطع غيار النظام الكهربائي'},
            {'name': 'نظام العادم', 'code': 'EXH', 'description': 'قطع غيار نظام العادم'},
            {'name': 'مواد استهلاكية', 'code': 'CON', 'description': 'مواد استهلاكية للصيانة'}
        ]
        
        for class_data in additional_classifications:
            ItemClassification.objects.get_or_create(
                code=class_data['code'],
                tenant_id=self.tenant_id,
                defaults=class_data
            )
        
        # Get classifications
        engine_class = ItemClassification.objects.filter(code='ENG', tenant_id=self.tenant_id).first()
        brake_class = ItemClassification.objects.filter(code='BRK', tenant_id=self.tenant_id).first()
        oil_class = ItemClassification.objects.filter(code='OIL', tenant_id=self.tenant_id).first()
        suspension_class = ItemClassification.objects.filter(code='SUS', tenant_id=self.tenant_id).first()
        cooling_class = ItemClassification.objects.filter(code='COL', tenant_id=self.tenant_id).first()
        electrical_class = ItemClassification.objects.filter(code='ELE', tenant_id=self.tenant_id).first()
        consumable_class = ItemClassification.objects.filter(code='CON', tenant_id=self.tenant_id).first()
        
        # Comprehensive inventory items with realistic Egyptian market pricing
        comprehensive_items = [
            # Engine Parts - Expanded
            {'sku': 'ENG006', 'name': 'شمعات الإشعال', 'price': 45, 'unit': piece_unit, 'classification': engine_class, 'min_stock': 50, 'category': 'part'},
            {'sku': 'ENG007', 'name': 'فلتر هواء المحرك', 'price': 85, 'unit': piece_unit, 'classification': engine_class, 'min_stock': 30, 'category': 'part'},
            {'sku': 'ENG008', 'name': 'حزام التوقيت', 'price': 320, 'unit': piece_unit, 'classification': engine_class, 'min_stock': 15, 'category': 'part'},
            {'sku': 'ENG009', 'name': 'طرمبة المياه', 'price': 850, 'unit': piece_unit, 'classification': engine_class, 'min_stock': 10, 'category': 'part'},
            {'sku': 'ENG010', 'name': 'كاتم الصوت', 'price': 650, 'unit': piece_unit, 'classification': engine_class, 'min_stock': 8, 'category': 'part'},
            
            # Suspension Parts
            {'sku': 'SUS001', 'name': 'ممتص صدمات أمامي', 'price': 750, 'unit': piece_unit, 'classification': suspension_class, 'min_stock': 12, 'category': 'part'},
            {'sku': 'SUS002', 'name': 'ممتص صدمات خلفي', 'price': 680, 'unit': piece_unit, 'classification': suspension_class, 'min_stock': 10, 'category': 'part'},
            {'sku': 'SUS003', 'name': 'سوستة أمامية', 'price': 1200, 'unit': piece_unit, 'classification': suspension_class, 'min_stock': 8, 'category': 'part'},
            {'sku': 'SUS004', 'name': 'جلدة عامود الدركسون', 'price': 180, 'unit': piece_unit, 'classification': suspension_class, 'min_stock': 20, 'category': 'part'},
            
            # Cooling System
            {'sku': 'COL001', 'name': 'رادياتير', 'price': 1800, 'unit': piece_unit, 'classification': cooling_class, 'min_stock': 5, 'category': 'part'},
            {'sku': 'COL002', 'name': 'مروحة التبريد', 'price': 450, 'unit': piece_unit, 'classification': cooling_class, 'min_stock': 8, 'category': 'part'},
            {'sku': 'COL003', 'name': 'ثيرموستات', 'price': 95, 'unit': piece_unit, 'classification': cooling_class, 'min_stock': 25, 'category': 'part'},
            {'sku': 'COL004', 'name': 'سائل التبريد', 'price': 45, 'unit': liter_unit, 'classification': cooling_class, 'min_stock': 100, 'category': 'consumable'},
            
            # Electrical System
            {'sku': 'ELE001', 'name': 'بطارية 70 أمبير', 'price': 1200, 'unit': piece_unit, 'classification': electrical_class, 'min_stock': 10, 'category': 'part'},
            {'sku': 'ELE002', 'name': 'دينامو', 'price': 950, 'unit': piece_unit, 'classification': electrical_class, 'min_stock': 6, 'category': 'part'},
            {'sku': 'ELE003', 'name': 'مارش', 'price': 1100, 'unit': piece_unit, 'classification': electrical_class, 'min_stock': 5, 'category': 'part'},
            {'sku': 'ELE004', 'name': 'لمبة كشاف أمامي', 'price': 120, 'unit': piece_unit, 'classification': electrical_class, 'min_stock': 40, 'category': 'part'},
            
            # Additional Oils and Fluids
            {'sku': 'OIL005', 'name': 'زيت الدفرنس', 'price': 85, 'unit': liter_unit, 'classification': oil_class, 'min_stock': 30, 'category': 'consumable'},
            {'sku': 'OIL006', 'name': 'سائل الكلتش', 'price': 65, 'unit': liter_unit, 'classification': oil_class, 'min_stock': 25, 'category': 'consumable'},
            {'sku': 'OIL007', 'name': 'شحم للجريس', 'price': 35, 'unit': kg_unit, 'classification': oil_class, 'min_stock': 50, 'category': 'consumable'},
            
            # Consumables
            {'sku': 'CON001', 'name': 'ورق الصنفرة', 'price': 15, 'unit': piece_unit, 'classification': consumable_class, 'min_stock': 200, 'category': 'consumable'},
            {'sku': 'CON002', 'name': 'مادة تنظيف المحرك', 'price': 25, 'unit': liter_unit, 'classification': consumable_class, 'min_stock': 80, 'category': 'consumable'},
            {'sku': 'CON003', 'name': 'مادة لحام المعادن', 'price': 120, 'unit': kg_unit, 'classification': consumable_class, 'min_stock': 20, 'category': 'consumable'},
            {'sku': 'CON004', 'name': 'أكياس قمامة للنفايات', 'price': 2, 'unit': piece_unit, 'classification': consumable_class, 'min_stock': 500, 'category': 'consumable'},
        ]
        
        for item_data in comprehensive_items:
            if item_data['unit'] and item_data['classification']:
                Item.objects.get_or_create(
                    sku=item_data['sku'],
                    tenant_id=self.tenant_id,
                    defaults={
                        'name': item_data['name'],
                        'description': f"قطعة غيار أصلية عالية الجودة - {item_data['name']}",
                        'quantity': random.randint(item_data['min_stock'], item_data['min_stock'] * 3),
                        'unit_of_measurement': item_data['unit'],
                        'unit_price': Decimal(str(item_data['price'])),
                        'min_stock_level': item_data['min_stock'],
                        'category': item_data['category'],
                        'classification': item_data['classification']
                    }
                )

    def create_maintenance_operations(self):
        """Create maintenance operations that use inventory items"""
        self.stdout.write('Creating maintenance operations...')
        
        # First create a maintenance schedule
        maintenance_schedule, created = MaintenanceSchedule.objects.get_or_create(
            name='الصيانة الدورية الشاملة',
            tenant_id=self.tenant_id,
            defaults={
                'description': 'جدولة الصيانة الدورية للمركبات',
                'interval_type': 'mileage',
                'mileage_interval': 10000,
                'time_interval_months': 6,
                'is_active': True
            }
        )
        
        # Get or create operation types
        operation_types = [
            {
                'name': 'تغيير زيت المحرك',
                'description': 'تغيير زيت المحرك والفلتر',
                'duration_minutes': 30,
                'sequence': 10
            },
            {
                'name': 'فحص وصيانة الفرامل',
                'description': 'فحص وتغيير تيل وديسك الفرامل',
                'duration_minutes': 90,
                'sequence': 20
            },
            {
                'name': 'صيانة نظام التعليق',
                'description': 'فحص وتغيير ممتصات الصدمات والسوست',
                'duration_minutes': 120,
                'sequence': 30
            },
            {
                'name': 'صيانة نظام التبريد',
                'description': 'فحص وتنظيف نظام التبريد وتغيير السائل',
                'duration_minutes': 60,
                'sequence': 40
            },
            {
                'name': 'صيانة النظام الكهربائي',
                'description': 'فحص البطارية والدينامو والمارش',
                'duration_minutes': 45,
                'sequence': 50
            }
        ]
        
        operations = []
        for op_data in operation_types:
            operation, created = ScheduleOperation.objects.get_or_create(
                name=op_data['name'],
                maintenance_schedule=maintenance_schedule,
                tenant_id=self.tenant_id,
                defaults=op_data
            )
            operations.append(operation)

    def create_bills_of_materials(self):
        """Create Bills of Materials for complex operations"""
        self.stdout.write('Creating bills of materials...')
        
        operations = ScheduleOperation.objects.filter(tenant_id=self.tenant_id)
        items = Item.objects.filter(tenant_id=self.tenant_id)
        
        # Create a sample finished item for BOM
        finished_item = items.first()
        if not finished_item:
            return
        
        for operation in operations:
            # Create BOM for each operation
            bom, created = BillOfMaterials.objects.get_or_create(
                name=f"BOM - {operation.name}",
                finished_item=finished_item,
                tenant_id=self.tenant_id,
                defaults={
                    'description': f"قائمة المواد المطلوبة لعملية {operation.name}",
                    'is_active': True
                }
            )
            
            # Add relevant items to BOM based on operation type
            relevant_items = []
            if 'زيت' in operation.name:
                relevant_items = items.filter(classification__code='OIL')
            elif 'فرامل' in operation.name:
                relevant_items = items.filter(classification__code='BRK')
            elif 'تعليق' in operation.name:
                relevant_items = items.filter(classification__code='SUS')
            elif 'تبريد' in operation.name:
                relevant_items = items.filter(classification__code='COL')
            elif 'كهربائي' in operation.name:
                relevant_items = items.filter(classification__code='ELE')
            else:
                relevant_items = items[:3]  # Default items
            
            for item in relevant_items[:4]:  # Limit to 4 items per BOM
                BOMItem.objects.get_or_create(
                    bom=bom,
                    item=item,
                    tenant_id=self.tenant_id,
                    defaults={
                        'quantity': random.uniform(1, 4),
                        'unit_of_measure': item.unit_of_measurement.symbol if item.unit_of_measurement else 'قطعة',
                        'is_optional': random.choice([True, False])
                    }
                )

    def create_comprehensive_work_orders(self):
        """Create detailed work orders with materials and operations"""
        self.stdout.write('Creating comprehensive work orders...')
        
        vehicles = Vehicle.objects.filter(tenant_id=self.tenant_id)
        operations = ScheduleOperation.objects.filter(tenant_id=self.tenant_id)
        work_order_types = WorkOrderType.objects.filter(tenant_id=self.tenant_id)
        service_centers = ServiceCenter.objects.filter(tenant_id=self.tenant_id)
        items = Item.objects.filter(tenant_id=self.tenant_id)
        
        import time
        timestamp = int(time.time())
        
        for i, vehicle in enumerate(vehicles[:15], 1):  # Create 15 detailed work orders
            work_order = WorkOrder.objects.create(
                work_order_number=f'WO-COMP-{timestamp}-{str(i).zfill(3)}',
                work_order_type=random.choice(work_order_types),
                description=f'صيانة شاملة للمركبة {vehicle.make} {vehicle.model} - {vehicle.license_plate}',
                priority=random.choice(['low', 'medium', 'high']),
                status=random.choice(['planned', 'in_progress', 'completed']),
                service_center=random.choice(service_centers),
                vehicle=vehicle,
                customer=vehicle.owner,
                current_odometer=random.randint(50000, 150000),
                estimated_cost=Decimal('0.0'),  # Will calculate based on operations
                planned_start_date=timezone.now() + timedelta(days=random.randint(1, 30)),
                tenant_id=self.tenant_id
            )
            
            # Add operations to work order
            selected_operations = random.sample(list(operations), random.randint(2, 4))
            total_estimated_cost = Decimal('0.0')
            
            for operation in selected_operations:
                work_order_operation = WorkOrderOperation.objects.create(
                    work_order=work_order,
                    name=operation.name,
                    description=operation.description,
                    duration_minutes=operation.duration_minutes,
                    tenant_id=self.tenant_id
                )
                
                # Calculate labor cost (assuming 50 EGP per hour)
                labor_cost = Decimal('50.0') * (Decimal(str(operation.duration_minutes)) / 60)
                total_estimated_cost += labor_cost
                
                # Add materials for this operation
                operation_items = []
                if 'زيت' in operation.name:
                    operation_items = items.filter(classification__code='OIL')
                elif 'فرامل' in operation.name:
                    operation_items = items.filter(classification__code='BRK')
                elif 'تعليق' in operation.name:
                    operation_items = items.filter(classification__code='SUS')
                elif 'تبريد' in operation.name:
                    operation_items = items.filter(classification__code='COL')
                elif 'كهربائي' in operation.name:
                    operation_items = items.filter(classification__code='ELE')
                
                for item in operation_items[:3]:  # Max 3 items per operation
                    if random.choice([True, False]):  # 50% chance to include each item
                        quantity = random.randint(1, 3)
                        material_cost = item.unit_price * quantity
                        
                        WorkOrderMaterial.objects.create(
                            work_order=work_order,
                            item=item,
                            quantity=quantity,
                            unit_of_measure=item.unit_of_measurement.symbol if item.unit_of_measurement else 'قطعة',
                            is_consumed=False,  # Will be updated when work is completed
                            tenant_id=self.tenant_id
                        )
                        
                        total_estimated_cost += material_cost
            
            # Update work order estimated cost
            work_order.estimated_cost = total_estimated_cost
            work_order.save()

    def create_warehouse_operations(self):
        """Create warehouse locations and transfer movements"""
        self.stdout.write('Creating warehouse operations...')
        
        # Create location types first
        location_types_data = [
            {
                'name': 'Main Warehouse',
                'code': 'WAREHOUSE',
                'description': 'Primary storage facility',
                'is_storage': True,
                'is_receiving': True,
                'is_shipping': True
            },
            {
                'name': 'Service Center Storage',
                'code': 'SERVICE',
                'description': 'Storage at service center',
                'is_storage': True,
                'is_service': True
            },
            {
                'name': 'Retail Point',
                'code': 'RETAIL',
                'description': 'Retail sales point',
                'is_storage': True,
                'is_shipping': True
            }
        ]
        
        location_types = []
        for type_data in location_types_data:
            location_type, created = LocationType.objects.get_or_create(
                code=type_data['code'],
                tenant_id=self.tenant_id,
                defaults=type_data
            )
            location_types.append(location_type)
        
        # Create warehouse locations
        warehouse_type = LocationType.objects.filter(code='WAREHOUSE', tenant_id=self.tenant_id).first()
        service_type = LocationType.objects.filter(code='SERVICE', tenant_id=self.tenant_id).first()
        retail_type = LocationType.objects.filter(code='RETAIL', tenant_id=self.tenant_id).first()
        
        locations_data = [
            {
                'name': 'المخزن الرئيسي - القاهرة',
                'code': 'WH-CAI-01',
                'location_type': warehouse_type,
                'address': 'مدينة نصر، القاهرة',
                'city': 'Cairo'
            },
            {
                'name': 'مخزن قطع الغيار - الإسكندرية',
                'code': 'WH-ALX-01',
                'location_type': warehouse_type,
                'address': 'سموحة، الإسكندرية',
                'city': 'Alexandria'
            },
            {
                'name': 'مخزن الزيوت والسوائل',
                'code': 'WH-OIL-01',
                'location_type': service_type,
                'address': 'الجيزة',
                'city': 'Giza'
            },
            {
                'name': 'نقطة البيع - النزهة',
                'code': 'POS-NAZ-01',
                'location_type': retail_type,
                'address': 'النزهة، القاهرة الجديدة',
                'city': 'New Cairo'
            }
        ]
        
        locations = []
        for location_data in locations_data:
            location, created = Location.objects.get_or_create(
                code=location_data['code'],
                tenant_id=self.tenant_id,
                defaults=location_data
            )
            locations.append(location)
        
        # Create transfer orders between locations
        items = Item.objects.filter(tenant_id=self.tenant_id)
        
        import time
        timestamp = int(time.time())
        
        for i in range(10):  # Create 10 transfer orders
            if len(locations) >= 2:
                source_location = random.choice(locations)
                destination_location = random.choice([loc for loc in locations if loc != source_location])
                
                transfer_order = TransferOrder.objects.create(
                    reference=f'TO-COMP-{timestamp}-{str(i+1).zfill(3)}',
                    source_location=source_location,
                    destination_location=destination_location,
                    status=random.choice(['pending', 'in_transit', 'completed']),
                    notes=f'نقل مخزون من {source_location.name} إلى {destination_location.name}',
                    tenant_id=self.tenant_id
                )
                
                # Add items to transfer order
                selected_items = random.sample(list(items), min(len(items), random.randint(3, 6)))
                for item in selected_items:
                    TransferOrderItem.objects.create(
                        transfer_order=transfer_order,
                        item=item,
                        quantity=random.randint(5, 30),
                        notes=f'نقل {item.name}',
                        tenant_id=self.tenant_id
                    )
                
                # Update items count
                transfer_order.items_count = transfer_order.items.count()
                transfer_order.save()
                
                # Create simple transfer record for completed transfers
                if transfer_order.status == 'completed':
                    Transfer.objects.create(
                        source_location=source_location,
                        destination_location=destination_location,
                        items_count=transfer_order.items_count,
                        reference=transfer_order.reference,
                        notes=transfer_order.notes,
                        tenant_id=self.tenant_id
                    )

    def create_interconnected_sales(self):
        """Create sales orders that reduce inventory"""
        self.stdout.write('Creating interconnected sales...')
        
        sales_customers = SalesCustomer.objects.filter(tenant_id=self.tenant_id)
        items = Item.objects.filter(tenant_id=self.tenant_id)
        
        # Create additional sales orders
        import time
        timestamp = int(time.time())
        
        for i in range(25):  # Create 25 sales orders
            customer = random.choice(list(sales_customers))
            order = SalesOrder.objects.create(
                order_number=f'SO-COMP-{timestamp}-{str(i+1).zfill(3)}',
                customer=customer,
                order_date=date.today() - timedelta(days=random.randint(0, 60)),
                status=random.choice(['confirmed', 'shipped', 'delivered']),
                tenant_id=self.tenant_id
            )
            
            # Add items that would actually be sold together
            selected_items = random.sample(list(items), min(len(items), random.randint(2, 6)))
            
            for item in selected_items:
                quantity = random.randint(1, 8)
                # Add markup to cost price for retail
                unit_price = item.unit_price * Decimal(str(random.uniform(1.2, 1.8)))
                
                SalesOrderItem.objects.create(
                    sales_order=order,
                    item=item,
                    quantity=quantity,
                    unit_price=unit_price,
                    tenant_id=self.tenant_id
                )
                
                # Reduce inventory for delivered orders
                if order.status == 'delivered':
                    if item.quantity >= quantity:
                        item.quantity -= quantity
                        item.save()

    def create_purchase_workflows(self):
        """Create purchase orders and receipts that increase inventory"""
        self.stdout.write('Creating purchase workflows...')
        
        suppliers = Supplier.objects.filter(tenant_id=self.tenant_id)
        items = Item.objects.filter(tenant_id=self.tenant_id)
        
        # Create additional purchase orders
        import time
        timestamp = int(time.time())
        
        for i in range(20):  # Create 20 purchase orders
            supplier = random.choice(list(suppliers))
            
            # Create purchase order
            po = PurchaseOrder.objects.create(
                order_number=f'PO-COMP-{timestamp}-{str(i+1).zfill(3)}',
                supplier=supplier,
                order_date=date.today() - timedelta(days=random.randint(10, 90)),
                expected_delivery_date=date.today() + timedelta(days=random.randint(5, 30)),
                status=random.choice(['confirmed', 'received', 'partially_received']),
                tenant_id=self.tenant_id
            )
            
            # Add items to purchase order
            selected_items = random.sample(list(items), min(len(items), random.randint(4, 10)))
            
            for item in selected_items:
                quantity = random.randint(20, 100)
                # Wholesale pricing (lower than retail)
                unit_price = item.unit_price * Decimal(str(random.uniform(0.6, 0.8)))
                
                po_item = PurchaseOrderItem.objects.create(
                    purchase_order=po,
                    item=item,
                    quantity=quantity,
                    unit_price=unit_price,
                    tenant_id=self.tenant_id
                )
                
                # Create receipt for received orders
                if po.status in ['received', 'partially_received']:
                    received_qty = quantity if po.status == 'received' else random.randint(1, quantity)
                    
                    # Create receipt if it doesn't exist
                    receipt, created = PurchaseReceipt.objects.get_or_create(
                        purchase_order=po,
                        tenant_id=self.tenant_id,
                        defaults={
                            'receipt_number': f'PR-{po.order_number}',
                            'receipt_date': po.order_date + timedelta(days=random.randint(3, 15)),
                            'notes': f'استلام بضاعة من {supplier.name}'
                        }
                    )
                    
                    PurchaseReceiptItem.objects.create(
                        purchase_receipt=receipt,
                        purchase_order_item=po_item,
                        quantity=received_qty,
                        tenant_id=self.tenant_id
                    )
                    
                    # Increase inventory for received items
                    item.quantity += received_qty
                    item.save()

    def create_inventory_movements(self):
        """Create inventory movements to track all transactions"""
        self.stdout.write('Creating inventory movements...')
        
        # Get movement types
        movement_types = MovementType.objects.filter(tenant_id=self.tenant_id)
        if not movement_types.exists():
            self.stdout.write('No movement types found. Creating basic ones.')
            # Create basic movement types
            MovementType.objects.create(
                code='PURCHASE',
                name='شراء',
                is_inbound=True,
                is_outbound=False,
                tenant_id=self.tenant_id
            )
            MovementType.objects.create(
                code='SALE',
                name='بيع',
                is_inbound=False,
                is_outbound=True,
                tenant_id=self.tenant_id
            )
            MovementType.objects.create(
                code='ADJUSTMENT',
                name='تعديل',
                is_inbound=True,
                is_outbound=True,
                tenant_id=self.tenant_id
            )
        
        purchase_type = MovementType.objects.filter(code='PURCHASE', tenant_id=self.tenant_id).first()
        sale_type = MovementType.objects.filter(code='SALE', tenant_id=self.tenant_id).first()
        adjustment_type = MovementType.objects.filter(code='ADJUSTMENT', tenant_id=self.tenant_id).first()
        
        items = Item.objects.filter(tenant_id=self.tenant_id)
        
        # Create movements for sales (outbound)
        sales_items = SalesOrderItem.objects.filter(tenant_id=self.tenant_id)
        for sales_item in sales_items[:20]:  # Limit to avoid too many records
            if sales_item.sales_order.status == 'delivered' and sale_type:
                Movement.objects.create(
                    item=sales_item.item,
                    movement_type_ref=sale_type,
                    quantity=-sales_item.quantity,  # Negative for outbound
                    reference=sales_item.sales_order.order_number,
                    notes=f'بيع للعميل {sales_item.sales_order.customer.name}',
                    tenant_id=self.tenant_id
                )
        
        # Create movements for purchases (inbound)
        receipt_items = PurchaseReceiptItem.objects.filter(tenant_id=self.tenant_id)
        for receipt_item in receipt_items[:20]:  # Limit to avoid too many records
            if purchase_type:
                Movement.objects.create(
                    item=receipt_item.purchase_order_item.item,
                    movement_type_ref=purchase_type,
                    quantity=receipt_item.quantity,  # Positive for inbound
                    reference=receipt_item.purchase_receipt.receipt_number,
                    notes=f'شراء من المورد {receipt_item.purchase_receipt.purchase_order.supplier.name}',
                    tenant_id=self.tenant_id
                )
        
        # Create some adjustment movements
        for item in items[:10]:  # Create adjustments for first 10 items
            if adjustment_type:
                adjustment_qty = random.randint(-5, 10)
                if adjustment_qty != 0:
                    Movement.objects.create(
                        item=item,
                        movement_type_ref=adjustment_type,
                        quantity=adjustment_qty,
                        reference=f'ADJ-{random.randint(1000, 9999)}',
                        notes='تعديل المخزون - جرد دوري',
                        tenant_id=self.tenant_id
                    )
        
        self.stdout.write(self.style.SUCCESS('✓ Comprehensive demo data created successfully!'))
        self.stdout.write(self.style.SUCCESS('Data includes:'))
        self.stdout.write(f'  - Comprehensive inventory with {Item.objects.filter(tenant_id=self.tenant_id).count()} items')
        self.stdout.write(f'  - {ScheduleOperation.objects.filter(tenant_id=self.tenant_id).count()} maintenance operations')
        self.stdout.write(f'  - {BillOfMaterials.objects.filter(tenant_id=self.tenant_id).count()} bills of materials')
        self.stdout.write(f'  - {WorkOrder.objects.filter(tenant_id=self.tenant_id).count()} detailed work orders')
        self.stdout.write(f'  - {TransferOrder.objects.filter(tenant_id=self.tenant_id).count()} warehouse transfer orders')
        self.stdout.write(f'  - {SalesOrder.objects.filter(tenant_id=self.tenant_id).count()} sales orders')
        self.stdout.write(f'  - {PurchaseOrder.objects.filter(tenant_id=self.tenant_id).count()} purchase orders')
        self.stdout.write(f'  - {Movement.objects.filter(tenant_id=self.tenant_id).count()} inventory movements')
        self.stdout.write(self.style.SUCCESS('All data is interconnected and affects inventory levels!')) 