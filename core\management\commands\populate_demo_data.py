from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Demo data creation has been disabled - this command no longer populates demo data'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.WARNING(
                'Demo data creation has been disabled. '
                'This command no longer populates demo data. '
                'Please use the real system to create data as needed.'
            )
        ) 