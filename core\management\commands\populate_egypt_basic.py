from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
import random
from datetime import date, timedelta
import uuid

# Import all necessary models
from setup.models import (
    ServiceLevel, Franchise, Company, ServiceCenter, Customer, 
    VehicleMake, VehicleModel, Vehicle, ServiceCenterType
)
from inventory.models import (
    ItemClassification, UnitOfMeasurement, Item
)


class Command(BaseCommand):
    help = 'Populate basic demo data for Egypt market with EGP currency'

    def handle(self, *args, **options):
        self.tenant_id = uuid.uuid4()
        self.stdout.write(self.style.SUCCESS(f'Generated tenant ID: {self.tenant_id}'))
        
        self.stdout.write(self.style.SUCCESS('Starting Egypt basic demo data population...'))
        
        # Create basic data first
        self.create_service_levels()
        self.create_franchises_and_companies()
        self.create_vehicle_makes_and_models()
        self.create_basic_inventory()
        self.create_sample_customers()
        
        self.stdout.write(self.style.SUCCESS('Egypt basic demo data population completed!'))

    def create_service_levels(self):
        """Create service levels for Egypt market"""
        self.stdout.write('Creating service levels...')
        
        service_levels = [
            {
                'name': 'Bronze Service',
                'description': 'Basic service level for standard customers',
                'priority': 3,
                'response_time_hours': 48,
                'resolution_time_hours': 120,
                'emergency_response_time_hours': 12,
                'availability_target_percent': Decimal('95.0')
            },
            {
                'name': 'Silver Service', 
                'description': 'Enhanced service level for preferred customers',
                'priority': 2,
                'response_time_hours': 24,
                'resolution_time_hours': 72,
                'emergency_response_time_hours': 6,
                'availability_target_percent': Decimal('98.0')
            },
            {
                'name': 'Gold Service',
                'description': 'Premium service level for VIP customers',
                'priority': 1,
                'response_time_hours': 12,
                'resolution_time_hours': 48,
                'emergency_response_time_hours': 4,
                'availability_target_percent': Decimal('99.5')
            }
        ]
        
        for level_data in service_levels:
            service_level, created = ServiceLevel.objects.get_or_create(
                name=level_data['name'],
                defaults=level_data
            )
            if created:
                self.stdout.write(f'  ✓ Created service level: {service_level.name}')

    def create_franchises_and_companies(self):
        """Create franchises and companies for Egypt market"""
        self.stdout.write('Creating franchises and companies...')
        
        # Create main franchise
        franchise, created = Franchise.objects.get_or_create(
            code="EAS",
            defaults={
                'name': "Egypt Auto Services",
                'tenant_id': self.tenant_id,
                'notes': 'Leading automotive service franchise in Egypt',
                'address': 'Cairo, Egypt',
                'city': 'Cairo',
                'country': 'Egypt',
                'phone': '+20-2-12345678',
                'email': '<EMAIL>',
                'is_active': True
            }
        )
        if created:
            self.stdout.write(f'  ✓ Created franchise: {franchise.name}')
        
        # Create service center types first
        center_types = [
            {'name': 'Standard Service Center', 'description': 'Standard automotive service center', 'max_capacity': 50},
            {'name': 'Express Service', 'description': 'Quick service center for basic maintenance', 'max_capacity': 30}
        ]
        
        for type_data in center_types:
            center_type, created = ServiceCenterType.objects.get_or_create(
                name=type_data['name'],
                defaults=type_data
            )
            if created:
                self.stdout.write(f'  ✓ Created service center type: {center_type.name}')
        
        # Create companies
        companies_data = [
            {
                'name': 'Cairo Auto Center',
                'code': 'CAC',
                'city': 'Cairo',
                'address': 'Nasr City, Cairo, Egypt',
                'phone': '+20-2-11111111'
            },
            {
                'name': 'Alexandria Motors',
                'code': 'ALX',
                'city': 'Alexandria', 
                'address': 'Smouha, Alexandria, Egypt',
                'phone': '+20-3-22222222'
            }
        ]
        
        for company_data in companies_data:
            company, created = Company.objects.get_or_create(
                code=company_data['code'],
                defaults={
                    'name': company_data['name'],
                    'franchise': franchise,
                    'tenant_id': self.tenant_id,
                    'city': company_data['city'],
                    'address': company_data['address'],
                    'phone': company_data['phone'],
                    'country': 'Egypt',
                    'email': f"{company_data['code'].lower()}@egyptautoservices.com",
                    'is_active': True,
                    'service_level': ServiceLevel.objects.first()
                }
            )
            if created:
                self.stdout.write(f'  ✓ Created company: {company.name}')
        
        # Create service centers
        companies = Company.objects.filter(tenant_id=self.tenant_id)
        default_center_type = ServiceCenterType.objects.first()
        
        for company in companies:
            center, created = ServiceCenter.objects.get_or_create(
                code=f'{company.code}-01',
                tenant_id=self.tenant_id,
                defaults={
                    'name': f'{company.name} Main Center',
                    'company': company,
                    'center_type': default_center_type,
                    'address': company.address,
                    'capacity': 30,
                    'phone': company.phone,
                    'email': f"{company.code.lower()}-<EMAIL>",
                    'is_active': True,
                    'service_level': ServiceLevel.objects.first()
                }
            )
            if created:
                self.stdout.write(f'  ✓ Created service center: {center.name}')

    def create_vehicle_makes_and_models(self):
        """Create popular vehicle makes and models in Egypt"""
        self.stdout.write('Creating vehicle makes and models...')
        
        # Popular car brands in Egypt
        makes_models = {
            'Toyota': ['Corolla', 'Camry', 'RAV4'],
            'Hyundai': ['Elantra', 'Accent', 'Tucson'],
            'Nissan': ['Sunny', 'Sentra', 'X-Trail'],
            'Chevrolet': ['Cruze', 'Aveo', 'Captiva'],
            'Renault': ['Logan', 'Megane', 'Duster']
        }
        
        for make_name, models in makes_models.items():
            # Check if make already exists (due to unique constraint)
            try:
                make = VehicleMake.objects.get(name=make_name)
                self.stdout.write(f'  → Make {make_name} already exists')
            except VehicleMake.DoesNotExist:
                make = VehicleMake.objects.create(
                    name=make_name,
                    description=f'{make_name} vehicles available in Egypt',
                    is_active=True,
                    tenant_id=self.tenant_id
                )
                self.stdout.write(f'  ✓ Created vehicle make: {make.name}')
            
            for model_name in models:
                model, created = VehicleModel.objects.get_or_create(
                    make=make,
                    name=model_name,
                    tenant_id=self.tenant_id,
                    defaults={
                        'description': f'{make_name} {model_name}',
                        'year_introduced': random.randint(2010, 2020),
                        'vehicle_class': random.choice(['Sedan', 'Hatchback', 'SUV']),
                        'is_active': True
                    }
                )
                if created:
                    self.stdout.write(f'  ✓ Created vehicle model: {model}')

    def create_basic_inventory(self):
        """Create basic inventory structure"""
        self.stdout.write('Creating basic inventory...')
        
        # Create units of measurement
        units = [
            {'name': 'قطعة', 'symbol': 'قطعة'},
            {'name': 'لتر', 'symbol': 'لتر'},
            {'name': 'كيلوجرام', 'symbol': 'كجم'}
        ]
        
        for unit_data in units:
            unit, created = UnitOfMeasurement.objects.get_or_create(
                name=unit_data['name'],
                tenant_id=self.tenant_id,
                defaults=unit_data
            )
            if created:
                self.stdout.write(f'  ✓ Created unit: {unit.name}')
        
        # Create item classifications
        classifications = [
            {'name': 'قطع غيار المحرك', 'code': 'ENG', 'description': 'قطع غيار وصيانة المحرك'},
            {'name': 'قطع غيار الفرامل', 'code': 'BRK', 'description': 'نظام الفرامل والأمان'},
            {'name': 'زيوت ومواد التشحيم', 'code': 'OIL', 'description': 'زيوت المحرك وزيوت الفتيس'}
        ]
        
        for class_data in classifications:
            classification, created = ItemClassification.objects.get_or_create(
                code=class_data['code'],
                tenant_id=self.tenant_id,
                defaults=class_data
            )
            if created:
                self.stdout.write(f'  ✓ Created classification: {classification.name}')
        
        # Create some basic items
        piece_unit = UnitOfMeasurement.objects.filter(name='قطعة', tenant_id=self.tenant_id).first()
        liter_unit = UnitOfMeasurement.objects.filter(name='لتر', tenant_id=self.tenant_id).first()
        engine_class = ItemClassification.objects.filter(code='ENG', tenant_id=self.tenant_id).first()
        oil_class = ItemClassification.objects.filter(code='OIL', tenant_id=self.tenant_id).first()
        
        items_data = [
            {
                'sku': 'ENG001',
                'name': 'مكابس المحرك',
                'price': 1500,
                'unit': piece_unit,
                'classification': engine_class
            },
            {
                'sku': 'OIL001',
                'name': 'زيت محرك 5W-30',
                'price': 180,
                'unit': liter_unit,
                'classification': oil_class
            }
        ]
        
        for item_data in items_data:
            if item_data['unit'] and item_data['classification']:
                item, created = Item.objects.get_or_create(
                    sku=item_data['sku'],
                    tenant_id=self.tenant_id,
                    defaults={
                        'name': item_data['name'],
                        'description': f"قطعة أصلية - {item_data['name']}",
                        'quantity': random.randint(20, 100),
                        'unit_of_measurement': item_data['unit'],
                        'unit_price': Decimal(str(item_data['price'])),
                        'min_stock_level': 10,
                        'category': 'part',
                        'classification': item_data['classification']
                    }
                )
                if created:
                    self.stdout.write(f'  ✓ Created item: {item.name}')

    def create_sample_customers(self):
        """Create sample customers"""
        self.stdout.write('Creating sample customers...')
        
        # Egyptian names
        customers_data = [
            {
                'first_name': 'Ahmed',
                'last_name': 'Hassan',
                'phone': '+20-10-12345678',
                'city': 'Cairo'
            },
            {
                'first_name': 'Mohamed',
                'last_name': 'Ali',
                'phone': '+20-10-23456789',
                'city': 'Alexandria'
            },
            {
                'first_name': 'Mahmoud',
                'last_name': 'Ibrahim',
                'phone': '+20-10-34567890',
                'city': 'Giza'
            }
        ]
        
        service_centers = ServiceCenter.objects.filter(tenant_id=self.tenant_id)
        
        for customer_data in customers_data:
            customer, created = Customer.objects.get_or_create(
                phone=customer_data['phone'],
                tenant_id=self.tenant_id,
                defaults={
                    'first_name': customer_data['first_name'],
                    'last_name': customer_data['last_name'],
                    'email': f"{customer_data['first_name'].lower()}.{customer_data['last_name'].lower()}@email.com",
                    'address': f"شارع الجمهورية، {customer_data['city']}، مصر",
                    'city': customer_data['city'],
                    'country': 'Egypt',
                    'customer_type': 'individual',
                    'gender': 'male',
                    'is_active': True,
                    'service_center': service_centers.first() if service_centers.exists() else None
                }
            )
            if created:
                self.stdout.write(f'  ✓ Created customer: {customer.full_name}')
        
        # Print summary
        self.stdout.write(self.style.SUCCESS('\n=== Egypt Demo Data Summary ==='))
        self.stdout.write(f'Service Levels: {ServiceLevel.objects.count()}')
        self.stdout.write(f'Franchises: {Franchise.objects.filter(tenant_id=self.tenant_id).count()}')
        self.stdout.write(f'Companies: {Company.objects.filter(tenant_id=self.tenant_id).count()}')
        self.stdout.write(f'Service Centers: {ServiceCenter.objects.filter(tenant_id=self.tenant_id).count()}')
        self.stdout.write(f'Vehicle Makes: {VehicleMake.objects.filter(tenant_id=self.tenant_id).count()}')
        self.stdout.write(f'Vehicle Models: {VehicleModel.objects.filter(tenant_id=self.tenant_id).count()}')
        self.stdout.write(f'Customers: {Customer.objects.filter(tenant_id=self.tenant_id).count()}')
        self.stdout.write(f'Items: {Item.objects.filter(tenant_id=self.tenant_id).count()}')
        self.stdout.write(f'Tenant ID: {self.tenant_id}')
        self.stdout.write(self.style.SUCCESS('All prices are in EGP (Egyptian Pounds)')) 