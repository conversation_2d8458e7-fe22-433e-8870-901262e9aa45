from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
import random
from datetime import date, timedelta
import uuid

# Import all necessary models
from setup.models import (
    ServiceLevel, Franchise, Company, ServiceCenter, Customer, 
    VehicleMake, VehicleModel, Vehicle, ServiceHistory
)
from inventory.models import (
    ItemClassification, UnitOfMeasurement, UnitConversion, Item, 
    MovementType, Movement, VehicleCompatibility, OperationCompatibility,
    VehicleModelPart, OperationPricing, PartPricing
)
from work_orders.models import (
    WorkOrderType, MaintenanceSchedule, ScheduleOperation, OperationPart,
    BillOfMaterials, BOMItem, WorkOrder, WorkOrderOperation, WorkOrderMaterial
)
from sales.models import Customer as SalesCustomer, SalesOrder, SalesOrderItem
from purchases.models import Supplier, PurchaseOrder, PurchaseOrderItem, PurchaseReceipt, PurchaseReceiptItem
from billing.models import CustomerPreference, InsurancePolicy, WarrantyType
from billing.models_classification import CustomerClassification, ClassificationCriteria
from user_roles.models import Role, UserRole
from reports.models import Report, Dashboard


class Command(BaseCommand):
    help = 'Populate demo data for Egypt market with EGP currency'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            default=None,
            help='Tenant ID to use for demo data (will generate UUID if not provided)'
        )
        parser.add_argument(
            '--clear-data',
            action='store_true',
            help='Clear existing data before populating'
        )

    def handle(self, *args, **options):
        # Generate or use provided tenant_id as UUID
        if options['tenant_id']:
            try:
                self.tenant_id = uuid.UUID(options['tenant_id'])
            except ValueError:
                # If not a valid UUID, generate a new one
                self.tenant_id = uuid.uuid4()
                self.stdout.write(self.style.WARNING(f'Invalid UUID provided, generated new one: {self.tenant_id}'))
        else:
            self.tenant_id = uuid.uuid4()
            self.stdout.write(self.style.SUCCESS(f'Generated tenant ID: {self.tenant_id}'))
        
        if options['clear_data']:
            self.stdout.write(self.style.WARNING('Clearing existing demo data...'))
            # Clear data in reverse dependency order
            self.clear_data()
        
        self.stdout.write(self.style.SUCCESS('Starting Egypt demo data population...'))
        
        # Populate data in dependency order
        self.create_service_levels()
        self.create_franchises_and_companies()
        self.create_vehicle_makes_and_models()
        self.create_customers()
        self.create_vehicles()
        self.create_customer_classifications()
        self.create_inventory_data()
        self.create_work_order_data()
        self.create_sales_data()
        self.create_purchase_data()
        self.create_billing_data()
        self.create_reports_and_dashboards()
        
        self.stdout.write(self.style.SUCCESS('Egypt demo data population completed!'))

    def clear_data(self):
        """Clear existing demo data"""
        # Clear in reverse dependency order
        try:
            Dashboard.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            Report.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            WorkOrder.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            SalesOrder.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            PurchaseOrder.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            Item.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            Vehicle.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            Customer.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            ServiceCenter.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            Company.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass
        try:
            Franchise.objects.filter(tenant_id=self.tenant_id).delete()
        except:
            pass

    def create_service_levels(self):
        """Create service levels for Egypt market"""
        self.stdout.write('Creating service levels...')
        
        service_levels = [
            {
                'name': 'Bronze Service',
                'description': 'Basic service level for standard customers',
                'priority': 3,
                'response_time_hours': 48,
                'resolution_time_hours': 120,
                'emergency_response_time_hours': 12,
                'availability_target_percent': Decimal('95.0')
            },
            {
                'name': 'Silver Service',
                'description': 'Enhanced service level for preferred customers',
                'priority': 2,
                'response_time_hours': 24,
                'resolution_time_hours': 72,
                'emergency_response_time_hours': 6,
                'availability_target_percent': Decimal('98.0')
            },
            {
                'name': 'Gold Service',
                'description': 'Premium service level for VIP customers',
                'priority': 1,
                'response_time_hours': 12,
                'resolution_time_hours': 48,
                'emergency_response_time_hours': 4,
                'availability_target_percent': Decimal('99.5')
            }
        ]
        
        for level_data in service_levels:
            # Note: ServiceLevel model may not have tenant_id field
            ServiceLevel.objects.get_or_create(
                name=level_data['name'],
                defaults=level_data
            )

    def create_franchises_and_companies(self):
        """Create franchises and companies for Egypt market"""
        self.stdout.write('Creating franchises and companies...')
        
        # Create main franchise - El Mikaneeky
        franchise, created = Franchise.objects.get_or_create(
            code="ELMIK",
            defaults={
                'name': "El Mikaneeky",
                'tenant_id': self.tenant_id,
                'notes': 'Leading automotive service franchise in Egypt - specializing in mechanical, electrical, and comprehensive car maintenance services. Partners with BOSCH for advanced diagnostic equipment.',
                'address': 'Sheikh Zayed, Giza, Egypt',
                'city': 'Giza',
                'country': 'Egypt',
                'phone': '+20-2-38851234',
                'email': '<EMAIL>',
                'is_active': True
            }
        )
        
        # Create El Mikaneeky companies in different Egyptian cities
        companies_data = [
            {
                'name': 'El Mikaneeky Cairo',
                'code': 'ELMIK-CAI',
                'city': 'Cairo',
                'address': 'Nasr City, Cairo, Egypt',
                'phone': '+20-2-26751234'
            },
            {
                'name': 'El Mikaneeky Alexandria',
                'code': 'ELMIK-ALX',
                'city': 'Alexandria',
                'address': 'Smouha, Alexandria, Egypt',
                'phone': '+20-3-42281234'
            },
            {
                'name': 'El Mikaneeky Sheikh Zayed',
                'code': 'ELMIK-SZ',
                'city': 'Sheikh Zayed',
                'address': '6th of October, Giza, Egypt',
                'phone': '+20-2-38851234'
            },
            {
                'name': 'El Mikaneeky New Cairo',
                'code': 'ELMIK-NC',
                'city': 'New Cairo',
                'address': '5th Settlement, New Cairo, Egypt',
                'phone': '+20-2-25981234'
            }
        ]
        
        import time
        tenant_short = str(self.tenant_id)[:8]  # Use first 8 chars of tenant_id for uniqueness
        
        for company_data in companies_data:
            unique_code = f"{company_data['code']}-{tenant_short}"
            Company.objects.get_or_create(
                code=unique_code,
                tenant_id=self.tenant_id,  # Include tenant_id in lookup
                defaults={
                    'name': company_data['name'],
                    'franchise': franchise,
                    'city': company_data['city'],
                    'address': company_data['address'],
                    'phone': company_data['phone'],
                    'country': 'Egypt',
                    'email': f"{company_data['code'].lower()}@elmikaneeky.com",
                    'is_active': True,
                    'service_level': ServiceLevel.objects.first()  # ServiceLevel doesn't use tenant_id
                }
            )
        
        # Create service center types first
        from setup.models import ServiceCenterType
        center_types = [
            {'name': 'Standard Service Center', 'description': 'Standard automotive service center', 'max_capacity': 50},
            {'name': 'Express Service', 'description': 'Quick service center for basic maintenance', 'max_capacity': 30},
            {'name': 'Specialty Shop', 'description': 'Specialized service center', 'max_capacity': 20}
        ]
        
        for type_data in center_types:
            ServiceCenterType.objects.get_or_create(
                name=type_data['name'],
                defaults=type_data
            )
        
        # Create service centers
        companies = Company.objects.filter(tenant_id=self.tenant_id)
        default_center_type = ServiceCenterType.objects.first()
        service_centers_data = [
            {
                'name': 'El Mikaneeky Cairo - Nasr City',
                'code': 'ELMIK-CAI-01',
                'company': companies.filter(code=f'ELMIK-CAI-{tenant_short}').first(),
                'address': 'Nasr City, Cairo',
                'capacity': 50
            },
            {
                'name': 'El Mikaneeky Cairo - Heliopolis',
                'code': 'ELMIK-CAI-02', 
                'company': companies.filter(code=f'ELMIK-CAI-{tenant_short}').first(),
                'address': 'Heliopolis, Cairo',
                'capacity': 35
            },
            {
                'name': 'El Mikaneeky Alexandria - Smouha',
                'code': 'ELMIK-ALX-01',
                'company': companies.filter(code=f'ELMIK-ALX-{tenant_short}').first(),
                'address': 'Smouha, Alexandria',
                'capacity': 30
            },
            {
                'name': 'El Mikaneeky Sheikh Zayed - Main Branch',
                'code': 'ELMIK-SZ-01',
                'company': companies.filter(code=f'ELMIK-SZ-{tenant_short}').first(),
                'address': '6th of October, Sheikh Zayed',
                'capacity': 45
            },
            {
                'name': 'El Mikaneeky New Cairo - 5th Settlement',
                'code': 'ELMIK-NC-01',
                'company': companies.filter(code=f'ELMIK-NC-{tenant_short}').first(),
                'address': '5th Settlement, New Cairo',
                'capacity': 40
            }
        ]
        
        for center_data in service_centers_data:
            company = center_data['company']
            if company:  # Only create if company exists
                ServiceCenter.objects.get_or_create(
                    code=center_data['code'],
                    tenant_id=self.tenant_id,
                    defaults={
                        'name': center_data['name'],
                        'company': company,
                        'center_type': default_center_type,
                        'address': center_data['address'],
                        'capacity': center_data['capacity'],
                        'phone': company.phone if company else '',
                        'email': f"{center_data['code'].lower()}@elmikaneeky.com",
                        'is_active': True,
                        'service_level': ServiceLevel.objects.first()  # ServiceLevel doesn't use tenant_id
                    }
                )
            else:
                self.stdout.write(f"Skipping service center {center_data['name']} - no company found")

    def create_vehicle_makes_and_models(self):
        """Create popular vehicle makes and models in Egypt"""
        self.stdout.write('Creating vehicle makes and models...')
        
        # Popular car brands in Egypt
        makes_models = {
            'Toyota': ['Corolla', 'Camry', 'RAV4', 'Yaris', 'Avensis', 'Prius'],
            'Hyundai': ['Elantra', 'Accent', 'Tucson', 'i10', 'i20', 'Creta'],
            'Nissan': ['Sunny', 'Sentra', 'X-Trail', 'Micra', 'Altima'],
            'Chevrolet': ['Cruze', 'Aveo', 'Captiva', 'Lanos', 'Optra'],
            'Renault': ['Logan', 'Megane', 'Duster', 'Fluence', 'Symbol'],
            'Peugeot': ['301', '308', '2008', '3008', '508'],
            'Kia': ['Cerato', 'Rio', 'Sportage', 'Picanto', 'Sorento'],
            'Mitsubishi': ['Lancer', 'ASX', 'Outlander', 'Pajero'],
        }
        
        tenant_short = str(self.tenant_id)[:8]
        for make_name, models in makes_models.items():
            unique_make_name = f"{make_name}-{tenant_short}"
            make, created = VehicleMake.objects.get_or_create(
                name=unique_make_name,
                tenant_id=self.tenant_id,  # Include tenant_id in lookup for uniqueness
                defaults={
                    'description': f'{make_name} vehicles available in Egypt',
                    'is_active': True,
                }
            )
            
            for model_name in models:
                VehicleModel.objects.get_or_create(
                    make=make,
                    name=model_name,
                    tenant_id=self.tenant_id,
                    defaults={
                        'description': f'{make_name} {model_name}',
                        'year_introduced': random.randint(2010, 2020),
                        'vehicle_class': random.choice(['Sedan', 'Hatchback', 'SUV', 'Crossover']),
                        'is_active': True
                    }
                )

    def create_customers(self):
        """Create sample customers with Egyptian names and details"""
        self.stdout.write('Creating customers...')
        
        # Common Egyptian names
        first_names = ['Ahmed', 'Mohamed', 'Mahmoud', 'Ali', 'Omar', 'Amr', 'Khaled', 'Tamer', 'Hany', 'Sherif']
        last_names = ['Hassan', 'Mohamed', 'Ali', 'Ibrahim', 'Abdel Rahman', 'Farouk', 'Mansour', 'Saleh', 'Nasser', 'El Sayed']
        
        cities = ['Cairo', 'Alexandria', 'Giza', 'Shubra El Kheima', 'Port Said', 'Suez', 'Luxor', 'Mansoura', 'Tanta', 'Asyut']
        
        for i in range(50):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            city = random.choice(cities)
            
            Customer.objects.get_or_create(
                phone=f'+20-10-{random.randint(10000000, 99999999)}',
                tenant_id=self.tenant_id,
                defaults={
                    'first_name': first_name,
                    'last_name': last_name,
                    'email': f'{first_name.lower()}.{last_name.lower()}{i}@email.com',
                    'address': f'{random.randint(1, 99)} {random.choice(["El Nasr", "El Tahrir", "El Nile", "El Gomhoria"])} Street, {city}',
                    'city': city,
                    'country': 'Egypt',
                    'customer_type': random.choice(['individual', 'corporate']),
                    'gender': random.choice(['male', 'female']),
                    'date_of_birth': date(random.randint(1970, 2000), random.randint(1, 12), random.randint(1, 28)),
                    'id_type': 'national_id',
                    'id_number': str(random.randint(20000000000000, 29999999999999)),
                    'is_active': True
                }
            )

    def create_vehicles(self):
        """Create sample vehicles for customers"""
        self.stdout.write('Creating vehicles...')
        
        customers = Customer.objects.filter(tenant_id=self.tenant_id)[:30]  # Use first 30 customers
        makes = VehicleMake.objects.filter(tenant_id=self.tenant_id)
        service_centers = ServiceCenter.objects.filter(tenant_id=self.tenant_id)
        
        if not makes.exists():
            self.stdout.write("No vehicle makes found. Skipping vehicle creation.")
            return
            
        if not service_centers.exists():
            self.stdout.write("No service centers found. Skipping vehicle creation.")
            return
        
        colors = ['أبيض', 'أسود', 'فضي', 'أحمر', 'أزرق', 'رمادي', 'بني', 'أخضر']
        
        for customer in customers:
            make = random.choice(list(makes))  # Convert to list first
            models = VehicleModel.objects.filter(make=make, tenant_id=self.tenant_id)
            if models.exists():
                model = random.choice(list(models))  # Convert to list first
                
                # Extract original make name by removing tenant suffix
                original_make_name = make.name.split('-')[0] if '-' in make.name else make.name
                
                Vehicle.objects.get_or_create(
                    owner=customer,
                    license_plate=f"{random.choice(['أ', 'ب', 'ج', 'د'])}{random.randint(1000, 9999)}{random.choice(['ق', 'م', 'ج'])}",
                    tenant_id=self.tenant_id,
                    defaults={
                        'make': original_make_name,  # Use original make name without tenant suffix
                        'model': model.name,
                        'year': random.randint(2015, 2024),
                        'vin': f'EG{random.randint(10000000000000000, 99999999999999999)}',
                        'color': random.choice(colors),
                        'standard_make': make,
                        'standard_model': model,
                        'service_center': random.choice(list(service_centers)),
                        # Removed invalid fields: current_mileage, engine_type, transmission_type, fuel_type, is_active
                    }
                )

    def create_customer_classifications(self):
        """Create customer classification system"""
        self.stdout.write('Creating customer classifications...')
        
        classifications = [
            {
                'name': 'Regular Customer',
                'level': 1,
                'discount_percentage': Decimal('0.00'),
                'service_priority': 1,
                'color': '#gray'
            },
            {
                'name': 'Silver Customer', 
                'level': 2,
                'discount_percentage': Decimal('5.00'),
                'service_priority': 2,
                'color': '#silver'
            },
            {
                'name': 'Gold Customer',
                'level': 3, 
                'discount_percentage': Decimal('10.00'),
                'service_priority': 3,
                'color': '#gold'
            },
            {
                'name': 'Platinum Customer',
                'level': 4,
                'discount_percentage': Decimal('15.00'),
                'service_priority': 4,
                'color': '#platinum'
            }
        ]
        
        for class_data in classifications:
            CustomerClassification.objects.get_or_create(
                name=class_data['name'],
                tenant_id=self.tenant_id,
                defaults=class_data
            )

    def create_inventory_data(self):
        """Create inventory items, classifications, and related data"""
        self.stdout.write('Creating inventory data...')
        
        # Create units of measurement
        units = [
            {'name': 'قطعة', 'symbol': 'قطعة'},
            {'name': 'لتر', 'symbol': 'لتر'},
            {'name': 'كيلوجرام', 'symbol': 'كجم'},
            {'name': 'متر', 'symbol': 'م'},
            {'name': 'عبوة', 'symbol': 'عبوة'},
            {'name': 'صندوق', 'symbol': 'صندوق'}
        ]
        
        for unit_data in units:
            UnitOfMeasurement.objects.get_or_create(
                name=unit_data['name'],
                tenant_id=self.tenant_id,
                defaults=unit_data
            )
        
        # Create item classifications
        classifications = [
            {'name': 'قطع غيار المحرك', 'code': 'ENG', 'description': 'قطع غيار وصيانة المحرك'},
            {'name': 'قطع غيار الفرامل', 'code': 'BRK', 'description': 'نظام الفرامل والأمان'},
            {'name': 'زيوت ومواد التشحيم', 'code': 'OIL', 'description': 'زيوت المحرك وزيوت الفتيس'},
            {'name': 'فلاتر', 'code': 'FLT', 'description': 'فلاتر الهواء والزيت والوقود'},
            {'name': 'إطارات وعجل', 'code': 'TIR', 'description': 'إطارات وقطع غيار العجل'},
            {'name': 'بطاريات', 'code': 'BAT', 'description': 'بطاريات السيارات'},
            {'name': 'إكسسوارات', 'code': 'ACC', 'description': 'إكسسوارات داخلية وخارجية'}
        ]
        
        for class_data in classifications:
            ItemClassification.objects.get_or_create(
                code=class_data['code'],
                tenant_id=self.tenant_id,
                defaults=class_data
            )
        
        # Create inventory items with Egypt market pricing in EGP
        piece_unit = UnitOfMeasurement.objects.filter(name='قطعة', tenant_id=self.tenant_id).first()
        liter_unit = UnitOfMeasurement.objects.filter(name='لتر', tenant_id=self.tenant_id).first()
        
        # Engine parts
        engine_class = ItemClassification.objects.filter(code='ENG', tenant_id=self.tenant_id).first()
        engine_parts = [
            {'sku': 'ENG001', 'name': 'مكابس المحرك', 'price': 1500, 'min_stock': 10},
            {'sku': 'ENG002', 'name': 'حلقات المكابس', 'price': 800, 'min_stock': 20},
            {'sku': 'ENG003', 'name': 'صمامات المحرك', 'price': 450, 'min_stock': 15},
            {'sku': 'ENG004', 'name': 'غطاء المحرك', 'price': 2200, 'min_stock': 5},
            {'sku': 'ENG005', 'name': 'حساس الأكسجين', 'price': 950, 'min_stock': 12}
        ]
        
        for part in engine_parts:
            Item.objects.get_or_create(
                sku=part['sku'],
                tenant_id=self.tenant_id,
                defaults={
                    'name': part['name'],
                    'description': f"قطعة غيار أصلية - {part['name']}",
                    'quantity': random.randint(20, 100),
                    'unit_of_measurement': piece_unit,
                    'unit_price': Decimal(str(part['price'])),
                    'min_stock_level': part['min_stock'],
                    'category': 'part',
                    'classification': engine_class
                }
            )
        
        # Brake parts
        brake_class = ItemClassification.objects.filter(code='BRK', tenant_id=self.tenant_id).first()
        brake_parts = [
            {'sku': 'BRK001', 'name': 'تيل الفرامل الأمامية', 'price': 350, 'min_stock': 25},
            {'sku': 'BRK002', 'name': 'تيل الفرامل الخلفية', 'price': 280, 'min_stock': 20},
            {'sku': 'BRK003', 'name': 'ديسك الفرامل', 'price': 850, 'min_stock': 15},
            {'sku': 'BRK004', 'name': 'سائل الفرامل', 'price': 120, 'min_stock': 30},
        ]
        
        for part in brake_parts:
            Item.objects.get_or_create(
                sku=part['sku'],
                tenant_id=self.tenant_id,
                defaults={
                    'name': part['name'],
                    'description': f"قطعة فرامل عالية الجودة - {part['name']}",
                    'quantity': random.randint(20, 80),
                    'unit_of_measurement': piece_unit,
                    'unit_price': Decimal(str(part['price'])),
                    'min_stock_level': part['min_stock'],
                    'category': 'part',
                    'classification': brake_class
                }
            )
        
        # Oils and lubricants
        oil_class = ItemClassification.objects.filter(code='OIL', tenant_id=self.tenant_id).first()
        oils = [
            {'sku': 'OIL001', 'name': 'زيت محرك 5W-30', 'price': 180, 'min_stock': 50},
            {'sku': 'OIL002', 'name': 'زيت محرك 10W-40', 'price': 165, 'min_stock': 45},
            {'sku': 'OIL003', 'name': 'زيت فتيس أوتوماتيك', 'price': 220, 'min_stock': 30},
            {'sku': 'OIL004', 'name': 'زيت باور ستيرنج', 'price': 95, 'min_stock': 25},
        ]
        
        for oil in oils:
            Item.objects.get_or_create(
                sku=oil['sku'],
                tenant_id=self.tenant_id,
                defaults={
                    'name': oil['name'],
                    'description': f"زيت عالي الجودة - {oil['name']}",
                    'quantity': random.randint(30, 120),
                    'unit_of_measurement': liter_unit,
                    'unit_price': Decimal(str(oil['price'])),
                    'min_stock_level': oil['min_stock'],
                    'category': 'consumable',
                    'classification': oil_class
                }
            )

        # Create movement types
        movement_types = [
            {'code': 'PURCHASE', 'name': 'شراء', 'is_inbound': True, 'is_outbound': False},
            {'code': 'SALE', 'name': 'بيع', 'is_inbound': False, 'is_outbound': True},
            {'code': 'ADJUSTMENT', 'name': 'تعديل', 'is_inbound': True, 'is_outbound': True},
            {'code': 'RETURN', 'name': 'إرجاع', 'is_inbound': True, 'is_outbound': False},
        ]
        
        for mt_data in movement_types:
            MovementType.objects.get_or_create(
                code=mt_data['code'],
                tenant_id=self.tenant_id,
                defaults=mt_data
            )

    def create_work_order_data(self):
        """Create work order types, maintenance schedules, and sample work orders"""
        self.stdout.write('Creating work order data...')
        
        # Create work order types
        work_order_types = [
            {'name': 'صيانة دورية', 'description': 'صيانة دورية للمركبة', 'color_code': '#28a745'},
            {'name': 'إصلاح عطل', 'description': 'إصلاح عطل في المركبة', 'color_code': '#dc3545'},
            {'name': 'تغيير زيت', 'description': 'تغيير زيت المحرك', 'color_code': '#ffc107'},
            {'name': 'فحص شامل', 'description': 'فحص شامل للمركبة', 'color_code': '#17a2b8'},
        ]
        
        for wot_data in work_order_types:
            WorkOrderType.objects.get_or_create(
                name=wot_data['name'],
                tenant_id=self.tenant_id,
                defaults=wot_data
            )
        
        # Create maintenance schedules
        schedules = [
            {
                'name': 'صيانة 5000 كم',
                'description': 'صيانة كل 5000 كيلومتر',
                'mileage_interval': 5000,
                'time_interval_months': 3
            },
            {
                'name': 'صيانة 10000 كم',
                'description': 'صيانة كل 10000 كيلومتر',
                'mileage_interval': 10000,
                'time_interval_months': 6
            },
            {
                'name': 'صيانة 20000 كم',
                'description': 'صيانة شاملة كل 20000 كيلومتر',
                'mileage_interval': 20000,
                'time_interval_months': 12
            }
        ]
        
        for schedule_data in schedules:
            MaintenanceSchedule.objects.get_or_create(
                name=schedule_data['name'],
                tenant_id=self.tenant_id,
                defaults=schedule_data
            )
        
        # Create sample work orders
        vehicles = Vehicle.objects.filter(tenant_id=self.tenant_id)[:10]
        work_order_types_list = WorkOrderType.objects.filter(tenant_id=self.tenant_id)
        service_centers = ServiceCenter.objects.filter(tenant_id=self.tenant_id)
        
        for i, vehicle in enumerate(vehicles, 1):
            WorkOrder.objects.get_or_create(
                work_order_number=f'WO-{str(i).zfill(6)}',
                tenant_id=self.tenant_id,
                defaults={
                    'work_order_type': random.choice(work_order_types_list),
                    'description': f'صيانة {vehicle.make} {vehicle.model} - {vehicle.license_plate}',
                    'priority': random.choice(['low', 'medium', 'high']),
                    'status': random.choice(['planned', 'in_progress', 'completed']),
                    'service_center': random.choice(service_centers),
                    'vehicle': vehicle,
                    'customer': vehicle.owner,
                    'current_odometer': random.randint(50000, 150000),
                    'estimated_cost': Decimal(str(random.randint(500, 3000))),
                    'planned_start_date': timezone.now() + timedelta(days=random.randint(1, 30))
                }
            )

    def create_sales_data(self):
        """Create sales customers, orders and related data"""
        self.stdout.write('Creating sales data...')
        
        # Create sales customers (different from setup customers)
        for i in range(20):
            SalesCustomer.objects.get_or_create(
                name=f'عميل مبيعات {i+1}',
                tenant_id=self.tenant_id,
                defaults={
                    'email': f'customer{i+1}@sales.com',
                    'phone': f'+20-12-{random.randint(10000000, 99999999)}',
                    'address': f'عنوان عميل {i+1}، القاهرة، مصر',
                    'is_active': True
                }
            )
        
        # Create sales orders
        sales_customers = SalesCustomer.objects.filter(tenant_id=self.tenant_id)
        items = Item.objects.filter(tenant_id=self.tenant_id)
        
        import time
        timestamp = int(time.time())
        for i in range(15):
            customer = random.choice(list(sales_customers))
            order, created = SalesOrder.objects.get_or_create(
                order_number=f'SO-{timestamp}-{str(i+1).zfill(3)}',
                tenant_id=self.tenant_id,
                defaults={
                    'customer': customer,
                    'order_date': date.today() - timedelta(days=random.randint(0, 90)),
                    'status': random.choice(['draft', 'confirmed', 'shipped', 'delivered']),
                }
            )
            
            # Add items to sales order (only if order was newly created)
            if created:
                selected_items = random.sample(list(items), random.randint(2, 5))
                for item in selected_items:
                    quantity = random.randint(1, 10)
                    unit_price = item.unit_price * Decimal(str(random.uniform(0.9, 1.2)))  # Add some price variation
                    
                    SalesOrderItem.objects.get_or_create(
                        sales_order=order,
                        item=item,
                        tenant_id=self.tenant_id,
                        defaults={
                            'quantity': quantity,
                            'unit_price': unit_price,
                        }
                    )

    def create_purchase_data(self):
        """Create suppliers, purchase orders and related data"""
        self.stdout.write('Creating purchase data...')
        
        import time
        timestamp = int(time.time())
        
        # Create suppliers
        suppliers_data = [
            {'name': 'شركة قطع غيار مصر', 'email': '<EMAIL>', 'phone': '+20-2-26751234'},
            {'name': 'مؤسسة الإخوان للسيارات', 'email': '<EMAIL>', 'phone': '+20-2-38851234'},
            {'name': 'شركة النصر للزيوت والمواد البترولية', 'email': '<EMAIL>', 'phone': '+20-2-42281234'},
            {'name': 'مصنع الدلتا للفلاتر والقطع', 'email': '<EMAIL>', 'phone': '+20-2-25981234'},
            {'name': 'شركة بوش مصر للقطع الأصلية', 'email': '<EMAIL>', 'phone': '+20-2-33751234'},
            {'name': 'الشركة المصرية للإطارات', 'email': '<EMAIL>', 'phone': '+20-3-42851234'},
        ]
        
        for supplier_data in suppliers_data:
            Supplier.objects.get_or_create(
                name=supplier_data['name'],
                tenant_id=self.tenant_id,
                defaults={
                    **supplier_data,
                    'address': f'القاهرة، مصر',
                    'is_active': True
                }
            )
        
        # Create purchase orders
        suppliers = Supplier.objects.filter(tenant_id=self.tenant_id)
        items = Item.objects.filter(tenant_id=self.tenant_id)
        
        for i in range(12):
            supplier = random.choice(list(suppliers))
            order, created = PurchaseOrder.objects.get_or_create(
                order_number=f'PO-{timestamp}-{str(i+1).zfill(3)}',
                tenant_id=self.tenant_id,
                defaults={
                    'supplier': supplier,
                    'order_date': date.today() - timedelta(days=random.randint(0, 60)),
                    'expected_delivery_date': date.today() + timedelta(days=random.randint(5, 20)),
                    'status': random.choice(['draft', 'sent', 'confirmed', 'received']),
                }
            )
            
            # Add items to purchase order (only if order was newly created)
            if created:
                selected_items = random.sample(list(items), random.randint(3, 8))
                for item in selected_items:
                    quantity = random.randint(10, 50)
                    unit_price = item.unit_price * Decimal(str(random.uniform(0.7, 0.9)))  # Wholesale pricing
                    
                    PurchaseOrderItem.objects.get_or_create(
                        purchase_order=order,
                        item=item,
                        tenant_id=self.tenant_id,
                        defaults={
                            'quantity': quantity,
                            'unit_price': unit_price,
                        }
                    )

    def create_billing_data(self):
        """Create billing related data like customer preferences, warranties, etc."""
        self.stdout.write('Creating billing data...')
        
        # Create warranty types
        warranty_types = [
            {
                'name': 'ضمان قطع الغيار',
                'description': 'ضمان على قطع الغيار المستبدلة',
                'default_duration_months': 12,
                'parts_covered': 'جميع قطع الغيار',
                'labor_covered': False
            },
            {
                'name': 'ضمان العمالة',
                'description': 'ضمان على العمالة والتركيب',
                'default_duration_months': 6,
                'parts_covered': '',
                'labor_covered': True
            },
            {
                'name': 'ضمان شامل',
                'description': 'ضمان شامل على القطع والعمالة',
                'default_duration_months': 12,
                'parts_covered': 'جميع قطع الغيار',
                'labor_covered': True
            }
        ]
        
        for wt_data in warranty_types:
            WarrantyType.objects.get_or_create(
                name=wt_data['name'],
                tenant_id=self.tenant_id,
                defaults=wt_data
            )
        
        # Create customer preferences for some customers
        customers = Customer.objects.filter(tenant_id=self.tenant_id)[:20]
        classifications = CustomerClassification.objects.filter(tenant_id=self.tenant_id)
        
        for customer in customers:
            classification = random.choice(list(classifications))
            customer.classification = classification
            customer.save()
            
            CustomerPreference.objects.get_or_create(
                customer=customer,
                tenant_id=self.tenant_id,
                defaults={
                    'status': random.choice(['regular', 'vip', 'gold']),
                    'payment_terms': random.choice(['cash', 'credit_30']),
                    'credit_limit': Decimal(str(random.randint(5000, 50000))),
                    'default_discount_percentage': classification.discount_percentage,
                    # Removed invalid fields: preferred_service_center, communication_preferences
                }
            )

    def create_reports_and_dashboards(self):
        """Create sample reports and dashboards"""
        self.stdout.write('Creating reports and dashboards...')
        
        # Create reports
        reports_data = [
            {
                'name': 'تقرير المبيعات الشهرية',
                'description': 'تقرير بإجمالي المبيعات والأرباح الشهرية',
                'report_type': 'sales',
                'parameters': {'period': 'monthly', 'currency': 'EGP'}
            },
            {
                'name': 'تقرير المخزون',
                'description': 'تقرير بحالة المخزون والأصناف المنخفضة',
                'report_type': 'inventory',
                'parameters': {'show_low_stock': True, 'currency': 'EGP'}
            },
            {
                'name': 'تقرير أوامر العمل',
                'description': 'تقرير بحالة أوامر العمل والصيانة',
                'report_type': 'custom',
                'parameters': {'status': 'all', 'period': 'weekly'}
            }
        ]
        
        for report_data in reports_data:
            Report.objects.get_or_create(
                name=report_data['name'],
                tenant_id=self.tenant_id,
                defaults=report_data
            )
        
        # Create dashboard
        Dashboard.objects.get_or_create(
            name='لوحة التحكم الرئيسية',
            tenant_id=self.tenant_id,
            defaults={
                'description': 'لوحة التحكم الرئيسية لمتابعة الأعمال',
                'layout': {
                    'widgets': [
                        {'type': 'sales_summary', 'position': {'x': 0, 'y': 0}},
                        {'type': 'inventory_alerts', 'position': {'x': 1, 'y': 0}},
                        {'type': 'work_orders', 'position': {'x': 0, 'y': 1}},
                        {'type': 'customer_stats', 'position': {'x': 1, 'y': 1}}
                    ]
                },
                'is_default': True
            }
        )
        
        self.stdout.write(self.style.SUCCESS('✓ All demo data created successfully!'))
        self.stdout.write(self.style.SUCCESS('Data includes:'))
        self.stdout.write(f'  - Service levels and franchise structure')
        self.stdout.write(f'  - {Customer.objects.filter(tenant_id=self.tenant_id).count()} customers')
        self.stdout.write(f'  - {Vehicle.objects.filter(tenant_id=self.tenant_id).count()} vehicles')
        self.stdout.write(f'  - {Item.objects.filter(tenant_id=self.tenant_id).count()} inventory items')
        self.stdout.write(f'  - {WorkOrder.objects.filter(tenant_id=self.tenant_id).count()} work orders')
        self.stdout.write(f'  - {SalesOrder.objects.filter(tenant_id=self.tenant_id).count()} sales orders')
        self.stdout.write(f'  - {PurchaseOrder.objects.filter(tenant_id=self.tenant_id).count()} purchase orders')
        self.stdout.write(self.style.SUCCESS('All prices are in EGP (Egyptian Pounds)')) 