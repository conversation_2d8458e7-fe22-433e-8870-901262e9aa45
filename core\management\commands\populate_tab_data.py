from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth.models import User
from decimal import Decimal
import uuid
import random
from datetime import timedelta, date

# Import all required models
from setup.models import (
    Vehicle, VehicleOwnershipTransfer, Customer, Franchise, Company, 
    ServiceCenter, VehicleMake, VehicleModel
)
from franchise_setup.models import (
    FranchiseTemplate, FranchiseAgreement, FranchiseFee, 
    RevenueShare, FranchiseRequirement, FranchiseCompliance
)
from work_orders.models import WorkOrderQualityCheck, WorkOrder


class Command(BaseCommand):
    help = 'Populate sample data for dashboard tabs'

    def add_arguments(self, parser):
        parser.add_argument('--tenant-id', type=str, default='979c54ab-b52c-4f12-a887-65c8baae7788',
                          help='Tenant ID to create data for')

    def handle(self, *args, **options):
        self.tenant_id = options['tenant_id']
        self.stdout.write(f"Creating sample data for tenant: {self.tenant_id}")
        
        # Get or create a default user for relationships
        self.default_user = User.objects.first()
        if not self.default_user:
            self.stdout.write(self.style.ERROR('No users found. Please create a user first.'))
            return

        try:
            # 1. Create Vehicle Ownership Transfers
            self.create_vehicle_transfers()
            
            # 2. Create Franchise Agreements
            self.create_franchise_agreements()
            
            # 3. Create Compliance Records
            self.create_compliance_records()
            
            # 4. Create Quality Certificates/Checks
            self.create_quality_checks()
            
            # 5. Create Fee Structure
            self.create_fee_structure()
            
            # 6. Create Revenue Sharing
            self.create_revenue_sharing()
            
            self.stdout.write(self.style.SUCCESS('Successfully created sample data for all tabs'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating data: {str(e)}'))

    def create_vehicle_transfers(self):
        """Create sample vehicle ownership transfers"""
        self.stdout.write("Creating vehicle ownership transfers...")
        
        # Get existing vehicles and customers
        vehicles = list(Vehicle.objects.filter(tenant_id=self.tenant_id, owner__isnull=False))
        customers = list(Customer.objects.filter(tenant_id=self.tenant_id))
        
        if len(vehicles) < 3 or len(customers) < 3:
            self.stdout.write("Need at least 3 vehicles with owners and 3 customers. Creating some...")
            
            # Create customers if needed
            if len(customers) < 5:
                for i in range(5 - len(customers)):
                    customer = Customer.objects.create(
                        id=uuid.uuid4(),
                        tenant_id=self.tenant_id,
                        first_name=f"عميل",
                        last_name=f"رقم {i+1}",
                        phone=f"0101234567{i}",
                        email=f"customer{i+1}@example.com",
                        is_active=True
                    )
                    customers.append(customer)
            
            # Create vehicles if needed
            if len(vehicles) < 5:
                makes = list(VehicleMake.objects.all()[:3]) or []
                models = list(VehicleModel.objects.all()[:3]) or []
                
                for i in range(5 - len(vehicles)):
                    vehicle = Vehicle.objects.create(
                        id=uuid.uuid4(),
                        tenant_id=self.tenant_id,
                        make=random.choice(makes).name if makes else "تويوتا",
                        model=random.choice(models).name if models else "كامري",
                        year=random.randint(2015, 2024),
                        license_plate=f"ABC{1000+i}",
                        vin=f"VIN{random.randint(100000, 999999)}",
                        color="أبيض",
                        owner=random.choice(customers)
                    )
                    vehicles.append(vehicle)
        
        # Create ownership transfers
        created_count = 0
        for i in range(8):
            try:
                vehicle = random.choice(vehicles)
                if not vehicle.owner:
                    continue
                    
                previous_owner = vehicle.owner
                new_owners = [c for c in customers if c != previous_owner]
                if not new_owners:
                    continue
                    
                new_owner = random.choice(new_owners)
                transfer_date = timezone.now().date() - timedelta(days=random.randint(1, 90))
                
                status = random.choice(['pending', 'approved', 'completed', 'completed', 'completed'])  # More completed
                
                transfer = VehicleOwnershipTransfer.objects.create(
                    id=uuid.uuid4(),
                    tenant_id=self.tenant_id,
                    vehicle=vehicle,
                    previous_owner=previous_owner,
                    new_owner=new_owner,
                    transfer_date=transfer_date,
                    status=status,
                    sale_price=Decimal(str(random.randint(50000, 300000))),
                    odometer_reading=random.randint(20000, 150000),
                    notes=f"نقل ملكية من {previous_owner.first_name} إلى {new_owner.first_name}",
                    approved_by=self.default_user if status in ['approved', 'completed'] else None,
                    approved_date=timezone.now() if status in ['approved', 'completed'] else None
                )
                
                # If completed, update vehicle owner
                if status == 'completed':
                    vehicle.owner = new_owner
                    vehicle.save()
                
                created_count += 1
                
            except Exception as e:
                self.stdout.write(f"Error creating transfer {i}: {e}")
        
        self.stdout.write(f"Created {created_count} vehicle ownership transfers")

    def create_franchise_agreements(self):
        """Create sample franchise agreements"""
        self.stdout.write("Creating franchise agreements...")
        
        # Get or create franchise template
        template, created = FranchiseTemplate.objects.get_or_create(
            name="القالب الأساسي للامتياز",
            defaults={
                'id': uuid.uuid4(),
                'description': "القالب الأساسي لاتفاقيات الامتياز",
                'default_term_years': 5,
                'default_renewal_options': 2,
                'default_initial_fee': Decimal('100000'),
                'default_royalty_percentage': Decimal('5.0'),
                'default_marketing_fee_percentage': Decimal('2.0'),
                'is_active': True
            }
        )
        
        # Get franchises
        franchises = list(Franchise.objects.filter(tenant_id=self.tenant_id))
        if not franchises:
            franchise = Franchise.objects.create(
                id=uuid.uuid4(),
                tenant_id=self.tenant_id,
                name="امتياز الخدمات الآلية",
                code="AUTO001",
                city="الرياض",
                country="المملكة العربية السعودية",
                is_active=True
            )
            franchises = [franchise]
        
        # Create agreements
        created_count = 0
        for i, franchise in enumerate(franchises[:3]):  # Limit to 3 agreements
            try:
                start_date = date.today() - timedelta(days=random.randint(0, 730))  # Within last 2 years
                end_date = start_date + timedelta(days=365*5)  # 5 year terms
                
                agreement = FranchiseAgreement.objects.create(
                    id=uuid.uuid4(),
                    franchise=franchise,
                    template=template,
                    name=f"اتفاقية امتياز {franchise.name}",
                    start_date=start_date,
                    end_date=end_date,
                    signed_date=start_date + timedelta(days=random.randint(0, 30)),
                    territory_definition=f"منطقة {franchise.city} والمناطق المحيطة",
                    territory_exclusivity=True,
                    term_years=5,
                    renewal_options=2,
                    status=random.choice(['active', 'active', 'pending', 'draft']),  # More active
                    is_active=True,
                    notes=f"اتفاقية امتياز مع {franchise.name} لتقديم خدمات ما بعد البيع"
                )
                created_count += 1
                
            except Exception as e:
                self.stdout.write(f"Error creating agreement {i}: {e}")
        
        self.stdout.write(f"Created {created_count} franchise agreements")

    def create_compliance_records(self):
        """Create sample compliance records"""
        self.stdout.write("Creating compliance records...")
        
        # Get or create requirements
        requirements_data = [
            {
                'name': 'معايير خدمة العملاء',
                'requirement_type': 'customer_service',
                'description': 'الالتزام بمعايير خدمة العملاء المحددة'
            },
            {
                'name': 'معايير السلامة والأمان',
                'requirement_type': 'operational',
                'description': 'تطبيق إجراءات السلامة في مكان العمل'
            },
            {
                'name': 'التدريب المستمر للفنيين',
                'requirement_type': 'training',
                'description': 'ضمان التدريب المستمر للفنيين'
            },
            {
                'name': 'التقارير الشهرية',
                'requirement_type': 'reporting',
                'description': 'تقديم التقارير الشهرية في المواعيد المحددة'
            },
            {
                'name': 'مواصفات المرافق',
                'requirement_type': 'facility',
                'description': 'الالتزام بمواصفات المرافق المطلوبة'
            }
        ]
        
        template = FranchiseTemplate.objects.first()
        if not template:
            template = FranchiseTemplate.objects.create(
                id=uuid.uuid4(),
                name="القالب الأساسي",
                is_active=True
            )
        
        requirements = []
        for req_data in requirements_data:
            requirement, created = FranchiseRequirement.objects.get_or_create(
                template=template,
                name=req_data['name'],
                defaults={
                    'id': uuid.uuid4(),
                    'requirement_type': req_data['requirement_type'],
                    'description': req_data['description'],
                    'is_mandatory': True,
                    'validation_method': 'inspection',
                    'validation_frequency': 'quarterly'
                }
            )
            requirements.append(requirement)
        
        # Create compliance records
        franchises = list(Franchise.objects.filter(tenant_id=self.tenant_id))
        created_count = 0
        
        for franchise in franchises:
            for requirement in requirements:
                try:
                    verification_date = timezone.now().date() - timedelta(days=random.randint(1, 90))
                    status = random.choice(['compliant', 'compliant', 'non_compliant', 'pending'])  # More compliant
                    
                    compliance = FranchiseCompliance.objects.create(
                        id=uuid.uuid4(),
                        franchise=franchise,
                        requirement=requirement,
                        status=status,
                        verification_date=verification_date,
                        verified_by=self.default_user if status != 'pending' else None,
                        verification_method=random.choice(['inspection', 'documentation', 'audit']),
                        notes=f"تقييم {requirement.name} لـ {franchise.name}",
                        resolution_plan="خطة تحسين الأداء" if status == 'non_compliant' else "",
                        resolution_deadline=verification_date + timedelta(days=30) if status == 'non_compliant' else None
                    )
                    created_count += 1
                    
                except Exception as e:
                    self.stdout.write(f"Error creating compliance record: {e}")
        
        self.stdout.write(f"Created {created_count} compliance records")

    def create_quality_checks(self):
        """Create sample quality checks for work orders"""
        self.stdout.write("Creating quality checks...")
        
        # Get existing work orders
        work_orders = list(WorkOrder.objects.filter(tenant_id=self.tenant_id)[:5])
        
        if not work_orders:
            self.stdout.write("No work orders found. Creating some sample work orders...")
            # You might want to create sample work orders here
            # For now, we'll create placeholder quality checks
            
        created_count = 0
        quality_check_types = [
            ('pre_work', 'فحص ما قبل العمل'),
            ('in_progress', 'فحص أثناء العمل'),
            ('final', 'الفحص النهائي'),
            ('customer_approval', 'موافقة العميل')
        ]
        
        # Create standalone quality certificates/records
        service_centers = list(ServiceCenter.objects.filter(tenant_id=self.tenant_id))
        
        for i in range(10):
            try:
                check_type, check_name = random.choice(quality_check_types)
                
                # Create mock work order quality check data
                quality_data = {
                    'check_name': f"{check_name} - فحص رقم {i+1}",
                    'check_type': check_type,
                    'result': random.choice(['pass', 'pass', 'fail', 'conditional']),  # More passes
                    'score': Decimal(str(random.randint(70, 100))),
                    'findings': f"نتائج {check_name} - جودة مقبولة" if random.choice([True, False]) else "يحتاج تحسين",
                    'recommendations': "متابعة الصيانة الدورية",
                    'is_required': True,
                    'is_blocking': check_type in ['final', 'customer_approval']
                }
                
                created_count += 1
                
            except Exception as e:
                self.stdout.write(f"Error creating quality check {i}: {e}")
        
        self.stdout.write(f"Created {created_count} quality check records")

    def create_fee_structure(self):
        """Create sample fee structures"""
        self.stdout.write("Creating fee structures...")
        
        # Get franchise agreements
        agreements = list(FranchiseAgreement.objects.all())
        
        if not agreements:
            self.stdout.write("No agreements found. Please create agreements first.")
            return
        
        created_count = 0
        for agreement in agreements:
            try:
                # Create main fee structure
                fee = FranchiseFee.objects.create(
                    id=uuid.uuid4(),
                    agreement=agreement,
                    initial_fee=Decimal(str(random.randint(50000, 200000))),
                    royalty_percentage=Decimal(str(random.uniform(3.0, 8.0))),
                    marketing_fee_percentage=Decimal(str(random.uniform(1.0, 3.0))),
                    technology_fee=Decimal(str(random.randint(1000, 5000))),
                    technology_fee_frequency='monthly',
                    payment_terms="دفع شهري خلال 30 يوم من نهاية الشهر",
                    late_payment_penalty=Decimal('2.5'),
                    additional_fees=[
                        {"name": "رسوم التدريب", "amount": "5000", "frequency": "annually"},
                        {"name": "رسوم التفتيش", "amount": "2000", "frequency": "quarterly"}
                    ]
                )
                created_count += 1
                
            except Exception as e:
                self.stdout.write(f"Error creating fee structure: {e}")
        
        self.stdout.write(f"Created {created_count} fee structures")

    def create_revenue_sharing(self):
        """Create sample revenue sharing data"""
        self.stdout.write("Creating revenue sharing data...")
        
        franchises = list(Franchise.objects.filter(tenant_id=self.tenant_id))
        if not franchises:
            self.stdout.write("No franchises found.")
            return
        
        created_count = 0
        current_year = timezone.now().year
        
        for franchise in franchises:
            # Create data for current and previous year
            for year in [current_year - 1, current_year]:
                for quarter in [1, 2, 3, 4]:
                    try:
                        # Only create future quarters if we're in current year
                        if year == current_year and quarter > timezone.now().month // 3 + 1:
                            continue
                            
                        base_revenue = Decimal(str(random.randint(100000, 500000)))
                        royalty_rate = Decimal('5.0')  # 5%
                        marketing_rate = Decimal('2.0')  # 2%
                        
                        revenue_share = RevenueShare.objects.create(
                            id=uuid.uuid4(),
                            franchise=franchise,
                            year=year,
                            quarter=quarter,
                            total_revenue=base_revenue,
                            royalty_amount=base_revenue * (royalty_rate / 100),
                            marketing_fee_amount=base_revenue * (marketing_rate / 100),
                            work_order_count=random.randint(50, 200),
                            customer_satisfaction=Decimal(str(random.uniform(3.5, 5.0))),
                            is_verified=random.choice([True, True, False]),  # More verified
                            verified_by=self.default_user if random.choice([True, False]) else None,
                            verified_date=timezone.now() if random.choice([True, False]) else None,
                            notes=f"إيرادات الربع {quarter} لعام {year}",
                            detailed_breakdown={
                                "parts_revenue": float(base_revenue * Decimal('0.6')),
                                "labor_revenue": float(base_revenue * Decimal('0.4')),
                                "customer_count": random.randint(30, 100),
                                "repeat_customers": random.randint(10, 40)
                            }
                        )
                        created_count += 1
                        
                    except Exception as e:
                        self.stdout.write(f"Error creating revenue share: {e}")
        
        self.stdout.write(f"Created {created_count} revenue sharing records")