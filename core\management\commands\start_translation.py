import os
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.conf import settings


class Command(BaseCommand):
    help = 'Prepares or updates translation files for Arabic'

    def add_arguments(self, parser):
        parser.add_argument(
            '--compile',
            action='store_true',
            help='Compile the messages after extraction',
        )

    def handle(self, *args, **options):
        self.stdout.write('Starting translation preparation for Arabic (ar)...')
        
        # Create locale directories if they don't exist
        locale_path = os.path.join(settings.BASE_DIR, 'locale')
        if not os.path.exists(locale_path):
            os.makedirs(locale_path)
            self.stdout.write(self.style.SUCCESS(f'Created locale directory at {locale_path}'))
        
        ar_path = os.path.join(locale_path, 'ar', 'LC_MESSAGES')
        if not os.path.exists(ar_path):
            os.makedirs(ar_path)
            self.stdout.write(self.style.SUCCESS(f'Created Arabic locale directory at {ar_path}'))
        
        # Extract messages for all installed apps
        try:
            self.stdout.write('Extracting translatable strings...')
            call_command('makemessages', locale=['ar'], ignore_patterns=['env/*', 'venv/*'])
            self.stdout.write(self.style.SUCCESS('Successfully extracted messages for Arabic'))
        except Exception as e:
            raise CommandError(f'Error extracting messages: {e}')
        
        # Compile messages if requested
        if options['compile']:
            try:
                self.stdout.write('Compiling message files...')
                call_command('compilemessages', locale=['ar'])
                self.stdout.write(self.style.SUCCESS('Successfully compiled messages'))
            except Exception as e:
                raise CommandError(f'Error compiling messages: {e}')
        
        self.stdout.write(self.style.SUCCESS(
            'Translation preparation complete! Edit the .po files in the locale/ar/LC_MESSAGES directory to add translations.'
        )) 