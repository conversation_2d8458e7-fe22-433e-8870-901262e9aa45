#!/usr/bin/env python
"""
Comprehensive validation script for Phase 1 and Phase 2 implementations
"""

import os
import sys
import django
import traceback
import uuid
from decimal import Decimal
from datetime import date, timedelta
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError


class Command(BaseCommand):
    help = 'Validate Phase 1 and Phase 2 implementations'

    def __init__(self):
        super().__init__()
        self.tenant_id = "979c54ab-b52c-4f12-a887-65c8baae7788"  # From env-sample
        self.validation_errors = []
        self.validation_warnings = []
        self.phase1_results = {}
        self.phase2_results = {}

    def add_arguments(self, parser):
        parser.add_argument(
            '--phase',
            type=str,
            choices=['1', '2', 'all'],
            default='all',
            help='Which phase to validate (1, 2, or all)'
        )
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix issues automatically'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 Starting Phase Validation...'))
        
        phase = options.get('phase', 'all')
        should_fix = options.get('fix', False)
        
        try:
            if phase in ['1', 'all']:
                self.validate_phase1()
            
            if phase in ['2', 'all']:
                self.validate_phase2()
                
            self.display_summary()
            
            if should_fix:
                self.attempt_fixes()
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Critical error during validation: {e}'))
            traceback.print_exc()

    def validate_phase1(self):
        """Validate Phase 1: Notification fixes and translation issues"""
        self.stdout.write(self.style.WARNING('\n📋 PHASE 1 VALIDATION'))
        self.stdout.write('=' * 50)
        
        # Test 1: Translation imports
        self.test_translation_imports()
        
        # Test 2: Email templates
        self.test_email_templates()
        
        # Test 3: Notification service
        self.test_notification_service()
        
        # Test 4: Work order signals
        self.test_work_order_signals()
        
        # Test 5: Work order creation flow
        self.test_work_order_creation()

    def test_translation_imports(self):
        """Test if translation imports are working correctly"""
        try:
            self.stdout.write('🔹 Testing translation imports...')
            
            # Test work_orders.signals
            from work_orders.signals import _notify_work_order_created, _notify_work_order_status_change
            from django.utils.translation import gettext_lazy as _
            
            # Test that underscore function works
            test_message = _("Test message")
            
            self.phase1_results['translation_imports'] = 'PASS'
            self.stdout.write(self.style.SUCCESS('  ✅ Translation imports working'))
            
        except Exception as e:
            self.phase1_results['translation_imports'] = f'FAIL: {e}'
            self.validation_errors.append(f'Translation imports failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Translation imports failed: {e}'))

    def test_email_templates(self):
        """Test if email templates exist and are accessible"""
        try:
            self.stdout.write('🔹 Testing email templates...')
            
            import os
            from django.conf import settings
            
            template_dir = os.path.join(settings.BASE_DIR, 'notifications', 'templates', 'notifications', 'emails')
            required_templates = [
                'work_order_created.html',
                'work_order_status_change.html', 
                'parts_request.html'
            ]
            
            missing_templates = []
            for template in required_templates:
                template_path = os.path.join(template_dir, template)
                if not os.path.exists(template_path):
                    missing_templates.append(template)
            
            if missing_templates:
                self.phase1_results['email_templates'] = f'FAIL: Missing {missing_templates}'
                self.validation_errors.append(f'Missing email templates: {missing_templates}')
                self.stdout.write(self.style.ERROR(f'  ❌ Missing templates: {missing_templates}'))
            else:
                self.phase1_results['email_templates'] = 'PASS'
                self.stdout.write(self.style.SUCCESS('  ✅ All email templates exist'))
                
        except Exception as e:
            self.phase1_results['email_templates'] = f'FAIL: {e}'
            self.validation_errors.append(f'Email template test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Email template test failed: {e}'))

    def test_notification_service(self):
        """Test notification service functionality"""
        try:
            self.stdout.write('🔹 Testing notification service...')
            
            from notifications.services import NotificationService
            from notifications.models import NotificationType
            
            # Test creating a notification type
            notification_type, created = NotificationType.objects.get_or_create(
                code='test_notification',
                defaults={
                    'name': 'Test Notification',
                    'type': 'test',
                    'icon': 'fa-test',
                    'color': 'blue'
                }
            )
            
            # Test creating a user for notification
            test_user, created = User.objects.get_or_create(
                username='test_notification_user',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'User'
                }
            )
            
            # Test creating a notification
            notification = NotificationService.create_notification(
                recipient=test_user,
                notification_type_code='test_notification',
                title='Test Notification',
                message='This is a test notification',
                tenant_id=self.tenant_id,
                send_email=False  # Don't actually send email
            )
            
            if notification:
                self.phase1_results['notification_service'] = 'PASS'
                self.stdout.write(self.style.SUCCESS('  ✅ Notification service working'))
            else:
                self.phase1_results['notification_service'] = 'FAIL: No notification created'
                self.validation_errors.append('Notification service failed to create notification')
                self.stdout.write(self.style.ERROR('  ❌ Notification service failed'))
                
        except Exception as e:
            self.phase1_results['notification_service'] = f'FAIL: {e}'
            self.validation_errors.append(f'Notification service test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Notification service failed: {e}'))

    def test_work_order_signals(self):
        """Test work order signals functionality"""
        try:
            self.stdout.write('🔹 Testing work order signals...')
            
            # Import required models
            from work_orders.models import WorkOrder, WorkOrderType
            from setup.models import Customer, Vehicle, ServiceCenter, Company, Franchise
            
            # Test signal functions directly
            from work_orders.signals import _notify_work_order_created, _notify_work_order_status_change
            
            # Create minimal test data
            test_customer = Customer.objects.filter(tenant_id=self.tenant_id).first()
            test_vehicle = Vehicle.objects.filter(tenant_id=self.tenant_id).first()
            test_service_center = ServiceCenter.objects.filter(tenant_id=self.tenant_id).first()
            test_work_order_type = WorkOrderType.objects.filter(tenant_id=self.tenant_id).first()
            
            if not all([test_customer, test_vehicle, test_service_center, test_work_order_type]):
                self.phase1_results['work_order_signals'] = 'SKIP: Missing test data (customer, vehicle, service center, or work order type)'
                self.validation_warnings.append('Cannot test work order signals - missing basic data')
                self.stdout.write(self.style.WARNING('  ⚠️ Skipping work order signals test - missing test data'))
                return
            
            # Create a test work order
            test_work_order = WorkOrder.objects.create(
                tenant_id=self.tenant_id,
                work_order_number=f'TEST-SIGNAL-{uuid.uuid4().hex[:8].upper()}',
                customer=test_customer,
                vehicle=test_vehicle,
                service_center=test_service_center,
                work_order_type=test_work_order_type,
                description='Test work order for signal validation',
                priority='medium',
                status='planned'
            )
            
            # Test signal functions
            _notify_work_order_created(test_work_order)
            _notify_work_order_status_change(test_work_order, 'planned', 'in_progress')
            
            # Clean up test work order
            test_work_order.delete()
            
            self.phase1_results['work_order_signals'] = 'PASS'
            self.stdout.write(self.style.SUCCESS('  ✅ Work order signals working'))
            
        except Exception as e:
            self.phase1_results['work_order_signals'] = f'FAIL: {e}'
            self.validation_errors.append(f'Work order signals test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Work order signals failed: {e}'))

    def test_work_order_creation(self):
        """Test work order creation flow"""
        try:
            self.stdout.write('🔹 Testing work order creation flow...')
            
            from work_orders.models import WorkOrder, WorkOrderType
            from setup.models import Customer, Vehicle, ServiceCenter
            
            # Get test data
            test_customer = Customer.objects.filter(tenant_id=self.tenant_id).first()
            test_vehicle = Vehicle.objects.filter(tenant_id=self.tenant_id).first()
            test_service_center = ServiceCenter.objects.filter(tenant_id=self.tenant_id).first()
            test_work_order_type = WorkOrderType.objects.filter(tenant_id=self.tenant_id).first()
            
            if not all([test_customer, test_vehicle, test_service_center, test_work_order_type]):
                self.phase1_results['work_order_creation'] = 'SKIP: Missing test data'
                self.validation_warnings.append('Cannot test work order creation - missing test data')
                self.stdout.write(self.style.WARNING('  ⚠️ Skipping work order creation test - missing test data'))
                return
            
            # Test creating work order
            work_order = WorkOrder.objects.create(
                tenant_id=self.tenant_id,
                work_order_number=f'TEST-CREATE-{uuid.uuid4().hex[:8].upper()}',
                customer=test_customer,
                vehicle=test_vehicle,
                service_center=test_service_center,
                work_order_type=test_work_order_type,
                description='Test work order creation',
                priority='medium',
                status='planned'
            )
            
            # Test status transition
            work_order.status = 'in_progress'
            work_order.save()
            
            # Clean up
            work_order.delete()
            
            self.phase1_results['work_order_creation'] = 'PASS'
            self.stdout.write(self.style.SUCCESS('  ✅ Work order creation flow working'))
            
        except Exception as e:
            self.phase1_results['work_order_creation'] = f'FAIL: {e}'
            self.validation_errors.append(f'Work order creation test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Work order creation failed: {e}'))

    def validate_phase2(self):
        """Validate Phase 2: Pricing models and services"""
        self.stdout.write(self.style.WARNING('\n📋 PHASE 2 VALIDATION'))
        self.stdout.write('=' * 50)
        
        # Test 1: Pricing models
        self.test_pricing_models()
        
        # Test 2: Dynamic pricing engine
        self.test_dynamic_pricing_engine()
        
        # Test 3: Inventory integration services
        self.test_inventory_integration()
        
        # Test 4: API endpoints
        self.test_api_endpoints()
        
        # Test 5: Service integration
        self.test_service_integration()

    def test_pricing_models(self):
        """Test Phase 2 pricing models"""
        try:
            self.stdout.write('🔹 Testing pricing models...')
            
            from inventory.models import SeasonalPricing, ServicePricing, PricingRule, PricingHistory
            from work_orders.models import WorkOrderType
            from setup.models import VehicleMake, ServiceCenter
            
            # Test SeasonalPricing
            seasonal_pricing = SeasonalPricing.objects.create(
                tenant_id=self.tenant_id,
                name='Test Seasonal Pricing',
                season_type='custom',
                pricing_type='both',
                start_date=date.today(),
                end_date=date.today() + timedelta(days=30),
                adjustment_type='percentage',
                adjustment_value=Decimal('10.0'),
                is_active=True
            )
            
            # Test ServicePricing
            work_order_type = WorkOrderType.objects.filter(tenant_id=self.tenant_id).first()
            vehicle_make = VehicleMake.objects.filter(tenant_id=self.tenant_id).first()
            service_center = ServiceCenter.objects.filter(tenant_id=self.tenant_id).first()
            
            if work_order_type and vehicle_make and service_center:
                service_pricing = ServicePricing.objects.create(
                    tenant_id=self.tenant_id,
                    name='Test Service Pricing',
                    pricing_strategy='standard',
                    operation_type=work_order_type,
                    vehicle_make=vehicle_make,
                    service_center=service_center,
                    base_service_price=Decimal('100.0'),
                    labor_rate_per_hour=Decimal('50.0'),
                    estimated_labor_hours=Decimal('2.0'),
                    valid_from=date.today(),
                    is_active=True
                )
            
            # Test PricingRule
            pricing_rule = PricingRule.objects.create(
                tenant_id=self.tenant_id,
                name='Test Pricing Rule',
                rule_type='discount',
                condition_type='order_value',
                condition_operator='gte',
                condition_value='1000',
                action_type='percentage',
                action_value=Decimal('5.0'),
                applies_to='total',
                valid_from=date.today(),
                is_active=True
            )
            
            # Clean up test data
            seasonal_pricing.delete()
            if 'service_pricing' in locals():
                service_pricing.delete()
            pricing_rule.delete()
            
            self.phase2_results['pricing_models'] = 'PASS'
            self.stdout.write(self.style.SUCCESS('  ✅ Pricing models working'))
            
        except Exception as e:
            self.phase2_results['pricing_models'] = f'FAIL: {e}'
            self.validation_errors.append(f'Pricing models test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Pricing models failed: {e}'))

    def test_dynamic_pricing_engine(self):
        """Test dynamic pricing engine"""
        try:
            self.stdout.write('🔹 Testing dynamic pricing engine...')
            
            from inventory.services import DynamicPricingEngine
            from work_orders.models import WorkOrderType
            from setup.models import Vehicle, ServiceCenter, Customer
            
            # Get test data
            work_order_type = WorkOrderType.objects.filter(tenant_id=self.tenant_id).first()
            vehicle = Vehicle.objects.filter(tenant_id=self.tenant_id).first()
            service_center = ServiceCenter.objects.filter(tenant_id=self.tenant_id).first()
            customer = Customer.objects.filter(tenant_id=self.tenant_id).first()
            
            if not all([work_order_type, vehicle, service_center, customer]):
                self.phase2_results['dynamic_pricing_engine'] = 'SKIP: Missing test data'
                self.validation_warnings.append('Cannot test dynamic pricing engine - missing test data')
                self.stdout.write(self.style.WARNING('  ⚠️ Skipping dynamic pricing engine test - missing test data'))
                return
            
            # Test pricing engine
            engine = DynamicPricingEngine(self.tenant_id)
            
            result = engine.calculate_service_price(
                operation_type_id=str(work_order_type.id),
                vehicle=vehicle,
                service_center=service_center,
                customer=customer,
                parts_list=[],
                pricing_strategy='standard'
            )
            
            if result and result.get('success'):
                # Check if we have final calculation with total price
                final_calc = result.get('final_calculation', {})
                if 'total_price' in final_calc:
                    self.phase2_results['dynamic_pricing_engine'] = 'PASS'
                    self.stdout.write(self.style.SUCCESS('  ✅ Dynamic pricing engine working'))
                else:
                    self.phase2_results['dynamic_pricing_engine'] = 'FAIL: No total price in result'
                    self.validation_errors.append('Dynamic pricing engine missing total price')
                    self.stdout.write(self.style.ERROR('  ❌ Dynamic pricing engine failed - no total price'))
            elif result and not result.get('success'):
                error_msg = result.get('error', 'Unknown error')
                self.phase2_results['dynamic_pricing_engine'] = f'FAIL: {error_msg}'
                self.validation_errors.append(f'Dynamic pricing engine failed: {error_msg}')
                self.stdout.write(self.style.ERROR(f'  ❌ Dynamic pricing engine failed: {error_msg}'))
            else:
                self.phase2_results['dynamic_pricing_engine'] = 'FAIL: No pricing result'
                self.validation_errors.append('Dynamic pricing engine returned no result')
                self.stdout.write(self.style.ERROR('  ❌ Dynamic pricing engine failed'))
                
        except Exception as e:
            import traceback
            error_detail = f'{e}\n{traceback.format_exc()}'
            self.phase2_results['dynamic_pricing_engine'] = f'FAIL: {e}'
            self.validation_errors.append(f'Dynamic pricing engine test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Dynamic pricing engine failed: {e}'))
            self.stdout.write(self.style.ERROR(f'Full traceback:\n{traceback.format_exc()}'))

    def test_inventory_integration(self):
        """Test inventory integration services"""
        try:
            self.stdout.write('🔹 Testing inventory integration services...')
            
            from inventory.services import InventoryIntegrationService, AutomatedPartsRequestService
            from inventory.models import Item
            
            # Test InventoryIntegrationService
            inventory_service = InventoryIntegrationService(self.tenant_id)
            
            # Test with sample parts list
            test_item = Item.objects.filter(tenant_id=self.tenant_id).first()
            if test_item:
                parts_list = [{'item_id': str(test_item.id), 'quantity': 2.0}]
                result = inventory_service.validate_stock_availability(parts_list)
                
                if result and 'total_items' in result:
                    self.phase2_results['inventory_integration'] = 'PASS'
                    self.stdout.write(self.style.SUCCESS('  ✅ Inventory integration services working'))
                else:
                    self.phase2_results['inventory_integration'] = 'FAIL: No validation result'
                    self.validation_errors.append('Inventory integration service returned no result')
                    self.stdout.write(self.style.ERROR('  ❌ Inventory integration failed'))
            else:
                self.phase2_results['inventory_integration'] = 'SKIP: No test items available'
                self.validation_warnings.append('Cannot test inventory integration - no items available')
                self.stdout.write(self.style.WARNING('  ⚠️ Skipping inventory integration test - no items'))
                
        except Exception as e:
            self.phase2_results['inventory_integration'] = f'FAIL: {e}'
            self.validation_errors.append(f'Inventory integration test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Inventory integration failed: {e}'))

    def test_api_endpoints(self):
        """Test Phase 2 API endpoints"""
        try:
            self.stdout.write('🔹 Testing API endpoints...')
            
            # Import API viewsets
            from api.views import PricingAPIViewSet, InventoryAPIViewSet, WorkOrderIntegrationAPIViewSet
            
            # Test that viewsets can be instantiated
            pricing_viewset = PricingAPIViewSet()
            inventory_viewset = InventoryAPIViewSet()
            integration_viewset = WorkOrderIntegrationAPIViewSet()
            
            # Check if required methods exist
            required_methods = {
                'PricingAPIViewSet': ['calculate_service_price', 'calculate_work_order_pricing', 'pricing_strategies'],
                'InventoryAPIViewSet': ['validate_stock_availability', 'create_parts_request', 'run_automated_monitoring'],
                'WorkOrderIntegrationAPIViewSet': ['allocate_stock_for_work_order', 'validate_work_order_transition']
            }
            
            missing_methods = []
            viewsets = {
                'PricingAPIViewSet': pricing_viewset,
                'InventoryAPIViewSet': inventory_viewset,
                'WorkOrderIntegrationAPIViewSet': integration_viewset
            }
            
            for viewset_name, methods in required_methods.items():
                viewset = viewsets[viewset_name]
                for method in methods:
                    if not hasattr(viewset, method):
                        missing_methods.append(f'{viewset_name}.{method}')
            
            if missing_methods:
                self.phase2_results['api_endpoints'] = f'FAIL: Missing methods {missing_methods}'
                self.validation_errors.append(f'API endpoints missing methods: {missing_methods}')
                self.stdout.write(self.style.ERROR(f'  ❌ API endpoints missing methods: {missing_methods}'))
            else:
                self.phase2_results['api_endpoints'] = 'PASS'
                self.stdout.write(self.style.SUCCESS('  ✅ API endpoints structure valid'))
                
        except Exception as e:
            self.phase2_results['api_endpoints'] = f'FAIL: {e}'
            self.validation_errors.append(f'API endpoints test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ API endpoints failed: {e}'))

    def test_service_integration(self):
        """Test service integration between components"""
        try:
            self.stdout.write('🔹 Testing service integration...')
            
            from work_orders.services import WorkOrderPricingService
            from work_orders.models import WorkOrder
            
            # Get test work order
            test_work_order = WorkOrder.objects.filter(tenant_id=self.tenant_id).first()
            
            if test_work_order:
                # Test pricing service integration
                result = WorkOrderPricingService.calculate_work_order_estimate(
                    test_work_order,
                    pricing_strategy='standard',
                    force_recalculate=True
                )
                
                if result and 'success' in result:
                    self.phase2_results['service_integration'] = 'PASS'
                    self.stdout.write(self.style.SUCCESS('  ✅ Service integration working'))
                else:
                    self.phase2_results['service_integration'] = 'FAIL: No pricing result'
                    self.validation_errors.append('Service integration failed to calculate pricing')
                    self.stdout.write(self.style.ERROR('  ❌ Service integration failed'))
            else:
                self.phase2_results['service_integration'] = 'SKIP: No test work order available'
                self.validation_warnings.append('Cannot test service integration - no work orders available')
                self.stdout.write(self.style.WARNING('  ⚠️ Skipping service integration test - no work orders'))
                
        except Exception as e:
            import traceback
            self.phase2_results['service_integration'] = f'FAIL: {e}'
            self.validation_errors.append(f'Service integration test failed: {e}')
            self.stdout.write(self.style.ERROR(f'  ❌ Service integration failed: {e}'))
            self.stdout.write(self.style.ERROR(f'Service integration traceback:\n{traceback.format_exc()}'))

    def display_summary(self):
        """Display validation summary"""
        self.stdout.write(self.style.SUCCESS('\n📊 VALIDATION SUMMARY'))
        self.stdout.write('=' * 50)
        
        # Phase 1 summary
        if self.phase1_results:
            self.stdout.write(self.style.WARNING('\n🔧 Phase 1 Results:'))
            for test, result in self.phase1_results.items():
                status = '✅' if result == 'PASS' else '⚠️' if result.startswith('SKIP') else '❌'
                self.stdout.write(f'  {status} {test}: {result}')
        
        # Phase 2 summary
        if self.phase2_results:
            self.stdout.write(self.style.WARNING('\n💰 Phase 2 Results:'))
            for test, result in self.phase2_results.items():
                status = '✅' if result == 'PASS' else '⚠️' if result.startswith('SKIP') else '❌'
                self.stdout.write(f'  {status} {test}: {result}')
        
        # Overall summary
        total_tests = len(self.phase1_results) + len(self.phase2_results)
        passed_tests = sum(1 for result in list(self.phase1_results.values()) + list(self.phase2_results.values()) if result == 'PASS')
        failed_tests = len(self.validation_errors)
        skipped_tests = sum(1 for result in list(self.phase1_results.values()) + list(self.phase2_results.values()) if result.startswith('SKIP'))
        
        self.stdout.write(f'\n📈 Overall Results:')
        self.stdout.write(f'  Total Tests: {total_tests}')
        self.stdout.write(f'  Passed: {passed_tests}')
        self.stdout.write(f'  Failed: {failed_tests}')
        self.stdout.write(f'  Skipped: {skipped_tests}')
        
        # Errors and warnings
        if self.validation_errors:
            self.stdout.write(self.style.ERROR('\n❌ Critical Issues:'))
            for error in self.validation_errors:
                self.stdout.write(f'  • {error}')
        
        if self.validation_warnings:
            self.stdout.write(self.style.WARNING('\n⚠️ Warnings:'))
            for warning in self.validation_warnings:
                self.stdout.write(f'  • {warning}')
                
        # Completion status
        if failed_tests == 0:
            self.stdout.write(self.style.SUCCESS('\n🎉 All tests passed! Both phases are working correctly.'))
        else:
            self.stdout.write(self.style.ERROR(f'\n🔧 {failed_tests} issues need to be fixed.'))

    def attempt_fixes(self):
        """Attempt to fix identified issues automatically"""
        self.stdout.write(self.style.WARNING('\n🔧 ATTEMPTING AUTOMATIC FIXES'))
        self.stdout.write('=' * 50)
        
        # This will be implemented based on the specific issues found
        self.stdout.write('  Automatic fixes will be implemented based on validation results...') 