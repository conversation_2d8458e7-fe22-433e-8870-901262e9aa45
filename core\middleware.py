from django.utils.deprecation import MiddlewareMixin
from django.utils.functional import SimpleLazyObject
from django.utils import translation
import uuid
import threading

# Thread local storage for tenant context
_tenant_context = threading.local()

def get_current_tenant_id():
    """
    Get the current tenant ID from thread local storage
    """
    return getattr(_tenant_context, 'tenant_id', None)

def set_current_tenant_id(tenant_id):
    """
    Set the current tenant ID in thread local storage
    """
    _tenant_context.tenant_id = tenant_id


def get_tenant_id_from_request(request):
    """
    Extract tenant ID from request.
    This could be from a session, a subdomain, a header, etc.
    For now, we'll use a simple header for demonstration.
    """
    # First try from headers
    tenant_id = request.headers.get('X-Tenant-ID')
    
    # Then try from session
    if not tenant_id and hasattr(request, 'session') and 'tenant_id' in request.session:
        tenant_id = request.session['tenant_id']
    
    # Then try from query parameters
    if not tenant_id:
        tenant_id = request.GET.get('tenant_id')
    
    # Return None if no tenant ID is provided
    if not tenant_id:
        return None
    
    # Try to convert tenant_id to a valid UUID
    try:
        return uuid.UUID(tenant_id)
    except (ValueError, TypeError):
        # If tenant_id is not a valid UUID, return None
        return None


class CurrentTenantMiddleware:
    """
    Middleware that sets the tenant_id based on the request
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Clear tenant ID at the start of request
        set_current_tenant_id(None)
        
        # For single-tenant setups, we don't require tenant_id
        # Set a default tenant_id for all requests - using the existing tenant with data
        default_tenant_id = uuid.UUID('979c54ab-b52c-4f12-a887-65c8baae7788')
        
        # Always set the default tenant_id in thread local storage
        set_current_tenant_id(default_tenant_id)
        # Also store in request object for easy access in views
        request.tenant_id = default_tenant_id
                
        # Process the request
        response = self.get_response(request)
        
        # Clear tenant ID at the end of request
        set_current_tenant_id(None)
        
        return response


class AdminLanguageMiddleware(MiddlewareMixin):
    """
    Middleware that forces the admin site to use Arabic language by default.
    """
    
    def process_request(self, request):
        if request.path.startswith('/admin/'):
            translation.activate('ar')
            request.LANGUAGE_CODE = 'ar'


class AdminEnglishMiddleware(MiddlewareMixin):
    """
    Middleware that forces the admin site to use English language,
    overriding any other language settings.
    This takes precedence over ForceArabicLanguageMiddleware.
    """
    
    def process_request(self, request):
        # Check for both standard admin path and custom admin path
        if request.path.startswith('/admin/') or request.path.startswith('/dzJAMvwB/'):
            translation.activate('en')
            request.LANGUAGE_CODE = 'en'


class ForceArabicLanguageMiddleware(MiddlewareMixin):
    """
    Middleware that forces the frontend site to use Arabic language by default,
    regardless of user preferences or URL language prefix.
    Admin pages are excluded from this (handled by AdminEnglishMiddleware).
    """
    
    def process_request(self, request):
        # Skip forcing Arabic for admin pages (both standard and custom paths)
        if not (request.path.startswith('/admin/') or request.path.startswith('/dzJAMvwB/')):
            translation.activate('ar')
            request.LANGUAGE_CODE = 'ar' 