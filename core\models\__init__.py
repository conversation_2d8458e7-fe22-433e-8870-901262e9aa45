from django.db import models
from django.db.models.query import QuerySet
from django.conf import settings
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel


class TenantQuerySet(QuerySet):
    def for_tenant(self, tenant_id):
        return self.filter(tenant_id=tenant_id)


class TenantManager(models.Manager):
    def get_queryset(self):
        queryset = TenantQuerySet(self.model, using=self._db)
        return queryset

    def for_tenant(self, tenant_id):
        return self.get_queryset().for_tenant(tenant_id)


# Note: This class is deprecated. Use the TenantModel from common.py instead
class _TenantModelDeprecated(models.Model):
    """
    Base model for all tenant-scoped models.
    Automatically adds tenant_id field and filtering.
    """
    tenant_id = models.IntegerField(db_index=True)
    
    objects = TenantManager()
    
    class Meta:
        abstract = True

__all__ = ['TimeStampedModel', 'UUIDPrimaryKeyModel', 'TenantModel'] 