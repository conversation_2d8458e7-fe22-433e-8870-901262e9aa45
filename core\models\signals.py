from django.db.models.signals import pre_save
from django.dispatch import receiver
from django.apps import apps

from core.middleware import get_current_tenant_id
from core.models import TenantModel


@receiver(pre_save)
def set_tenant_id(sender, instance, **kwargs):
    """
    Signal to automatically set tenant_id on model instances before saving
    """
    # Skip if not a TenantModel
    if not isinstance(instance, TenantModel):
        return
        
    # Skip if tenant_id is already set
    if instance.tenant_id:
        return
        
    # Get current tenant ID from middleware
    tenant_id = get_current_tenant_id()
    
    # Set tenant_id on the instance
    if tenant_id:
        instance.tenant_id = tenant_id 