from django.db import models
from django.utils.translation import gettext_lazy as _


class BaseQuerySet(models.QuerySet):
    """
    Base QuerySet that provides tenant filtering
    capabilities for multi-tenancy. All querysets for tenant-aware
    models should inherit from this.
    """
    
    def for_tenant(self, tenant_id):
        """
        Filter objects by tenant ID.
        """
        return self.filter(tenant_id=tenant_id)
        
    def get_for_tenant(self, tenant_id, **kwargs):
        """
        Get a single object for the specified tenant.
        """
        return self.filter(tenant_id=tenant_id, **kwargs).get()
        
    def create_for_tenant(self, tenant_id, **kwargs):
        """
        Create a new object for the specified tenant.
        """
        return self.create(tenant_id=tenant_id, **kwargs) 