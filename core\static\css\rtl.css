/* RTL-specific styles for Arabic language */

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    text-align: right;
}

/* Fix for Font Awesome icons in RTL */
.fa, .fas, .far, .fal, .fab {
    margin-left: 1rem;
    margin-right: 0;
}

/* Make icons inside circles centered */
span.rounded-full .fa, 
span.rounded-full .fas, 
span.rounded-full .far, 
span.rounded-full .fal, 
span.rounded-full .fab {
    margin: 0;
}

/* Better spacing for inline block elements with icons */
span.inline-block {
    margin-right: 0.5rem;
}

/* Card title improvements */
h3.text-lg {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* Correct text alignment for tables */
th, td {
    text-align: right;
}

/* Fix for margins in RTL context */
.me-1, .me-2, .me-3, .me-4 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.ms-1, .ms-2, .ms-3, .ms-4 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

/* Better card layout for setup guide */
.bg-gray-50 {
    border-right: 4px solid #3b82f6;
    transition: all 0.2s ease;
}

.bg-gray-50:hover {
    border-right-width: 6px;
    transform: translateX(-2px);
}

.bg-gray-50:last-child {
    border-right-color: #3b82f6;
}

/* Status card borders */
.border-right-blue {
    border-right: 4px solid #3b82f6;
    transition: all 0.2s ease;
}

.border-right-blue:hover {
    border-right-width: 6px;
    transform: translateX(-2px);
}

/* Fix border directions for RTL */
.border-l-4 {
    border-left-width: 0;
    border-right-width: 4px;
}

.border-r-4 {
    border-right-width: 4px;
}

/* Fix for RTL button padding */
button.flex, a.inline-flex {
    flex-direction: row-reverse;
}

a.inline-flex i {
    margin-left: 1.5rem;
    margin-right: 0;
}

/* Better button spacing for Arabic text */
a.inline-flex {
    letter-spacing: 0.05em;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    min-width: 110px;
    text-align: center;
    justify-content: center;
}

a.inline-flex i.fas {
    margin-left: 1.5rem;
}

/* Button text positioning */
a.inline-flex span.inline-block {
    margin-right: 0;
    position: relative;
    top: 1px;
}

/* Extra spacing for play icons */
a.inline-flex i.fa-play, 
a.inline-flex i.fa-arrow-right {
    margin-left: 2rem;
}

/* Headings icon spacing */
h1 i, h2 i, h3 i {
    margin-left: 1rem;
    margin-right: 0;
}

/* RTL specific table fixes */
table thead th {
    text-align: right;
}

/* Fix for action columns */
th:last-child, td:last-child {
    text-align: left;
}

/* Animation for RTL */
@keyframes fadeInRTL {
    0% { opacity: 0; transform: translateX(20px); }
    100% { opacity: 1; transform: translateX(0); }
}

.fade-in-rtl {
    animation: fadeInRTL 0.5s ease-in;
}

/* Improve circle icon sizes */
.h-12.w-12 {
    height: 3.5rem;
    width: 3.5rem;
}

.fa-lg {
    font-size: 1.5rem;
}

/* General spacing improvements */
.p-5 {
    padding: 1.25rem;
}

.mt-5 {
    margin-top: 1.25rem;
}

.mr-4 {
    margin-right: 1rem;
}

.mr-6 {
    margin-right: 1.5rem;
}

.ml-4 {
    margin-left: 1rem;
}

/* Fix flex direction for RTL */
.flex {
    flex-direction: row-reverse;
}

/* Override for specific flex items that should keep original direction */
.flex.justify-between,
.flex.items-center,
table .flex {
    flex-direction: row;
}

/* Card spacing improvements */
.gap-6 {
    gap: 1.75rem;
}

/* Import Arabic font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* Special class for button text */
.button-text {
    margin-right: 0;
    position: relative;
    top: 1px;
    font-weight: 500;
    font-size: 0.95rem;
    letter-spacing: 0.05em;
}

/* Increase the spacing more between the icon and text */
a.inline-flex i.fa-play,
a.inline-flex i.fa-arrow-right {
    margin-left: 2.5rem !important;
}

/* Wider spacing for all links with icons */
a i.fas {
    margin-left: 1rem;
}

/* Even more spacing for specific button icons */
a.inline-flex i.fa-play,
a.inline-flex i.fa-arrow-right {
    margin-left: 2.5rem !important;
}

/* Add some spacing to the status cards links */
.border-right-blue a i.fas {
    margin-left: 1.25rem;
} 