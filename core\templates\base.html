<!DOCTYPE html>
{% load i18n %}
{% load static %}
<html lang="{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "نظام ما بعد البيع" %}{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">
    
    <!-- Tailwind CSS CDN for Development -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Tajawal', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe', 
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    <link href="{% static 'css/app.css' %}" rel="stylesheet">
    
    <!-- Flowbite CSS & JS from CDN -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js" defer></script>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.4" defer></script>
    
    <!-- Global Notification System -->
    <script src="{% static 'js/notifications.js' %}" defer></script>
    
    <!-- Google Tajawal Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- RTL CSS -->
    <link href="/static/css/rtl.css" rel="stylesheet">
    
    <style>
        /* Animations and transitions */
        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: scale(1.03);
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }
        /* Fix for RTL Font Awesome icons */
        .fa, .fas, .far, .fal, .fab {
            display: inline-block;
        }
        
        /* Base RTL styles */
        body {
            font-family: 'Tajawal', sans-serif;
        }
        
        /* Notification banner styles */
        .notification-banner {
            transition: all 0.3s ease-in-out;
            max-height: 0;
            overflow: hidden;
        }
        .notification-banner.expanded {
            max-height: 300px;
        }
        .notification-toggle {
            transition: transform 0.3s ease;
        }
        .notification-toggle.rotated {
            transform: rotate(180deg);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white border-gray-200 px-4 py-2.5 shadow-md">
        <div class="container flex flex-wrap justify-between items-center mx-auto">
            <!-- Logo -->
            <a href="{% url 'home' %}" class="flex items-center">
                <img src="/static/images/logo.png" alt="Logo" class="h-10 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}">
                <span class="self-center text-xl font-semibold whitespace-nowrap md:block hidden">{% trans "نظام ما بعد البيع" %}</span>
            </a>
            
            <!-- Main Navigation - Enhanced with Labels -->
            <div class="hidden md:flex items-center justify-center">
                <!-- Dynamic Navigation Menu -->
                <ul class="flex items-center {% if LANGUAGE_CODE == 'ar' %}space-x-0 space-x-reverse rtl{% else %}space-x-0{% endif %}">
                    {% for tab in navigation_menu %}
                        {% if not tab.parent_tab %}
                    <li class="px-4">
                                <a href="{% url tab.url_name %}" class="flex flex-col items-center {% if current_tab_code == tab.code %}text-blue-700 bg-blue-50 rounded-lg px-3 py-2{% else %}text-gray-700 hover:text-blue-700{% endif %} transition-colors group">
                                    <i class="{{ tab.icon_class }} text-xl mb-1 group-hover:scale-110 transition-transform"></i>
                                    <span class="text-xs font-medium">{{ tab.name_localized }}</span>
                        </a>
                    </li>
                            {% if not forloop.last %}
                                {% with next_tab=navigation_menu|slice:forloop.counter|first %}
                                    {% if not next_tab.parent_tab %}
                    <li class="border-l border-gray-300 h-12"></li>
                    {% endif %}
                                {% endwith %}
                    {% endif %}
                    {% endif %}
                    {% endfor %}
                </ul>
            </div>
            
            <!-- User Menu -->
            <div class="flex items-center">
                {% if user.is_authenticated %}
                
                <!-- Notifications Dropdown -->
                <div class="relative mr-4">
                    <button type="button" 
                            class="relative p-2 text-gray-600 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg transition-colors"
                            id="notifications-dropdown-button" 
                            aria-expanded="false" 
                            data-dropdown-toggle="notifications-dropdown">
                        <span class="sr-only">{% trans "عرض الإشعارات" %}</span>
                        <i class="fas fa-bell text-xl"></i>
                        <!-- Notification badge -->
                        <span id="notification-badge" 
                              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">
                            0
                        </span>
                    </button>
                    
                    <!-- Notifications dropdown content -->
                    <div id="notifications-dropdown" 
                         class="hidden absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
                        
                        <!-- Header -->
                        <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
                            <div class="flex justify-between items-center">
                                <h3 class="text-sm font-semibold text-gray-900">{% trans "الإشعارات والمهام" %}</h3>
                                <div class="flex items-center space-x-2">
                                    <button onclick="markAllNotificationsRead()" 
                                            class="text-xs text-blue-600 hover:text-blue-800">
                                        {% trans "تحديد الكل كمقروء" %}
                                    </button>
                                    <a href="{% url 'notifications:notification_center' %}" 
                                       class="text-xs text-gray-500 hover:text-gray-700">
                                        {% trans "عرض الكل" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notifications content -->
                        <div id="notifications-content" class="max-h-80 overflow-y-auto">
                            <!-- Loading state -->
                            <div id="notifications-loading" class="flex items-center justify-center py-8">
                                <div class="loading-spinner"></div>
                                <span class="ml-2 text-sm text-gray-500">{% trans "جاري التحميل..." %}</span>
                            </div>
                            
                            <!-- Empty state -->
                            <div id="notifications-empty" class="hidden text-center py-8 text-gray-500">
                                <i class="fas fa-bell-slash text-3xl mb-2"></i>
                                <p class="text-sm">{% trans "لا توجد إشعارات جديدة" %}</p>
                            </div>
                            
                            <!-- Notifications list -->
                            <div id="notifications-list" class="divide-y divide-gray-100">
                                <!-- Dynamic notifications will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- Footer -->
                        <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
                            <div class="flex justify-between">
                                <a href="{% url 'notifications:action_center' %}" 
                                   class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                                    <i class="fas fa-tasks mr-1"></i>
                                    {% trans "مركز المهام" %}
                                </a>
                                <span id="notifications-count" class="text-xs text-gray-500">
                                    0 {% trans "إشعار" %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button type="button" class="flex text-sm focus:ring-4 focus:ring-blue-300" id="user-menu-button" aria-expanded="false" data-dropdown-toggle="user-dropdown">
                    <span class="sr-only">{% trans "فتح قائمة المستخدم" %}</span>
                    <div class="w-8 h-8 text-white flex items-center justify-center bg-blue-600 rounded-full shadow-md">
                        {{ user.username|first|upper }}
                    </div>
                </button>
                <!-- Dropdown menu -->
                <div class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded-lg divide-y divide-gray-100 shadow-lg" id="user-dropdown">
                    <div class="py-3 px-4 bg-blue-600 rounded-t-lg">
                        <span class="block text-sm font-semibold text-white">{{ user.username }}</span>
                        <span class="block text-xs text-blue-100 truncate">{{ user.email }}</span>
                    </div>
                    <ul class="py-1" aria-labelledby="user-menu-button">
                        <li>
                            <a href="{% url 'core:main_dashboard' %}" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors">
                                <i class="fas fa-tachometer-alt text-blue-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "لوحة التحكم الرئيسية" %}
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors">
                                <i class="fas fa-user-cog text-indigo-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "الملف الشخصي" %}
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors">
                                <i class="fas fa-cog text-indigo-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "إعداداتي" %}
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'core:logout' %}" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-red-600 transition-colors">
                                <i class="fas fa-sign-out-alt text-red-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "تسجيل الخروج" %}
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
                
                <!-- Mobile menu button -->
                <button data-collapse-toggle="mobile-menu" type="button" class="inline-flex items-center p-2 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %} text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200" aria-controls="mobile-menu" aria-expanded="false">
                    <span class="sr-only">{% trans "فتح القائمة الرئيسية" %}</span>
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div class="hidden w-full md:hidden" id="mobile-menu">
            <!-- Dynamic Mobile Navigation -->
            <ul class="flex flex-col mt-4 text-center bg-gray-50 rounded-lg p-2">
                {% for tab in navigation_menu %}
                    {% if not tab.parent_tab %}
                <li class="py-1">
                            <a href="{% url tab.url_name %}" class="flex items-center justify-center py-3 px-4 {% if current_tab_code == tab.code %}bg-blue-100 text-blue-700{% else %}text-gray-700 hover:bg-white hover:text-blue-700{% endif %} rounded-lg transition-colors">
                                <i class="{{ tab.icon_class }} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {{ tab.name_localized }}
                    </a>
                </li>
                {% endif %}
                {% endfor %}
            </ul>
        </div>
    </nav>
    
    <!-- Notification Banner -->
    {% if user.is_authenticated %}
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 shadow-md">
        <div class="container mx-auto px-4">
            <!-- Notification Toggle Button -->
            <button 
                id="notification-toggle" 
                class="w-full py-3 flex items-center justify-between text-white hover:bg-blue-700 hover:bg-opacity-30 transition-colors rounded-lg"
                onclick="toggleNotificationBanner()"
            >
                <div class="flex items-center">
                    <i class="fas fa-bell {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %} text-lg"></i>
                    <span class="font-medium">{% trans "الإشعارات" %}</span>
                    <span id="notification-count" class="{% if LANGUAGE_CODE == 'ar' %}mr-2{% else %}ml-2{% endif %} bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                        {% if messages %}{{ messages|length }}{% else %}0{% endif %}
                    </span>
                </div>
                <i id="notification-arrow" class="fas fa-chevron-down notification-toggle transition-transform"></i>
            </button>
            
            <!-- Expandable Notification Content -->
            <div id="notification-banner" class="notification-banner">
                <div class="pb-4">
                    {% if messages %}
                        <div class="space-y-2">
                            {% for message in messages %}
                            <div class="bg-white bg-opacity-10 rounded-lg p-3 flex items-start">
                                <i class="fas fa-info-circle text-blue-200 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %} mt-1 flex-shrink-0"></i>
                                <div class="flex-1">
                                    <p class="text-white text-sm">{{ message }}</p>
                                    <span class="text-blue-200 text-xs">{% trans "الآن" %}</span>
                                </div>
                                <button class="text-blue-200 hover:text-white {% if LANGUAGE_CODE == 'ar' %}mr-2{% else %}ml-2{% endif %} flex-shrink-0" onclick="dismissNotification(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Show More Button -->
                        <div class="mt-4 text-center">
                            <a href="http://localhost:4000/notifications/" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-colors">
                                <i class="fas fa-external-link-alt {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                                {% trans "عرض جميع الإشعارات" %}
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-6">
                            <i class="fas fa-bell-slash text-blue-200 text-3xl mb-2"></i>
                            <p class="text-white text-sm">{% trans "لا توجد إشعارات جديدة" %}</p>
                            <a href="http://localhost:4000/notifications/" class="inline-flex items-center mt-2 px-3 py-1 bg-white bg-opacity-20 text-white text-xs rounded-lg hover:bg-opacity-30 transition-colors">
                                <i class="fas fa-history {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                {% trans "عرض السجل" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-white shadow-inner mt-8 py-4">
        <div class="container mx-auto px-4">
            <p class="text-center text-gray-500 text-sm">
                {% trans "نظام ما بعد البيع" %} &copy; {% now "Y" %}
            </p>
        </div>
    </footer>
    
    <script>
        function toggleNotificationBanner() {
            const banner = document.getElementById('notification-banner');
            const arrow = document.getElementById('notification-arrow');
            
            banner.classList.toggle('expanded');
            arrow.classList.toggle('rotated');
        }
        
        function dismissNotification(button) {
            const notification = button.closest('.bg-white');
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
                updateNotificationCount();
            }, 300);
        }
        
        function updateNotificationCount() {
            const notifications = document.querySelectorAll('#notification-banner .bg-white');
            const countElement = document.getElementById('notification-count');
            countElement.textContent = notifications.length;
            
            if (notifications.length === 0) {
                const isRTL = '{{ LANGUAGE_CODE }}' === 'ar';
                const noNotificationsText = '{% trans "لا توجد إشعارات جديدة" %}';
                const viewHistoryText = '{% trans "عرض السجل" %}';
                const historyIconClass = isRTL ? 'ml-1' : 'mr-1';
                
                document.getElementById('notification-banner').innerHTML = 
                    '<div class="pb-4">' +
                        '<div class="text-center py-6">' +
                            '<i class="fas fa-bell-slash text-blue-200 text-3xl mb-2"></i>' +
                            '<p class="text-white text-sm">' + noNotificationsText + '</p>' +
                            '<a href="http://localhost:4000/notifications/" class="inline-flex items-center mt-2 px-3 py-1 bg-white bg-opacity-20 text-white text-xs rounded-lg hover:bg-opacity-30 transition-colors">' +
                                '<i class="fas fa-history ' + historyIconClass + '"></i>' +
                                viewHistoryText +
                            '</a>' +
                        '</div>' +
                    '</div>';
            }
        }
        
        // Auto-collapse notification banner after 10 seconds if expanded
        document.addEventListener('DOMContentLoaded', function() {
            const banner = document.getElementById('notification-banner');
            const arrow = document.getElementById('notification-arrow');
            
            // Auto-expand if there are notifications
            {% if messages %}
            setTimeout(function() {
                banner.classList.add('expanded');
                arrow.classList.add('rotated');
                
                // Auto-collapse after 10 seconds
                setTimeout(function() {
                    banner.classList.remove('expanded');
                    arrow.classList.remove('rotated');
                }, 10000);
            }, 500);
            {% endif %}
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 