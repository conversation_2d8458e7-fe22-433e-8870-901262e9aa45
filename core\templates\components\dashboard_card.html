{% load i18n %}
{# 
Dashboard Card Component
-------------------------
Usage: 
{% include "components/dashboard_card.html" with 
  title="Card Title"
  value="123"
  subtitle="Subtitle text"
  icon="fa-cube"
  icon_color="primary"
  link="/some/url"
  link_text="Action Link"
  animate=True
%}

Parameters:
- title: The card title (required)
- value: The main value/number to display (required)
- subtitle: Additional text below the value (optional)
- icon: Font Awesome icon class (optional)
- icon_color: Color scheme for the icon (primary, success, warning, danger, info) (optional, default: primary)
- link: URL for the action link (optional)
- link_text: Text for the action link (optional)
- animate: Whether to apply animation effects (optional, default: False)
#}

<div class="card hover-shadow {% if animate %}hover-scale animate-fade-in{% endif %}">
  <div class="p-5">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-500">{{ title }}</p>
        <p class="text-3xl font-bold text-gray-800 mt-1">{{ value }}</p>
        {% if subtitle %}
        <p class="text-sm text-gray-500 mt-1">{{ subtitle }}</p>
        {% endif %}
      </div>
      
      {% if icon %}
      <div class="p-3 rounded-full 
        {% if icon_color == 'success' %}
          bg-accent-500 text-white
        {% elif icon_color == 'warning' %}
          bg-yellow-400 text-white
        {% elif icon_color == 'danger' %}
          bg-red-500 text-white
        {% elif icon_color == 'info' %}
          bg-blue-400 text-white
        {% else %}
          bg-primary-500 text-white
        {% endif %}">
        <i class="fas {{ icon }} w-6 h-6"></i>
      </div>
      {% endif %}
    </div>
    
    {% if link and link_text %}
    <div class="mt-4">
      <a href="{{ link }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
        {{ link_text }} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
      </a>
    </div>
    {% endif %}
  </div>
</div> 