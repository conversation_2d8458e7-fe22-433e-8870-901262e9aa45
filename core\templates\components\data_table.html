{% load i18n %}
{# 
Data Table Component
-------------------------
Usage: 
{% include "components/data_table.html" with 
  columns=columns
  rows=rows
  empty_message="No data available"
  table_id="my-table"
  paginated=True
  sortable=True
%}

Parameters:
- columns: List of column definitions, each containing 'name', 'key', and optional 'sortable', 'align' (optional)
- rows: List of data rows (required)
- empty_message: Message to display when there are no rows (optional)
- table_id: HTML ID for the table (optional)
- paginated: Whether to include pagination controls (optional, default: False)
- sortable: Whether to enable column sorting (optional, default: False)
- current_page: Current page number for pagination (optional, default: 1)
- total_pages: Total number of pages (optional)
- page_url: Base URL for pagination links (optional)
#}

<div class="table-container" 
     {% if table_id %}id="{{ table_id }}-container"{% endif %}
     {% if sortable %}
     hx-target="this"
     hx-swap="outerHTML"
     {% endif %}>
  <table class="table" {% if table_id %}id="{{ table_id }}"{% endif %}>
    <!-- Table Header -->
    <thead class="table-header">
      <tr>
        {% for column in columns %}
        <th scope="col" class="table-cell {% if column.align == 'right' %}text-right rtl:text-left{% elif column.align == 'center' %}text-center{% else %}text-left rtl:text-right{% endif %}">
          {% if sortable and column.sortable %}
          <a href="#" 
             class="group inline-flex items-center"
             {% if sortable %}
             hx-get="{{ page_url }}?sort={{ column.key }}&order={% if sort_key == column.key and sort_order == 'asc' %}desc{% else %}asc{% endif %}"
             {% endif %}>
            {{ column.name }}
            <span class="ml-2 rtl:mr-2 rtl:ml-0 flex-none rounded text-gray-400 group-hover:visible group-focus:visible">
              {% if sort_key == column.key %}
                {% if sort_order == 'asc' %}
                <i class="fas fa-sort-up"></i>
                {% else %}
                <i class="fas fa-sort-down"></i>
                {% endif %}
              {% else %}
                <i class="fas fa-sort"></i>
              {% endif %}
            </span>
          </a>
          {% else %}
          {{ column.name }}
          {% endif %}
        </th>
        {% endfor %}
      </tr>
    </thead>
    
    <!-- Table Body -->
    <tbody>
      {% if rows %}
        {% for row in rows %}
        <tr class="table-row">
          {% for column in columns %}
          <td class="table-cell {% if column.align == 'right' %}text-right rtl:text-left{% elif column.align == 'center' %}text-center{% else %}text-left rtl:text-right{% endif %}">
            {{ row|get_item:column.key|safe }}
          </td>
          {% endfor %}
        </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td colspan="{{ columns|length }}" class="table-cell text-center text-gray-500">
            {{ empty_message|default:_("No items found.") }}
          </td>
        </tr>
      {% endif %}
    </tbody>
  </table>
  
  <!-- Pagination Controls -->
  {% if paginated and total_pages > 1 %}
  <div class="py-3 px-4 border-t border-gray-200 bg-gray-50 flex items-center justify-between">
    <div class="flex-1 flex justify-between items-center">
      <div>
        <p class="text-sm text-gray-700">
          {% blocktrans with start=page_start end=page_end total=total_items %}
          Showing <span class="font-medium">{{ start }}</span> to <span class="font-medium">{{ end }}</span> of <span class="font-medium">{{ total }}</span> results
          {% endblocktrans %}
        </p>
      </div>
      <div>
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse" aria-label="Pagination">
          <!-- Previous Page -->
          <a href="{% if current_page > 1 %}{{ page_url }}?page={{ current_page|add:'-1' }}&sort={{ sort_key }}&order={{ sort_order }}{% else %}#{% endif %}"
             class="relative inline-flex items-center px-2 py-2 rounded-l-md rtl:rounded-r-md rtl:rounded-l-none border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 {% if current_page <= 1 %}opacity-50 cursor-not-allowed{% endif %}">
            <span class="sr-only">{% trans "Previous" %}</span>
            <i class="fas fa-chevron-left rtl:rotate-180"></i>
          </a>
          
          <!-- Page Numbers -->
          {% for page_num in page_range %}
            {% if page_num == current_page %}
            <a href="#" aria-current="page" class="z-10 bg-primary-50 border-primary-500 text-primary-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
              {{ page_num }}
            </a>
            {% else %}
            <a href="{{ page_url }}?page={{ page_num }}&sort={{ sort_key }}&order={{ sort_order }}" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
              {{ page_num }}
            </a>
            {% endif %}
          {% endfor %}
          
          <!-- Next Page -->
          <a href="{% if current_page < total_pages %}{{ page_url }}?page={{ current_page|add:'1' }}&sort={{ sort_key }}&order={{ sort_order }}{% else %}#{% endif %}" 
             class="relative inline-flex items-center px-2 py-2 rounded-r-md rtl:rounded-l-md rtl:rounded-r-none border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 {% if current_page >= total_pages %}opacity-50 cursor-not-allowed{% endif %}">
            <span class="sr-only">{% trans "Next" %}</span>
            <i class="fas fa-chevron-right rtl:rotate-180"></i>
          </a>
        </nav>
      </div>
    </div>
  </div>
  {% endif %}
</div> 