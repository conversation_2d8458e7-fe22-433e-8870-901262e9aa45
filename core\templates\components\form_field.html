{% load i18n %}
{# 
Form Field Component
-------------------------
Usage: 
{% include "components/form_field.html" with 
  field=form.field_name
  label="Custom Label"
  required=True
  help_text="Help text"
  icon="fa-user"
%}

Parameters:
- field: The form field (required)
- label: Custom label text (optional, defaults to field.label)
- required: Whether the field is required (optional, defaults to field.field.required)
- help_text: Custom help text (optional, defaults to field.help_text)
- icon: Font Awesome icon class to display in the field (optional)
#}

{% with field_id=field.auto_id %}
<div class="mb-4">
  {# Label #}
  <label for="{{ field_id }}" class="form-label">
    {{ label|default:field.label }}
    {% if required|default:field.field.required %}
    <span class="text-red-500">*</span>
    {% endif %}
  </label>
  
  {# Field #}
  <div class="relative">
    {% if icon %}
    <div class="absolute inset-y-0 left-0 rtl:right-0 rtl:left-auto flex items-center pl-3 rtl:pr-3 rtl:pl-0 pointer-events-none">
      <i class="fas {{ icon }} text-gray-400"></i>
    </div>
    {% endif %}
    
    {% if field.field.widget.input_type == 'checkbox' %}
      <div class="flex items-center">
        {{ field }}
        {% if help_text|default:field.help_text %}
        <span class="text-sm text-gray-500 ml-2 rtl:mr-2 rtl:ml-0">{{ help_text|default:field.help_text }}</span>
        {% endif %}
      </div>
    {% elif field.field.widget.input_type == 'radio' %}
      <div class="space-y-2">
        {{ field }}
      </div>
      {% if help_text|default:field.help_text %}
      <p class="text-sm text-gray-500 mt-1">{{ help_text|default:field.help_text }}</p>
      {% endif %}
    {% elif field.field.widget.input_type == 'select' %}
      {{ field }}
      {% if help_text|default:field.help_text %}
      <p class="text-sm text-gray-500 mt-1">{{ help_text|default:field.help_text }}</p>
      {% endif %}
    {% elif field.field.widget.input_type == 'textarea' %}
      {{ field }}
      {% if help_text|default:field.help_text %}
      <p class="text-sm text-gray-500 mt-1">{{ help_text|default:field.help_text }}</p>
      {% endif %}
    {% else %}
      {{ field }}
      {% if help_text|default:field.help_text %}
      <p class="text-sm text-gray-500 mt-1">{{ help_text|default:field.help_text }}</p>
      {% endif %}
    {% endif %}
  </div>
  
  {# Error Message #}
  {% if field.errors %}
  <div class="error-message text-sm text-red-500 mt-1 rtl:text-right">
    {% for error in field.errors %}
    <p>{{ error }}</p>
    {% endfor %}
  </div>
  {% endif %}
</div>
{% endwith %} 