{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load waffle_tags %}

{% block title %}{% trans "لوحة التحكم" %} - {{ block.super }}{% endblock %}

{% block body_class %}dashboard-layout{% endblock %}

{% block extra_css %}
<!-- Dashboard specific styles -->
<style>
    /* Dashboard layout improvements */
    .dashboard-sidebar {
        transition: transform 0.3s ease-in-out;
        box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    }
    
    .dashboard-main {
        min-height: calc(100vh - 8rem);
    }
    
    /* Notification styles */
    .notification {
        animation: slideInDown 0.3s ease-out;
    }
    
    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    /* Card hover effects */
    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    /* Sidebar menu animations */
    .sidebar-menu li a {
        transition: all 0.2s ease;
    }
    
    .sidebar-menu li a:hover {
        background-color: #f3f4f6;
        padding-right: 1.5rem;
    }
    
    /* Active menu item */
    .sidebar-menu li a.active {
        background-color: #3b82f6;
        color: white;
        border-radius: 0.5rem;
    }
    
    /* Mobile sidebar overlay */
    .sidebar-overlay {
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s ease;
    }
    
    /* Loading states */
    .loading-spinner {
        border: 2px solid #f3f4f6;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        width: 1rem;
        height: 1rem;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="flex h-screen bg-gray-50">
    <!-- Sidebar -->
    <div id="sidebar" class="dashboard-sidebar fixed inset-y-0 right-0 z-50 w-64 bg-white border-l border-gray-200 transform lg:translate-x-0 lg:static lg:inset-0">
        <!-- Sidebar header -->
        <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <div class="flex items-center">
                <i class="fas fa-tachometer-alt text-blue-600 text-xl mr-2"></i>
                <h2 class="text-lg font-semibold text-gray-800">{% trans "لوحة التحكم" %}</h2>
            </div>
            <button id="sidebar-close" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <!-- User info -->
        {% if user.is_authenticated %}
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    {{ user.first_name|first|default:user.username|first|upper }}
                </div>
                <div class="mr-3">
                    <p class="text-sm font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</p>
                    <p class="text-xs text-gray-500">{{ user.email|default:"" }}</p>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Navigation menu -->
        <div class="py-4">
            <ul class="sidebar-menu space-y-1 px-3">
                <li>
                    <a href="{% url 'core:main_dashboard' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-md {% if request.resolver_match.url_name == 'main_dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt w-5 h-5 ml-3"></i>
                        <span>{% trans "الرئيسية" %}</span>
                    </a>
                </li>
                
                {% if perms.work_orders.view_workorder or user.is_staff %}
                <li>
                    <a href="{% url 'work_orders:work_order_list' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-md {% if 'work_orders' in request.path %}active{% endif %}">
                        <i class="fas fa-tools w-5 h-5 ml-3"></i>
                        <span>{% trans "أوامر العمل" %}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if perms.inventory.view_item or user.is_staff %}
                <li>
                    <a href="/core/supply-chain/inventory/" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-md {% if '/supply-chain/inventory/' in request.path %}active{% endif %}">
                        <i class="fas fa-warehouse w-5 h-5 ml-3"></i>
                        <span>{% trans "المخزون" %}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if perms.sales.view_sale or user.is_staff %}
                <li>
                    <a href="{% url 'sales:dashboard' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-md {% if 'sales' in request.path %}active{% endif %}">
                        <i class="fas fa-cash-register w-5 h-5 ml-3"></i>
                        <span>{% trans "المبيعات" %}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if perms.purchases.view_purchase or user.is_staff %}
                <li>
                    <a href="/core/supply-chain/purchases/" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-md {% if '/supply-chain/purchases/' in request.path %}active{% endif %}">
                        <i class="fas fa-shopping-cart w-5 h-5 ml-3"></i>
                        <span>{% trans "المشتريات" %}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if perms.reports.view_report or user.is_staff %}
                <li>
                    <a href="{% url 'reports:report_list' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-md {% if 'reports' in request.path %}active{% endif %}">
                        <i class="fas fa-chart-line w-5 h-5 ml-3"></i>
                        <span>{% trans "التقارير" %}</span>
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_staff %}
                <li>
                    <a href="{% url 'admin:index' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-md">
                        <i class="fas fa-cog w-5 h-5 ml-3"></i>
                        <span>{% trans "الإدارة" %}</span>
                    </a>
                </li>
                {% endif %}
                
                <li>
                    <a href="{% url 'core:logout' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 rounded-md">
                        <i class="fas fa-sign-out-alt w-5 h-5 ml-3"></i>
                        <span>{% trans "تسجيل الخروج" %}</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- Main content -->
    <div class="dashboard-main flex-1 flex flex-col overflow-hidden lg:mr-64">
        <!-- Top bar -->
        <header class="bg-white border-b border-gray-200 px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <button id="sidebar-toggle" class="lg:hidden mr-4 p-2 rounded-md text-gray-400 hover:text-gray-600">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-semibold text-gray-900">{% block page_title %}{% trans "لوحة التحكم" %}{% endblock %}</h1>
                        <p class="text-sm text-gray-500">{% block page_subtitle %}{% trans "مرحباً بك في نظام إدارة الامتياز" %}{% endblock %}</p>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page content -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
            <!-- Notifications container -->
            <div id="notifications-container" class="fixed top-20 left-4 right-4 z-[99999] space-y-2"></div>
            
            {% block dashboard_content %}
            <!-- Default dashboard content -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- Statistics cards will be populated by JavaScript -->
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Recent activities and other widgets -->
            </div>
            {% endblock %}
        </main>
    </div>
</div>

<!-- Global notification system -->
<script>
window.notificationSystem = {
    showNotification: function(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications-container');
        const notification = document.createElement('div');
        
        // Set notification type classes
        let bgColor, borderColor, textColor, iconClass;
        switch(type) {
            case 'success':
                bgColor = 'bg-green-50';
                borderColor = 'border-green-400';
                textColor = 'text-green-800';
                iconClass = 'fa-check-circle';
                break;
            case 'error':
                bgColor = 'bg-red-50';
                borderColor = 'border-red-400';
                textColor = 'text-red-800';
                iconClass = 'fa-exclamation-circle';
                break;
            case 'warning':
                bgColor = 'bg-yellow-50';
                borderColor = 'border-yellow-400';
                textColor = 'text-yellow-800';
                iconClass = 'fa-exclamation-triangle';
                break;
            default: // info
                bgColor = 'bg-blue-50';
                borderColor = 'border-blue-400';
                textColor = 'text-blue-800';
                iconClass = 'fa-info-circle';
        }
        
        // Create notification HTML
        notification.className = `notification p-4 rounded-md border ${bgColor} ${borderColor} ${textColor} shadow-md`;
        notification.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas ${iconClass}"></i>
                </div>
                <div class="mr-3">
                    <p>${message}</p>
                </div>
                <div class="mr-auto">
                    <button type="button" class="notification-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        
        // Add close button functionality
        notification.querySelector('.notification-close').addEventListener('click', function() {
            notification.remove();
        });
        
        // Add to container
        container.appendChild(notification);
        
        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                notification.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, duration);
        }
        
        return notification;
    }
};

// Mobile sidebar toggle
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarClose = document.getElementById('sidebar-close');
    const sidebar = document.getElementById('sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.remove('transform', 'translate-x-full');
            sidebar.classList.add('translate-x-0');
            
            // Add overlay
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay fixed inset-0 z-40 bg-black bg-opacity-50';
            overlay.id = 'sidebar-overlay';
            document.body.appendChild(overlay);
            
            // Close on overlay click
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('translate-x-0');
                sidebar.classList.add('translate-x-full');
                overlay.remove();
            });
        });
    }
    
    if (sidebarClose && sidebar) {
        sidebarClose.addEventListener('click', function() {
            sidebar.classList.remove('translate-x-0');
            sidebar.classList.add('translate-x-full');
            
            // Remove overlay
            const overlay = document.getElementById('sidebar-overlay');
            if (overlay) {
                overlay.remove();
            }
        });
    }
});
</script>

{% block dashboard_scripts %}{% endblock %}
{% endblock %} 