{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block body_class %}login-page{% endblock %}

{% block extra_css %}
<style>
    /* RTL specific styles for form inputs */
    input[type="text"], 
    input[type="password"] {
        direction: rtl !important;
        text-align: right !important;
    }
    .rtl {
        direction: rtl !important;
    }
    /* Fix label alignment */
    label {
        display: block;
        text-align: right !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="flex items-center justify-center min-h-screen" style="background-size: cover; background-position: stretch;">
    <div class="bg-white dark:bg-gray-800 shadow-2xl rounded-lg p-8 md:p-10 max-w-md w-full mx-auto backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90 transition duration-200 ease-out">
        <div class="text-center mb-8">
            <img src="{% static 'images/logo.png' %}" alt="{% trans 'After-Sales System Logo' %}" class="mx-auto h-20 mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">{% trans "After-Sales System" %}</h1>
            
            <p class="text-gray-600 dark:text-gray-400 mb-2">
                {% trans "Welcome to the After-Sales Franchise Management System" %}
            </p>
        </div>
        
        <form method="post" class="space-y-6 rtl" dir="rtl">
            {% csrf_token %}
            
            <div class="rtl text-right">
                <label for="id_username" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans "Username" %}</label>
                <input type="text" name="username" id="id_username" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 text-right" placeholder="{% trans 'Username' %}" required>
            </div>
            
            <div class="rtl text-right">
                <label for="id_password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{% trans "Password" %}</label>
                <input type="password" name="password" id="id_password" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 text-right" placeholder="••••••••" required>
            </div>
            
            <div class="flex items-center justify-between rtl">
                <a href="#" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">{% trans "Forgot password?" %}</a>
                <div class="flex items-start">
                    <div class="ml-3 text-sm">
                        <label for="remember" class="text-gray-500 dark:text-gray-300">{% trans "Remember me" %}</label>
                    </div>
                    <div class="flex items-center h-5">
                        <input id="remember" name="remember" aria-describedby="remember" type="checkbox" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800">
                    </div>
                </div>
            </div>
            
            <button type="submit" class="w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 transition duration-200 ease-out hover:-translate-y-0.5 hover:shadow-lg">{% trans "Sign in" %}</button>
            
            {% if form.errors %}
            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 rtl text-right" role="alert">
                <span class="font-medium">{% trans "Error!" %}</span> {% trans "Please correct the errors below." %}
            </div>
            {% endif %}
        </form>
    </div>
</div>
{% endblock %} 