{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "لوحة التحكم الرئيسية" %} | {% trans "نظام Aftersails" %}{% endblock %}

{% block extra_css %}
<style>
  .dashboard-widget {
    transition: all 0.3s ease;
  }
  .dashboard-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  .metric-value {
    direction: ltr;
    display: inline-block;
    font-family: 'Ta<PERSON>wal', sans-serif;
    text-align: start;
  }
  .stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
  }
  .stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
  }
  .app-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
  }
  @media (min-width: 768px) {
    .app-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
  }
  @media (min-width: 1024px) {
    .app-grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .app-card {
      flex: 1;
      min-width: calc(20% - 0.8rem);
      max-width: calc(20% - 0.8rem);
    }
  }
  .app-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
  }
  .app-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
  }
  .supply-chain-card {
    position: relative;
    overflow: hidden;
  }
  .supply-chain-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    border-radius: 50%;
    transform: translate(20px, -20px);
  }
  .supply-chain-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(59, 130, 246, 0.25);
    border: 2px solid #3b82f6;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  .analytics-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
  }
  .filter-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
  }
  .alert-card {
    border-left: 4px solid;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
  }
  .alert-warning {
    border-left-color: #f59e0b;
    background-color: #fef3c7;
  }
  .alert-danger {
    border-left-color: #ef4444;
    background-color: #fee2e2;
  }
  .alert-info {
    border-left-color: #3b82f6;
    background-color: #dbeafe;
  }
  .chart-container {
    height: 300px;
    position: relative;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Filter Section -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
            <div>
                <select name="service_center" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                    <option value="">{% trans "جميع مراكز الخدمة" %}</option>
                    {% for center in service_centers %}
                        <option value="{{ center.id }}" {% if current_service_center == center.id|stringformat:"s" %}selected{% endif %}>
                            {{ center.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <input type="date" name="date_from" value="{{ current_date_from }}" 
                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
            </div>
            <div>
                <input type="date" name="date_to" value="{{ current_date_to }}" 
                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
            </div>
            <div>
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors">
                    <i class="fas fa-search mr-1"></i>{% trans "تصفية" %}
                </button>
            </div>
        </form>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Work Orders -->
        <div class="stats-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-white/80 text-sm">{% trans "أوامر العمل" %}</p>
                    <p class="text-2xl font-bold metric-value">{{ work_orders_count|default:0 }}</p>
                    <p class="text-white/60 text-xs">{% trans "نشط:" %} {{ active_work_orders_count|default:0 }}</p>
                </div>
                <i class="fas fa-tasks text-3xl text-white/30"></i>
            </div>
        </div>

        <!-- Inventory -->
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-white/80 text-sm">{% trans "عناصر المخزون" %}</p>
                    <p class="text-2xl font-bold metric-value">{{ inventory_count|default:0 }}</p>
                    <p class="text-white/60 text-xs">{% trans "مخزون منخفض:" %} {{ low_stock_count|default:0 }}</p>
                </div>
                <i class="fas fa-warehouse text-3xl text-white/30"></i>
            </div>
        </div>

        <!-- Sales -->
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-white/80 text-sm">{% trans "المبيعات الشهرية" %}</p>
                    <p class="text-2xl font-bold metric-value">{{ monthly_sales_amount|floatformat:0|default:0 }}</p>
                    <p class="text-white/60 text-xs">{% trans "العملاء:" %} {{ customers_count|default:0 }}</p>
                </div>
                <i class="fas fa-chart-line text-3xl text-white/30"></i>
            </div>
        </div>

        <!-- Warehouse -->
        <div class="stats-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-white/80 text-sm">{% trans "المستودعات" %}</p>
                    <p class="text-2xl font-bold metric-value">{{ warehouses_count|default:0 }}</p>
                    <p class="text-white/60 text-xs">{% trans "الاستخدام:" %} {{ warehouse_utilization|default:0 }}%</p>
                </div>
                <i class="fas fa-boxes text-3xl text-white/30"></i>
            </div>
        </div>
    </div>

    <!-- Alerts Section
    {% if alerts %}
    <div class="analytics-card">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
            <i class="fas fa-exclamation-triangle text-yellow-600 ml-2"></i>
            {% trans "تنبيهات النظام" %}
        </h3>
        <div class="space-y-3">
            {% for alert in alerts %}
            <div class="alert-card alert-{{ alert.level }}">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        {% if alert.level == 'warning' %}
                        <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                        {% elif alert.level == 'danger' %}
                        <i class="fas fa-exclamation-circle text-red-600"></i>
                        {% else %}
                        <i class="fas fa-info-circle text-blue-600"></i>
                        {% endif %}
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium">{{ alert.title }}</h4>
                        <p class="text-sm text-gray-600">{{ alert.message }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ alert.timestamp|timesince }} {% trans "منذ" %}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %} -->

    <!-- Analytics Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Stranded Items Analytics -->
        <div class="analytics-card">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fas fa-clock text-orange-600 ml-2"></i>
                {% trans "العناصر المتوقفة في المستودع" %}
            </h3>
            <div class="chart-container" style="height: 300px; position: relative;">
                <canvas id="stranded-items-chart"></canvas>
                <div id="stranded-items-loading" class="absolute inset-0 flex items-center justify-center text-gray-500">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin text-4xl mb-2"></i>
                        <p>{% trans "جاري تحميل البيانات..." %}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Work Order Status Distribution -->
        <div class="analytics-card">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fas fa-pie-chart text-blue-600 ml-2"></i>
                {% trans "توزيع حالات أوامر العمل" %}
            </h3>
            <div class="chart-container" style="height: 300px; position: relative;">
                <canvas id="work-order-status-chart"></canvas>
                <div id="work-order-status-loading" class="absolute inset-0 flex items-center justify-center text-gray-500">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin text-4xl mb-2"></i>
                        <p>{% trans "جاري تحميل البيانات..." %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Grid -->
    <div class="analytics-card">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
            <i class="fas fa-th-large text-purple-600 ml-2"></i>
            {% trans "تطبيقات النظام" %}
        </h3>
        <div class="app-grid">
            <!-- Supply Chain Management (Combined) -->
            {% if user.is_superuser or primary_role.can_access_inventory or primary_role.can_access_purchases or primary_role.can_access_warehouse %}
            <a href="{% url 'core:supply_chain_dashboard' %}" class="app-card supply-chain-card">
                <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-cubes text-white text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900">
                            {% trans "إدارة سلسلة التوريد" %}
                        </h4>
                        <p class="text-sm text-gray-600">{% trans "المخزون • المشتريات • المستودعات" %}</p>
                    </div>
                </div>
                <div class="grid grid-cols-3 gap-2 text-sm">
                    <div class="text-center p-2 bg-blue-50 rounded border border-blue-200">
                        <div class="font-semibold text-blue-600 metric-value">{{ inventory_count|default:0 }}</div>
                        <div class="text-gray-600 text-xs">{% trans "الأصناف" %}</div>
                    </div>
                    <div class="text-center p-2 bg-purple-50 rounded border border-purple-200">
                        <div class="font-semibold text-purple-600 metric-value">{{ purchases_count|default:0 }}</div>
                        <div class="text-gray-600 text-xs">{% trans "المشتريات" %}</div>
                    </div>
                    <div class="text-center p-2 bg-yellow-50 rounded border border-yellow-200">
                        <div class="font-semibold text-yellow-600 metric-value">{{ warehouses_count|default:0 }}</div>
                        <div class="text-gray-600 text-xs">{% trans "المستودعات" %}</div>
                    </div>
                </div>
            </a>
            {% endif %}



            <!-- Work Orders App -->
            {% if user.is_superuser or primary_role.can_access_work_orders %}
            <a href="{% url 'work_orders:work_order_list' %}" class="app-card group hover:border-blue-300 hover:bg-blue-50 transition-all duration-300">
                <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center ml-3 group-hover:bg-blue-200">
                        <i class="fas fa-tasks text-indigo-600 text-xl group-hover:text-blue-700"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 group-hover:text-blue-800">{% trans "أوامر العمل" %}</h4>
                        <p class="text-sm text-gray-600 group-hover:text-blue-600">{% trans "إدارة الصيانة والإصلاح" %}</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <div class="font-semibold text-indigo-600 metric-value">{{ active_work_orders_count|default:0 }}</div>
                        <div class="text-gray-600">{% trans "نشط" %}</div>
                    </div>
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <div class="font-semibold text-green-600 metric-value">{{ completed_work_orders_count|default:0 }}</div>
                        <div class="text-gray-600">{% trans "مكتمل" %}</div>
                    </div>
                </div>
            </a>
            {% endif %}

            <!-- Sales App -->
            {% if user.is_superuser or primary_role.can_access_sales %}
            <a href="{% url 'sales:dashboard' %}" class="app-card group hover:border-blue-300 hover:bg-blue-50 transition-all duration-300">
                <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-3 group-hover:bg-blue-200">
                        <i class="fas fa-shopping-cart text-green-600 text-xl group-hover:text-blue-700"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 group-hover:text-blue-800">{% trans "إدارة المبيعات" %}</h4>
                        <p class="text-sm text-gray-600 group-hover:text-blue-600">{% trans "المبيعات والفواتير" %}</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <div class="font-semibold text-green-600 metric-value">{{ sales_count|default:0 }}</div>
                        <div class="text-gray-600">{% trans "الطلبات" %}</div>
                    </div>
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <div class="font-semibold text-blue-600 metric-value">{{ customers_count|default:0 }}</div>
                        <div class="text-gray-600">{% trans "العملاء" %}</div>
                    </div>
                </div>
            </a>
            {% endif %}



            <!-- Reports App -->
            {% if user.is_superuser or primary_role.can_access_reports %}
            <a href="{% url 'reports:report_list' %}" class="app-card group hover:border-blue-300 hover:bg-blue-50 transition-all duration-300">
                <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center ml-3 group-hover:bg-blue-200">
                        <i class="fas fa-chart-bar text-red-600 text-xl group-hover:text-blue-700"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 group-hover:text-blue-800">{% trans "التقارير والتحليلات" %}</h4>
                        <p class="text-sm text-gray-600 group-hover:text-blue-600">{% trans "تقارير مفصلة ولوحات تحكم" %}</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <div class="font-semibold text-red-600 metric-value">{{ reports_count|default:0 }}</div>
                        <div class="text-gray-600">{% trans "التقارير" %}</div>
                    </div>
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <div class="font-semibold text-blue-600 metric-value">{{ dashboards_count|default:0 }}</div>
                        <div class="text-gray-600">{% trans "لوحات التحكم" %}</div>
                    </div>
                </div>
            </a>
            {% endif %}

            <!-- Setup App -->
            {% if user.is_superuser or primary_role.can_access_setup %}
            <a href="{% url 'setup:dashboard' %}" class="app-card group hover:border-blue-300 hover:bg-blue-50 transition-all duration-300">
                <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center ml-3 group-hover:bg-blue-200">
                        <i class="fas fa-cogs text-gray-600 text-xl group-hover:text-blue-700"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 group-hover:text-blue-800">{% trans "الإعدادات" %}</h4>
                        <p class="text-sm text-gray-600 group-hover:text-blue-600">{% trans "إعداد النظام والبيانات الأساسية" %}</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <div class="font-semibold text-gray-600 metric-value">{{ service_centers_count|default:0 }}</div>
                        <div class="text-gray-600">{% trans "مراكز الخدمة" %}</div>
                    </div>
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <div class="font-semibold text-purple-600 metric-value">{{ franchises_count|default:0 }}</div>
                        <div class="text-gray-600">{% trans "الامتيازات" %}</div>
                    </div>
                </div>
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Recent Activities -->
    {% if recent_activities %}
    <div class="analytics-card">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
            <i class="fas fa-history text-green-600 ml-2"></i>
            {% trans "الأنشطة الحديثة" %}
        </h3>
        <div class="space-y-3">
            {% for activity in recent_activities %}
            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                    {% if activity.type == 'work_order' %}
                    <i class="fas fa-tasks text-blue-600 text-sm"></i>
                    {% elif activity.type == 'inventory' %}
                    <i class="fas fa-warehouse text-green-600 text-sm"></i>
                    {% elif activity.type == 'purchase' %}
                    <i class="fas fa-shopping-basket text-purple-600 text-sm"></i>
                    {% else %}
                    <i class="fas fa-circle text-gray-600 text-sm"></i>
                    {% endif %}
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-900">{{ activity.description }}</p>
                    <p class="text-xs text-gray-500">{{ activity.timestamp|timesince }} {% trans "منذ" %}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js for analytics -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'js/work_order_status.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load stranded items analytics
    loadStrandedItemsChart();
    
    // Load work order status chart
    loadWorkOrderStatusChart();
});

function loadStrandedItemsChart() {
    console.log('Loading stranded items chart...');
    
    // Fetch stranded items data
    fetch('{% url "core:analytics_stranded_items" %}')
        .then(response => {
            console.log('Stranded items response status:', response.status);
            if (!response.ok) {
                throw new Error('HTTP ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('Stranded items data:', data);
            
            // Hide loading indicator
            const loadingDiv = document.getElementById('stranded-items-loading');
            if (loadingDiv) loadingDiv.style.display = 'none';
            
            const ctx = document.getElementById('stranded-items-chart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            label: '{% trans "أيام في المستودع" %}',
                            data: data.values || [],
                            backgroundColor: 'rgba(249, 115, 22, 0.8)',
                            borderColor: 'rgba(249, 115, 22, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '{% trans "عدد الأيام" %}'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '{% trans "العناصر" %}'
                                }
                            }
                        }
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error loading stranded items chart:', error);
            const loadingDiv = document.getElementById('stranded-items-loading');
            if (loadingDiv) {
                loadingDiv.innerHTML = '<div class="text-center"><i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-2"></i><p class="text-red-500">{% trans "خطأ في تحميل البيانات" %}<br><small>' + error.message + '</small></p></div>';
            }
        });
}

function loadWorkOrderStatusChart() {
    console.log('Loading work order status chart...');
    
    // Fetch work order status data
    fetch('{% url "core:analytics_work_order_status" %}')
        .then(response => {
            console.log('Work order status response status:', response.status);
            if (!response.ok) {
                throw new Error('HTTP ' + response.status);
            }
            return response.json();
        })  
        .then(data => {
            console.log('Work order status data:', data);
            
            // Hide loading indicator
            const loadingDiv = document.getElementById('work-order-status-loading');
            if (loadingDiv) loadingDiv.style.display = 'none';
            
            const ctx = document.getElementById('work-order-status-chart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            data: data.values || [],
                            backgroundColor: [
                                '#6B7280', // planned - gray
                                '#3B82F6', // in_progress - blue
                                '#F59E0B', // on_hold - yellow
                                '#10B981', // completed - green
                                '#EF4444', // cancelled - red
                                '#8B5CF6'  // additional - purple
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error loading work order status chart:', error);
            const loadingDiv = document.getElementById('work-order-status-loading');
            if (loadingDiv) {
                loadingDiv.innerHTML = '<div class="text-center"><i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-2"></i><p class="text-red-500">{% trans "خطأ في تحميل البيانات" %}<br><small>' + error.message + '</small></p></div>';
            }
        });
}
</script>
{% endblock %} 