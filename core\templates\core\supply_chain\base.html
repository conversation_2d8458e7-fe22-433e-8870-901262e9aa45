{% extends 'core/dashboard_base.html' %}
{% load static %}
{% load i18n %}

{% block extra_css %}
{{ block.super }}
<style>
/* Compact Supply Chain Styles */
.supply-chain-nav {
    background: transparent;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: none;
}

.supply-chain-nav .nav-title {
    color: white;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
}

.supply-chain-breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 8px 15px;
    margin-bottom: 15px;
}

.supply-chain-breadcrumb .breadcrumb {
    margin: 0;
    background: transparent;
    padding: 0;
    font-size: 0.85rem;
}

.supply-chain-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.supply-chain-breadcrumb .breadcrumb-item.active {
    color: white;
    font-weight: 500;
}

.hub-navigation {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.hub-card {
    background: transparent;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    box-shadow: none;
}

.hub-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
    background: rgba(255, 255, 255, 0.1);
}

.hub-card .icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.hub-card .title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.hub-card .description {
    color: #718096;
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 15px;
}

.hub-card .stats {
    display: flex;
    justify-content: space-around;
    margin-top: 15px;
    padding-top: 15px;
    border-top: none;
}

.hub-card .stat .number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #667eea;
}

.hub-card .stat .label {
    font-size: 0.7rem;
    color: #718096;
    margin-top: 3px;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.action-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.35);
    color: white;
    text-decoration: none;
}

.action-btn i {
    margin-right: 8px;
    font-size: 0.9rem;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    box-shadow: 0 2px 8px rgba(72, 187, 120, 0.25);
}

.action-btn.warning {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    box-shadow: 0 2px 8px rgba(237, 137, 54, 0.25);
}

.back-button {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    border-radius: 8px;
    padding: 8px 15px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-weight: 500;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.back-button:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    color: #2d3748;
    text-decoration: none;
}

.back-button i {
    margin-right: 6px;
}

.page-header {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.page-header .title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
}

.page-header .subtitle {
    color: #718096;
    font-size: 0.9rem;
}

.content-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.content-section h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.quick-stat {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.quick-stat .number {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.quick-stat .label {
    font-size: 0.75rem;
    opacity: 0.9;
}

.alert-badge {
    background: #fed7d7;
    color: #c53030;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    margin-left: 8px;
}

.success-badge {
    background: #c6f6d5;
    color: #276749;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    margin-left: 8px;
}

/* Arabic RTL Support */
[dir="rtl"] .action-btn i {
    margin-right: 0;
    margin-left: 8px;
}

[dir="rtl"] .back-button i {
    margin-right: 0;
    margin-left: 6px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .hub-navigation {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .quick-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .supply-chain-nav .nav-title {
        font-size: 1.2rem;
    }
    
    .page-header .title {
        font-size: 1.3rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Compact Supply Chain Navigation -->
    <div class="supply-chain-nav">
        

        <!-- Compact Action Buttons -->
        <div class="action-buttons">
            <a href="{% url 'core:supply_chain_dashboard' %}" class="action-btn">
                <i class="fas fa-tachometer-alt"></i> {% trans "الرئيسية" %}
            </a>
            <a href="{% url 'core:supply_chain_inventory_hub' %}" class="action-btn secondary">
                <i class="fas fa-boxes"></i> {% trans "المخزون" %}
            </a>
            <a href="{% url 'core:supply_chain_warehouse_hub' %}" class="action-btn">
                <i class="fas fa-warehouse"></i> {% trans "المستودعات" %}
            </a>
            <a href="{% url 'core:supply_chain_purchase_hub' %}" class="action-btn warning">
                <i class="fas fa-shopping-cart"></i> {% trans "المشتريات" %}
            </a>
        </div>
    </div>

   

  

    <!-- Page Content -->
    <div class="page-content">
        {% block supply_chain_content %}{% endblock %}
    </div>

    <!-- Compact Quick Actions -->
    <div class="content-section">
        <h5><i class="fas fa-bolt"></i> {% trans "إجراءات سريعة" %}</h5>
        <div class="action-buttons">
            <a href="{% url 'core:supply_chain_low_stock' %}" class="action-btn warning">
                <i class="fas fa-exclamation-triangle"></i> {% trans "مخزون منخفض" %}
            </a>
            <a href="{% url 'core:supply_chain_stock_adjustment' %}" class="action-btn">
                <i class="fas fa-edit"></i> {% trans "تعديل مخزون" %}
            </a>
            <a href="{% url 'core:supply_chain_emergency_reorder' %}" class="action-btn warning">
                <i class="fas fa-redo"></i> {% trans "طلب طارئ" %}
            </a>
            <a href="{% url 'core:supply_chain_reports' %}" class="action-btn secondary">
                <i class="fas fa-chart-bar"></i> {% trans "التقارير" %}
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Compact animations
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';
                setTimeout(() => {
                    icon.className = originalClass;
                }, 1500);
            }
        });
    });
});

function showSuccessMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'alert alert-success position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
    toast.innerHTML = `${message}<button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>`;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
}

function showErrorMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'alert alert-danger position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
    toast.innerHTML = `${message}<button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>`;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
}
</script>
{% endblock %} 