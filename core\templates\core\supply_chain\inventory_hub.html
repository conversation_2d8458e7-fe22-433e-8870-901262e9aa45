{% extends 'core/supply_chain/base.html' %}
{% load static %}
{% load i18n %}

{% block extra_css %}
{{ block.super }}
<style>
.hub-card {
    background: none;
    border: none;
    text-decoration: none;
    color: inherit;
    width: 100%;
    text-align: inherit;
}

.hub-card:hover {
    transform: translateY(-5px);
    color: inherit;
}

.hub-card:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}
</style>
{% endblock %}

{% block page_title %}المخزون{% endblock %}
{% block page_subtitle %}إدارة الأصناف والكميات{% endblock %}

{% block page_badges %}
{% if low_stock_count > 0 %}
<span class="alert-badge">{{ low_stock_count }} {% trans "مخزون منخفض" %}</span>
{% endif %}
{% if out_of_stock_count > 0 %}
<span class="alert-badge">{{ out_of_stock_count }} {% trans "نفد المخزون" %}</span>
{% endif %}
{% endblock %}

{% block supply_chain_content %}
<!-- Quick Inventory Stats -->
<div class="quick-stats">
    <div class="quick-stat">
        <div class="number">1,248</div>
        <div class="label">الأصناف</div>
    </div>
    <div class="quick-stat">
        <div class="number">23</div>
        <div class="label">منخفض</div>
    </div>
    <div class="quick-stat">
        <div class="number">156</div>
        <div class="label">فئات</div>
    </div>
    <div class="quick-stat">
        <div class="number">98.5%</div>
        <div class="label">دقة</div>
    </div>
</div>

<!-- Compact Action Buttons -->
<div class="hub-navigation">
    <button type="button" class="hub-card" data-action="show-items">
        <div class="icon">
            <i class="fas fa-list"></i>
        </div>
        <div class="title">قائمة الأصناف</div>
        <div class="description">عرض وإدارة جميع الأصناف</div>
        <div class="stats">
            <div class="stat">
                <div class="number" data-stat="total_items">1,248</div>
                <div class="label">صنف</div>
            </div>
            <div class="stat">
                <div class="number" data-stat="active_items">856</div>
                <div class="label">نشط</div>
            </div>
        </div>
    </button>

    <button type="button" class="hub-card" data-action="add-item">
        <div class="icon">
            <i class="fas fa-plus-circle"></i>
        </div>
        <div class="title">إضافة صنف</div>
        <div class="description">إضافة صنف جديد للمخزون</div>
    </button>

    <a href="{% url 'core:supply_chain_categories' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-tags"></i>
        </div>
        <div class="title">الفئات</div>
        <div class="description">إدارة فئات الأصناف</div>
        <div class="stats">
            <div class="stat">
                <div class="number">156</div>
                <div class="label">فئة</div>
            </div>
            <div class="stat">
                <div class="number">12</div>
                <div class="label">رئيسية</div>
            </div>
        </div>
    </a>

    <button type="button" class="hub-card" data-action="show-movements">
        <div class="icon">
            <i class="fas fa-exchange-alt"></i>
        </div>
        <div class="title">حركات المخزون</div>
        <div class="description">تتبع حركات الدخول والخروج</div>
        <div class="stats">
            <div class="stat">
                <div class="number" data-stat="total_movements">1,543</div>
                <div class="label">حركة</div>
            </div>
            <div class="stat">
                <div class="number" data-stat="today_movements">45</div>
                <div class="label">اليوم</div>
            </div>
        </div>
    </button>

    <button type="button" class="hub-card" data-action="show-low-stock">
        <div class="icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="title">مخزون منخفض</div>
        <div class="description">أصناف تحتاج إعادة طلب</div>
        <div class="stats">
            <div class="stat">
                <div class="number" data-stat="low_stock_count" data-low-stock-count>23</div>
                <div class="label">صنف</div>
            </div>
            <div class="stat">
                <div class="number" data-stat="critical_stock_count">5</div>
                <div class="label">عاجل</div>
            </div>
        </div>
    </button>

    <button type="button" class="hub-card" data-action="stock-adjustment">
        <div class="icon">
            <i class="fas fa-edit"></i>
        </div>
        <div class="title">تعديل مخزون</div>
        <div class="description">تعديل كميات المخزون</div>
    </button>
</div>

<!-- Recent Activities -->
<div class="content-section">
    <h5><i class="fas fa-clock"></i> آخر الحركات</h5>
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>النوع</th>
                    <th>الكمية</th>
                    <th>التاريخ</th>
                    <th>المستخدم</th>
                </tr>
            </thead>
            <tbody>
                {% if recent_movements %}
                    {% for movement in recent_movements %}
                    <tr>
                        <td>
                            <strong>{{ movement.item.name }}</strong><br>
                            <small class="text-muted">الفئة: {{ movement.item.classification.name|default:"غير محدد" }}</small>
                        </td>
                        <td>
                            {% if movement.movement_type == 'in' %}
                                <span class="badge bg-success">دخول</span>
                            {% elif movement.movement_type == 'out' %}
                                <span class="badge bg-danger">خروج</span>
                            {% else %}
                                <span class="badge bg-warning">تعديل</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if movement.movement_type == 'in' %}
                                +{{ movement.quantity }}
                            {% elif movement.movement_type == 'out' %}
                                -{{ movement.quantity }}
                            {% else %}
                                تصحيح
                            {% endif %}
                        </td>
                        <td>{{ movement.created_at|timesince }} مضت</td>
                        <td>{{ movement.created_by.get_full_name|default:"مستخدم النظام" }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <!-- No movements found -->
                    <tr>
                        <td colspan="5" class="text-center text-muted">
                            <i class="fas fa-info-circle"></i>
                            لا توجد حركات مخزون حديثة
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
    <div class="text-center">
        <a href="{% url 'core:supply_chain_movements' %}" class="btn btn-outline-primary">
            عرض جميع الحركات
        </a>
    </div>
</div>

<!-- Quick Alerts -->
<div class="content-section">
    <h5><i class="fas fa-bell"></i> تنبيهات</h5>
    <div class="row">
        <div class="col-md-6">
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> مخزون منخفض</h6>
                <p>23 صنف وصل للحد الأدنى ويحتاج إعادة طلب</p>
                <a href="{% url 'core:supply_chain_low_stock' %}" class="btn btn-sm btn-warning">عرض القائمة</a>
            </div>
        </div>
        <div class="col-md-6">
            <div class="alert alert-info">
                <h6><i class="fas fa-chart-bar"></i> تقرير المخزون</h6>
                <p>تقرير المخزون الشهري جاهز للمراجعة</p>
                <a href="{% url 'core:supply_chain_inventory_report' %}" class="btn btn-sm btn-info">عرض التقرير</a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="content-section">
    <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
    <div class="row">
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-primary w-100 mb-2" data-action="add-movement">
                <i class="fas fa-plus-circle"></i><br>
                تسجيل حركة
            </button>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-success w-100 mb-2" data-action="add-location">
                <i class="fas fa-map-marker-alt"></i><br>
                إضافة موقع
            </button>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-info w-100 mb-2" data-action="show-locations">
                <i class="fas fa-warehouse"></i><br>
                عرض المواقع
            </button>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-warning w-100 mb-2" data-action="add-supplier">
                <i class="fas fa-truck"></i><br>
                إضافة مورد
            </button>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-secondary w-100 mb-2" data-action="show-suppliers">
                <i class="fas fa-users"></i><br>
                عرض الموردين
            </button>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger w-100 mb-2" data-action="item-locations">
                <i class="fas fa-search-location"></i><br>
                تتبع المواضع
            </button>
        </div>
    </div>
</div>

<!-- Inventory Chart -->
<div class="content-section">
    <h5><i class="fas fa-chart-line"></i> إحصائيات المخزون</h5>
    <div class="row">
        <div class="col-md-8">
            <canvas id="inventoryChart" width="400" height="200"></canvas>
        </div>
        <div class="col-md-4">
            <div class="list-group">
                <div class="list-group-item d-flex justify-content-between">
                    <span>أصناف نشطة</span>
                    <strong>856</strong>
                </div>
                <div class="list-group-item d-flex justify-content-between">
                    <span>أصناف غير نشطة</span>
                    <strong>392</strong>
                </div>
                <div class="list-group-item d-flex justify-content-between">
                    <span>متوسط قيمة الصنف</span>
                    <strong>2,450 ج.م</strong>
                </div>
                <div class="list-group-item d-flex justify-content-between">
                    <span>إجمالي قيمة المخزون</span>
                    <strong>3,057,600 ج.م</strong>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'js/supply_chain.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inventory movements chart
    const ctx = document.getElementById('inventoryChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'دخول',
                data: [120, 150, 180, 90, 200, 170],
                backgroundColor: '#48bb78'
            }, {
                label: 'خروج',
                data: [80, 120, 140, 110, 160, 130],
                backgroundColor: '#f56565'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
{% endblock %} 