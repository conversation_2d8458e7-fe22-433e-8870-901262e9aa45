{% extends 'core/supply_chain/base.html' %}
{% load static %}
{% load i18n %}

{% block page_title %}قائمة الأصناف{% endblock %}
{% block page_subtitle %}عرض وإدارة جميع الأصناف{% endblock %}

{% block supply_chain_content %}
<!-- Quick Search and Filters -->
<div class="content-section">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" id="itemSearch" class="form-control" placeholder="البحث في الأصناف...">
                <button class="btn btn-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-md-6">
            <div class="d-flex gap-2">
                <select class="form-select" id="categoryFilter">
                    <option value="">جميع الفئات</option>
                    <option value="auto_parts">قطع السيارات</option>
                    <option value="oils">زيوت ومواد</option>
                    <option value="filters">فلاتر</option>
                </select>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="low_stock">مخزون منخفض</option>
                    <option value="out_of_stock">نفد المخزون</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Compact Action Buttons -->
<div class="content-section">
    <div class="d-flex justify-content-between align-items-center">
        <div class="d-flex gap-2">
            <button class="btn btn-primary" onclick="bulkActions()">
                <i class="fas fa-check-square"></i> إجراءات مجمعة
            </button>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#quickStockModal">
                <i class="fas fa-edit"></i> تعديل سريع
            </button>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="exportItems()">
                <i class="fas fa-download"></i> تصدير
            </button>
            <a href="{% url 'core:supply_chain_item_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة صنف
            </a>
        </div>
    </div>
</div>

<!-- Items Grid/List -->
<div class="content-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h5><i class="fas fa-boxes"></i> الأصناف (1,248)</h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary active" id="gridView">
                <i class="fas fa-th"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" id="listView">
                <i class="fas fa-list"></i>
            </button>
        </div>
    </div>

    <!-- Grid View (Default) -->
    <div id="itemsGrid" class="row">
        <div class="col-md-3 mb-3">
            <div class="card item-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <input type="checkbox" class="form-check-input item-checkbox">
                        <span class="badge bg-success">نشط</span>
                    </div>
                    <h6 class="card-title">قطعة غيار ABC123</h6>
                    <p class="card-text text-muted small">قطع السيارات</p>
                    <div class="item-stats">
                        <div class="d-flex justify-content-between">
                            <span>المخزون:</span>
                            <strong class="text-success">150</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>السعر:</span>
                            <strong>250 ج.م</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>الحد الأدنى:</span>
                            <strong>20</strong>
                        </div>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" onclick="quickAdjust('ABC123')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card item-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <input type="checkbox" class="form-check-input item-checkbox">
                        <span class="badge bg-warning">منخفض</span>
                    </div>
                    <h6 class="card-title">زيت محرك 5W30</h6>
                    <p class="card-text text-muted small">زيوت ومواد</p>
                    <div class="item-stats">
                        <div class="d-flex justify-content-between">
                            <span>المخزون:</span>
                            <strong class="text-warning">8</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>السعر:</span>
                            <strong>180 ج.م</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>الحد الأدنى:</span>
                            <strong>15</strong>
                        </div>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" onclick="quickAdjust('5W30')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card item-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <input type="checkbox" class="form-check-input item-checkbox">
                        <span class="badge bg-danger">نفد</span>
                    </div>
                    <h6 class="card-title">فلتر هواء DEF456</h6>
                    <p class="card-text text-muted small">فلاتر</p>
                    <div class="item-stats">
                        <div class="d-flex justify-content-between">
                            <span>المخزون:</span>
                            <strong class="text-danger">0</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>السعر:</span>
                            <strong>95 ج.م</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>الحد الأدنى:</span>
                            <strong>10</strong>
                        </div>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" onclick="quickAdjust('DEF456')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card item-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <input type="checkbox" class="form-check-input item-checkbox">
                        <span class="badge bg-success">نشط</span>
                    </div>
                    <h6 class="card-title">إطار 195/65R15</h6>
                    <p class="card-text text-muted small">إطارات</p>
                    <div class="item-stats">
                        <div class="d-flex justify-content-between">
                            <span>المخزون:</span>
                            <strong class="text-success">45</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>السعر:</span>
                            <strong>850 ج.م</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>الحد الأدنى:</span>
                            <strong>5</strong>
                        </div>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" onclick="quickAdjust('195/65R15')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- List View (Hidden by default) -->
    <div id="itemsList" style="display: none;">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAll"></th>
                        <th>الصنف</th>
                        <th>الفئة</th>
                        <th>المخزون</th>
                        <th>السعر</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox" class="item-checkbox"></td>
                        <td>
                            <strong>قطعة غيار ABC123</strong><br>
                            <small class="text-muted">SKU: ABC123</small>
                        </td>
                        <td>قطع السيارات</td>
                        <td><span class="text-success">150</span></td>
                        <td>250 ج.م</td>
                        <td><span class="badge bg-success">نشط</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="item-checkbox"></td>
                        <td>
                            <strong>زيت محرك 5W30</strong><br>
                            <small class="text-muted">SKU: 5W30</small>
                        </td>
                        <td>زيوت ومواد</td>
                        <td><span class="text-warning">8</span></td>
                        <td>180 ج.م</td>
                        <td><span class="badge bg-warning">منخفض</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <nav aria-label="صفحات الأصناف">
        <ul class="pagination justify-content-center">
            <li class="page-item disabled">
                <span class="page-link">السابق</span>
            </li>
            <li class="page-item active">
                <span class="page-link">1</span>
            </li>
            <li class="page-item">
                <a class="page-link" href="#">2</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#">3</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#">التالي</a>
            </li>
        </ul>
    </nav>
</div>

<!-- Quick Stock Adjustment Modal -->
<div class="modal fade" id="quickStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل مخزون سريع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">الصنف</label>
                    <select class="form-select" id="quickItemSelect">
                        <option>اختر الصنف...</option>
                        <option value="ABC123">قطعة غيار ABC123</option>
                        <option value="5W30">زيت محرك 5W30</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">نوع التعديل</label>
                    <select class="form-select" id="adjustmentType">
                        <option value="add">إضافة</option>
                        <option value="subtract">خصم</option>
                        <option value="set">تحديد</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">الكمية</label>
                    <input type="number" class="form-control" id="adjustmentQuantity">
                </div>
                <div class="mb-3">
                    <label class="form-label">السبب</label>
                    <textarea class="form-control" id="adjustmentReason" rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="applyStockAdjustment()">تطبيق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
.item-card {
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.item-stats {
    font-size: 0.85rem;
    border-top: 1px solid #e2e8f0;
    padding-top: 10px;
    margin-top: 10px;
}

.item-stats > div {
    margin-bottom: 5px;
}

.btn-group .btn {
    padding: 5px 10px;
}

.search-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View toggle
    document.getElementById('gridView').addEventListener('click', function() {
        document.getElementById('itemsGrid').style.display = 'block';
        document.getElementById('itemsList').style.display = 'none';
        this.classList.add('active');
        document.getElementById('listView').classList.remove('active');
    });

    document.getElementById('listView').addEventListener('click', function() {
        document.getElementById('itemsGrid').style.display = 'none';
        document.getElementById('itemsList').style.display = 'block';
        this.classList.add('active');
        document.getElementById('gridView').classList.remove('active');
    });

    // Search functionality
    document.getElementById('itemSearch').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        // Here you would implement search logic
        console.log('Searching for:', searchTerm);
    });

    // Select all checkbox
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        checkboxes.forEach(cb => cb.checked = this.checked);
    });
});

function quickAdjust(itemCode) {
    document.getElementById('quickItemSelect').value = itemCode;
    new bootstrap.Modal(document.getElementById('quickStockModal')).show();
}

function bulkActions() {
    const selected = document.querySelectorAll('.item-checkbox:checked');
    if (selected.length === 0) {
        showErrorMessage('الرجاء اختيار صنف واحد على الأقل');
        return;
    }
    showSuccessMessage(`تم اختيار ${selected.length} صنف للإجراءات المجمعة`);
}

function exportItems() {
    showSuccessMessage('جاري تصدير قائمة الأصناف...');
    // Export logic here
}

function applyStockAdjustment() {
    const item = document.getElementById('quickItemSelect').value;
    const type = document.getElementById('adjustmentType').value;
    const quantity = document.getElementById('adjustmentQuantity').value;
    const reason = document.getElementById('adjustmentReason').value;

    if (!item || !quantity) {
        showErrorMessage('الرجاء ملء جميع الحقول المطلوبة');
        return;
    }

    showSuccessMessage(`تم تعديل مخزون ${item} بنجاح`);
    bootstrap.Modal.getInstance(document.getElementById('quickStockModal')).hide();
    
    // Reset form
    document.getElementById('quickItemSelect').value = '';
    document.getElementById('adjustmentQuantity').value = '';
    document.getElementById('adjustmentReason').value = '';
}
</script>
{% endblock %} 