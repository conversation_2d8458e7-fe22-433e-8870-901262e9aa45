{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "Location Management" %}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">{% trans "Location Management" %}</h1>
                    <p class="text-muted">{% trans "Manage warehouse locations" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="openModal('location-modal')">
                        <i class="fas fa-plus me-2"></i>{% trans "Add Location" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Locations List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Warehouse Locations" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Code" %}</th>
                                    <th>{% trans "Name" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Capacity" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
                                            <p>{% trans "No locations found. Create your first location to get started." %}</p>
                                            <button class="btn btn-primary" onclick="openModal('location-modal')">
                                                <i class="fas fa-plus me-2"></i>{% trans "Add Location" %}
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include location modal -->
{% include 'core/supply_chain/modals/location_modal.html' %}

<script>
// Modal functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}
</script>
{% endblock %} 