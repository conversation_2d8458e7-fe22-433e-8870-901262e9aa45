{% load i18n %}
<!-- Add Item Modal -->
<div id="item-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="modal-header flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">{% trans "Add New Item" %}</h3>
            <button onclick="closeModal('item-modal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="item-form" class="mt-3">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "SKU" %}</label>
                    <input type="text" name="sku" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Name" %}</label>
                    <input type="text" name="name" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Description" %}</label>
                    <textarea name="description" rows="3" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Category" %}</label>
                    <select name="category" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{% trans "Select Category" %}</option>
                        <option value="part">{% trans "Part" %}</option>
                        <option value="consumable">{% trans "Consumable" %}</option>
                        <option value="tool">{% trans "Tool" %}</option>
                        <option value="equipment">{% trans "Equipment" %}</option>
                        <option value="material">{% trans "Material" %}</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Unit Price" %}</label>
                    <input type="number" name="unit_price" step="0.01" min="0" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Initial Quantity" %}</label>
                    <input type="number" name="quantity" step="0.01" min="0" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Min Stock Level" %}</label>
                    <input type="number" name="min_stock_level" step="0.01" min="0" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
            
            <div class="modal-footer flex justify-end pt-4 mt-4 space-x-3">
                <button type="button" onclick="closeModal('item-modal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    {% trans "Cancel" %}
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    {% trans "Create Item" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('item-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '{% trans "Creating..." %}';
    submitBtn.disabled = true;
    
    fetch('{% url "inventory:item_create" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('item-modal');
            location.reload(); // Refresh to show new item
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating item');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});
</script>