{% load i18n %}
<!-- Location Modal -->
<div id="location-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="modal-header flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">{% trans "Add New Location" %}</h3>
            <button onclick="closeModal('location-modal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="location-form" class="mt-3">
            {% csrf_token %}
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Location Name" %}</label>
                        <input type="text" name="name" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Location Code" %}</label>
                        <input type="text" name="code" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Location Type" %}</label>
                    <select name="location_type" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{% trans "Select Type" %}</option>
                        <option value="warehouse">{% trans "Warehouse" %}</option>
                        <option value="storage">{% trans "Storage Area" %}</option>
                        <option value="shelf">{% trans "Shelf" %}</option>
                        <option value="bin">{% trans "Bin" %}</option>
                        <option value="zone">{% trans "Zone" %}</option>
                        <option value="dock">{% trans "Loading Dock" %}</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Parent Location" %}</label>
                    <select name="parent_location" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{% trans "None (Top Level)" %}</option>
                        <!-- Locations will be loaded via AJAX -->
                    </select>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Capacity" %}</label>
                        <input type="number" name="capacity" step="0.01" min="0" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Unit" %}</label>
                        <select name="capacity_unit" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="items">{% trans "Items" %}</option>
                            <option value="m3">{% trans "Cubic Meters" %}</option>
                            <option value="kg">{% trans "Kilograms" %}</option>
                            <option value="pallets">{% trans "Pallets" %}</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_storage" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">{% trans "Storage Location" %}</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Address" %}</label>
                    <textarea name="address" rows="2" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Description" %}</label>
                    <textarea name="description" rows="3" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_active" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">{% trans "Active" %}</span>
                        </label>
                    </div>
                    <div class="flex items-center">
                        <label class="flex items-center">
                            <input type="checkbox" name="allow_negative_stock" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">{% trans "Allow Negative Stock" %}</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer flex justify-end pt-4 mt-4 space-x-3">
                <button type="button" onclick="closeModal('location-modal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    {% trans "Cancel" %}
                </button>
                <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                    {% trans "Create Location" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadParentLocations();
});

function loadParentLocations() {
    fetch('{% url "warehouse:api_locations_list" %}')
        .then(response => response.json())
        .then(data => {
            const parentSelect = document.querySelector('#location-modal select[name="parent_location"]');
            parentSelect.innerHTML = '<option value="">{% trans "None (Top Level)" %}</option>';
            
            data.locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location.id;
                option.textContent = location.name;
                parentSelect.appendChild(option);
            });
        })
        .catch(error => console.error('Error loading parent locations:', error));
}

document.getElementById('location-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '{% trans "Creating..." %}';
    submitBtn.disabled = true;
    
    fetch('{% url "warehouse:location_create" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('location-modal');
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating location');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});
</script>