{% load i18n %}
<!-- Movement Modal -->
<div id="movement-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="modal-header flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">{% trans "Record Stock Movement" %}</h3>
            <button onclick="closeModal('movement-modal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="movement-form" class="mt-3">
            {% csrf_token %}
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Item" %}</label>
                    <select name="item" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{% trans "Select Item" %}</option>
                        <!-- Items will be loaded via AJAX -->
                    </select>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Movement Type" %}</label>
                        <select name="movement_type" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Type" %}</option>
                            <option value="purchase">{% trans "Purchase" %}</option>
                            <option value="sale">{% trans "Sale" %}</option>
                            <option value="transfer">{% trans "Transfer" %}</option>
                            <option value="adjustment">{% trans "Adjustment" %}</option>
                            <option value="return">{% trans "Return" %}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Quantity" %}</label>
                        <input type="number" name="quantity" step="0.01" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Reference" %}</label>
                    <input type="text" name="reference" placeholder="{% trans 'Order number, invoice, etc.' %}" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Notes" %}</label>
                    <textarea name="notes" rows="3" placeholder="{% trans 'Additional details about this movement...' %}" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
            </div>
            
            <div class="modal-footer flex justify-end pt-4 mt-4 space-x-3">
                <button type="button" onclick="closeModal('movement-modal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    {% trans "Cancel" %}
                </button>
                <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                    {% trans "Record Movement" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Load items when modal opens
document.addEventListener('DOMContentLoaded', function() {
    const itemSelect = document.querySelector('#movement-modal select[name="item"]');
    
    if (itemSelect) {
        // Load items via AJAX
        fetch('{% url "inventory:api_items_list" %}')
            .then(response => response.json())
            .then(data => {
                itemSelect.innerHTML = '<option value="">{% trans "Select Item" %}</option>';
                data.items.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.sku} - ${item.name}`;
                    itemSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading items:', error));
    }
});

document.getElementById('movement-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '{% trans "Recording..." %}';
    submitBtn.disabled = true;
    
    fetch('{% url "inventory:stock_movement_create" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('movement-modal');
            location.reload(); // Refresh to show new movement
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error recording movement');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});
</script>