{% load i18n %}
<!-- Purchase Order Modal -->
<div id="purchase-order-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-5/6 lg:w-4/5 shadow-lg rounded-md bg-white">
        <div class="modal-header flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">{% trans "Create Purchase Order" %}</h3>
            <button onclick="closeModal('purchase-order-modal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="purchase-order-form" class="mt-3">
            {% csrf_token %}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column: Order Details -->
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-gray-900 border-b pb-2">{% trans "Order Information" %}</h4>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Supplier" %}</label>
                        <select name="supplier" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Supplier" %}</option>
                            <!-- Suppliers will be loaded via AJAX -->
                        </select>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Order Date" %}</label>
                            <input type="date" name="order_date" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Expected Delivery" %}</label>
                            <input type="date" name="expected_delivery_date" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Reference Number" %}</label>
                        <input type="text" name="reference_number" placeholder="{% trans 'Auto-generated if left empty' %}" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Priority" %}</label>
                        <select name="priority" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="low">{% trans "Low" %}</option>
                            <option value="normal" selected>{% trans "Normal" %}</option>
                            <option value="high">{% trans "High" %}</option>
                            <option value="urgent">{% trans "Urgent" %}</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Delivery Location" %}</label>
                        <select name="delivery_location" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Location" %}</option>
                            <!-- Locations will be loaded via AJAX -->
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Notes" %}</label>
                        <textarea name="notes" rows="3" placeholder="{% trans 'Additional order details...' %}" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                </div>
                
                <!-- Right Column: Order Items -->
                <div class="space-y-4">
                    <div class="flex justify-between items-center border-b pb-2">
                        <h4 class="text-md font-semibold text-gray-900">{% trans "Order Items" %}</h4>
                        <button type="button" onclick="addOrderItem()" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-plus mr-1"></i>{% trans "Add Item" %}
                        </button>
                    </div>
                    
                    <div id="order-items" class="space-y-3 max-h-96 overflow-y-auto">
                        <div class="order-item border rounded-lg p-3 bg-gray-50">
                            <div class="grid grid-cols-12 gap-2 items-end">
                                <div class="col-span-6">
                                    <label class="block text-xs font-medium text-gray-700 mb-1">{% trans "Item" %}</label>
                                    <select name="items[]" required class="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" onchange="updateItemInfo(this)">
                                        <option value="">{% trans "Select Item" %}</option>
                                    </select>
                                </div>
                                <div class="col-span-2">
                                    <label class="block text-xs font-medium text-gray-700 mb-1">{% trans "Qty" %}</label>
                                    <input type="number" name="quantities[]" step="0.01" min="0" required class="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" onchange="calculateItemTotal(this)">
                                </div>
                                <div class="col-span-2">
                                    <label class="block text-xs font-medium text-gray-700 mb-1">{% trans "Unit Price" %}</label>
                                    <input type="number" name="unit_prices[]" step="0.01" min="0" required class="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" onchange="calculateItemTotal(this)">
                                </div>
                                <div class="col-span-1">
                                    <label class="block text-xs font-medium text-gray-700 mb-1">{% trans "Total" %}</label>
                                    <div class="text-xs text-gray-600 p-2 bg-white rounded border item-total">0.00</div>
                                </div>
                                <div class="col-span-1">
                                    <button type="button" onclick="removeOrderItem(this)" class="text-red-600 hover:text-red-800 p-2">
                                        <i class="fas fa-trash text-xs"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="text" name="item_notes[]" placeholder="{% trans 'Item notes...' %}" class="w-full p-1 text-xs border border-gray-300 rounded-md">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Summary -->
                    <div class="border-t pt-4 mt-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-blue-800">{% trans "Subtotal:" %}</span>
                                <span id="order-subtotal" class="text-sm font-bold text-blue-900">0.00</span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-blue-700">{% trans "Tax:" %}</span>
                                <span id="order-tax" class="text-sm text-blue-800">0.00</span>
                            </div>
                            <div class="flex justify-between items-center border-t border-blue-300 pt-2">
                                <span class="text-base font-bold text-blue-900">{% trans "Total:" %}</span>
                                <span id="order-total" class="text-base font-bold text-blue-900">0.00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer flex justify-end pt-4 mt-6 border-t space-x-3">
                <button type="button" onclick="closeModal('purchase-order-modal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    {% trans "Cancel" %}
                </button>
                <button type="button" onclick="saveDraft()" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                    {% trans "Save as Draft" %}
                </button>
                <button type="submit" class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700">
                    {% trans "Create Order" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
let itemsData = [];
let suppliersData = [];

document.addEventListener('DOMContentLoaded', function() {
    loadSuppliers();
    loadItems();
    loadLocations();
    
    // Set today's date as default
    document.querySelector('input[name="order_date"]').value = new Date().toISOString().split('T')[0];
});

function loadSuppliers() {
    fetch('{% url "purchases:api_suppliers_list" %}')
        .then(response => response.json())
        .then(data => {
            suppliersData = data.suppliers;
            const supplierSelect = document.querySelector('#purchase-order-modal select[name="supplier"]');
            supplierSelect.innerHTML = '<option value="">{% trans "Select Supplier" %}</option>';
            
            data.suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.textContent = supplier.name;
                supplierSelect.appendChild(option);
            });
        })
        .catch(error => console.error('Error loading suppliers:', error));
}

function loadItems() {
    fetch('{% url "inventory:api_items_list" %}')
        .then(response => response.json())
        .then(data => {
            itemsData = data.items;
            updateItemSelects();
        })
        .catch(error => console.error('Error loading items:', error));
}

function loadLocations() {
    fetch('{% url "warehouse:api_locations_list" %}')
        .then(response => response.json())
        .then(data => {
            const locationSelect = document.querySelector('#purchase-order-modal select[name="delivery_location"]');
            locationSelect.innerHTML = '<option value="">{% trans "Select Location" %}</option>';
            
            data.locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location.id;
                option.textContent = location.name;
                locationSelect.appendChild(option);
            });
        })
        .catch(error => console.error('Error loading locations:', error));
}

function updateItemSelects() {
    const itemSelects = document.querySelectorAll('#purchase-order-modal select[name="items[]"]');
    itemSelects.forEach(select => {
        select.innerHTML = '<option value="">{% trans "Select Item" %}</option>';
        itemsData.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.sku} - ${item.name}`;
            option.dataset.unitPrice = item.unit_price;
            select.appendChild(option);
        });
    });
}

function addOrderItem() {
    const container = document.getElementById('order-items');
    const newItem = container.children[0].cloneNode(true);
    
    // Clear values
    newItem.querySelector('select').selectedIndex = 0;
    newItem.querySelectorAll('input').forEach(input => input.value = '');
    newItem.querySelector('.item-total').textContent = '0.00';
    
    container.appendChild(newItem);
    updateItemSelects();
    updateOrderTotal();
}

function removeOrderItem(button) {
    const container = document.getElementById('order-items');
    if (container.children.length > 1) {
        button.closest('.order-item').remove();
        updateOrderTotal();
    }
}

function updateItemInfo(select) {
    const selectedOption = select.selectedOptions[0];
    const orderItem = select.closest('.order-item');
    const unitPriceInput = orderItem.querySelector('input[name="unit_prices[]"]');
    
    if (selectedOption && selectedOption.dataset.unitPrice) {
        unitPriceInput.value = selectedOption.dataset.unitPrice;
        calculateItemTotal(unitPriceInput);
    }
}

function calculateItemTotal(input) {
    const orderItem = input.closest('.order-item');
    const quantity = parseFloat(orderItem.querySelector('input[name="quantities[]"]').value) || 0;
    const unitPrice = parseFloat(orderItem.querySelector('input[name="unit_prices[]"]').value) || 0;
    const total = quantity * unitPrice;
    
    orderItem.querySelector('.item-total').textContent = total.toFixed(2);
    updateOrderTotal();
}

function updateOrderTotal() {
    const itemTotals = document.querySelectorAll('.item-total');
    let subtotal = 0;
    
    itemTotals.forEach(totalElement => {
        subtotal += parseFloat(totalElement.textContent) || 0;
    });
    
    const tax = subtotal * 0.1; // 10% tax
    const total = subtotal + tax;
    
    document.getElementById('order-subtotal').textContent = subtotal.toFixed(2);
    document.getElementById('order-tax').textContent = tax.toFixed(2);
    document.getElementById('order-total').textContent = total.toFixed(2);
}

function saveDraft() {
    // Implement save as draft functionality
    alert('{% trans "Save as draft functionality to be implemented" %}');
}

document.getElementById('purchase-order-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '{% trans "Creating..." %}';
    submitBtn.disabled = true;
    
    fetch('{% url "purchases:purchase_order_create" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('purchase-order-modal');
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating purchase order');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});

// Auto-calculate totals when inputs change
document.addEventListener('input', function(e) {
    if (e.target.matches('input[name="quantities[]"], input[name="unit_prices[]"]')) {
        calculateItemTotal(e.target);
    }
});
</script>