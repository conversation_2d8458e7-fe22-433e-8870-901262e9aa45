{% load i18n %}
<!-- Quick Actions Modal -->
<div id="quick-actions-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
        <div class="modal-header flex justify-between items-center pb-3 border-b">
            <h3 class="text-xl font-bold text-gray-900">{% trans "Quick Actions" %}</h3>
            <button onclick="closeModal('quick-actions-modal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="mt-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                
                <!-- Inventory Actions -->
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-blue-600 border-b border-blue-200 pb-2">
                        <i class="fas fa-boxes mr-2"></i>{% trans "Inventory" %}
                    </h4>
                    
                    <button onclick="closeModal('quick-actions-modal'); openModal('item-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-plus text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Add New Item" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Add inventory item" %}</div>
                        </div>
                    </button>
                    
                    <button onclick="closeModal('quick-actions-modal'); openModal('stock-adjustment-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-edit text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Stock Adjustment" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Adjust stock levels" %}</div>
                        </div>
                    </button>
                    
                    <button onclick="closeModal('quick-actions-modal'); openModal('movement-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-exchange-alt text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Record Movement" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Log stock movement" %}</div>
                        </div>
                    </button>
                    
                    <a href="{% url 'core:supply_chain_low_stock' %}" onclick="closeModal('quick-actions-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Low Stock Report" %}</div>
                            <div class="text-sm text-gray-600">{% trans "View low stock items" %}</div>
                        </div>
                    </a>
                </div>
                
                <!-- Warehouse Actions -->
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-green-600 border-b border-green-200 pb-2">
                        <i class="fas fa-warehouse mr-2"></i>{% trans "Warehouse" %}
                    </h4>
                    
                    <button onclick="closeModal('quick-actions-modal'); openModal('transfer-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-truck text-green-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Create Transfer" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Transfer between locations" %}</div>
                        </div>
                    </button>
                    
                    <button onclick="closeModal('quick-actions-modal'); openModal('location-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-map-marker-alt text-green-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Add Location" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Create new location" %}</div>
                        </div>
                    </button>
                    
                    <a href="{% url 'core:supply_chain_item_locations' %}" onclick="closeModal('quick-actions-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-search-location text-green-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Item Locations" %}</div>
                            <div class="text-sm text-gray-600">{% trans "View item locations" %}</div>
                        </div>
                    </a>
                    
                    <a href="{% url 'core:supply_chain_transfers' %}" onclick="closeModal('quick-actions-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-list text-green-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Transfer Orders" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Manage transfers" %}</div>
                        </div>
                    </a>
                </div>
                
                <!-- Purchasing Actions -->
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-orange-600 border-b border-orange-200 pb-2">
                        <i class="fas fa-shopping-cart mr-2"></i>{% trans "Purchasing" %}
                    </h4>
                    
                    <button onclick="closeModal('quick-actions-modal'); openModal('purchase-order-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-plus text-orange-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Create Purchase Order" %}</div>
                            <div class="text-sm text-gray-600">{% trans "New purchase order" %}</div>
                        </div>
                    </button>
                    
                    <button onclick="closeModal('quick-actions-modal'); openModal('supplier-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user-tie text-orange-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Add Supplier" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Register new supplier" %}</div>
                        </div>
                    </button>
                    
                    <a href="{% url 'core:supply_chain_pending_orders' %}" onclick="closeModal('quick-actions-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-clock text-yellow-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Pending Orders" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Review pending orders" %}</div>
                        </div>
                    </a>
                    
                    <a href="{% url 'core:supply_chain_suppliers' %}" onclick="closeModal('quick-actions-modal')" class="w-full flex items-center p-4 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 text-left transition-all">
                        <div class="flex-shrink-0 w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-users text-orange-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">{% trans "Manage Suppliers" %}</div>
                            <div class="text-sm text-gray-600">{% trans "View all suppliers" %}</div>
                        </div>
                    </a>
                </div>
            </div>
            
            <!-- Additional Quick Tools -->
            <div class="mt-8 pt-6 border-t">
                <h4 class="text-lg font-semibold text-gray-700 mb-4">
                    <i class="fas fa-tools mr-2"></i>{% trans "Additional Tools" %}
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="{% url 'core:supply_chain_reports' %}" onclick="closeModal('quick-actions-modal')" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all">
                        <i class="fas fa-chart-bar text-gray-600 mr-3"></i>
                        <span class="font-medium text-gray-800">{% trans "Reports" %}</span>
                    </a>
                    
                    <a href="{% url 'core:supply_chain_workflow' %}" onclick="closeModal('quick-actions-modal')" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all">
                        <i class="fas fa-sitemap text-gray-600 mr-3"></i>
                        <span class="font-medium text-gray-800">{% trans "Workflow" %}</span>
                    </a>
                    
                    <button onclick="performBulkStockUpdate()" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all text-left">
                        <i class="fas fa-file-upload text-gray-600 mr-3"></i>
                        <span class="font-medium text-gray-800">{% trans "Bulk Update" %}</span>
                    </button>
                    
                    <button onclick="generateEmergencyReorder()" class="flex items-center p-3 bg-red-50 rounded-lg hover:bg-red-100 transition-all text-left">
                        <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                        <span class="font-medium text-red-800">{% trans "Emergency Reorder" %}</span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="modal-footer flex justify-end pt-4 mt-6 border-t">
            <button onclick="closeModal('quick-actions-modal')" class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                {% trans "Close" %}
            </button>
        </div>
    </div>
</div>

<script>
function performBulkStockUpdate() {
    closeModal('quick-actions-modal');
    // Navigate to bulk update page or open bulk update modal
    window.location.href = '{% url "core:supply_chain_bulk_stock_update" %}';
}

function generateEmergencyReorder() {
    closeModal('quick-actions-modal');
    
    // Show confirmation dialog
    if (confirm('{% trans "Generate emergency reorder for all low stock items?" %}')) {
        // Make AJAX call to generate emergency reorder
        fetch('{% url "core:supply_chain_emergency_reorder" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{% trans "Emergency reorder generated successfully!" %}');
                location.reload();
            } else {
                alert('Error: ' + (data.error || 'Unknown error occurred'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error generating emergency reorder');
        });
    }
}
</script>