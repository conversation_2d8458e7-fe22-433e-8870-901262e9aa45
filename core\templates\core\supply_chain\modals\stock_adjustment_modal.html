{% load i18n %}
<!-- Stock Adjustment Modal -->
<div id="stock-adjustment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="modal-header flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">{% trans "Stock Adjustment" %}</h3>
            <button onclick="closeModal('stock-adjustment-modal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="stock-adjustment-form" class="mt-3">
            {% csrf_token %}
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Item" %}</label>
                    <select name="item" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" onchange="updateCurrentStock(this.value)">
                        <option value="">{% trans "Select Item" %}</option>
                        <!-- Items will be loaded via AJAX -->
                    </select>
                </div>
                
                <div id="current-stock-display" class="bg-blue-50 border border-blue-200 rounded-lg p-3 hidden">
                    <div class="text-sm text-blue-800">
                        <strong>{% trans "Current Stock:" %}</strong> <span id="current-stock-value">0</span>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Adjustment Type" %}</label>
                        <select name="adjustment_type" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Type" %}</option>
                            <option value="increase">{% trans "Increase Stock" %}</option>
                            <option value="decrease">{% trans "Decrease Stock" %}</option>
                            <option value="set">{% trans "Set Exact Amount" %}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Quantity" %}</label>
                        <input type="number" name="quantity" step="0.01" min="0" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Reason" %}</label>
                    <select name="reason" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{% trans "Select Reason" %}</option>
                        <option value="damaged">{% trans "Damaged Items" %}</option>
                        <option value="expired">{% trans "Expired Items" %}</option>
                        <option value="found">{% trans "Found Items" %}</option>
                        <option value="lost">{% trans "Lost Items" %}</option>
                        <option value="count_correction">{% trans "Count Correction" %}</option>
                        <option value="other">{% trans "Other" %}</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Notes" %}</label>
                    <textarea name="notes" rows="3" placeholder="{% trans 'Additional details about this adjustment...' %}" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
            </div>
            
            <div class="modal-footer flex justify-end pt-4 mt-4 space-x-3">
                <button type="button" onclick="closeModal('stock-adjustment-modal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    {% trans "Cancel" %}
                </button>
                <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    {% trans "Apply Adjustment" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Load items when modal opens
document.addEventListener('DOMContentLoaded', function() {
    const itemSelect = document.querySelector('#stock-adjustment-modal select[name="item"]');
    
    // Load items via AJAX
    fetch('{% url "inventory:api_items_list" %}')
        .then(response => response.json())
        .then(data => {
            itemSelect.innerHTML = '<option value="">{% trans "Select Item" %}</option>';
            data.items.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = `${item.sku} - ${item.name}`;
                option.dataset.currentStock = item.quantity;
                itemSelect.appendChild(option);
            });
        })
        .catch(error => console.error('Error loading items:', error));
});

function updateCurrentStock(itemId) {
    const select = document.querySelector('#stock-adjustment-modal select[name="item"]');
    const selectedOption = select.querySelector(`option[value="${itemId}"]`);
    const display = document.getElementById('current-stock-display');
    const valueSpan = document.getElementById('current-stock-value');
    
    if (selectedOption && itemId) {
        const currentStock = selectedOption.dataset.currentStock || '0';
        valueSpan.textContent = currentStock;
        display.classList.remove('hidden');
    } else {
        display.classList.add('hidden');
    }
}

document.getElementById('stock-adjustment-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '{% trans "Processing..." %}';
    submitBtn.disabled = true;
    
    fetch('{% url "inventory:stock_adjustment" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('stock-adjustment-modal');
            location.reload(); // Refresh to show updated stock
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error processing adjustment');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});
</script>