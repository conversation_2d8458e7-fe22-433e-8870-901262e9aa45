{% load i18n %}
<!-- Supplier Modal -->
<div id="supplier-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="modal-header flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">{% trans "Add New Supplier" %}</h3>
            <button onclick="closeModal('supplier-modal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="supplier-form" class="mt-3">
            {% csrf_token %}
            <div class="space-y-4">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Company Name" %}</label>
                        <input type="text" name="company_name" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Supplier Code" %}</label>
                        <input type="text" name="supplier_code" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <!-- Contact Information -->
                <h4 class="text-md font-semibold text-gray-900 border-b pb-2 mt-6">{% trans "Contact Information" %}</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Contact Person" %}</label>
                        <input type="text" name="contact_person" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Job Title" %}</label>
                        <input type="text" name="job_title" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Email" %}</label>
                        <input type="email" name="email" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Phone" %}</label>
                        <input type="tel" name="phone" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Mobile" %}</label>
                        <input type="tel" name="mobile" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Fax" %}</label>
                        <input type="tel" name="fax" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <!-- Address Information -->
                <h4 class="text-md font-semibold text-gray-900 border-b pb-2 mt-6">{% trans "Address Information" %}</h4>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Street Address" %}</label>
                    <textarea name="address" rows="2" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "City" %}</label>
                        <input type="text" name="city" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "State/Province" %}</label>
                        <input type="text" name="state" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Postal Code" %}</label>
                        <input type="text" name="postal_code" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Country" %}</label>
                    <select name="country" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{% trans "Select Country" %}</option>
                        <option value="EG">{% trans "Egypt" %}</option>
                        <option value="SA">{% trans "Saudi Arabia" %}</option>
                        <option value="AE">{% trans "United Arab Emirates" %}</option>
                        <option value="US">{% trans "United States" %}</option>
                        <option value="DE">{% trans "Germany" %}</option>
                        <option value="CN">{% trans "China" %}</option>
                        <option value="JP">{% trans "Japan" %}</option>
                        <option value="IN">{% trans "India" %}</option>
                    </select>
                </div>
                
                <!-- Business Information -->
                <h4 class="text-md font-semibold text-gray-900 border-b pb-2 mt-6">{% trans "Business Information" %}</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Tax Registration Number" %}</label>
                        <input type="text" name="tax_number" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Commercial Registration" %}</label>
                        <input type="text" name="commercial_registration" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Payment Terms (Days)" %}</label>
                        <input type="number" name="payment_terms" min="0" value="30" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Credit Limit" %}</label>
                        <input type="number" name="credit_limit" step="0.01" min="0" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Supplier Category" %}</label>
                        <select name="category" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Category" %}</option>
                            <option value="parts">{% trans "Auto Parts" %}</option>
                            <option value="tools">{% trans "Tools & Equipment" %}</option>
                            <option value="consumables">{% trans "Consumables" %}</option>
                            <option value="services">{% trans "Services" %}</option>
                            <option value="other">{% trans "Other" %}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Currency" %}</label>
                        <select name="currency" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="EGP">EGP - Egyptian Pound</option>
                            <option value="USD">USD - US Dollar</option>
                            <option value="EUR">EUR - Euro</option>
                            <option value="SAR">SAR - Saudi Riyal</option>
                            <option value="AED">AED - UAE Dirham</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Notes" %}</label>
                    <textarea name="notes" rows="3" placeholder="{% trans 'Additional supplier information...' %}" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <!-- Status -->
                <div class="flex items-center space-x-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">{% trans "Active Supplier" %}</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_preferred" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">{% trans "Preferred Supplier" %}</span>
                    </label>
                </div>
            </div>
            
            <div class="modal-footer flex justify-end pt-4 mt-6 border-t space-x-3">
                <button type="button" onclick="closeModal('supplier-modal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    {% trans "Cancel" %}
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    {% trans "Create Supplier" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('supplier-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '{% trans "Creating..." %}';
    submitBtn.disabled = true;
    
    fetch('{% url "purchases:supplier_create" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('supplier-modal');
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating supplier');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});
</script>