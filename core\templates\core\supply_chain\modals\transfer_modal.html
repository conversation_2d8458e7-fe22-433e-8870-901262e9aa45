{% load i18n %}
<!-- Transfer Modal -->
<div id="transfer-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="modal-header flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">{% trans "Create Transfer Order" %}</h3>
            <button onclick="closeModal('transfer-modal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="transfer-form" class="mt-3">
            {% csrf_token %}
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "From Location" %}</label>
                        <select name="source_location" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Source Location" %}</option>
                            <!-- Locations will be loaded via AJAX -->
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "To Location" %}</label>
                        <select name="destination_location" required class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Destination Location" %}</option>
                            <!-- Locations will be loaded via AJAX -->
                        </select>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Reference Number" %}</label>
                    <input type="text" name="reference" placeholder="{% trans 'Auto-generated if left empty' %}" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Priority" %}</label>
                    <select name="priority" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="low">{% trans "Low" %}</option>
                        <option value="normal" selected>{% trans "Normal" %}</option>
                        <option value="high">{% trans "High" %}</option>
                        <option value="urgent">{% trans "Urgent" %}</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Expected Date" %}</label>
                    <input type="date" name="expected_date" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Notes" %}</label>
                    <textarea name="notes" rows="3" placeholder="{% trans 'Additional transfer details...' %}" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <!-- Transfer Items Section -->
                <div class="border-t pt-4">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-md font-semibold text-gray-900">{% trans "Items to Transfer" %}</h4>
                        <button type="button" onclick="addTransferItem()" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-plus mr-1"></i>{% trans "Add Item" %}
                        </button>
                    </div>
                    <div id="transfer-items" class="space-y-3">
                        <div class="transfer-item grid grid-cols-12 gap-2 items-end">
                            <div class="col-span-6">
                                <label class="block text-xs font-medium text-gray-700 mb-1">{% trans "Item" %}</label>
                                <select name="items[]" required class="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">{% trans "Select Item" %}</option>
                                </select>
                            </div>
                            <div class="col-span-3">
                                <label class="block text-xs font-medium text-gray-700 mb-1">{% trans "Quantity" %}</label>
                                <input type="number" name="quantities[]" step="0.01" min="0" required class="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div class="col-span-2">
                                <label class="block text-xs font-medium text-gray-700 mb-1">{% trans "Available" %}</label>
                                <div class="text-sm text-gray-600 p-2 bg-gray-50 rounded available-qty">0</div>
                            </div>
                            <div class="col-span-1">
                                <button type="button" onclick="removeTransferItem(this)" class="text-red-600 hover:text-red-800 p-2">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer flex justify-end pt-4 mt-4 space-x-3">
                <button type="button" onclick="closeModal('transfer-modal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    {% trans "Cancel" %}
                </button>
                <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    {% trans "Create Transfer" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
let itemsData = [];

document.addEventListener('DOMContentLoaded', function() {
    loadLocations();
    loadItems();
});

function loadLocations() {
    fetch('{% url "warehouse:api_locations_list" %}')
        .then(response => response.json())
        .then(data => {
            const sourceSelect = document.querySelector('#transfer-modal select[name="source_location"]');
            const destSelect = document.querySelector('#transfer-modal select[name="destination_location"]');
            
            [sourceSelect, destSelect].forEach(select => {
                select.innerHTML = '<option value="">{% trans "Select Location" %}</option>';
                data.locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    select.appendChild(option);
                });
            });
        })
        .catch(error => console.error('Error loading locations:', error));
}

function loadItems() {
    fetch('{% url "inventory:api_items_list" %}')
        .then(response => response.json())
        .then(data => {
            itemsData = data.items;
            updateItemSelects();
        })
        .catch(error => console.error('Error loading items:', error));
}

function updateItemSelects() {
    const itemSelects = document.querySelectorAll('#transfer-modal select[name="items[]"]');
    itemSelects.forEach(select => {
        select.innerHTML = '<option value="">{% trans "Select Item" %}</option>';
        itemsData.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.sku} - ${item.name}`;
            option.dataset.available = item.quantity;
            select.appendChild(option);
        });
    });
}

function addTransferItem() {
    const container = document.getElementById('transfer-items');
    const newItem = container.children[0].cloneNode(true);
    
    // Clear values
    newItem.querySelector('select').selectedIndex = 0;
    newItem.querySelector('input').value = '';
    newItem.querySelector('.available-qty').textContent = '0';
    
    container.appendChild(newItem);
    updateItemSelects();
}

function removeTransferItem(button) {
    const container = document.getElementById('transfer-items');
    if (container.children.length > 1) {
        button.closest('.transfer-item').remove();
    }
}

// Update available quantity when item is selected
document.addEventListener('change', function(e) {
    if (e.target.matches('select[name="items[]"]')) {
        const selectedOption = e.target.selectedOptions[0];
        const availableQty = e.target.closest('.transfer-item').querySelector('.available-qty');
        availableQty.textContent = selectedOption ? (selectedOption.dataset.available || '0') : '0';
    }
});

document.getElementById('transfer-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.textContent = '{% trans "Creating..." %}';
    submitBtn.disabled = true;
    
    fetch('{% url "warehouse:transfer_create" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('transfer-modal');
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating transfer');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});
</script>