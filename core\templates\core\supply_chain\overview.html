{% extends 'core/supply_chain/base.html' %}
{% load static %}
{% load i18n %}

{% block page_title %}نظرة عامة{% endblock %}
{% block page_subtitle %}مراقبة شاملة لسلسلة التوريد{% endblock %}

{% block supply_chain_content %}
<!-- Enhanced KPI Dashboard with Real Data -->
<div class="compact-kpi-grid">
    <div class="kpi-card {% if purchase_efficiency >= 80 %}success{% elif purchase_efficiency >= 60 %}info{% else %}warning{% endif %}">
        <div class="kpi-icon"><i class="fas fa-shopping-cart"></i></div>
        <div class="kpi-data">
            <div class="kpi-value">{{ purchase_efficiency|default:0 }}%</div>
            <div class="kpi-label">كفاءة المشتريات</div>
        </div>
    </div>
    <div class="kpi-card {% if warehouse_efficiency >= 80 %}success{% elif warehouse_efficiency >= 60 %}info{% else %}warning{% endif %}">
        <div class="kpi-icon"><i class="fas fa-warehouse"></i></div>
        <div class="kpi-data">
            <div class="kpi-value">{{ warehouse_efficiency|default:0 }}%</div>
            <div class="kpi-label">كفاءة المستودعات</div>
        </div>
    </div>
    <div class="kpi-card {% if inventory_accuracy >= 80 %}success{% elif inventory_accuracy >= 60 %}info{% else %}warning{% endif %}">
        <div class="kpi-icon"><i class="fas fa-check-circle"></i></div>
        <div class="kpi-data">
            <div class="kpi-value">{{ inventory_accuracy|default:0 }}%</div>
            <div class="kpi-label">دقة المخزون</div>
        </div>
    </div>
    <div class="kpi-card {% if urgent_tasks > 5 %}error{% elif urgent_tasks > 0 %}warning{% else %}success{% endif %}">
        <div class="kpi-icon"><i class="fas fa-exclamation-circle"></i></div>
        <div class="kpi-data">
            <div class="kpi-value">{{ urgent_tasks|default:0 }}</div>
            <div class="kpi-label">مهام عاجلة</div>
        </div>
    </div>
</div>

<!-- Enhanced Hub Navigation with Real Data -->
<div class="hub-navigation">
    <a href="{% url 'core:supply_chain_inventory_hub' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-boxes"></i>
        </div>
        <div class="title">المخزون</div>
        <div class="description">إدارة الأصناف والمخزون</div>
        <div class="stats">
            <div class="stat">
                <div class="number">{{ inventory_hub.total_items|default:0 }}</div>
                <div class="label">صنف</div>
            </div>
            <div class="stat">
                <div class="number {% if inventory_hub.low_stock_count > 0 %}text-red-600{% endif %}">{{ inventory_hub.low_stock_count|default:0 }}</div>
                <div class="label">منخفض</div>
            </div>
        </div>
    </a>
    
    <a href="{% url 'core:supply_chain_warehouse_hub' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-warehouse"></i>
        </div>
        <div class="title">المستودعات</div>
        <div class="description">إدارة المستودعات والمواقع</div>
        <div class="stats">
            <div class="stat">
                <div class="number">{{ warehouse_hub.total_warehouses|default:0 }}</div>
                <div class="label">مستودع</div>
            </div>
            <div class="stat">
                <div class="number">{{ warehouse_hub.warehouse_utilization|default:0 }}%</div>
                <div class="label">إشغال</div>
            </div>
        </div>
    </a>
    
    <a href="{% url 'core:supply_chain_purchase_hub' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <div class="title">المشتريات</div>
        <div class="description">إدارة الطلبات والموردين</div>
        <div class="stats">
            <div class="stat">
                <div class="number">{{ purchase_hub.total_orders|default:0 }}</div>
                <div class="label">طلب</div>
            </div>
            <div class="stat">
                <div class="number {% if purchase_hub.pending_orders > 0 %}text-yellow-600{% endif %}">{{ purchase_hub.pending_orders|default:0 }}</div>
                <div class="label">معلق</div>
            </div>
        </div>
    </a>
    
    <a href="{% url 'core:supply_chain_reports' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-chart-bar"></i>
        </div>
        <div class="title">التقارير</div>
        <div class="description">تقارير وإحصائيات شاملة</div>
        <div class="stats">
            <div class="stat">
                <div class="number">{{ reports_hub.total_reports|default:0 }}</div>
                <div class="label">تقرير</div>
            </div>
            <div class="stat">
                <div class="number">{{ reports_hub.automated_reports|default:0 }}</div>
                <div class="label">تلقائي</div>
            </div>
        </div>
    </a>
</div>

<!-- Main Content Grid -->
<div class="main-content-grid">
    <!-- Enhanced Live Activity Feed with Real Data -->
    <div class="content-card">
        <h6 class="card-title"><i class="fas fa-pulse"></i> النشاط المباشر</h6>
        <div class="activity-feed">
            {% if activities %}
                {% for activity in activities %}
                <div class="activity-item">
                    <div class="activity-icon {{ activity.type }}">
                        <i class="{{ activity.icon }}"></i>
                    </div>
                    <div class="activity-content">
                        <span class="activity-text">{{ activity.title }}</span>
                        <small class="activity-time">{{ activity.description }} - {{ activity.time|timesince }} مضت</small>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="activity-item">
                    <div class="activity-icon inventory">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="activity-content">
                        <span class="activity-text">لا توجد أنشطة حديثة</span>
                        <small class="activity-time">ابدأ بإجراء عمليات للحصول على معلومات الأنشطة</small>
                    </div>
                </div>
            {% endif %}
        </div>
        <a href="#" class="card-footer-link">عرض جميع الأنشطة</a>
    </div>

    <!-- Enhanced Critical Alerts with Real Data -->
    <div class="content-card">
        <h6 class="card-title"><i class="fas fa-bell"></i> تنبيهات مهمة</h6>
        <div class="alerts-list">
            {% if alerts %}
                {% for alert in alerts %}
                <div class="alert-item {{ alert.type }}">
                    <div class="alert-icon"><i class="{{ alert.icon }}"></i></div>
                    <div class="alert-content">
                        <span class="alert-title">{{ alert.title }}</span>
                        <small>{{ alert.message }}</small>
                    </div>
                    <a href="{% url alert.url %}" class="alert-action">عرض</a>
                </div>
                {% endfor %}
            {% else %}
                <div class="alert-item success">
                    <div class="alert-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="alert-content">
                        <span class="alert-title">كل شيء على ما يرام</span>
                        <small>لا توجد تنبيهات عاجلة</small>
                    </div>
                    <span class="alert-action" style="color: #22c55e;">ممتاز</span>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Performance Chart -->
    <div class="content-card chart-card">
        <h6 class="card-title"><i class="fas fa-chart-line"></i> الأداء الأسبوعي</h6>
        <div class="chart-container">
            <canvas id="performanceChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-summary">
            <div class="summary-item">
                <span class="summary-label">المعالجة</span>
                <span class="summary-value">94%</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">التسليم</span>
                <span class="summary-value">87%</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">الجودة</span>
                <span class="summary-value">96%</span>
            </div>
        </div>
    </div>
</div>

<!-- Compact Quick Actions -->
<div class="action-buttons">
    <button class="action-btn" onclick="quickStockCheck()">
        <i class="fas fa-search"></i>
        <span>فحص مخزون</span>
    </button>
    <button class="action-btn" onclick="quickTransfer()">
        <i class="fas fa-exchange-alt"></i>
        <span>نقل سريع</span>
    </button>
    <button class="action-btn" onclick="urgentOrder()">
        <i class="fas fa-plus-circle"></i>
        <span>طلب عاجل</span>
    </button>
    <button class="action-btn" onclick="generateReport()">
        <i class="fas fa-file-download"></i>
        <span>تقرير فوري</span>
    </button>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
/* Compact KPI Grid */
.compact-kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.kpi-card {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.kpi-card.success { border-color: #48bb78; }
.kpi-card.info { border-color: #4299e1; }
.kpi-card.primary { border-color: #667eea; }
.kpi-card.warning { border-color: #ed8936; }
.kpi-card.error { border-color: #dc2626; }

.kpi-icon {
    font-size: 1.5rem;
    margin-left: 15px;
    opacity: 0.8;
}

.kpi-card.success .kpi-icon { color: #48bb78; }
.kpi-card.info .kpi-icon { color: #4299e1; }
.kpi-card.primary .kpi-icon { color: #667eea; }
.kpi-card.warning .kpi-icon { color: #ed8936; }
.kpi-card.error .kpi-icon { color: #dc2626; }

.kpi-value {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 2px;
}

.kpi-label {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
}



/* Main Content Grid */
.main-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.content-card {
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
    padding: 12px 15px;
    margin: 0;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

/* Activity Feed */
.activity-feed {
    padding: 10px 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    margin-left: 10px;
}

.activity-icon.inventory { background: #e6fffa; color: #2c7a7b; }
.activity-icon.warehouse { background: #ebf8ff; color: #2b6cb0; }
.activity-icon.purchase { background: #fef5e7; color: #c05621; }

.activity-content {
    flex: 1;
}

.activity-text {
    font-size: 0.8rem;
    font-weight: 500;
    display: block;
}

.activity-time {
    font-size: 0.7rem;
    color: #718096;
}

/* Alerts List */
.alerts-list {
    padding: 10px 15px;
}

.alert-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.alert-item:last-child {
    border-bottom: none;
}

.alert-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    margin-left: 10px;
}

.alert-item.warning .alert-icon { background: #fffbeb; color: #c05621; }
.alert-item.info .alert-icon { background: #ebf8ff; color: #2b6cb0; }
.alert-item.success .alert-icon { background: #f0fff4; color: #276749; }
.alert-item.error .alert-icon { background: #fef2f2; color: #dc2626; }

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: 0.8rem;
    font-weight: 500;
    display: block;
}

.alert-content small {
    font-size: 0.7rem;
    color: #718096;
}

.alert-action {
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
}

.alert-item.warning .alert-action { 
    background: #fffbeb; 
    color: #c05621; 
    border: 1px solid #fed7aa;
}

.alert-item.info .alert-action { 
    background: #ebf8ff; 
    color: #2b6cb0; 
    border: 1px solid #bfdbfe;
}

.alert-item.success .alert-action { 
    background: #f0fff4; 
    color: #276749; 
    border: 1px solid #bbf7d0;
}

.alert-item.error .alert-action { 
    background: #fef2f2; 
    color: #dc2626; 
    border: 1px solid #fecaca;
}

/* Chart Card */
.chart-container {
    padding: 15px;
    height: 200px;
}

.chart-summary {
    display: flex;
    justify-content: space-around;
    padding: 10px 15px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.summary-item {
    text-align: center;
}

.summary-label {
    font-size: 0.7rem;
    color: #718096;
    display: block;
}

.summary-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
}

/* Card Footer Links */
.card-footer-link {
    display: block;
    padding: 8px 15px;
    background: #f8fafc;
    color: #667eea;
    text-decoration: none;
    font-size: 0.75rem;
    font-weight: 500;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.card-footer-link:hover {
    background: #e2e8f0;
    text-decoration: none;
    color: #5a67d8;
}



/* Responsive */
@media (max-width: 768px) {
    .compact-kpi-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .main-content-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Performance chart
    const ctx = document.getElementById('performanceChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
            datasets: [{
                label: 'المعالجة',
                data: [92, 94, 89, 96, 94, 91, 94],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4
            }, {
                label: 'التسليم',
                data: [85, 87, 84, 89, 87, 86, 87],
                borderColor: '#4299e1',
                backgroundColor: 'rgba(66, 153, 225, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
});

// Quick Action Functions
function quickStockCheck() {
    window.location.href = "{% url 'core:supply_chain_items_list' %}";
}

function quickTransfer() {
    window.location.href = "{% url 'core:supply_chain_transfer_create' %}";
}

function urgentOrder() {
    window.location.href = "{% url 'core:supply_chain_purchase_order_create' %}";
}

function generateReport() {
    window.location.href = "{% url 'core:supply_chain_reports' %}";
}

// Auto-refresh every 60 seconds
setInterval(function() {
    // You can add AJAX calls here to refresh data
}, 60000);
</script>
{% endblock %} 