{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "Purchase Orders" %}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">{% trans "Purchase Orders" %}</h1>
                    <p class="text-muted">{% trans "Manage purchase orders" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="openModal('purchase-order-modal')">
                        <i class="fas fa-plus me-2"></i>{% trans "Create Purchase Order" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchase Orders List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Purchase Orders" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "Order #" %}</th>
                                    <th>{% trans "Supplier" %}</th>
                                    <th>{% trans "Date" %}</th>
                                    <th>{% trans "Total" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                            <p>{% trans "No purchase orders found. Create your first purchase order to get started." %}</p>
                                            <button class="btn btn-primary" onclick="openModal('purchase-order-modal')">
                                                <i class="fas fa-plus me-2"></i>{% trans "Create Purchase Order" %}
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include purchase order modal -->
{% include 'core/supply_chain/modals/purchase_order_modal.html' %}

<script>
// Modal functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}
</script>
{% endblock %} 