{% extends "core/dashboard_base.html" %}
{% load i18n %}
{% load static %}
{% load core_tags %}

{% block title %}{% trans "Unified Supply Chain Dashboard" %}{% endblock %}

{% block extra_css %}
<style>
    .stat-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .stat-card.primary { border-left-color: #3b82f6; }
    .stat-card.success { border-left-color: #10b981; }
    .stat-card.warning { border-left-color: #f59e0b; }
    .stat-card.danger { border-left-color: #ef4444; }
    .stat-card.info { border-left-color: #06b6d4; }
    
    .metric-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .section-header {
        border-bottom: 2px solid #e5e7eb;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
    }
    
    .quick-action-btn {
        transition: all 0.2s ease;
    }
    .quick-action-btn:hover {
        transform: scale(1.05);
    }
    
    .alert-item {
        border-left: 4px solid;
        transition: all 0.2s ease;
    }
    .alert-item:hover {
        background-color: #f9fafb;
    }
    
    .activity-timeline {
        position: relative;
        padding-left: 2rem;
    }
    .activity-timeline::before {
        content: '';
        position: absolute;
        left: 8px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e5e7eb;
    }
    .activity-item {
        position: relative;
        margin-bottom: 1rem;
    }
    .activity-item::before {
        content: '';
        position: absolute;
        left: -2rem;
        top: 6px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #3b82f6;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #e5e7eb;
    }
    
    .modal-header {
        border-bottom: 1px solid #e5e7eb;
    }
    .modal-footer {
        border-top: 1px solid #e5e7eb;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">{% trans "Supply Chain Management" %}</h1>
            <p class="text-gray-600 mt-1">{% trans "Unified dashboard for inventory, warehousing, and purchasing" %}</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="openQuickActionModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-plus mr-2"></i>{% trans "Quick Actions" %}
            </button>
            <button onclick="refreshDashboard()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-sync-alt mr-2"></i>{% trans "Refresh" %}
            </button>
        </div>
    </div>

    <!-- Key Metrics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Inventory Metrics -->
        <div class="stat-card primary bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-2xl font-bold text-gray-900" id="total-items">{{ total_items|default:0 }}</div>
                    <div class="text-sm text-gray-600">{% trans "Total Items" %}</div>
                </div>
                <div class="metric-icon bg-blue-100 text-blue-600">
                    <i class="fas fa-boxes"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-red-600 font-medium" id="low-stock-count">{{ low_stock_items|default:0 }}</span>
                <span class="text-gray-600 ml-1">{% trans "low stock items" %}</span>
            </div>
        </div>

        <!-- Warehouse Metrics -->
        <div class="stat-card success bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-2xl font-bold text-gray-900" id="active-warehouses">{{ active_warehouses|default:0 }}</div>
                    <div class="text-sm text-gray-600">{% trans "Active Warehouses" %}</div>
                </div>
                <div class="metric-icon bg-green-100 text-green-600">
                    <i class="fas fa-warehouse"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-blue-600 font-medium" id="pending-transfers">{{ pending_transfers|default:0 }}</span>
                <span class="text-gray-600 ml-1">{% trans "pending transfers" %}</span>
            </div>
        </div>

        <!-- Purchase Metrics -->
        <div class="stat-card warning bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-2xl font-bold text-gray-900" id="total-purchase-orders">{{ total_purchase_orders|default:0 }}</div>
                    <div class="text-sm text-gray-600">{% trans "Purchase Orders" %}</div>
                </div>
                <div class="metric-icon bg-yellow-100 text-yellow-600">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-orange-600 font-medium" id="pending-orders">{{ pending_purchase_orders|default:0 }}</span>
                <span class="text-gray-600 ml-1">{% trans "pending approval" %}</span>
            </div>
        </div>

        <!-- Efficiency Score -->
        <div class="stat-card info bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between">
                <div>
                    <div class="text-2xl font-bold text-gray-900" id="efficiency-score">{{ inventory_accuracy|default:85 }}%</div>
                    <div class="text-sm text-gray-600">{% trans "System Efficiency" %}</div>
                </div>
                <div class="metric-icon bg-cyan-100 text-cyan-600">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center text-sm">
                <span class="text-green-600 font-medium">+2.5%</span>
                <span class="text-gray-600 ml-1">{% trans "vs last month" %}</span>
            </div>
        </div>
    </div>    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <!-- Left Column: Inventory & Warehouse -->
        <div class="xl:col-span-2 space-y-6">
            <!-- Inventory Management -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="section-header p-6 pb-4">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-boxes text-blue-600 mr-3"></i>
                            {% trans "Inventory Management" %}
                        </h2>
                        <a href="{% url 'core:supply_chain_inventory_hub' %}" class="text-blue-600 hover:text-blue-800 text-sm">
                            {% trans "View All" %} <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
                <div class="p-6 pt-0">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <button onclick="openModal('item-modal')" class="quick-action-btn p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 text-center">
                            <i class="fas fa-plus-circle text-2xl text-gray-400 mb-2"></i>
                            <div class="text-sm font-medium text-gray-700">{% trans "Add Item" %}</div>
                        </button>
                        <button onclick="openModal('stock-adjustment-modal')" class="quick-action-btn p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 text-center">
                            <i class="fas fa-edit text-2xl text-gray-400 mb-2"></i>
                            <div class="text-sm font-medium text-gray-700">{% trans "Stock Adjustment" %}</div>
                        </button>
                        <button onclick="openModal('movement-modal')" class="quick-action-btn p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 text-center">
                            <i class="fas fa-exchange-alt text-2xl text-gray-400 mb-2"></i>
                            <div class="text-sm font-medium text-gray-700">{% trans "Record Movement" %}</div>
                        </button>
                    </div>
                    
                    <!-- Quick Action Buttons for Arabic Interface -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <button onclick="showAllItems()" class="bg-blue-100 hover:bg-blue-200 text-blue-800 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-list text-lg mb-2"></i>
                            <div class="font-medium">عرض جميع الأصناف</div>
                        </button>
                        <button onclick="showLowStockAlerts()" class="bg-red-100 hover:bg-red-200 text-red-800 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-exclamation-triangle text-lg mb-2"></i>
                            <div class="font-medium">تنبيهات المخزون المنخفض</div>
                        </button>
                    </div>
                    
                    <!-- Inventory Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="text-red-800 font-medium">{% trans "Critical Stock Levels" %}</div>
                                <i class="fas fa-exclamation-triangle text-red-600"></i>
                            </div>
                            <div class="mt-2">
                                <div class="text-2xl font-bold text-red-900" id="critical-stock">{{ out_of_stock_items|default:0 }}</div>
                                <div class="text-sm text-red-700">{% trans "items out of stock" %}</div>
                            </div>
                        </div>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="text-blue-800 font-medium">{% trans "Categories" %}</div>
                                <i class="fas fa-tags text-blue-600"></i>
                            </div>
                            <div class="mt-2">
                                <div class="text-2xl font-bold text-blue-900" id="total-categories">{{ total_categories|default:0 }}</div>
                                <div class="text-sm text-blue-700">{% trans "active categories" %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warehouse Management -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="section-header p-6 pb-4">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-warehouse text-green-600 mr-3"></i>
                            {% trans "Warehouse Operations" %}
                        </h2>
                        <a href="{% url 'core:supply_chain_warehouse_hub' %}" class="text-green-600 hover:text-green-800 text-sm">
                            {% trans "View All" %} <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
                <div class="p-6 pt-0">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <button onclick="openModal('transfer-modal')" class="quick-action-btn p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 text-center">
                            <i class="fas fa-truck text-2xl text-gray-400 mb-2"></i>
                            <div class="text-sm font-medium text-gray-700">{% trans "Create Transfer" %}</div>
                        </button>
                        <button onclick="openModal('location-modal')" class="quick-action-btn p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-yellow-500 hover:bg-yellow-50 text-center">
                            <i class="fas fa-map-marker-alt text-2xl text-gray-400 mb-2"></i>
                            <div class="text-sm font-medium text-gray-700">{% trans "Add Location" %}</div>
                        </button>
                        <button onclick="showItemLocations()" class="quick-action-btn p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-500 hover:bg-indigo-50 text-center">
                            <i class="fas fa-search-location text-2xl text-gray-400 mb-2"></i>
                            <div class="text-sm font-medium text-gray-700">{% trans "Item Locations" %}</div>
                        </button>
                    </div>
                    
                    <!-- Warehouse Operations Arabic Buttons -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <button onclick="showWarehouseOperations()" class="bg-green-100 hover:bg-green-200 text-green-800 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-warehouse text-lg mb-2"></i>
                            <div class="font-medium">عمليات المستودع</div>
                        </button>
                        <button onclick="showLocationManagement()" class="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-map-marked-alt text-lg mb-2"></i>
                            <div class="font-medium">إدارة المواقع والتحويلات وتتبع مواضع الأصناف</div>
                        </button>
                    </div>
                    
                    <!-- Warehouse Statistics Arabic -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                            <div class="text-blue-800 font-medium mb-1">المواقع</div>
                            <div class="text-2xl font-bold text-blue-900">{{ active_warehouses|default:8 }}</div>
                            <button onclick="showLocationsModal()" class="text-blue-600 hover:text-blue-800 text-sm mt-2">إدارة المواقع</button>
                        </div>
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
                            <div class="text-orange-800 font-medium mb-1">التحويلات المعلقة</div>
                            <div class="text-2xl font-bold text-orange-900">{{ pending_transfers|default:3 }}</div>
                            <button onclick="showTransfersModal()" class="text-orange-600 hover:text-orange-800 text-sm mt-2">عرض التحويلات</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>        <!-- Right Column: Purchase Management & Alerts -->
        <div class="space-y-6">
            <!-- Purchase Management -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="section-header p-6 pb-4">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-shopping-cart text-orange-600 mr-3"></i>
                            {% trans "Purchasing" %}
                        </h2>
                        <a href="{% url 'core:supply_chain_purchase_hub' %}" class="text-orange-600 hover:text-orange-800 text-sm">
                            {% trans "View All" %} <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
                <div class="p-6 pt-0">
                    <div class="grid grid-cols-1 gap-4 mb-6">
                        <button onclick="openModal('purchase-order-modal')" class="quick-action-btn p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 text-center">
                            <i class="fas fa-plus-circle text-2xl text-gray-400 mb-2"></i>
                            <div class="text-sm font-medium text-gray-700">{% trans "Create Purchase Order" %}</div>
                        </button>
                        <button onclick="openModal('supplier-modal')" class="quick-action-btn p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 text-center">
                            <i class="fas fa-user-tie text-2xl text-gray-400 mb-2"></i>
                            <div class="text-sm font-medium text-gray-700">{% trans "Add Supplier" %}</div>
                        </button>
                    </div>
                    
                    <!-- Purchasing Arabic Section -->
                    <div class="mb-6 p-4 bg-orange-50 rounded-lg">
                        <h3 class="text-lg font-bold text-orange-800 mb-3 text-center">المشتريات والتوريد</h3>
                        <p class="text-sm text-orange-700 text-center mb-4">إنشاء أوامر الشراء وإدارة الموردين ومتابعة التسليمات</p>
                        
                        <!-- Purchasing Statistics -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div class="bg-white border border-orange-200 rounded-lg p-3 text-center">
                                <div class="text-orange-800 font-medium mb-1">الموردون</div>
                                <div class="text-2xl font-bold text-orange-900">{{ total_suppliers|default:15 }}</div>
                            </div>
                            <div class="bg-white border border-orange-200 rounded-lg p-3 text-center">
                                <div class="text-orange-800 font-medium mb-1">الأوامر المفتوحة</div>
                                <div class="text-2xl font-bold text-orange-900">{{ pending_purchase_orders|default:7 }}</div>
                            </div>
                        </div>
                        
                        <!-- Purchasing Actions -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <button onclick="showPurchaseOrders()" class="bg-orange-600 hover:bg-orange-700 text-white p-3 rounded-lg text-center transition-colors">
                                <i class="fas fa-shopping-cart text-lg mb-1"></i>
                                <div class="font-medium">أوامر الشراء</div>
                            </button>
                            <button onclick="showSuppliersManagement()" class="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg text-center transition-colors">
                                <i class="fas fa-users text-lg mb-1"></i>
                                <div class="font-medium">إدارة الموردين</div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts & Notifications -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="section-header p-6 pb-4">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-bell text-red-600 mr-3"></i>
                        {% trans "Alerts & Notifications" %}
                    </h2>
                </div>
                <div class="p-6 pt-0">
                    <div class="space-y-3" id="alerts-container">
                        {% for alert in alerts %}
                        <div class="alert-item p-4 rounded-lg border-l-4 {% if alert.type == 'error' %}border-red-500 bg-red-50{% elif alert.type == 'warning' %}border-yellow-500 bg-yellow-50{% elif alert.type == 'info' %}border-blue-500 bg-blue-50{% else %}border-gray-500 bg-gray-50{% endif %}">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="{{ alert.icon }} {% if alert.type == 'error' %}text-red-600{% elif alert.type == 'warning' %}text-yellow-600{% elif alert.type == 'info' %}text-blue-600{% else %}text-gray-600{% endif %}"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium {% if alert.type == 'error' %}text-red-800{% elif alert.type == 'warning' %}text-yellow-800{% elif alert.type == 'info' %}text-blue-800{% else %}text-gray-800{% endif %}">
                                        {{ alert.title }}
                                    </h3>
                                    <div class="mt-1 text-sm {% if alert.type == 'error' %}text-red-700{% elif alert.type == 'warning' %}text-yellow-700{% elif alert.type == 'info' %}text-blue-700{% else %}text-gray-700{% endif %}">
                                        {{ alert.message }}
                                    </div>
                                    {% if alert.url %}
                                    <div class="mt-2">
                                        <a href="{% url alert.url %}" class="text-sm font-medium {% if alert.type == 'error' %}text-red-800 hover:text-red-900{% elif alert.type == 'warning' %}text-yellow-800 hover:text-yellow-900{% elif alert.type == 'info' %}text-blue-800 hover:text-blue-900{% else %}text-gray-800 hover:text-gray-900{% endif %}">
                                            {% trans "View Details" %} <i class="fas fa-arrow-right ml-1"></i>
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-check-circle text-4xl mb-3 text-green-500"></i>
                            <div class="text-lg font-medium">{% trans "All Good!" %}</div>
                            <div class="text-sm">{% trans "No alerts at this time" %}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>            <!-- System Status Arabic Section -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="section-header p-6 pb-4">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-bar text-green-600 mr-3"></i>
                        حالة النظام
                    </h2>
                </div>
                <div class="p-6 pt-0">
                    <div class="space-y-4">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="text-green-800 font-medium">صحة المخزون</div>
                                <div class="text-2xl font-bold text-green-900">92%</div>
                            </div>
                            <div class="w-full bg-green-200 rounded-full h-2 mt-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                        </div>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="text-blue-800 font-medium">كفاءة المستودع</div>
                                <div class="text-2xl font-bold text-blue-900">88%</div>
                            </div>
                            <div class="w-full bg-blue-200 rounded-full h-2 mt-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 88%"></div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="text-yellow-800 font-medium">أداء المشتريات</div>
                                <div class="text-2xl font-bold text-yellow-900">75%</div>
                            </div>
                            <div class="w-full bg-yellow-200 rounded-full h-2 mt-2">
                                <div class="bg-yellow-600 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="text-center mt-4">
                            <button onclick="showDetailedAnalytics()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                                عرض التحليلات التفصيلية
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activities -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="section-header p-6 pb-4">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-history text-gray-600 mr-3"></i>
                        الأنشطة الأخيرة
                    </h2>
                </div>
                <div class="p-6 pt-0">
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-green-50 rounded-lg">
                            <i class="fas fa-plus-circle text-green-600 mr-3"></i>
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">تمت إضافة 5 أصناف جديدة</div>
                                <div class="text-sm text-gray-600">اليوم</div>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                            <i class="fas fa-truck text-blue-600 mr-3"></i>
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">تم إكمال 3 تحويلات</div>
                                <div class="text-sm text-gray-600">اليوم</div>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                            <i class="fas fa-clock text-orange-600 mr-3"></i>
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">2 أوامر شراء في انتظار الموافقة</div>
                                <div class="text-sm text-gray-600">أمس</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <button onclick="showAllActivities()" class="text-gray-600 hover:text-gray-800 text-sm">
                            عرض جميع الأنشطة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions Arabic -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="section-header p-6 pb-4">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-bolt text-purple-600 mr-3"></i>
                        الإجراءات السريعة
                    </h2>
                </div>
                <div class="p-6 pt-0">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <button onclick="openModal('item-modal')" class="bg-blue-100 hover:bg-blue-200 text-blue-800 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-plus text-lg mb-2"></i>
                            <div class="font-medium">إضافة صنف جديد</div>
                        </button>
                        <button onclick="openModal('stock-adjustment-modal')" class="bg-green-100 hover:bg-green-200 text-green-800 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-edit text-lg mb-2"></i>
                            <div class="font-medium">تعديل مستوى المخزون</div>
                        </button>
                        <button onclick="openModal('transfer-modal')" class="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-exchange-alt text-lg mb-2"></i>
                            <div class="font-medium">إنشاء تحويل جديد</div>
                        </button>
                        <button onclick="openModal('purchase-order-modal')" class="bg-orange-100 hover:bg-orange-200 text-orange-800 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-shopping-cart text-lg mb-2"></i>
                            <div class="font-medium">أمر شراء جديد</div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
{% include 'core/supply_chain/modals/item_modal.html' %}
{% include 'core/supply_chain/modals/stock_adjustment_modal.html' %}
{% include 'core/supply_chain/modals/movement_modal.html' %}
{% include 'core/supply_chain/modals/transfer_modal.html' %}
{% include 'core/supply_chain/modals/location_modal.html' %}
{% include 'core/supply_chain/modals/purchase_order_modal.html' %}
{% include 'core/supply_chain/modals/supplier_modal.html' %}
{% include 'core/supply_chain/modals/quick_actions_modal.html' %}

{% endblock %}

{% block extra_js %}
<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
}

function openQuickActionModal() {
    openModal('quick-actions-modal');
}

function refreshDashboard() {
    // Show loading state
    const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>{% trans "Refreshing..." %}';
    refreshBtn.disabled = true;
    
    // Simulate refresh
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function showItemLocations() {
    // This would open a modal or navigate to item locations page
    window.location.href = '{% url "core:supply_chain_item_locations" %}';
}

// Auto-refresh alerts every 5 minutes
setInterval(() => {
    fetch('{% url "core:api_alerts_notifications" %}')
        .then(response => response.json())
        .then(data => {
            // Update alerts container
            const alertsContainer = document.getElementById('alerts-container');
            if (data.alerts && data.alerts.length > 0) {
                // Update alerts display
                updateAlertsDisplay(data.alerts);
            }
        })
        .catch(error => console.error('Error fetching alerts:', error));
}, 300000); // 5 minutes

function updateAlertsDisplay(alerts) {
    const container = document.getElementById('alerts-container');
    if (!container) return;
    
    if (alerts.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-check-circle text-4xl mb-3 text-green-500"></i>
                <div class="text-lg font-medium">{% trans "All Good!" %}</div>
                <div class="text-sm">{% trans "No alerts at this time" %}</div>
            </div>
        `;
        return;
    }
    
    let html = '';
    alerts.forEach(alert => {
        const colorClass = alert.type === 'error' ? 'border-red-500 bg-red-50' :
                          alert.type === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                          alert.type === 'info' ? 'border-blue-500 bg-blue-50' : 'border-gray-500 bg-gray-50';
        
        const textColor = alert.type === 'error' ? 'text-red-600' :
                         alert.type === 'warning' ? 'text-yellow-600' :
                         alert.type === 'info' ? 'text-blue-600' : 'text-gray-600';
        
        html += `
            <div class="alert-item p-4 rounded-lg border-l-4 ${colorClass}">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="${alert.icon} ${textColor}"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium">${alert.title}</h3>
                        <div class="mt-1 text-sm">${alert.message}</div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Arabic Action Functions
function showAllItems() {
    // Open items modal or navigate to items page
    window.open('{% url "core:supply_chain_items_list" %}', '_blank');
}

function showLowStockAlerts() {
    // Open low stock alerts modal
    window.open('{% url "core:supply_chain_low_stock" %}', '_blank');
}

function showWarehouseOperations() {
    // Show warehouse operations modal
    window.open('{% url "core:supply_chain_warehouse_hub" %}', '_blank');
}

function showLocationManagement() {
    // Show location management modal
    window.open('{% url "core:supply_chain_locations" %}', '_blank');
}

function showLocationsModal() {
    // Open location management modal
    openModal('location-modal');
}

function showTransfersModal() {
    // Open transfers modal
    openModal('transfer-modal');
}

function showPurchaseOrders() {
    // Navigate to purchase orders
    window.open('{% url "core:supply_chain_purchase_orders" %}', '_blank');
}

function showSuppliersManagement() {
    // Open suppliers modal
    openModal('supplier-modal');
}

function showDetailedAnalytics() {
    // Navigate to detailed analytics
    window.open('{% url "core:supply_chain_reports" %}', '_blank');
}

function showAllActivities() {
    // Navigate to all activities
    window.open('{% url "core:api_recent_activities" %}', '_blank');
}

// Initialize tooltips and other UI elements
document.addEventListener('DOMContentLoaded', function() {
    // Add any initialization code here
});
</script>
{% endblock %}