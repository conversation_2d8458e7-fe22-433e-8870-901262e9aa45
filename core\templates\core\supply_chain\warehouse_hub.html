{% extends 'core/supply_chain/base.html' %}
{% load static %}
{% load i18n %}

{% block page_title %}المستودعات{% endblock %}
{% block page_subtitle %}إدارة شاملة للمخزون والمستودعات والمشتريات{% endblock %}

{% block supply_chain_content %}
<!-- Quick Warehouse Stats -->
<div class="quick-stats">
    <div class="quick-stat">
        <div class="number">12</div>
        <div class="label">مستودع</div>
    </div>
    <div class="quick-stat">
        <div class="number">245</div>
        <div class="label">موقع</div>
    </div>
    <div class="quick-stat">
        <div class="number">78%</div>
        <div class="label">إشغال</div>
    </div>
    <div class="quick-stat">
        <div class="number">43</div>
        <div class="label">أصناف</div>
    </div>
    <div class="quick-stat">
        <div class="number">15</div>
        <div class="label">نقل معلق</div>
    </div>
    <div class="quick-stat">
        <div class="number">8</div>
        <div class="label">طلبات</div>
    </div>
</div>

<!-- Compact Action Buttons -->
<div class="hub-navigation">
    <a href="{% url 'core:supply_chain_warehouses_list' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-list"></i>
        </div>
        <div class="title">قائمة المستودعات</div>
        <div class="description">عرض وإدارة جميع المستودعات</div>
        <div class="stats">
            <div class="stat">
                <div class="number">12</div>
                <div class="label">مستودع</div>
            </div>
            <div class="stat">
                <div class="number">245</div>
                <div class="label">موقع</div>
            </div>
        </div>
    </a>

    <a href="{% url 'core:supply_chain_warehouse_create' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-plus-circle"></i>
        </div>
        <div class="title">مستودع جديد</div>
        <div class="description">إنشاء مستودع جديد</div>
    </a>

    <a href="{% url 'core:supply_chain_locations_list' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-map-marker-alt"></i>
        </div>
        <div class="title">مواقع التخزين</div>
        <div class="description">إدارة المواقع والرفوف</div>
        <div class="stats">
            <div class="stat">
                <div class="number">245</div>
                <div class="label">موقع</div>
            </div>
            <div class="stat">
                <div class="number">78%</div>
                <div class="label">إشغال</div>
            </div>
        </div>
    </a>

    <a href="{% url 'core:supply_chain_transfer_orders' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-exchange-alt"></i>
        </div>
        <div class="title">أوامر النقل</div>
        <div class="description">إدارة النقل بين المستودعات</div>
        <div class="stats">
            <div class="stat">
                <div class="number">15</div>
                <div class="label">معلق</div>
            </div>
            <div class="stat">
                <div class="number">45</div>
                <div class="label">مكتمل</div>
            </div>
        </div>
    </a>

    <a href="{% url 'core:supply_chain_items_list' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-cubes"></i>
        </div>
        <div class="title">الأصناف</div>
        <div class="description">عرض وإدارة أصناف المخزون</div>
        <div class="stats">
            <div class="stat">
                <div class="number">43</div>
                <div class="label">صنف</div>
            </div>
            <div class="stat">
                <div class="number">5</div>
                <div class="label">منخفض</div>
            </div>
        </div>
    </a>

    <a href="{% url 'core:supply_chain_purchase_orders' %}" class="hub-card">
        <div class="icon">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <div class="title">طلبات الشراء</div>
        <div class="description">إدارة أوامر الشراء</div>
        <div class="stats">
            <div class="stat">
                <div class="number">8</div>
                <div class="label">طلب</div>
            </div>
            <div class="stat">
                <div class="number">3</div>
                <div class="label">معلق</div>
            </div>
        </div>
    </a>
</div>

<!-- Quick Visual Summary -->
<div class="visual-summary">
    <div class="row">
        <!-- Warehouse Capacity -->
        <div class="col-md-4">
            <div class="visual-card">
                <h6><i class="fas fa-chart-pie"></i> السعة</h6>
                <div class="capacity-visual">
                    <div class="capacity-circle">
                        <div class="percentage">78%</div>
                        <div class="label">مشغول</div>
                    </div>
                </div>
                <div class="capacity-details">
                    <div class="detail">
                        <span>إجمالي:</span>
                        <strong>15,000 م³</strong>
                    </div>
                    <div class="detail">
                        <span>متاح:</span>
                        <strong>3,300 م³</strong>
                    </div>
                </div>
                <a href="{% url 'core:supply_chain_capacity_report' %}" class="btn btn-sm btn-outline-primary w-100">تقرير مفصل</a>
            </div>
        </div>

        <!-- Warehouse Zones -->
        <div class="col-md-4">
            <div class="visual-card">
                <h6><i class="fas fa-sitemap"></i> المناطق</h6>
                <div class="zones-grid">
                    <div class="zone" data-capacity="85">
                        <span class="zone-name">أ</span>
                        <span class="zone-percent">85%</span>
                    </div>
                    <div class="zone" data-capacity="72">
                        <span class="zone-name">ب</span>
                        <span class="zone-percent">72%</span>
                    </div>
                    <div class="zone" data-capacity="95">
                        <span class="zone-name">ج</span>
                        <span class="zone-percent">95%</span>
                    </div>
                    <div class="zone" data-capacity="60">
                        <span class="zone-name">د</span>
                        <span class="zone-percent">60%</span>
                    </div>
                </div>
                <a href="{% url 'core:supply_chain_item_locations' %}" class="btn btn-sm btn-outline-primary w-100">خريطة المواقع</a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-md-4">
            <div class="visual-card">
                <h6><i class="fas fa-clock"></i> النشاط الأخير</h6>
                <div class="activity-list">
                    <div class="activity-item">
                        <i class="fas fa-plus text-success"></i>
                        <span>إضافة صنف جديد</span>
                        <small>منذ 5 دقائق</small>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-exchange-alt text-info"></i>
                        <span>نقل TR-001</span>
                        <small>منذ 15 دقيقة</small>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-shopping-cart text-primary"></i>
                        <span>طلب شراء PO-024</span>
                        <small>منذ ساعة</small>
                    </div>
                </div>
                <a href="{% url 'core:supply_chain_overview' %}" class="btn btn-sm btn-outline-primary w-100">جميع الأنشطة</a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Bar -->
<div class="quick-actions-bar">
    <h6><i class="fas fa-bolt"></i> إجراءات سريعة</h6>
    <div class="action-buttons">
        <button class="action-btn" onclick="openStockAdjustment()">
            <i class="fas fa-edit"></i>
            <span>تعديل مخزون</span>
        </button>
        <button class="action-btn" onclick="createTransfer()">
            <i class="fas fa-truck"></i>
            <span>نقل سريع</span>
        </button>
        <button class="action-btn" onclick="emergencyReorder()">
            <i class="fas fa-exclamation-circle"></i>
            <span>طلب طارئ</span>
        </button>
        <button class="action-btn" onclick="generateReport()">
            <i class="fas fa-chart-bar"></i>
            <span>تقرير فوري</span>
        </button>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>

/* Visual Summary */
.visual-summary {
    margin-bottom: 20px;
}

.visual-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    height: 100%;
}

.visual-card h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
}

/* Capacity Visual */
.capacity-visual {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
}

.capacity-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(#667eea 0% 78%, #e2e8f0 78% 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.capacity-circle::before {
    content: '';
    position: absolute;
    width: 60px;
    height: 60px;
    background: white;
    border-radius: 50%;
}

.percentage {
    font-size: 1rem;
    font-weight: 600;
    color: #667eea;
    z-index: 1;
}

.label {
    font-size: 0.65rem;
    color: #718096;
    z-index: 1;
}

.capacity-details {
    margin-bottom: 10px;
}

.detail {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    margin-bottom: 3px;
}

/* Zones Grid */
.zones-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 10px;
}

.zone {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    position: relative;
}

.zone[data-capacity="85"] { background: #fed7d7; border-color: #fc8181; }
.zone[data-capacity="72"] { background: #fefcbf; border-color: #f6e05e; }
.zone[data-capacity="95"] { background: #fed7e2; border-color: #f687b3; }
.zone[data-capacity="60"] { background: #c6f6d5; border-color: #68d391; }

.zone-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.zone-percent {
    font-size: 0.7rem;
    display: block;
    margin-top: 2px;
}

/* Activity List */
.activity-list {
    margin-bottom: 10px;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f1f5f9;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item i {
    margin-left: 8px;
    width: 16px;
}

.activity-item span {
    flex: 1;
    font-size: 0.75rem;
    font-weight: 500;
}

.activity-item small {
    font-size: 0.65rem;
    color: #718096;
}

/* Quick Actions Bar */
.quick-actions-bar {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e2e8f0;
}

.quick-actions-bar h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
}



/* Responsive */
@media (max-width: 768px) {
    .quick-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}
</style>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
function openStockAdjustment() {
    window.location.href = "{% url 'core:supply_chain_stock_adjustment' %}";
}

function createTransfer() {
    window.location.href = "{% url 'core:supply_chain_transfer_create' %}";
}

function emergencyReorder() {
    window.location.href = "{% url 'core:supply_chain_emergency_reorder' %}";
}

function generateReport() {
    window.location.href = "{% url 'core:supply_chain_reports' %}";
}

// Auto-refresh data every 30 seconds
setInterval(function() {
    // Add AJAX calls here to update stats if needed
}, 30000);
</script>
{% endblock %} 