{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% csrf_token %}

{% block title %}{% trans "Supply Chain Management Hub" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/supply-chain-dashboard.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* Override any dark backgrounds */
    body {
        background-color: #f8fafc !important;
    }
    
    .dashboard-main {
        background-color: #f8fafc !important;
    }

    /* Prevent auto scroll */
    html {
        scroll-behavior: auto !important;
    }
    
    * {
        scroll-behavior: auto !important;
    }

    /* Tab container styling */
    .tab-container {
        background: white;
        border-radius: 0.75rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 1rem;
        overflow: hidden;
        display: flex;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .tab-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
        border: none;
        background: transparent;
        cursor: pointer;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        border-left: 1px solid #e5e7eb;
        color: #6b7280;
        text-decoration: none;
        min-width: 120px;
        flex: 1;
        gap: 0.5rem;
        min-height: 80px;
        scroll-behavior: auto !important;
    }

    .tab-button:first-child {
        border-left: none;
    }

    .tab-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    }

    .tab-button.active {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .tab-button .tab-icon {
        width: 1.5rem;
        height: 1.5rem;
        margin-bottom: 0.25rem;
    }

    .tab-button .tab-label {
        font-weight: 600;
        text-align: center;
        font-size: 0.875rem;
    }

    .tab-button .tab-count {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 9999px;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 700;
        min-width: 1.5rem;
        text-align: center;
        line-height: 1;
    }

    .tab-button.active .tab-count {
        background-color: rgba(255, 255, 255, 0.2);
    }

    /* Tab content styling */
    .tab-content {
        display: none;
        animation: fadeIn 0.3s ease;
        padding-top: 0.5rem;
    }

    .tab-content.active {
        display: block;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Card styling */
    .dashboard-card {
        background: white;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 1rem;
        border-bottom: 1px solid #e5e7eb;
        text-align: center;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* Action buttons */
    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        scroll-behavior: auto !important;
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-warning { background-color: #f59e0b; color: white; }
    .btn-info { background-color: #06b6d4; color: white; }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Grid layouts */
    .grid-2 { display: grid; grid-template-columns: repeat(2, 1fr); gap: 0.75rem; }
    .grid-3 { display: grid; grid-template-columns: repeat(3, 1fr); gap: 0.75rem; }
    .grid-4 { display: grid; grid-template-columns: repeat(4, 1fr); gap: 0.75rem; }

    @media (max-width: 768px) {
        .grid-2, .grid-3, .grid-4 { 
            grid-template-columns: 1fr; 
            gap: 0.5rem;
        }
    }

    /* Statistics styling */
    .stat-card {
        background: white;
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        border: 1px solid #e5e7eb;
        text-align: center;
        min-height: auto;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .stat-number {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 0.125rem;
        line-height: 1.1;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.6875rem;
        line-height: 1.2;
        font-weight: 500;
    }

    .stat-card .flex {
        margin-bottom: 0.125rem;
    }

    .stat-card .flex i {
        font-size: 1rem;
    }

    /* Compact statistics layout */
    .stat-card .flex.items-center.justify-center {
        min-height: auto;
        height: auto;
    }

    .stat-card .flex.items-center.justify-center.mb-2 {
        margin-bottom: 0.125rem;
    }

    /* Extra compact styles */
    .stat-card .text-2xl {
        font-size: 1rem !important;
    }

    .stat-card .mr-3 {
        margin-right: 0.25rem !important;
    }

    .stat-card .mr-1 {
        margin-right: 0.125rem !important;
    }

    .stat-card .mb-2 {
        margin-bottom: 0.125rem !important;
    }

    .stat-card .mb-3 {
        margin-bottom: 0.25rem !important;
    }

    /* Loading state */
    .loading {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
    }

    /* RTL Support */
    html[dir="rtl"] .tab-button {
        border-left: none;
        border-right: 1px solid #e5e7eb;
    }

    html[dir="rtl"] .tab-button:first-child {
        border-right: none;
    }

    html[dir="rtl"] .tab-button:last-child {
        border-right: 1px solid #e5e7eb;
    }

    /* Modal Enhancements - Fixed Positioning */
    [id$="Modal"] {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        backdrop-filter: blur(2px);
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 1rem !important;
        margin: 0 !important;
        transform: none !important;
        overflow: auto !important;
    }

    [id$="Modal"].hidden {
        display: none !important;
    }

    /* Ensure modal content stays centered */
    [id$="Modal"] .bg-white {
        position: relative !important;
        margin: auto !important;
        transform: none !important;
        max-height: calc(100vh - 2rem) !important;
        max-width: calc(100vw - 2rem) !important;
        overflow-y: auto !important;
    }

    /* Prevent any hover or mouse effects on modals */
    [id$="Modal"]:hover {
        transform: none !important;
    }

    [id$="Modal"] *:hover {
        transform: none !important;
    }

    [id$="Modal"] .bg-white {
        animation: modalSlideIn 0.3s ease-out;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border: 1px solid #e5e7eb;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Enhanced button hover effects */
    .action-btn {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.4s, height 0.4s;
    }

    .action-btn:hover::before {
        width: 300px;
        height: 300px;
    }

    /* Table styling improvements */
    table th {
        position: sticky;
        top: 0;
        z-index: 10;
        background: #f9fafb !important;
    }

    /* Enhanced scrollbar */
    .overflow-y-auto::-webkit-scrollbar {
        width: 8px;
    }

    .overflow-y-auto::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .overflow-y-auto::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .overflow-y-auto::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Improved form focus states */
    input:focus, select:focus, textarea:focus {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Loading spinner enhancement */
    .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Enhanced popup animations */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    @keyframes scaleIn {
        from {
            transform: scale(0.95);
            opacity: 0;
        }
        to {
            transform: scale(1);
            opacity: 1;
        }
    }
    
    @keyframes progressBar {
        from {
            width: 100%;
        }
        to {
            width: 0%;
        }
    }
    
    /* Enhanced notification styling */
    .notification-enter {
        animation: slideInRight 0.3s ease-out;
    }
    
    .notification-exit {
        animation: slideOutRight 0.3s ease-in;
    }
    
    /* Dialog modal backdrop styling */
    .dialog-backdrop {
        backdrop-filter: blur(4px);
    }
    
    /* Custom scrollbar for modal content */
    .modal-content::-webkit-scrollbar {
        width: 6px;
    }
    
    .modal-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 6px;
    }
    
    .modal-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 6px;
    }
    
    .modal-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
    
    /* Enhanced button hover effects for dialogs */
    .dialog-button {
        transition: all 0.2s ease;
    }
    
    .dialog-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    /* Notification progress bar animation */
    .notification-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: currentColor;
        opacity: 0.3;
        border-radius: 0 0 8px 8px;
    }
    
    /* RTL support for notifications */
    html[dir="rtl"] .notification-enter {
        animation: slideInLeft 0.3s ease-out;
    }
    
    html[dir="rtl"] .notification-exit {
        animation: slideOutLeft 0.3s ease-in;
    }
    
    @keyframes slideInLeft {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutLeft {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(-100%);
            opacity: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50" dir="rtl">
    <div class="container mx-auto px-4 py-4">
        
        

        <!-- Tab Navigation -->
        <div class="tab-container">
            <button class="tab-button active" onclick="showTab('main-dashboard', this)">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                </svg>
                <span class="tab-label">لوحة التحكم الرئيسية</span>
                <span class="tab-count">1</span>
            </button>
            
          
            
            <button class="tab-button" onclick="showTab('inventory-management', this)">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <span class="tab-label">إدارة المخزون</span>
                <span class="tab-count">245</span>
            </button>
            
            <button class="tab-button" onclick="showTab('warehouse-operations', this)">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="tab-label">عمليات المستودع</span>
                <span class="tab-count">8</span>
            </button>
            
            <button class="tab-button" onclick="showTab('purchasing', this)">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
                </svg>
                <span class="tab-label">المشتريات والتوريد</span>
                <span class="tab-count">7</span>
            </button>
        </div>

        <!-- Tab Content -->
        
        <!-- Main Dashboard Tab -->
        <div id="main-dashboard" class="tab-content active">
            <div class="grid-3 mb-3">
                <!-- Overview Statistics -->
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-boxes text-blue-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-blue-600">245</div>
                    <div class="stat-label">
                        <i class="fas fa-boxes text-blue-500 mr-1"></i>
                        إجمالي الأصناف
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-warehouse text-green-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-green-600">8</div>
                    <div class="stat-label">
                        <i class="fas fa-warehouse text-green-500 mr-1"></i>
                        المستودعات النشطة
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-shopping-cart text-orange-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-orange-600">7</div>
                    <div class="stat-label">
                        <i class="fas fa-shopping-cart text-orange-500 mr-1"></i>
                        أوامر الشراء المفتوحة
                    </div>
                </div>
            </div>
            
            <div class="grid-2">
                <!-- Quick Access Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">الوصول السريع</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button onclick="showTab('inventory-management')" class="action-btn btn-primary w-full">
                                <i class="fas fa-boxes"></i>
                                إدارة المخزون
                            </button>
                            <button onclick="showTab('warehouse-operations')" class="action-btn btn-success w-full">
                                <i class="fas fa-warehouse"></i>
                                عمليات المستودع
                            </button>
                            <button onclick="showTab('purchasing')" class="action-btn btn-warning w-full">
                                <i class="fas fa-shopping-cart"></i>
                                المشتريات والتوريد
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- System Status -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">حالة النظام</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">صحة المخزون</span>
                                    <span class="text-sm font-bold text-green-600">92%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 92%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">كفاءة المستودع</span>
                                    <span class="text-sm font-bold text-blue-600">88%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 88%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">أداء المشتريات</span>
                                    <span class="text-sm font-bold text-yellow-600">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-600 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unified Dashboard Tab -->
        <div id="unified-dashboard" class="tab-content">
            <div class="loading">
                <i class="fas fa-spinner fa-spin text-2xl mb-4"></i>
                <div>جاري تحميل لوحة التحكم الموحدة...</div>
            </div>
        </div>

        <!-- Inventory Management Tab -->
        <div id="inventory-management" class="tab-content">
            <div class="grid-4 mb-3">
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-boxes text-blue-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-blue-600">245</div>
                    <div class="stat-label">
                        <i class="fas fa-boxes text-blue-500 mr-1"></i>
                        إجمالي الأصناف
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-exclamation-triangle text-red-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-red-600">12</div>
                    <div class="stat-label">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-1"></i>
                        مخزون منخفض
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-tags text-green-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-green-600">15</div>
                    <div class="stat-label">
                        <i class="fas fa-tags text-green-500 mr-1"></i>
                        الفئات
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-exchange-alt text-purple-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-purple-600">1,234</div>
                    <div class="stat-label">
                        <i class="fas fa-exchange-alt text-purple-500 mr-1"></i>
                        إجمالي الحركات
                    </div>
                </div>
            </div>
            
            <div class="grid-2">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">إدارة الأصناف</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button onclick="openModal('addItemModal')" class="action-btn btn-primary w-full">
                                <i class="fas fa-plus"></i>
                                إضافة صنف جديد
                            </button>
                            <button onclick="openModal('viewItemsModal')" class="action-btn btn-info w-full">
                                <i class="fas fa-list"></i>
                                عرض جميع الأصناف
                            </button>
                            <button onclick="openModal('lowStockModal')" class="action-btn btn-warning w-full">
                                <i class="fas fa-exclamation-triangle"></i>
                                تنبيهات المخزون المنخفض
                            </button>
                        </div>
                    </div>
                </div>
                
                
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">حركات المخزون</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button onclick="openModal('addMovementModal')" class="action-btn btn-success w-full">
                                <i class="fas fa-plus-circle"></i>
                                تسجيل حركة جديدة
                            </button>
                            <button onclick="openModal('viewMovementsModal')" class="action-btn btn-info w-full">
                                <i class="fas fa-history"></i>
                                عرض سجل الحركات
                            </button>
                            <button onclick="openModal('adjustStockModal')" class="action-btn btn-warning w-full">
                                <i class="fas fa-edit"></i>
                                تعديل مستوى المخزون
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Warehouse Operations Tab -->
        <div id="warehouse-operations" class="tab-content">
            <div class="grid-3 mb-3">
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-warehouse text-green-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-green-600">8</div>
                    <div class="stat-label">
                        <i class="fas fa-warehouse text-green-500 mr-1"></i>
                        المستودعات النشطة
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-clock text-orange-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-orange-600">3</div>
                    <div class="stat-label">
                        <i class="fas fa-clock text-orange-500 mr-1"></i>
                        التحويلات المعلقة
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-map-marker-alt text-blue-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-blue-600">156</div>
                    <div class="stat-label">
                        <i class="fas fa-map-marker-alt text-blue-500 mr-1"></i>
                        المواقع المسجلة
                    </div>
                </div>
            </div>
            
            <div class="grid-2">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">إدارة المستودعات</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button onclick="openModal('addWarehouseModal')" class="action-btn btn-primary w-full">
                                <i class="fas fa-warehouse"></i>
                                إضافة مستودع جديد
                            </button>
                            <button onclick="openModal('manageLocationsModal')" class="action-btn btn-info w-full">
                                <i class="fas fa-map-marker-alt"></i>
                                إدارة المواقع
                            </button>
                            <button onclick="openModal('trackItemsModal')" class="action-btn btn-success w-full">
                                <i class="fas fa-search-location"></i>
                                تتبع مواضع الأصناف
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">التحويلات</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button onclick="openModal('addTransferModal')" class="action-btn btn-primary w-full">
                                <i class="fas fa-truck"></i>
                                إنشاء تحويل جديد
                            </button>
                            <button onclick="openModal('pendingTransfersModal')" class="action-btn btn-warning w-full">
                                <i class="fas fa-clock"></i>
                                التحويلات المعلقة
                            </button>
                            <button onclick="openModal('transferHistoryModal')" class="action-btn btn-info w-full">
                                <i class="fas fa-history"></i>
                                سجل التحويلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchasing Tab -->
        <div id="purchasing" class="tab-content">
            <div class="grid-3 mb-4">
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-shopping-cart text-orange-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-orange-600">7</div>
                    <div class="stat-label">
                        <i class="fas fa-shopping-cart text-orange-500 mr-1"></i>
                        أوامر الشراء المفتوحة
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-users text-blue-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-blue-600">15</div>
                    <div class="stat-label">
                        <i class="fas fa-users text-blue-500 mr-1"></i>
                        الموردون النشطون
                    </div>
                </div>
                <div class="stat-card">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-chart-line text-green-600 text-2xl mr-3"></i>
                    </div>
                    <div class="stat-number text-green-600">89%</div>
                    <div class="stat-label">
                        <i class="fas fa-chart-line text-green-500 mr-1"></i>
                        معدل الأداء
                    </div>
                </div>
            </div>
            
            <div class="grid-2">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">أوامر الشراء</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button onclick="openModal('addPurchaseOrderModal')" class="action-btn btn-primary w-full">
                                <i class="fas fa-plus"></i>
                                إنشاء أمر شراء جديد
                            </button>
                            <button onclick="openModal('pendingPurchaseOrdersModal')" class="action-btn btn-warning w-full">
                                <i class="fas fa-clock"></i>
                                الأوامر في انتظار الموافقة
                            </button>
                            <button onclick="openModal('viewPurchaseOrdersModal')" class="action-btn btn-info w-full">
                                <i class="fas fa-list"></i>
                                عرض جميع الأوامر
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">إدارة الموردين</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button onclick="openModal('addSupplierModal')" class="action-btn btn-primary w-full">
                                <i class="fas fa-user-plus"></i>
                                إضافة مورد جديد
                            </button>
                            <button onclick="openModal('viewSuppliersModal')" class="action-btn btn-info w-full">
                                <i class="fas fa-users"></i>
                                عرض جميع الموردين
                            </button>
                            <button onclick="openModal('supplierPerformanceModal')" class="action-btn btn-success w-full">
                                <i class="fas fa-chart-line"></i>
                                تقييم أداء الموردين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Popups -->
<!-- Add Item Modal -->
<div id="addItemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 xl:w-1/2 max-h-screen overflow-y-auto">
        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-plus"></i>
                إضافة صنف جديد
            </h3>
            <button onclick="closeModal('addItemModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="addItemForm" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-barcode text-gray-500"></i> رمز الصنف
                    </label>
                    <input type="text" name="sku" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل رمز الصنف">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-tag text-gray-500"></i> اسم الصنف
                    </label>
                    <input type="text" name="name" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل اسم الصنف" required>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-align-left text-gray-500"></i> الوصف
                    </label>
                    <textarea name="description" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل وصف الصنف"></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-cubes text-gray-500"></i> الكمية الحالية
                    </label>
                    <input type="number" name="quantity" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0.00">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-dollar-sign text-gray-500"></i> سعر الوحدة
                    </label>
                    <input type="number" name="unit_price" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0.00">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-level-down-alt text-gray-500"></i> الحد الأدنى للمخزون
                    </label>
                    <input type="number" name="min_stock_level" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="10.00">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-ruler text-gray-500"></i> وحدة القياس
                    </label>
                    <select name="unit_of_measurement" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">اختر وحدة القياس</option>
                        <option value="piece">قطعة</option>
                        <option value="kg">كيلوجرام</option>
                        <option value="liter">لتر</option>
                        <option value="meter">متر</option>
                    </select>
                </div>
            </div>
            
            <!-- Batch Tracking Section -->
            <div class="border-t border-gray-200 pt-6 mt-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <i class="fas fa-layer-group text-blue-600"></i>
                    إعدادات تتبع الدفعات
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-tasks text-gray-500"></i> مستوى تتبع الدفعات
                        </label>
                        <select name="batch_tracking_level" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" onchange="toggleBatchFields(this.value)">
                            <option value="none">لا يوجد تتبع دفعات</option>
                            <option value="basic">تتبع أساسي للدفعات</option>
                            <option value="full">تتبع كامل مع تاريخ الانتهاء</option>
                        </select>
                    </div>
                    <div id="inventory-method-field" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-sort text-gray-500"></i> طريقة الجرد
                        </label>
                        <select name="inventory_method" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="fifo">الأول يدخل الأول يخرج (FIFO)</option>
                            <option value="filo">الأول يدخل الأخير يخرج (FILO)</option>
                            <option value="manual">اختيار يدوي</option>
                        </select>
                    </div>
                    <div id="batch-prefix-field" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-hashtag text-gray-500"></i> بادئة رقم الدفعة
                        </label>
                        <input type="text" name="batch_number_prefix" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="مثال: BP" maxlength="10">
                    </div>
                    <div id="auto-batch-field" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                            <input type="checkbox" name="auto_generate_batch_number" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <i class="fas fa-magic text-gray-500"></i>
                            توليد رقم الدفعة تلقائياً
                        </label>
                    </div>
                    <div id="expiry-tracking-field" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                            <input type="checkbox" name="requires_expiry_tracking" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <i class="fas fa-calendar-times text-gray-500"></i>
                            تتبع تاريخ الانتهاء مطلوب
                        </label>
                    </div>
                    <div id="shelf-life-field" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-hourglass-half text-gray-500"></i> مدة الصلاحية الافتراضية (أيام)
                        </label>
                        <input type="number" name="default_shelf_life_days" min="1" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="365">
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('addItemModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ الصنف
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Movement Modal -->
<div id="addMovementModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto">
        <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-plus-circle"></i>
                تسجيل حركة مخزون جديدة
            </h3>
            <button onclick="closeModal('addMovementModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="addMovementForm" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-box text-gray-500"></i> الصنف
                    </label>
                    <select name="item" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" required>
                        <option value="">اختر الصنف</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-exchange-alt text-gray-500"></i> نوع الحركة
                    </label>
                    <select name="movement_type" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" required>
                        <option value="">اختر نوع الحركة</option>
                        <option value="in">دخول</option>
                        <option value="out">خروج</option>
                        <option value="adjustment">تسوية</option>
                        <option value="transfer">تحويل</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-cubes text-gray-500"></i> الكمية
                    </label>
                    <input type="number" name="quantity" step="0.01" min="0.01" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="أدخل الكمية" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-file-alt text-gray-500"></i> رقم المرجع
                    </label>
                    <input type="text" name="reference" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="رقم الفاتورة أو المرجع">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-warehouse text-gray-500"></i> المستودع
                    </label>
                    <select name="warehouse" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">اختر المستودع</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-comment text-gray-500"></i> ملاحظات
                    </label>
                    <textarea name="notes" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="أدخل ملاحظات إضافية"></textarea>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('addMovementModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    تسجيل الحركة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Warehouse Modal -->
<div id="addWarehouseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 xl:w-3/4 max-h-screen overflow-y-auto">
        <div class="bg-purple-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-warehouse"></i>
                إضافة مستودع جديد
            </h3>
            <button onclick="closeModal('addWarehouseModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="addWarehouseForm" class="p-6">
            <!-- Warehouse Type and Organizational Linking Section -->
            <div class="border-b border-gray-200 pb-6 mb-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                    <i class="fas fa-sitemap text-purple-600"></i>
                    نوع المستودع والربط التنظيمي
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-layer-group text-gray-500"></i> نوع المستودع *
                        </label>
                        <select name="location_type" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" required>
                            <option value="">اختر نوع المستودع</option>
                            <option value="MAIN_WAREHOUSE">مستودع رئيسي</option>
                            <option value="WAREHOUSE">مستودع فرعي</option>
                            <option value="SHOWROOM">صالة عرض</option>
                            <option value="WORKSHOP">ورشة</option>
                            <option value="OFFICE">مكتب</option>
                            <option value="STORAGE_ROOM">غرفة تخزين</option>
                            <option value="COLD_STORAGE">تخزين بارد</option>
                            <option value="HAZMAT_STORAGE">تخزين مواد خطرة</option>
                            <option value="RECEIVING_DOCK">رصيف الاستلام</option>
                            <option value="SHIPPING_DOCK">رصيف الشحن</option>
                            <option value="PICKING_AREA">منطقة التجميع</option>
                            <option value="PACKING_AREA">منطقة التعبئة</option>
                            <option value="QUALITY_CONTROL">مراقبة الجودة</option>
                            <option value="RETURNS_AREA">منطقة المرتجعات</option>
                            <option value="QUARANTINE">الحجر الصحي</option>
                            <option value="TRANSIT">نقل مؤقت</option>
                            <option value="VIRTUAL">موقع افتراضي</option>
                            <option value="EXTERNAL">موقع خارجي</option>
                            <option value="OTHER">أخرى</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-star text-gray-500"></i> تصنيف المستودع
                        </label>
                        <select name="warehouse_priority" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            <option value="primary">مستودع رئيسي لمركز الخدمة</option>
                            <option value="secondary">مستودع ثانوي لمركز الخدمة</option>
                            <option value="independent">مستودع مستقل</option>
                        </select>
                    </div>
                </div>
                
                <!-- Organizational Hierarchy Section -->
                <div class="mt-4">
                    <h5 class="text-md font-medium text-gray-800 mb-3 flex items-center gap-2">
                        <i class="fas fa-network-wired text-purple-500"></i>
                        الربط بالهيكل التنظيمي
                    </h5>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-crown text-gray-500"></i> الامتياز
                            </label>
                            <select name="franchise" id="warehouse-franchise-select" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" onchange="loadCompaniesForWarehouse(this.value)">
                                <option value="">اختر الامتياز</option>
                                <!-- Will be populated via API -->
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-building text-gray-500"></i> الشركة
                            </label>
                            <select name="company" id="warehouse-company-select" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" onchange="loadServiceCentersForWarehouse(this.value)" disabled>
                                <option value="">اختر الشركة أولاً</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-store text-gray-500"></i> مركز الخدمة
                            </label>
                            <select name="service_center" id="warehouse-service-center-select" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" disabled>
                                <option value="">اختر مركز الخدمة</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p class="text-sm text-blue-700 flex items-center gap-2">
                            <i class="fas fa-info-circle"></i>
                            يمكن ربط المستودع بمركز خدمة محدد أو تركه كمستودع مستقل على مستوى الشركة أو الامتياز
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Basic Information Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-warehouse text-gray-500"></i> اسم المستودع *
                    </label>
                    <input type="text" name="name" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="أدخل اسم المستودع" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-hashtag text-gray-500"></i> رمز المستودع *
                    </label>
                    <input type="text" name="code" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="أدخل رمز المستودع" required>
                </div>
                <div class="md:col-span-2 lg:col-span-3">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt text-gray-500"></i> العنوان
                    </label>
                    <textarea name="address" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="أدخل عنوان المستودع"></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-city text-gray-500"></i> المدينة
                    </label>
                    <input type="text" name="city" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="أدخل المدينة">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-phone text-gray-500"></i> رقم الهاتف
                    </label>
                    <input type="tel" name="phone" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="أدخل رقم الهاتف">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user text-gray-500"></i> اسم المسؤول
                    </label>
                    <input type="text" name="contact_name" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="أدخل اسم المسؤول">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope text-gray-500"></i> البريد الإلكتروني
                    </label>
                    <input type="email" name="email" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="أدخل البريد الإلكتروني">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-expand text-gray-500"></i> المساحة (متر مربع)
                    </label>
                    <input type="number" name="area_sqm" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="المساحة">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-layer-group text-gray-500"></i> الحد الأقصى للأصناف
                    </label>
                    <input type="number" name="max_items" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="الحد الأقصى">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('addWarehouseModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ المستودع
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Supplier Modal -->
<div id="addSupplierModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto">
        <div class="bg-orange-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-user-plus"></i>
                إضافة مورد جديد
            </h3>
            <button onclick="closeModal('addSupplierModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="addSupplierForm" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user text-gray-500"></i> اسم المورد
                    </label>
                    <input type="text" name="name" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="أدخل اسم المورد" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope text-gray-500"></i> البريد الإلكتروني
                    </label>
                    <input type="email" name="email" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="أدخل البريد الإلكتروني">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-phone text-gray-500"></i> رقم الهاتف
                    </label>
                    <input type="tel" name="phone" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="أدخل رقم الهاتف">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt text-gray-500"></i> العنوان
                    </label>
                    <textarea name="address" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="أدخل عنوان المورد"></textarea>
                </div>
                <div class="md:col-span-2">
                    <label class="flex items-center space-x-3">
                        <input type="checkbox" name="is_active" checked class="w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2">
                        <span class="text-sm font-medium text-gray-700">مورد نشط</span>
                    </label>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('addSupplierModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ المورد
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Purchase Order Modal -->
<div id="addPurchaseOrderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 max-h-screen overflow-y-auto">
        <div class="bg-indigo-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-plus"></i>
                إنشاء أمر شراء جديد
            </h3>
            <button onclick="closeModal('addPurchaseOrderModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="addPurchaseOrderForm" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-hashtag text-gray-500"></i> رقم أمر الشراء
                    </label>
                    <input type="text" name="order_number" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="سيتم إنشاؤه تلقائياً" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user text-gray-500"></i> المورد
                    </label>
                    <select name="supplier" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                        <option value="">اختر المورد</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> تاريخ الطلب
                    </label>
                    <input type="date" name="order_date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-check text-gray-500"></i> تاريخ التسليم المتوقع
                    </label>
                    <input type="date" name="expected_delivery_date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-comment text-gray-500"></i> الملاحظات
                    </label>
                    <textarea name="notes" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="أدخل ملاحظات إضافية"></textarea>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('addPurchaseOrderModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    إنشاء أمر الشراء
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Items Modal -->
<div id="viewItemsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-list"></i>
                عرض جميع الأصناف
            </h3>
            <button onclick="closeModal('viewItemsModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Search and Filter -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search text-gray-500"></i> البحث
                    </label>
                    <input type="text" id="itemSearch" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="ابحث بالاسم أو الرمز">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-tags text-gray-500"></i> الفئة
                    </label>
                    <select id="itemCategoryFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">جميع الفئات</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-layer-group text-gray-500"></i> حالة المخزون
                    </label>
                    <select id="itemStockFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        <option value="in_stock">متوفر</option>
                        <option value="low_stock">منخفض</option>
                        <option value="out_of_stock">نافد</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-boxes text-gray-500"></i> تتبع الدفعات
                    </label>
                    <select id="batchTrackingFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">جميع الأنواع</option>
                        <option value="none">بدون تتبع</option>
                        <option value="basic">تتبع أساسي</option>
                        <option value="full">تتبع كامل</option>
                    </select>
                </div>
            </div>
            
            <!-- Items Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-barcode text-gray-600"></i> الرمز
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-tag text-gray-600"></i> الاسم
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cubes text-gray-600"></i> الكمية
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-layer-group text-gray-600"></i> الدفعات
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-dollar-sign text-gray-600"></i> السعر
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cogs text-gray-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="itemsTableBody">
                        <tr>
                            <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-blue-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Batch Details Modal -->
<div id="batchDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-indigo-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-layer-group"></i>
                تفاصيل الدفعات
            </h3>
            <button onclick="closeModal('batchDetailsModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Item Information -->
            <div id="batchItemInfo" class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="text-sm font-medium text-gray-600">اسم الصنف:</label>
                        <div id="batchItemName" class="font-semibold text-gray-900"></div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-600">رمز الصنف:</label>
                        <div id="batchItemSku" class="font-semibold text-gray-900"></div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-600">مستوى التتبع:</label>
                        <div id="batchTrackingLevel" class="font-semibold"></div>
                    </div>
                </div>
            </div>

            <!-- Batch Filter -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search text-gray-500"></i> بحث في الدفعات
                    </label>
                    <input type="text" id="batchSearch" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="ابحث برقم الدفعة">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-clock text-gray-500"></i> حالة الانتهاء
                    </label>
                    <select id="expiryStatusFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        <option value="valid">صالحة</option>
                        <option value="expiring_soon">تنتهي قريباً</option>
                        <option value="expired">منتهية الصلاحية</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-sort text-gray-500"></i> ترتيب حسب
                    </label>
                    <select id="batchSortBy" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        <option value="received_date">تاريخ الاستلام</option>
                        <option value="expiry_date">تاريخ الانتهاء</option>
                        <option value="batch_number">رقم الدفعة</option>
                        <option value="quantity">الكمية</option>
                    </select>
                </div>
            </div>

            <!-- Batches Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-hashtag text-gray-600"></i> رقم الدفعة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cubes text-gray-600"></i> الكمية
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-lock text-gray-600"></i> محجوزة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-check text-gray-600"></i> متاحة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-calendar text-gray-600"></i> تاريخ الاستلام
                            </th>
                            <th id="expiryColumn" class="border border-gray-300 px-4 py-2 text-right" style="display: none;">
                                <i class="fas fa-calendar-times text-gray-600"></i> تاريخ الانتهاء
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-info-circle text-gray-600"></i> الحالة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cogs text-gray-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="batchesTableBody">
                        <tr>
                            <td colspan="8" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-indigo-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Add New Batch Button -->
            <div class="mt-6 flex justify-end">
                <button onclick="openAddBatchModal()" class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 flex items-center gap-2">
                    <i class="fas fa-plus"></i>
                    إضافة دفعة جديدة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Batch Modal -->
<div id="addBatchModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto">
        <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-plus"></i>
                إضافة دفعة جديدة
            </h3>
            <button onclick="closeModal('addBatchModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="addBatchForm" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-box text-gray-500"></i> الصنف
                    </label>
                    <select name="item" id="batchItemSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" required onchange="updateBatchForm(this.value)">
                        <option value="">اختر الصنف</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-hashtag text-gray-500"></i> رقم الدفعة
                    </label>
                    <input type="text" name="batch_number" id="batchNumberInput" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="سيتم التوليد تلقائياً" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-cubes text-gray-500"></i> الكمية الأولية
                    </label>
                    <input type="number" name="initial_quantity" step="0.01" min="0.01" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="أدخل الكمية" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-dollar-sign text-gray-500"></i> سعر الشراء
                    </label>
                    <input type="number" name="purchase_price" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="0.00">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> تاريخ الاستلام
                    </label>
                    <input type="date" name="received_date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" required>
                </div>
                <div id="manufacturedDateField" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-industry text-gray-500"></i> تاريخ التصنيع
                    </label>
                    <input type="date" name="manufactured_date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                <div id="expiryDateField" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-times text-gray-500"></i> تاريخ الانتهاء
                    </label>
                    <input type="date" name="expiry_date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-truck text-gray-500"></i> مرجع دفعة المورد
                    </label>
                    <input type="text" name="supplier_batch_ref" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="رقم دفعة المورد">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt text-gray-500"></i> موقع المستودع
                    </label>
                    <input type="text" name="warehouse_location" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="مثال: A1-S2-P3">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-comment text-gray-500"></i> ملاحظات
                    </label>
                    <textarea name="notes" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="أدخل ملاحظات إضافية"></textarea>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('addBatchModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ الدفعة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Low Stock Modal -->
<div id="lowStockModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-4/5 lg:w-3/4 max-h-screen overflow-y-auto">
        <div class="bg-yellow-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-exclamation-triangle"></i>
                تنبيهات المخزون المنخفض
            </h3>
            <button onclick="closeModal('lowStockModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <div class="mb-4 p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
                    <p class="text-yellow-800">الأصناف التي وصلت إلى الحد الأدنى للمخزون أو أقل</p>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-red-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-barcode text-red-600"></i> الرمز
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-tag text-red-600"></i> الاسم
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cubes text-red-600"></i> الكمية الحالية
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-level-down-alt text-red-600"></i> الحد الأدنى
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-layer-group text-red-600"></i> الدفعات
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-chart-line text-red-600"></i> الحالة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cogs text-red-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="lowStockTableBody">
                        <tr>
                            <td colspan="7" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-yellow-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Quick Adjust Stock Modal -->
<div id="quickAdjustStockModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto">
        <div class="bg-orange-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-edit"></i>
                تعديل سريع للمخزون
            </h3>
            <button onclick="closeModal('quickAdjustStockModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="quickAdjustStockForm" class="p-6">
            <input type="hidden" id="quickAdjustItemId" name="item_id">
            
            <!-- Item Info Display -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="text-sm font-medium text-gray-600">اسم الصنف:</label>
                        <div id="quickAdjustItemName" class="font-semibold text-gray-900"></div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-600">الرمز:</label>
                        <div id="quickAdjustItemSku" class="font-semibold text-gray-900"></div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-600">الكمية الحالية:</label>
                        <div id="quickAdjustCurrentQty" class="font-semibold text-red-600"></div>
                    </div>
                </div>
            </div>

            <!-- Batch Selection (if applicable) -->
            <div id="batchSelectionSection" class="mb-4" style="display: none;">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-layer-group text-gray-500"></i> اختر الدفعة
                </label>
                <select id="batchSelect" name="batch_id" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    <option value="">اختر الدفعة للتعديل</option>
                </select>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-plus text-gray-500"></i> إضافة كمية
                    </label>
                    <input type="number" name="add_quantity" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="0.00">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-minus text-gray-500"></i> طرح كمية
                    </label>
                    <input type="number" name="subtract_quantity" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="0.00">
                </div>
            </div>

            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-list-alt text-gray-500"></i> سبب التعديل
                </label>
                <select name="adjustment_reason" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" required>
                    <option value="">اختر السبب</option>
                    <option value="stock_replenishment">إعادة تعبئة المخزون</option>
                    <option value="inventory_count">جرد مخزون</option>
                    <option value="damage">تلف</option>
                    <option value="loss">فقدان</option>
                    <option value="correction">تصحيح خطأ</option>
                    <option value="transfer">تحويل</option>
                    <option value="other">أخرى</option>
                </select>
            </div>

            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-comment text-gray-500"></i> ملاحظات
                </label>
                <textarea name="notes" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="أدخل ملاحظات إضافية"></textarea>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('quickAdjustStockModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ التعديل
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Quick Purchase Order Modal -->
<div id="quickPurchaseOrderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 max-h-screen overflow-y-auto">
        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-shopping-cart"></i>
                إنشاء أمر شراء سريع
            </h3>
            <button onclick="closeModal('quickPurchaseOrderModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="quickPurchaseOrderForm" class="p-6">
            <input type="hidden" id="purchaseOrderItemId" name="item_id">
            
            <!-- Item Info Display -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="text-sm font-medium text-gray-600">اسم الصنف:</label>
                        <div id="purchaseOrderItemName" class="font-semibold text-gray-900"></div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-600">الرمز:</label>
                        <div id="purchaseOrderItemSku" class="font-semibold text-gray-900"></div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-600">الكمية الحالية:</label>
                        <div id="purchaseOrderCurrentQty" class="font-semibold text-red-600"></div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-600">الحد الأدنى:</label>
                        <div id="purchaseOrderMinStock" class="font-semibold text-gray-600"></div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user text-gray-500"></i> المورد
                    </label>
                    <select name="supplier_id" id="supplierSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                        <option value="">اختر المورد</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-cubes text-gray-500"></i> الكمية المطلوبة
                    </label>
                    <input type="number" name="quantity" id="orderQuantity" step="0.01" min="0.01" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-dollar-sign text-gray-500"></i> سعر الوحدة المتوقع
                    </label>
                    <input type="number" name="unit_price" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0.00">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> تاريخ التسليم المطلوب
                    </label>
                    <input type="date" name="delivery_date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
            </div>

            <!-- Batch Information (if applicable) -->
            <div id="batchOrderSection" class="mt-4" style="display: none;">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 mb-3">
                        <i class="fas fa-layer-group"></i> معلومات الدفعة
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-hashtag text-gray-500"></i> رقم الدفعة المطلوب
                            </label>
                            <input type="text" name="requested_batch_number" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="اختياري">
                        </div>
                        <div id="expiryRequiredField" style="display: none;">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-times text-gray-500"></i> تاريخ انتهاء الصلاحية المطلوب
                            </label>
                            <input type="date" name="required_expiry_date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-exclamation-circle text-gray-500"></i> الأولوية
                </label>
                <select name="priority" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="normal">عادية</option>
                    <option value="high" selected>عالية</option>
                    <option value="urgent">عاجلة</option>
                </select>
            </div>

            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-comment text-gray-500"></i> ملاحظات
                </label>
                <textarea name="notes" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="معلومات إضافية للمورد أو متطلبات خاصة"></textarea>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('quickPurchaseOrderModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2">
                    <i class="fas fa-shopping-cart"></i>
                    إنشاء الأمر
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Adjust Stock Modal -->
<div id="adjustStockModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto">
        <div class="bg-yellow-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-edit"></i>
                تعديل مستوى المخزون
            </h3>
            <button onclick="closeModal('adjustStockModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="adjustStockForm" class="p-6">
            <div class="grid grid-cols-1 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search text-gray-500"></i> الصنف
                    </label>
                    <select name="item" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent" required>
                        <option value="">اختر الصنف</option>
                    </select>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-cubes text-gray-500"></i> الكمية الحالية
                        </label>
                        <input type="number" name="current_quantity" class="w-full p-3 border border-gray-300 rounded-lg bg-gray-100" readonly>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-edit text-gray-500"></i> الكمية الجديدة
                        </label>
                        <input type="number" name="new_quantity" step="0.01" min="0" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent" required>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-list-alt text-gray-500"></i> سبب التعديل
                    </label>
                    <select name="reason" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent" required>
                        <option value="">اختر السبب</option>
                        <option value="inventory_count">جرد مخزون</option>
                        <option value="damage">تلف</option>
                        <option value="theft">فقدان</option>
                        <option value="correction">تصحيح خطأ</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-comment text-gray-500"></i> ملاحظات
                    </label>
                    <textarea name="notes" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent" placeholder="أدخل ملاحظات إضافية"></textarea>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('adjustStockModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ التعديل
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Movements Modal -->
<div id="viewMovementsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-history"></i>
                عرض سجل الحركات
            </h3>
            <button onclick="closeModal('viewMovementsModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Filter Options -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> من تاريخ
                    </label>
                    <input type="date" id="movementDateFrom" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> إلى تاريخ
                    </label>
                    <input type="date" id="movementDateTo" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-exchange-alt text-gray-500"></i> نوع الحركة
                    </label>
                    <select id="movementTypeFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">جميع الأنواع</option>
                        <option value="in">دخول</option>
                        <option value="out">خروج</option>
                        <option value="adjustment">تسوية</option>
                        <option value="transfer">تحويل</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search text-gray-500"></i> الصنف
                    </label>
                    <select id="movementItemFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">جميع الأصناف</option>
                    </select>
                </div>
            </div>
            
            <!-- Movements Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-calendar text-gray-600"></i> التاريخ
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-tag text-gray-600"></i> الصنف
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-exchange-alt text-gray-600"></i> النوع
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cubes text-gray-600"></i> الكمية
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-file-alt text-gray-600"></i> المرجع
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-user text-gray-600"></i> المستخدم
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-layer-group text-gray-600"></i> الدفعة
                            </th>
                        </tr>
                    </thead>
                    <tbody id="movementsTableBody">
                        <tr>
                            <td colspan="7" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-green-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Manage Locations Modal -->
<div id="manageLocationsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-purple-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-map-marker-alt"></i>
                إدارة المواقع
            </h3>
            <button onclick="closeModal('manageLocationsModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
          
            
            <!-- Locations Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-barcode text-gray-600"></i> الرمز
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-warehouse text-gray-600"></i> الاسم والنوع
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-map-marker-alt text-gray-600"></i> العنوان
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-expand text-gray-600"></i> المساحة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cubes text-gray-600"></i> الأصناف
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-toggle-on text-gray-600"></i> الحالة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cogs text-gray-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="locationsTableBody">
                        <tr>
                            <td colspan="7" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-purple-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Track Items Modal -->
<div id="trackItemsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-indigo-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-search-location"></i>
                تتبع مواضع الأصناف
            </h3>
            <button onclick="closeModal('trackItemsModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Search Item -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search text-gray-500"></i> البحث عن صنف
                    </label>
                    <select id="trackItemSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        <option value="">اختر الصنف للتتبع</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-warehouse text-gray-500"></i> المستودع
                    </label>
                    <select id="trackLocationFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        <option value="">جميع المستودعات</option>
                    </select>
                </div>
            </div>
            
            <!-- Item Locations Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-warehouse text-gray-600"></i> المستودع
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-map-pin text-gray-600"></i> الموقع المحدد
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cubes text-gray-600"></i> الكمية
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-level-down-alt text-gray-600"></i> نقطة إعادة الطلب
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-level-up-alt text-gray-600"></i> الحد الأقصى
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-calendar text-gray-600"></i> آخر تحديث
                            </th>
                        </tr>
                    </thead>
                    <tbody id="itemLocationsTableBody">
                        <tr>
                            <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <i class="fas fa-search text-2xl mb-2"></i><br>
                                اختر صنف للعرض
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Transfer Modal -->
<div id="addTransferModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 max-h-screen overflow-y-auto">
        <div class="bg-cyan-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-truck"></i>
                إنشاء تحويل جديد
            </h3>
            <button onclick="closeModal('addTransferModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="addTransferForm" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-hashtag text-gray-500"></i> رقم التحويل
                    </label>
                    <input type="text" name="transfer_number" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent" placeholder="سيتم إنشاؤه تلقائياً" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> تاريخ التحويل
                    </label>
                    <input type="date" name="transfer_date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-warehouse text-gray-500"></i> من مستودع
                    </label>
                    <select name="from_location" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent" required>
                        <option value="">اختر المستودع المصدر</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-warehouse text-gray-500"></i> إلى مستودع
                    </label>
                    <select name="to_location" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent" required>
                        <option value="">اختر المستودع الهدف</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-box text-gray-500"></i> الصنف
                    </label>
                    <select name="item" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent" required>
                        <option value="">اختر الصنف</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-cubes text-gray-500"></i> الكمية
                    </label>
                    <input type="number" name="quantity" step="0.01" min="0.01" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-list-alt text-gray-500"></i> الحالة
                    </label>
                    <select name="status" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent">
                        <option value="pending">في الانتظار</option>
                        <option value="in_transit">في الطريق</option>
                        <option value="completed">مكتمل</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-comment text-gray-500"></i> ملاحظات
                    </label>
                    <textarea name="notes" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent" placeholder="أدخل ملاحظات إضافية"></textarea>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('addTransferModal')" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button type="submit" class="px-6 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    إنشاء التحويل
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Pending Transfers Modal -->
<div id="pendingTransfersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-orange-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-clock"></i>
                التحويلات المعلقة
            </h3>
            <button onclick="closeModal('pendingTransfersModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <div class="mb-4 p-4 bg-orange-50 border-l-4 border-orange-400 rounded">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-orange-600 mr-2"></i>
                    <p class="text-orange-800">التحويلات التي تحتاج إلى إجراء أو موافقة</p>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-hashtag text-gray-600"></i> رقم التحويل
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-calendar text-gray-600"></i> التاريخ
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-warehouse text-gray-600"></i> من
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-warehouse text-gray-600"></i> إلى
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-box text-gray-600"></i> الصنف
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cubes text-gray-600"></i> الكمية
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cogs text-gray-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="pendingTransfersTableBody">
                        <tr>
                            <td colspan="7" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-orange-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Transfer History Modal -->
<div id="transferHistoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-gray-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-history"></i>
                سجل التحويلات
            </h3>
            <button onclick="closeModal('transferHistoryModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Filter Options -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> من تاريخ
                    </label>
                    <input type="date" id="transferDateFrom" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> إلى تاريخ
                    </label>
                    <input type="date" id="transferDateTo" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-list-alt text-gray-500"></i> الحالة
                    </label>
                    <select id="transferStatusFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="in_transit">في الطريق</option>
                        <option value="completed">مكتمل</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-hashtag text-gray-600"></i> رقم التحويل
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-calendar text-gray-600"></i> التاريخ
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-warehouse text-gray-600"></i> من
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-warehouse text-gray-600"></i> إلى
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-box text-gray-600"></i> الصنف
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cubes text-gray-600"></i> الكمية
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-check-circle text-gray-600"></i> الحالة
                            </th>
                        </tr>
                    </thead>
                    <tbody id="transferHistoryTableBody">
                        <tr>
                            <td colspan="7" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-gray-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Pending Purchase Orders Modal -->
<div id="pendingPurchaseOrdersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-orange-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-clock"></i>
                الأوامر في انتظار الموافقة
            </h3>
            <button onclick="closeModal('pendingPurchaseOrdersModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <div class="mb-4 p-4 bg-orange-50 border-l-4 border-orange-400 rounded">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-orange-600 mr-2"></i>
                    <p class="text-orange-800">أوامر الشراء التي تحتاج إلى مراجعة وموافقة</p>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-hashtag text-gray-600"></i> رقم الأمر
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-calendar text-gray-600"></i> تاريخ الطلب
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-user text-gray-600"></i> المورد
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-dollar-sign text-gray-600"></i> القيمة الإجمالية
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-calendar-check text-gray-600"></i> تاريخ التسليم
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cogs text-gray-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="pendingPurchaseOrdersTableBody">
                        <tr>
                            <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-orange-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- View Purchase Orders Modal -->
<div id="viewPurchaseOrdersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-indigo-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-list"></i>
                عرض جميع الأوامر
            </h3>
            <button onclick="closeModal('viewPurchaseOrdersModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Filter Options -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> من تاريخ
                    </label>
                    <input type="date" id="poDateFrom" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar text-gray-500"></i> إلى تاريخ
                    </label>
                    <input type="date" id="poDateTo" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user text-gray-500"></i> المورد
                    </label>
                    <select id="poSupplierFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        <option value="">جميع الموردين</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-check-circle text-gray-500"></i> الحالة
                    </label>
                    <select id="poStatusFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        <option value="draft">مسودة</option>
                        <option value="pending">في الانتظار</option>
                        <option value="approved">معتمد</option>
                        <option value="received">مستلم</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-hashtag text-gray-600"></i> رقم الأمر
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-calendar text-gray-600"></i> التاريخ
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-user text-gray-600"></i> المورد
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-dollar-sign text-gray-600"></i> القيمة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-check-circle text-gray-600"></i> الحالة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cogs text-gray-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="purchaseOrdersTableBody">
                        <tr>
                            <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-indigo-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- View Suppliers Modal -->
<div id="viewSuppliersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-teal-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-users"></i>
                عرض جميع الموردين
            </h3>
            <button onclick="closeModal('viewSuppliersModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Add Supplier Button -->
            <div class="mb-6">
                <button onclick="openModal('addSupplierModal')" class="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 flex items-center gap-2">
                    <i class="fas fa-plus"></i>
                    إضافة مورد جديد
                </button>
            </div>
            
            <!-- Search -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-search text-gray-500"></i> البحث
                    </label>
                    <input type="text" id="supplierSearch" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent" placeholder="ابحث بالاسم أو البريد الإلكتروني">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-toggle-on text-gray-500"></i> الحالة
                    </label>
                    <select id="supplierStatusFilter" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-user text-gray-600"></i> الاسم
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-envelope text-gray-600"></i> البريد الإلكتروني
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-phone text-gray-600"></i> الهاتف
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-map-marker-alt text-gray-600"></i> العنوان
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-toggle-on text-gray-600"></i> الحالة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-cogs text-gray-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="suppliersTableBody">
                        <tr>
                            <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-teal-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Supplier Performance Modal -->
<div id="supplierPerformanceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50" style="display: none;">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-5/6 lg:w-4/5 max-h-screen overflow-y-auto">
        <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
            <h3 class="text-lg font-semibold flex items-center gap-2">
                <i class="fas fa-chart-line"></i>
                تقييم أداء الموردين
            </h3>
            <button onclick="closeModal('supplierPerformanceModal')" class="text-white hover:text-gray-300">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Performance Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-600 text-sm font-medium">إجمالي الموردين</p>
                            <p class="text-2xl font-bold text-blue-800">15</p>
                        </div>
                        <i class="fas fa-users text-blue-600 text-2xl"></i>
                    </div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-600 text-sm font-medium">أداء ممتاز</p>
                            <p class="text-2xl font-bold text-green-800">8</p>
                        </div>
                        <i class="fas fa-star text-green-600 text-2xl"></i>
                    </div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-yellow-600 text-sm font-medium">أداء متوسط</p>
                            <p class="text-2xl font-bold text-yellow-800">5</p>
                        </div>
                        <i class="fas fa-star-half-alt text-yellow-600 text-2xl"></i>
                    </div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-red-600 text-sm font-medium">يحتاج تحسين</p>
                            <p class="text-2xl font-bold text-red-800">2</p>
                        </div>
                        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Performance Table -->
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-user text-gray-600"></i> المورد
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-shopping-cart text-gray-600"></i> عدد الأوامر
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-dollar-sign text-gray-600"></i> إجمالي القيمة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-clock text-gray-600"></i> متوسط وقت التسليم
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-check-circle text-gray-600"></i> معدل التسليم في الوقت
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-star text-gray-600"></i> التقييم
                            </th>
                        </tr>
                    </thead>
                    <tbody id="supplierPerformanceTableBody">
                        <tr>
                            <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center">
                                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-green-500"></i>
                                    <span class="text-lg font-medium">جاري تحميل البيانات...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Prevent auto scroll first
    preventAutoScroll();
    
    // Get saved tab from localStorage or default to main-dashboard
    const savedTab = localStorage.getItem('activeSupplyChainTab') || 'main-dashboard';
    
    // Reset all tabs first
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });
    
    // Show the saved or default tab
    const selectedTab = document.getElementById(savedTab);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    
    // Find and activate the corresponding button
    const targetButton = document.querySelector(`button[onclick*="${savedTab}"]`);
    if (targetButton) {
        targetButton.classList.add('active');
    }
    
    console.log('Active tab restored:', savedTab);
    
    // Add form submission handlers
    const quickAdjustForm = document.getElementById('quickAdjustStockForm');
    if (quickAdjustForm) {
        quickAdjustForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const addQty = parseFloat(formData.get('add_quantity')) || 0;
            const subtractQty = parseFloat(formData.get('subtract_quantity')) || 0;
            
            // Validate that only one quantity is provided
            if ((addQty > 0 && subtractQty > 0) || (addQty === 0 && subtractQty === 0)) {
                showNotification('يرجى إدخال إما كمية الإضافة أو كمية الطرح فقط', 'error');
                return;
            }
            
            try {
                const adjustmentData = {
                    item_id: formData.get('item_id'),
                    batch_id: formData.get('batch_id') || null,
                    quantity: addQty > 0 ? addQty : -subtractQty,
                    movement_type: addQty > 0 ? 'IN' : 'OUT',
                    reason: formData.get('adjustment_reason'),
                    notes: formData.get('notes')
                };
                
                const response = await fetch('/api/supply-chain/stock-adjustment/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(adjustmentData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification('تم تعديل المخزون بنجاح', 'success');
                    closeModal('quickAdjustStockModal');
                    loadLowStockData(); // Refresh the low stock table
                    this.reset(); // Reset form
                } else {
                    showNotification(data.message || 'خطأ في تعديل المخزون', 'error');
                }
            } catch (error) {
                console.error('Error adjusting stock:', error);
                showNotification('خطأ في تعديل المخزون', 'error');
            }
        });
    }

    // Handle Quick Purchase Order Form Submission
    const quickPurchaseForm = document.getElementById('quickPurchaseOrderForm');
    if (quickPurchaseForm) {
        quickPurchaseForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            try {
                const orderData = {
                    supplier_id: formData.get('supplier_id'),
                    items: [{
                        item_id: formData.get('item_id'),
                        quantity: parseFloat(formData.get('quantity')),
                        unit_price: parseFloat(formData.get('unit_price')) || 0,
                        batch_number: formData.get('requested_batch_number') || null,
                        expiry_date: formData.get('required_expiry_date') || null
                    }],
                    delivery_date: formData.get('delivery_date'),
                    priority: formData.get('priority'),
                    notes: formData.get('notes'),
                    status: 'PENDING'
                };
                
                const response = await fetch('/api/supply-chain/purchase-orders/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(orderData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification('تم إنشاء أمر الشراء بنجاح', 'success');
                    closeModal('quickPurchaseOrderModal');
                    this.reset(); // Reset form
                } else {
                    showNotification(data.message || 'خطأ في إنشاء أمر الشراء', 'error');
                }
            } catch (error) {
                console.error('Error creating purchase order:', error);
                showNotification('خطأ في إنشاء أمر الشراء', 'error');
            }
        });
    }
});

// Modal management functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        modal.classList.remove('hidden');
        
        // Load data based on modal type
        switch(modalId) {
            case 'viewItemsModal':
                loadItemsData();
                break;
            case 'lowStockModal':
                loadLowStockData();
                break;
            case 'viewMovementsModal':
                loadMovementsData();
                break;
            case 'manageLocationsModal':
                loadLocationsData();
                break;
            case 'viewSuppliersModal':
                loadSuppliersData();
                break;
            case 'viewPurchaseOrdersModal':
                loadPurchaseOrdersData();
                break;
            case 'pendingTransfersModal':
                loadPendingTransfersData();
                break;
            case 'pendingPurchaseOrdersModal':
                loadPendingPurchaseOrdersData();
                break;
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.add('hidden');
    }
}

// Data loading functions
async function loadItemsData() {
    const tableBody = document.getElementById('itemsTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 6, 'جاري تحميل البيانات...');
        
        const response = await fetch('/api/supply-chain/items/');
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(item => {
                const row = document.createElement('tr');
                const batchInfo = getBatchInfoDisplay(item);
                const batchActions = getBatchActionButtons(item);
                
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2">${item.sku || '-'}</td>
                    <td class="border border-gray-300 px-4 py-2">${item.name}</td>
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="text-lg font-medium ${item.quantity <= 0 ? 'text-red-600' : item.is_low_stock ? 'text-yellow-600' : 'text-green-600'}">
                            ${parseFloat(item.quantity).toFixed(2)}
                        </span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">${batchInfo}</td>
                    <td class="border border-gray-300 px-4 py-2">${parseFloat(item.unit_price).toFixed(2)} ج.م</td>
                    <td class="border border-gray-300 px-4 py-2">
                        <div class="flex gap-2">
                            <button class="text-blue-600 hover:text-blue-800" onclick="editItem('${item.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800" onclick="viewItemDetails('${item.id}')" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${batchActions}
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 6, 'لا توجد أصناف مسجلة', 'fas fa-box-open');
        }
    } catch (error) {
        console.error('Error loading items:', error);
        showErrorState(tableBody, 6, 'خطأ في تحميل البيانات');
    }
}

// Get batch information display based on tracking level
function getBatchInfoDisplay(item) {
    const batchLevel = item.batch_tracking_level || 'none';
    
    if (batchLevel === 'none') {
        return `<span class="text-gray-400 text-sm"><i class="fas fa-minus"></i> بدون تتبع</span>`;
    }
    
    const batchCount = item.batch_summary?.total_batches || 0;
    const expiredBatches = item.batch_summary?.expired_batches || 0;
    const expiringBatches = item.batch_summary?.expiring_batches || 0;
    
    let statusClass = 'text-blue-600';
    let statusIcon = 'fa-layer-group';
    let statusLabel = '';
    
    if (expiredBatches > 0) {
        statusClass = 'text-red-600';
        statusIcon = 'fa-exclamation-triangle';
        statusLabel = 'منتهية الصلاحية';
    } else if (expiringBatches > 0) {
        statusClass = 'text-orange-600';
        statusIcon = 'fa-clock';
        statusLabel = 'تنتهي قريباً';
    } else if (batchLevel === 'full') {
        statusClass = 'text-green-600';
        statusIcon = 'fa-shield-alt';
        statusLabel = 'تتبع كامل';
    } else {
        statusLabel = 'تتبع أساسي';
    }
    
    return `
        <div class="flex flex-col">
            <span class="${statusClass} text-sm font-medium">
                <i class="fas ${statusIcon}"></i> ${statusLabel}
            </span>
            ${batchCount > 0 ? `
                <div class="text-xs text-gray-500 mt-1">
                    ${batchCount} دفعة${expiredBatches > 0 ? ` • ${expiredBatches} منتهية` : ''}${expiringBatches > 0 ? ` • ${expiringBatches} تنتهي قريباً` : ''}
                </div>
            ` : `<div class="text-xs text-gray-500 mt-1">لا توجد دفعات</div>`}
        </div>
    `;
}

// Get batch action buttons based on tracking level
function getBatchActionButtons(item) {
    const batchLevel = item.batch_tracking_level || 'none';
    
    if (batchLevel === 'none') {
        return '';
    }
    
    return `
        <button class="text-indigo-600 hover:text-indigo-800" onclick="viewBatches('${item.id}')" title="عرض الدفعات">
            <i class="fas fa-layer-group"></i>
        </button>
    `;
}

async function loadLowStockData() {
    const tableBody = document.getElementById('lowStockTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 7, 'جاري تحميل البيانات...');
        
        const response = await fetch('/api/supply-chain/low-stock/');
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(item => {
                const row = document.createElement('tr');
                const statusClass = item.quantity <= 0 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800';
                const statusText = item.quantity <= 0 ? 'نافد' : 'منخفض';
                
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2">${item.sku || '-'}</td>
                    <td class="border border-gray-300 px-4 py-2">${item.name}</td>
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="text-lg font-medium text-red-600">
                            ${parseFloat(item.quantity).toFixed(2)}
                        </span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">${parseFloat(item.min_stock_level).toFixed(2)}</td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        ${getBatchInfoDisplayLowStock(item)}
                    </td>
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="px-2 py-1 rounded text-sm ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">
                        <div class="flex gap-2 justify-center">
                            <button class="text-orange-600 hover:text-orange-800 hover:bg-orange-100 px-2 py-1 rounded" onclick="quickAdjustStock('${item.id}')" title="تعديل المخزون">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-blue-600 hover:text-blue-800 hover:bg-blue-100 px-2 py-1 rounded" onclick="quickCreatePurchaseOrder('${item.id}')" title="إنشاء أمر شراء">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 7, 'لا توجد أصناف منخفضة المخزون', 'fas fa-check-circle');
        }
    } catch (error) {
        console.error('Error loading low stock items:', error);
        showErrorState(tableBody, 7, 'خطأ في تحميل البيانات');
    }
}

// Helper function for batch info display in low stock alerts
function getBatchInfoDisplayLowStock(item) {
    if (!item.batch_tracking_level || item.batch_tracking_level === 'none') {
        return '<span class="text-gray-500 text-sm">بدون تتبع</span>';
    }
    
    const batchCount = item.batch_count || 0;
    const statusClass = item.batch_tracking_level === 'full' ? 'text-green-600' : 'text-blue-600';
    const icon = item.batch_tracking_level === 'full' ? 'fas fa-layer-group' : 'fas fa-boxes';
    
    let displayText = '';
    if (batchCount === 0) {
        displayText = 'لا توجد دفعات';
    } else if (batchCount === 1) {
        displayText = 'دفعة واحدة';
    } else if (batchCount === 2) {
        displayText = 'دفعتان';
    } else if (batchCount <= 10) {
        displayText = `${batchCount} دفعات`;
    } else {
        displayText = `${batchCount} دفعة`;
    }
    
    return `
        <span class="${statusClass} text-sm font-medium">
            <i class="${icon}"></i>
            ${displayText}
        </span>
    `;
}

async function loadMovementsData() {
    const tableBody = document.getElementById('movementsTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 7, 'جاري تحميل البيانات...');
        
        const response = await fetch('/api/supply-chain/movements/');
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(movement => {
                const row = document.createElement('tr');
                const typeClass = movement.movement_type === 'IN' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                const typeText = movement.movement_type === 'IN' ? 'دخول' : 'خروج';
                const batchInfo = movement.batch_number ? 
                    `<span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">${movement.batch_number}</span>` : 
                    '<span class="text-gray-400 text-sm">-</span>';
                
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2">${new Date(movement.created_at).toLocaleDateString('ar-EG')}</td>
                    <td class="border border-gray-300 px-4 py-2">${movement.item_name}</td>
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="px-2 py-1 rounded text-sm ${typeClass}">
                            ${typeText}
                        </span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">${movement.quantity}</td>
                    <td class="border border-gray-300 px-4 py-2">${movement.reference || '-'}</td>
                    <td class="border border-gray-300 px-4 py-2">${movement.user_name || '-'}</td>
                    <td class="border border-gray-300 px-4 py-2">${batchInfo}</td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 7, 'لا توجد حركات مسجلة', 'fas fa-exchange-alt');
        }
    } catch (error) {
        console.error('Error loading movements:', error);
        showErrorState(tableBody, 7, 'خطأ في تحميل البيانات');
    }
}

async function loadLocationsData() {
    const tableBody = document.getElementById('locationsTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 7, 'جاري تحميل البيانات...');
        
        const response = await fetch('/api/supply-chain/locations/');
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(location => {
                const row = document.createElement('tr');
                
                // Status badge styling
                const statusClass = location.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                const statusIcon = location.is_active ? 'fas fa-check-circle' : 'fas fa-times-circle';
                
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2 font-mono text-sm">${location.code}</td>
                    <td class="border border-gray-300 px-4 py-2 font-medium">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-warehouse text-gray-500"></i>
                            ${location.name}
                        </div>
                        ${location.location_type !== '-' ? `<span class="text-xs text-gray-500">${getLocationTypeText(location.location_type)}</span>` : ''}
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-sm">
                        <div class="max-w-xs truncate" title="${location.formatted_address}">
                            ${location.formatted_address}
                        </div>
                        ${location.phone ? `<div class="text-xs text-gray-500"><i class="fas fa-phone"></i> ${location.phone}</div>` : ''}
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        <span class="font-medium">${location.area_display}</span>
                        ${location.max_items ? `<div class="text-xs text-gray-500">سعة: ${location.max_items.toLocaleString()} صنف</div>` : ''}
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        <span class="font-medium text-blue-600">${location.items_display}</span>
                        ${location.total_quantity > 0 ? `<div class="text-xs text-gray-500">إجمالي: ${location.total_quantity} وحدة</div>` : ''}
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded text-sm ${statusClass}">
                            <i class="${statusIcon}"></i>
                            ${location.status_display}
                        </span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        <div class="flex justify-center gap-2">
                            <button onclick="viewLocation('${location.id}')" class="text-blue-600 hover:text-blue-800" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editLocation('${location.id}')" class="text-green-600 hover:text-green-800" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${!location.is_active ? 
                                `<button onclick="activateLocation('${location.id}')" class="text-green-600 hover:text-green-800" title="تفعيل">
                                    <i class="fas fa-toggle-on"></i>
                                </button>` :
                                `<button onclick="deactivateLocation('${location.id}')" class="text-red-600 hover:text-red-800" title="إلغاء التفعيل">
                                    <i class="fas fa-toggle-off"></i>
                                </button>`
                            }
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 7, 'لا توجد مواقع مسجلة', 'fas fa-map-marker-alt');
        }
    } catch (error) {
        console.error('Error loading locations:', error);
        showErrorState(tableBody, 7, 'خطأ في تحميل البيانات');
    }
}

async function loadSuppliersData() {
    const tableBody = document.getElementById('suppliersTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 4, 'جاري تحميل البيانات...');
        
        const response = await fetch('/api/supply-chain/suppliers/');
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(supplier => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2">${supplier.name}</td>
                    <td class="border border-gray-300 px-4 py-2">${supplier.contact_person || '-'}</td>
                    <td class="border border-gray-300 px-4 py-2">${supplier.phone || '-'}</td>
                    <td class="border border-gray-300 px-4 py-2">${supplier.email || '-'}</td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 4, 'لا توجد موردين مسجلين', 'fas fa-users');
        }
    } catch (error) {
        console.error('Error loading suppliers:', error);
        showErrorState(tableBody, 4, 'خطأ في تحميل البيانات');
    }
}

async function loadPurchaseOrdersData() {
    const tableBody = document.getElementById('purchaseOrdersTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 5, 'جاري تحميل البيانات...');
        
        const response = await fetch('/api/supply-chain/purchase-orders/');
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(order => {
                const row = document.createElement('tr');
                const statusClass = order.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 
                                   order.status === 'APPROVED' ? 'bg-green-100 text-green-800' : 
                                   'bg-red-100 text-red-800';
                const statusText = order.status === 'PENDING' ? 'معلق' : 
                                  order.status === 'APPROVED' ? 'موافق عليه' : 'مرفوض';
                
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2">${order.order_number}</td>
                    <td class="border border-gray-300 px-4 py-2">${order.supplier_name}</td>
                    <td class="border border-gray-300 px-4 py-2">${parseFloat(order.total_amount).toFixed(2)} ج.م</td>
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="px-2 py-1 rounded text-sm ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">${new Date(order.created_at).toLocaleDateString('ar-EG')}</td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 5, 'لا توجد أوامر شراء مسجلة', 'fas fa-shopping-cart');
        }
    } catch (error) {
        console.error('Error loading purchase orders:', error);
        showErrorState(tableBody, 5, 'خطأ في تحميل البيانات');
    }
}

async function loadPendingTransfersData() {
    const tableBody = document.getElementById('pendingTransfersTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 5, 'جاري تحميل البيانات...');
        
        const response = await fetch('/api/supply-chain/transfers/pending/');
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(transfer => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2">${transfer.transfer_number}</td>
                    <td class="border border-gray-300 px-4 py-2">${transfer.from_location}</td>
                    <td class="border border-gray-300 px-4 py-2">${transfer.to_location}</td>
                    <td class="border border-gray-300 px-4 py-2">${transfer.items_count}</td>
                    <td class="border border-gray-300 px-4 py-2">${new Date(transfer.created_at).toLocaleDateString('ar-EG')}</td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 5, 'لا توجد تحويلات معلقة', 'fas fa-check-circle');
        }
    } catch (error) {
        console.error('Error loading pending transfers:', error);
        showErrorState(tableBody, 5, 'خطأ في تحميل البيانات');
    }
}

async function loadPendingPurchaseOrdersData() {
    const tableBody = document.getElementById('pendingPurchaseOrdersTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 5, 'جاري تحميل البيانات...');
        
        const response = await fetch('/api/supply-chain/purchase-orders/pending/');
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2">${order.order_number}</td>
                    <td class="border border-gray-300 px-4 py-2">${order.supplier_name}</td>
                    <td class="border border-gray-300 px-4 py-2">${parseFloat(order.total_amount).toFixed(2)} ج.م</td>
                    <td class="border border-gray-300 px-4 py-2">${new Date(order.created_at).toLocaleDateString('ar-EG')}</td>
                    <td class="border border-gray-300 px-4 py-2">
                        <div class="flex gap-2">
                            <button class="text-green-600 hover:text-green-800" onclick="approvePurchaseOrder('${order.id}')" title="موافقة">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800" onclick="rejectPurchaseOrder('${order.id}')" title="رفض">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 5, 'لا توجد أوامر شراء معلقة', 'fas fa-check-circle');
        }
    } catch (error) {
        console.error('Error loading pending purchase orders:', error);
        showErrorState(tableBody, 5, 'خطأ في تحميل البيانات');
    }
}

// Item Tracking Functions
async function loadItemTrackingData() {
    console.log('Loading item tracking data...');
    await loadItemsForTracking();
    await loadWarehousesForTracking();
    
    // Add event listeners for dropdowns
    const itemSelect = document.getElementById('trackItemSelect');
    const locationFilter = document.getElementById('trackLocationFilter');
    
    if (itemSelect) {
        itemSelect.addEventListener('change', loadItemPositions);
    }
    if (locationFilter) {
        locationFilter.addEventListener('change', loadItemPositions);
    }
}

// Load items for tracking dropdown
async function loadItemsForTracking() {
    try {
        const response = await fetch('/api/items/');
        const data = await response.json();
        
        if (data.success) {
            const select = document.getElementById('trackItemSelect');
            if (select) {
                select.innerHTML = '<option value="">اختر الصنف للتتبع</option>';
                
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.name} (${item.sku})`;
                    select.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('Error loading items for tracking:', error);
        showNotification('خطأ في تحميل الأصناف', 'error');
    }
}

// Load warehouses for tracking dropdown
async function loadWarehousesForTracking() {
    try {
        const response = await fetch('/api/locations/');
        const data = await response.json();
        
        if (data.success) {
            const select = document.getElementById('trackLocationFilter');
            if (select) {
                select.innerHTML = '<option value="">جميع المستودعات</option>';
                
                data.data.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = `${location.name} (${location.code})`;
                    select.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('Error loading warehouses for tracking:', error);
        showNotification('خطأ في تحميل المستودعات', 'error');
    }
}

// Load item positions when item or warehouse selection changes
async function loadItemPositions() {
    const itemSelect = document.getElementById('trackItemSelect');
    const warehouseSelect = document.getElementById('trackLocationFilter');
    const tableBody = document.getElementById('itemLocationsTableBody');
    
    if (!itemSelect || !tableBody) return;
    
    const itemId = itemSelect.value;
    const warehouseId = warehouseSelect ? warehouseSelect.value : '';
    
    if (!itemId) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                    <i class="fas fa-search text-2xl mb-2"></i><br>
                    اختر صنف للعرض
                </td>
            </tr>
        `;
        return;
    }

    showItemPositionsLoading();

    try {
        let url = `/api/item-locations/?item_id=${itemId}`;
        if (warehouseId) {
            url += `&location_id=${warehouseId}`;
        }

        const response = await fetch(url);
        const data = await response.json();
        
        if (data.success) {
            displayItemPositions(data.data);
        } else {
            showItemPositionsError('فشل في تحميل مواضع الأصناف');
        }
    } catch (error) {
        console.error('Error loading item positions:', error);
        showItemPositionsError('خطأ في الاتصال بالخادم');
    }
}

// Display item positions in table
function displayItemPositions(positions) {
    const tableBody = document.getElementById('itemLocationsTableBody');
    if (!tableBody) return;
    
    if (positions.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                    <i class="fas fa-search text-2xl mb-2"></i><br>
                    لا توجد مواضع للصنف المحدد
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = positions.map(position => `
        <tr class="hover:bg-gray-50 ${position.quantity <= 0 ? 'bg-red-50' : ''}">
            <td class="border border-gray-300 px-4 py-3">
                <div class="text-sm font-medium text-gray-900">${position.location_name}</div>
                <div class="text-xs text-gray-500">${position.location_code}</div>
            </td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    ${position.location_type || 'مستودع'}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="text-sm font-medium ${position.quantity > 0 ? 'text-green-600' : 'text-red-600'}">
                    ${parseFloat(position.quantity).toFixed(2)}
                    ${position.quantity <= 0 ? '<i class="fas fa-exclamation-triangle text-red-500 mr-1"></i>' : ''}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="text-sm text-gray-900">
                    ${position.reorder_point > 0 ? parseFloat(position.reorder_point).toFixed(2) : 'غير محدد'}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="text-sm text-gray-900">
                    ${position.max_stock > 0 ? parseFloat(position.max_stock).toFixed(2) : 'غير محدد'}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="text-sm text-gray-500">
                    ${new Date(position.updated_at).toLocaleDateString('ar-SA')}
                </span>
            </td>
        </tr>
    `).join('');
}

// Show loading state for item positions
function showItemPositionsLoading() {
    const tableBody = document.getElementById('itemLocationsTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = `
        <tr>
            <td colspan="6" class="border border-gray-300 px-4 py-8 text-center">
                <div class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 ml-2"></div>
                    جاري تحميل مواضع الأصناف...
                </div>
            </td>
        </tr>
    `;
}

// Show error state for item positions
function showItemPositionsError(message) {
    const tableBody = document.getElementById('itemLocationsTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = `
        <tr>
            <td colspan="6" class="border border-gray-300 px-4 py-8 text-center text-red-500">
                <i class="fas fa-exclamation-triangle text-2xl mb-2"></i><br>
                ${message}
            </td>
        </tr>
    `;
}

// Utility functions for table states
function showLoadingState(tableBody, colspan, message) {
    tableBody.innerHTML = `
        <tr>
            <td colspan="${colspan}" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                <div class="flex flex-col items-center justify-center">
                    <i class="fas fa-spinner fa-spin text-3xl mb-3 text-blue-500"></i>
                    <span class="text-lg font-medium">${message}</span>
                </div>
            </td>
        </tr>
    `;
}

function showEmptyState(tableBody, colspan, message, icon) {
    tableBody.innerHTML = `
        <tr>
            <td colspan="${colspan}" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                <div class="flex flex-col items-center justify-center">
                    <i class="${icon} text-3xl mb-3 text-gray-400"></i>
                    <span class="text-lg font-medium">${message}</span>
                </div>
            </td>
        </tr>
    `;
}

function showErrorState(tableBody, colspan, message) {
    tableBody.innerHTML = `
        <tr>
            <td colspan="${colspan}" class="border border-gray-300 px-4 py-8 text-center text-red-500">
                <div class="flex flex-col items-center justify-center">
                    <i class="fas fa-exclamation-circle text-3xl mb-3"></i>
                    <span class="text-lg font-medium">${message}</span>
                    <button onclick="location.reload()" class="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                        إعادة المحاولة
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// Modal management functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        modal.classList.remove('hidden');
        
        // Load data based on modal type
        switch(modalId) {
            case 'viewItemsModal':
                loadItemsData();
                break;
            case 'lowStockModal':
                loadLowStockData();
                break;
            case 'viewMovementsModal':
                loadMovementsData();
                break;
            case 'manageLocationsModal':
                loadLocationsData();
                break;
            case 'viewSuppliersModal':
                loadSuppliersData();
                break;
            case 'viewPurchaseOrdersModal':
                loadPurchaseOrdersData();
                break;
            case 'pendingTransfersModal':
                loadPendingTransfersData();
                break;
            case 'trackItemsModal':
                loadItemTrackingData();
                break;
            case 'pendingPurchaseOrdersModal':
                loadPendingPurchaseOrdersData();
                break;
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.add('hidden');
    }
}

// Item action functions
async function editItem(itemId) {
    try {
        const response = await fetch(`/api/supply-chain/items/${itemId}/`);
        const data = await response.json();
        
        if (data.success) {
            const item = data.data;
            
            // Create edit modal content
            const editModalHtml = `
                <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto">
                    <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
                        <h3 class="text-lg font-semibold flex items-center gap-2">
                            <i class="fas fa-edit"></i>
                            تعديل الصنف: ${item.name}
                        </h3>
                        <button onclick="closeEditModal()" class="text-white hover:text-gray-300">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="editItemForm" class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الرمز</label>
                                <input type="text" name="sku" value="${item.sku}" class="w-full p-3 border border-gray-300 rounded-lg bg-gray-100" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الاسم *</label>
                                <input type="text" name="name" value="${item.name}" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                                <input type="text" name="category" value="${item.category}" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">السعر</label>
                                <input type="number" step="0.01" name="unit_price" value="${item.unit_price}" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الكمية الحالية</label>
                                <input type="number" step="0.01" name="quantity" value="${item.quantity}" class="w-full p-3 border border-gray-300 rounded-lg bg-gray-100" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى</label>
                                <input type="number" step="0.01" name="min_stock_level" value="${item.min_stock_level}" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                            <textarea name="description" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">${item.description}</textarea>
                        </div>
                        <div class="flex justify-end gap-3 mt-6">
                            <button type="button" onclick="closeEditModal()" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                                إلغاء
                            </button>
                            <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2">
                                <i class="fas fa-save"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            `;
            
            // Create and show modal
            const editModal = document.createElement('div');
            editModal.id = 'editItemModal';
            editModal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
            editModal.innerHTML = editModalHtml;
            document.body.appendChild(editModal);
            
            // Handle form submission
            document.getElementById('editItemForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveItemChanges(itemId, this);
            });
            
        } else {
            showNotification('خطأ في تحميل بيانات الصنف', 'error');
        }
    } catch (error) {
        console.error('Error loading item details:', error);
        showNotification('خطأ في تحميل بيانات الصنف', 'error');
    }
}

async function viewItemDetails(itemId) {
    try {
        const response = await fetch(`/api/supply-chain/items/${itemId}/`);
        const data = await response.json();
        
        if (data.success) {
            const item = data.data;
            
            // Create view modal content
            const viewModalHtml = `
                <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-screen overflow-y-auto">
                    <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
                        <h3 class="text-lg font-semibold flex items-center gap-2">
                            <i class="fas fa-eye"></i>
                            تفاصيل الصنف: ${item.name}
                        </h3>
                        <button onclick="closeViewModal()" class="text-white hover:text-gray-300">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الرمز</label>
                                    <div class="p-3 bg-gray-50 rounded-lg border">${item.sku}</div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الاسم</label>
                                    <div class="p-3 bg-gray-50 rounded-lg border">${item.name}</div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الفئة</label>
                                    <div class="p-3 bg-gray-50 rounded-lg border">${item.category || '-'}</div>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الكمية المتاحة</label>
                                    <div class="p-3 bg-gray-50 rounded-lg border">
                                        <span class="text-lg font-medium ${item.quantity <= 0 ? 'text-red-600' : item.is_low_stock ? 'text-yellow-600' : 'text-green-600'}">
                                            ${parseFloat(item.quantity).toFixed(2)}
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">السعر</label>
                                    <div class="p-3 bg-gray-50 rounded-lg border">${parseFloat(item.unit_price).toFixed(2)} ج.م</div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الحد الأدنى</label>
                                    <div class="p-3 bg-gray-50 rounded-lg border">${parseFloat(item.min_stock_level).toFixed(2)}</div>
                                </div>
                            </div>
                        </div>
                        
                        ${item.description ? `
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                            <div class="p-3 bg-gray-50 rounded-lg border">${item.description}</div>
                        </div>
                        ` : ''}
                        
                        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                            <h4 class="font-medium text-blue-900 mb-2">معلومات إضافية</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">تاريخ الإنشاء:</span>
                                    <span class="font-medium">${new Date(item.created_at).toLocaleDateString('ar-EG')}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">آخر تحديث:</span>
                                    <span class="font-medium">${new Date(item.updated_at).toLocaleDateString('ar-EG')}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">حالة المخزون:</span>
                                    <span class="font-medium ${item.quantity <= 0 ? 'text-red-600' : item.is_low_stock ? 'text-yellow-600' : 'text-green-600'}">
                                        ${item.quantity <= 0 ? 'نافد' : item.is_low_stock ? 'منخفض' : 'متوفر'}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-end gap-3 mt-6">
                            <button type="button" onclick="closeViewModal()" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">
                                إغلاق
                            </button>
                            <button type="button" onclick="closeViewModal(); editItem('${itemId}')" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            // Create and show modal
            const viewModal = document.createElement('div');
            viewModal.id = 'viewItemModal';
            viewModal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
            viewModal.innerHTML = viewModalHtml;
            document.body.appendChild(viewModal);
            
        } else {
            showNotification('خطأ في تحميل بيانات الصنف', 'error');
        }
    } catch (error) {
        console.error('Error loading item details:', error);
        showNotification('خطأ في تحميل بيانات الصنف', 'error');
    }
}

function closeEditModal() {
    const modal = document.getElementById('editItemModal');
    if (modal) {
        modal.remove();
    }
}

function closeViewModal() {
    const modal = document.getElementById('viewItemModal');
    if (modal) {
        modal.remove();
    }
}

async function saveItemChanges(itemId, form) {
    try {
        const formData = new FormData(form);
        const data = {
            name: formData.get('name'),
            category: formData.get('category'),
            unit_price: parseFloat(formData.get('unit_price')),
            min_stock_level: parseFloat(formData.get('min_stock_level')),
            description: formData.get('description')
        };
        
        const response = await fetch(`/api/supply-chain/items/${itemId}/`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('تم تحديث الصنف بنجاح', 'success');
            closeEditModal();
            // Refresh the items table
            loadItemsData();
        } else {
            showNotification('خطأ في تحديث الصنف: ' + (result.error || 'خطأ غير معروف'), 'error');
        }
    } catch (error) {
        console.error('Error saving item changes:', error);
        showNotification('خطأ في تحديث الصنف', 'error');
    }
}

function getCsrfToken() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfToken ? csrfToken.value : '';
}

// Batch-related functions
async function viewBatches(itemId) {
    try {
        // First get item details
        const itemResponse = await fetch(`/api/supply-chain/items/${itemId}/`);
        const itemData = await itemResponse.json();
        
        if (!itemData.success) {
            showNotification('خطأ في جلب بيانات الصنف', 'error');
            return;
        }
        
        const item = itemData.data;
        
        // Update item info in modal
        document.getElementById('batchItemName').textContent = item.name;
        document.getElementById('batchItemSku').textContent = item.sku;
        document.getElementById('batchTrackingLevel').textContent = getBatchTrackingLevelLabel(item.batch_tracking_level);
        document.getElementById('batchTrackingLevel').className = `font-semibold ${getBatchTrackingLevelClass(item.batch_tracking_level)}`;
        
        // Show/hide expiry column based on tracking level
        const expiryColumn = document.getElementById('expiryColumn');
        if (item.batch_tracking_level === 'full' && item.requires_expiry_tracking) {
            expiryColumn.style.display = 'table-cell';
        } else {
            expiryColumn.style.display = 'none';
        }
        
        // Open modal and load batch data
        openModal('batchDetailsModal');
        await loadBatchData(itemId);
        
    } catch (error) {
        console.error('Error viewing batches:', error);
        showNotification('خطأ في عرض الدفعات', 'error');
    }
}

async function loadBatchData(itemId) {
    const tableBody = document.getElementById('batchesTableBody');
    if (!tableBody) return;
    
    try {
        showLoadingState(tableBody, 8, 'جاري تحميل دفعات الصنف...');
        
        const response = await fetch(`/api/supply-chain/batches/?item_id=${itemId}`);
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            tableBody.innerHTML = '';
            data.data.forEach(batch => {
                const row = document.createElement('tr');
                const expiryStatus = getBatchExpiryStatus(batch);
                const expiryCell = batch.expiry_date ? 
                    `<td class="border border-gray-300 px-4 py-2">${formatDate(batch.expiry_date)}</td>` : 
                    '<td class="border border-gray-300 px-4 py-2">-</td>';
                
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">${batch.batch_number}</span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="font-semibold">${parseFloat(batch.current_quantity).toFixed(2)}</span>
                        <span class="text-gray-500 text-sm">/${parseFloat(batch.initial_quantity).toFixed(2)}</span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="text-orange-600 font-medium">${parseFloat(batch.reserved_quantity || 0).toFixed(2)}</span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="text-green-600 font-medium">${parseFloat(batch.available_quantity || batch.current_quantity).toFixed(2)}</span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">${formatDate(batch.received_date)}</td>
                    ${batch.expiry_date ? expiryCell : ''}
                    <td class="border border-gray-300 px-4 py-2">
                        <span class="px-2 py-1 rounded text-sm ${expiryStatus.class}">
                            ${expiryStatus.label}
                        </span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2">
                        <div class="flex gap-2">
                            <button class="text-blue-600 hover:text-blue-800" onclick="editBatch('${batch.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800" onclick="moveBatch('${batch.id}')" title="حركة">
                                <i class="fas fa-exchange-alt"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        } else {
            showEmptyState(tableBody, 8, 'لا توجد دفعات لهذا الصنف', 'fas fa-layer-group');
        }
    } catch (error) {
        console.error('Error loading batch data:', error);
        showErrorState(tableBody, 8, 'خطأ في تحميل بيانات الدفعات');
    }
}

function getBatchTrackingLevelLabel(level) {
    switch (level) {
        case 'basic': return 'تتبع أساسي للدفعات';
        case 'full': return 'تتبع كامل مع تاريخ الانتهاء';
        default: return 'بدون تتبع دفعات';
    }
}

function getBatchTrackingLevelClass(level) {
    switch (level) {
        case 'basic': return 'text-blue-600';
        case 'full': return 'text-green-600';
        default: return 'text-gray-600';
    }
}

function getBatchExpiryStatus(batch) {
    if (!batch.expiry_date) {
        return { class: 'bg-gray-100 text-gray-800', label: 'بدون انتهاء' };
    }
    
    const today = new Date();
    const expiryDate = new Date(batch.expiry_date);
    const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
    
    if (daysUntilExpiry < 0) {
        return { class: 'bg-red-100 text-red-800', label: 'منتهية الصلاحية' };
    } else if (daysUntilExpiry <= 7) {
        return { class: 'bg-orange-100 text-orange-800', label: 'تنتهي قريباً' };
    } else if (daysUntilExpiry <= 30) {
        return { class: 'bg-yellow-100 text-yellow-800', label: 'تحذير' };
    } else {
        return { class: 'bg-green-100 text-green-800', label: 'صالحة' };
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
}

async function openAddBatchModal() {
    // Load items with batch tracking enabled
    await loadBatchEnabledItems();
    
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    document.querySelector('input[name="received_date"]').value = today;
    
    openModal('addBatchModal');
}

async function loadBatchEnabledItems() {
    try {
        const response = await fetch('/api/supply-chain/items/?batch_tracking=true');
        const data = await response.json();
        
        const select = document.getElementById('batchItemSelect');
        select.innerHTML = '<option value="">اختر الصنف</option>';
        
        if (data.success && data.data.length > 0) {
            data.data.forEach(item => {
                if (item.batch_tracking_level && item.batch_tracking_level !== 'none') {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.name} (${item.sku})`;
                    option.dataset.trackingLevel = item.batch_tracking_level;
                    option.dataset.autoGenerate = item.auto_generate_batch_number;
                    option.dataset.prefix = item.batch_number_prefix || '';
                    option.dataset.requiresExpiry = item.requires_expiry_tracking;
                    select.appendChild(option);
                }
            });
        }
    } catch (error) {
        console.error('Error loading batch enabled items:', error);
        showNotification('خطأ في تحميل الأصناف', 'error');
    }
}

function updateBatchForm(itemId) {
    const select = document.getElementById('batchItemSelect');
    const selectedOption = select.options[select.selectedIndex];
    
    if (!selectedOption || !selectedOption.value) {
        // Hide all conditional fields
        document.getElementById('manufacturedDateField').style.display = 'none';
        document.getElementById('expiryDateField').style.display = 'none';
        return;
    }
    
    const trackingLevel = selectedOption.dataset.trackingLevel;
    const autoGenerate = selectedOption.dataset.autoGenerate === 'true';
    const prefix = selectedOption.dataset.prefix;
    const requiresExpiry = selectedOption.dataset.requiresExpiry === 'true';
    
    // Handle batch number generation
    const batchNumberInput = document.getElementById('batchNumberInput');
    if (autoGenerate) {
        batchNumberInput.placeholder = 'سيتم التوليد تلقائياً';
        batchNumberInput.disabled = true;
        generateNextBatchNumber(itemId, prefix);
    } else {
        batchNumberInput.placeholder = 'أدخل رقم الدفعة';
        batchNumberInput.disabled = false;
        batchNumberInput.value = '';
    }
    
    // Show/hide conditional fields based on tracking level
    if (trackingLevel === 'full') {
        document.getElementById('manufacturedDateField').style.display = 'block';
        if (requiresExpiry) {
            document.getElementById('expiryDateField').style.display = 'block';
        }
    } else {
        document.getElementById('manufacturedDateField').style.display = 'none';
        document.getElementById('expiryDateField').style.display = 'none';
    }
}

async function generateNextBatchNumber(itemId, prefix) {
    try {
        const response = await fetch(`/api/supply-chain/items/${itemId}/next-batch-number/`);
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('batchNumberInput').value = data.batch_number;
        }
    } catch (error) {
        console.error('Error generating batch number:', error);
    }
}

// Additional action functions
function approvePurchaseOrder(orderId) {
    showNotification('سيتم إضافة وظيفة الموافقة على أمر الشراء قريباً', 'info');
}

function rejectPurchaseOrder(orderId) {
    showNotification('سيتم إضافة وظيفة رفض أمر الشراء قريباً', 'info');
}

// Location Management Functions
async function viewLocation(locationId) {
    try {
        const response = await fetch(`/api/supply-chain/locations/?id=${locationId}`);
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            const location = data.data[0];
            
            // Create view modal content
            const modalContent = `
                <div class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50" id="viewLocationModal">
                    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-2/3 lg:w-1/2 max-h-screen overflow-y-auto">
                        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
                            <h3 class="text-lg font-semibold flex items-center gap-2">
                                <i class="fas fa-warehouse"></i>
                                تفاصيل الموقع - ${location.name}
                            </h3>
                            <button onclick="closeViewLocationModal()" class="text-white hover:text-gray-300">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">الرمز</label>
                                        <p class="mt-1 font-mono text-lg">${location.code}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">الاسم</label>
                                        <p class="mt-1 text-lg font-medium">${location.name}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">النوع</label>
                                        <p class="mt-1">${getLocationTypeText(location.location_type)}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">العنوان</label>
                                        <p class="mt-1">${location.formatted_address}</p>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">المساحة</label>
                                        <p class="mt-1 text-lg font-medium">${location.area_display}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">عدد الأصناف</label>
                                        <p class="mt-1 text-lg font-medium text-blue-600">${location.items_display}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">إجمالي الكمية</label>
                                        <p class="mt-1 text-lg">${location.total_quantity} وحدة</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">الحالة</label>
                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded text-sm ${location.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                            <i class="${location.is_active ? 'fas fa-check-circle' : 'fas fa-times-circle'}"></i>
                                            ${location.status_display}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            ${location.contact_name || location.phone || location.email ? `
                                <div class="mt-6 border-t pt-4">
                                    <h4 class="font-medium text-gray-900 mb-3">معلومات الاتصال</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        ${location.contact_name ? `<div><label class="block text-sm text-gray-600">الشخص المسؤول</label><p>${location.contact_name}</p></div>` : ''}
                                        ${location.phone ? `<div><label class="block text-sm text-gray-600">الهاتف</label><p><i class="fas fa-phone text-gray-400"></i> ${location.phone}</p></div>` : ''}
                                        ${location.email ? `<div><label class="block text-sm text-gray-600">البريد الإلكتروني</label><p><i class="fas fa-envelope text-gray-400"></i> ${location.email}</p></div>` : ''}
                                    </div>
                                </div>
                            ` : ''}
                            
                            ${location.notes ? `
                                <div class="mt-6 border-t pt-4">
                                    <h4 class="font-medium text-gray-900 mb-2">ملاحظات</h4>
                                    <p class="text-gray-700">${location.notes}</p>
                                </div>
                            ` : ''}
                            
                            <div class="mt-6 flex justify-end gap-3">
                                <button onclick="closeViewLocationModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                    إغلاق
                                </button>
                                <button onclick="editLocation('${location.id}'); closeViewLocationModal();" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalContent);
        } else {
            showNotification('لم يتم العثور على الموقع', 'error');
        }
    } catch (error) {
        console.error('Error viewing location:', error);
        showNotification('خطأ في عرض تفاصيل الموقع', 'error');
    }
}

function closeViewLocationModal() {
    const modal = document.getElementById('viewLocationModal');
    if (modal) {
        modal.remove();
    }
}

function closeEditLocationModal() {
    const modal = document.getElementById('editLocationModal');
    if (modal) {
        modal.remove();
    }
}

async function updateLocation(event, locationId) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await fetch(`/api/supply-chain/locations/${locationId}/`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        if (result.success) {
            showNotification('تم تحديث الموقع بنجاح', 'success');
            closeEditLocationModal();
            loadLocationsData(); // Refresh the table
        } else {
            showNotification('خطأ في تحديث الموقع', 'error');
        }
    } catch (error) {
        console.error('Error updating location:', error);
        showNotification('خطأ في تحديث الموقع', 'error');
    }
}

function getLocationTypeText(type) {
    const typeTexts = {
        'MAIN_WAREHOUSE': 'مستودع رئيسي',
        'WAREHOUSE': 'مستودع فرعي', 
        'SHOWROOM': 'صالة عرض',
        'WORKSHOP': 'ورشة',
        'OFFICE': 'مكتب',
        'STORAGE_ROOM': 'غرفة تخزين',
        'SECTION': 'قسم',
        'SHELF': 'رف',
        'BIN': 'صندوق',
        'RACK': 'رف معدني',
        'ZONE': 'منطقة',
        'BAY': 'خليج',
        'LEVEL': 'مستوى',
        'COLD_STORAGE': 'تخزين بارد',
        'HAZMAT_STORAGE': 'تخزين مواد خطرة',
        'RECEIVING_DOCK': 'رصيف الاستلام',
        'SHIPPING_DOCK': 'رصيف الشحن',
        'PICKING_AREA': 'منطقة التجميع',
        'PACKING_AREA': 'منطقة التعبئة',
        'QUALITY_CONTROL': 'مراقبة الجودة',
        'RETURNS_AREA': 'منطقة المرتجعات',
        'QUARANTINE': 'الحجر الصحي',
        'TRANSIT': 'نقل مؤقت',
        'VIRTUAL': 'موقع افتراضي',
        'EXTERNAL': 'موقع خارجي',
        'CUSTOMER_SITE': 'موقع العميل',
        'SUPPLIER_SITE': 'موقع المورد',
        'OTHER': 'أخرى'
    };
    return typeTexts[type] || type || '-';
}

// Warehouse Management Functions
async function loadCompaniesForWarehouse(franchiseId) {
    const companySelect = document.getElementById('warehouse-company-select');
    const serviceCenterSelect = document.getElementById('warehouse-service-center-select');
    
    // Reset dependent dropdowns
    companySelect.innerHTML = '<option value="">جاري التحميل...</option>';
    companySelect.disabled = true;
    serviceCenterSelect.innerHTML = '<option value="">اختر مركز الخدمة</option>';
    serviceCenterSelect.disabled = true;
    
    if (!franchiseId) {
        companySelect.innerHTML = '<option value="">اختر الامتياز أولاً</option>';
        return;
    }
    
    try {
        const response = await fetch(`/setup/api/companies-by-franchise/?franchise_id=${franchiseId}`);
        const data = await response.json();
        
        companySelect.innerHTML = '<option value="">اختر الشركة</option>';
        if (data.companies) {
            data.companies.forEach(company => {
                const option = document.createElement('option');
                option.value = company.id;
                option.textContent = company.name;
                companySelect.appendChild(option);
            });
        }
        companySelect.disabled = false;
    } catch (error) {
        console.error('Error loading companies:', error);
        companySelect.innerHTML = '<option value="">خطأ في التحميل</option>';
        showNotification('خطأ في تحميل بيانات الشركات', 'error');
    }
}

async function loadServiceCentersForWarehouse(companyId) {
    const serviceCenterSelect = document.getElementById('warehouse-service-center-select');
    
    serviceCenterSelect.innerHTML = '<option value="">جاري التحميل...</option>';
    serviceCenterSelect.disabled = true;
    
    if (!companyId) {
        serviceCenterSelect.innerHTML = '<option value="">اختر الشركة أولاً</option>';
        return;
    }
    
    try {
        const response = await fetch(`/setup/api/service-centers-by-company/?company_id=${companyId}`);
        const data = await response.json();
        
        serviceCenterSelect.innerHTML = '<option value="">اختر مركز الخدمة (اختياري)</option>';
        if (data.service_centers) {
            data.service_centers.forEach(center => {
                const option = document.createElement('option');
                option.value = center.id;
                option.textContent = center.name;
                serviceCenterSelect.appendChild(option);
            });
        }
        serviceCenterSelect.disabled = false;
    } catch (error) {
        console.error('Error loading service centers:', error);
        serviceCenterSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
        showNotification('خطأ في تحميل بيانات مراكز الخدمة', 'error');
    }
}

// Load initial organizational data when warehouse modal opens
async function loadWarehouseOrganizationalData() {
    try {
        const response = await fetch('/setup/api/franchises/');
        const data = await response.json();
        
        const franchiseSelect = document.getElementById('warehouse-franchise-select');
        franchiseSelect.innerHTML = '<option value="">اختر الامتياز</option>';
        
        if (data.franchises) {
            data.franchises.forEach(franchise => {
                const option = document.createElement('option');
                option.value = franchise.id;
                option.textContent = franchise.name;
                franchiseSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading franchises:', error);
        showNotification('خطأ في تحميل بيانات الامتيازات', 'error');
    }
}

// Handle warehouse form submission
async function handleWarehouseFormSubmission(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Validate required fields
    if (!data.name || !data.code || !data.location_type) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    try {
        const response = await fetch('/api/supply-chain/warehouses/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        if (result.success) {
            showNotification('تم إنشاء المستودع بنجاح', 'success');
            closeModal('addWarehouseModal');
            
            // Reset form
            form.reset();
            document.getElementById('warehouse-company-select').disabled = true;
            document.getElementById('warehouse-service-center-select').disabled = true;
            
            // Refresh locations data if function exists
            if (typeof loadLocationsData === 'function') {
                loadLocationsData();
            }
        } else {
            showNotification(result.message || 'خطأ في إنشاء المستودع', 'error');
        }
    } catch (error) {
        console.error('Error creating warehouse:', error);
        showNotification('خطأ في إنشاء المستودع', 'error');
    }
}

// Enhanced modal opening to load organizational data
function openWarehouseModal() {
    openModal('addWarehouseModal');
    loadWarehouseOrganizationalData();
}

// Override the existing modal opening for warehouse
document.addEventListener('DOMContentLoaded', function() {
    // Update the warehouse button to use the new function
    const warehouseButton = document.querySelector('button[onclick="openModal(\'addWarehouseModal\')"]');
    if (warehouseButton) {
        warehouseButton.setAttribute('onclick', 'openWarehouseModal()');
    }
    
    // Add form submission handler
    const warehouseForm = document.getElementById('addWarehouseForm');
    if (warehouseForm) {
        warehouseForm.addEventListener('submit', handleWarehouseFormSubmission);
    }
});

async function editLocation(locationId) {
    try {
        const response = await fetch(`/api/supply-chain/locations/?id=${locationId}`);
        const data = await response.json();
        
        if (data.success && data.data.length > 0) {
            const location = data.data[0];
            
            // Create edit modal content
            const modalContent = `
                <div class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50" id="editLocationModal">
                    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-2/3 lg:w-1/2 max-h-screen overflow-y-auto">
                        <div class="bg-green-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
                            <h3 class="text-lg font-semibold flex items-center gap-2">
                                <i class="fas fa-edit"></i>
                                تعديل الموقع - ${location.name}
                            </h3>
                            <button onclick="closeEditLocationModal()" class="text-white hover:text-gray-300">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <div class="p-6">
                            <form id="edit-location-form" onsubmit="updateLocation(event, '${location.id}')">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع *</label>
                                        <input type="text" name="name" value="${location.name}" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">رمز الموقع</label>
                                        <input type="text" name="code" value="${location.code || ''}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الموقع</label>
                                        <select name="location_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option value="MAIN_WAREHOUSE" ${location.location_type === 'MAIN_WAREHOUSE' ? 'selected' : ''}>مستودع رئيسي</option>
                                            <option value="WAREHOUSE" ${location.location_type === 'WAREHOUSE' ? 'selected' : ''}>مستودع فرعي</option>
                                            <option value="SHOWROOM" ${location.location_type === 'SHOWROOM' ? 'selected' : ''}>صالة عرض</option>
                                            <option value="WORKSHOP" ${location.location_type === 'WORKSHOP' ? 'selected' : ''}>ورشة</option>
                                            <option value="OFFICE" ${location.location_type === 'OFFICE' ? 'selected' : ''}>مكتب</option>
                                            <option value="STORAGE_ROOM" ${location.location_type === 'STORAGE_ROOM' ? 'selected' : ''}>غرفة تخزين</option>
                                            <option value="SECTION" ${location.location_type === 'SECTION' ? 'selected' : ''}>قسم</option>
                                            <option value="SHELF" ${location.location_type === 'SHELF' ? 'selected' : ''}>رف</option>
                                            <option value="BIN" ${location.location_type === 'BIN' ? 'selected' : ''}>صندوق</option>
                                            <option value="RACK" ${location.location_type === 'RACK' ? 'selected' : ''}>رف معدني</option>
                                            <option value="ZONE" ${location.location_type === 'ZONE' ? 'selected' : ''}>منطقة</option>
                                            <option value="BAY" ${location.location_type === 'BAY' ? 'selected' : ''}>خليج</option>
                                            <option value="LEVEL" ${location.location_type === 'LEVEL' ? 'selected' : ''}>مستوى</option>
                                            <option value="COLD_STORAGE" ${location.location_type === 'COLD_STORAGE' ? 'selected' : ''}>تخزين بارد</option>
                                            <option value="HAZMAT_STORAGE" ${location.location_type === 'HAZMAT_STORAGE' ? 'selected' : ''}>تخزين مواد خطرة</option>
                                            <option value="RECEIVING_DOCK" ${location.location_type === 'RECEIVING_DOCK' ? 'selected' : ''}>رصيف الاستلام</option>
                                            <option value="SHIPPING_DOCK" ${location.location_type === 'SHIPPING_DOCK' ? 'selected' : ''}>رصيف الشحن</option>
                                            <option value="PICKING_AREA" ${location.location_type === 'PICKING_AREA' ? 'selected' : ''}>منطقة التجميع</option>
                                            <option value="PACKING_AREA" ${location.location_type === 'PACKING_AREA' ? 'selected' : ''}>منطقة التعبئة</option>
                                            <option value="QUALITY_CONTROL" ${location.location_type === 'QUALITY_CONTROL' ? 'selected' : ''}>مراقبة الجودة</option>
                                            <option value="RETURNS_AREA" ${location.location_type === 'RETURNS_AREA' ? 'selected' : ''}>منطقة المرتجعات</option>
                                            <option value="QUARANTINE" ${location.location_type === 'QUARANTINE' ? 'selected' : ''}>الحجر الصحي</option>
                                            <option value="TRANSIT" ${location.location_type === 'TRANSIT' ? 'selected' : ''}>نقل مؤقت</option>
                                            <option value="VIRTUAL" ${location.location_type === 'VIRTUAL' ? 'selected' : ''}>موقع افتراضي</option>
                                            <option value="EXTERNAL" ${location.location_type === 'EXTERNAL' ? 'selected' : ''}>موقع خارجي</option>
                                            <option value="CUSTOMER_SITE" ${location.location_type === 'CUSTOMER_SITE' ? 'selected' : ''}>موقع العميل</option>
                                            <option value="SUPPLIER_SITE" ${location.location_type === 'SUPPLIER_SITE' ? 'selected' : ''}>موقع المورد</option>
                                            <option value="OTHER" ${location.location_type === 'OTHER' ? 'selected' : ''}>أخرى</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">السعة القصوى</label>
                                        <input type="number" name="capacity" value="${location.capacity || ''}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الشخص المسؤول</label>
                                        <input type="text" name="contact_name" value="${location.contact_name || ''}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الهاتف</label>
                                        <input type="tel" name="phone" value="${location.phone || ''}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                    <input type="email" name="email" value="${location.email || ''}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                    <textarea name="address" rows="3" 
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">${location.address || ''}</textarea>
                                </div>
                                
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                                    <textarea name="notes" rows="3" 
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">${location.notes || ''}</textarea>
                                </div>
                                
                                <div class="mt-6 flex justify-end gap-3">
                                    <button type="button" onclick="closeEditLocationModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                        إلغاء
                                    </button>
                                    <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                                        <i class="fas fa-save"></i> حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalContent);
        } else {
            showNotification('لم يتم العثور على الموقع', 'error');
        }
    } catch (error) {
        console.error('Error loading location for edit:', error);
        showNotification('خطأ في تحميل بيانات الموقع', 'error');
    }
}

function manageLocationItems(locationId) {
    showNotification('سيتم إضافة وظيفة إدارة أصناف الموقع قريباً', 'info');
}

async function activateLocation(locationId) {
    showConfirmDialog(
        'تفعيل الموقع',
        'هل أنت متأكد من تفعيل هذا الموقع؟ سيتم السماح بجميع العمليات المرتبطة به.',
        async () => {
            try {
                const response = await fetch(`/api/supply-chain/locations/${locationId}/`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken(),
                    },
                    body: JSON.stringify({ is_active: true })
                });
                
                const data = await response.json();
                if (data.success) {
                    showNotification('تم تفعيل الموقع بنجاح', 'success');
                    loadLocationsData(); // Refresh the table
                } else {
                    showNotification('خطأ في تفعيل الموقع', 'error');
                }
            } catch (error) {
                console.error('Error activating location:', error);
                showNotification('خطأ في تفعيل الموقع', 'error');
            }
        },
        null,
        'تفعيل',
        'إلغاء'
    );
}

async function deactivateLocation(locationId) {
    showConfirmDialog(
        'إلغاء تفعيل الموقع',
        'هل أنت متأكد من إلغاء تفعيل هذا الموقع؟ سيتم إيقاف جميع العمليات المرتبطة به ولن يتمكن المستخدمون من الوصول إليه.',
        async () => {
            try {
                const response = await fetch(`/api/supply-chain/locations/${locationId}/`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken(),
                    },
                    body: JSON.stringify({ is_active: false })
                });
                
                const data = await response.json();
                if (data.success) {
                    showNotification('تم إلغاء تفعيل الموقع بنجاح', 'success');
                    loadLocationsData(); // Refresh the table
                } else {
                    showNotification('خطأ في إلغاء تفعيل الموقع', 'error');
                }
            } catch (error) {
                console.error('Error deactivating location:', error);
                showNotification('خطأ في إلغاء تفعيل الموقع', 'error');
            }
        },
        null,
        'إلغاء التفعيل',
        'إلغاء'
    );
}

// Quick Stock Adjustment Function
async function quickAdjustStock(itemId) {
    try {
        // Fetch item details first
        const response = await fetch(`/api/supply-chain/items/${itemId}/`);
        const data = await response.json();
        
        if (data.success) {
            const item = data.data;
            
            // Populate the quick adjust modal
            document.getElementById('quickAdjustItemId').value = item.id;
            document.getElementById('quickAdjustItemName').textContent = item.name;
            document.getElementById('quickAdjustItemSku').textContent = item.sku || '-';
            document.getElementById('quickAdjustCurrentQty').textContent = parseFloat(item.quantity).toFixed(2);
            
            // Handle batch selection if applicable
            const batchSection = document.getElementById('batchSelectionSection');
            const batchSelect = document.getElementById('batchSelect');
            
            if (item.batch_tracking_level && item.batch_tracking_level !== 'none') {
                // Load batches for this item
                const batchResponse = await fetch(`/api/supply-chain/items/${itemId}/batches/`);
                const batchData = await batchResponse.json();
                
                if (batchData.success && batchData.data.length > 0) {
                    batchSelect.innerHTML = '<option value="">اختر الدفعة للتعديل</option>';
                    batchData.data.forEach(batch => {
                        const option = document.createElement('option');
                        option.value = batch.id;
                        option.textContent = `${batch.batch_number} - الكمية: ${batch.quantity}${batch.expiry_date ? ` - انتهاء: ${new Date(batch.expiry_date).toLocaleDateString('ar-EG')}` : ''}`;
                        batchSelect.appendChild(option);
                    });
                    batchSection.style.display = 'block';
                } else {
                    batchSection.style.display = 'none';
                }
            } else {
                batchSection.style.display = 'none';
            }
            
            // Show the modal
            openModal('quickAdjustStockModal');
        }
    } catch (error) {
        console.error('Error loading item details:', error);
        showNotification('خطأ في تحميل بيانات الصنف', 'error');
    }
}

// Quick Purchase Order Creation Function
async function quickCreatePurchaseOrder(itemId) {
    try {
        // Fetch item details and suppliers
        const [itemResponse, suppliersResponse] = await Promise.all([
            fetch(`/api/supply-chain/items/${itemId}/`),
            fetch('/api/supply-chain/suppliers/')
        ]);
        
        const itemData = await itemResponse.json();
        const suppliersData = await suppliersResponse.json();
        
        if (itemData.success) {
            const item = itemData.data;
            
            // Populate the purchase order modal
            document.getElementById('purchaseOrderItemId').value = item.id;
            document.getElementById('purchaseOrderItemName').textContent = item.name;
            document.getElementById('purchaseOrderItemSku').textContent = item.sku || '-';
            document.getElementById('purchaseOrderCurrentQty').textContent = parseFloat(item.quantity).toFixed(2);
            document.getElementById('purchaseOrderMinStock').textContent = parseFloat(item.min_stock_level).toFixed(2);
            
            // Calculate suggested order quantity (2x minimum stock level)
            const suggestedQty = (item.min_stock_level * 2) - item.quantity;
            document.getElementById('orderQuantity').value = Math.max(suggestedQty, item.min_stock_level).toFixed(2);
            
            // Populate suppliers dropdown
            const supplierSelect = document.getElementById('supplierSelect');
            supplierSelect.innerHTML = '<option value="">اختر المورد</option>';
            
            if (suppliersData.success && suppliersData.data.length > 0) {
                suppliersData.data.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.id;
                    option.textContent = supplier.name;
                    supplierSelect.appendChild(option);
                });
            }
            
            // Handle batch information if applicable
            const batchOrderSection = document.getElementById('batchOrderSection');
            const expiryField = document.getElementById('expiryRequiredField');
            
            if (item.batch_tracking_level && item.batch_tracking_level !== 'none') {
                batchOrderSection.style.display = 'block';
                if (item.requires_expiry_tracking) {
                    expiryField.style.display = 'block';
                } else {
                    expiryField.style.display = 'none';
                }
            } else {
                batchOrderSection.style.display = 'none';
            }
            
            // Set default delivery date (7 days from now)
            const deliveryDate = new Date();
            deliveryDate.setDate(deliveryDate.getDate() + 7);
            document.querySelector('input[name="delivery_date"]').value = deliveryDate.toISOString().split('T')[0];
            
            // Show the modal
            openModal('quickPurchaseOrderModal');
        }
    } catch (error) {
        console.error('Error preparing purchase order:', error);
        showNotification('خطأ في تحضير أمر الشراء', 'error');
    }
}

function adjustStock(itemId) {
    quickAdjustStock(itemId);
}

function createPurchaseOrder(itemId) {
    quickCreatePurchaseOrder(itemId);
}

// Enhanced Toast notification system with Arabic UI
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    const typeConfig = {
        'success': {
            bg: 'bg-green-50',
            border: 'border-green-200',
            text: 'text-green-800',
            icon: 'fas fa-check-circle text-green-500',
            title: 'نجح'
        },
        'error': {
            bg: 'bg-red-50',
            border: 'border-red-200', 
            text: 'text-red-800',
            icon: 'fas fa-exclamation-circle text-red-500',
            title: 'خطأ'
        },
        'warning': {
            bg: 'bg-yellow-50',
            border: 'border-yellow-200',
            text: 'text-yellow-800', 
            icon: 'fas fa-exclamation-triangle text-yellow-500',
            title: 'تحذير'
        },
        'info': {
            bg: 'bg-blue-50',
            border: 'border-blue-200',
            text: 'text-blue-800',
            icon: 'fas fa-info-circle text-blue-500',
            title: 'معلومات'
        }
    };
    
    const config = typeConfig[type] || typeConfig['info'];
    
    notification.className = `fixed top-4 right-4 p-4 border rounded-lg shadow-lg z-50 max-w-sm transition-all duration-300 transform translate-x-0 ${config.bg} ${config.border} ${config.text}`;
    notification.style.animation = 'slideInRight 0.3s ease-out';
    
    notification.innerHTML = `
        <div class="flex items-start gap-3">
            <div class="flex-shrink-0">
                <i class="${config.icon} text-lg"></i>
            </div>
            <div class="flex-1 min-w-0">
                <div class="text-sm font-semibold mb-1">${config.title}</div>
                <div class="text-sm leading-5">${message}</div>
            </div>
            <button onclick="removeNotification(this)" class="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
        <div class="absolute bottom-0 left-0 h-1 bg-current opacity-20 transition-all duration-${duration}" style="width: 100%; animation: progressBar ${duration}ms linear;"></div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after specified duration
    setTimeout(() => {
        removeNotification(notification.querySelector('button'));
    }, duration);
}

function removeNotification(button) {
    const notification = button.closest('div[class*="fixed"]');
    if (notification) {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }
}

// Enhanced confirm dialog with Arabic UI
function showConfirmDialog(title, message, onConfirm, onCancel = null, confirmText = 'تأكيد', cancelText = 'إلغاء') {
    const modal = document.createElement('div');
    modal.id = 'confirmDialog';
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50';
    modal.style.animation = 'fadeIn 0.2s ease-out';
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-200" style="animation: scaleIn 0.2s ease-out;">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center gap-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-question-circle text-2xl text-blue-500"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
                </div>
            </div>
            <div class="px-6 py-4">
                <p class="text-gray-700 leading-6">${message}</p>
            </div>
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex gap-3 justify-end">
                <button onclick="closeConfirmDialog(false)" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-times text-sm"></i>
                    ${cancelText}
                </button>
                <button onclick="closeConfirmDialog(true)" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-check text-sm"></i>
                    ${confirmText}
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Store callback functions
    window.confirmDialogCallbacks = { onConfirm, onCancel };
}

function closeConfirmDialog(confirmed) {
    const modal = document.getElementById('confirmDialog');
    if (modal) {
        modal.style.animation = 'fadeOut 0.2s ease-in';
        setTimeout(() => {
            modal.remove();
            
            // Execute callbacks
            if (confirmed && window.confirmDialogCallbacks.onConfirm) {
                window.confirmDialogCallbacks.onConfirm();
            } else if (!confirmed && window.confirmDialogCallbacks.onCancel) {
                window.confirmDialogCallbacks.onCancel();
            }
            
            // Clean up
            delete window.confirmDialogCallbacks;
        }, 200);
    }
}

// Enhanced alert dialog with Arabic UI
function showAlertDialog(title, message, type = 'info', buttonText = 'موافق') {
    const typeConfig = {
        'success': {
            icon: 'fas fa-check-circle text-green-500',
            headerBg: 'bg-green-50',
            borderColor: 'border-green-200'
        },
        'error': {
            icon: 'fas fa-exclamation-circle text-red-500', 
            headerBg: 'bg-red-50',
            borderColor: 'border-red-200'
        },
        'warning': {
            icon: 'fas fa-exclamation-triangle text-yellow-500',
            headerBg: 'bg-yellow-50', 
            borderColor: 'border-yellow-200'
        },
        'info': {
            icon: 'fas fa-info-circle text-blue-500',
            headerBg: 'bg-blue-50',
            borderColor: 'border-blue-200'
        }
    };
    
    const config = typeConfig[type] || typeConfig['info'];
    
    const modal = document.createElement('div');
    modal.id = 'alertDialog';
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50';
    modal.style.animation = 'fadeIn 0.2s ease-out';
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-200 border ${config.borderColor}" style="animation: scaleIn 0.2s ease-out;">
            <div class="px-6 py-4 ${config.headerBg} rounded-t-lg border-b ${config.borderColor}">
                <div class="flex items-center gap-3">
                    <div class="flex-shrink-0">
                        <i class="${config.icon} text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
                </div>
            </div>
            <div class="px-6 py-4">
                <p class="text-gray-700 leading-6">${message}</p>
            </div>
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end">
                <button onclick="closeAlertDialog()" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-check text-sm"></i>
                    ${buttonText}
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

function closeAlertDialog() {
    const modal = document.getElementById('alertDialog');
    if (modal) {
        modal.style.animation = 'fadeOut 0.2s ease-in';
        setTimeout(() => {
            modal.remove();
        }, 200);
    }
}

// Existing functions...
function showTab(tabId, clickedElement = null) {
    // Prevent any automatic scrolling
    if (window.event) {
        window.event.preventDefault();
    }
    
    console.log('Switching to tab:', tabId);
    
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(tabId);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    
    // Add active class to clicked button
    if (clickedElement) {
        clickedElement.classList.add('active');
    } else if (window.event && window.event.target) {
        window.event.target.closest('.tab-button').classList.add('active');
    } else {
        // If called programmatically, find the button by onclick attribute
        const targetButton = document.querySelector(`button[onclick*="${tabId}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
        }
    }
    
    // Save active tab to localStorage only (no URL hash to prevent scrolling)
    localStorage.setItem('activeSupplyChainTab', tabId);
    console.log('Tab saved to localStorage:', tabId);
    
    // Load content for unified dashboard
    if (tabId === 'unified-dashboard') {
        loadUnifiedDashboard();
    }
}

function loadUnifiedDashboard() {
    const unifiedTab = document.getElementById('unified-dashboard');
    
    // Show loading state
    unifiedTab.innerHTML = `
        <div class="loading">
            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i>
            <div>جاري تحميل لوحة التحكم الموحدة...</div>
        </div>
    `;
    
    // Load the unified dashboard content via AJAX
    fetch('/core/unified-dashboard/')
        .then(response => response.text())
        .then(html => {
            // Extract just the content div from the response
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const content = doc.querySelector('.container-fluid, .container, .min-h-screen > div, main');
            
            if (content) {
                unifiedTab.innerHTML = content.innerHTML;
            } else {
                unifiedTab.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">خطأ في التحميل</h3>
                        <p class="text-gray-600">تعذر تحميل محتوى لوحة التحكم الموحدة</p>
                        <button onclick="loadUnifiedDashboard()" class="action-btn btn-primary mt-4">
                            <i class="fas fa-redo"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading unified dashboard:', error);
            unifiedTab.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">خطأ في الاتصال</h3>
                    <p class="text-gray-600">تعذر الاتصال بالخادم</p>
                    <button onclick="loadUnifiedDashboard()" class="action-btn btn-primary mt-4">
                        <i class="fas fa-redo"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        });
}

// Prevent auto scroll
function preventAutoScroll() {
    // Prevent hash changes from scrolling
    if ('scrollRestoration' in history) {
        history.scrollRestoration = 'manual';
    }
    
    // Prevent anchor scrolling
    window.addEventListener('hashchange', function(e) {
        e.preventDefault();
        return false;
    });
    
    // Override scroll to functions
    const originalScrollTo = window.scrollTo;
    const originalScrollBy = window.scrollBy;
    
    window.scrollTo = function(x, y) {
        // Only allow manual scrolling, not automatic
        if (arguments.length === 0) return;
        if (typeof x === 'object' && x.behavior === 'smooth') return;
        return originalScrollTo.call(this, x, y);
    };
    
    window.scrollBy = function(x, y) {
        // Only allow manual scrolling
        if (arguments.length === 0) return;
        if (typeof x === 'object' && x.behavior === 'smooth') return;
        return originalScrollBy.call(this, x, y);
    };
}

// Toggle batch tracking fields based on selected level
function toggleBatchFields(level) {
    const inventoryMethodField = document.getElementById('inventory-method-field');
    const batchPrefixField = document.getElementById('batch-prefix-field');
    const autoBatchField = document.getElementById('auto-batch-field');
    const expiryTrackingField = document.getElementById('expiry-tracking-field');
    const shelfLifeField = document.getElementById('shelf-life-field');

    if (level === 'none') {
        // Hide all batch-related fields
        inventoryMethodField.style.display = 'none';
        batchPrefixField.style.display = 'none';
        autoBatchField.style.display = 'none';
        expiryTrackingField.style.display = 'none';
        shelfLifeField.style.display = 'none';
    } else if (level === 'basic') {
        // Show basic batch tracking fields
        inventoryMethodField.style.display = 'block';
        batchPrefixField.style.display = 'block';
        autoBatchField.style.display = 'block';
        expiryTrackingField.style.display = 'none';
        shelfLifeField.style.display = 'none';
    } else if (level === 'full') {
        // Show all batch tracking fields
        inventoryMethodField.style.display = 'block';
        batchPrefixField.style.display = 'block';
        autoBatchField.style.display = 'block';
        expiryTrackingField.style.display = 'block';
        shelfLifeField.style.display = 'block';
    }
}
</script>
{% endblock %}