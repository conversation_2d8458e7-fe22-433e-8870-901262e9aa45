{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Supply Chain Management Hub" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/supply-chain-dashboard.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* Override any dark backgrounds */
    body {
        background-color: #f8fafc !important;
    }
    
    .dashboard-main {
        background-color: #f8fafc !important;
    }

    /* Tab container styling */
    .tab-container {
        background: white;
        border-radius: 0.75rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 1.5rem;
        overflow: hidden;
        display: flex;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .tab-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
        border: none;
        background: transparent;
        cursor: pointer;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        border-left: 1px solid #e5e7eb;
        color: #6b7280;
        text-decoration: none;
        min-width: 120px;
        flex: 1;
        gap: 0.5rem;
        min-height: 80px;
    }

    .tab-button:first-child {
        border-left: none;
    }

    .tab-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    }

    .tab-button.active {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .tab-button .tab-icon {
        width: 1.5rem;
        height: 1.5rem;
        margin-bottom: 0.25rem;
    }

    .tab-button .tab-label {
        font-weight: 600;
        text-align: center;
        font-size: 0.875rem;
    }

    .tab-button .tab-count {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 9999px;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 700;
        min-width: 1.5rem;
        text-align: center;
        line-height: 1;
    }

    .tab-button.active .tab-count {
        background-color: rgba(255, 255, 255, 0.2);
    }

    /* Tab content styling */
    .tab-content {
        display: none;
        animation: fadeIn 0.3s ease;
    }

    .tab-content.active {
        display: block;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Card styling */
    .dashboard-card {
        background: white;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 1rem;
        border-bottom: 1px solid #e5e7eb;
        text-align: center;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* Action buttons */
    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-warning { background-color: #f59e0b; color: white; }
    .btn-info { background-color: #06b6d4; color: white; }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Grid layouts */
    .grid-2 { display: grid; grid-template-columns: repeat(2, 1fr); gap: 1.5rem; }
    .grid-3 { display: grid; grid-template-columns: repeat(3, 1fr); gap: 1.5rem; }
    .grid-4 { display: grid; grid-template-columns: repeat(4, 1fr); gap: 1.5rem; }

    @media (max-width: 768px) {
        .grid-2, .grid-3, .grid-4 { 
            grid-template-columns: 1fr; 
        }
    }

    /* Statistics styling */
    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.875rem;
    }

    /* Loading state */
    .loading {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
    }

    /* RTL Support */
    html[dir="rtl"] .tab-button {
        border-left: none;
        border-right: 1px solid #e5e7eb;
    }

    html[dir="rtl"] .tab-button:first-child {
        border-right: none;
    }

    html[dir="rtl"] .tab-button:last-child {
        border-right: 1px solid #e5e7eb;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50" dir="rtl">
    <div class="container mx-auto px-4 py-6">
        
       

        <!-- Tab Navigation -->
        <div class="tab-container">
            <button class="tab-button active" onclick="showTab('main-dashboard')">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                </svg>
                <span class="tab-label">لوحة التحكم الرئيسية</span>
                <span class="tab-count">1</span>
            </button>
            
            <button class="tab-button" onclick="showTab('unified-dashboard')">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <span class="tab-label">لوحة التحكم الموحدة</span>
                <span class="tab-count">1</span>
            </button>
            
            <button class="tab-button" onclick="showTab('inventory-management')">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <span class="tab-label">إدارة المخزون</span>
                <span class="tab-count">245</span>
            </button>
            
            <button class="tab-button" onclick="showTab('warehouse-operations')">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="tab-label">عمليات المستودع</span>
                <span class="tab-count">8</span>
            </button>
            
            <button class="tab-button" onclick="showTab('purchasing')">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
                </svg>
                <span class="tab-label">المشتريات والتوريد</span>
                <span class="tab-count">7</span>
            </button>
        </div>

        <!-- Tab Content -->
        
        <!-- Main Dashboard Tab -->
        <div id="main-dashboard" class="tab-content active">
            <div class="grid-3 mb-6">
                <!-- Overview Statistics -->
                <div class="stat-card">
                    <div class="stat-number text-blue-600">245</div>
                    <div class="stat-label">إجمالي الأصناف</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-green-600">8</div>
                    <div class="stat-label">المستودعات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-orange-600">7</div>
                    <div class="stat-label">أوامر الشراء المفتوحة</div>
                </div>
            </div>
            
            <div class="grid-2">
                <!-- Quick Access Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">الوصول السريع</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button onclick="showTab('inventory-management')" class="action-btn btn-primary w-full">
                                <i class="fas fa-boxes"></i>
                                إدارة المخزون
                            </button>
                            <button onclick="showTab('warehouse-operations')" class="action-btn btn-success w-full">
                                <i class="fas fa-warehouse"></i>
                                عمليات المستودع
                            </button>
                            <button onclick="showTab('purchasing')" class="action-btn btn-warning w-full">
                                <i class="fas fa-shopping-cart"></i>
                                المشتريات والتوريد
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- System Status -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">حالة النظام</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">صحة المخزون</span>
                                    <span class="text-sm font-bold text-green-600">92%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 92%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">كفاءة المستودع</span>
                                    <span class="text-sm font-bold text-blue-600">88%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 88%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium">أداء المشتريات</span>
                                    <span class="text-sm font-bold text-yellow-600">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-600 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unified Dashboard Tab -->
        <div id="unified-dashboard" class="tab-content">
            <div class="loading">
                <i class="fas fa-spinner fa-spin text-2xl mb-4"></i>
                <div>جاري تحميل لوحة التحكم الموحدة...</div>
            </div>
        </div>

        <!-- Inventory Management Tab -->
        <div id="inventory-management" class="tab-content">
            <div class="grid-4 mb-6">
                <div class="stat-card">
                    <div class="stat-number text-blue-600">245</div>
                    <div class="stat-label">إجمالي الأصناف</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-red-600">12</div>
                    <div class="stat-label">مخزون منخفض</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-green-600">15</div>
                    <div class="stat-label">الفئات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-purple-600">1,234</div>
                    <div class="stat-label">إجمالي الحركات</div>
                </div>
            </div>
            
            <div class="grid-2">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">إدارة الأصناف</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button class="action-btn btn-primary w-full">
                                <i class="fas fa-plus"></i>
                                إضافة صنف جديد
                            </button>
                            <button class="action-btn btn-info w-full">
                                <i class="fas fa-list"></i>
                                عرض جميع الأصناف
                            </button>
                            <button class="action-btn btn-warning w-full">
                                <i class="fas fa-exclamation-triangle"></i>
                                تنبيهات المخزون المنخفض
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">حركات المخزون</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button class="action-btn btn-success w-full">
                                <i class="fas fa-plus-circle"></i>
                                تسجيل حركة جديدة
                            </button>
                            <button class="action-btn btn-info w-full">
                                <i class="fas fa-history"></i>
                                عرض سجل الحركات
                            </button>
                            <button class="action-btn btn-warning w-full">
                                <i class="fas fa-edit"></i>
                                تعديل مستوى المخزون
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Warehouse Operations Tab -->
        <div id="warehouse-operations" class="tab-content">
            <div class="grid-3 mb-6">
                <div class="stat-card">
                    <div class="stat-number text-green-600">8</div>
                    <div class="stat-label">المستودعات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-orange-600">3</div>
                    <div class="stat-label">التحويلات المعلقة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-blue-600">156</div>
                    <div class="stat-label">المواقع المسجلة</div>
                </div>
            </div>
            
            <div class="grid-2">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">إدارة المستودعات</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button class="action-btn btn-primary w-full">
                                <i class="fas fa-warehouse"></i>
                                إضافة مستودع جديد
                            </button>
                            <button class="action-btn btn-info w-full">
                                <i class="fas fa-map-marker-alt"></i>
                                إدارة المواقع
                            </button>
                            <button class="action-btn btn-success w-full">
                                <i class="fas fa-search-location"></i>
                                تتبع مواضع الأصناف
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">التحويلات</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button class="action-btn btn-primary w-full">
                                <i class="fas fa-truck"></i>
                                إنشاء تحويل جديد
                            </button>
                            <button class="action-btn btn-warning w-full">
                                <i class="fas fa-clock"></i>
                                التحويلات المعلقة
                            </button>
                            <button class="action-btn btn-info w-full">
                                <i class="fas fa-history"></i>
                                سجل التحويلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchasing Tab -->
        <div id="purchasing" class="tab-content">
            <div class="grid-3 mb-6">
                <div class="stat-card">
                    <div class="stat-number text-orange-600">7</div>
                    <div class="stat-label">أوامر الشراء المفتوحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-blue-600">15</div>
                    <div class="stat-label">الموردون النشطون</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number text-green-600">89%</div>
                    <div class="stat-label">معدل الأداء</div>
                </div>
            </div>
            
            <div class="grid-2">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">أوامر الشراء</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button class="action-btn btn-primary w-full">
                                <i class="fas fa-plus"></i>
                                إنشاء أمر شراء جديد
                            </button>
                            <button class="action-btn btn-warning w-full">
                                <i class="fas fa-clock"></i>
                                الأوامر في انتظار الموافقة
                            </button>
                            <button class="action-btn btn-info w-full">
                                <i class="fas fa-list"></i>
                                عرض جميع الأوامر
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="font-semibold text-gray-900">إدارة الموردين</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-3">
                            <button class="action-btn btn-primary w-full">
                                <i class="fas fa-user-plus"></i>
                                إضافة مورد جديد
                            </button>
                            <button class="action-btn btn-info w-full">
                                <i class="fas fa-users"></i>
                                عرض جميع الموردين
                            </button>
                            <button class="action-btn btn-success w-full">
                                <i class="fas fa-chart-line"></i>
                                تقييم أداء الموردين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabId) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(tabId);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    
    // Add active class to clicked button
    event.target.closest('.tab-button').classList.add('active');
    
    // Load content for unified dashboard
    if (tabId === 'unified-dashboard') {
        loadUnifiedDashboard();
    }
}

function loadUnifiedDashboard() {
    const unifiedTab = document.getElementById('unified-dashboard');
    
    // Show loading state
    unifiedTab.innerHTML = `
        <div class="loading">
            <i class="fas fa-spinner fa-spin text-2xl mb-4"></i>
            <div>جاري تحميل لوحة التحكم الموحدة...</div>
        </div>
    `;
    
    // Load the unified dashboard content via AJAX
    fetch('{% url "core:unified_supply_chain_dashboard" %}')
        .then(response => response.text())
        .then(html => {
            // Extract just the content div from the response
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const content = doc.querySelector('.container-fluid, .container, .min-h-screen > div, main');
            
            if (content) {
                unifiedTab.innerHTML = content.innerHTML;
            } else {
                unifiedTab.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">خطأ في التحميل</h3>
                        <p class="text-gray-600">تعذر تحميل محتوى لوحة التحكم الموحدة</p>
                        <button onclick="loadUnifiedDashboard()" class="action-btn btn-primary mt-4">
                            <i class="fas fa-redo"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading unified dashboard:', error);
            unifiedTab.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">خطأ في الاتصال</h3>
                    <p class="text-gray-600">تعذر الاتصال بالخادم</p>
                    <button onclick="loadUnifiedDashboard()" class="action-btn btn-primary mt-4">
                        <i class="fas fa-redo"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Set first tab as active on page load
    showTab('main-dashboard');
});
</script>
{% endblock %}