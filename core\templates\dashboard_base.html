{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load waffle_tags %}

{% block title %}{% trans "لوحة التحكم" %} - {{ block.super }}{% endblock %}

{% block body_class %}dashboard-layout{% endblock %}

{% block extra_css %}
<!-- Dashboard specific styles -->
<style>
    .dashboard-sidebar {
        transition: transform 0.3s ease-in-out;
        box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    }
    
    .dashboard-main {
        min-height: calc(100vh - 8rem);
    }
    
    .notification {
        animation: slideInDown 0.3s ease-out;
    }
    
    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .sidebar-menu li a {
        transition: all 0.2s ease;
    }
    
    .sidebar-menu li a:hover {
        background-color: #f3f4f6;
        padding-right: 1.5rem;
    }
    
    .sidebar-menu li a.active {
        background-color: #3b82f6;
        color: white;
        border-radius: 0.5rem;
    }
    
    .loading-spinner {
        border: 2px solid #f3f4f6;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        width: 1rem;
        height: 1rem;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="flex h-screen bg-gray-50">
    <!-- Sidebar -->
    <div id="sidebar" class="dashboard-sidebar fixed inset-y-0 right-0 z-50 w-64 bg-white border-l border-gray-200 transform lg:translate-x-0 lg:static lg:inset-0">
        <!-- Sidebar header -->
        <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <div class="flex items-center">
                <i class="fas fa-tachometer-alt text-blue-600 text-xl mr-2"></i>
                <h2 class="text-lg font-semibold text-gray-800">{% trans "لوحة التحكم" %}</h2>
            </div>
            <button id="sidebar-close" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <!-- User info -->
        {% if user.is_authenticated %}
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    {{ user.first_name|first|default:user.username|first|upper }}
                </div>
                <div class="mr-3">
                    <p class="text-sm font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</p>
                    <p class="text-xs text-gray-500">{{ user.email|default:"" }}</p>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Navigation menu -->
        <nav class="mt-4 px-4">
            <ul class="sidebar-menu space-y-2">
                <!-- Dashboard -->
                <li>
                    <a href="{% url 'core:main_dashboard' %}" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
                        <i class="fas fa-home ml-3 text-gray-400"></i>
                        {% trans "الرئيسية" %}
                    </a>
                </li>
                
                <!-- Work Orders -->
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="work-orders-menu">
                        <div class="flex items-center">
                            <i class="fas fa-wrench ml-3 text-gray-400"></i>
                            {% trans "أوامر العمل" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="work-orders-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="{% url 'work_orders:work_order_list' %}" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "قائمة أوامر العمل" %}</a></li>
                        <li><a href="{% url 'work_orders:work_order_create' %}" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "إنشاء أمر عمل جديد" %}</a></li>
                    </ul>
                </li>
                
                <!-- Inventory -->
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="inventory-menu">
                        <div class="flex items-center">
                            <i class="fas fa-boxes ml-3 text-gray-400"></i>
                            {% trans "المخزون" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="inventory-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "قائمة الأصناف" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "حركة المخزون" %}</a></li>
                    </ul>
                </li>
                
                <!-- Other modules... -->
            </ul>
        </nav>
    </div>
    
    <!-- Main content -->
    <div class="dashboard-main flex-1 flex flex-col overflow-hidden lg:mr-64">
        <!-- Top bar -->
        <header class="bg-white border-b border-gray-200 px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <button id="sidebar-toggle" class="lg:hidden mr-4 p-2 rounded-md text-gray-400 hover:text-gray-600">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-semibold text-gray-900">{% block page_title %}{% trans "لوحة التحكم" %}{% endblock %}</h1>
                        <p class="text-sm text-gray-500">{% block page_subtitle %}{% trans "مرحباً بك في نظام إدارة الامتياز" %}{% endblock %}</p>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page content -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
            <!-- Notifications container -->
            <div id="notifications-container" class="fixed top-20 left-4 right-4 z-[99999] space-y-2"></div>
            
            {% block dashboard_content %}
            <!-- Default dashboard content -->
            <div class="text-center py-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">{% trans "مرحباً بك في لوحة التحكم" %}</h2>
                <p class="text-gray-600">{% trans "اختر من القائمة الجانبية للبدء" %}</p>
            </div>
            {% endblock %}
        </main>
    </div>
</div>

<script>
// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Submenu toggles
    const submenuToggles = document.querySelectorAll('[data-toggle="submenu"]');
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const target = document.getElementById(targetId);
            const chevron = this.querySelector('.fa-chevron-down');
            
            if (target) {
                target.classList.toggle('hidden');
                chevron.classList.toggle('rotate-180');
            }
        });
    });
});
</script>
{% endblock %} 