from django import template
from django.template.defaultfilters import stringfilter

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """Get an item from a dictionary by key"""
    if dictionary is None:
        return None
    
    # Handle dot notation for nested dictionaries
    if '.' in key:
        parts = key.split('.')
        value = dictionary
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            elif hasattr(value, part):
                value = getattr(value, part)
            else:
                return None
        return value
    
    # Handle standard dictionary access
    if isinstance(dictionary, dict) and key in dictionary:
        return dictionary[key]
    
    # Handle object attribute access
    if hasattr(dictionary, key):
        return getattr(dictionary, key)
    
    # If all else fails, try various access methods:
    try:
        return dictionary[key]
    except (KeyError, TypeError):
        pass
    
    try:
        return getattr(dictionary, key)
    except (AttributeError, TypeError):
        pass
    
    try:
        return dictionary.get(key)
    except (AttributeError, TypeError):
        return None

@register.filter
@stringfilter
def truncate_middle(value, length=50):
    """Truncate text in the middle, preserving start and end"""
    if len(value) <= length:
        return value
    
    # Determine how much to show at the beginning and end
    half_length = (length - 3) // 2
    start = value[:half_length]
    end = value[-half_length:]
    
    return f"{start}...{end}"

@register.simple_tag(takes_context=True)
def is_active_url(context, url_name, exact=False, **kwargs):
    """Check if the current URL matches the given URL name"""
    request = context.get('request')
    if not request:
        return ""
    
    # Get the current path
    current_path = request.path
    
    # Attempt to reverse the URL
    try:
        from django.urls import reverse
        url = reverse(url_name, kwargs=kwargs)
        
        if exact:
            # Exact matching
            return "active" if current_path == url else ""
        else:
            # Prefix matching
            return "active" if current_path.startswith(url) else ""
    except:
        return ""

@register.filter
def as_form_field_class(field):
    """Return appropriate form field class based on field type"""
    widget_type = field.field.widget.__class__.__name__.lower()
    
    if 'checkbox' in widget_type:
        return 'form-checkbox'
    elif 'radio' in widget_type:
        return 'form-radio'
    elif 'select' in widget_type:
        return 'form-select'
    elif 'textarea' in widget_type:
        return 'form-textarea'
    else:
        return 'form-input' 