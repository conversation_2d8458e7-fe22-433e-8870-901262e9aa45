from django.urls import path, include
from . import views

app_name = 'core'

urlpatterns = [
    path('', views.main_dashboard, name='main_dashboard'),
    path('supply-chain/', views.supply_chain_dashboard, name='supply_chain_dashboard'),
    
    # ========== SUPPLY CHAIN MANAGEMENT SYSTEM ==========
    # Main Supply Chain Hub
    path('supply-chain/overview/', views.supply_chain_overview, name='supply_chain_overview'),
    path('supply-chain/unified/', views.unified_supply_chain_dashboard, name='unified_supply_chain_dashboard'),
    
    # Inventory Management Hub
    path('supply-chain/inventory/', views.supply_chain_inventory_hub, name='supply_chain_inventory_hub'),
    path('supply-chain/inventory/items/', views.supply_chain_items_list, name='supply_chain_items_list'),
    path('supply-chain/inventory/items/create/', views.supply_chain_item_create, name='supply_chain_item_create'),
    path('supply-chain/inventory/items/<uuid:pk>/', views.supply_chain_item_detail, name='supply_chain_item_detail'),
    path('supply-chain/inventory/categories/', views.supply_chain_categories, name='supply_chain_categories'),
    path('supply-chain/inventory/movements/', views.supply_chain_movements, name='supply_chain_movements'),
    path('supply-chain/inventory/low-stock/', views.supply_chain_low_stock, name='supply_chain_low_stock'),
    path('supply-chain/inventory/stock-adjustment/', views.supply_chain_stock_adjustment, name='supply_chain_stock_adjustment'),
    
    # Warehouse Management Hub  
    path('supply-chain/warehouse/', views.supply_chain_warehouse_hub, name='supply_chain_warehouse_hub'),
    path('supply-chain/warehouse/list/', views.supply_chain_warehouses_list, name='supply_chain_warehouses_list'),
    path('supply-chain/warehouse/create/', views.supply_chain_warehouse_create, name='supply_chain_warehouse_create'),
    path('supply-chain/warehouse/locations/', views.supply_chain_locations, name='supply_chain_locations'),
    path('supply-chain/warehouse/locations/list/', views.supply_chain_locations_list, name='supply_chain_locations_list'),
    path('supply-chain/warehouse/locations/create/', views.supply_chain_location_create, name='supply_chain_location_create'),
    path('supply-chain/warehouse/transfers/', views.supply_chain_transfers, name='supply_chain_transfers'),
    path('supply-chain/warehouse/transfer-orders/', views.supply_chain_transfer_orders, name='supply_chain_transfer_orders'),
    path('supply-chain/warehouse/transfers/create/', views.supply_chain_transfer_create, name='supply_chain_transfer_create'),
    path('supply-chain/warehouse/transfers/<uuid:pk>/', views.supply_chain_transfer_detail, name='supply_chain_transfer_detail'),
    path('supply-chain/warehouse/transfers/pending/', views.supply_chain_pending_transfers, name='supply_chain_pending_transfers'),
    path('supply-chain/warehouse/item-locations/', views.supply_chain_item_locations, name='supply_chain_item_locations'),
    path('supply-chain/warehouse/capacity-report/', views.supply_chain_capacity_report, name='supply_chain_capacity_report'),
    path('supply-chain/warehouse/efficiency-report/', views.supply_chain_efficiency_report, name='supply_chain_efficiency_report'),
    
    # Purchase Management Hub
    path('supply-chain/purchases/', views.supply_chain_purchase_hub, name='supply_chain_purchase_hub'),
    path('supply-chain/purchases/orders/', views.supply_chain_purchase_orders, name='supply_chain_purchase_orders'),
    path('supply-chain/purchases/orders/create/', views.supply_chain_purchase_order_create, name='supply_chain_purchase_order_create'),
    path('supply-chain/purchases/orders/<uuid:pk>/', views.supply_chain_purchase_order_detail, name='supply_chain_purchase_order_detail'),
    path('supply-chain/purchases/orders/pending/', views.supply_chain_pending_orders, name='supply_chain_pending_orders'),
    path('supply-chain/purchases/orders/overdue/', views.supply_chain_overdue_orders, name='supply_chain_overdue_orders'),
    path('supply-chain/purchases/orders/approval-pending/', views.supply_chain_approval_pending, name='supply_chain_approval_pending'),
    path('supply-chain/purchases/suppliers/', views.supply_chain_suppliers, name='supply_chain_suppliers'),
    path('supply-chain/purchases/suppliers/create/', views.supply_chain_supplier_create, name='supply_chain_supplier_create'),
    path('supply-chain/purchases/suppliers/performance/', views.supply_chain_supplier_performance, name='supply_chain_supplier_performance'),
    path('supply-chain/purchases/receipts/', views.supply_chain_receipts, name='supply_chain_receipts'),
    path('supply-chain/purchases/analytics/', views.supply_chain_purchase_analytics, name='supply_chain_purchase_analytics'),
    path('supply-chain/purchases/savings-report/', views.supply_chain_savings_report, name='supply_chain_savings_report'),
    
    # Shipping & Logistics
    path('supply-chain/shipments/incoming/', views.supply_chain_incoming_shipments, name='supply_chain_incoming_shipments'),
    
    # Workflow & Process Management
    path('supply-chain/workflow/', views.supply_chain_workflow, name='supply_chain_workflow'),
    path('supply-chain/workflow/purchase-to-inventory/', views.supply_chain_purchase_to_inventory, name='supply_chain_purchase_to_inventory'),
    path('supply-chain/workflow/stock-replenishment/', views.supply_chain_stock_replenishment, name='supply_chain_stock_replenishment'),
    path('supply-chain/workflow/transfer-management/', views.supply_chain_transfer_management, name='supply_chain_transfer_management'),
    
    # Reports & Analytics
    path('supply-chain/reports/', views.supply_chain_reports, name='supply_chain_reports'),
    path('supply-chain/reports/inventory-summary/', views.supply_chain_inventory_report, name='supply_chain_inventory_report'),
    path('supply-chain/reports/purchase-analysis/', views.supply_chain_purchase_report, name='supply_chain_purchase_report'),
    path('supply-chain/reports/warehouse-utilization/', views.supply_chain_warehouse_report, name='supply_chain_warehouse_report'),
    
    # Quick Actions
    path('supply-chain/actions/', views.supply_chain_quick_actions, name='supply_chain_quick_actions'),
    path('supply-chain/actions/bulk-stock-update/', views.supply_chain_bulk_stock_update, name='supply_chain_bulk_stock_update'),
    path('supply-chain/actions/emergency-reorder/', views.supply_chain_emergency_reorder, name='supply_chain_emergency_reorder'),
    
    # Original nested URLs (for backward compatibility)
    path('supply-chain/inventory/', include('inventory.urls')),
    path('supply-chain/warehouse/', include('warehouse.urls')),
    path('supply-chain/purchases/', include('purchases.urls')),
    
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('post-login-redirect/', views.post_login_redirect, name='post_login_redirect'),
    
    # Analytics API endpoints
    path('api/analytics/stranded-items/', views.analytics_stranded_items, name='analytics_stranded_items'),
    path('api/analytics/work-order-status/', views.analytics_work_order_status, name='analytics_work_order_status'),
    
    # Dashboard API endpoints
    path('api/recent-activities/', views.api_recent_activities, name='api_recent_activities'),
    path('api/recent-sales-orders/', views.api_recent_sales_orders, name='api_recent_sales_orders'),
    
    # Enhanced Supply Chain API endpoints
    path('api/supply-chain/metrics/', views.api_supply_chain_metrics, name='api_supply_chain_metrics'),
    path('api/supply-chain/workflow-status/', views.api_workflow_status, name='api_workflow_status'),
    path('api/supply-chain/alerts/', views.api_alerts_notifications, name='api_alerts_notifications'),
    path('api/supply-chain/quick-actions/', views.api_quick_actions, name='api_quick_actions'),
    path('api/supply-chain/performance/', views.api_performance_analytics, name='api_performance_analytics'),
    path('api/supply-chain/warehouses/', views.api_warehouse_create, name='api_warehouse_create'),
] 