from django.conf import settings
from core.middleware import get_current_tenant_id
import uuid

def get_tenant_id_from_request(request):
    """
    Get tenant_id from request using the proper method.
    The User model does not have tenant_id, so we get it from:
    1. request.tenant_id (set by middleware)
    2. Thread local storage
    3. Session
    4. Default fallback
    """
    # First try: request.tenant_id (set by CurrentTenantMiddleware)
    tenant_id = getattr(request, 'tenant_id', None)
    
    # Second try: thread local storage
    if not tenant_id:
        tenant_id = get_current_tenant_id()
    
    # Third try: session
    if not tenant_id and hasattr(request, 'session'):
        tenant_id = request.session.get('tenant_id', None)
    
    # Fourth try: from request parameters (for testing/API)
    if not tenant_id:
        tenant_id = request.GET.get('tenant_id') or request.POST.get('tenant_id')
    
    # Convert to UUID if it's a string
    if tenant_id and isinstance(tenant_id, str):
        try:
            tenant_id = uuid.UUID(tenant_id)
        except (ValueError, TypeError):
            tenant_id = None
    
    # Fallback: use a default tenant_id for single-tenant setups
    if not tenant_id:
        # In multi-tenant systems, this should ideally throw an error
        # For now, we'll use the actual tenant_id from the data
        tenant_id = uuid.UUID('979c54ab-b52c-4f12-a887-65c8baae7788')
    
    return tenant_id

def get_user_role_info(user):
    """
    Get user role information for the authenticated user.
    Returns the primary role and available permissions.
    """
    if not user.is_authenticated:
        return None, {}
    
    # Superuser has all permissions
    if user.is_superuser:
        return None, {
            'can_access_inventory': True,
            'can_access_warehouse': True,
            'can_access_purchases': True,
            'can_access_sales': True,
            'can_access_billing': True,
            'can_access_work_orders': True,
            'can_access_reports': True,
            'can_access_setup': True,
            'can_access_settings': True,
        }
    
    try:
        # Get user roles
        user_roles = user.user_roles.filter(is_active=True)
        
        # Find primary role
        primary_role = None
        for user_role in user_roles:
            if user_role.is_primary:
                primary_role = user_role
                break
        
        # If no primary role found, use the first active role
        if not primary_role and user_roles.exists():
            primary_role = user_roles.first()
        
        # Build permissions dictionary
        permissions = {}
        if primary_role and primary_role.role:
            # Access permissions from the Role object, not the UserRole object
            permissions = {
                'can_access_inventory': primary_role.role.can_access_inventory,
                'can_access_warehouse': primary_role.role.can_access_warehouse,
                'can_access_purchases': primary_role.role.can_access_purchases,
                'can_access_sales': primary_role.role.can_access_sales,
                'can_access_billing': getattr(primary_role.role, 'can_access_billing', False),
                'can_access_work_orders': primary_role.role.can_access_work_orders,
                'can_access_reports': primary_role.role.can_access_reports,
                'can_access_setup': primary_role.role.can_access_setup,
                'can_access_settings': getattr(primary_role.role, 'can_access_settings', False),
            }
        else:
            # Default permissions if no role found
            permissions = {
                'can_access_inventory': False,
                'can_access_warehouse': False,
                'can_access_purchases': False,
                'can_access_sales': False,
                'can_access_billing': False,
                'can_access_work_orders': False,
                'can_access_reports': False,
                'can_access_setup': False,
                'can_access_settings': False,
            }
        
        return primary_role, permissions
        
    except Exception as e:
        print(f"Error getting user role info: {str(e)}")
        # Return default permissions on error
        return None, {
            'can_access_inventory': False,
            'can_access_warehouse': False,
            'can_access_purchases': False,
            'can_access_sales': False,
            'can_access_billing': False,
            'can_access_work_orders': False,
            'can_access_reports': False,
            'can_access_setup': False,
            'can_access_settings': False,
        } 