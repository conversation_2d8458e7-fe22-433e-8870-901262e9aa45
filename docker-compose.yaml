version: "3.3"

networks:
  frontend:
  backend:

volumes:
  postgres_data:
  static:
  media:

services:
  app:
    restart: always
    build: .
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - static:/static
      - media:/media
    container_name: AfterSales-BE-App
    env_file: .env
    depends_on:
      - db
    ports:
      - "8137:8000"
    networks:
      - backend
      - frontend
  db:
    restart: always
    image: postgres:14
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - postgres_data:/var/lib/postgresql/data/
    container_name: AfterSales-BE-DB
    env_file: .env
    ports:
      - "8136:5432"
    networks:
      - backend
  nginx:
    restart: always
    image: ramy7elmygb/pcapi:nginx
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - static:/static
      - media:/media
    container_name: AfterSales-BE-Nginx
    env_file: .env
    ports:
      - "8135:443"
    depends_on:
      - app
    networks:
      - frontend