#db
POSTGRES_DB=postgres
POSTGRES_PASSWORD=
POSTGRES_USER=postgres

#settings
ENV=TEST #( LOCAL, TEST, PRODUCTION, DR )
DEBUG=True
SECRET_KEY=
TENANT_ID=979c54ab-b52c-4f12-a887-65c8baae7788
SQL_ENGINE=django.db.backends.postgresql
SQL_DATABASE=postgres
SQL_USER=postgres
SQL_PASSWORD=
SQL_HOST=Project-DB
SQL_PORT=5432
EMAIL_HOST=
EMAIL_PORT=
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
SENTRY_DSN=

# Work Order Automation Settings
WORK_ORDER_AUTO_CREATE_SALES_ORDER=true
SALES_ORDER_AUTO_CREATE_INVOICE=true

