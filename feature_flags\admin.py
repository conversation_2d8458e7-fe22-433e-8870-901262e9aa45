from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from feature_flags.models import ModuleFlag, TenantModuleFlag, FeatureFlag, TenantFeatureFlag


class FeatureFlagInline(admin.TabularInline):
    model = FeatureFlag
    extra = 0


@admin.register(ModuleFlag)
class ModuleFlagAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'description')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    inlines = [FeatureFlagInline]


@admin.register(TenantModuleFlag)
class TenantModuleFlagAdmin(admin.ModelAdmin):
    list_display = ('module_flag', 'tenant_id', 'is_active')
    list_filter = ('is_active', 'module_flag')
    search_fields = ('tenant_id', 'module_flag__name')


@admin.register(FeatureFlag)
class FeatureFlagAdmin(admin.ModelAdmin):
    list_display = ('name', 'module', 'is_active', 'description')
    list_filter = ('is_active', 'module')
    search_fields = ('name', 'description')


@admin.register(TenantFeatureFlag)
class TenantFeatureFlagAdmin(admin.ModelAdmin):
    list_display = ('feature_flag', 'tenant_id', 'is_active')
    list_filter = ('is_active', 'feature_flag', 'feature_flag__module')
    search_fields = ('tenant_id', 'feature_flag__name')
