from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from feature_flags.models import ModuleFlag, FeatureFlag


class Command(BaseCommand):
    help = _('Create default module and feature flags')

    def handle(self, *args, **options):
        # Define modules
        modules = [
            {
                'name': 'inventory',
                'description': _('Inventory management module'),
                'is_active': True,
                'features': [
                    {
                        'name': 'inventory_advanced',
                        'description': _('Advanced inventory features like stock movements'),
                        'is_active': True,
                    },
                    {
                        'name': 'barcode_scanning',
                        'description': _('Barcode scanning for inventory items'),
                        'is_active': False,
                    },
                ]
            },
            {
                'name': 'warehouse',
                'description': _('Warehouse and location management'),
                'is_active': True,
                'features': [
                    {
                        'name': 'multi_location',
                        'description': _('Multiple warehouse locations'),
                        'is_active': True,
                    },
                    {
                        'name': 'bin_tracking',
                        'description': _('Bin location tracking within warehouses'),
                        'is_active': False,
                    },
                ]
            },
            {
                'name': 'sales',
                'description': _('Sales orders and invoicing'),
                'is_active': False,
                'features': [
                    {
                        'name': 'sales_orders',
                        'description': _('Create and manage sales orders'),
                        'is_active': False,
                    },
                    {
                        'name': 'sales_returns',
                        'description': _('Process sales returns'),
                        'is_active': False,
                    },
                ]
            },
            {
                'name': 'purchases',
                'description': _('Purchase orders and vendor management'),
                'is_active': False,
                'features': [
                    {
                        'name': 'purchase_orders',
                        'description': _('Create and manage purchase orders'),
                        'is_active': False,
                    },
                    {
                        'name': 'vendor_management',
                        'description': _('Manage vendors and suppliers'),
                        'is_active': False,
                    },
                ]
            },
            {
                'name': 'reports',
                'description': _('Reporting and analytics'),
                'is_active': False,
                'features': [
                    {
                        'name': 'inventory_reports',
                        'description': _('Inventory valuation and stock reports'),
                        'is_active': False,
                    },
                    {
                        'name': 'sales_reports',
                        'description': _('Sales performance reports'),
                        'is_active': False,
                    },
                ]
            },
            {
                'name': 'api',
                'description': _('API access for integration with other systems'),
                'is_active': False,
                'features': [
                    {
                        'name': 'api_read',
                        'description': _('Read-only API access'),
                        'is_active': False,
                    },
                    {
                        'name': 'api_write',
                        'description': _('Write API access'),
                        'is_active': False,
                    },
                ]
            },
        ]
        
        # Create modules and features
        for module_data in modules:
            features_data = module_data.pop('features', [])
            module, created = ModuleFlag.objects.update_or_create(
                name=module_data['name'],
                defaults=module_data
            )
            
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created module {module.name}'))
            else:
                self.stdout.write(self.style.SUCCESS(f'Updated module {module.name}'))
                
            # Create features for this module
            for feature_data in features_data:
                feature, feature_created = FeatureFlag.objects.update_or_create(
                    name=feature_data['name'],
                    defaults={**feature_data, 'module': module}
                )
                
                if feature_created:
                    self.stdout.write(self.style.SUCCESS(f'  Created feature {feature.name}'))
                else:
                    self.stdout.write(self.style.SUCCESS(f'  Updated feature {feature.name}'))
                    
        self.stdout.write(self.style.SUCCESS('All done!')) 