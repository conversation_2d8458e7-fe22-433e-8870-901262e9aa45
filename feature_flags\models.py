from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet


class ModuleFlag(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    System-wide module flags for enabling/disabling entire modules
    """
    name = models.CharField(_("Name"), max_length=100, unique=True)
    description = models.TextField(_("Description"), blank=True)
    is_active = models.BooleanField(_("Active"), default=False)
    
    class Meta:
        verbose_name = _("Module Flag")
        verbose_name_plural = _("Module Flags")
        
    def __str__(self):
        return f"{self.name} ({'On' if self.is_active else 'Off'})"


class TenantModuleFlag(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Tenant-specific module flags that override system defaults
    """
    module_flag = models.ForeignKey(ModuleFlag, on_delete=models.CASCADE, related_name='tenant_flags')
    is_active = models.BooleanField(_("Active"), default=False)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Tenant Module Flag")
        verbose_name_plural = _("Tenant Module Flags")
        unique_together = [['tenant_id', 'module_flag']]
        
    def __str__(self):
        return f"{self.module_flag.name} for tenant {self.tenant_id} ({'On' if self.is_active else 'Off'})"


class FeatureFlag(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    System-wide feature flags for granular feature control within modules
    """
    name = models.CharField(_("Name"), max_length=100, unique=True)
    description = models.TextField(_("Description"), blank=True)
    module = models.ForeignKey(ModuleFlag, on_delete=models.CASCADE, related_name='features')
    is_active = models.BooleanField(_("Active"), default=False)
    
    class Meta:
        verbose_name = _("Feature Flag")
        verbose_name_plural = _("Feature Flags")
        
    def __str__(self):
        return f"{self.name} ({'On' if self.is_active else 'Off'})"


class TenantFeatureFlag(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Tenant-specific feature flags that override system defaults
    """
    feature_flag = models.ForeignKey(FeatureFlag, on_delete=models.CASCADE, related_name='tenant_flags')
    is_active = models.BooleanField(_("Active"), default=False)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Tenant Feature Flag")
        verbose_name_plural = _("Tenant Feature Flags")
        unique_together = [['tenant_id', 'feature_flag']]
        
    def __str__(self):
        return f"{self.feature_flag.name} for tenant {self.tenant_id} ({'On' if self.is_active else 'Off'})"
