from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    FranchiseTemplate, 
    FranchiseAgreement, 
    FranchiseFee, 
    RevenueShare, 
    FranchiseRequirement,
    FranchiseCompliance
)


class FranchiseRequirementInline(admin.TabularInline):
    model = FranchiseRequirement
    extra = 0
    fields = ('name', 'requirement_type', 'is_mandatory', 'validation_method', 'validation_frequency')


@admin.register(FranchiseTemplate)
class FranchiseTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'default_term_years', 'default_royalty_percentage', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'is_active')
        }),
        (_('Agreement Defaults'), {
            'fields': ('default_term_years', 'default_renewal_options', 'default_territory_definition')
        }),
        (_('Financial Defaults'), {
            'fields': ('default_initial_fee', 'default_royalty_percentage', 
                      'default_marketing_fee_percentage', 'default_minimum_performance')
        }),
        (_('Operational Defaults'), {
            'fields': ('default_training_requirements', 'default_operational_standards', 
                      'default_technology_requirements')
        }),
        (_('Documentation'), {
            'fields': ('agreement_template', 'operations_manual_template')
        }),
    )
    inlines = [FranchiseRequirementInline]


class FranchiseFeeInline(admin.StackedInline):
    model = FranchiseFee
    extra = 0


@admin.register(FranchiseAgreement)
class FranchiseAgreementAdmin(admin.ModelAdmin):
    list_display = ('franchise', 'name', 'start_date', 'end_date', 'status', 'is_active')
    list_filter = ('status', 'is_active')
    search_fields = ('name', 'franchise__name', 'territory_definition')
    date_hierarchy = 'start_date'
    raw_id_fields = ('franchise', 'template')
    fieldsets = (
        (None, {
            'fields': ('franchise', 'template', 'name')
        }),
        (_('Agreement Dates'), {
            'fields': ('start_date', 'end_date', 'signed_date')
        }),
        (_('Territory Information'), {
            'fields': ('territory_definition', 'territory_exclusivity')
        }),
        (_('Agreement Details'), {
            'fields': ('term_years', 'renewal_options', 'renewal_terms', 'termination_conditions')
        }),
        (_('Status'), {
            'fields': ('is_active', 'status')
        }),
        (_('Documentation'), {
            'fields': ('agreement_document', 'additional_documents')
        }),
        (_('Notes'), {
            'fields': ('notes',)
        }),
    )
    inlines = [FranchiseFeeInline]


@admin.register(RevenueShare)
class RevenueShareAdmin(admin.ModelAdmin):
    list_display = ('franchise', 'year', 'quarter', 'total_revenue', 'royalty_amount', 'is_verified')
    list_filter = ('year', 'quarter', 'is_verified')
    search_fields = ('franchise__name',)
    date_hierarchy = 'verified_date'
    raw_id_fields = ('franchise', 'verified_by')
    fieldsets = (
        (None, {
            'fields': ('franchise', 'year', 'quarter')
        }),
        (_('Revenue Metrics'), {
            'fields': ('total_revenue', 'royalty_amount', 'marketing_fee_amount')
        }),
        (_('Performance Metrics'), {
            'fields': ('work_order_count', 'customer_satisfaction')
        }),
        (_('Verification'), {
            'fields': ('is_verified', 'verified_by', 'verified_date')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'detailed_breakdown')
        }),
    )


@admin.register(FranchiseRequirement)
class FranchiseRequirementAdmin(admin.ModelAdmin):
    list_display = ('name', 'template', 'requirement_type', 'is_mandatory', 'validation_method')
    list_filter = ('requirement_type', 'is_mandatory', 'validation_method', 'validation_frequency')
    search_fields = ('name', 'description', 'template__name')
    raw_id_fields = ('template',)
    fieldsets = (
        (None, {
            'fields': ('template', 'name', 'description')
        }),
        (_('Requirement Details'), {
            'fields': ('requirement_type', 'is_mandatory')
        }),
        (_('Validation'), {
            'fields': ('validation_method', 'validation_frequency')
        }),
        (_('Specifications'), {
            'fields': ('detailed_specifications',)
        }),
    )


@admin.register(FranchiseCompliance)
class FranchiseComplianceAdmin(admin.ModelAdmin):
    list_display = ('franchise', 'requirement', 'status', 'verification_date')
    list_filter = ('status', 'verification_method')
    search_fields = ('franchise__name', 'requirement__name', 'notes')
    date_hierarchy = 'verification_date'
    raw_id_fields = ('franchise', 'requirement', 'verified_by')
    fieldsets = (
        (None, {
            'fields': ('franchise', 'requirement', 'status')
        }),
        (_('Verification'), {
            'fields': ('verification_date', 'verified_by', 'verification_method')
        }),
        (_('Documentation'), {
            'fields': ('documentation', 'notes')
        }),
        (_('Resolution (For Non-Compliance)'), {
            'fields': ('resolution_plan', 'resolution_deadline'),
            'classes': ('collapse',)
        }),
    )
