# Generated by Django 4.2.20 on 2025-05-07 11:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('setup', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FranchiseAgreement',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Agreement Name')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('signed_date', models.DateField(blank=True, null=True, verbose_name='Signed Date')),
                ('territory_definition', models.TextField(blank=True, verbose_name='Territory Definition')),
                ('territory_exclusivity', models.BooleanField(default=True, verbose_name='Territory Exclusivity')),
                ('term_years', models.PositiveSmallIntegerField(default=5, verbose_name='Term (Years)')),
                ('renewal_options', models.PositiveSmallIntegerField(default=1, verbose_name='Renewal Options')),
                ('renewal_terms', models.TextField(blank=True, verbose_name='Renewal Terms')),
                ('termination_conditions', models.TextField(blank=True, verbose_name='Termination Conditions')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('active', 'Active'), ('renewal', 'Pending Renewal'), ('terminated', 'Terminated'), ('expired', 'Expired')], default='draft', max_length=50, verbose_name='Status')),
                ('agreement_document', models.FileField(blank=True, null=True, upload_to='franchise_agreements', verbose_name='Agreement Document')),
                ('additional_documents', models.JSONField(blank=True, default=list, verbose_name='Additional Documents')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('franchise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='agreements', to='setup.franchise', verbose_name='Franchise')),
            ],
            options={
                'verbose_name': 'Franchise Agreement',
                'verbose_name_plural': 'Franchise Agreements',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='FranchiseTemplate',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Template Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('default_term_years', models.PositiveSmallIntegerField(default=5, verbose_name='Default Term (Years)')),
                ('default_renewal_options', models.PositiveSmallIntegerField(default=1, verbose_name='Default Renewal Options')),
                ('default_territory_definition', models.TextField(blank=True, verbose_name='Default Territory Definition')),
                ('default_initial_fee', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='Default Initial Fee')),
                ('default_royalty_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Default Royalty Percentage')),
                ('default_marketing_fee_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Default Marketing Fee Percentage')),
                ('default_minimum_performance', models.JSONField(blank=True, default=dict, verbose_name='Default Minimum Performance')),
                ('default_training_requirements', models.JSONField(blank=True, default=list, verbose_name='Default Training Requirements')),
                ('default_operational_standards', models.JSONField(blank=True, default=list, verbose_name='Default Operational Standards')),
                ('default_technology_requirements', models.JSONField(blank=True, default=list, verbose_name='Default Technology Requirements')),
                ('agreement_template', models.FileField(blank=True, null=True, upload_to='franchise_templates', verbose_name='Agreement Template')),
                ('operations_manual_template', models.FileField(blank=True, null=True, upload_to='franchise_templates', verbose_name='Operations Manual Template')),
            ],
            options={
                'verbose_name': 'Franchise Template',
                'verbose_name_plural': 'Franchise Templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='FranchiseRequirement',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Requirement Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('requirement_type', models.CharField(choices=[('operational', 'Operational Standard'), ('training', 'Training Requirement'), ('reporting', 'Reporting Requirement'), ('facility', 'Facility Requirement'), ('staffing', 'Staffing Requirement'), ('technology', 'Technology Requirement'), ('marketing', 'Marketing Requirement'), ('customer_service', 'Customer Service Standard'), ('other', 'Other')], max_length=50, verbose_name='Requirement Type')),
                ('is_mandatory', models.BooleanField(default=True, verbose_name='Is Mandatory')),
                ('validation_method', models.CharField(choices=[('self_report', 'Self Reporting'), ('inspection', 'Physical Inspection'), ('documentation', 'Documentation Review'), ('audit', 'Audit'), ('customer_feedback', 'Customer Feedback'), ('other', 'Other')], default='inspection', max_length=50, verbose_name='Validation Method')),
                ('validation_frequency', models.CharField(choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('semi_annually', 'Semi-Annually'), ('annually', 'Annually'), ('initial_only', 'Initial Setup Only')], default='annually', max_length=50, verbose_name='Validation Frequency')),
                ('detailed_specifications', models.JSONField(blank=True, default=dict, verbose_name='Detailed Specifications')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requirements', to='franchise_setup.franchisetemplate', verbose_name='Franchise Template')),
            ],
            options={
                'verbose_name': 'Franchise Requirement',
                'verbose_name_plural': 'Franchise Requirements',
                'ordering': ['requirement_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FranchiseFee',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('initial_fee', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='Initial Fee')),
                ('royalty_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Royalty Percentage')),
                ('marketing_fee_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Marketing Fee Percentage')),
                ('technology_fee', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Technology Fee')),
                ('technology_fee_frequency', models.CharField(choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('annually', 'Annually'), ('one_time', 'One Time')], default='monthly', max_length=20, verbose_name='Technology Fee Frequency')),
                ('payment_terms', models.TextField(blank=True, verbose_name='Payment Terms')),
                ('late_payment_penalty', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Late Payment Penalty')),
                ('additional_fees', models.JSONField(blank=True, default=list, verbose_name='Additional Fees')),
                ('agreement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fees', to='franchise_setup.franchiseagreement', verbose_name='Franchise Agreement')),
            ],
            options={
                'verbose_name': 'Franchise Fee',
                'verbose_name_plural': 'Franchise Fees',
            },
        ),
        migrations.AddField(
            model_name='franchiseagreement',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='agreements', to='franchise_setup.franchisetemplate', verbose_name='Agreement Template'),
        ),
        migrations.CreateModel(
            name='RevenueShare',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveSmallIntegerField(verbose_name='Year')),
                ('quarter', models.PositiveSmallIntegerField(choices=[(1, 'Q1'), (2, 'Q2'), (3, 'Q3'), (4, 'Q4')], verbose_name='Quarter')),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Total Revenue')),
                ('royalty_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='Royalty Amount')),
                ('marketing_fee_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='Marketing Fee Amount')),
                ('work_order_count', models.PositiveIntegerField(default=0, verbose_name='Work Order Count')),
                ('customer_satisfaction', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Customer Satisfaction')),
                ('is_verified', models.BooleanField(default=False, verbose_name='Is Verified')),
                ('verified_date', models.DateTimeField(blank=True, null=True, verbose_name='Verified Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('detailed_breakdown', models.JSONField(blank=True, default=dict, verbose_name='Detailed Breakdown')),
                ('franchise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revenue_shares', to='setup.franchise', verbose_name='Franchise')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_revenue_shares', to=settings.AUTH_USER_MODEL, verbose_name='Verified By')),
            ],
            options={
                'verbose_name': 'Revenue Share',
                'verbose_name_plural': 'Revenue Shares',
                'ordering': ['-year', '-quarter'],
                'unique_together': {('franchise', 'year', 'quarter')},
            },
        ),
        migrations.CreateModel(
            name='FranchiseCompliance',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('compliant', 'Compliant'), ('non_compliant', 'Non-Compliant'), ('pending', 'Pending Verification'), ('exempt', 'Exempt'), ('in_progress', 'In Progress')], default='pending', max_length=50, verbose_name='Status')),
                ('verification_date', models.DateField(blank=True, null=True, verbose_name='Verification Date')),
                ('verification_method', models.CharField(blank=True, choices=[('self_report', 'Self Reporting'), ('inspection', 'Physical Inspection'), ('documentation', 'Documentation Review'), ('audit', 'Audit'), ('customer_feedback', 'Customer Feedback'), ('other', 'Other')], max_length=50, null=True, verbose_name='Verification Method')),
                ('documentation', models.JSONField(blank=True, default=list, verbose_name='Documentation')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('resolution_plan', models.TextField(blank=True, verbose_name='Resolution Plan')),
                ('resolution_deadline', models.DateField(blank=True, null=True, verbose_name='Resolution Deadline')),
                ('franchise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_records', to='setup.franchise', verbose_name='Franchise')),
                ('requirement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_records', to='franchise_setup.franchiserequirement', verbose_name='Requirement')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_compliances', to=settings.AUTH_USER_MODEL, verbose_name='Verified By')),
            ],
            options={
                'verbose_name': 'Franchise Compliance',
                'verbose_name_plural': 'Franchise Compliance Records',
                'ordering': ['-verification_date'],
                'unique_together': {('franchise', 'requirement', 'verification_date')},
            },
        ),
    ]
