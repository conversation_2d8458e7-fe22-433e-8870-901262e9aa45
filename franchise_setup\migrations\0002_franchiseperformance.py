# Generated by Django 4.2.20 on 2025-07-06 10:41

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0018_add_nationality_field"),
        ("franchise_setup", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="FranchisePerformance",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("year", models.PositiveIntegerField(verbose_name="Year")),
                (
                    "quarter",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Q1"), (2, "Q2"), (3, "Q3"), (4, "Q4")],
                        verbose_name="Quarter",
                    ),
                ),
                (
                    "total_revenue",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Total Revenue",
                    ),
                ),
                (
                    "total_profit",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Total Profit",
                    ),
                ),
                (
                    "royalty_paid",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=12,
                        verbose_name="Royalty Paid",
                    ),
                ),
                (
                    "marketing_fee_paid",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=12,
                        verbose_name="Marketing Fee Paid",
                    ),
                ),
                (
                    "total_work_orders",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Total Work Orders"
                    ),
                ),
                (
                    "completed_work_orders",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Completed Work Orders"
                    ),
                ),
                (
                    "customer_satisfaction_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        help_text="Score out of 10",
                        max_digits=3,
                        null=True,
                        verbose_name="Customer Satisfaction Score",
                    ),
                ),
                (
                    "average_order_value",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="Average Order Value",
                    ),
                ),
                (
                    "total_staff",
                    models.PositiveIntegerField(default=0, verbose_name="Total Staff"),
                ),
                (
                    "staff_turnover_rate",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Percentage",
                        max_digits=5,
                        verbose_name="Staff Turnover Rate",
                    ),
                ),
                (
                    "compliance_score",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Percentage",
                        max_digits=5,
                        verbose_name="Compliance Score",
                    ),
                ),
                (
                    "quality_audit_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        help_text="Score out of 10",
                        max_digits=3,
                        null=True,
                        verbose_name="Quality Audit Score",
                    ),
                ),
                (
                    "revenue_growth_rate",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Percentage compared to previous period",
                        max_digits=5,
                        verbose_name="Revenue Growth Rate",
                    ),
                ),
                (
                    "customer_growth_rate",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Percentage compared to previous period",
                        max_digits=5,
                        verbose_name="Customer Growth Rate",
                    ),
                ),
                (
                    "performance_metrics",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        verbose_name="Additional Performance Metrics",
                    ),
                ),
                (
                    "overall_rating",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("excellent", "Excellent"),
                            ("good", "Good"),
                            ("satisfactory", "Satisfactory"),
                            ("needs_improvement", "Needs Improvement"),
                            ("poor", "Poor"),
                        ],
                        max_length=20,
                        null=True,
                        verbose_name="Overall Performance Rating",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, verbose_name="Performance Notes"),
                ),
                (
                    "profit_margin",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Calculated as (profit/revenue) * 100",
                        max_digits=5,
                        verbose_name="Profit Margin",
                    ),
                ),
                (
                    "completion_rate",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Calculated as (completed/total) * 100",
                        max_digits=5,
                        verbose_name="Work Order Completion Rate",
                    ),
                ),
                (
                    "franchise",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_records",
                        to="setup.franchise",
                        verbose_name="Franchise",
                    ),
                ),
            ],
            options={
                "verbose_name": "Franchise Performance",
                "verbose_name_plural": "Franchise Performance Records",
                "ordering": ["-year", "-quarter"],
                "unique_together": {("franchise", "year", "quarter")},
            },
        ),
    ]
