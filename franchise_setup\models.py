from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet
from setup.models import Franchise, Company, ServiceCenter
from django.contrib.auth.models import User


class FranchiseTemplate(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Reusable template for franchise agreements and configurations
    """
    name = models.CharField(_("Template Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    # Agreement Defaults
    default_term_years = models.PositiveSmallIntegerField(_("Default Term (Years)"), default=5)
    default_renewal_options = models.PositiveSmallIntegerField(_("Default Renewal Options"), default=1)
    default_territory_definition = models.TextField(_("Default Territory Definition"), blank=True)
    
    # Financial Defaults
    default_initial_fee = models.DecimalField(_("Default Initial Fee"), max_digits=12, decimal_places=2, default=0)
    default_royalty_percentage = models.DecimalField(_("Default Royalty Percentage"), max_digits=5, decimal_places=2, default=0)
    default_marketing_fee_percentage = models.DecimalField(_("Default Marketing Fee Percentage"), max_digits=5, decimal_places=2, default=0)
    default_minimum_performance = models.JSONField(_("Default Minimum Performance"), default=dict, blank=True)
    
    # Operational Defaults
    default_training_requirements = models.JSONField(_("Default Training Requirements"), default=list, blank=True)
    default_operational_standards = models.JSONField(_("Default Operational Standards"), default=list, blank=True)
    default_technology_requirements = models.JSONField(_("Default Technology Requirements"), default=list, blank=True)
    
    # Documentation Defaults
    agreement_template = models.FileField(_("Agreement Template"), upload_to='franchise_templates', blank=True, null=True)
    operations_manual_template = models.FileField(_("Operations Manual Template"), upload_to='franchise_templates', blank=True, null=True)
    
    class Meta:
        verbose_name = _("Franchise Template")
        verbose_name_plural = _("Franchise Templates")
        ordering = ['name']
        
    def __str__(self):
        return self.name


class FranchiseAgreement(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Legal and operational agreement between franchisor and franchisee
    """
    franchise = models.ForeignKey(
        Franchise,
        on_delete=models.CASCADE,
        related_name="agreements",
        verbose_name=_("Franchise")
    )
    template = models.ForeignKey(
        FranchiseTemplate,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="agreements",
        verbose_name=_("Agreement Template")
    )
    name = models.CharField(_("Agreement Name"), max_length=255)
    
    # Agreement Dates
    start_date = models.DateField(_("Start Date"))
    end_date = models.DateField(_("End Date"))
    signed_date = models.DateField(_("Signed Date"), null=True, blank=True)
    
    # Territory Information
    territory_definition = models.TextField(_("Territory Definition"), blank=True)
    territory_exclusivity = models.BooleanField(_("Territory Exclusivity"), default=True)
    
    # Agreement Details
    term_years = models.PositiveSmallIntegerField(_("Term (Years)"), default=5)
    renewal_options = models.PositiveSmallIntegerField(_("Renewal Options"), default=1)
    renewal_terms = models.TextField(_("Renewal Terms"), blank=True)
    termination_conditions = models.TextField(_("Termination Conditions"), blank=True)
    
    # Agreement Status
    is_active = models.BooleanField(_("Is Active"), default=True)
    status = models.CharField(_("Status"), max_length=50, choices=[
        ('draft', _('Draft')),
        ('pending', _('Pending Approval')),
        ('active', _('Active')),
        ('renewal', _('Pending Renewal')),
        ('terminated', _('Terminated')),
        ('expired', _('Expired')),
    ], default='draft')
    
    # Documentation
    agreement_document = models.FileField(_("Agreement Document"), upload_to='franchise_agreements', blank=True, null=True)
    additional_documents = models.JSONField(_("Additional Documents"), default=list, blank=True)
    
    # Other Information
    notes = models.TextField(_("Notes"), blank=True)
    
    class Meta:
        verbose_name = _("Franchise Agreement")
        verbose_name_plural = _("Franchise Agreements")
        ordering = ['-start_date']
        
    def __str__(self):
        return f"{self.franchise.name} - {self.name}"


class FranchiseFee(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Fee structure for a franchise
    """
    agreement = models.ForeignKey(
        FranchiseAgreement,
        on_delete=models.CASCADE,
        related_name="fees",
        verbose_name=_("Franchise Agreement")
    )
    
    # Fee Types
    initial_fee = models.DecimalField(_("Initial Fee"), max_digits=12, decimal_places=2, default=0)
    royalty_percentage = models.DecimalField(_("Royalty Percentage"), max_digits=5, decimal_places=2, default=0)
    marketing_fee_percentage = models.DecimalField(_("Marketing Fee Percentage"), max_digits=5, decimal_places=2, default=0)
    technology_fee = models.DecimalField(_("Technology Fee"), max_digits=10, decimal_places=2, default=0)
    technology_fee_frequency = models.CharField(_("Technology Fee Frequency"), max_length=20, choices=[
        ('monthly', _('Monthly')),
        ('quarterly', _('Quarterly')),
        ('annually', _('Annually')),
        ('one_time', _('One Time')),
    ], default='monthly')
    
    # Payment Terms
    payment_terms = models.TextField(_("Payment Terms"), blank=True)
    late_payment_penalty = models.DecimalField(_("Late Payment Penalty"), max_digits=5, decimal_places=2, default=0)
    
    # Other Fees
    additional_fees = models.JSONField(_("Additional Fees"), default=list, blank=True)
    
    class Meta:
        verbose_name = _("Franchise Fee")
        verbose_name_plural = _("Franchise Fees")
        
    def __str__(self):
        return f"{self.agreement.franchise.name} - Fees"


class RevenueShare(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Revenue sharing and performance metrics for franchises
    """
    franchise = models.ForeignKey(
        Franchise,
        on_delete=models.CASCADE,
        related_name="revenue_shares",
        verbose_name=_("Franchise")
    )
    year = models.PositiveSmallIntegerField(_("Year"))
    quarter = models.PositiveSmallIntegerField(_("Quarter"), choices=[
        (1, _('Q1')),
        (2, _('Q2')),
        (3, _('Q3')),
        (4, _('Q4')),
    ])
    
    # Revenue Metrics
    total_revenue = models.DecimalField(_("Total Revenue"), max_digits=15, decimal_places=2, default=0)
    royalty_amount = models.DecimalField(_("Royalty Amount"), max_digits=12, decimal_places=2, default=0)
    marketing_fee_amount = models.DecimalField(_("Marketing Fee Amount"), max_digits=12, decimal_places=2, default=0)
    
    # Performance Metrics
    work_order_count = models.PositiveIntegerField(_("Work Order Count"), default=0)
    customer_satisfaction = models.DecimalField(_("Customer Satisfaction"), max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Status and Verification
    is_verified = models.BooleanField(_("Is Verified"), default=False)
    verified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="verified_revenue_shares",
        verbose_name=_("Verified By")
    )
    verified_date = models.DateTimeField(_("Verified Date"), null=True, blank=True)
    
    # Notes and Additional Data
    notes = models.TextField(_("Notes"), blank=True)
    detailed_breakdown = models.JSONField(_("Detailed Breakdown"), default=dict, blank=True)
    
    class Meta:
        verbose_name = _("Revenue Share")
        verbose_name_plural = _("Revenue Shares")
        unique_together = [['franchise', 'year', 'quarter']]
        ordering = ['-year', '-quarter']
        
    def __str__(self):
        return f"{self.franchise.name} - {self.year} Q{self.quarter}"


class FranchiseRequirement(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Operational requirements and standards for franchises
    """
    template = models.ForeignKey(
        FranchiseTemplate,
        on_delete=models.CASCADE,
        related_name="requirements",
        verbose_name=_("Franchise Template")
    )
    name = models.CharField(_("Requirement Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    
    # Requirement Type
    requirement_type = models.CharField(_("Requirement Type"), max_length=50, choices=[
        ('operational', _('Operational Standard')),
        ('training', _('Training Requirement')),
        ('reporting', _('Reporting Requirement')),
        ('facility', _('Facility Requirement')),
        ('staffing', _('Staffing Requirement')),
        ('technology', _('Technology Requirement')),
        ('marketing', _('Marketing Requirement')),
        ('customer_service', _('Customer Service Standard')),
        ('other', _('Other')),
    ])
    
    # Validation
    is_mandatory = models.BooleanField(_("Is Mandatory"), default=True)
    validation_method = models.CharField(_("Validation Method"), max_length=50, choices=[
        ('self_report', _('Self Reporting')),
        ('inspection', _('Physical Inspection')),
        ('documentation', _('Documentation Review')),
        ('audit', _('Audit')),
        ('customer_feedback', _('Customer Feedback')),
        ('other', _('Other')),
    ], default='inspection')
    validation_frequency = models.CharField(_("Validation Frequency"), max_length=50, choices=[
        ('monthly', _('Monthly')),
        ('quarterly', _('Quarterly')),
        ('semi_annually', _('Semi-Annually')),
        ('annually', _('Annually')),
        ('initial_only', _('Initial Setup Only')),
    ], default='annually')
    
    # Other Information
    detailed_specifications = models.JSONField(_("Detailed Specifications"), default=dict, blank=True)
    
    class Meta:
        verbose_name = _("Franchise Requirement")
        verbose_name_plural = _("Franchise Requirements")
        ordering = ['requirement_type', 'name']
        
    def __str__(self):
        return f"{self.get_requirement_type_display()}: {self.name}"


class FranchiseCompliance(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Compliance records for franchise requirements
    """
    franchise = models.ForeignKey(
        Franchise,
        on_delete=models.CASCADE,
        related_name="compliance_records",
        verbose_name=_("Franchise")
    )
    requirement = models.ForeignKey(
        FranchiseRequirement,
        on_delete=models.CASCADE,
        related_name="compliance_records",
        verbose_name=_("Requirement")
    )
    
    # Compliance Status
    status = models.CharField(_("Status"), max_length=50, choices=[
        ('compliant', _('Compliant')),
        ('non_compliant', _('Non-Compliant')),
        ('pending', _('Pending Verification')),
        ('exempt', _('Exempt')),
        ('in_progress', _('In Progress')),
    ], default='pending')
    
    # Verification Details
    verification_date = models.DateField(_("Verification Date"), null=True, blank=True)
    verified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="verified_compliances",
        verbose_name=_("Verified By")
    )
    verification_method = models.CharField(_("Verification Method"), max_length=50, choices=[
        ('self_report', _('Self Reporting')),
        ('inspection', _('Physical Inspection')),
        ('documentation', _('Documentation Review')),
        ('audit', _('Audit')),
        ('customer_feedback', _('Customer Feedback')),
        ('other', _('Other')),
    ], null=True, blank=True)
    
    # Documentation and Notes
    documentation = models.JSONField(_("Documentation"), default=list, blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    # For Non-Compliance
    resolution_plan = models.TextField(_("Resolution Plan"), blank=True)
    resolution_deadline = models.DateField(_("Resolution Deadline"), null=True, blank=True)
    
    class Meta:
        verbose_name = _("Franchise Compliance")
        verbose_name_plural = _("Franchise Compliance Records")
        unique_together = [['franchise', 'requirement', 'verification_date']]
        ordering = ['-verification_date']
        
    def __str__(self):
        return f"{self.franchise.name} - {self.requirement.name} - {self.get_status_display()}"
