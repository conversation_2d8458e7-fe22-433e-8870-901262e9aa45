{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "Fee Structure Management" %}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 {% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <a href="{% url 'setup:dashboard' %}" 
                       class="{% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %} text-gray-500 hover:text-gray-700">
                        <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-lg"></i>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{% trans "Fee Structure Management" %}</h1>
                        <p class="text-sm text-gray-600">{% trans "إدارة هيكل الرسوم والمدفوعات" %}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                    <a href="#" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "إضافة رسوم جديدة" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="bg-white rounded-lg shadow p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "نوع الرسوم" %}</label>
                    <select name="fee_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">{% trans "جميع الأنواع" %}</option>
                        {% for key, value in fee_types.items %}
                        <option value="{{ key }}" {% if request.GET.fee_type == key %}selected{% endif %}>{{ value }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "تكرار الدفع" %}</label>
                    <select name="payment_frequency" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">{% trans "جميع التكرارات" %}</option>
                        {% for key, value in payment_frequencies.items %}
                        <option value="{{ key }}" {% if request.GET.payment_frequency == key %}selected{% endif %}>{{ value }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                    <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-search {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "بحث" %}
                    </button>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                    <a href="{% url 'franchise_setup:fee_structure_list' %}" class="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 inline-flex items-center justify-center">
                        <i class="fas fa-times {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "إعادة تعيين" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Fee Structure Table -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    {% trans "قائمة هياكل الرسوم" %}
                    {% if page_obj %}
                    <span class="text-sm text-gray-500">({{ page_obj.paginator.count }} {% trans "عنصر" %})</span>
                    {% endif %}
                </h3>
            </div>

            {% if page_obj.object_list %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "نوع الرسوم" %}
                            </th>
                            <th class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المبلغ" %}
                            </th>
                            <th class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "تكرار الدفع" %}
                            </th>
                            <th class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الاتفاقية" %}
                            </th>
                            <th class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for fee in page_obj %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div class="flex items-center">
                                    <div class="bg-blue-100 rounded-lg p-2 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                                        <i class="fas fa-money-bill-wave text-blue-600"></i>
                                    </div>
                                    {{ fee.get_fee_type_display }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="font-medium">{{ fee.amount|floatformat:2 }}</span>
                                <span class="text-gray-500">{{ fee.currency }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ fee.get_payment_frequency_display }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% if fee.agreement %}
                                <a href="{% url 'franchise_setup:agreement_detail' fee.agreement.pk %}" class="text-blue-600 hover:text-blue-900">
                                    {{ fee.agreement.name }}
                                </a>
                                {% else %}
                                <span class="text-gray-500">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                    <a href="{% url 'franchise_setup:fee_structure_detail' fee.pk %}" 
                                       class="text-blue-600 hover:text-blue-900" title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="text-green-600 hover:text-green-900" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="#" class="text-red-600 hover:text-red-900" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "السابق" %}
                    </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "التالي" %}
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {% trans "عرض" %}
                            <span class="font-medium">{{ page_obj.start_index }}</span>
                            {% trans "إلى" %}
                            <span class="font-medium">{{ page_obj.end_index }}</span>
                            {% trans "من" %}
                            <span class="font-medium">{{ page_obj.paginator.count }}</span>
                            {% trans "نتيجة" %}
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-{% if LANGUAGE_CODE == 'ar' %}r{% else %}l{% endif %}-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %}"></i>
                            </a>
                            {% endif %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                {{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}
                            </span>
                            {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-{% if LANGUAGE_CODE == 'ar' %}l{% else %}r{% endif %}-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}"></i>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}

            {% else %}
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-money-bill-wave text-3xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد هياكل رسوم" %}</h3>
                <p class="text-gray-500 mb-6">{% trans "لم يتم العثور على أي هياكل رسوم مطابقة للمعايير المحددة." %}</p>
                <a href="#" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                    {% trans "إضافة هيكل رسوم جديد" %}
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 