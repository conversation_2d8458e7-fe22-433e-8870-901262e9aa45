{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 {% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <a href="{% url 'setup:dashboard' %}" 
                       class="{% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %} text-gray-500 hover:text-gray-700">
                        <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-lg"></i>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ page_title }}</h1>
                        <p class="text-sm text-gray-600">{% trans "إعداد الامتياز" %}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {% trans "قيد التطوير" %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Section Header -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-8 text-center">
                <div class="max-w-3xl mx-auto">
                    <div class="bg-blue-400 bg-opacity-30 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-cogs text-3xl text-white"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-white mb-2">{{ section_title }}</h2>
                    <p class="text-blue-100 text-lg">{{ description }}</p>
                </div>
            </div>

            <!-- Content Area -->
            <div class="px-6 py-8">
                <div class="max-w-4xl mx-auto">
                    <!-- Under Development Notice -->
                    <div class="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-8">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-amber-500 text-2xl"></i>
                            </div>
                            <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %}">
                                <h3 class="text-lg font-medium text-amber-800">
                                    {% trans "هذا القسم قيد التطوير" %}
                                </h3>
                                <p class="text-amber-700 mt-1">
                                    {% trans "نحن نعمل بجد لإضافة هذه الميزات الجديدة. سيتم إطلاقها قريباً مع تحديثات شاملة ومحسنة." %}
                                </p>
                            </div>
                        </div>
                    </div>

                    {% if features %}
                    <!-- Features Preview -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-4">
                                {% trans "الميزات المخططة" %}
                            </h3>
                            <ul class="space-y-3">
                                {% for feature in features %}
                                <li class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></i>
                                    <span class="text-gray-700">{{ feature }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-4">
                                {% trans "الفوائد المتوقعة" %}
                            </h3>
                            <ul class="space-y-3">
                                <li class="flex items-center">
                                    <i class="fas fa-chart-line text-blue-500 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></i>
                                    <span class="text-gray-700">{% trans "تحسين الكفاءة التشغيلية" %}</span>
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-shield-alt text-green-500 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></i>
                                    <span class="text-gray-700">{% trans "ضمان الامتثال والجودة" %}</span>
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-users text-purple-500 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></i>
                                    <span class="text-gray-700">{% trans "تحسين تجربة المستخدم" %}</span>
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-analytics text-orange-500 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></i>
                                    <span class="text-gray-700">{% trans "تقارير وتحليلات متقدمة" %}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Timeline -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">
                            {% trans "الجدول الزمني للتطوير" %}
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-pencil-ruler text-white text-sm"></i>
                                </div>
                                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                                    <p class="text-sm font-medium text-gray-900">{% trans "مرحلة التصميم والتخطيط" %}</p>
                                    <p class="text-sm text-gray-600">{% trans "تحديد المتطلبات وتصميم واجهات المستخدم" %}</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-code text-white text-sm"></i>
                                </div>
                                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                                    <p class="text-sm font-medium text-gray-900">{% trans "مرحلة التطوير" %}</p>
                                    <p class="text-sm text-gray-600">{% trans "برمجة الميزات وتطوير الوظائف" %}</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-rocket text-white text-sm"></i>
                                </div>
                                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                                    <p class="text-sm font-medium text-gray-900">{% trans "الإطلاق التجريبي" %}</p>
                                    <p class="text-sm text-gray-600">{% trans "اختبار الميزات مع مجموعة محدودة من المستخدمين" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Section -->
                    <div class="mt-8 text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            {% trans "هل لديك اقتراحات؟" %}
                        </h3>
                        <p class="text-gray-600 mb-4">
                            {% trans "نحن نقدر ملاحظاتكم ومقترحاتكم لتحسين هذه الميزات" %}
                        </p>
                        <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-comments {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                            {% trans "إرسال اقتراح" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 