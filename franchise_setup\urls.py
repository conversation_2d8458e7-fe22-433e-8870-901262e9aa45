from django.urls import path
from . import views

app_name = 'franchise_setup'

urlpatterns = [
    # Dashboard
    path('', views.franchise_dashboard, name='dashboard'),
    
    # Agreement management
    path('agreements/', views.agreement_list, name='agreement_list'),
    path('agreements/<uuid:pk>/', views.agreement_detail, name='agreement_detail'),
    
    # Template management
    path('templates/', views.template_list, name='template_list'),
    path('templates/<uuid:pk>/', views.template_detail, name='template_detail'),
    
    # Requirements management  
    path('requirements/', views.requirements_list, name='requirements_list'),
    path('requirements/<uuid:pk>/', views.requirements_detail, name='requirements_detail'),
    
    # Compliance management
    path('compliance/', views.compliance_list, name='compliance_list'),
    path('compliance/<uuid:pk>/update-status/', views.update_compliance_status, name='update_compliance_status'),
    
    # Quality certificates
    path('quality-certificates/', views.quality_certificates_list, name='quality_certificates_list'),
    path('quality-certificates/<uuid:pk>/', views.quality_certificates_detail, name='quality_certificates_detail'),
    
    # Performance indicators  
    path('performance-indicators/', views.performance_indicators_list, name='performance_indicators_list'),
    
    # Revenue share management
    path('revenue/', views.revenue_share_list, name='revenue_share_list'),
    
    # Fee structure
    path('fee-structure/', views.fee_structure_list, name='fee_structure_list'),
    path('fee-structure/<uuid:pk>/', views.fee_structure_detail, name='fee_structure_detail'),
    
    # Financial reports
    path('financial-reports/', views.financial_reports_list, name='financial_reports_list'),
    path('financial-reports/quarterly/', views.quarterly_financial_report, name='quarterly_financial_report'),
    path('financial-reports/annual/', views.annual_financial_report, name='annual_financial_report'),
] 