from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Sum, Avg, Count
from django.views.decorators.http import require_POST
from .models import (
    FranchiseTemplate, 
    FranchiseAgreement, 
    FranchiseFee, 
    RevenueShare,
    FranchiseRequirement,
    FranchiseCompliance
)
from setup.models import Franchise
from user_roles.middleware import role_required


@login_required
@role_required('can_access_setup')
def franchise_dashboard(request):
    """
    Dashboard view showing franchise metrics and compliance summary
    """
    # Get counts
    agreement_count = FranchiseAgreement.objects.count()
    active_agreement_count = FranchiseAgreement.objects.filter(status='active').count()
    franchise_count = Franchise.objects.count()
    
    # Get compliance stats
    compliance_stats = {
        'compliant': FranchiseCompliance.objects.filter(status='compliant').count(),
        'non_compliant': FranchiseCompliance.objects.filter(status='non_compliant').count(),
        'pending': FranchiseCompliance.objects.filter(status='pending').count(),
        'total': FranchiseCompliance.objects.count()
    }
    
    # Get revenue data for chart
    revenue_data = RevenueShare.objects.values('year', 'quarter').annotate(
        total=Sum('total_revenue'),
        royalty=Sum('royalty_amount')
    ).order_by('year', 'quarter')[:8]
    
    # Get upcoming renewals
    upcoming_renewals = FranchiseAgreement.objects.filter(
        status__in=['active', 'renewal']
    ).order_by('end_date')[:5]
    
    context = {
        'agreement_count': agreement_count,
        'active_agreement_count': active_agreement_count,
        'franchise_count': franchise_count,
        'compliance_stats': compliance_stats,
        'revenue_data': list(revenue_data),
        'upcoming_renewals': upcoming_renewals,
        'page_title': _("Franchise Management Dashboard")
    }
    
    return render(request, 'franchise_setup/dashboard.html', context)


@login_required
@role_required('can_access_setup')
def agreement_list(request):
    """
    List view for franchise agreements
    """
    agreements = FranchiseAgreement.objects.all().select_related('franchise')
    
    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        agreements = agreements.filter(status=status_filter)
    
    # Filter by franchise if provided
    franchise_filter = request.GET.get('franchise')
    if franchise_filter:
        agreements = agreements.filter(franchise_id=franchise_filter)
    
    # Pagination
    paginator = Paginator(agreements.order_by('-start_date'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'statuses': FranchiseAgreement._meta.get_field('status').choices,
        'franchises': Franchise.objects.all(),
        'page_title': _("Franchise Agreements")
    }
    
    return render(request, 'franchise_setup/agreement_list.html', context)


@login_required
@role_required('can_access_setup')
def agreement_detail(request, pk):
    """
    Detail view for a franchise agreement
    """
    agreement = get_object_or_404(FranchiseAgreement.objects.select_related(
        'franchise', 'template'
    ), pk=pk)
    
    # Get fees associated with this agreement
    fees = agreement.fees.all()
    
    # Get revenue shares for this franchise
    revenue_shares = RevenueShare.objects.filter(
        franchise=agreement.franchise
    ).order_by('-year', '-quarter')[:8]
    
    # Get compliance records for this franchise
    compliance_records = FranchiseCompliance.objects.filter(
        franchise=agreement.franchise
    ).select_related('requirement').order_by('-verification_date')[:10]
    
    context = {
        'agreement': agreement,
        'fees': fees,
        'revenue_shares': revenue_shares,
        'compliance_records': compliance_records,
        'page_title': _("Agreement: ") + agreement.name
    }
    
    return render(request, 'franchise_setup/agreement_detail.html', context)


@login_required
@role_required('can_access_setup')
def compliance_list(request):
    """
    List view for franchise compliance records
    """
    compliance_records = FranchiseCompliance.objects.all().select_related(
        'franchise', 'requirement'
    )
    
    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        compliance_records = compliance_records.filter(status=status_filter)
    
    # Filter by franchise if provided
    franchise_filter = request.GET.get('franchise')
    if franchise_filter:
        compliance_records = compliance_records.filter(franchise_id=franchise_filter)
    
    # Filter by requirement type if provided
    requirement_type = request.GET.get('requirement_type')
    if requirement_type:
        compliance_records = compliance_records.filter(requirement__requirement_type=requirement_type)
    
    # Pagination
    paginator = Paginator(compliance_records.order_by('-verification_date'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'statuses': dict(FranchiseCompliance._meta.get_field('status').choices),
        'requirement_types': dict(FranchiseRequirement._meta.get_field('requirement_type').choices),
        'franchises': Franchise.objects.all(),
        'page_title': _("Franchise Compliance")
    }
    
    return render(request, 'franchise_setup/compliance_list.html', context)


@login_required
@role_required('can_access_setup')
def revenue_share_list(request):
    """
    List view for revenue shares
    """
    revenue_shares = RevenueShare.objects.all().select_related('franchise')
    
    # Filter by year if provided
    year_filter = request.GET.get('year')
    if year_filter:
        revenue_shares = revenue_shares.filter(year=year_filter)
    
    # Filter by quarter if provided
    quarter_filter = request.GET.get('quarter')
    if quarter_filter:
        revenue_shares = revenue_shares.filter(quarter=quarter_filter)
    
    # Filter by franchise if provided
    franchise_filter = request.GET.get('franchise')
    if franchise_filter:
        revenue_shares = revenue_shares.filter(franchise_id=franchise_filter)
    
    # Filter by verification status
    verification_filter = request.GET.get('is_verified')
    if verification_filter:
        is_verified = verification_filter == 'true'
        revenue_shares = revenue_shares.filter(is_verified=is_verified)
    
    # Pagination
    paginator = Paginator(revenue_shares.order_by('-year', '-quarter'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get available years for filter
    years = RevenueShare.objects.values_list('year', flat=True).distinct().order_by('-year')
    
    context = {
        'page_obj': page_obj,
        'years': years,
        'quarters': dict(RevenueShare._meta.get_field('quarter').choices),
        'franchises': Franchise.objects.all(),
        'page_title': _("Revenue Shares")
    }
    
    return render(request, 'franchise_setup/revenue_share_list.html', context)


@login_required
@role_required('can_access_setup')
def template_list(request):
    """
    List view for franchise templates
    """
    templates = FranchiseTemplate.objects.all()
    
    # Filter by active status if provided
    active_filter = request.GET.get('is_active')
    if active_filter:
        is_active = active_filter == 'true'
        templates = templates.filter(is_active=is_active)
    
    # Pagination
    paginator = Paginator(templates.order_by('name'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'page_title': _("Franchise Templates")
    }
    
    return render(request, 'franchise_setup/template_list.html', context)


@login_required
@role_required('can_access_setup')
def template_detail(request, pk):
    """
    Detail view for a franchise template
    """
    template = get_object_or_404(FranchiseTemplate, pk=pk)
    
    # Get requirements associated with this template
    requirements = template.requirements.all().order_by('requirement_type', 'name')
    
    # Get agreements using this template
    agreements = template.agreements.all().select_related('franchise')[:10]
    
    context = {
        'template': template,
        'requirements': requirements,
        'agreements': agreements,
        'page_title': _("Template: ") + template.name
    }
    
    return render(request, 'franchise_setup/template_detail.html', context)


@login_required
@role_required('can_access_setup')
@require_POST
def update_compliance_status(request, pk):
    """
    Update compliance status for a specific record
    """
    compliance = get_object_or_404(FranchiseCompliance, pk=pk)
    
    new_status = request.POST.get('status')
    notes = request.POST.get('notes', '')
    
    if new_status in ['compliant', 'non_compliant', 'pending']:
        compliance.status = new_status
        if notes:
            compliance.notes = notes
        compliance.save()
        
        messages.success(request, _('Compliance status updated successfully'))
    else:
        messages.error(request, _('Invalid status provided'))
    
    return redirect('franchise_setup:compliance_list')


@login_required
@role_required('can_access_setup')
def requirements_list(request):
    """
    List view for franchise requirements
    """
    requirements = FranchiseRequirement.objects.all()
    
    # Filter by requirement type if provided
    requirement_type = request.GET.get('requirement_type')
    if requirement_type:
        requirements = requirements.filter(requirement_type=requirement_type)
    
    # Filter by priority if provided
    priority_filter = request.GET.get('priority')
    if priority_filter:
        requirements = requirements.filter(priority=priority_filter)
    
    # Pagination
    paginator = Paginator(requirements.order_by('priority', 'name'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'requirement_types': dict(FranchiseRequirement._meta.get_field('requirement_type').choices),
        'priorities': dict(FranchiseRequirement._meta.get_field('priority').choices),
        'page_title': _("Franchise Requirements")
    }
    
    return render(request, 'franchise_setup/requirements_list.html', context)


@login_required
@role_required('can_access_setup')
def requirements_detail(request, pk):
    """
    Detail view for a franchise requirement
    """
    requirement = get_object_or_404(FranchiseRequirement, pk=pk)
    
    # Get compliance records for this requirement
    compliance_records = FranchiseCompliance.objects.filter(
        requirement=requirement
    ).select_related('franchise').order_by('-verification_date')
    
    context = {
        'requirement': requirement,
        'compliance_records': compliance_records,
        'page_title': _("Requirement: ") + requirement.name
    }
    
    return render(request, 'franchise_setup/requirements_detail.html', context)


@login_required
@role_required('can_access_setup')
def quality_certificates_list(request):
    """
    List view for quality certificates
    """
    # Placeholder view for quality certificates management
    context = {
        'page_title': _("Quality Certificates"),
        'section_title': _("Quality Management System"),
        'description': _("Manage quality certificates and certifications for franchise operations."),
        'features': [
            _("ISO 9001 Quality Management"),
            _("Service Quality Standards"),
            _("Certificate Tracking & Renewal"),
            _("Audit Reports & Compliance"),
            _("Quality Metrics & KPIs")
        ]
    }
    
    return render(request, 'franchise_setup/placeholder_page.html', context)


@login_required
@role_required('can_access_setup')
def quality_certificates_detail(request, pk):
    """
    Detail view for a quality certificate
    """
    context = {
        'page_title': _("Quality Certificate Details"),
        'section_title': _("Certificate Information"),
        'description': _("Detailed view of quality certificate and related documentation.")
    }
    
    return render(request, 'franchise_setup/placeholder_page.html', context)


@login_required
@role_required('can_access_setup')
def performance_indicators_list(request):
    """
    List view for performance indicators
    """
    context = {
        'page_title': _("Performance Indicators"),
        'section_title': _("Key Performance Indicators (KPIs)"),
        'description': _("Monitor and track franchise performance across various metrics."),
        'features': [
            _("Revenue Performance Tracking"),
            _("Customer Satisfaction Metrics"),
            _("Service Quality Indicators"),
            _("Operational Efficiency Metrics"),
            _("Compliance Score Monitoring"),
            _("Benchmark Comparisons")
        ]
    }
    
    return render(request, 'franchise_setup/placeholder_page.html', context)


@login_required
@role_required('can_access_setup')
def fee_structure_list(request):
    """
    List view for fee structure management
    """
    fees = FranchiseFee.objects.all().select_related('agreement')
    
    # Filter by fee type if provided
    fee_type = request.GET.get('fee_type')
    if fee_type:
        fees = fees.filter(fee_type=fee_type)
    
    # Filter by payment frequency if provided
    frequency_filter = request.GET.get('payment_frequency')
    if frequency_filter:
        fees = fees.filter(payment_frequency=frequency_filter)
    
    # Pagination
    paginator = Paginator(fees.order_by('fee_type', 'amount'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'fee_types': dict(FranchiseFee._meta.get_field('fee_type').choices),
        'payment_frequencies': dict(FranchiseFee._meta.get_field('payment_frequency').choices),
        'page_title': _("Fee Structure Management")
    }
    
    return render(request, 'franchise_setup/fee_structure_list.html', context)


@login_required
@role_required('can_access_setup')
def fee_structure_detail(request, pk):
    """
    Detail view for a fee structure
    """
    fee = get_object_or_404(FranchiseFee.objects.select_related('agreement'), pk=pk)
    
    context = {
        'fee': fee,
        'page_title': _("Fee Structure: ") + str(fee.fee_type)
    }
    
    return render(request, 'franchise_setup/fee_structure_detail.html', context)


@login_required
@role_required('can_access_setup')
def financial_reports_list(request):
    """
    List view for financial reports
    """
    context = {
        'page_title': _("Financial Reports"),
        'section_title': _("Franchise Financial Reporting"),
        'description': _("Comprehensive financial reports and analytics for franchise operations."),
        'features': [
            _("Quarterly Revenue Reports"),
            _("Annual Financial Summaries"),
            _("Royalty Payment Tracking"),
            _("Cost Analysis & Profitability"),
            _("Cash Flow Statements"),
            _("Financial Performance Benchmarks")
        ]
    }
    
    return render(request, 'franchise_setup/placeholder_page.html', context)


@login_required
@role_required('can_access_setup')
def quarterly_financial_report(request):
    """
    Quarterly financial report view
    """
    context = {
        'page_title': _("Quarterly Financial Report"),
        'section_title': _("Q{} {} Financial Performance").format(
            request.GET.get('quarter', '1'),
            request.GET.get('year', '2025')
        ),
        'description': _("Detailed quarterly financial analysis and performance metrics.")
    }
    
    return render(request, 'franchise_setup/placeholder_page.html', context)


@login_required
@role_required('can_access_setup')
def annual_financial_report(request):
    """
    Annual financial report view
    """
    context = {
        'page_title': _("Annual Financial Report"),
        'section_title': _("{} Annual Financial Report").format(
            request.GET.get('year', '2024')
        ),
        'description': _("Comprehensive annual financial analysis and year-over-year comparisons.")
    }
    
    return render(request, 'franchise_setup/placeholder_page.html', context)
