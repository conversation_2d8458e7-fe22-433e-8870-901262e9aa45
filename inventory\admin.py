from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from inventory.models import (
    Item, Movement, ItemDocument, UnitOfMeasurement, UnitConversion, 
    MovementType, VehicleCompatibility, OperationCompatibility, 
    ItemClassification, VehicleOperationCompatibility, VehicleModelPart, 
    OperationPricing, PartPricing, ItemBatch, BatchMovement, 
    SlowMovingStockConfig, InventoryValuationMethod
)
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from import_export.fields import Field
import waffle
from core.admin import TenantAdminMixin


class MovementInline(admin.TabularInline):
    model = Movement
    extra = 0
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('unit_of_measurement',)


class ItemDocumentInline(admin.TabularInline):
    model = ItemDocument
    extra = 1
    readonly_fields = ('created_at', 'document_preview')
    fields = ('title', 'document_type', 'file', 'description', 'is_public', 'document_preview', 'created_at')
    
    def document_preview(self, obj):
        """Display preview for image documents"""
        if obj.id and obj.file and obj.is_image():
            try:
                return format_html('<a href="{}" target="_blank"><img src="{}" width="100" height="auto" /></a>', 
                                 obj.file.url, obj.file.url)
            except (ValueError, FileNotFoundError, AttributeError):
                return "File not found"
        elif obj.id and obj.file:
            try:
                return format_html('<a href="{}" target="_blank">{}</a>', obj.file.url, obj.filename())
            except (ValueError, FileNotFoundError, AttributeError):
                return "File not found"
        return "-"
    document_preview.short_description = _("Preview")


class UnitConversionInline(admin.TabularInline):
    model = UnitConversion
    fk_name = 'from_unit'
    extra = 1
    verbose_name = _("Conversion to another unit")
    verbose_name_plural = _("Conversions to other units")


class BatchMovementInline(admin.TabularInline):
    model = BatchMovement
    fk_name = 'batch'
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('movement_type', 'quantity', 'reference_number', 'notes', 'created_at')


class ItemBatchInline(admin.TabularInline):
    """Inline for showing batches in Item admin"""
    model = ItemBatch
    extra = 0
    readonly_fields = ('available_quantity_display', 'age_display', 'created_at')
    fields = ('batch_number', 'current_quantity', 'available_quantity_display', 'status', 'received_date', 'expiry_date', 'age_display')
    
    def available_quantity_display(self, obj):
        if obj.pk:
            return obj.available_quantity
        return "-"
    available_quantity_display.short_description = _("Available")
    
    def age_display(self, obj):
        if obj.pk and obj.received_date:
            return f"{obj.age_in_stock_days} {_('days')}"
        return "-"
    age_display.short_description = _("Age")


class ItemResource(resources.ModelResource):
    """Resource for import/export of inventory items"""
    tenant_id = Field(attribute='tenant_id', column_name='tenant_id')
    unit_symbol = Field(column_name='unit_symbol')
    
    class Meta:
        model = Item
        import_id_fields = ('id',)
        export_order = ('id', 'name', 'sku', 'description', 'quantity', 'unit_symbol', 'attributes', 'tenant_id')
        skip_unchanged = True
        report_skipped = False
    
    def dehydrate_unit_symbol(self, item):
        return item.unit_of_measurement.symbol if item.unit_of_measurement else ""


class MovementResource(resources.ModelResource):
    """
    Resource for import/export of Movement records
    """
    class Meta:
        model = Movement
        fields = ('id', 'tenant_id', 'item__sku', 'quantity', 'unit_of_measurement__symbol', 
                 'movement_type', 'movement_type_ref__name', 'reference', 'notes', 'created_at')
        export_order = fields


class UnitOfMeasurementResource(resources.ModelResource):
    """Resource for import/export of units of measurement"""
    tenant_id = Field(attribute='tenant_id', column_name='tenant_id')
    
    class Meta:
        model = UnitOfMeasurement
        import_id_fields = ('id',)
        skip_unchanged = True
        report_skipped = False


class UnitConversionResource(resources.ModelResource):
    """Resource for import/export of unit conversions"""
    tenant_id = Field(attribute='tenant_id', column_name='tenant_id')
    from_unit_symbol = Field(column_name='from_unit_symbol')
    to_unit_symbol = Field(column_name='to_unit_symbol')
    
    class Meta:
        model = UnitConversion
        import_id_fields = ('id',)
        skip_unchanged = True
        report_skipped = False
    
    def dehydrate_from_unit_symbol(self, conversion):
        return conversion.from_unit.symbol if conversion.from_unit else ""
        
    def dehydrate_to_unit_symbol(self, conversion):
        return conversion.to_unit.symbol if conversion.to_unit else ""


class ItemDocumentResource(resources.ModelResource):
    """Resource for import/export of item documents"""
    item_name = Field(column_name='item_name')
    tenant_id = Field(attribute='tenant_id', column_name='tenant_id')
    filename = Field(column_name='filename')
    
    class Meta:
        model = ItemDocument
        import_id_fields = ('id',)
        skip_unchanged = True
        report_skipped = False
        fields = ('id', 'title', 'document_type', 'description', 'is_public', 
                 'item', 'item_name', 'tenant_id', 'filename', 'created_at')
    
    def dehydrate_item_name(self, document):
        return document.item.name if document.item else ""
        
    def dehydrate_filename(self, document):
        return document.filename()


class ItemClassificationResource(resources.ModelResource):
    """Resource for import/export of item classifications"""
    tenant_id = Field(attribute='tenant_id', column_name='tenant_id')
    parent_name = Field(column_name='parent_name')
    
    class Meta:
        model = ItemClassification
        import_id_fields = ('id',)
        fields = ('id', 'name', 'code', 'description', 'parent', 'parent_name', 'level', 'is_active', 'tenant_id')
        export_order = fields
        skip_unchanged = True
        report_skipped = False
    
    def dehydrate_parent_name(self, classification):
        return classification.parent.name if classification.parent else ""


@admin.register(ItemClassification)
class ItemClassificationAdmin(TenantAdminMixin, ImportExportModelAdmin):
    resource_class = ItemClassificationResource
    list_display = ('name', 'code', 'parent', 'level', 'is_active', 'item_count', 'tenant_id')
    list_filter = ('is_active', 'level', 'tenant_id')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('level', 'created_at', 'updated_at')
    autocomplete_fields = ('parent',)
    
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description', 'parent', 'level', 'is_active')
        }),
        (_('Display Options'), {
            'fields': ('icon', 'color')
        }),
        (_('Custom Attributes'), {
            'fields': ('attributes',),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def item_count(self, obj):
        return obj.items.count()
    item_count.short_description = _("Items")


@admin.register(UnitOfMeasurement)
class UnitOfMeasurementAdmin(TenantAdminMixin, ImportExportModelAdmin):
    resource_class = UnitOfMeasurementResource
    list_display = ('name', 'symbol', 'is_base_unit', 'tenant_id')
    list_filter = ('is_base_unit', 'tenant_id')
    search_fields = ('name', 'symbol', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [UnitConversionInline]
    fieldsets = (
        (None, {
            'fields': ('name', 'symbol', 'description', 'is_base_unit')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(UnitConversion)
class UnitConversionAdmin(TenantAdminMixin, ImportExportModelAdmin):
    resource_class = UnitConversionResource
    list_display = ('from_unit', 'to_unit', 'conversion_factor', 'tenant_id')
    list_filter = ('from_unit', 'to_unit', 'tenant_id')
    search_fields = ('from_unit__name', 'to_unit__name')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('from_unit', 'to_unit')
    fieldsets = (
        (None, {
            'fields': ('from_unit', 'to_unit', 'conversion_factor')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Item)
class ItemAdmin(TenantAdminMixin, ImportExportModelAdmin):
    resource_class = ItemResource
    list_display = ('name', 'sku', 'quantity', 'unit_display', 'unit_price', 'min_stock_level', 'is_low_stock', 'classification', 'batch_summary', 'tenant_id')
    list_filter = ('tenant_id', 'unit_of_measurement', 'classification', 'category')
    search_fields = ('name', 'sku', 'description')
    readonly_fields = ('created_at', 'updated_at', 'is_low_stock')
    autocomplete_fields = ('unit_of_measurement', 'classification')
    inlines = [ItemDocumentInline, MovementInline, ItemBatchInline]
    fieldsets = (
        (None, {
            'fields': ('sku', 'name', 'description', 'unit_price')
        }),
        (_('Stock Information'), {
            'fields': ('quantity', 'unit_of_measurement', 'min_stock_level', 'is_low_stock')
        }),
        (_('Categorization'), {
            'fields': ('category', 'item_type', 'classification')
        }),
        (_('Dynamic Attributes'), {
            'fields': ('attributes',),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.short_description = _("Low Stock")
    is_low_stock.boolean = True
    
    def unit_display(self, obj):
        if obj.unit_of_measurement:
            return f"{obj.unit_of_measurement.symbol}"
        return "-"
    unit_display.short_description = _("Unit")
    
    def batch_summary(self, obj):
        """Display batch summary for item"""
        active_batches = obj.batches.filter(status='active', current_quantity__gt=0).count()
        if active_batches == 0:
            return "-"
        
        total_batched_qty = sum(batch.current_quantity for batch in obj.batches.filter(status='active'))
        expired_batches = obj.batches.filter(status='expired').count()
        
        summary = f"{active_batches} {_('active')}"
        if expired_batches > 0:
            summary += f", {expired_batches} {_('expired')}"
        
        return format_html('{}<br/><small>{} {}</small>', summary, total_batched_qty, _('total qty'))
    batch_summary.short_description = _("Batches")


@admin.register(Movement)
class MovementAdmin(TenantAdminMixin, ImportExportModelAdmin):
    resource_class = MovementResource
    
    list_display = ('item', 'quantity', 'movement_display', 'reference', 'created_at')
    list_filter = ('movement_type', 'movement_type_ref', 'created_at')
    search_fields = ('item__name', 'item__sku', 'reference', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('item', 'unit_of_measurement', 'movement_type_ref')
    
    fieldsets = (
        (None, {
            'fields': ('item', 'quantity', 'unit_of_measurement')
        }),
        ('Movement Details', {
            'fields': ('movement_type_ref', 'movement_type', 'reference', 'notes')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def movement_display(self, obj):
        """Display the movement type name with color if available"""
        if obj.movement_type_ref:
            if obj.movement_type_ref.color:
                return format_html(
                    '<span style="color: {};">{}</span>',
                    obj.movement_type_ref.color,
                    obj.movement_type_ref.name
                )
            return obj.movement_type_ref.name
        return obj.get_movement_type_display() if obj.movement_type else "-"
    
    movement_display.short_description = _("Movement Type")
    movement_display.admin_order_field = 'movement_type_ref__name'


@admin.register(ItemDocument)
class ItemDocumentAdmin(TenantAdminMixin, ImportExportModelAdmin):
    resource_class = ItemDocumentResource
    list_display = ('title', 'item', 'document_type', 'is_public', 'created_at', 'file_preview')
    list_filter = ('document_type', 'is_public', 'created_at')
    search_fields = ('title', 'item__name', 'item__sku', 'description')
    readonly_fields = ('created_at', 'updated_at', 'file_preview')
    raw_id_fields = ('item',)
    date_hierarchy = 'created_at'
    fieldsets = (
        (None, {
            'fields': ('item', 'title', 'document_type', 'file', 'description')
        }),
        (_('Access Control'), {
            'fields': ('is_public',)
        }),
        (_('Preview'), {
            'fields': ('file_preview',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def file_preview(self, obj):
        """Display preview for the document"""
        if not obj.id:
            return "-"
            
        if not obj.file:
            return "No file attached"

        try:
            if obj.is_image():
                return format_html('<a href="{}" target="_blank"><img src="{}" width="200" height="auto" /></a>', 
                                 obj.file.url, obj.file.url)
            else:
                file_icon = "📄"  # Default file icon
                extension = obj.file_extension()
                
                # Show more specific icons based on extension
                if extension in ['pdf']:
                    file_icon = "📕"  # Book for PDF
                elif extension in ['doc', 'docx', 'txt', 'rtf']:
                    file_icon = "📝"  # Note for documents
                elif extension in ['xls', 'xlsx', 'csv']:
                    file_icon = "📊"  # Chart for spreadsheets
                elif extension in ['zip', 'rar', 'tar', 'gz']:
                    file_icon = "🗜️"  # Compression for archives
                    
                return format_html('{} <a href="{}" target="_blank">{}</a> ({} KB)', 
                                 file_icon, obj.file.url, obj.filename(), 
                                 round(obj.file.size / 1024, 1) if obj.file else 0)
        except (ValueError, FileNotFoundError, AttributeError):
            return "File not accessible"
    
    file_preview.short_description = _("File")


@admin.register(MovementType)
class MovementTypeAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'code', 'is_inbound', 'is_outbound', 'is_active', 'sequence')
    list_filter = ('is_inbound', 'is_outbound', 'is_active', 'requires_approval')
    search_fields = ('name', 'code', 'description')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'code', 'name', 'description')
        }),
        ('Behavior', {
            'fields': ('is_inbound', 'is_outbound', 'requires_reference', 'requires_approval')
        }),
        ('Display', {
            'fields': ('icon', 'color', 'sequence', 'is_active')
        }),
    )


@admin.register(VehicleCompatibility)
class VehicleCompatibilityAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('item', 'make', 'model', 'year_range', 'variant', 'engine')
    list_filter = ('make', 'model', 'year_from')
    search_fields = ('item__name', 'item__sku', 'make', 'model', 'variant', 'engine')
    autocomplete_fields = ('item',)
    
    def year_range(self, obj):
        if obj.year_to:
            return f"{obj.year_from} - {obj.year_to}"
        return f"{obj.year_from}+"
    year_range.short_description = _("Year Range")


class VehicleOperationCompatibilityInline(admin.TabularInline):
    model = VehicleOperationCompatibility
    extra = 1
    autocomplete_fields = ('vehicle_make', 'vehicle_model')
    fields = ('vehicle_make', 'vehicle_model', 'year_from', 'year_to', 'duration_minutes')
    

@admin.register(VehicleOperationCompatibility)
class VehicleOperationCompatibilityAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('operation_compatibility', 'vehicle_make', 'vehicle_model', 'year_range', 'duration_minutes_display')
    list_filter = ('vehicle_make', 'vehicle_model')
    search_fields = ('operation_compatibility__item__name', 'vehicle_make__name', 'vehicle_model__name')
    autocomplete_fields = ('operation_compatibility', 'vehicle_make', 'vehicle_model')
    
    def year_range(self, obj):
        if obj.year_from and obj.year_to:
            return f"{obj.year_from} - {obj.year_to}"
        elif obj.year_from:
            return f"{obj.year_from}+"
        return "-"
    year_range.short_description = _('Year Range')
    
    def duration_minutes_display(self, obj):
        return obj.duration_minutes_display()
    duration_minutes_display.short_description = _('Duration')
    duration_minutes_display.admin_order_field = 'duration_minutes'


@admin.register(OperationCompatibility)
class OperationCompatibilityAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = (
        'item', 
        'operation_type', 
        'vehicle_count',
        'operation_description',
        'duration_minutes_display',
        'maintenance_schedule',
        'is_required', 
        'is_common', 
    )
    list_filter = (
        'operation_type',
        'operation_description',
        'is_required',
        'is_common',
        'maintenance_schedule',
        'vehicle_compatibilities__vehicle_make',
    )
    search_fields = (
        'item__name',
        'item__part_number',
        'operation_type__name',
        'operation_description',
        'vehicle_compatibilities__vehicle_make__name',
        'vehicle_compatibilities__vehicle_model__name',
    )
    autocomplete_fields = (
        'item',
        'operation_type',
        'maintenance_schedule',
        'vehicle_make',
        'vehicle_model',
    )
    inlines = [VehicleOperationCompatibilityInline]
    actions = ['migrate_legacy_vehicle_data']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': (
                'item',
                'operation_type',
                'maintenance_schedule',
                'is_required',
                'is_common',
            )
        }),
        (_('Legacy Vehicle Fields (Will be deprecated)'), {
            'fields': (
                'vehicle_make',
                'vehicle_model',
                'year_from',
                'year_to',
            ),
            'classes': ('collapse',),
        }),
        (_('Operation Details'), {
            'fields': (
                'operation_description',
                'duration_minutes',
            ),
        }),
    )
    
    def vehicle_count(self, obj):
        count = obj.vehicle_compatibilities.count()
        return count if count > 0 else "-"
    vehicle_count.short_description = _('Compatible Vehicles')
    
    def year_range(self, obj):
        if obj.year_from and obj.year_to:
            return f"{obj.year_from} - {obj.year_to}"
        elif obj.year_from:
            return f"{obj.year_from}+"
        return "-"
    year_range.short_description = _('Year Range')

    def migrate_legacy_vehicle_data(self, request, queryset):
        migrated_count = 0
        for compat in queryset:
            if compat.migrate_legacy_vehicle_data():
                migrated_count += 1
        
        if migrated_count:
            self.message_user(request, _(f"Successfully migrated {migrated_count} records to the new vehicle compatibility structure."))
        else:
            self.message_user(request, _("No records needed migration."))
    migrate_legacy_vehicle_data.short_description = _("Migrate legacy vehicle data to new structure")

    def duration_minutes_display(self, obj):
        return obj.duration_minutes_display() # Use the model method instead of duration_display
    duration_minutes_display.short_description = _('Duration')
    duration_minutes_display.admin_order_field = 'duration_minutes'


class OperationPricingResource(resources.ModelResource):
    """Resource for import/export of operation pricing data"""
    operation_type_name = Field(column_name='operation_type_name')
    vehicle_make_name = Field(column_name='vehicle_make_name')
    vehicle_model_name = Field(column_name='vehicle_model_name')
    franchise_name = Field(column_name='franchise_name')
    company_name = Field(column_name='company_name')
    service_center_name = Field(column_name='service_center_name')
    total_price = Field(column_name='total_price')
    
    class Meta:
        model = OperationPricing
        fields = ('id', 'tenant_id', 'operation_type', 'operation_type_name',
                 'vehicle_make', 'vehicle_make_name', 'vehicle_model', 'vehicle_model_name',
                 'franchise', 'franchise_name', 'company', 'company_name', 
                 'service_center', 'service_center_name',
                 'year_from', 'year_to', 'base_price', 'labor_hours', 'labor_rate', 
                 'total_price', 'is_active', 'notes')
        export_order = fields
        import_id_fields = ('id',)
        skip_unchanged = True
    
    def dehydrate_operation_type_name(self, pricing):
        return pricing.operation_type.name if pricing.operation_type else ""
        
    def dehydrate_vehicle_make_name(self, pricing):
        return pricing.vehicle_make.name if pricing.vehicle_make else ""
        
    def dehydrate_vehicle_model_name(self, pricing):
        return pricing.vehicle_model.name if pricing.vehicle_model else ""
        
    def dehydrate_franchise_name(self, pricing):
        return pricing.franchise.name if pricing.franchise else ""
        
    def dehydrate_company_name(self, pricing):
        return pricing.company.name if pricing.company else ""
        
    def dehydrate_service_center_name(self, pricing):
        return pricing.service_center.name if pricing.service_center else ""
        
    def dehydrate_total_price(self, pricing):
        return pricing.total_price


class PartPricingResource(resources.ModelResource):
    """Resource for import/export of part pricing data"""
    item_name = Field(column_name='item_name')
    item_sku = Field(column_name='item_sku')
    operation_type_name = Field(column_name='operation_type_name')
    vehicle_make_name = Field(column_name='vehicle_make_name')
    vehicle_model_name = Field(column_name='vehicle_model_name')
    franchise_name = Field(column_name='franchise_name')
    company_name = Field(column_name='company_name')
    service_center_name = Field(column_name='service_center_name')
    
    class Meta:
        model = PartPricing
        fields = ('id', 'tenant_id', 'item', 'item_name', 'item_sku',
                 'operation_type', 'operation_type_name', 
                 'vehicle_make', 'vehicle_make_name', 
                 'vehicle_model', 'vehicle_model_name',
                 'franchise', 'franchise_name', 
                 'company', 'company_name', 
                 'service_center', 'service_center_name',
                 'price', 'is_special_pricing', 
                 'valid_from', 'valid_to', 'is_active', 'notes')
        export_order = fields
        import_id_fields = ('id',)
        skip_unchanged = True
    
    def dehydrate_item_name(self, pricing):
        return pricing.item.name if pricing.item else ""
        
    def dehydrate_item_sku(self, pricing):
        return pricing.item.sku if pricing.item else ""
        
    def dehydrate_operation_type_name(self, pricing):
        return pricing.operation_type.name if pricing.operation_type else ""
        
    def dehydrate_vehicle_make_name(self, pricing):
        return pricing.vehicle_make.name if pricing.vehicle_make else ""
        
    def dehydrate_vehicle_model_name(self, pricing):
        return pricing.vehicle_model.name if pricing.vehicle_model else ""
        
    def dehydrate_franchise_name(self, pricing):
        return pricing.franchise.name if pricing.franchise else ""
        
    def dehydrate_company_name(self, pricing):
        return pricing.company.name if pricing.company else ""
        
    def dehydrate_service_center_name(self, pricing):
        return pricing.service_center.name if pricing.service_center else ""


@admin.register(OperationPricing)
class OperationPricingAdmin(TenantAdminMixin, ImportExportModelAdmin):
    resource_class = OperationPricingResource
    list_display = ['operation_type', 'vehicle_make', 'vehicle_model', 'location_display', 'year_range', 'base_price', 'labor_display', 'total_price', 'is_active']
    list_filter = ['operation_type', 'vehicle_make', 'franchise', 'company', 'is_active', 'tenant_id']
    search_fields = ['operation_type__name', 'vehicle_make__name', 'vehicle_model__name', 'franchise__name', 'company__name', 'service_center__name', 'notes']
    readonly_fields = ['total_price', 'created_at', 'updated_at']
    autocomplete_fields = ['operation_type', 'vehicle_make', 'vehicle_model', 'franchise', 'company', 'service_center']
    list_editable = ['base_price', 'is_active']
    list_per_page = 25
    
    fieldsets = (
        (_('Operation Information'), {
            'fields': ('operation_type', 'base_price')
        }),
        (_('Vehicle Information'), {
            'fields': ('vehicle_make', 'vehicle_model', 'year_from', 'year_to')
        }),
        (_('Location Information'), {
            'fields': ('franchise', 'company', 'service_center'),
            'description': _('Pricing can be specific to a franchise, company, or service center. More specific levels override less specific ones.')
        }),
        (_('Labor Information'), {
            'fields': ('labor_hours', 'labor_rate', 'total_price')
        }),
        (_('Additional Information'), {
            'fields': ('is_active', 'notes')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def year_range(self, obj):
        """Display year range in a friendly format"""
        if obj.year_from and obj.year_to:
            return f"{obj.year_from}-{obj.year_to}"
        elif obj.year_from:
            return f"{obj.year_from}+"
        return "-"
    year_range.short_description = _("Year Range")
    
    def labor_display(self, obj):
        """Display labor information in a compact format"""
        return f"{obj.labor_hours}h × {obj.labor_rate}"
    labor_display.short_description = _("Labor")
    
    def location_display(self, obj):
        """Display location information in a compact format"""
        if obj.service_center:
            return f"{obj.service_center.name}"
        elif obj.company:
            return f"{obj.company.name}"
        elif obj.franchise:
            return f"{obj.franchise.name}"
        return "-"
    location_display.short_description = _("Location")
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        
        # Filter for the tenant if available
        tenant_id = getattr(request, 'tenant_id', None)
        if tenant_id:
            if 'tenant_id' in form.base_fields:
                form.base_fields['tenant_id'].initial = tenant_id
                
        return form
    
    def save_model(self, request, obj, form, change):
        """Ensure tenant ID is set"""
        if not obj.tenant_id:
            tenant_id = getattr(request, 'tenant_id', None)
            if tenant_id:
                obj.tenant_id = tenant_id
        super().save_model(request, obj, form, change)
    
    actions = ['mark_active', 'mark_inactive']
    
    def mark_active(self, request, queryset):
        """Mark selected pricings as active"""
        updated = queryset.update(is_active=True)
        self.message_user(request, _("%(count)d pricing entries marked as active.") % {'count': updated})
    mark_active.short_description = _("Mark selected pricings as active")
    
    def mark_inactive(self, request, queryset):
        """Mark selected pricings as inactive"""
        updated = queryset.update(is_active=False)
        self.message_user(request, _("%(count)d pricing entries marked as inactive.") % {'count': updated})
    mark_inactive.short_description = _("Mark selected pricings as inactive")


@admin.register(PartPricing)
class PartPricingAdmin(TenantAdminMixin, ImportExportModelAdmin):
    resource_class = PartPricingResource
    list_display = ['item', 'operation_type', 'vehicle_make', 'vehicle_model', 'location_display', 'price', 'validity_period', 'is_special_pricing', 'is_active']
    list_filter = ['operation_type', 'vehicle_make', 'franchise', 'company', 'is_special_pricing', 'is_active', 'tenant_id']
    search_fields = ['item__name', 'item__sku', 'operation_type__name', 'vehicle_make__name', 'vehicle_model__name', 'franchise__name', 'company__name', 'service_center__name', 'notes']
    readonly_fields = ['created_at', 'updated_at']
    autocomplete_fields = ['item', 'operation_type', 'vehicle_make', 'vehicle_model', 'franchise', 'company', 'service_center']
    list_editable = ['price', 'is_active']
    list_per_page = 25
    date_hierarchy = 'valid_from'
    
    fieldsets = (
        (_('Part Information'), {
            'fields': ('item', 'price')
        }),
        (_('Operation & Vehicle Specifics'), {
            'fields': ('operation_type', 'vehicle_make', 'vehicle_model')
        }),
        (_('Location Information'), {
            'fields': ('franchise', 'company', 'service_center'),
            'description': _('Pricing can be specific to a franchise, company, or service center. More specific levels override less specific ones.')
        }),
        (_('Pricing Validity'), {
            'fields': ('is_special_pricing', 'valid_from', 'valid_to', 'is_active')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def validity_period(self, obj):
        """Display validity period in a friendly format"""
        if obj.valid_from and obj.valid_to:
            return f"{obj.valid_from} - {obj.valid_to}"
        elif obj.valid_from:
            return _("From %(date)s") % {'date': obj.valid_from}
        elif obj.valid_to:
            return _("Until %(date)s") % {'date': obj.valid_to}
        return _("Unlimited")
    validity_period.short_description = _("Validity Period")
    
    def location_display(self, obj):
        """Display location information in a compact format"""
        if obj.service_center:
            return f"{obj.service_center.name}"
        elif obj.company:
            return f"{obj.company.name}"
        elif obj.franchise:
            return f"{obj.franchise.name}"
        return "-"
    location_display.short_description = _("Location")
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        
        # Filter for the tenant if available
        tenant_id = getattr(request, 'tenant_id', None)
        if tenant_id:
            if 'tenant_id' in form.base_fields:
                form.base_fields['tenant_id'].initial = tenant_id
                
            # Filter item queryset by tenant
            if 'item' in form.base_fields:
                form.base_fields['item'].queryset = form.base_fields['item'].queryset.filter(tenant_id=tenant_id)
                
        return form
    
    def save_model(self, request, obj, form, change):
        """Ensure tenant ID is set"""
        if not obj.tenant_id:
            tenant_id = getattr(request, 'tenant_id', None)
            if tenant_id:
                obj.tenant_id = tenant_id
        super().save_model(request, obj, form, change)
    
    actions = ['mark_active', 'mark_inactive', 'mark_special', 'mark_regular']
    
    def mark_active(self, request, queryset):
        """Mark selected pricings as active"""
        updated = queryset.update(is_active=True)
        self.message_user(request, _("%(count)d pricing entries marked as active.") % {'count': updated})
    mark_active.short_description = _("Mark selected pricings as active")
    
    def mark_inactive(self, request, queryset):
        """Mark selected pricings as inactive"""
        updated = queryset.update(is_active=False)
        self.message_user(request, _("%(count)d pricing entries marked as inactive.") % {'count': updated})
    mark_inactive.short_description = _("Mark selected pricings as inactive")
    
    def mark_special(self, request, queryset):
        """Mark selected pricings as special"""
        updated = queryset.update(is_special_pricing=True)
        self.message_user(request, _("%(count)d pricing entries marked as special pricing.") % {'count': updated})
    mark_special.short_description = _("Mark selected pricings as special")
    
    def mark_regular(self, request, queryset):
        """Mark selected pricings as regular"""
        updated = queryset.update(is_special_pricing=False)
        self.message_user(request, _("%(count)d pricing entries marked as regular pricing.") % {'count': updated})
    mark_regular.short_description = _("Mark selected pricings as regular")


@admin.register(ItemBatch)
class ItemBatchAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = [
        'batch_number', 'item', 'current_quantity', 'reserved_quantity', 'available_quantity_display',
        'purchase_price', 'received_date', 'expiry_date', 'status', 'age_display', 'expiry_status'
    ]
    list_filter = ['status', 'received_date', 'expiry_date', 'item__classification']
    search_fields = ['batch_number', 'item__name', 'item__sku', 'supplier_batch_ref', 'purchase_order_ref']
    readonly_fields = [
        'created_at', 'updated_at', 'available_quantity_display', 
        'age_display', 'expiry_status', 'is_expired_display'
    ]
    autocomplete_fields = ['item']
    date_hierarchy = 'received_date'
    list_per_page = 25
    inlines = [BatchMovementInline]
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('item', 'batch_number', 'status')
        }),
        (_('Quantity Information'), {
            'fields': (
                ('initial_quantity', 'current_quantity', 'reserved_quantity'),
                'available_quantity_display'
            )
        }),
        (_('Pricing Information'), {
            'fields': ('purchase_price', 'selling_price')
        }),
        (_('Dates'), {
            'fields': (
                ('manufactured_date', 'received_date', 'expiry_date'),
                ('age_display', 'expiry_status', 'is_expired_display')
            )
        }),
        (_('References'), {
            'fields': ('supplier_batch_ref', 'purchase_order_ref', 'warehouse_location')
        }),
        (_('Additional Information'), {
            'fields': ('attributes', 'notes'),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def available_quantity_display(self, obj):
        return obj.available_quantity
    available_quantity_display.short_description = _("Available Quantity")
    
    def age_display(self, obj):
        return f"{obj.age_in_stock_days} {_('days')}"
    age_display.short_description = _("Age in Stock")
    
    def expiry_status(self, obj):
        if not obj.expiry_date:
            return "-"
        
        days = obj.days_until_expiry
        if days < 0:
            return format_html('<span style="color: red;">⚠️ {}</span>', _("Expired"))
        elif days <= 30:
            return format_html('<span style="color: orange;">⚠️ {} {}</span>', days, _("days left"))
        else:
            return format_html('<span style="color: green;">✓ {} {}</span>', days, _("days left"))
    expiry_status.short_description = _("Expiry Status")
    
    def is_expired_display(self, obj):
        return obj.is_expired
    is_expired_display.boolean = True
    is_expired_display.short_description = _("Is Expired")
    
    actions = ['mark_expired', 'mark_active', 'create_movement']
    
    def mark_expired(self, request, queryset):
        """Mark selected batches as expired"""
        updated = queryset.update(status='expired')
        self.message_user(request, _("%(count)d batches marked as expired.") % {'count': updated})
    mark_expired.short_description = _("Mark selected batches as expired")
    
    def mark_active(self, request, queryset):
        """Mark selected batches as active"""
        updated = queryset.update(status='active')
        self.message_user(request, _("%(count)d batches marked as active.") % {'count': updated})
    mark_active.short_description = _("Mark selected batches as active")


@admin.register(BatchMovement)
class BatchMovementAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ['batch', 'movement_type', 'quantity', 'reference_number', 'created_at']
    list_filter = ['movement_type', 'created_at', 'batch__item']
    search_fields = ['batch__batch_number', 'batch__item__name', 'reference_number', 'notes']
    readonly_fields = ['created_at', 'updated_at']
    autocomplete_fields = ['batch', 'movement']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (_('Movement Information'), {
            'fields': ('batch', 'movement_type', 'quantity', 'movement')
        }),
        (_('References'), {
            'fields': ('reference_number',)
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SlowMovingStockConfig)
class SlowMovingStockConfigAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = [
        'config_target', 'days_threshold', 'minimum_quantity_threshold',
        'auto_discount_enabled', 'discount_percentage', 'is_active'
    ]
    list_filter = ['auto_discount_enabled', 'notify_managers', 'is_active']
    search_fields = ['item__name', 'classification__name']
    autocomplete_fields = ['item', 'classification']
    
    fieldsets = (
        (_('Target'), {
            'fields': ('item', 'classification'),
            'description': _('Configure for a specific item or item classification. Leave both empty for global settings.')
        }),
        (_('Thresholds'), {
            'fields': ('days_threshold', 'minimum_quantity_threshold')
        }),
        (_('Actions'), {
            'fields': ('auto_discount_enabled', 'discount_percentage', 'notify_managers')
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
    )
    
    def config_target(self, obj):
        if obj.item:
            return f"Item: {obj.item.name}"
        elif obj.classification:
            return f"Classification: {obj.classification.name}"
        return "Global"
    config_target.short_description = _("Configuration Target")


@admin.register(InventoryValuationMethod)
class InventoryValuationMethodAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ['method_name', 'is_default', 'is_active']
    list_filter = ['method_name', 'is_default', 'is_active']
    search_fields = ['method_name', 'description']
    
    fieldsets = (
        (_('Valuation Method'), {
            'fields': ('method_name', 'description')
        }),
        (_('Status'), {
            'fields': ('is_default', 'is_active')
        }),
    )
