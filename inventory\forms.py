from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import Item, ItemClassification, Movement, UnitOfMeasurement, VehicleModelPart, OperationCompatibility, ItemBatch, SlowMovingStockConfig, InventoryValuationMethod

class ItemForm(forms.ModelForm):
    """Form for creating and updating inventory items"""
    
    class Meta:
        model = Item
        fields = [
            'sku', 'name', 'description', 'quantity', 'classification',
            'unit_of_measurement', 'unit_price', 'min_stock_level', 
            'category', 'item_type'
        ]
        widgets = {
            'sku': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رمز الصنف')
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('اسم الصنف')
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('وصف الصنف')
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0'
            }),
            'classification': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'unit_of_measurement': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0'
            }),
            'min_stock_level': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'item_type': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('نوع الصنف (اختياري)')
            }),
        }
        labels = {
            'sku': _('رمز الصنف'),
            'name': _('اسم الصنف'),
            'description': _('الوصف'),
            'quantity': _('الكمية الحالية'),
            'classification': _('التصنيف'),
            'unit_of_measurement': _('وحدة القياس'),
            'unit_price': _('سعر الوحدة'),
            'min_stock_level': _('الحد الأدنى للمخزون'),
            'category': _('الفئة'),
            'item_type': _('نوع الصنف'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filter choices based on user's tenant
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                self.fields['classification'].queryset = ItemClassification.objects.filter(tenant_id=tenant_id)
                self.fields['unit_of_measurement'].queryset = UnitOfMeasurement.objects.filter(tenant_id=tenant_id)
            
            # Make some fields optional
            self.fields['classification'].required = False
            self.fields['unit_of_measurement'].required = False
            self.fields['item_type'].required = False
    
    def clean_sku(self):
        sku = self.cleaned_data.get('sku')
        if sku:
            # Check for duplicate SKU (excluding current instance)
            queryset = Item.objects.filter(sku=sku)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise ValidationError(_('رمز الصنف موجود مسبقاً'))
        return sku
    
    def clean(self):
        cleaned_data = super().clean()
        # Add any custom validation here if needed
        return cleaned_data

class CategoryForm(forms.ModelForm):
    """Form for creating and updating inventory categories"""
    
    class Meta:
        model = ItemClassification
        fields = ['name', 'description', 'parent']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('اسم الفئة')
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('وصف الفئة (اختياري)')
            }),
            'parent': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
        }
        labels = {
            'name': _('اسم الفئة'),
            'description': _('الوصف'),
            'parent': _('الفئة الأب'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filter parent categories based on user's tenant (excluding self)
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                queryset = ItemClassification.objects.filter(tenant_id=tenant_id)
                if self.instance.pk:
                    queryset = queryset.exclude(pk=self.instance.pk)
                self.fields['parent'].queryset = queryset
            
        # Make parent category optional
        self.fields['parent'].empty_label = _('-- بدون فئة أب --')
        self.fields['parent'].required = False

class StockMovementForm(forms.ModelForm):
    """Form for creating stock movements"""
    
    class Meta:
        model = Movement
        fields = ['item', 'movement_type', 'quantity', 'unit_of_measurement', 'reference', 'notes']
        widgets = {
            'item': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'data-search': 'true'
            }),
            'movement_type': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0.01'
            }),
            'unit_of_measurement': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ملاحظات الحركة')
            }),
            'reference': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم المرجع (اختياري)')
            }),
        }
        labels = {
            'item': _('الصنف'),
            'movement_type': _('نوع الحركة'),
            'quantity': _('الكمية'),
            'unit_of_measurement': _('وحدة القياس'),
            'notes': _('ملاحظات'),
            'reference': _('رقم المرجع'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filter choices based on user's tenant
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                self.fields['item'].queryset = Item.objects.filter(
                    tenant_id=tenant_id
                ).order_by('name')
                
                # Filter unit of measurement
                self.fields['unit_of_measurement'].queryset = UnitOfMeasurement.objects.filter(
                    tenant_id=tenant_id
                ).order_by('name')
            
            # Make unit of measurement optional
            self.fields['unit_of_measurement'].required = False
    
    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity <= 0:
            raise ValidationError(_('الكمية يجب أن تكون أكبر من صفر'))
        return quantity

class VehicleCompatibilityForm(forms.ModelForm):
    """Form for managing vehicle-part compatibility"""
    
    class Meta:
        model = VehicleModelPart
        fields = ['item', 'make', 'model', 'year_from', 'year_to', 'notes']
        widgets = {
            'item': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'data-search': 'true'
            }),
            'make': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('ماركة المركبة')
            }),
            'model': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('موديل المركبة')
            }),
            'year_from': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'min': '1900',
                'max': '2030'
            }),
            'year_to': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'min': '1900',
                'max': '2030'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 2,
                'placeholder': _('ملاحظات (اختياري)')
            }),
        }
        labels = {
            'item': _('الصنف'),
            'make': _('ماركة المركبة'),
            'model': _('موديل المركبة'),
            'year_from': _('من سنة'),
            'year_to': _('إلى سنة'),
            'notes': _('ملاحظات'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filter items based on user's tenant
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                self.fields['item'].queryset = Item.objects.filter(
                    tenant_id=tenant_id
                ).order_by('name')
    
    def clean(self):
        cleaned_data = super().clean()
        year_from = cleaned_data.get('year_from')
        year_to = cleaned_data.get('year_to')
        
        if year_from and year_to and year_from > year_to:
            raise ValidationError({
                'year_to': _('سنة النهاية يجب أن تكون أكبر من أو تساوي سنة البداية')
            })
        
        return cleaned_data

class QuickStockAdjustmentForm(forms.Form):
    """Form for quick stock adjustments"""
    
    ADJUSTMENT_CHOICES = [
        ('adjustment_in', _('إضافة للمخزون')),
        ('adjustment_out', _('خصم من المخزون')),
    ]
    
    item = forms.ModelChoiceField(
        queryset=Item.objects.none(),
        label=_('الصنف'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'data-search': 'true'
        })
    )
    
    adjustment_type = forms.ChoiceField(
        choices=ADJUSTMENT_CHOICES,
        label=_('نوع التعديل'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
        })
    )
    
    quantity = forms.DecimalField(
        label=_('الكمية'),
        min_value=0.01,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'step': '0.01'
        })
    )
    
    reason = forms.CharField(
        label=_('السبب'),
        widget=forms.Textarea(attrs={
            'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'rows': 3,
            'placeholder': _('سبب التعديل')
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                self.fields['item'].queryset = Item.objects.filter(
                    tenant_id=tenant_id
                ).order_by('name')

class ItemSearchForm(forms.Form):
    """Form for searching items with advanced filters"""
    
    search = forms.CharField(
        required=False,
        label=_('البحث'),
        widget=forms.TextInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'placeholder': _('ابحث بالاسم أو الرمز أو الباركود')
        })
    )
    
    category = forms.ModelChoiceField(
        queryset=ItemClassification.objects.none(),
        required=False,
        empty_label=_('-- جميع الفئات --'),
        label=_('الفئة'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
        })
    )
    
    stock_level = forms.ChoiceField(
        choices=[
            ('', _('-- جميع مستويات المخزون --')),
            ('in_stock', _('متوفر')),
            ('low', _('مخزون منخفض')),
            ('out_of_stock', _('نفد المخزون')),
        ],
        required=False,
        label=_('مستوى المخزون'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                self.fields['category'].queryset = ItemClassification.objects.filter(
                    tenant_id=tenant_id
                ).order_by('name')


class ItemBatchForm(forms.ModelForm):
    """Form for creating and updating item batches"""
    
    class Meta:
        model = ItemBatch
        fields = [
            'batch_number', 'initial_quantity', 'current_quantity', 
            'purchase_price', 'selling_price', 'manufactured_date', 
            'received_date', 'expiry_date', 'supplier_batch_ref', 'notes'
        ]
        widgets = {
            'batch_number': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم الدفعة')
            }),
            'initial_quantity': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0'
            }),
            'current_quantity': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0'
            }),
            'purchase_price': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0'
            }),
            'selling_price': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0'
            }),
            'manufactured_date': forms.DateInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'type': 'date'
            }),
            'received_date': forms.DateInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'type': 'date'
            }),
            'expiry_date': forms.DateInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'type': 'date'
            }),
            'supplier_batch_ref': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('مرجع دفعة المورد')
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ملاحظات الدفعة')
            }),
        }
        labels = {
            'batch_number': _('رقم الدفعة'),
            'initial_quantity': _('الكمية الأولية'),
            'current_quantity': _('الكمية الحالية'),
            'purchase_price': _('سعر الشراء'),
            'selling_price': _('سعر البيع'),
            'manufactured_date': _('تاريخ الإنتاج'),
            'received_date': _('تاريخ الاستلام'),
            'expiry_date': _('تاريخ الانتهاء'),
            'supplier_batch_ref': _('مرجع دفعة المورد'),
            'notes': _('ملاحظات'),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        initial_quantity = cleaned_data.get('initial_quantity')
        current_quantity = cleaned_data.get('current_quantity')
        manufactured_date = cleaned_data.get('manufactured_date')
        expiry_date = cleaned_data.get('expiry_date')
        received_date = cleaned_data.get('received_date')
        
        # Validate quantity relationships
        if initial_quantity and current_quantity:
            if current_quantity > initial_quantity:
                raise ValidationError(_('الكمية الحالية لا يمكن أن تكون أكبر من الكمية الأولية'))
        
        # Validate dates
        if manufactured_date and expiry_date:
            if manufactured_date >= expiry_date:
                raise ValidationError(_('تاريخ الإنتاج يجب أن يكون قبل تاريخ الانتهاء'))
                
        if manufactured_date and received_date:
            if manufactured_date > received_date:
                raise ValidationError(_('تاريخ الإنتاج يجب أن يكون قبل أو يساوي تاريخ الاستلام'))
        
        return cleaned_data


class StockMovementWithBatchForm(forms.ModelForm):
    """Enhanced stock movement form with batch support"""
    
    batch = forms.ModelChoiceField(
        queryset=ItemBatch.objects.none(),
        required=False,
        empty_label=_('-- بدون دفعة محددة --'),
        label=_('الدفعة'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
        })
    )
    
    create_new_batch = forms.BooleanField(
        required=False,
        label=_('إنشاء دفعة جديدة'),
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox mt-1 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    # New batch fields (shown when create_new_batch is checked)
    new_batch_number = forms.CharField(
        required=False,
        label=_('رقم الدفعة الجديدة'),
        widget=forms.TextInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'placeholder': _('رقم الدفعة')
        })
    )
    
    new_batch_purchase_price = forms.DecimalField(
        required=False,
        label=_('سعر الشراء للدفعة'),
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'step': '0.01',
            'min': '0'
        })
    )
    
    new_batch_expiry_date = forms.DateField(
        required=False,
        label=_('تاريخ انتهاء الدفعة'),
        widget=forms.DateInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'type': 'date'
        })
    )
    
    class Meta:
        model = Movement
        fields = ['item', 'movement_type', 'quantity', 'unit_of_measurement', 'reference', 'notes']
        widgets = {
            'item': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'data-search': 'true'
            }),
            'movement_type': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0.01'
            }),
            'unit_of_measurement': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ملاحظات الحركة')
            }),
            'reference': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم المرجع (اختياري)')
            }),
        }
        labels = {
            'item': _('الصنف'),
            'movement_type': _('نوع الحركة'),
            'quantity': _('الكمية'),
            'unit_of_measurement': _('وحدة القياس'),
            'notes': _('ملاحظات'),
            'reference': _('رقم المرجع'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filter choices based on user's tenant
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                self.fields['item'].queryset = Item.objects.filter(
                    tenant_id=tenant_id
                ).order_by('name')
                
                # Filter unit of measurement
                self.fields['unit_of_measurement'].queryset = UnitOfMeasurement.objects.filter(
                    tenant_id=tenant_id
                ).order_by('name')
        
        # Update batch queryset when item is selected (will be handled by JavaScript)
        self.fields['batch'].queryset = ItemBatch.objects.filter(status='active')
    
    def clean(self):
        cleaned_data = super().clean()
        create_new_batch = cleaned_data.get('create_new_batch')
        batch = cleaned_data.get('batch')
        new_batch_number = cleaned_data.get('new_batch_number')
        movement_type = cleaned_data.get('movement_type')
        quantity = cleaned_data.get('quantity')
        
        # Validate batch requirements for purchase movements
        if movement_type == 'purchase' and create_new_batch and not new_batch_number:
            raise ValidationError(_('رقم الدفعة مطلوب عند إنشاء دفعة جديدة'))
        
        # Validate batch selection for outbound movements
        if movement_type in ['sale', 'adjustment_out'] and not batch and not create_new_batch:
            raise ValidationError(_('يجب تحديد دفعة للحركات الصادرة'))
        
        # Validate batch quantity for outbound movements
        if batch and movement_type in ['sale', 'adjustment_out'] and quantity:
            if quantity > batch.current_quantity:
                raise ValidationError(_('الكمية المطلوبة أكبر من المتوفر في الدفعة'))
        
        return cleaned_data


class SlowMovingStockConfigForm(forms.ModelForm):
    """Form for configuring slow-moving stock settings"""
    
    class Meta:
        model = SlowMovingStockConfig
        fields = [
            'days_threshold', 'minimum_quantity_threshold', 
            'auto_discount_enabled', 'discount_percentage'
        ]
        widgets = {
            'days_threshold': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'min': '1'
            }),
            'minimum_quantity_threshold': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0'
            }),
            'auto_discount_enabled': forms.CheckboxInput(attrs={
                'class': 'form-checkbox mt-1 focus:ring-blue-500 focus:border-blue-500'
            }),
            'discount_percentage': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
        }
        labels = {
            'days_threshold': _('عدد الأيام للكشف عن الركود'),
            'minimum_quantity_threshold': _('الحد الأدنى للكمية'),
            'auto_discount_enabled': _('تفعيل التخفيض التلقائي'),
            'discount_percentage': _('نسبة التخفيض (%)'),
        }
    
    def clean_discount_percentage(self):
        discount_percentage = self.cleaned_data.get('discount_percentage')
        auto_discount_enabled = self.cleaned_data.get('auto_discount_enabled')
        
        if auto_discount_enabled and not discount_percentage:
            raise ValidationError(_('نسبة التخفيض مطلوبة عند تفعيل التخفيض التلقائي'))
        
        if discount_percentage and discount_percentage > 100:
            raise ValidationError(_('نسبة التخفيض لا يمكن أن تكون أكبر من 100%'))
        
        return discount_percentage 