# Inventory Demo Data Population Scripts

This directory contains Django management commands for populating the inventory system with demo data.

## Egyptian Market Data Population

The `populate_egyptian_data.py` script creates sample inventory data for the Egyptian market, including:

- Units of measurement with Arabic names
- Item classifications in Arabic
- Movement types in Arabic
- Vehicle makes and models popular in Egypt
- Sample Hyundai Elantra 2007 vehicles (matching the screenshot)
- Inventory items and parts for these vehicles
- Sample movements and operations

### Usage

Run the command with:

```bash
python manage.py populate_egyptian_data --tenant=YOUR_TENANT_ID
```

Optional parameters:
- `--tenant`: Specify a tenant ID (if not provided, a random UUID will be generated)
- `--clear`: Clear existing data for the tenant before populating

### Data Created

1. **Vehicle Makes/Models**: Hyundai, Toyota, Kia, Chevrolet, BMW, Mercedes with popular models
2. **Vehicles**: Four Hyundai Elantra 2007 vehicles with plates "شبكة1321" as shown in the screenshot
3. **Parts**: Various parts specifically for Hyundai Elantra
4. **Consumables**: Engine oil, coolant, brake fluid
5. **Operations**: Oil change, brake service, tune-up with pricing
6. **Movements**: Purchase records for initial stock and some sample sales/adjustments

### Example

```bash
# Create demo data with a specific tenant ID
python manage.py populate_egyptian_data --tenant=550e8400-e29b-41d4-a716-************

# Create demo data and clear existing data first
python manage.py populate_egyptian_data --tenant=550e8400-e29b-41d4-a716-************ --clear
```