import random
from decimal import Decimal
from datetime import date, timedelta
from django.core.management.base import BaseCommand
from django.db import transaction
from inventory.models import Item, PartPricing
from setup.models import VehicleMake, VehicleModel, Franchise, Company, ServiceCenter


class Command(BaseCommand):
    help = 'Creates simple test part pricing data with location-specific prices'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            required=True,
            help='Tenant ID to use for the pricing data',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=20,
            help='Number of pricing entries to create',
        )
        parser.add_argument(
            '--with-locations',
            action='store_true',
            help='Include location-specific pricing',
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant')
        count = options.get('count')
        with_locations = options.get('with_locations', False)
        
        self.stdout.write(f"Creating {count} test part pricing entries for tenant {tenant_id}")
        
        # Get necessary data
        items = list(Item.objects.filter(tenant_id=tenant_id))
        makes = list(VehicleMake.objects.all())
        
        if not items:
            self.stdout.write(self.style.ERROR('No items found for the specified tenant'))
            return
            
        if not makes:
            self.stdout.write(self.style.ERROR('No vehicle makes found'))
            return
        
        # Get location data if requested
        locations = None
        if with_locations:
            franchises = list(Franchise.objects.all())
            companies = list(Company.objects.all())
            service_centers = list(ServiceCenter.objects.filter(tenant_id=tenant_id))
            
            if not franchises and not companies and not service_centers:
                self.stdout.write(self.style.WARNING('No location data found. Proceeding without location-specific pricing.'))
                with_locations = False
            else:
                locations = {
                    'franchises': franchises,
                    'companies': companies,
                    'service_centers': service_centers
                }
                self.stdout.write(f"Found {len(franchises)} franchises, {len(companies)} companies, and {len(service_centers)} service centers")
            
        self.stdout.write(f"Found {len(items)} items and {len(makes)} vehicle makes")
        
        # Create part pricing data
        created = 0
        
        try:
            with transaction.atomic():
                # 1. Basic part pricing without vehicle specifics
                for i in range(min(count // 3, len(items))):
                    item = items[i]
                    price = item.unit_price if item.unit_price else Decimal(str(random.randint(10, 500)))
                    
                    part_pricing = PartPricing.objects.create(
                        tenant_id=tenant_id,
                        item=item,
                        price=price,
                        is_special_pricing=False,
                        is_active=True,
                        notes=f"Standard pricing for {item.name}"
                    )
                    created += 1
                    self.stdout.write(f"Created standard pricing for {item.name}: {price}")
                
                # 2. Vehicle-specific pricing
                for i in range(min(count // 3, len(items))):
                    item = random.choice(items)
                    make = random.choice(makes)
                    
                    # Try to get a model for this make
                    models = list(VehicleModel.objects.filter(make=make))
                    model = random.choice(models) if models and random.random() < 0.7 else None
                    
                    # Calculate price with a vehicle premium
                    base_price = item.unit_price if item.unit_price else Decimal(str(random.randint(10, 500)))
                    price = base_price * Decimal('1.2')  # 20% premium for vehicle-specific
                    
                    # Special pricing for some entries
                    is_special = random.random() < 0.3
                    valid_from = None
                    valid_to = None
                    
                    if is_special:
                        # Special pricing typically has date constraints
                        today = date.today()
                        valid_from = today - timedelta(days=random.randint(0, 30))
                        valid_to = today + timedelta(days=random.randint(30, 90))
                        price = base_price * Decimal('0.8')  # 20% discount for special pricing
                    
                    # Create some pricing variations
                    pricing_type = random.choice(["Standard", "Premium", "Discount", "Wholesale", "Retail"])
                    
                    part_pricing = PartPricing.objects.create(
                        tenant_id=tenant_id,
                        item=item,
                        vehicle_make=make,
                        vehicle_model=model,
                        price=price.quantize(Decimal('0.01')),
                        is_special_pricing=is_special,
                        valid_from=valid_from,
                        valid_to=valid_to,
                        is_active=True,
                        notes=f"{pricing_type} {'special' if is_special else 'vehicle-specific'} pricing for {item.name} on {make.name} {model.name if model else ''}"
                    )
                    created += 1
                    self.stdout.write(f"Created {pricing_type} vehicle pricing for {item.name} on {make.name}: {price}")
                    
                    # Add some discount variations for the same item/vehicle
                    if random.random() < 0.3:  # 30% chance to add discount variations
                        discount_percentages = [5, 10, 15, 20, 25, 30]
                        discount_pct = random.choice(discount_percentages)
                        discount_price = base_price * (1 - Decimal(str(discount_pct/100)))
                        
                        # Set discount validity period
                        today = date.today()
                        discount_valid_from = today
                        discount_valid_to = today + timedelta(days=random.randint(7, 60))
                        
                        part_pricing = PartPricing.objects.create(
                            tenant_id=tenant_id,
                            item=item,
                            vehicle_make=make,
                            vehicle_model=model,
                            price=discount_price.quantize(Decimal('0.01')),
                            is_special_pricing=True,
                            valid_from=discount_valid_from,
                            valid_to=discount_valid_to,
                            is_active=True,
                            notes=f"{discount_pct}% discount special offer for {item.name} on {make.name} {model.name if model else ''}"
                        )
                        created += 1
                        self.stdout.write(f"Created {discount_pct}% discount pricing for {item.name} on {make.name}: {discount_price}")
                
                # 3. Location-specific pricing if requested
                if with_locations and locations:
                    remaining = count - created
                    
                    # Create pricing at each location level
                    self.stdout.write("Creating location-specific pricing entries...")
                    
                    # Franchise level pricing
                    if locations['franchises']:
                        for i in range(min(remaining // 3, len(locations['franchises']))):
                            franchise = random.choice(locations['franchises'])
                            item = random.choice(items)
                            
                            # Franchise pricing is typically standardized across a region
                            base_price = item.unit_price if item.unit_price else Decimal(str(random.randint(10, 500)))
                            
                            # Franchise pricing can be with or without vehicle specifics
                            if random.random() < 0.5:
                                # General pricing for the franchise
                                price = base_price * Decimal('1.05')  # 5% premium for franchise-branded pricing
                                
                                part_pricing = PartPricing.objects.create(
                                    tenant_id=tenant_id,
                                    item=item,
                                    franchise=franchise,
                                    price=price.quantize(Decimal('0.01')),
                                    is_special_pricing=False,
                                    is_active=True,
                                    notes=f"Franchise standard pricing for {item.name} at {franchise.name}"
                                )
                                created += 1
                                self.stdout.write(f"Created franchise pricing for {item.name} at {franchise.name}: {price}")
                            else:
                                # Vehicle-specific pricing at franchise level
                                make = random.choice(makes)
                                price = base_price * Decimal('1.1')  # 10% premium for franchise+vehicle pricing
                                
                                part_pricing = PartPricing.objects.create(
                                    tenant_id=tenant_id,
                                    item=item,
                                    franchise=franchise,
                                    vehicle_make=make,
                                    price=price.quantize(Decimal('0.01')),
                                    is_special_pricing=False,
                                    is_active=True,
                                    notes=f"Franchise vehicle-specific pricing for {item.name} on {make.name} at {franchise.name}"
                                )
                                created += 1
                                self.stdout.write(f"Created franchise vehicle pricing for {item.name} on {make.name} at {franchise.name}: {price}")
                    
                    # Company level pricing
                    if locations['companies']:
                        for i in range(min(remaining // 3, len(locations['companies']))):
                            company = random.choice(locations['companies'])
                            item = random.choice(items)
                            
                            # Company pricing typically has a different structure than the franchise
                            base_price = item.unit_price if item.unit_price else Decimal(str(random.randint(10, 500)))
                            
                            # Special pricing for some entries
                            is_special = random.random() < 0.4  # 40% chance of special pricing at company level
                            valid_from = None
                            valid_to = None
                            
                            if is_special:
                                # Special company promotions
                                today = date.today()
                                valid_from = today
                                valid_to = today + timedelta(days=random.randint(14, 90))
                                price = base_price * Decimal('0.85')  # 15% discount for company promotions
                                
                                part_pricing = PartPricing.objects.create(
                                    tenant_id=tenant_id,
                                    item=item,
                                    company=company,
                                    price=price.quantize(Decimal('0.01')),
                                    is_special_pricing=True,
                                    valid_from=valid_from,
                                    valid_to=valid_to,
                                    is_active=True,
                                    notes=f"Company promotional pricing for {item.name} at {company.name}"
                                )
                                created += 1
                                self.stdout.write(f"Created company promo pricing for {item.name} at {company.name}: {price}")
                            else:
                                # Standard company pricing
                                price = base_price * Decimal('1.15')  # 15% premium for company-branded pricing
                                
                                part_pricing = PartPricing.objects.create(
                                    tenant_id=tenant_id,
                                    item=item,
                                    company=company,
                                    price=price.quantize(Decimal('0.01')),
                                    is_special_pricing=False,
                                    is_active=True,
                                    notes=f"Company standard pricing for {item.name} at {company.name}"
                                )
                                created += 1
                                self.stdout.write(f"Created company pricing for {item.name} at {company.name}: {price}")
                    
                    # Service center level pricing
                    if locations['service_centers']:
                        for i in range(min(remaining // 3, len(locations['service_centers']))):
                            service_center = random.choice(locations['service_centers'])
                            item = random.choice(items)
                            
                            # Service centers may have location-specific pricing based on demand
                            base_price = item.unit_price if item.unit_price else Decimal(str(random.randint(10, 500)))
                            
                            # Service centers have different pricing strategies
                            pricing_strategy = random.choice(["Standard", "Premium", "Economy", "Local", "Rush"])
                            
                            if pricing_strategy == "Premium":
                                price = base_price * Decimal('1.25')  # 25% premium for premium service centers
                            elif pricing_strategy == "Economy":
                                price = base_price * Decimal('0.9')  # 10% discount for economy service centers
                            elif pricing_strategy == "Local":
                                price = base_price * Decimal('1.05')  # 5% premium for local service centers
                            elif pricing_strategy == "Rush":
                                price = base_price * Decimal('1.35')  # 35% premium for rush service
                            else:  # Standard
                                price = base_price * Decimal('1.1')  # 10% premium for standard service centers
                            
                            part_pricing = PartPricing.objects.create(
                                tenant_id=tenant_id,
                                item=item,
                                service_center=service_center,
                                price=price.quantize(Decimal('0.01')),
                                is_special_pricing=(pricing_strategy in ["Economy", "Rush"]),
                                is_active=True,
                                notes=f"{pricing_strategy} service center pricing for {item.name} at {service_center.name}"
                            )
                            created += 1
                            self.stdout.write(f"Created {pricing_strategy} service center pricing for {item.name} at {service_center.name}: {price}")
                            
                            # Add some vehicle-specific pricing at service center level
                            if random.random() < 0.3:  # 30% chance
                                make = random.choice(makes)
                                models = list(VehicleModel.objects.filter(make=make))
                                model = random.choice(models) if models and random.random() < 0.7 else None
                                
                                # Service center with vehicle-specific pricing
                                sc_vehicle_price = base_price * Decimal('1.3')  # 30% premium for specialized service
                                
                                part_pricing = PartPricing.objects.create(
                                    tenant_id=tenant_id,
                                    item=item,
                                    service_center=service_center,
                                    vehicle_make=make,
                                    vehicle_model=model,
                                    price=sc_vehicle_price.quantize(Decimal('0.01')),
                                    is_special_pricing=False,
                                    is_active=True,
                                    notes=f"Service center vehicle-specific pricing for {item.name} on {make.name} {model.name if model else ''} at {service_center.name}"
                                )
                                created += 1
                                self.stdout.write(f"Created service center vehicle pricing for {item.name} on {make.name} at {service_center.name}: {sc_vehicle_price}")
        
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating pricing data: {e}"))
            return
        
        self.stdout.write(self.style.SUCCESS(f"Successfully created {created} part pricing entries"))
        
        # Verify entries were created
        part_count = PartPricing.objects.filter(tenant_id=tenant_id).count()
        
        self.stdout.write(self.style.SUCCESS(f"Database now has {part_count} part pricing entries for tenant {tenant_id}")) 