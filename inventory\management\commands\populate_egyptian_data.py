import random
import uuid
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from inventory.models import (
    Item, Movement, ItemDocument, UnitOfMeasurement, UnitConversion, 
    MovementType, VehicleCompatibility, OperationCompatibility, 
    ItemClassification, VehicleOperationCompatibility, VehicleModelPart, 
    OperationPricing, PartPricing
)
from setup.models import (
    VehicleMake, VehicleModel, Vehicle, Customer, ServiceCenter
)
from work_orders.models import WorkOrderType, MaintenanceSchedule

class Command(BaseCommand):
    help = 'Populate database with demo data for Egyptian market'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Tenant ID to use for creating data',
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating',
        )
    
    def handle(self, *args, **options):
        tenant_id = options.get('tenant')
        if not tenant_id:
            tenant_id = uuid.uuid4()
            self.stdout.write(f"No tenant ID provided, using: {tenant_id}")
        else:
            try:
                tenant_id = uuid.UUID(tenant_id)
            except ValueError:
                self.stdout.write(self.style.ERROR(f"Invalid tenant ID: {tenant_id}"))
                return
        
        clear_data = options.get('clear', False)
        
        if clear_data:
            self.clear_data(tenant_id)
        
        with transaction.atomic():
            self.create_units(tenant_id)
            self.create_classifications(tenant_id)
            self.create_movement_types(tenant_id)
            
            # Create vehicle makes and models (Egyptian market)
            makes_models = self.create_vehicle_makes_models(tenant_id)
            
            # Create vehicles (based on screenshot data)
            vehicles = self.create_vehicles(tenant_id, makes_models)
            
            # Create inventory items and parts
            self.create_inventory_items(tenant_id, makes_models)
            
            # Create movement data
            self.create_movements(tenant_id)
            
            # Create operation data
            self.create_operations(tenant_id, makes_models)
        
        self.stdout.write(self.style.SUCCESS(f"Successfully populated demo data for tenant: {tenant_id}"))
    
    def clear_data(self, tenant_id):
        """Clear existing data for the specified tenant"""
        self.stdout.write(f"Clearing existing data for tenant: {tenant_id}")
        
        Item.objects.filter(tenant_id=tenant_id).delete()
        Movement.objects.filter(tenant_id=tenant_id).delete()
        ItemDocument.objects.filter(tenant_id=tenant_id).delete()
        UnitOfMeasurement.objects.filter(tenant_id=tenant_id).delete()
        UnitConversion.objects.filter(tenant_id=tenant_id).delete()
        MovementType.objects.filter(tenant_id=tenant_id).delete()
        VehicleCompatibility.objects.filter(tenant_id=tenant_id).delete()
        OperationCompatibility.objects.filter(tenant_id=tenant_id).delete()
        ItemClassification.objects.filter(tenant_id=tenant_id).delete()
        VehicleOperationCompatibility.objects.filter(tenant_id=tenant_id).delete()
        VehicleModelPart.objects.filter(tenant_id=tenant_id).delete()
        OperationPricing.objects.filter(tenant_id=tenant_id).delete()
        PartPricing.objects.filter(tenant_id=tenant_id).delete()
        
        # Clear related models
        VehicleMake.objects.filter(tenant_id=tenant_id).delete()
        VehicleModel.objects.filter(tenant_id=tenant_id).delete()
        Vehicle.objects.filter(tenant_id=tenant_id).delete()
    
    def create_units(self, tenant_id):
        """Create units of measurement"""
        self.stdout.write("Creating units of measurement...")
        
        # Create base units
        piece = UnitOfMeasurement.objects.create(
            tenant_id=tenant_id,
            name="قطعة",
            symbol="قطعة",
            description="Standard unit for individual parts",
            is_base_unit=True
        )
        
        liter = UnitOfMeasurement.objects.create(
            tenant_id=tenant_id,
            name="لتر",
            symbol="ل",
            description="Standard unit for liquids",
            is_base_unit=True
        )
        
        kilogram = UnitOfMeasurement.objects.create(
            tenant_id=tenant_id,
            name="كيلوجرام",
            symbol="كجم",
            description="Standard unit for weight",
            is_base_unit=True
        )
        
        # Create derived units
        gallon = UnitOfMeasurement.objects.create(
            tenant_id=tenant_id,
            name="جالون",
            symbol="جالون",
            description="Imperial gallon",
            is_base_unit=False
        )
        
        # Create conversions
        UnitConversion.objects.create(
            tenant_id=tenant_id,
            from_unit=gallon,
            to_unit=liter,
            conversion_factor=4.54609
        )
        
        UnitConversion.objects.create(
            tenant_id=tenant_id,
            from_unit=liter,
            to_unit=gallon,
            conversion_factor=0.219969
        )
        
        return {
            'piece': piece,
            'liter': liter,
            'kilogram': kilogram,
            'gallon': gallon
        }
    
    def create_classifications(self, tenant_id):
        """Create item classifications"""
        self.stdout.write("Creating item classifications...")
        
        # Create top-level classifications
        parts = ItemClassification.objects.create(
            tenant_id=tenant_id,
            name="قطع غيار",
            code="PARTS",
            description="قطع غيار السيارات",
            level=0,
            is_active=True
        )
        
        consumables = ItemClassification.objects.create(
            tenant_id=tenant_id,
            name="مواد استهلاكية",
            code="CONSUMABLES",
            description="المواد الاستهلاكية للسيارات",
            level=0,
            is_active=True
        )
        
        tools = ItemClassification.objects.create(
            tenant_id=tenant_id,
            name="أدوات",
            code="TOOLS",
            description="أدوات إصلاح وصيانة",
            level=0,
            is_active=True
        )
        
        # Create sub-classifications for parts
        engine_parts = ItemClassification.objects.create(
            tenant_id=tenant_id,
            name="قطع غيار المحرك",
            code="ENGINE",
            description="قطع غيار المحرك",
            parent=parts,
            level=1,
            is_active=True
        )
        
        transmission_parts = ItemClassification.objects.create(
            tenant_id=tenant_id,
            name="قطع غيار ناقل الحركة",
            code="TRANSMISSION",
            description="قطع غيار ناقل الحركة",
            parent=parts,
            level=1,
            is_active=True
        )
        
        brake_parts = ItemClassification.objects.create(
            tenant_id=tenant_id,
            name="قطع غيار الفرامل",
            code="BRAKES",
            description="قطع غيار الفرامل",
            parent=parts,
            level=1,
            is_active=True
        )
        
        # Create sub-classifications for consumables
        oils = ItemClassification.objects.create(
            tenant_id=tenant_id,
            name="زيوت",
            code="OILS",
            description="زيوت المحرك وناقل الحركة",
            parent=consumables,
            level=1,
            is_active=True
        )
        
        fluids = ItemClassification.objects.create(
            tenant_id=tenant_id,
            name="سوائل",
            code="FLUIDS",
            description="سوائل أخرى (فرامل، تبريد، إلخ)",
            parent=consumables,
            level=1,
            is_active=True
        )
        
        return {
            'parts': parts,
            'consumables': consumables,
            'tools': tools,
            'engine_parts': engine_parts,
            'transmission_parts': transmission_parts,
            'brake_parts': brake_parts,
            'oils': oils,
            'fluids': fluids
        }
    
    def create_movement_types(self, tenant_id):
        """Create movement types"""
        self.stdout.write("Creating movement types...")
        
        purchase = MovementType.objects.create(
            tenant_id=tenant_id,
            code="PURCHASE",
            name="شراء",
            description="شراء مخزون",
            is_inbound=True,
            is_outbound=False,
            color="#28a745",
            is_active=True,
            requires_reference=True,
            sequence=10
        )
        
        sale = MovementType.objects.create(
            tenant_id=tenant_id,
            code="SALE",
            name="بيع",
            description="بيع مخزون",
            is_inbound=False,
            is_outbound=True,
            color="#dc3545",
            is_active=True,
            requires_reference=True,
            sequence=20
        )
        
        adjustment = MovementType.objects.create(
            tenant_id=tenant_id,
            code="ADJUSTMENT",
            name="تعديل",
            description="تعديل المخزون",
            is_inbound=True,
            is_outbound=True,
            color="#ffc107",
            is_active=True,
            requires_reference=False,
            sequence=30
        )
        
        transfer = MovementType.objects.create(
            tenant_id=tenant_id,
            code="TRANSFER",
            name="نقل",
            description="نقل المخزون بين المواقع",
            is_inbound=True,
            is_outbound=True,
            color="#17a2b8",
            is_active=True,
            requires_reference=True,
            sequence=40
        )
        
        return {
            'purchase': purchase,
            'sale': sale,
            'adjustment': adjustment,
            'transfer': transfer
        }
    
    def create_vehicle_makes_models(self, tenant_id):
        """Create vehicle makes and models for Egyptian market"""
        self.stdout.write("Creating vehicle makes and models...")
        
        makes_models = {}
        
        # Create makes
        hyundai = VehicleMake.objects.create(
            tenant_id=tenant_id,
            name="هيونداي",
            code="HYUNDAI",
            country_of_origin="كوريا الجنوبية",
            is_active=True
        )
        
        toyota = VehicleMake.objects.create(
            tenant_id=tenant_id,
            name="تويوتا",
            code="TOYOTA",
            country_of_origin="اليابان",
            is_active=True
        )
        
        kia = VehicleMake.objects.create(
            tenant_id=tenant_id,
            name="كيا",
            code="KIA",
            country_of_origin="كوريا الجنوبية",
            is_active=True
        )
        
        chevrolet = VehicleMake.objects.create(
            tenant_id=tenant_id,
            name="شيفروليه",
            code="CHEVROLET",
            country_of_origin="الولايات المتحدة",
            is_active=True
        )
        
        bmw = VehicleMake.objects.create(
            tenant_id=tenant_id,
            name="بي إم دبليو",
            code="BMW",
            country_of_origin="ألمانيا",
            is_active=True
        )
        
        mercedes = VehicleMake.objects.create(
            tenant_id=tenant_id,
            name="مرسيدس",
            code="MERCEDES",
            country_of_origin="ألمانيا",
            is_active=True
        )
        
        # Create models for Hyundai
        elantra = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=hyundai,
            name="إلنترا",
            code="ELANTRA",
            is_active=True
        )
        
        accent = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=hyundai,
            name="أكسنت",
            code="ACCENT",
            is_active=True
        )
        
        tucson = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=hyundai,
            name="توسان",
            code="TUCSON",
            is_active=True
        )
        
        # Create models for Toyota
        corolla = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=toyota,
            name="كورولا",
            code="COROLLA",
            is_active=True
        )
        
        camry = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=toyota,
            name="كامري",
            code="CAMRY",
            is_active=True
        )
        
        land_cruiser = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=toyota,
            name="لاند كروزر",
            code="LAND_CRUISER",
            is_active=True
        )
        
        # Create models for Kia
        cerato = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=kia,
            name="سيراتو",
            code="CERATO",
            is_active=True
        )
        
        sportage = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=kia,
            name="سبورتاج",
            code="SPORTAGE",
            is_active=True
        )
        
        # Create models for Chevrolet
        cruze = VehicleModel.objects.create(
            tenant_id=tenant_id,
            make=chevrolet,
            name="كروز",
            code="CRUZE",
            is_active=True
        )
        
        makes_models['hyundai'] = {
            'make': hyundai,
            'models': {
                'elantra': elantra,
                'accent': accent,
                'tucson': tucson
            }
        }
        
        makes_models['toyota'] = {
            'make': toyota,
            'models': {
                'corolla': corolla,
                'camry': camry,
                'land_cruiser': land_cruiser
            }
        }
        
        makes_models['kia'] = {
            'make': kia,
            'models': {
                'cerato': cerato,
                'sportage': sportage
            }
        }
        
        makes_models['chevrolet'] = {
            'make': chevrolet,
            'models': {
                'cruze': cruze
            }
        }
        
        makes_models['bmw'] = {
            'make': bmw,
            'models': {}
        }
        
        makes_models['mercedes'] = {
            'make': mercedes,
            'models': {}
        }
        
        return makes_models
    
    def create_vehicles(self, tenant_id, makes_models):
        """Create vehicle records based on screenshot data"""
        self.stdout.write("Creating vehicle records...")
        
        # Create a test customer
        customer = Customer.objects.create(
            tenant_id=tenant_id,
            first_name="بكر",
            last_name="نصر",
            phone="0123456789",
            email="<EMAIL>",
            is_active=True
        )
        
        # Create service center
        service_center = ServiceCenter.objects.create(
            tenant_id=tenant_id,
            name="مركز الخدمة الرئيسي",
            code="MAIN_SC",
            is_active=True
        )
        
        # Create vehicles based on screenshot data (Hyundai Elantra 2007)
        vehicles = []
        
        # Create four Hyundai Elantra vehicles from the screenshot
        for i in range(4):
            if i < 2:
                vin_value = "asd46578"
            elif i == 2:
                vin_value = "asda87874789789asd"
            else:
                vin_value = "ش54ج6س4ش4ج65ي64"
                
            vehicle = Vehicle.objects.create(
                tenant_id=tenant_id,
                make=makes_models['hyundai']['make'],
                model=makes_models['hyundai']['models']['elantra'],
                year=2007,
                license_plate="شبكة1321",
                vin=vin_value,
                customer=customer,
                service_center=service_center,
                is_active=True
            )
            vehicles.append(vehicle)
        
        return vehicles
    
    def create_inventory_items(self, tenant_id, makes_models):
        """Create inventory items and parts"""
        self.stdout.write("Creating inventory items and parts...")
        
        # Get units
        units = UnitOfMeasurement.objects.filter(tenant_id=tenant_id)
        unit_piece = next((u for u in units if u.symbol == "قطعة"), None)
        unit_liter = next((u for u in units if u.symbol == "ل"), None)
        
        # Get classifications
        classifications = ItemClassification.objects.filter(tenant_id=tenant_id)
        engine_parts = next((c for c in classifications if c.code == "ENGINE"), None)
        brake_parts = next((c for c in classifications if c.code == "BRAKES"), None)
        oils = next((c for c in classifications if c.code == "OILS"), None)
        fluids = next((c for c in classifications if c.code == "FLUIDS"), None)
        
        # Create common parts for Hyundai Elantra
        elantra_parts = [
            {
                'name': 'فلتر زيت هيونداي إلنترا',
                'sku': 'HYU-EL-OIL-FILTER',
                'description': 'فلتر زيت أصلي لسيارة هيونداي إلنترا',
                'quantity': 25,
                'unit_price': 150.00,
                'min_stock_level': 5,
                'category': 'part',
                'classification': engine_parts,
                'unit': unit_piece
            },
            {
                'name': 'فلتر هواء هيونداي إلنترا',
                'sku': 'HYU-EL-AIR-FILTER',
                'description': 'فلتر هواء أصلي لسيارة هيونداي إلنترا',
                'quantity': 20,
                'unit_price': 120.00,
                'min_stock_level': 5,
                'category': 'part',
                'classification': engine_parts,
                'unit': unit_piece
            },
            {
                'name': 'بوجيهات هيونداي إلنترا',
                'sku': 'HYU-EL-SPARK-PLUGS',
                'description': 'طقم بوجيهات أصلي لسيارة هيونداي إلنترا',
                'quantity': 15,
                'unit_price': 350.00,
                'min_stock_level': 3,
                'category': 'part',
                'classification': engine_parts,
                'unit': unit_piece
            },
            {
                'name': 'تيل فرامل أمامي هيونداي إلنترا',
                'sku': 'HYU-EL-BRAKE-PADS-F',
                'description': 'تيل فرامل أمامي أصلي لسيارة هيونداي إلنترا',
                'quantity': 10,
                'unit_price': 500.00,
                'min_stock_level': 2,
                'category': 'part',
                'classification': brake_parts,
                'unit': unit_piece
            },
            {
                'name': 'تيل فرامل خلفي هيونداي إلنترا',
                'sku': 'HYU-EL-BRAKE-PADS-R',
                'description': 'تيل فرامل خلفي أصلي لسيارة هيونداي إلنترا',
                'quantity': 10,
                'unit_price': 450.00,
                'min_stock_level': 2,
                'category': 'part',
                'classification': brake_parts,
                'unit': unit_piece
            },
            {
                'name': 'زيت محرك 5W-30',
                'sku': 'OIL-5W30-1L',
                'description': 'زيت محرك عالي الجودة 5W-30',
                'quantity': 50,
                'unit_price': 120.00,
                'min_stock_level': 10,
                'category': 'consumable',
                'classification': oils,
                'unit': unit_liter
            },
            {
                'name': 'سائل تبريد المحرك',
                'sku': 'COOLANT-1L',
                'description': 'سائل تبريد المحرك عالي الجودة',
                'quantity': 30,
                'unit_price': 80.00,
                'min_stock_level': 5,
                'category': 'consumable',
                'classification': fluids,
                'unit': unit_liter
            },
            {
                'name': 'سائل فرامل DOT 4',
                'sku': 'BRAKE-FLUID-DOT4',
                'description': 'سائل فرامل DOT 4 عالي الجودة',
                'quantity': 20,
                'unit_price': 90.00,
                'min_stock_level': 5,
                'category': 'consumable',
                'classification': fluids,
                'unit': unit_liter
            }
        ]
        
        created_items = []
        
        # Create items
        for part_data in elantra_parts:
            item = Item.objects.create(
                tenant_id=tenant_id,
                name=part_data['name'],
                sku=part_data['sku'],
                description=part_data['description'],
                quantity=part_data['quantity'],
                unit_of_measurement=part_data['unit'],
                unit_price=part_data['unit_price'],
                min_stock_level=part_data['min_stock_level'],
                category=part_data['category'],
                classification=part_data['classification']
            )
            
            # Create vehicle compatibility for parts
            if part_data['category'] == 'part' and not part_data['sku'].startswith('OIL') and not part_data['sku'].startswith('COOLANT') and not part_data['sku'].startswith('BRAKE-FLUID'):
                VehicleCompatibility.objects.create(
                    tenant_id=tenant_id,
                    item=item,
                    make="هيونداي",
                    model="إلنترا",
                    year_from=2006,
                    year_to=2010
                )
            
            created_items.append(item)
        
        return created_items
    
    def create_movements(self, tenant_id):
        """Create inventory movements"""
        self.stdout.write("Creating inventory movements...")
        
        # Get movement types
        movement_types = MovementType.objects.filter(tenant_id=tenant_id)
        movement_type_dict = {mt.code: mt for mt in movement_types}
        
        # Get items
        items = Item.objects.filter(tenant_id=tenant_id)
        
        # Create purchase movements for initial stock
        for item in items:
            Movement.objects.create(
                tenant_id=tenant_id,
                item=item,
                quantity=item.quantity,
                unit_of_measurement=item.unit_of_measurement,
                movement_type_ref=movement_type_dict['PURCHASE'],
                reference="PO-INITIAL",
                notes="المخزون الأولي"
            )
        
        # Create some random movements
        for _ in range(10):
            item = random.choice(list(items))
            movement_type = random.choice([
                movement_type_dict['SALE'],
                movement_type_dict['ADJUSTMENT']
            ])
            
            quantity = random.randint(1, 5)
            if movement_type.code == 'SALE':
                quantity = -quantity  # Negative for sales
            
            Movement.objects.create(
                tenant_id=tenant_id,
                item=item,
                quantity=quantity,
                unit_of_measurement=item.unit_of_measurement,
                movement_type_ref=movement_type,
                reference=f"REF-{random.randint(1000, 9999)}",
                notes="حركة مخزون تجريبية"
            )
    
    def create_operations(self, tenant_id, makes_models):
        """Create operation data"""
        self.stdout.write("Creating operation data...")
        
        # Create work order types
        oil_change = WorkOrderType.objects.create(
            tenant_id=tenant_id,
            name="تغيير زيت",
            code="OIL_CHANGE",
            is_active=True
        )
        
        brake_service = WorkOrderType.objects.create(
            tenant_id=tenant_id,
            name="صيانة الفرامل",
            code="BRAKE_SERVICE",
            is_active=True
        )
        
        tune_up = WorkOrderType.objects.create(
            tenant_id=tenant_id,
            name="ضبط المحرك",
            code="TUNE_UP",
            is_active=True
        )
        
        # Create maintenance schedules
        regular_service = MaintenanceSchedule.objects.create(
            tenant_id=tenant_id,
            name="صيانة دورية",
            code="REGULAR",
            interval_type="mileage",
            interval_value=10000,
            is_active=True
        )
        
        # Get items
        oil_filter = Item.objects.filter(tenant_id=tenant_id, sku='HYU-EL-OIL-FILTER').first()
        engine_oil = Item.objects.filter(tenant_id=tenant_id, sku='OIL-5W30-1L').first()
        brake_pads_front = Item.objects.filter(tenant_id=tenant_id, sku='HYU-EL-BRAKE-PADS-F').first()
        brake_pads_rear = Item.objects.filter(tenant_id=tenant_id, sku='HYU-EL-BRAKE-PADS-R').first()
        brake_fluid = Item.objects.filter(tenant_id=tenant_id, sku='BRAKE-FLUID-DOT4').first()
        spark_plugs = Item.objects.filter(tenant_id=tenant_id, sku='HYU-EL-SPARK-PLUGS').first()
        air_filter = Item.objects.filter(tenant_id=tenant_id, sku='HYU-EL-AIR-FILTER').first()
        
        # Create operation compatibilities
        if oil_filter and engine_oil:
            oil_change_op = OperationCompatibility.objects.create(
                tenant_id=tenant_id,
                item=oil_filter,
                operation_type=oil_change,
                is_required=True,
                is_common=True,
                maintenance_schedule=regular_service,
                operation_description='oil_change',
                duration_minutes=30
            )
            
            # Add vehicle compatibility
            VehicleOperationCompatibility.objects.create(
                tenant_id=tenant_id,
                operation_compatibility=oil_change_op,
                vehicle_make=makes_models['hyundai']['make'],
                vehicle_model=makes_models['hyundai']['models']['elantra'],
                year_from=2006,
                year_to=2010,
                duration_minutes=30
            )
            
            # Create pricing
            OperationPricing.objects.create(
                tenant_id=tenant_id,
                operation_type=oil_change,
                vehicle_make=makes_models['hyundai']['make'],
                vehicle_model=makes_models['hyundai']['models']['elantra'],
                year_from=2006,
                year_to=2010,
                base_price=200.00,
                labor_hours=0.5,
                labor_rate=100.00,
                is_active=True
            )
            
            # Create part pricing
            PartPricing.objects.create(
                tenant_id=tenant_id,
                item=oil_filter,
                operation_type=oil_change,
                vehicle_make=makes_models['hyundai']['make'],
                vehicle_model=makes_models['hyundai']['models']['elantra'],
                price=150.00,
                is_active=True
            )
            
            PartPricing.objects.create(
                tenant_id=tenant_id,
                item=engine_oil,
                operation_type=oil_change,
                vehicle_make=makes_models['hyundai']['make'],
                vehicle_model=makes_models['hyundai']['models']['elantra'],
                price=120.00,
                is_active=True
            )
        
        if brake_pads_front and brake_fluid:
            brake_service_op = OperationCompatibility.objects.create(
                tenant_id=tenant_id,
                item=brake_pads_front,
                operation_type=brake_service,
                is_required=True,
                is_common=True,
                maintenance_schedule=regular_service,
                operation_description='brake_service',
                duration_minutes=60
            )
            
            # Add vehicle compatibility
            VehicleOperationCompatibility.objects.create(
                tenant_id=tenant_id,
                operation_compatibility=brake_service_op,
                vehicle_make=makes_models['hyundai']['make'],
                vehicle_model=makes_models['hyundai']['models']['elantra'],
                year_from=2006,
                year_to=2010,
                duration_minutes=60
            )
            
            # Create pricing
            OperationPricing.objects.create(
                tenant_id=tenant_id,
                operation_type=brake_service,
                vehicle_make=makes_models['hyundai']['make'],
                vehicle_model=makes_models['hyundai']['models']['elantra'],
                year_from=2006,
                year_to=2010,
                base_price=400.00,
                labor_hours=1.0,
                labor_rate=100.00,
                is_active=True
            )
        
        if spark_plugs and air_filter:
            tune_up_op = OperationCompatibility.objects.create(
                tenant_id=tenant_id,
                item=spark_plugs,
                operation_type=tune_up,
                is_required=True,
                is_common=True,
                maintenance_schedule=regular_service,
                operation_description='spark_plugs',
                duration_minutes=90
            )
            
            # Add vehicle compatibility
            VehicleOperationCompatibility.objects.create(
                tenant_id=tenant_id,
                operation_compatibility=tune_up_op,
                vehicle_make=makes_models['hyundai']['make'],
                vehicle_model=makes_models['hyundai']['models']['elantra'],
                year_from=2006,
                year_to=2010,
                duration_minutes=90
            )
            
            # Create pricing
            OperationPricing.objects.create(
                tenant_id=tenant_id,
                operation_type=tune_up,
                vehicle_make=makes_models['hyundai']['make'],
                vehicle_model=makes_models['hyundai']['models']['elantra'],
                year_from=2006,
                year_to=2010,
                base_price=600.00,
                labor_hours=1.5,
                labor_rate=100.00,
                is_active=True
            )