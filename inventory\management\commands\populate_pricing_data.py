import random
import traceback
from decimal import Decimal
from datetime import date, timedelta
from django.core.management.base import BaseCommand
from django.db import transaction
from inventory.models import Item, OperationPricing, PartPricing
from setup.models import VehicleMake, VehicleModel
from work_orders.models import WorkOrderType


class Command(BaseCommand):
    help = 'Populates operation and part pricing demo data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            type=str,
            help='Tenant ID to use for the pricing data',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=100,
            help='Number of part pricing entries to create (default: 100)',
        )
        parser.add_argument(
            '--operations',
            type=int,
            default=50,
            help='Number of operation pricing entries to create (default: 50)',
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing pricing data before adding new entries',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Display verbose output',
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant')
        part_count = options.get('count')
        operation_count = options.get('operations')
        clear_existing = options.get('clear')
        verbose = options.get('verbose', False)

        try:
            self.stdout.write(self.style.SUCCESS(f"Starting pricing data population with tenant ID: {tenant_id}"))

            if not tenant_id:
                self.stdout.write(self.style.ERROR('Please provide a tenant ID'))
                return

            # Clear existing data if requested
            if clear_existing:
                self.stdout.write(self.style.WARNING('Clearing existing pricing data...'))
                part_deleted = PartPricing.objects.filter(tenant_id=tenant_id).delete()
                op_deleted = OperationPricing.objects.filter(tenant_id=tenant_id).delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {part_deleted[0]} part pricing entries and {op_deleted[0]} operation pricing entries'))

            # Get necessary data
            items = list(Item.objects.filter(tenant_id=tenant_id))
            self.stdout.write(self.style.SUCCESS(f'Found {len(items)} items for tenant {tenant_id}'))
            
            operation_types = list(WorkOrderType.objects.filter(tenant_id=tenant_id))
            self.stdout.write(self.style.SUCCESS(f'Found {len(operation_types)} operation types for tenant {tenant_id}'))
            
            makes = list(VehicleMake.objects.all())
            self.stdout.write(self.style.SUCCESS(f'Found {len(makes)} vehicle makes'))

            if not items:
                self.stdout.write(self.style.ERROR('No items found for the specified tenant'))
                return

            if not operation_types:
                self.stdout.write(self.style.ERROR('No operation types found for the specified tenant'))
                return

            if not makes:
                self.stdout.write(self.style.ERROR('No vehicle makes found'))
                return

            if verbose:
                self.stdout.write(self.style.SUCCESS(f'Sample items: {", ".join([item.name for item in items[:5]])}'))
                self.stdout.write(self.style.SUCCESS(f'Sample operation types: {", ".join([op.name for op in operation_types[:5]])}'))
                self.stdout.write(self.style.SUCCESS(f'Sample makes: {", ".join([make.name for make in makes[:5]])}'))

            try:
                with transaction.atomic():
                    # Create operation pricing data
                    self.create_operation_pricing(tenant_id, operation_types, makes, operation_count, verbose)
                    
                    # Create part pricing data
                    self.create_part_pricing(tenant_id, items, operation_types, makes, part_count, verbose)
                
                self.stdout.write(self.style.SUCCESS(f'Successfully created part pricing and operation pricing entries'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error during transaction: {e}'))
                self.stdout.write(self.style.ERROR(traceback.format_exc()))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Unexpected error: {e}'))
            self.stdout.write(self.style.ERROR(traceback.format_exc()))

    def create_operation_pricing(self, tenant_id, operation_types, makes, count, verbose=False):
        """Create operation pricing demo data"""
        self.stdout.write('Creating operation pricing entries...')
        created = 0
        
        # Base prices for different operation types
        operation_base_prices = {
            'oil_change': (25, 50),
            'brake_service': (100, 200),
            'transmission_service': (150, 300),
            'coolant_flush': (50, 100),
            'tire_rotation': (20, 40),
            'wheel_alignment': (60, 120),
            'ac_service': (80, 150),
            'diagnostics': (40, 80),
            'other': (30, 150)
        }
        
        # Labor rates by vehicle make premium level
        labor_rates = {
            'economy': Decimal('30.00'),      # For brands like Kia, Hyundai
            'standard': Decimal('45.00'),     # For brands like Ford, Toyota
            'premium': Decimal('65.00'),      # For brands like BMW, Mercedes
            'luxury': Decimal('85.00')        # For brands like Porsche, Ferrari
        }
        
        # Categorize makes by premium level
        premium_levels = {}
        for make in makes:
            name = make.name.lower()
            if name in ['bmw', 'mercedes', 'audi', 'lexus', 'infiniti', 'acura']:
                premium_levels[make.id] = 'premium'
            elif name in ['porsche', 'ferrari', 'lamborghini', 'bentley', 'rolls royce', 'maserati']:
                premium_levels[make.id] = 'luxury'
            elif name in ['kia', 'hyundai', 'suzuki', 'dacia', 'lada', 'seat', 'mitsubishi']:
                premium_levels[make.id] = 'economy'
            else:
                premium_levels[make.id] = 'standard'
        
        # Create entries for each operation type
        for op_type in operation_types:
            if verbose:
                self.stdout.write(f"Creating pricing for operation type: {op_type.name}")
            
            # Get models for some makes
            for make in random.sample(makes, min(len(makes), 5)):
                if verbose:
                    self.stdout.write(f"  Working on make: {make.name}")
                
                try:
                    models = list(VehicleModel.objects.filter(make=make))
                    
                    # No models for this make, create pricing for the make only
                    if not models:
                        if verbose:
                            self.stdout.write(f"    No models found, creating make-only pricing")
                        
                        self.create_single_operation_pricing(
                            tenant_id, op_type, make, None, premium_levels, operation_base_prices, labor_rates
                        )
                        created += 1
                    else:
                        # Create some pricing for specific models
                        for model in random.sample(models, min(len(models), 3)):
                            if verbose:
                                self.stdout.write(f"    Creating pricing for model: {model.name}")
                            
                            self.create_single_operation_pricing(
                                tenant_id, op_type, make, model, premium_levels, operation_base_prices, labor_rates
                            )
                            created += 1
                    
                    # Also create some make-wide pricing (without model)
                    if verbose:
                        self.stdout.write(f"    Creating make-wide pricing")
                    
                    self.create_single_operation_pricing(
                        tenant_id, op_type, make, None, premium_levels, operation_base_prices, labor_rates
                    )
                    created += 1
                    
                    if created >= count:
                        break
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Error creating pricing for make {make.name}: {e}"))
            
            if created >= count:
                break
        
        self.stdout.write(self.style.SUCCESS(f'Created {created} operation pricing entries'))
        
        # Verify entries were created
        db_count = OperationPricing.objects.filter(tenant_id=tenant_id).count()
        self.stdout.write(self.style.SUCCESS(f'Database now has {db_count} operation pricing entries for this tenant'))

    def create_single_operation_pricing(self, tenant_id, op_type, make, model, premium_levels, operation_base_prices, labor_rates):
        """Create a single operation pricing entry"""
        # Determine operation category for base price
        op_category = 'other'
        op_name = op_type.name.lower()
        for category in operation_base_prices.keys():
            if category in op_name:
                op_category = category
                break
        
        # Get base price range and premium level
        base_min, base_max = operation_base_prices.get(op_category, (30, 150))
        premium_level = premium_levels.get(make.id, 'standard')
        
        # Adjust price range based on premium level
        if premium_level == 'premium':
            base_min = int(base_min * 1.3)
            base_max = int(base_max * 1.3)
        elif premium_level == 'luxury':
            base_min = int(base_min * 1.8)
            base_max = int(base_max * 1.8)
        elif premium_level == 'economy':
            base_min = int(base_min * 0.8)
            base_max = int(base_max * 0.8)
        
        # Generate pricing data
        base_price = Decimal(str(random.randint(base_min, base_max)))
        labor_hours = Decimal(str(random.randint(5, 25) / 10))  # 0.5 to 2.5 hours
        labor_rate = labor_rates.get(premium_level, Decimal('45.00'))
        
        # Add year range for some entries
        year_from = None
        year_to = None
        if random.random() < 0.7:  # 70% chance of having year constraints
            current_year = date.today().year
            year_from = random.randint(current_year - 15, current_year - 5)
            if random.random() < 0.5:  # 50% chance of having an end year
                year_to = random.randint(year_from + 3, current_year)
        
        # Create the pricing entry
        OperationPricing.objects.create(
            tenant_id=tenant_id,
            operation_type=op_type,
            vehicle_make=make,
            vehicle_model=model,
            year_from=year_from,
            year_to=year_to,
            base_price=base_price,
            labor_hours=labor_hours,
            labor_rate=labor_rate,
            is_active=True,
            notes=f"Demo pricing for {op_type.name} on {make.name} {model.name if model else 'all models'}"
        )

    def create_part_pricing(self, tenant_id, items, operation_types, makes, count, verbose=False):
        """Create part pricing demo data"""
        self.stdout.write('Creating part pricing entries...')
        created = 0
        
        # Categorize items by type
        item_types = {}
        for item in items:
            if not item.category:
                item_types[item.id] = 'other'
            else:
                item_types[item.id] = item.category
        
        # Price multipliers for special pricing
        special_price_multipliers = {
            'discount': Decimal('0.8'),     # 20% off
            'promotion': Decimal('0.85'),   # 15% off
            'clearance': Decimal('0.7'),    # 30% off
            'premium': Decimal('1.2'),      # 20% premium
        }
        
        # Create different pricing strategies
        today = date.today()
        
        try:
            # First, create standard pricing for all items (without vehicle or operation specifics)
            if verbose:
                self.stdout.write(f"Creating standard pricing for items")
                
            for item in items[:min(len(items), count // 3)]:
                if verbose:
                    self.stdout.write(f"  Creating standard pricing for item: {item.name}")
                    
                price = item.unit_price if item.unit_price else Decimal(str(random.randint(10, 500)))
                
                PartPricing.objects.create(
                    tenant_id=tenant_id,
                    item=item,
                    price=price,
                    is_special_pricing=False,
                    is_active=True,
                    notes=f"Standard pricing for {item.name}"
                )
                created += 1
            
            # Next, create operation-specific pricing
            if verbose:
                self.stdout.write(f"Creating operation-specific pricing")
                
            for item in random.sample(items, min(len(items), count // 3)):
                if verbose:
                    self.stdout.write(f"  Working on item: {item.name}")
                    
                # Find relevant operations for this item type
                relevant_ops = self.get_relevant_operations(item_types.get(item.id, 'other'), operation_types)
                if not relevant_ops:
                    relevant_ops = random.sample(operation_types, min(len(operation_types), 2))
                
                for op_type in relevant_ops:
                    if verbose:
                        self.stdout.write(f"    Creating pricing for operation: {op_type.name}")
                        
                    base_price = item.unit_price if item.unit_price else Decimal(str(random.randint(10, 500)))
                    # Operation-specific pricing is usually higher (bundled service)
                    price = base_price * Decimal('1.1')
                    
                    PartPricing.objects.create(
                        tenant_id=tenant_id,
                        item=item,
                        operation_type=op_type,
                        price=price,
                        is_special_pricing=False,
                        is_active=True,
                        notes=f"Operation-specific pricing for {item.name} when used in {op_type.name}"
                    )
                    created += 1
                    
                    if created >= count:
                        break
            
            # Finally, create vehicle and operation specific pricing
            remaining = count - created
            if remaining > 0:
                if verbose:
                    self.stdout.write(f"Creating vehicle and operation specific pricing, {remaining} entries remaining")
                    
                for i in range(min(remaining, len(items) * 2)):
                    item = random.choice(items)
                    make = random.choice(makes)
                    
                    if verbose and i % 10 == 0:  # Only log every 10th item to avoid excessive output
                        self.stdout.write(f"  Working on item: {item.name}, make: {make.name}")
                        
                    models = list(VehicleModel.objects.filter(make=make))
                    model = random.choice(models) if models and random.random() < 0.7 else None
                    op_type = random.choice(operation_types) if random.random() < 0.5 else None
                    
                    # Determine if this is special pricing
                    is_special = random.random() < 0.3  # 30% chance of special pricing
                    special_type = None
                    valid_from = None
                    valid_to = None
                    
                    if is_special:
                        special_types = list(special_price_multipliers.keys())
                        special_type = random.choice(special_types)
                        
                        # Set validity period for special pricing
                        if random.random() < 0.7:  # 70% chance of having date constraints
                            valid_from = today - timedelta(days=random.randint(0, 30))
                            valid_to = today + timedelta(days=random.randint(30, 90))
                    
                    # Calculate price
                    base_price = item.unit_price if item.unit_price else Decimal(str(random.randint(10, 500)))
                    if is_special and special_type:
                        price = base_price * special_price_multipliers.get(special_type, Decimal('1.0'))
                    else:
                        # Slight variation in normal pricing
                        price = base_price * Decimal(str(random.uniform(0.95, 1.15)))
                    
                    notes = f"{'Special' if is_special else 'Standard'} pricing for {item.name}"
                    if special_type:
                        notes += f" ({special_type})"
                    if make:
                        notes += f" for {make.name}"
                        if model:
                            notes += f" {model.name}"
                    if op_type:
                        notes += f" during {op_type.name}"
                    
                    PartPricing.objects.create(
                        tenant_id=tenant_id,
                        item=item,
                        operation_type=op_type,
                        vehicle_make=make,
                        vehicle_model=model,
                        price=price.quantize(Decimal('0.01')),
                        is_special_pricing=is_special,
                        valid_from=valid_from,
                        valid_to=valid_to,
                        is_active=True,
                        notes=notes
                    )
                    created += 1
                
            self.stdout.write(self.style.SUCCESS(f'Created {created} part pricing entries'))
            
            # Verify entries were created
            db_count = PartPricing.objects.filter(tenant_id=tenant_id).count()
            self.stdout.write(self.style.SUCCESS(f'Database now has {db_count} part pricing entries for this tenant'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error in create_part_pricing: {e}"))
            self.stdout.write(self.style.ERROR(traceback.format_exc()))

    def get_relevant_operations(self, item_type, operation_types):
        """Get operations relevant to a specific item type"""
        relevant_ops = []
        for op in operation_types:
            op_name = op.name.lower()
            
            if item_type == 'part':
                if any(term in op_name for term in ['repair', 'replace', 'install']):
                    relevant_ops.append(op)
            elif item_type == 'consumable':
                if any(term in op_name for term in ['oil', 'fluid', 'change', 'replace']):
                    relevant_ops.append(op)
            elif item_type == 'tool':
                if any(term in op_name for term in ['repair', 'diagnose']):
                    relevant_ops.append(op)
            elif item_type == 'material':
                if any(term in op_name for term in ['body', 'paint', 'finish', 'repair']):
                    relevant_ops.append(op)
                    
        return relevant_ops 