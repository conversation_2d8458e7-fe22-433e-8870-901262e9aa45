# Generated by Django 4.2.20 on 2025-05-07 09:45

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Item',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('sku', models.CharField(db_index=True, max_length=100, verbose_name='SKU')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Unit Price')),
                ('min_stock_level', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Minimum Stock Level')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Attributes')),
            ],
            options={
                'verbose_name': 'Item',
                'verbose_name_plural': 'Items',
                'unique_together': {('tenant_id', 'sku')},
            },
        ),
        migrations.CreateModel(
            name='Movement',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Quantity')),
                ('movement_type', models.CharField(choices=[('purchase', 'Purchase'), ('sale', 'Sale'), ('adjustment', 'Adjustment'), ('transfer', 'Transfer'), ('return', 'Return')], max_length=20, verbose_name='Movement Type')),
                ('reference', models.CharField(blank=True, max_length=100, verbose_name='Reference')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.item')),
            ],
            options={
                'verbose_name': 'Movement',
                'verbose_name_plural': 'Movements',
                'ordering': ['-created_at'],
            },
        ),
    ]
