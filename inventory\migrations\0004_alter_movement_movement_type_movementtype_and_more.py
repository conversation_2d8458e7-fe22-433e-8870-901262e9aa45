# Generated by Django 4.2.20 on 2025-05-08 09:08

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_unitofmeasurement_item_unit_of_measurement_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='movement',
            name='movement_type',
            field=models.CharField(blank=True, choices=[('purchase', 'Purchase'), ('sale', 'Sale'), ('adjustment', 'Adjustment'), ('transfer', 'Transfer'), ('return', 'Return')], max_length=20, null=True, verbose_name='Legacy Movement Type'),
        ),
        migrations.CreateModel(
            name='MovementType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('code', models.CharField(db_index=True, help_text='Unique code for this movement type', max_length=50, verbose_name='Code')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_inbound', models.BooleanField(help_text='Does this movement type increase stock?', verbose_name='Is Inbound')),
                ('is_outbound', models.BooleanField(help_text='Does this movement type decrease stock?', verbose_name='Is Outbound')),
                ('icon', models.CharField(blank=True, help_text='Icon name for UI display', max_length=50, verbose_name='Icon')),
                ('color', models.CharField(blank=True, help_text='Color code for UI display', max_length=20, verbose_name='Color')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('requires_reference', models.BooleanField(default=False, help_text='Is a reference document required?', verbose_name='Requires Reference')),
                ('requires_approval', models.BooleanField(default=False, help_text='Does this type of movement require approval?', verbose_name='Requires Approval')),
                ('sequence', models.PositiveIntegerField(default=10, help_text='Display order in UI', verbose_name='Sequence')),
            ],
            options={
                'verbose_name': 'Movement Type',
                'verbose_name_plural': 'Movement Types',
                'ordering': ['sequence', 'name'],
                'unique_together': {('tenant_id', 'code')},
            },
        ),
        migrations.AddField(
            model_name='movement',
            name='movement_type_ref',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='movements', to='inventory.movementtype', verbose_name='Movement Type'),
        ),
    ]
