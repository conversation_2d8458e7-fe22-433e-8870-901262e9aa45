# Generated by Django 4.2.20 on 2025-05-08 09:51

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('work_orders', '0001_initial'),
        ('inventory', '0004_alter_movement_movement_type_movementtype_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='item',
            name='category',
            field=models.CharField(blank=True, choices=[('part', 'Part'), ('consumable', 'Consumable'), ('tool', 'Tool'), ('equipment', 'Equipment'), ('material', 'Material'), ('finished_good', 'Finished Good')], max_length=50, verbose_name='Category'),
        ),
        migrations.AddField(
            model_name='item',
            name='item_type',
            field=models.CharField(blank=True, help_text='Specific type within category', max_length=100, verbose_name='Item Type'),
        ),
        migrations.CreateModel(
            name='VehicleCompatibility',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('make', models.CharField(db_index=True, max_length=100, verbose_name='Vehicle Make')),
                ('model', models.CharField(db_index=True, max_length=100, verbose_name='Vehicle Model')),
                ('year_from', models.PositiveIntegerField(db_index=True, verbose_name='Year From')),
                ('year_to', models.PositiveIntegerField(blank=True, db_index=True, help_text='Leave blank if still compatible with current models', null=True, verbose_name='Year To')),
                ('variant', models.CharField(blank=True, help_text='Specific variant or trim level if applicable', max_length=100, verbose_name='Variant/Trim')),
                ('engine', models.CharField(blank=True, help_text='Engine type or code if applicable', max_length=100, verbose_name='Engine')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicle_compatibilities', to='inventory.item', verbose_name='Item')),
            ],
            options={
                'verbose_name': 'Vehicle Compatibility',
                'verbose_name_plural': 'Vehicle Compatibilities',
                'ordering': ['make', 'model', 'year_from'],
                'unique_together': {('tenant_id', 'item', 'make', 'model', 'year_from', 'variant', 'engine')},
            },
        ),
        migrations.CreateModel(
            name='OperationCompatibility',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('is_required', models.BooleanField(default=False, help_text='Is this item required for this operation type?', verbose_name='Required')),
                ('is_common', models.BooleanField(default=False, help_text='Is this item commonly used for this operation type?', verbose_name='Commonly Used')),
                ('default_quantity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Default Quantity')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operation_compatibilities', to='inventory.item', verbose_name='Item')),
                ('operation_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compatible_items', to='work_orders.workordertype', verbose_name='Operation Type')),
            ],
            options={
                'verbose_name': 'Operation Compatibility',
                'verbose_name_plural': 'Operation Compatibilities',
                'ordering': ['operation_type__name', 'item__name'],
                'unique_together': {('tenant_id', 'item', 'operation_type')},
            },
        ),
    ]
