# Generated by Django 4.2.20 on 2025-05-19 16:06

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("work_orders", "0004_workorder_customer"),
        ("inventory", "0007_operationcompatibility_duration_minutes_and_more"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="operationcompatibility",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="operationcompatibility",
            name="engine_displacement",
            field=models.CharField(
                blank=True,
                help_text="Engine size (e.g., 2.0L, 1.6L)",
                max_length=50,
                verbose_name="Engine Displacement",
            ),
        ),
        migrations.AddField(
            model_name="operationcompatibility",
            name="engine_type",
            field=models.CharField(
                blank=True,
                help_text="Compatible engine type or code",
                max_length=100,
                verbose_name="Engine Type",
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="operationcompatibility",
            name="maintenance_schedule",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="compatible_operations",
                to="work_orders.maintenanceschedule",
                verbose_name="Maintenance Schedule",
            ),
        ),
        migrations.AddField(
            model_name="operationcompatibility",
            name="vehicle_make",
            field=models.CharField(
                blank=True,
                null=True,
                help_text="Make of vehicle this part is compatible with",
                max_length=100,
                verbose_name="Vehicle Make",
            ),
        ),
        migrations.AddField(
            model_name="operationcompatibility",
            name="vehicle_model",
            field=models.CharField(
                blank=True,
                null=True,
                help_text="Model of vehicle this part is compatible with",
                max_length=100,
                verbose_name="Vehicle Model",
            ),
        ),
        migrations.AddField(
            model_name="operationcompatibility",
            name="year_from",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Starting year of vehicle compatibility",
                null=True,
                verbose_name="Year From",
            ),
        ),
        migrations.AddField(
            model_name="operationcompatibility",
            name="year_to",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Ending year of vehicle compatibility",
                null=True,
                verbose_name="Year To",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="operationcompatibility",
            unique_together={
                (
                    "tenant_id",
                    "item",
                    "operation_type",
                    "vehicle_make",
                    "vehicle_model",
                    "engine_type",
                )
            },
        ),
        migrations.CreateModel(
            name="VehicleModelPart",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(db_index=True, verbose_name="Tenant ID"),
                ),
                (
                    "make",
                    models.CharField(
                        db_index=True, max_length=100, verbose_name="Vehicle Make"
                    ),
                ),
                (
                    "model",
                    models.CharField(
                        db_index=True, max_length=100, verbose_name="Vehicle Model"
                    ),
                ),
                (
                    "year_from",
                    models.PositiveIntegerField(
                        db_index=True, verbose_name="Year From"
                    ),
                ),
                (
                    "year_to",
                    models.PositiveIntegerField(
                        blank=True, db_index=True, null=True, verbose_name="Year To"
                    ),
                ),
                (
                    "engine_type",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Engine Type"
                    ),
                ),
                (
                    "engine_displacement",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Engine Displacement"
                    ),
                ),
                (
                    "transmission_type",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Transmission Type"
                    ),
                ),
                (
                    "is_oem",
                    models.BooleanField(
                        default=False,
                        help_text="Is this an Original Equipment Manufacturer part?",
                        verbose_name="Is OEM Part",
                    ),
                ),
                (
                    "fits_position",
                    models.CharField(
                        blank=True,
                        help_text="Position on vehicle (e.g., front, rear, left, right)",
                        max_length=50,
                        verbose_name="Position",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "attributes",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Custom Attributes"
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vehicle_models",
                        to="inventory.item",
                        verbose_name="Part/Item",
                    ),
                ),
                (
                    "maintenance_schedule",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="vehicle_parts",
                        to="work_orders.maintenanceschedule",
                        verbose_name="Maintenance Schedule",
                    ),
                ),
            ],
            options={
                "verbose_name": "Vehicle Model Part",
                "verbose_name_plural": "Vehicle Model Parts",
                "ordering": ["make", "model", "year_from"],
                "unique_together": {
                    ("tenant_id", "make", "model", "year_from", "item", "engine_type")
                },
            },
        ),
    ]
