# Generated by Django 4.2.20 on 2025-05-20 09:00

from django.db import migrations, models
import django.db.models.deletion

def convert_empty_strings_to_none(apps, schema_editor):
    OperationCompatibility = apps.get_model('inventory', 'OperationCompatibility')
    db_alias = schema_editor.connection.alias
    # Ensure fields exist before trying to update them, as they are Char<PERSON>ields at this stage
    # In migration 0008 they were added as CharFields.
    operation_compatibilities = OperationCompatibility.objects.using(db_alias).all()
    for oc in operation_compatibilities:
        needs_save = False
        # Check if the attribute exists and is an empty string
        if hasattr(oc, 'vehicle_make') and oc.vehicle_make == '':
            oc.vehicle_make = None
            needs_save = True
        if hasattr(oc, 'vehicle_model') and oc.vehicle_model == '':
            oc.vehicle_model = None
            needs_save = True
        
        if needs_save:
            # Specify update_fields to avoid saving other potentially problematic fields
            # if the model's save() method has complex side effects.
            update_fields_list = []
            if hasattr(oc, 'vehicle_make'):
                update_fields_list.append('vehicle_make')
            if hasattr(oc, 'vehicle_model'):
                update_fields_list.append('vehicle_model')
            if update_fields_list: # only save if fields were actually updated
                 oc.save(update_fields=update_fields_list)

class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0012_populate_egyptian_vehicle_data"),
        ("work_orders", "0004_workorder_customer"),
        ("inventory", "0008_alter_operationcompatibility_unique_together_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="operationcompatibility",
            options={
                "verbose_name": "Operation Compatibility",
                "verbose_name_plural": "Operation Compatibilities",
            },
        ),
        migrations.AlterUniqueTogether(
            name="operationcompatibility",
            unique_together=set(),
        ),
        migrations.RunPython(convert_empty_strings_to_none, migrations.RunPython.noop),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="duration_minutes",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Estimated time in minutes this part adds or relates to for the operation",
                null=True,
                verbose_name="Estimated Duration (minutes)",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="operation_description",
            field=models.CharField(
                blank=True,
                help_text="Specific description for this item in context of the operation (e.g., 'Front brake pads')",
                max_length=255,
                verbose_name="Operation Description",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="vehicle_make",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_compatibilities",
                to="setup.vehiclemake",
                verbose_name="Vehicle Make",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="vehicle_model",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_compatibilities",
                to="setup.vehiclemodel",
                verbose_name="Vehicle Model",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="year_from",
            field=models.PositiveIntegerField(
                blank=True,
                db_index=True,
                help_text="Vehicle model year from which this compatibility applies",
                null=True,
                verbose_name="Year From",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="year_to",
            field=models.PositiveIntegerField(
                blank=True,
                db_index=True,
                help_text="Vehicle model year up to which this compatibility applies (inclusive)",
                null=True,
                verbose_name="Year To",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="operationcompatibility",
            unique_together={
                (
                    "tenant_id",
                    "item",
                    "operation_type",
                    "maintenance_schedule",
                    "vehicle_make",
                    "vehicle_model",
                )
            },
        ),
    ]
