# Generated by Django 4.2.20 on 2025-05-20 11:35

from django.db import migrations


def migrate_vehicle_compatibilities(apps, schema_editor):
    """
    Migrate existing OperationCompatibility vehicle data to the new VehicleOperationCompatibility model
    """
    OperationCompatibility = apps.get_model('inventory', 'OperationCompatibility')
    VehicleOperationCompatibility = apps.get_model('inventory', 'VehicleOperationCompatibility')
    
    db_alias = schema_editor.connection.alias
    migrated_count = 0
    
    # Get all operation compatibilities with vehicle information
    for compat in OperationCompatibility.objects.using(db_alias).filter(vehicle_make__isnull=False):
        # Check if a similar vehicle compatibility already exists
        existing = VehicleOperationCompatibility.objects.using(db_alias).filter(
            operation_compatibility=compat,
            vehicle_make=compat.vehicle_make,
            vehicle_model=compat.vehicle_model,
            year_from=compat.year_from,
            year_to=compat.year_to
        ).first()
        
        if not existing:
            # Create new vehicle compatibility link
            VehicleOperationCompatibility.objects.using(db_alias).create(
                operation_compatibility=compat,
                vehicle_make=compat.vehicle_make,
                vehicle_model=compat.vehicle_model,
                year_from=compat.year_from,
                year_to=compat.year_to
            )
            migrated_count += 1
    
    print(f"Migrated {migrated_count} operation compatibilities to the new structure")


def reverse_migrate_vehicle_compatibilities(apps, schema_editor):
    """Reverse operation is not needed as the original data remains intact"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0014_alter_operationcompatibility_options_and_more'),
    ]

    operations = [
        migrations.RunPython(
            migrate_vehicle_compatibilities,
            reverse_migrate_vehicle_compatibilities
        ),
    ]
