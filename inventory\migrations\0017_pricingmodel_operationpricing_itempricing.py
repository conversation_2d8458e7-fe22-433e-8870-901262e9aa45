# Generated by Django 4.2.20 on 2025-05-26 09:15

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("work_orders", "0004_workorder_customer"),
        ("inventory", "0016_vehicleoperationcompatibility_duration_minutes"),
    ]

    operations = [
        migrations.CreateModel(
            name="PricingModel",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(db_index=True, verbose_name="Tenant ID"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Name")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "pricing_type",
                    models.CharField(
                        choices=[
                            ("standard", "Standard"),
                            ("premium", "Premium"),
                            ("economy", "Economy"),
                            ("contract", "Contract"),
                            ("warranty", "Warranty"),
                            ("promotional", "Promotional"),
                        ],
                        default="standard",
                        max_length=20,
                        verbose_name="Pricing Type",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "valid_from",
                    models.DateField(blank=True, null=True, verbose_name="Valid From"),
                ),
                (
                    "valid_to",
                    models.DateField(blank=True, null=True, verbose_name="Valid To"),
                ),
                (
                    "attributes",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Custom Attributes"
                    ),
                ),
            ],
            options={
                "verbose_name": "Pricing Model",
                "verbose_name_plural": "Pricing Models",
                "ordering": ["-valid_from", "name"],
            },
        ),
        migrations.CreateModel(
            name="OperationPricing",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(db_index=True, verbose_name="Tenant ID"),
                ),
                (
                    "vehicle_make",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Vehicle Make"
                    ),
                ),
                (
                    "vehicle_model",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Vehicle Model"
                    ),
                ),
                (
                    "year_from",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Year From"
                    ),
                ),
                (
                    "year_to",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Year To"
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Base Price"
                    ),
                ),
                (
                    "labor_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Labor Cost",
                    ),
                ),
                (
                    "standard_time_minutes",
                    models.PositiveIntegerField(
                        default=60, verbose_name="Standard Time (minutes)"
                    ),
                ),
                (
                    "engine_type",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Engine Type"
                    ),
                ),
                (
                    "transmission_type",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Transmission Type"
                    ),
                ),
                (
                    "vehicle_category",
                    models.CharField(
                        blank=True,
                        help_text="e.g., sedan, SUV, truck",
                        max_length=50,
                        verbose_name="Vehicle Category",
                    ),
                ),
                (
                    "additional_costs",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="JSON object with additional cost items",
                        verbose_name="Additional Costs",
                    ),
                ),
                (
                    "is_discounted",
                    models.BooleanField(default=False, verbose_name="Is Discounted"),
                ),
                (
                    "discount_percent",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name="Discount Percentage",
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Discount Amount",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "operation_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prices",
                        to="work_orders.workordertype",
                        verbose_name="Operation Type",
                    ),
                ),
                (
                    "pricing_model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="operation_prices",
                        to="inventory.pricingmodel",
                        verbose_name="Pricing Model",
                    ),
                ),
            ],
            options={
                "verbose_name": "Operation Pricing",
                "verbose_name_plural": "Operation Pricing",
                "ordering": ["pricing_model", "operation_type__name"],
                "unique_together": {
                    (
                        "tenant_id",
                        "pricing_model",
                        "operation_type",
                        "vehicle_make",
                        "vehicle_model",
                        "engine_type",
                    )
                },
            },
        ),
        migrations.CreateModel(
            name="ItemPricing",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(db_index=True, verbose_name="Tenant ID"),
                ),
                (
                    "vehicle_make",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Vehicle Make"
                    ),
                ),
                (
                    "vehicle_model",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Vehicle Model"
                    ),
                ),
                (
                    "year_from",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Year From"
                    ),
                ),
                (
                    "year_to",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Year To"
                    ),
                ),
                (
                    "engine_type",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Engine Type"
                    ),
                ),
                (
                    "cost_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Cost Price"
                    ),
                ),
                (
                    "retail_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Retail Price"
                    ),
                ),
                (
                    "wholesale_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=15,
                        null=True,
                        verbose_name="Wholesale Price",
                    ),
                ),
                (
                    "is_discounted",
                    models.BooleanField(default=False, verbose_name="Is Discounted"),
                ),
                (
                    "discount_percent",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name="Discount Percentage",
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Discount Amount",
                    ),
                ),
                (
                    "min_quantity",
                    models.PositiveIntegerField(
                        default=1, verbose_name="Minimum Quantity"
                    ),
                ),
                (
                    "markup_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Percentage markup from cost price",
                        max_digits=8,
                        verbose_name="Markup Percentage",
                    ),
                ),
                (
                    "is_taxable",
                    models.BooleanField(default=True, verbose_name="Is Taxable"),
                ),
                (
                    "tax_rate",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name="Tax Rate",
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        default="SAR", max_length=3, verbose_name="Currency"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prices",
                        to="inventory.item",
                        verbose_name="Item/Part",
                    ),
                ),
                (
                    "pricing_model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="item_prices",
                        to="inventory.pricingmodel",
                        verbose_name="Pricing Model",
                    ),
                ),
            ],
            options={
                "verbose_name": "Item Pricing",
                "verbose_name_plural": "Item Pricing",
                "ordering": ["pricing_model", "item__name"],
                "unique_together": {
                    (
                        "tenant_id",
                        "pricing_model",
                        "item",
                        "vehicle_make",
                        "vehicle_model",
                        "min_quantity",
                    )
                },
            },
        ),
    ]
