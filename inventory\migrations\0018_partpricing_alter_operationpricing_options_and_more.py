# Generated by Django 4.2.20 on 2025-05-26 09:30

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0012_populate_egyptian_vehicle_data"),
        ("work_orders", "0004_workorder_customer"),
        ("inventory", "0017_pricingmodel_operationpricing_itempricing"),
    ]

    operations = [
        migrations.CreateModel(
            name="PartPricing",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(db_index=True, verbose_name="Tenant ID"),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Price"
                    ),
                ),
                (
                    "is_special_pricing",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates if this is a special price (discount/promotion)",
                        verbose_name="Special Pricing",
                    ),
                ),
                (
                    "valid_from",
                    models.DateField(
                        blank=True,
                        help_text="Start date for this pricing",
                        null=True,
                        verbose_name="Valid From",
                    ),
                ),
                (
                    "valid_to",
                    models.DateField(
                        blank=True,
                        help_text="End date for this pricing",
                        null=True,
                        verbose_name="Valid To",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="part_prices",
                        to="inventory.item",
                        verbose_name="Part/Item",
                    ),
                ),
                (
                    "operation_type",
                    models.ForeignKey(
                        blank=True,
                        help_text="Optional - specific operation type pricing",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="part_prices",
                        to="work_orders.workordertype",
                        verbose_name="Operation Type",
                    ),
                ),
                (
                    "vehicle_make",
                    models.ForeignKey(
                        blank=True,
                        help_text="Optional - specific vehicle make pricing",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="part_prices",
                        to="setup.vehiclemake",
                        verbose_name="Vehicle Make",
                    ),
                ),
                (
                    "vehicle_model",
                    models.ForeignKey(
                        blank=True,
                        help_text="Optional - specific vehicle model pricing",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="part_prices",
                        to="setup.vehiclemodel",
                        verbose_name="Vehicle Model",
                    ),
                ),
            ],
            options={
                "verbose_name": "Part Pricing",
                "verbose_name_plural": "Part Pricings",
                "ordering": ["item__name", "-valid_from"],
            },
        ),
        migrations.AlterModelOptions(
            name="operationpricing",
            options={
                "ordering": [
                    "operation_type__name",
                    "vehicle_make__name",
                    "vehicle_model__name",
                ],
                "verbose_name": "Operation Pricing",
                "verbose_name_plural": "Operation Pricings",
            },
        ),
        migrations.AlterUniqueTogether(
            name="operationpricing",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="operationpricing",
            name="is_active",
            field=models.BooleanField(default=True, verbose_name="Is Active"),
        ),
        migrations.AddField(
            model_name="operationpricing",
            name="labor_hours",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Estimated labor hours required",
                max_digits=8,
                verbose_name="Labor Hours",
            ),
        ),
        migrations.AddField(
            model_name="operationpricing",
            name="labor_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Hourly labor rate",
                max_digits=15,
                verbose_name="Labor Rate",
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="base_price",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Base price for this operation",
                max_digits=15,
                verbose_name="Base Price",
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="operation_type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_prices",
                to="work_orders.workordertype",
                verbose_name="Operation Type",
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="vehicle_make",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_prices",
                to="setup.vehiclemake",
                verbose_name="Vehicle Make",
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="vehicle_model",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_prices",
                to="setup.vehiclemodel",
                verbose_name="Vehicle Model",
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="year_from",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Start year for this pricing",
                null=True,
                verbose_name="Year From",
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="year_to",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="End year for this pricing (leave blank for current)",
                null=True,
                verbose_name="Year To",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="operationpricing",
            unique_together={
                (
                    "tenant_id",
                    "operation_type",
                    "vehicle_make",
                    "vehicle_model",
                    "year_from",
                    "year_to",
                )
            },
        ),
        migrations.DeleteModel(
            name="ItemPricing",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="additional_costs",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="discount_amount",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="discount_percent",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="engine_type",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="is_discounted",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="labor_cost",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="pricing_model",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="standard_time_minutes",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="transmission_type",
        ),
        migrations.RemoveField(
            model_name="operationpricing",
            name="vehicle_category",
        ),
        migrations.DeleteModel(
            name="PricingModel",
        ),
    ]
