# Generated by Django 4.2.20 on 2025-06-06 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inventory", "0022_remove_operationcompatibility_recommended_quantity"),
    ]

    operations = [
        migrations.AlterField(
            model_name="item",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="itemclassification",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="itemdocument",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="movement",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="movementtype",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="partpricing",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="unitconversion",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="unitofmeasurement",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="vehiclecompatibility",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="vehiclemodelpart",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="vehicleoperationcompatibility",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
    ]
