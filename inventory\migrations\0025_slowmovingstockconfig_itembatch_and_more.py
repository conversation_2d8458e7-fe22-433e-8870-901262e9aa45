# Generated by Django 4.2.20 on 2025-06-16 16:08

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("inventory", "0024_alter_item_tenant_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SlowMovingStockConfig",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "days_threshold",
                    models.PositiveIntegerField(
                        default=90,
                        help_text="Number of days without movement to consider as slow-moving",
                        verbose_name="Days Threshold",
                    ),
                ),
                (
                    "minimum_quantity_threshold",
                    models.DecimalField(
                        decimal_places=4,
                        default=0,
                        help_text="Minimum quantity to be considered for slow-moving analysis",
                        max_digits=15,
                        verbose_name="Minimum Quantity Threshold",
                    ),
                ),
                (
                    "auto_discount_enabled",
                    models.BooleanField(
                        default=False,
                        help_text="Automatically apply discount to slow-moving items",
                        verbose_name="Auto Discount Enabled",
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Discount percentage to apply",
                        max_digits=5,
                        verbose_name="Discount Percentage",
                    ),
                ),
                (
                    "notify_managers",
                    models.BooleanField(
                        default=True,
                        help_text="Send notifications to managers about slow-moving stock",
                        verbose_name="Notify Managers",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "classification",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="slow_moving_config",
                        to="inventory.itemclassification",
                        verbose_name="Classification",
                    ),
                ),
                (
                    "item",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="slow_moving_config",
                        to="inventory.item",
                        verbose_name="Item",
                    ),
                ),
            ],
            options={
                "verbose_name": "Slow Moving Stock Configuration",
                "verbose_name_plural": "Slow Moving Stock Configurations",
            },
        ),
        migrations.CreateModel(
            name="ItemBatch",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "batch_number",
                    models.CharField(
                        db_index=True,
                        help_text="Unique batch identifier",
                        max_length=100,
                        verbose_name="Batch Number",
                    ),
                ),
                (
                    "initial_quantity",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Original quantity received in this batch",
                        max_digits=15,
                        verbose_name="Initial Quantity",
                    ),
                ),
                (
                    "current_quantity",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Remaining quantity in this batch",
                        max_digits=15,
                        verbose_name="Current Quantity",
                    ),
                ),
                (
                    "reserved_quantity",
                    models.DecimalField(
                        decimal_places=4,
                        default=0,
                        help_text="Quantity reserved for pending orders",
                        max_digits=15,
                        verbose_name="Reserved Quantity",
                    ),
                ),
                (
                    "purchase_price",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Cost price per unit for this batch",
                        max_digits=15,
                        verbose_name="Purchase Price per Unit",
                    ),
                ),
                (
                    "selling_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        help_text="Suggested selling price per unit for this batch",
                        max_digits=15,
                        null=True,
                        verbose_name="Selling Price per Unit",
                    ),
                ),
                (
                    "manufactured_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when the item was manufactured",
                        null=True,
                        verbose_name="Manufactured Date",
                    ),
                ),
                (
                    "received_date",
                    models.DateField(
                        help_text="Date when the batch was received in inventory",
                        verbose_name="Received Date",
                    ),
                ),
                (
                    "expiry_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when the batch expires",
                        null=True,
                        verbose_name="Expiry Date",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("expired", "Expired"),
                            ("reserved", "Reserved"),
                            ("damaged", "Damaged"),
                            ("returned", "Returned"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "supplier_batch_ref",
                    models.CharField(
                        blank=True,
                        help_text="Supplier's batch reference number",
                        max_length=100,
                        verbose_name="Supplier Batch Reference",
                    ),
                ),
                (
                    "purchase_order_ref",
                    models.CharField(
                        blank=True,
                        help_text="Reference to purchase order",
                        max_length=100,
                        verbose_name="Purchase Order Reference",
                    ),
                ),
                (
                    "warehouse_location",
                    models.CharField(
                        blank=True,
                        help_text="Specific location in warehouse (shelf, bin, etc.)",
                        max_length=100,
                        verbose_name="Warehouse Location",
                    ),
                ),
                (
                    "attributes",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional batch-specific attributes",
                        verbose_name="Custom Attributes",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="batches",
                        to="inventory.item",
                        verbose_name="Item",
                    ),
                ),
            ],
            options={
                "verbose_name": "Item Batch",
                "verbose_name_plural": "Item Batches",
                "ordering": ["received_date", "expiry_date"],
            },
        ),
        migrations.CreateModel(
            name="InventoryValuationMethod",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "is_global_default",
                    models.BooleanField(
                        default=False,
                        help_text="Use as default for all items without specific method",
                        verbose_name="Global Default",
                    ),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[
                            ("fifo", "First In, First Out (FIFO)"),
                            ("lifo", "Last In, First Out (LIFO)"),
                            ("weighted_average", "Weighted Average"),
                            ("standard_cost", "Standard Cost"),
                        ],
                        default="fifo",
                        max_length=20,
                        verbose_name="Valuation Method",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "classification",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="valuation_method",
                        to="inventory.itemclassification",
                        verbose_name="Classification",
                    ),
                ),
                (
                    "item",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="valuation_method",
                        to="inventory.item",
                        verbose_name="Item",
                    ),
                ),
            ],
            options={
                "verbose_name": "Inventory Valuation Method",
                "verbose_name_plural": "Inventory Valuation Methods",
            },
        ),
        migrations.CreateModel(
            name="BatchMovement",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Quantity moved from this batch",
                        max_digits=15,
                        verbose_name="Quantity",
                    ),
                ),
                (
                    "movement_type",
                    models.CharField(
                        choices=[
                            ("inbound", "Inbound"),
                            ("outbound", "Outbound"),
                            ("transfer", "Transfer"),
                            ("adjustment", "Adjustment"),
                            ("reservation", "Reservation"),
                            ("release_reservation", "Release Reservation"),
                        ],
                        max_length=50,
                        verbose_name="Movement Type",
                    ),
                ),
                (
                    "reference_number",
                    models.CharField(
                        blank=True,
                        help_text="Order number, invoice number, etc.",
                        max_length=100,
                        verbose_name="Reference Number",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "batch",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="movements",
                        to="inventory.itembatch",
                        verbose_name="Batch",
                    ),
                ),
                (
                    "destination_batch",
                    models.ForeignKey(
                        blank=True,
                        help_text="For transfers between batches",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="source_movements",
                        to="inventory.itembatch",
                        verbose_name="Destination Batch",
                    ),
                ),
                (
                    "movement",
                    models.ForeignKey(
                        blank=True,
                        help_text="Link to general movement record",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="batch_movements",
                        to="inventory.movement",
                        verbose_name="Movement",
                    ),
                ),
            ],
            options={
                "verbose_name": "Batch Movement",
                "verbose_name_plural": "Batch Movements",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="itembatch",
            index=models.Index(
                fields=["tenant_id", "item", "status"],
                name="inventory_i_tenant__109f52_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="itembatch",
            index=models.Index(
                fields=["tenant_id", "expiry_date"],
                name="inventory_i_tenant__db592c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="itembatch",
            index=models.Index(
                fields=["tenant_id", "received_date"],
                name="inventory_i_tenant__2f12e9_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="itembatch",
            unique_together={("tenant_id", "item", "batch_number")},
        ),
    ]
