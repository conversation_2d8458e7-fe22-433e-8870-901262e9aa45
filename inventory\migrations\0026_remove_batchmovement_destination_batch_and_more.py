# Generated by Django 4.2.20 on 2025-06-21 09:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inventory", "0025_slowmovingstockconfig_itembatch_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="batchmovement",
            name="destination_batch",
        ),
        migrations.AddField(
            model_name="inventoryvaluationmethod",
            name="description",
            field=models.TextField(blank=True, verbose_name="Description"),
        ),
        migrations.AddField(
            model_name="inventoryvaluationmethod",
            name="is_default",
            field=models.BooleanField(default=False, verbose_name="Is Default"),
        ),
        migrations.AddField(
            model_name="inventoryvaluationmethod",
            name="method_name",
            field=models.CharField(
                choices=[
                    ("fifo", "First In, First Out (FIFO)"),
                    ("lifo", "Last In, First Out (LIFO)"),
                    ("average", "Weighted Average Cost"),
                    ("standard", "Standard Cost"),
                ],
                default="fifo",
                max_length=50,
                verbose_name="Method Name",
            ),
        ),
        migrations.AlterField(
            model_name="batchmovement",
            name="movement_type",
            field=models.CharField(
                choices=[
                    ("in", "Inbound"),
                    ("out", "Outbound"),
                    ("adjustment", "Adjustment"),
                    ("transfer", "Transfer"),
                    ("reservation", "Reservation"),
                    ("release", "Release Reservation"),
                ],
                max_length=50,
                verbose_name="Movement Type",
            ),
        ),
        migrations.AlterField(
            model_name="batchmovement",
            name="reference_number",
            field=models.CharField(
                blank=True,
                help_text="Reference number for this movement",
                max_length=100,
                verbose_name="Reference Number",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="current_quantity",
            field=models.DecimalField(
                decimal_places=4,
                default=0,
                help_text="Current available quantity in this batch",
                max_digits=15,
                verbose_name="Current Quantity",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="expiry_date",
            field=models.DateField(
                blank=True,
                help_text="Date when this batch expires",
                null=True,
                verbose_name="Expiry Date",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="manufactured_date",
            field=models.DateField(
                blank=True,
                help_text="Date when this batch was manufactured",
                null=True,
                verbose_name="Manufactured Date",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="purchase_order_ref",
            field=models.CharField(
                blank=True,
                help_text="Purchase order reference for this batch",
                max_length=100,
                verbose_name="Purchase Order Reference",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="purchase_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=4,
                help_text="Price paid for this batch",
                max_digits=15,
                null=True,
                verbose_name="Purchase Price",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="received_date",
            field=models.DateField(
                help_text="Date when this batch was received",
                verbose_name="Received Date",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="selling_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=4,
                help_text="Recommended selling price for this batch",
                max_digits=15,
                null=True,
                verbose_name="Selling Price",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("reserved", "Reserved"),
                    ("expired", "Expired"),
                    ("recalled", "Recalled"),
                    ("damaged", "Damaged"),
                ],
                default="active",
                max_length=20,
                verbose_name="Status",
            ),
        ),
        migrations.AlterField(
            model_name="itembatch",
            name="supplier_batch_ref",
            field=models.CharField(
                blank=True,
                help_text="Supplier's reference for this batch",
                max_length=100,
                verbose_name="Supplier Batch Reference",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="inventoryvaluationmethod",
            unique_together={("tenant_id", "method_name")},
        ),
        migrations.RemoveField(
            model_name="inventoryvaluationmethod",
            name="classification",
        ),
        migrations.RemoveField(
            model_name="inventoryvaluationmethod",
            name="is_global_default",
        ),
        migrations.RemoveField(
            model_name="inventoryvaluationmethod",
            name="item",
        ),
        migrations.RemoveField(
            model_name="inventoryvaluationmethod",
            name="method",
        ),
    ]
