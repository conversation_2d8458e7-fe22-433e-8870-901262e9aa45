# Generated by Django 4.2.20 on 2025-06-24 12:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inventory", "0026_remove_batchmovement_destination_batch_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="item",
            name="auto_generate_batch_number",
            field=models.BooleanField(
                default=False,
                help_text="Automatically generate batch numbers for this item",
                verbose_name="Auto Generate Batch Number",
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="batch_number_prefix",
            field=models.CharField(
                blank=True,
                help_text="Prefix for auto-generated batch numbers",
                max_length=10,
                verbose_name="Batch Number Prefix",
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="batch_tracking_level",
            field=models.CharField(
                choices=[
                    ("none", "No Batch Tracking"),
                    ("basic", "Basic Batch Tracking"),
                    ("full", "Full Batch Tracking with Expiry"),
                ],
                default="none",
                help_text="Level of batch tracking for this item",
                max_length=20,
                verbose_name="Batch Tracking Level",
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="default_shelf_life_days",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Default shelf life in days for new batches",
                null=True,
                verbose_name="Default Shelf Life (Days)",
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="inventory_method",
            field=models.CharField(
                choices=[
                    ("fifo", "First In, First Out (FIFO)"),
                    ("filo", "First In, Last Out (FILO/LIFO)"),
                    ("manual", "Manual Selection"),
                ],
                default="fifo",
                help_text="Method for selecting batches when items are consumed",
                max_length=20,
                verbose_name="Inventory Method",
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="requires_expiry_tracking",
            field=models.BooleanField(
                default=False,
                help_text="Whether this item requires expiry date tracking",
                verbose_name="Requires Expiry Tracking",
            ),
        ),
    ]
