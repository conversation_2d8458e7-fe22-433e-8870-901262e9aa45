# Generated by Django 4.2.20 on 2025-07-06 10:20

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0018_add_nationality_field"),
        ("work_orders", "0013_alter_workorder_status"),
        ("sales", "0004_salesorder_labor_cost_salesorder_parts_cost_and_more"),
        ("inventory", "0027_add_batch_tracking"),
    ]

    operations = [
        migrations.CreateModel(
            name="ServicePricing",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Descriptive name for this pricing configuration",
                        max_length=200,
                        verbose_name="Service Pricing Name",
                    ),
                ),
                (
                    "pricing_strategy",
                    models.CharField(
                        choices=[
                            ("standard", "Standard Pricing"),
                            ("premium", "Premium Service"),
                            ("economy", "Economy Service"),
                            ("express", "Express/Rush Service"),
                            ("warranty", "Warranty Work"),
                            ("insurance", "Insurance Claim"),
                            ("fleet", "Fleet Customer"),
                            ("vip", "VIP Customer"),
                        ],
                        default="standard",
                        max_length=20,
                        verbose_name="Pricing Strategy",
                    ),
                ),
                (
                    "year_from",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Start year for vehicle compatibility",
                        null=True,
                        verbose_name="Year From",
                    ),
                ),
                (
                    "year_to",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="End year for vehicle compatibility (leave blank for current)",
                        null=True,
                        verbose_name="Year To",
                    ),
                ),
                (
                    "base_service_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Base price for the service",
                        max_digits=15,
                        verbose_name="Base Service Price",
                    ),
                ),
                (
                    "labor_rate_per_hour",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Hourly labor rate for this service type",
                        max_digits=15,
                        verbose_name="Labor Rate per Hour",
                    ),
                ),
                (
                    "estimated_labor_hours",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Estimated labor hours required",
                        max_digits=8,
                        verbose_name="Estimated Labor Hours",
                    ),
                ),
                (
                    "parts_markup_percentage",
                    models.DecimalField(
                        decimal_places=4,
                        default=0,
                        help_text="Markup percentage applied to parts cost",
                        max_digits=8,
                        verbose_name="Parts Markup Percentage",
                    ),
                ),
                (
                    "parts_handling_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Fixed fee for parts handling and procurement",
                        max_digits=15,
                        verbose_name="Parts Handling Fee",
                    ),
                ),
                (
                    "complexity_multiplier",
                    models.DecimalField(
                        decimal_places=2,
                        default=1.0,
                        help_text="Multiplier for service complexity (1.0 = standard)",
                        max_digits=5,
                        verbose_name="Complexity Multiplier",
                    ),
                ),
                (
                    "urgency_multiplier",
                    models.DecimalField(
                        decimal_places=2,
                        default=1.0,
                        help_text="Multiplier for urgent/express service (1.0 = standard)",
                        max_digits=5,
                        verbose_name="Urgency Multiplier",
                    ),
                ),
                (
                    "customer_tier_discount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Discount percentage for customer tier (0-100)",
                        max_digits=5,
                        verbose_name="Customer Tier Discount %",
                    ),
                ),
                (
                    "valid_from",
                    models.DateField(
                        help_text="Start date for this pricing",
                        verbose_name="Valid From",
                    ),
                ),
                (
                    "valid_to",
                    models.DateField(
                        blank=True,
                        help_text="End date for this pricing (leave blank for indefinite)",
                        null=True,
                        verbose_name="Valid To",
                    ),
                ),
                (
                    "auto_apply_seasonal",
                    models.BooleanField(
                        default=True,
                        help_text="Automatically apply seasonal pricing adjustments",
                        verbose_name="Auto Apply Seasonal Pricing",
                    ),
                ),
                (
                    "auto_calculate_parts",
                    models.BooleanField(
                        default=True,
                        help_text="Automatically calculate parts cost based on required materials",
                        verbose_name="Auto Calculate Parts Cost",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=10,
                        help_text="Higher numbers = higher priority when multiple rules apply",
                        verbose_name="Priority",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_pricing",
                        to="setup.company",
                        verbose_name="Company",
                    ),
                ),
                (
                    "franchise",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_pricing",
                        to="setup.franchise",
                        verbose_name="Franchise",
                    ),
                ),
                (
                    "maintenance_schedule",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="service_pricing",
                        to="work_orders.maintenanceschedule",
                        verbose_name="Maintenance Schedule",
                    ),
                ),
                (
                    "operation_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_pricing",
                        to="work_orders.workordertype",
                        verbose_name="Operation Type",
                    ),
                ),
                (
                    "service_center",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_pricing",
                        to="setup.servicecenter",
                        verbose_name="Service Center",
                    ),
                ),
                (
                    "vehicle_make",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_pricing",
                        to="setup.vehiclemake",
                        verbose_name="Vehicle Make",
                    ),
                ),
                (
                    "vehicle_model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_pricing",
                        to="setup.vehiclemodel",
                        verbose_name="Vehicle Model",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Pricing",
                "verbose_name_plural": "Service Pricings",
                "ordering": ["-priority", "operation_type__name"],
                "indexes": [
                    models.Index(
                        fields=["tenant_id", "operation_type", "is_active"],
                        name="inventory_s_tenant__a49107_idx",
                    ),
                    models.Index(
                        fields=["tenant_id", "valid_from", "valid_to"],
                        name="inventory_s_tenant__b7f460_idx",
                    ),
                ],
                "unique_together": {
                    (
                        "tenant_id",
                        "operation_type",
                        "vehicle_make",
                        "vehicle_model",
                        "year_from",
                        "year_to",
                        "service_center",
                        "pricing_strategy",
                    )
                },
            },
        ),
        migrations.CreateModel(
            name="SeasonalPricing",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name for this seasonal pricing period",
                        max_length=100,
                        verbose_name="Pricing Period Name",
                    ),
                ),
                (
                    "season_type",
                    models.CharField(
                        choices=[
                            ("spring", "Spring"),
                            ("summer", "Summer"),
                            ("autumn", "Autumn"),
                            ("winter", "Winter"),
                            ("ramadan", "Ramadan"),
                            ("hajj", "Hajj Season"),
                            ("eid", "Eid Period"),
                            ("custom", "Custom Period"),
                        ],
                        default="custom",
                        max_length=20,
                        verbose_name="Season Type",
                    ),
                ),
                (
                    "pricing_type",
                    models.CharField(
                        choices=[
                            ("operation", "Operation Pricing"),
                            ("part", "Part Pricing"),
                            ("both", "Both Operations and Parts"),
                        ],
                        default="both",
                        max_length=20,
                        verbose_name="Pricing Type",
                    ),
                ),
                (
                    "start_date",
                    models.DateField(
                        help_text="Start date for this pricing period",
                        verbose_name="Start Date",
                    ),
                ),
                (
                    "end_date",
                    models.DateField(
                        help_text="End date for this pricing period",
                        verbose_name="End Date",
                    ),
                ),
                (
                    "is_recurring",
                    models.BooleanField(
                        default=False,
                        help_text="Does this pricing repeat annually?",
                        verbose_name="Is Recurring",
                    ),
                ),
                (
                    "recurrence_start_month",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Month when recurring period starts (1-12)",
                        null=True,
                        verbose_name="Recurrence Start Month",
                    ),
                ),
                (
                    "recurrence_start_day",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Day when recurring period starts (1-31)",
                        null=True,
                        verbose_name="Recurrence Start Day",
                    ),
                ),
                (
                    "recurrence_end_month",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Month when recurring period ends (1-12)",
                        null=True,
                        verbose_name="Recurrence End Month",
                    ),
                ),
                (
                    "recurrence_end_day",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Day when recurring period ends (1-31)",
                        null=True,
                        verbose_name="Recurrence End Day",
                    ),
                ),
                (
                    "adjustment_type",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage Adjustment"),
                            ("fixed", "Fixed Amount Adjustment"),
                            ("override", "Price Override"),
                        ],
                        default="percentage",
                        max_length=20,
                        verbose_name="Adjustment Type",
                    ),
                ),
                (
                    "adjustment_value",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Percentage (-100 to 1000) or fixed amount",
                        max_digits=15,
                        verbose_name="Adjustment Value",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=10,
                        help_text="Higher numbers = higher priority when multiple rules apply",
                        verbose_name="Priority",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="seasonal_pricing",
                        to="setup.company",
                        verbose_name="Company",
                    ),
                ),
                (
                    "franchise",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="seasonal_pricing",
                        to="setup.franchise",
                        verbose_name="Franchise",
                    ),
                ),
                (
                    "item_classification",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="seasonal_pricing",
                        to="inventory.itemclassification",
                        verbose_name="Item Classification",
                    ),
                ),
                (
                    "operation_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="seasonal_pricing",
                        to="work_orders.workordertype",
                        verbose_name="Operation Type",
                    ),
                ),
                (
                    "service_center",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="seasonal_pricing",
                        to="setup.servicecenter",
                        verbose_name="Service Center",
                    ),
                ),
                (
                    "specific_item",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="seasonal_pricing",
                        to="inventory.item",
                        verbose_name="Specific Item",
                    ),
                ),
            ],
            options={
                "verbose_name": "Seasonal Pricing",
                "verbose_name_plural": "Seasonal Pricings",
                "ordering": ["-priority", "start_date"],
                "indexes": [
                    models.Index(
                        fields=["tenant_id", "start_date", "end_date"],
                        name="inventory_s_tenant__f0462b_idx",
                    ),
                    models.Index(
                        fields=["tenant_id", "is_active"],
                        name="inventory_s_tenant__34df8c_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PricingRule",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Descriptive name for this pricing rule",
                        max_length=200,
                        verbose_name="Rule Name",
                    ),
                ),
                (
                    "rule_type",
                    models.CharField(
                        choices=[
                            ("discount", "Discount Rule"),
                            ("surcharge", "Surcharge Rule"),
                            ("override", "Price Override Rule"),
                            ("bundle", "Bundle Pricing Rule"),
                        ],
                        default="discount",
                        max_length=20,
                        verbose_name="Rule Type",
                    ),
                ),
                (
                    "condition_type",
                    models.CharField(
                        choices=[
                            ("vehicle_age", "Vehicle Age"),
                            ("mileage", "Vehicle Mileage"),
                            ("customer_type", "Customer Type"),
                            ("order_value", "Order Value"),
                            ("service_history", "Service History"),
                            ("time_of_day", "Time of Day"),
                            ("day_of_week", "Day of Week"),
                            ("parts_quantity", "Parts Quantity"),
                            ("custom", "Custom Condition"),
                        ],
                        default="order_value",
                        max_length=20,
                        verbose_name="Condition Type",
                    ),
                ),
                (
                    "condition_operator",
                    models.CharField(
                        choices=[
                            ("gt", "Greater Than"),
                            ("gte", "Greater Than or Equal"),
                            ("lt", "Less Than"),
                            ("lte", "Less Than or Equal"),
                            ("eq", "Equal To"),
                            ("ne", "Not Equal To"),
                            ("in", "In Range"),
                            ("contains", "Contains"),
                        ],
                        default="gte",
                        max_length=20,
                        verbose_name="Condition Operator",
                    ),
                ),
                (
                    "condition_value",
                    models.CharField(
                        help_text="Value to compare against (can be number, text, or JSON)",
                        max_length=200,
                        verbose_name="Condition Value",
                    ),
                ),
                (
                    "condition_value_max",
                    models.CharField(
                        blank=True,
                        help_text="Maximum value for range conditions",
                        max_length=200,
                        verbose_name="Condition Max Value",
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage Adjustment"),
                            ("fixed", "Fixed Amount Adjustment"),
                            ("override", "Price Override"),
                            ("multiply", "Multiply by Factor"),
                        ],
                        default="percentage",
                        max_length=20,
                        verbose_name="Action Type",
                    ),
                ),
                (
                    "action_value",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Percentage, amount, or multiplier to apply",
                        max_digits=15,
                        verbose_name="Action Value",
                    ),
                ),
                (
                    "applies_to",
                    models.CharField(
                        choices=[
                            ("total", "Total Price"),
                            ("labor", "Labor Cost Only"),
                            ("parts", "Parts Cost Only"),
                            ("service", "Service Fee Only"),
                        ],
                        default="total",
                        max_length=20,
                        verbose_name="Applies To",
                    ),
                ),
                ("valid_from", models.DateField(verbose_name="Valid From")),
                (
                    "valid_to",
                    models.DateField(
                        blank=True,
                        help_text="Leave blank for indefinite validity",
                        null=True,
                        verbose_name="Valid To",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=10,
                        help_text="Higher numbers = higher priority",
                        verbose_name="Priority",
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(default=0, verbose_name="Usage Count"),
                ),
                (
                    "max_usage",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum number of times this rule can be applied",
                        null=True,
                        verbose_name="Max Usage",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pricing_rules",
                        to="setup.company",
                        verbose_name="Company",
                    ),
                ),
                (
                    "franchise",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pricing_rules",
                        to="setup.franchise",
                        verbose_name="Franchise",
                    ),
                ),
                (
                    "service_center",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pricing_rules",
                        to="setup.servicecenter",
                        verbose_name="Service Center",
                    ),
                ),
            ],
            options={
                "verbose_name": "Pricing Rule",
                "verbose_name_plural": "Pricing Rules",
                "ordering": ["-priority", "name"],
                "indexes": [
                    models.Index(
                        fields=["tenant_id", "is_active", "priority"],
                        name="inventory_p_tenant__45ce9a_idx",
                    ),
                    models.Index(
                        fields=["tenant_id", "valid_from", "valid_to"],
                        name="inventory_p_tenant__145714_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PricingHistory",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "base_service_price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Base Service Price",
                    ),
                ),
                (
                    "labor_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Labor Cost",
                    ),
                ),
                (
                    "parts_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Parts Cost",
                    ),
                ),
                (
                    "total_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=15, verbose_name="Total Price"
                    ),
                ),
                (
                    "seasonal_adjustment",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Seasonal Adjustment",
                    ),
                ),
                (
                    "rules_adjustment",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Rules Adjustment",
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="Discount Amount",
                    ),
                ),
                (
                    "pricing_strategy",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Pricing Strategy"
                    ),
                ),
                (
                    "calculation_method",
                    models.CharField(
                        blank=True,
                        help_text="How the price was calculated",
                        max_length=100,
                        verbose_name="Calculation Method",
                    ),
                ),
                (
                    "applied_rules",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of pricing rules that were applied",
                        verbose_name="Applied Rules",
                    ),
                ),
                (
                    "calculation_context",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Context data used for pricing calculation",
                        verbose_name="Calculation Context",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="pricing_history",
                        to="setup.customer",
                        verbose_name="Customer",
                    ),
                ),
                (
                    "operation_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="pricing_history",
                        to="work_orders.workordertype",
                        verbose_name="Operation Type",
                    ),
                ),
                (
                    "sales_order",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pricing_history",
                        to="sales.salesorder",
                        verbose_name="Sales Order",
                    ),
                ),
                (
                    "service_center",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="pricing_history",
                        to="setup.servicecenter",
                        verbose_name="Service Center",
                    ),
                ),
                (
                    "vehicle",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="pricing_history",
                        to="setup.vehicle",
                        verbose_name="Vehicle",
                    ),
                ),
                (
                    "work_order",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pricing_history",
                        to="work_orders.workorder",
                        verbose_name="Work Order",
                    ),
                ),
            ],
            options={
                "verbose_name": "Pricing History",
                "verbose_name_plural": "Pricing History",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["tenant_id", "created_at"],
                        name="inventory_p_tenant__d97d96_idx",
                    ),
                    models.Index(
                        fields=["tenant_id", "work_order"],
                        name="inventory_p_tenant__63d21a_idx",
                    ),
                    models.Index(
                        fields=["tenant_id", "operation_type"],
                        name="inventory_p_tenant__ba4738_idx",
                    ),
                    models.Index(
                        fields=["tenant_id", "service_center", "created_at"],
                        name="inventory_p_tenant__af381f_idx",
                    ),
                ],
            },
        ),
    ]
