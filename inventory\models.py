from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet
import waffle
import os
import uuid
from django.core.exceptions import ValidationError
from django.urls import reverse
from setup.models import VehicleMake, VehicleModel
from django.utils import timezone


class ItemClassification(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Classification system for inventory items to enable better reporting and filtering
    """
    name = models.Char<PERSON>ield(_("Classification Name"), max_length=100)
    code = models.Char<PERSON>ield(_("Classification Code"), max_length=50)
    description = models.TextField(_("Description"), blank=True)
    parent = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, blank=True,
        related_name='children',
        verbose_name=_("Parent Classification")
    )
    level = models.PositiveSmallIntegerField(_("Level"), default=0, help_text=_("Hierarchical level (0=top level)"))
    is_active = models.BooleanField(_("Is Active"), default=True)
    icon = models.CharField(_("Icon"), max_length=50, blank=True, help_text=_("Icon name for UI display"))
    color = models.CharField(_("Color"), max_length=20, blank=True, help_text=_("Color code for UI display"))
    # Additional configurable attributes
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item Classification")
        verbose_name_plural = _("Item Classifications")
        unique_together = [['tenant_id', 'code']]
        ordering = ['level', 'name']
        
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # Set the level based on parent
        if self.parent:
            self.level = self.parent.level + 1
        else:
            self.level = 0
        super().save(*args, **kwargs)
    
    def get_full_path(self):
        """Return full classification path including ancestors"""
        path = []
        current = self
        while current:
            path.insert(0, current.name)
            current = current.parent
        return " > ".join(path)
    
    def get_children_recursive(self):
        """Get all children recursively"""
        all_children = list(self.children.all())
        for child in self.children.all():
            all_children.extend(child.get_children_recursive())
        return all_children


class ItemQuerySet(BaseQuerySet):
    """
    Custom queryset for Item model with tenant awareness
    """
    
    def compatible_with_vehicle(self, make, model=None, year=None):
        """
        Filter items that are compatible with the specified vehicle parameters
        """
        qs = self.filter(vehicle_compatibilities__make__exact=make)
        
        if model:
            qs = qs.filter(vehicle_compatibilities__model__exact=model)
            
        if year and isinstance(year, int):
            qs = qs.filter(
                models.Q(vehicle_compatibilities__year_from__lte=year) & 
                (models.Q(vehicle_compatibilities__year_to__isnull=True) | models.Q(vehicle_compatibilities__year_to__gte=year))
            )
            
        return qs.distinct()
        
    def compatible_with_operation(self, operation_type_id):
        """
        Filter items that are compatible with the specified operation type
        """
        return self.filter(operation_compatibilities__operation_type_id=operation_type_id).distinct()
        
    def by_classification(self, classification_id):
        """
        Filter items by classification (including child classifications)
        """
        try:
            classification = ItemClassification.objects.get(pk=classification_id)
            # Get all child classifications
            child_ids = [child.id for child in classification.get_children_recursive()]
            # Add current classification
            classification_ids = [classification.id] + child_ids
            return self.filter(classification_id__in=classification_ids)
        except ItemClassification.DoesNotExist:
            return self.none()


class UnitOfMeasurement(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Units of measurement for inventory items
    """
    name = models.CharField(_("Name"), max_length=50)
    symbol = models.CharField(_("Symbol"), max_length=10)
    description = models.TextField(_("Description"), blank=True)
    is_base_unit = models.BooleanField(_("Is Base Unit"), default=False, 
                                      help_text=_("If checked, this unit will be used as a reference for conversions"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Unit of Measurement")
        verbose_name_plural = _("Units of Measurement")
        unique_together = [['tenant_id', 'name'], ['tenant_id', 'symbol']]
        
    def __str__(self):
        return f"{self.name} ({self.symbol})"


class UnitConversion(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Conversion rules between units of measurement
    """
    from_unit = models.ForeignKey(UnitOfMeasurement, related_name='conversions_from', 
                                 on_delete=models.CASCADE, verbose_name=_("From Unit"))
    to_unit = models.ForeignKey(UnitOfMeasurement, related_name='conversions_to', 
                               on_delete=models.CASCADE, verbose_name=_("To Unit"))
    conversion_factor = models.DecimalField(_("Conversion Factor"), max_digits=20, decimal_places=10,
                                          help_text=_("Multiply quantity in 'from_unit' by this factor to get quantity in 'to_unit'"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Unit Conversion")
        verbose_name_plural = _("Unit Conversions")
        unique_together = [['tenant_id', 'from_unit', 'to_unit']]
        
    def __str__(self):
        return f"{self.from_unit.symbol} → {self.to_unit.symbol} (×{self.conversion_factor})"
    
    def clean(self):
        """
        Validate that from_unit and to_unit are different and belong to the same tenant
        """
        if self.from_unit == self.to_unit:
            raise ValidationError(_("From unit and to unit must be different"))
        
        if self.from_unit.tenant_id != self.to_unit.tenant_id:
            raise ValidationError(_("Both units must belong to the same tenant"))
        
        super().clean()
    
    def convert(self, quantity):
        """
        Convert a quantity from from_unit to to_unit
        """
        return quantity * self.conversion_factor


class Item(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Core inventory item model with dynamic attributes via JSONField
    """
    sku = models.CharField(_("SKU"), max_length=100, db_index=True)
    name = models.CharField(_("Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2, default=0)
    unit_of_measurement = models.ForeignKey(UnitOfMeasurement, related_name='items', 
                                          on_delete=models.PROTECT, verbose_name=_("Unit of Measurement"),
                                          null=True, blank=True)
    unit_price = models.DecimalField(_("Unit Price"), max_digits=10, decimal_places=2, default=0)
    min_stock_level = models.DecimalField(_("Minimum Stock Level"), max_digits=10, decimal_places=2, default=0)
    attributes = models.JSONField(_("Attributes"), default=dict, blank=True)
    
    # Optional categories
    ITEM_CATEGORIES = (
        ('part', _('Part')), 
        ('consumable', _('Consumable')),
        ('tool', _('Tool')),
        ('equipment', _('Equipment')),
        ('material', _('Material')),
        ('finished_good', _('Finished Good')),
    )
    category = models.CharField(_("Category"), max_length=50, choices=ITEM_CATEGORIES, blank=True)
    item_type = models.CharField(_("Item Type"), max_length=100, blank=True, help_text=_("Specific type within category"))
    
    # Batch Tracking Configuration
    BATCH_LEVEL_CHOICES = (
        ('none', _('No Batch Tracking')),
        ('basic', _('Basic Batch Tracking')),
        ('full', _('Full Batch Tracking with Expiry')),
    )
    
    INVENTORY_METHOD_CHOICES = (
        ('fifo', _('First In, First Out (FIFO)')),
        ('filo', _('First In, Last Out (FILO/LIFO)')),
        ('manual', _('Manual Selection')),
    )
    
    batch_tracking_level = models.CharField(
        _("Batch Tracking Level"), 
        max_length=20, 
        choices=BATCH_LEVEL_CHOICES, 
        default='none',
        help_text=_("Level of batch tracking for this item")
    )
    
    inventory_method = models.CharField(
        _("Inventory Method"), 
        max_length=20, 
        choices=INVENTORY_METHOD_CHOICES, 
        default='fifo',
        help_text=_("Method for selecting batches when items are consumed")
    )
    
    requires_expiry_tracking = models.BooleanField(
        _("Requires Expiry Tracking"), 
        default=False,
        help_text=_("Whether this item requires expiry date tracking")
    )
    
    default_shelf_life_days = models.PositiveIntegerField(
        _("Default Shelf Life (Days)"), 
        null=True, blank=True,
        help_text=_("Default shelf life in days for new batches")
    )
    
    # Batch numbering configuration
    auto_generate_batch_number = models.BooleanField(
        _("Auto Generate Batch Number"), 
        default=False,
        help_text=_("Automatically generate batch numbers for this item")
    )
    
    batch_number_prefix = models.CharField(
        _("Batch Number Prefix"), 
        max_length=10, 
        blank=True,
        help_text=_("Prefix for auto-generated batch numbers")
    )
    
    # Add classification relationship
    classification = models.ForeignKey(
        ItemClassification,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='items',
        verbose_name=_("Classification")
    )
    
    objects = ItemQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item")
        verbose_name_plural = _("Items")
        unique_together = [['tenant_id', 'sku']]
        
    def __str__(self):
        return f"{self.name} ({self.sku})"
    
    @property
    def current_stock(self):
        """
        Returns current stock level (alias for quantity)
        """
        return self.quantity
        
    @property
    def is_low_stock(self):
        """
        Check if item is below minimum stock level
        """
        return self.quantity < self.min_stock_level

    def get_quantity_in_unit(self, target_unit):
        """
        Convert item quantity to specified unit
        
        Args:
            target_unit: UnitOfMeasurement instance or unit ID
            
        Returns:
            Decimal: Converted quantity
            
        Raises:
            ValueError: If no conversion path exists between units
        """
        if not self.unit_of_measurement:
            raise ValueError(_("Item does not have a unit of measurement defined"))
            
        # If target_unit is the same as item's unit, return quantity as is
        if (isinstance(target_unit, UnitOfMeasurement) and self.unit_of_measurement == target_unit) or \
           (isinstance(target_unit, (str, uuid.UUID)) and str(self.unit_of_measurement.id) == str(target_unit)):
            return self.quantity
        
        # Get target unit instance if an ID was provided
        if not isinstance(target_unit, UnitOfMeasurement):
            target_unit = UnitOfMeasurement.objects.get(pk=target_unit)
            
        # Look for direct conversion
        try:
            conversion = UnitConversion.objects.get(
                tenant_id=self.tenant_id,
                from_unit=self.unit_of_measurement,
                to_unit=target_unit
            )
            return conversion.convert(self.quantity)
        except UnitConversion.DoesNotExist:
            # Try inverse conversion
            try:
                conversion = UnitConversion.objects.get(
                    tenant_id=self.tenant_id,
                    from_unit=target_unit,
                    to_unit=self.unit_of_measurement
                )
                # Inverse factor for reverse conversion
                return self.quantity / conversion.conversion_factor
            except UnitConversion.DoesNotExist:
                # Could implement more complex conversion path finding here
                raise ValueError(_("No conversion path exists between {} and {}").format(
                    self.unit_of_measurement.symbol, target_unit.symbol))

    # Batch Management Methods
    def has_batch_tracking(self):
        """Check if this item has batch tracking enabled"""
        return self.batch_tracking_level != 'none'
    
    def requires_batch_tracking(self):
        """Check if this item requires batch tracking for all movements"""
        return self.batch_tracking_level in ['basic', 'full']
    
    def get_next_batch_number(self):
        """Generate the next batch number for this item"""
        if self.auto_generate_batch_number:
            prefix = self.batch_number_prefix or ''
            
            # Find the highest sequential number for this item
            existing_batches = self.batches.filter(
                batch_number__startswith=prefix
            ).order_by('-batch_number')
            
            if existing_batches.exists():
                last_batch = existing_batches.first()
                # Extract number from batch_number
                if last_batch.batch_number.startswith(prefix):
                    try:
                        last_number = int(last_batch.batch_number[len(prefix):])
                        next_number = last_number + 1
                    except ValueError:
                        next_number = 1
                else:
                    next_number = 1
            else:
                next_number = 1
            
            return f"{prefix}{next_number:04d}"
        return None
    
    def get_available_batches(self, quantity_needed=None):
        """
        Get available batches for this item based on inventory method
        
        Args:
            quantity_needed: Optional quantity filter
            
        Returns:
            QuerySet of ItemBatch objects ordered by inventory method
        """
        if not self.has_batch_tracking():
            return ItemBatch.objects.none()
        
        batches = self.batches.filter(
            status='active',
            current_quantity__gt=0
        )
        
        if quantity_needed:
            # Only return batches that have enough quantity
            batches = batches.filter(current_quantity__gte=quantity_needed)
        
        # Order by inventory method
        if self.inventory_method == 'fifo':
            # First In, First Out - oldest first
            batches = batches.order_by('received_date', 'created_at')
        elif self.inventory_method == 'filo':
            # First In, Last Out (LIFO) - newest first
            batches = batches.order_by('-received_date', '-created_at')
        else:
            # Manual selection - no specific order
            batches = batches.order_by('batch_number')
        
        return batches
    
    def allocate_batch_quantities(self, quantity_needed):
        """
        Allocate quantities from available batches based on inventory method
        
        Args:
            quantity_needed: Total quantity to allocate
            
        Returns:
            List of tuples: [(batch, allocated_quantity), ...]
        """
        if not self.has_batch_tracking():
            return []
        
        available_batches = self.get_available_batches()
        allocations = []
        remaining_quantity = quantity_needed
        
        for batch in available_batches:
            if remaining_quantity <= 0:
                break
                
            available_in_batch = batch.available_quantity
            if available_in_batch > 0:
                allocated = min(available_in_batch, remaining_quantity)
                allocations.append((batch, allocated))
                remaining_quantity -= allocated
        
        return allocations
    
    def get_batch_summary(self):
        """Get summary of all batches for this item"""
        if not self.has_batch_tracking():
            return None
        
        from django.db.models import Sum, Count
        
        summary = self.batches.aggregate(
            total_batches=Count('id'),
            total_quantity=Sum('current_quantity'),
            total_reserved=Sum('reserved_quantity'),
            active_batches=Count('id', filter=models.Q(status='active')),
            expired_batches=Count('id', filter=models.Q(status='expired')),
        )
        
        return summary
    
    def get_expiring_batches(self, days_ahead=30):
        """Get batches that will expire within specified days"""
        if not self.requires_expiry_tracking:
            return ItemBatch.objects.none()
        
        from django.utils import timezone
        from datetime import timedelta
        
        expiry_threshold = timezone.now().date() + timedelta(days=days_ahead)
        
        return self.batches.filter(
            status='active',
            expiry_date__lte=expiry_threshold,
            expiry_date__isnull=False
        ).order_by('expiry_date')
    
    def update_quantity_from_batches(self):
        """Update item quantity based on sum of all active batch quantities"""
        if self.has_batch_tracking():
            from django.db.models import Sum
            total_quantity = self.batches.filter(
                status='active'
            ).aggregate(
                total=Sum('current_quantity')
            )['total'] or 0
            
            self.quantity = total_quantity
            self.save(update_fields=['quantity'])
            
    def clean(self):
        """Validate item batch configuration"""
        super().clean()
        
        # Validate batch tracking configuration
        if self.batch_tracking_level == 'full' and not self.requires_expiry_tracking:
            # Full batch tracking should include expiry tracking
            self.requires_expiry_tracking = True
        
        # Validate auto batch number generation
        if self.auto_generate_batch_number and not self.batch_number_prefix:
            # Suggest a prefix based on SKU
            self.batch_number_prefix = self.sku[:3].upper() if self.sku else 'BTH'


def get_document_upload_path(instance, filename):
    """
    Generate unique file path for document uploads
    Format: inventory/documents/{tenant_id}/{item_id}/{uuid}_{filename}
    """
    unique_filename = f"{uuid.uuid4()}_{filename}"
    return os.path.join(
        'inventory', 
        'documents', 
        str(instance.tenant_id),
        str(instance.item.id), 
        unique_filename
    )


class ItemDocument(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Document attachments for inventory items (manuals, specifications, certifications, etc.)
    """
    DOCUMENT_TYPES = (
        ('manual', _('User Manual')),
        ('spec', _('Technical Specification')),
        ('cert', _('Certification')),
        ('warranty', _('Warranty Information')),
        ('image', _('Product Image')),
        ('other', _('Other')),
    )
    
    item = models.ForeignKey(Item, related_name='documents', on_delete=models.CASCADE, 
                           verbose_name=_("Item"))
    title = models.CharField(_("Title"), max_length=255)
    document_type = models.CharField(_("Document Type"), max_length=20, choices=DOCUMENT_TYPES, 
                                   default='other')
    file = models.FileField(_("File"), upload_to=get_document_upload_path)
    description = models.TextField(_("Description"), blank=True)
    is_public = models.BooleanField(_("Public"), default=False, 
                                  help_text=_("If checked, this document will be visible to all users"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item Document")
        verbose_name_plural = _("Item Documents")
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.title} - {self.item.name}"
    
    def filename(self):
        """
        Return just the filename portion of the file path
        """
        if not self.file or not self.file.name:
            return "No file"
        return os.path.basename(self.file.name)
    
    def file_extension(self):
        """
        Return the file extension (lowercase)
        """
        if not self.file or not self.file.name:
            return ""
        name, extension = os.path.splitext(self.file.name)
        return extension.lower()[1:] if extension else ""
    
    def is_image(self):
        """
        Check if the document is an image based on its extension
        """
        if not self.file:
            return False
        return self.file_extension() in ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']

    def delete(self, *args, **kwargs):
        """
        Delete the file from storage when the document is deleted
        """
        if self.file:
            # Delete the file from storage
            storage = self.file.storage
            if storage.exists(self.file.name):
                storage.delete(self.file.name)
                
        super().delete(*args, **kwargs)


class MovementType(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Dynamic configuration of movement types (in/out/transfer/etc.)
    """
    code = models.CharField(_("Code"), max_length=50, db_index=True, help_text=_("Unique code for this movement type"))
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    is_inbound = models.BooleanField(_("Is Inbound"), help_text=_("Does this movement type increase stock?"))
    is_outbound = models.BooleanField(_("Is Outbound"), help_text=_("Does this movement type decrease stock?"))
    icon = models.CharField(_("Icon"), max_length=50, blank=True, help_text=_("Icon name for UI display"))
    color = models.CharField(_("Color"), max_length=20, blank=True, help_text=_("Color code for UI display"))
    is_active = models.BooleanField(_("Is Active"), default=True)
    requires_reference = models.BooleanField(_("Requires Reference"), default=False, 
                                          help_text=_("Is a reference document required?"))
    requires_approval = models.BooleanField(_("Requires Approval"), default=False,
                                         help_text=_("Does this type of movement require approval?"))
    sequence = models.PositiveIntegerField(_("Sequence"), default=10, help_text=_("Display order in UI"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Movement Type")
        verbose_name_plural = _("Movement Types")
        unique_together = [['tenant_id', 'code']]
        ordering = ['sequence', 'name']
        
    def __str__(self):
        return self.name
        
    def save(self, *args, **kwargs):
        # If both inbound and outbound are True, this is a transfer
        # Make sure at least one of them is True
        if not (self.is_inbound or self.is_outbound):
            raise ValidationError(_("Movement type must be either inbound, outbound, or both"))
        super().save(*args, **kwargs)

    
class Movement(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Stock movement ledger for inventory items
    """
    # Legacy movement types kept for backward compatibility
    LEGACY_MOVEMENT_TYPES = (
        ('purchase', _('Purchase')),
        ('sale', _('Sale')),
        ('adjustment', _('Adjustment')),
        ('transfer', _('Transfer')),
        ('return', _('Return')),
    )
    
    item = models.ForeignKey(Item, related_name='movements', on_delete=models.CASCADE)
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    unit_of_measurement = models.ForeignKey(UnitOfMeasurement, related_name='movements',
                                          on_delete=models.PROTECT, verbose_name=_("Unit of Measurement"),
                                          null=True, blank=True)
    # New field referencing the dynamic movement type
    movement_type_ref = models.ForeignKey(MovementType, related_name='movements',
                                        on_delete=models.PROTECT, verbose_name=_("Movement Type"),
                                        null=True, blank=True)
    # Legacy field for backward compatibility
    movement_type = models.CharField(_("Legacy Movement Type"), max_length=20, 
                                   choices=LEGACY_MOVEMENT_TYPES, null=True, blank=True)
    reference = models.CharField(_("Reference"), max_length=100, blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Movement")
        verbose_name_plural = _("Movements")
        ordering = ['-created_at']
        
    def __str__(self):
        movement_name = self.get_movement_name()
        return f"{movement_name}: {self.quantity} x {self.item.name}"
    
    def get_movement_name(self):
        """Get the display name of the movement type"""
        if self.movement_type_ref:
            return self.movement_type_ref.name
        # Fallback to legacy type
        return self.get_movement_type_display() if self.movement_type else _("Unknown")
    
    def is_inbound(self):
        """Determine if this is an inbound movement"""
        if self.movement_type_ref:
            return self.movement_type_ref.is_inbound
        # Legacy logic
        return self.movement_type in ['purchase', 'return', 'adjustment'] and self.quantity > 0
    
    def is_outbound(self):
        """Determine if this is an outbound movement"""
        if self.movement_type_ref:
            return self.movement_type_ref.is_outbound
        # Legacy logic
        return self.movement_type in ['sale', 'adjustment'] and self.quantity < 0
    
    def clean(self):
        """
        Validate that at least one movement type field is populated
        """
        if not self.movement_type and not self.movement_type_ref:
            raise ValidationError(_("Either movement_type or movement_type_ref must be specified"))
        super().clean()
    
    def save(self, *args, **kwargs):
        """
        Override save method to update item quantity
        """
        # Update item quantity
        item = self.item
        if not self.id:  # Only update quantity on new movement creation
            if self.is_inbound():
                item.quantity += self.quantity
            elif self.is_outbound():
                item.quantity -= abs(self.quantity)
            item.save(update_fields=['quantity', 'updated_at'])
        
        super().save(*args, **kwargs)
    
    def delete(self, *args, **kwargs):
        """
        Override delete method to revert item quantity
        """
        # Revert item quantity
        item = self.item
        if self.is_inbound():
            item.quantity -= self.quantity
        elif self.is_outbound():
            item.quantity += abs(self.quantity)
        item.save(update_fields=['quantity', 'updated_at'])
        
        super().delete(*args, **kwargs)


class VehicleCompatibility(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Vehicle compatibility information for inventory items
    """
    item = models.ForeignKey(
        Item, 
        on_delete=models.CASCADE,
        related_name="vehicle_compatibilities",
        verbose_name=_("Item")
    )
    make = models.CharField(_("Vehicle Make"), max_length=100, db_index=True)
    model = models.CharField(_("Vehicle Model"), max_length=100, db_index=True)
    year_from = models.PositiveIntegerField(_("Year From"), db_index=True)
    year_to = models.PositiveIntegerField(_("Year To"), null=True, blank=True, db_index=True,
                                         help_text=_("Leave blank if still compatible with current models"))
    variant = models.CharField(_("Variant/Trim"), max_length=100, blank=True, 
                              help_text=_("Specific variant or trim level if applicable"))
    engine = models.CharField(_("Engine"), max_length=100, blank=True,
                             help_text=_("Engine type or code if applicable"))
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Vehicle Compatibility")
        verbose_name_plural = _("Vehicle Compatibilities")
        ordering = ['make', 'model', 'year_from']
        unique_together = [['tenant_id', 'item', 'make', 'model', 'year_from', 'variant', 'engine']]
        
    def __str__(self):
        year_range = f"{self.year_from}-{self.year_to}" if self.year_to else f"{self.year_from}+"
        return f"{self.make} {self.model} ({year_range})"
    
    def clean(self):
        """
        Validate year range
        """
        if self.year_to and self.year_from > self.year_to:
            raise ValidationError(_("Year from must be less than or equal to year to"))
        
        super().clean()


class VehicleOperationCompatibility(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Links operation compatibilities to specific vehicle makes/models with year ranges
    """
    operation_compatibility = models.ForeignKey(
        'OperationCompatibility',
        on_delete=models.CASCADE,
        related_name='vehicle_compatibilities',
        verbose_name=_("Operation Compatibility")
    )
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        related_name='operation_compatibilities',
        verbose_name=_("Vehicle Make")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE, 
        null=True, blank=True,
        related_name='operation_compatibilities',
        verbose_name=_("Vehicle Model")
    )
    year_from = models.PositiveIntegerField(
        _("Year From"),
        null=True, blank=True,
        help_text=_("Start year of compatible vehicle models")
    )
    year_to = models.PositiveIntegerField(
        _("Year To"),
        null=True, blank=True,
        help_text=_("End year of compatible vehicle models (leave blank for current)")
    )
    duration_minutes = models.PositiveIntegerField(
        _("Duration (minutes)"),
        null=True, blank=True,
        help_text=_("Estimated duration of this operation for this specific vehicle. Overrides the default duration.")
    )
    
    class Meta:
        verbose_name = _("Vehicle Operation Compatibility")
        verbose_name_plural = _("Vehicle Operation Compatibilities")
        unique_together = [
            ('operation_compatibility', 'vehicle_make', 'vehicle_model', 'year_from', 'year_to'),
        ]
        
    def __str__(self):
        vehicle_info = f"{self.vehicle_make.name}"
        if self.vehicle_model:
            vehicle_info += f" {self.vehicle_model.name}"
        if self.year_from:
            vehicle_info += f" ({self.year_from}"
            if self.year_to:
                vehicle_info += f"-{self.year_to}"
            vehicle_info += ")"
        return f"{self.operation_compatibility} - {vehicle_info}"

    def clean(self):
        """Validate year range"""
        if self.year_to and self.year_from and self.year_from > self.year_to:
            raise ValidationError(_("Year from must be less than or equal to year to"))
            
    def duration_minutes_display(self):
        """Return a human-readable duration"""
        if self.duration_minutes:
            hours = self.duration_minutes // 60
            minutes = self.duration_minutes % 60
            if hours and minutes:
                return f"{hours}h {minutes}m"
            elif hours:
                return f"{hours}h"
            else:
                return f"{minutes}m"
        # If no specific duration set, return the parent operation compatibility duration
        elif self.operation_compatibility and self.operation_compatibility.duration_minutes:
            return self.operation_compatibility.duration_minutes_display()
        return "-"
    duration_minutes_display.short_description = _("Duration")


class OperationCompatibility(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Links inventory items to compatible work order operations/types
    """
    item = models.ForeignKey(
        Item, 
        on_delete=models.CASCADE,
        related_name="operation_compatibilities",
        verbose_name=_("Item")
    )
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',  # Use string reference to avoid circular import
        on_delete=models.CASCADE,
        related_name="compatible_items",
        verbose_name=_("Operation Type")
    )
    is_required = models.BooleanField(_("Required"), default=False,
                                    help_text=_("Is this item required for this operation type?"))
    is_common = models.BooleanField(_("Common"), default=True,
                                   help_text=_("Is this item commonly used for this operation type?"))
    maintenance_schedule = models.ForeignKey(
        'work_orders.MaintenanceSchedule',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="operation_compatibilities",
        verbose_name=_("Maintenance Schedule")
    )
    
    # Vehicle compatibility is now handled through the related VehicleOperationCompatibility model
    # These fields are kept for backward compatibility but will be phased out
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name='operation_compatibilities_make',
        verbose_name=_("Legacy Vehicle Make")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name='operation_compatibilities_model',
        verbose_name=_("Legacy Vehicle Model")
    )
    year_from = models.PositiveIntegerField(
        _("Legacy Year From"),
        null=True, blank=True,
        help_text=_("Legacy field - use vehicle compatibilities instead")
    )
    year_to = models.PositiveIntegerField(
        _("Legacy Year To"),
        null=True, blank=True,
        help_text=_("Legacy field - use vehicle compatibilities instead")
    )
    
    # New fields for operation description and duration
    operation_description = models.CharField(
        _("Operation Description"),
        max_length=255,
        choices=[
            ('oil_change', _('Oil Change')),
            ('brake_service', _('Brake Service')),
            ('transmission_service', _('Transmission Service')),
            ('coolant_flush', _('Coolant Flush')),
            ('air_filter', _('Air Filter Replacement')),
            ('fuel_filter', _('Fuel Filter Replacement')),
            ('spark_plugs', _('Spark Plugs Replacement')),
            ('timing_belt', _('Timing Belt Replacement')),
            ('battery_replacement', _('Battery Replacement')),
            ('tire_rotation', _('Tire Rotation')),
            ('wheel_alignment', _('Wheel Alignment')),
            ('suspension_repair', _('Suspension Repair')),
            ('exhaust_repair', _('Exhaust System Repair')),
            ('ac_service', _('A/C Service')),
            ('diagnostics', _('Diagnostics')),
            ('other', _('Other')),
        ],
        blank=True,
        null=True
    )
    duration_minutes = models.PositiveIntegerField(
        _("Duration (minutes)"),
        null=True, blank=True,
        help_text=_("Estimated duration of this operation in minutes")
    )
    
    class Meta:
        verbose_name = _("Operation Compatibility")
        verbose_name_plural = _("Operation Compatibilities")
        unique_together = [
            ('item', 'operation_type', 'maintenance_schedule'),
        ]
        ordering = ['operation_type__name', 'item__name']
        
    def __str__(self):
        base_info = f"{self.item} - {self.operation_type}"
        if self.vehicle_compatibilities.exists():
            # Show count of compatible vehicles
            return f"{base_info} ({self.vehicle_compatibilities.count()} vehicles)"
        # Legacy display for old records
        vehicle_info = ""
        if self.vehicle_make:
            vehicle_info += f" - {self.vehicle_make}"
        if self.vehicle_model:
            vehicle_info += f" {self.vehicle_model}"
        if self.year_from:
            vehicle_info += f" ({self.year_from}"
            if self.year_to:
                vehicle_info += f"-{self.year_to}"
            vehicle_info += ")"
            
        return f"{base_info}{vehicle_info}"
    
    def vehicle_make_display(self):
        """For legacy data"""
        return self.vehicle_make.name if self.vehicle_make else "-"
    vehicle_make_display.short_description = _("Make")
    
    def vehicle_model_display(self):
        """For legacy data"""
        return self.vehicle_model.name if self.vehicle_model else "-"
    vehicle_model_display.short_description = _("Model")
    
    def duration_minutes_display(self):
        if self.duration_minutes:
            hours = self.duration_minutes // 60
            minutes = self.duration_minutes % 60
            if hours and minutes:
                return f"{hours}h {minutes}m"
            elif hours:
                return f"{hours}h"
            else:
                return f"{minutes}m"
        return "-"
    duration_minutes_display.short_description = _("Duration")
    
    def get_compatible_vehicles(self):
        """Return a list of vehicle makes/models this operation is compatible with"""
        return self.vehicle_compatibilities.all()
        
    def migrate_legacy_vehicle_data(self):
        """Migrate legacy vehicle data to the new structure"""
        if self.vehicle_make and not self.vehicle_compatibilities.filter(
            vehicle_make=self.vehicle_make,
            vehicle_model=self.vehicle_model,
            year_from=self.year_from,
            year_to=self.year_to
        ).exists():
            VehicleOperationCompatibility.objects.create(
                operation_compatibility=self,
                vehicle_make=self.vehicle_make,
                vehicle_model=self.vehicle_model,
                year_from=self.year_from,
                year_to=self.year_to
            )
            return True
        return False
    
    def is_compatible_with_vehicle(self, vehicle):
        """
        Check if this operation compatibility applies to the given vehicle
        
        Args:
            vehicle: Vehicle instance to check compatibility against
            
        Returns:
            bool: True if compatible, False otherwise
        """
        # First check new vehicle compatibilities
        for compat in self.vehicle_compatibilities.all():
            # Check make
            if vehicle.make and vehicle.make.id != compat.vehicle_make_id:
                continue
                
            # Check model if specified
            if compat.vehicle_model and vehicle.model and vehicle.model.id != compat.vehicle_model_id:
                continue
                
            # Check year range if specified
            if compat.year_from and vehicle.year and vehicle.year < compat.year_from:
                continue
                
            if compat.year_to and vehicle.year and vehicle.year > compat.year_to:
                continue
                
            # If we passed all checks, this vehicle is compatible
            return True
            
        # If no new compatibilities, check legacy fields
        # If no vehicle restrictions, compatible with all
        if not self.vehicle_make and not self.vehicle_model and not self.year_from:
            return True
            
        # Check make
        if self.vehicle_make and vehicle.make and vehicle.make.id != self.vehicle_make.id:
            return False
            
        # Check model
        if self.vehicle_model and vehicle.model and vehicle.model.id != self.vehicle_model.id:
            return False
            
        # Check year
        if self.year_from and vehicle.year and vehicle.year < self.year_from:
            return False
            
        if self.year_to and vehicle.year and vehicle.year > self.year_to:
            return False
            
        return True
        
    @classmethod
    def get_compatible_operations(cls, vehicle, item=None):
        """
        Get operation compatibilities that match the given vehicle and optionally item
        
        Args:
            vehicle: Vehicle instance to find compatibilities for
            item: Optional Item instance to filter by
            
        Returns:
            QuerySet of compatible OperationCompatibility instances
        """
        # Base query
        query = cls.objects.all()
        
        if item:
            query = query.filter(item=item)
        
        # Find through new vehicle compatibilities
        vehicle_compat_filter = models.Q(
            vehicle_compatibilities__vehicle_make=vehicle.make
        )
        
        # If model is provided, add to filter, including records without model constraint
        if vehicle.model:
            vehicle_compat_filter &= (
                models.Q(vehicle_compatibilities__vehicle_model__isnull=True) | 
                models.Q(vehicle_compatibilities__vehicle_model=vehicle.model)
            )
            
        # If year is provided, add to filter
        if vehicle.year:
            vehicle_compat_filter &= (
                models.Q(vehicle_compatibilities__year_from__isnull=True) |
                models.Q(vehicle_compatibilities__year_from__lte=vehicle.year)
            )
            vehicle_compat_filter &= (
                models.Q(vehicle_compatibilities__year_to__isnull=True) |
                models.Q(vehicle_compatibilities__year_to__gte=vehicle.year)
            )
        
        # Legacy filter
        legacy_filter = models.Q()
        if vehicle.make:
            legacy_filter &= (
                models.Q(vehicle_make__isnull=True) | 
                models.Q(vehicle_make=vehicle.make)
            )
            
        if vehicle.model:
            legacy_filter &= (
                models.Q(vehicle_model__isnull=True) | 
                models.Q(vehicle_model=vehicle.model)
            )
            
        if vehicle.year:
            legacy_filter &= (
                models.Q(year_from__isnull=True) |
                models.Q(year_from__lte=vehicle.year)
            )
            legacy_filter &= (
                models.Q(year_to__isnull=True) |
                models.Q(year_to__gte=vehicle.year)
            )
            
        # Combine new and legacy filters
        query = query.filter(vehicle_compat_filter | legacy_filter)
            
        return query.distinct()


class VehicleModelPart(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Maps specific vehicle models/years to their compatible spare parts
    """
    make = models.CharField(_("Vehicle Make"), max_length=100, db_index=True)
    model = models.CharField(_("Vehicle Model"), max_length=100, db_index=True)
    year_from = models.PositiveIntegerField(_("Year From"), db_index=True)
    year_to = models.PositiveIntegerField(_("Year To"), null=True, blank=True, db_index=True)
    
    # Part/Item information
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name="vehicle_models",
        verbose_name=_("Part/Item")
    )
    
    # Engine details
    engine_type = models.CharField(_("Engine Type"), max_length=100, blank=True)
    engine_displacement = models.CharField(_("Engine Displacement"), max_length=50, blank=True)
    transmission_type = models.CharField(_("Transmission Type"), max_length=50, blank=True)
    
    # Part details
    is_oem = models.BooleanField(_("Is OEM Part"), default=False,
                                help_text=_("Is this an Original Equipment Manufacturer part?"))
    fits_position = models.CharField(_("Position"), max_length=50, blank=True,
                                   help_text=_("Position on vehicle (e.g., front, rear, left, right)"))
    
    # Maintenance schedule link
    maintenance_schedule = models.ForeignKey(
        'work_orders.MaintenanceSchedule', 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="vehicle_parts",
        verbose_name=_("Maintenance Schedule")
    )
    
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Vehicle Model Part")
        verbose_name_plural = _("Vehicle Model Parts")
        ordering = ['make', 'model', 'year_from']
        unique_together = [['tenant_id', 'make', 'model', 'year_from', 'item', 'engine_type']]
        
    def __str__(self):
        year_range = f"{self.year_from}"
        if self.year_to:
            year_range += f"-{self.year_to}"
        return f"{self.item.name} - {self.make} {self.model} ({year_range})"
        
    def clean(self):
        """Validate year range"""
        if self.year_to and self.year_from > self.year_to:
            raise ValidationError(_("Year from must be less than or equal to year to"))
            
    @property
    def year_range_display(self):
        """Return a formatted year range string"""
        if self.year_to:
            return f"{self.year_from}-{self.year_to}"
        return f"{self.year_from}+"


class OperationPricing(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Pricing model for operations on different vehicle makes/models
    """
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',
        on_delete=models.CASCADE,
        related_name="operation_prices",
        verbose_name=_("Operation Type")
    )
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        related_name="operation_prices",
        verbose_name=_("Vehicle Make")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE,
        null=True, blank=True, 
        related_name="operation_prices",
        verbose_name=_("Vehicle Model")
    )
    # Location-specific pricing
    franchise = models.ForeignKey(
        'setup.Franchise',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="operation_prices",
        verbose_name=_("Franchise")
    )
    company = models.ForeignKey(
        'setup.Company',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="operation_prices",
        verbose_name=_("Company")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="operation_prices",
        verbose_name=_("Service Center")
    )
    # Time range pricing
    year_from = models.PositiveIntegerField(
        _("Year From"),
        null=True, blank=True,
        help_text=_("Start year for this pricing")
    )
    year_to = models.PositiveIntegerField(
        _("Year To"),
        null=True, blank=True,
        help_text=_("End year for this pricing (leave blank for current)")
    )
    base_price = models.DecimalField(
        _("Base Price"), 
        max_digits=15, 
        decimal_places=2,
        help_text=_("Base price for this operation")
    )
    labor_hours = models.DecimalField(
        _("Labor Hours"),
        max_digits=8,
        decimal_places=2,
        default=0,
        help_text=_("Estimated labor hours required")
    )
    labor_rate = models.DecimalField(
        _("Labor Rate"),
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text=_("Hourly labor rate")
    )
    is_active = models.BooleanField(_("Is Active"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Operation Pricing")
        verbose_name_plural = _("Operation Pricings")
        unique_together = [
            ['tenant_id', 'operation_type', 'vehicle_make', 'vehicle_model', 'year_from', 'year_to', 
             'franchise', 'company', 'service_center']
        ]
        ordering = ['operation_type__name', 'vehicle_make__name', 'vehicle_model__name']
        
    def __str__(self):
        model_str = f" - {self.vehicle_model}" if self.vehicle_model else ""
        year_str = f" ({self.year_from}"
        if self.year_to:
            year_str += f"-{self.year_to}"
        else:
            year_str += "-present"
        year_str += ")"
        
        return f"{self.operation_type.name} - {self.vehicle_make}{model_str}{year_str}"
    
    @property
    def total_price(self):
        """
        Calculate total price including labor
        """
        labor_cost = self.labor_hours * self.labor_rate
        return self.base_price + labor_cost
    
    def clean(self):
        """
        Validate year ranges and location hierarchy
        """
        if self.year_from and self.year_to and self.year_from > self.year_to:
            raise ValidationError(_("Year from cannot be greater than year to"))
            
        # Validate location hierarchy (can't have service center without company, etc.)
        if self.service_center and not self.company:
            self.company = self.service_center.company
            
        if self.company and not self.franchise and self.company.franchise:
            self.franchise = self.company.franchise
            
        # Can't specify multiple levels in hierarchy that don't match
        if self.service_center and self.company and self.service_center.company != self.company:
            raise ValidationError(_("Service center must belong to the specified company"))
            
        if self.company and self.franchise and self.company.franchise != self.franchise:
            raise ValidationError(_("Company must belong to the specified franchise"))
            
        super().clean()
        

class PartPricing(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Pricing model for parts with operation and vehicle specific pricing
    """
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name="part_prices",
        verbose_name=_("Part/Item")
    )
    # Location-specific pricing
    franchise = models.ForeignKey(
        'setup.Franchise',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="part_prices",
        verbose_name=_("Franchise")
    )
    company = models.ForeignKey(
        'setup.Company',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="part_prices",
        verbose_name=_("Company")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="part_prices",
        verbose_name=_("Service Center")
    )
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',
        on_delete=models.CASCADE,
        related_name="part_prices",
        verbose_name=_("Operation Type"),
        null=True, blank=True,
        help_text=_("Optional - specific operation type pricing")
    )
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        related_name="part_prices",
        verbose_name=_("Vehicle Make"),
        null=True, blank=True,
        help_text=_("Optional - specific vehicle make pricing")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE,
        null=True, blank=True, 
        related_name="part_prices",
        verbose_name=_("Vehicle Model"),
        help_text=_("Optional - specific vehicle model pricing")
    )
    price = models.DecimalField(
        _("Price"), 
        max_digits=15, 
        decimal_places=2
    )
    is_special_pricing = models.BooleanField(
        _("Special Pricing"), 
        default=False,
        help_text=_("Indicates if this is a special price (discount/promotion)")
    )
    valid_from = models.DateField(
        _("Valid From"),
        null=True, blank=True,
        help_text=_("Start date for this pricing")
    )
    valid_to = models.DateField(
        _("Valid To"),
        null=True, blank=True,
        help_text=_("End date for this pricing")
    )
    is_active = models.BooleanField(_("Is Active"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Part Pricing")
        verbose_name_plural = _("Part Pricings")
        ordering = ['item__name', '-valid_from']
        
    def __str__(self):
        operation_str = f" - {self.operation_type.name}" if self.operation_type else ""
        make_str = f" - {self.vehicle_make.name}" if self.vehicle_make else ""
        model_str = f" - {self.vehicle_model.name}" if self.vehicle_model else ""
        location_str = ""
        
        if self.service_center:
            location_str = f" @ {self.service_center.name}"
        elif self.company:
            location_str = f" @ {self.company.name}"
        elif self.franchise:
            location_str = f" @ {self.franchise.name}"
        
        return f"{self.item.name}{operation_str}{make_str}{model_str}{location_str}: {self.price}"
    
    def clean(self):
        """
        Validate date ranges and location hierarchy
        """
        if self.valid_from and self.valid_to and self.valid_from > self.valid_to:
            raise ValidationError(_("Valid from date cannot be after valid to date"))
            
        # Validate location hierarchy (can't have service center without company, etc.)
        if self.service_center and not self.company:
            self.company = self.service_center.company
            
        if self.company and not self.franchise and self.company.franchise:
            self.franchise = self.company.franchise
            
        # Can't specify multiple levels in hierarchy that don't match
        if self.service_center and self.company and self.service_center.company != self.company:
            raise ValidationError(_("Service center must belong to the specified company"))
            
        if self.company and self.franchise and self.company.franchise != self.franchise:
            raise ValidationError(_("Company must belong to the specified franchise"))
            
        super().clean()
    
    @classmethod
    def get_price_for_operation(cls, item, operation_type=None, vehicle=None, service_center=None, company=None, franchise=None):
        """
        Get the appropriate price for an item based on operation, vehicle, and location
        
        Searches in order of specificity:
        1. Most specific: Exact match for item + operation + vehicle + service center
        2. Company level: Exact match for item + operation + vehicle + company
        3. Franchise level: Exact match for item + operation + vehicle + franchise
        4. Location agnostic: Exact match for item + operation + vehicle
        5. Fallback to previous search logic if no location-specific pricing found
        
        Args:
            item: The Item object
            operation_type: Optional WorkOrderType object
            vehicle: Optional Vehicle object
            service_center: Optional ServiceCenter object
            company: Optional Company object
            franchise: Optional Franchise object
            
        Returns:
            Decimal: The appropriate price
        """
        # Start with most specific query
        queryset = cls.objects.filter(item=item, is_active=True)
        
        # Add date filtering
        today = timezone.now().date()
        date_filter = (
            (models.Q(valid_from__isnull=True) | models.Q(valid_from__lte=today)) &
            (models.Q(valid_to__isnull=True) | models.Q(valid_to__gte=today))
        )
        queryset = queryset.filter(date_filter)
        
        # Try most specific match by location first if provided
        if service_center:
            company = service_center.company
            franchise = company.franchise if company else None

        # Build location filters from most specific to least specific
        if service_center and vehicle and operation_type:
            # Most specific: service center + operation + vehicle
            price = queryset.filter(
                service_center=service_center,
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model=vehicle.model
            ).first()
            if price:
                return price.price
                
        if company and vehicle and operation_type:
            # Company level: company + operation + vehicle
            price = queryset.filter(
                company=company,
                service_center__isnull=True,
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model=vehicle.model
            ).first()
            if price:
                return price.price
                
        if franchise and vehicle and operation_type:
            # Franchise level: franchise + operation + vehicle
            price = queryset.filter(
                franchise=franchise,
                company__isnull=True,
                service_center__isnull=True,
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model=vehicle.model
            ).first()
            if price:
                return price.price
        
        # Try location-specific but less specific on other dimensions
        if service_center:
            # Just service center and item
            price = queryset.filter(
                service_center=service_center,
                operation_type__isnull=True,
                vehicle_make__isnull=True
            ).first()
            if price:
                return price.price
                
        if company:
            # Just company and item
            price = queryset.filter(
                company=company,
                service_center__isnull=True,
                operation_type__isnull=True,
                vehicle_make__isnull=True
            ).first()
            if price:
                return price.price
                
        if franchise:
            # Just franchise and item
            price = queryset.filter(
                franchise=franchise,
                company__isnull=True,
                service_center__isnull=True,
                operation_type__isnull=True,
                vehicle_make__isnull=True
            ).first()
            if price:
                return price.price
                
        # Fall back to the existing search logic if no location-specific pricing found
        # Try most specific match first
        if operation_type and vehicle:
            # Try exact operation + make + model
            price = queryset.filter(
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model=vehicle.model
            ).first()
            if price:
                return price.price
                
            # Try operation + make (any model)
            price = queryset.filter(
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model__isnull=True
            ).first()
            if price:
                return price.price
                
        # ... [rest of the existing method remains unchanged] ...
        
        # If no special price found, return item's unit price
        return item.unit_price


# Valuation Methods Configuration
class InventoryValuationMethod(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Configuration for inventory valuation methods"""
    method_name = models.CharField(_("Method Name"), max_length=50, choices=[
        ('fifo', _('First In, First Out (FIFO)')),
        ('lifo', _('Last In, First Out (LIFO)')),
        ('average', _('Weighted Average Cost')),
        ('standard', _('Standard Cost')),
    ], default='fifo')
    is_active = models.BooleanField(_("Is Active"), default=True)
    is_default = models.BooleanField(_("Is Default"), default=False)
    description = models.TextField(_("Description"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Inventory Valuation Method")
        verbose_name_plural = _("Inventory Valuation Methods")
        unique_together = [['tenant_id', 'method_name']]
        
    def __str__(self):
        return f"{self.get_method_name_display()}"
        
    def save(self, *args, **kwargs):
        # If this is set as default, unset other defaults
        if self.is_default:
            InventoryValuationMethod.objects.filter(
                tenant_id=self.tenant_id,
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)
        super().save(*args, **kwargs)


# Slow Moving Stock Configuration
class SlowMovingStockConfig(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Configuration for slow moving stock analysis and automation"""
    
    # Analysis thresholds
    days_threshold = models.PositiveIntegerField(
        _('Days Threshold'),
        default=90,
        help_text=_('Number of days without movement to consider as slow-moving')
    )
    
    minimum_quantity_threshold = models.DecimalField(
        _('Minimum Quantity Threshold'),
        max_digits=15,
        decimal_places=4,
        default=0,
        help_text=_('Minimum quantity to be considered for slow-moving analysis')
    )
    
    # Discount automation
    auto_discount_enabled = models.BooleanField(
        _('Auto Discount Enabled'),
        default=False,
        help_text=_('Automatically apply discount to slow-moving items')
    )
    
    discount_percentage = models.DecimalField(
        _('Discount Percentage'),
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text=_('Discount percentage to apply')
    )
    
    # Target relationships (only one should be filled based on config_target)
    item = models.OneToOneField(
        'Item',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='slow_moving_config',
        verbose_name=_('Item')
    )
    
    classification = models.OneToOneField(
        'ItemClassification',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='slow_moving_config',
        verbose_name=_('Classification')
    )
    
    # Notification settings
    notify_managers = models.BooleanField(
        _('Notify Managers'),
        default=True,
        help_text=_('Send notifications to managers about slow-moving stock')
    )
    
    is_active = models.BooleanField(
        _('Is Active'),
        default=True
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Slow Moving Stock Configuration")
        verbose_name_plural = _("Slow Moving Stock Configurations")
        
    def __str__(self):
        return f"Slow Moving Config - {self.days_threshold} days"


# ItemBatch Model
class ItemBatch(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Batch tracking for inventory items"""
    
    BATCH_STATUS_CHOICES = [
        ('active', _('Active')),
        ('reserved', _('Reserved')),
        ('expired', _('Expired')),
        ('recalled', _('Recalled')),
        ('damaged', _('Damaged')),
    ]
    
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name='batches',
        verbose_name=_("Item")
    )
    batch_number = models.CharField(
        _("Batch Number"),
        max_length=100,
        db_index=True,
        help_text=_("Unique batch identifier")
    )
    status = models.CharField(
        _("Status"),
        max_length=20,
        choices=BATCH_STATUS_CHOICES,
        default='active'
    )
    
    # Quantity tracking
    initial_quantity = models.DecimalField(
        _("Initial Quantity"),
        max_digits=15,
        decimal_places=4,
        help_text=_("Original quantity received in this batch")
    )
    current_quantity = models.DecimalField(
        _("Current Quantity"),
        max_digits=15,
        decimal_places=4,
        default=0,
        help_text=_("Current available quantity in this batch")
    )
    reserved_quantity = models.DecimalField(
        _("Reserved Quantity"),
        max_digits=15,
        decimal_places=4,
        default=0,
        help_text=_("Quantity reserved for pending orders")
    )
    
    # Pricing information
    purchase_price = models.DecimalField(
        _("Purchase Price"),
        max_digits=15,
        decimal_places=4,
        null=True,
        blank=True,
        help_text=_("Price paid for this batch")
    )
    selling_price = models.DecimalField(
        _("Selling Price"),
        max_digits=15,
        decimal_places=4,
        null=True,
        blank=True,
        help_text=_("Recommended selling price for this batch")
    )
    
    # Date tracking
    manufactured_date = models.DateField(
        _("Manufactured Date"),
        null=True,
        blank=True,
        help_text=_("Date when this batch was manufactured")
    )
    received_date = models.DateField(
        _("Received Date"),
        help_text=_("Date when this batch was received")
    )
    expiry_date = models.DateField(
        _("Expiry Date"),
        null=True,
        blank=True,
        help_text=_("Date when this batch expires")
    )
    
    # Reference information
    supplier_batch_ref = models.CharField(
        _("Supplier Batch Reference"),
        max_length=100,
        blank=True,
        help_text=_("Supplier's reference for this batch")
    )
    purchase_order_ref = models.CharField(
        _("Purchase Order Reference"),
        max_length=100,
        blank=True,
        help_text=_("Purchase order reference for this batch")
    )
    warehouse_location = models.CharField(
        _("Warehouse Location"),
        max_length=100,
        blank=True,
        help_text=_("Specific location in warehouse (shelf, bin, etc.)")
    )
    
    # Additional information
    attributes = models.JSONField(
        _("Custom Attributes"),
        default=dict,
        blank=True,
        help_text=_("Additional batch-specific attributes")
    )
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item Batch")
        verbose_name_plural = _("Item Batches")
        unique_together = [['tenant_id', 'item', 'batch_number']]
        ordering = ['received_date', 'expiry_date']
        indexes = [
            models.Index(fields=['tenant_id', 'item', 'status']),
            models.Index(fields=['tenant_id', 'expiry_date']),
            models.Index(fields=['tenant_id', 'received_date']),
        ]
    
    def __str__(self):
        return f"{self.item.name} - Batch {self.batch_number}"
    
    @property
    def available_quantity(self):
        """Available quantity (current - reserved)"""
        return max(0, self.current_quantity - self.reserved_quantity)
    
    @property
    def is_expired(self):
        """Check if batch is expired"""
        if not self.expiry_date:
            return False
        return timezone.now().date() > self.expiry_date
    
    @property
    def days_until_expiry(self):
        """Days until expiry (negative if expired)"""
        if not self.expiry_date:
            return None
        return (self.expiry_date - timezone.now().date()).days
    
    @property
    def age_in_stock_days(self):
        """Number of days since received"""
        if not self.received_date:
            return 0
        return (timezone.now().date() - self.received_date).days
    
    @property
    def expiry_status(self):
        """Return expiry status as a string"""
        if not self.expiry_date:
            return _("No expiry")
        
        days = self.days_until_expiry
        if days < 0:
            return _("Expired")
        elif days <= 30:
            return _("Expiring soon")
        else:
            return _("Good")
    
    def is_expired_display(self):
        """Display expiry status for admin"""
        return self.is_expired
    is_expired_display.boolean = True
    
    def clean(self):
        """Validate batch data"""
        from django.core.exceptions import ValidationError
        super().clean()
        
        # Validate quantity relationships
        if self.current_quantity > self.initial_quantity:
            raise ValidationError(_('Current quantity cannot exceed initial quantity'))
        
        if self.reserved_quantity > self.current_quantity:
            raise ValidationError(_('Reserved quantity cannot exceed current quantity'))
        
        # Validate dates
        if self.manufactured_date and self.received_date:
            if self.manufactured_date > self.received_date:
                raise ValidationError(_('Manufactured date must be before or equal to received date'))
        
        if self.manufactured_date and self.expiry_date:
            if self.manufactured_date >= self.expiry_date:
                raise ValidationError(_('Manufactured date must be before expiry date'))
    
    def save(self, *args, **kwargs):
        self.clean()
        
        # Auto-update status based on expiry
        if self.is_expired and self.status == 'active':
            self.status = 'expired'
        
        super().save(*args, **kwargs)


# BatchMovement Model  
class BatchMovement(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Movement tracking for specific batches"""
    
    batch = models.ForeignKey(
        ItemBatch,
        on_delete=models.CASCADE,
        related_name='movements',
        verbose_name=_("Batch")
    )
    quantity = models.DecimalField(
        _("Quantity"),
        max_digits=15,
        decimal_places=4,
        help_text=_("Quantity moved from this batch")
    )
    movement_type = models.CharField(
        _("Movement Type"),
        max_length=50,
        choices=[
            ('in', _('Inbound')),
            ('out', _('Outbound')),
            ('adjustment', _('Adjustment')),
            ('transfer', _('Transfer')),
            ('reservation', _('Reservation')),
            ('release', _('Release Reservation')),
        ]
    )
    reference_number = models.CharField(
        _("Reference Number"),
        max_length=100,
        blank=True,
        help_text=_("Reference number for this movement")
    )
    notes = models.TextField(_("Notes"), blank=True)
    
    # Link to general movement record
    movement = models.ForeignKey(
        Movement,
        on_delete=models.CASCADE,
        related_name='batch_movements',
        null=True,
        blank=True,
        verbose_name=_("Movement"),
        help_text=_("Link to general movement record")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Batch Movement")
        verbose_name_plural = _("Batch Movements")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.batch.batch_number} - {self.get_movement_type_display()}: {self.quantity}"
    
    def clean(self):
        """Validate movement data"""
        from django.core.exceptions import ValidationError
        super().clean()
        
        # Validate quantity is positive for outbound movements
        if self.movement_type in ['out', 'reservation'] and self.quantity < 0:
            raise ValidationError(_('Quantity must be positive for outbound movements'))
        
        # Validate sufficient stock for outbound movements
        if self.movement_type in ['out', 'reservation']:
            available = self.batch.available_quantity if self.movement_type == 'out' else self.batch.current_quantity
            if self.quantity > available:
                raise ValidationError(_('Insufficient quantity available in batch'))


class SeasonalPricing(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Seasonal and time-based pricing adjustments
    """
    SEASON_CHOICES = [
        ('spring', _('Spring')),
        ('summer', _('Summer')),
        ('autumn', _('Autumn')),
        ('winter', _('Winter')),
        ('ramadan', _('Ramadan')),
        ('hajj', _('Hajj Season')),
        ('eid', _('Eid Period')),
        ('custom', _('Custom Period')),
    ]
    
    PRICING_TYPE_CHOICES = [
        ('operation', _('Operation Pricing')),
        ('part', _('Part Pricing')),
        ('both', _('Both Operations and Parts')),
    ]
    
    name = models.CharField(
        _("Pricing Period Name"),
        max_length=100,
        help_text=_("Name for this seasonal pricing period")
    )
    season_type = models.CharField(
        _("Season Type"),
        max_length=20,
        choices=SEASON_CHOICES,
        default='custom'
    )
    pricing_type = models.CharField(
        _("Pricing Type"),
        max_length=20,
        choices=PRICING_TYPE_CHOICES,
        default='both'
    )
    
    # Date ranges
    start_date = models.DateField(
        _("Start Date"),
        help_text=_("Start date for this pricing period")
    )
    end_date = models.DateField(
        _("End Date"),
        help_text=_("End date for this pricing period")
    )
    
    # Recurring pattern
    is_recurring = models.BooleanField(
        _("Is Recurring"),
        default=False,
        help_text=_("Does this pricing repeat annually?")
    )
    recurrence_start_month = models.PositiveIntegerField(
        _("Recurrence Start Month"),
        null=True, blank=True,
        help_text=_("Month when recurring period starts (1-12)")
    )
    recurrence_start_day = models.PositiveIntegerField(
        _("Recurrence Start Day"),
        null=True, blank=True,
        help_text=_("Day when recurring period starts (1-31)")
    )
    recurrence_end_month = models.PositiveIntegerField(
        _("Recurrence End Month"),
        null=True, blank=True,
        help_text=_("Month when recurring period ends (1-12)")
    )
    recurrence_end_day = models.PositiveIntegerField(
        _("Recurrence End Day"),
        null=True, blank=True,
        help_text=_("Day when recurring period ends (1-31)")
    )
    
    # Pricing adjustments
    adjustment_type = models.CharField(
        _("Adjustment Type"),
        max_length=20,
        choices=[
            ('percentage', _('Percentage Adjustment')),
            ('fixed', _('Fixed Amount Adjustment')),
            ('override', _('Price Override')),
        ],
        default='percentage'
    )
    adjustment_value = models.DecimalField(
        _("Adjustment Value"),
        max_digits=15,
        decimal_places=4,
        help_text=_("Percentage (-100 to 1000) or fixed amount")
    )
    
    # Location targeting
    franchise = models.ForeignKey(
        'setup.Franchise',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="seasonal_pricing",
        verbose_name=_("Franchise")
    )
    company = models.ForeignKey(
        'setup.Company',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="seasonal_pricing",
        verbose_name=_("Company")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="seasonal_pricing",
        verbose_name=_("Service Center")
    )
    
    # Item/Service targeting
    item_classification = models.ForeignKey(
        ItemClassification,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="seasonal_pricing",
        verbose_name=_("Item Classification")
    )
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="seasonal_pricing",
        verbose_name=_("Operation Type")
    )
    specific_item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="seasonal_pricing",
        verbose_name=_("Specific Item")
    )
    
    is_active = models.BooleanField(_("Is Active"), default=True)
    priority = models.PositiveIntegerField(
        _("Priority"),
        default=10,
        help_text=_("Higher numbers = higher priority when multiple rules apply")
    )
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Seasonal Pricing")
        verbose_name_plural = _("Seasonal Pricings")
        ordering = ['-priority', 'start_date']
        indexes = [
            models.Index(fields=['tenant_id', 'start_date', 'end_date']),
            models.Index(fields=['tenant_id', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"
    
    def is_active_for_date(self, target_date=None):
        """Check if pricing is active for a given date"""
        from django.utils import timezone
        if target_date is None:
            target_date = timezone.now().date()
        
        if not self.is_active:
            return False
        
        if self.is_recurring:
            # Check if current date falls within recurring pattern
            target_month = target_date.month
            target_day = target_date.day
            
            # Simple case: within same year
            if (self.recurrence_start_month < self.recurrence_end_month or 
                (self.recurrence_start_month == self.recurrence_end_month and 
                 self.recurrence_start_day <= self.recurrence_end_day)):
                
                return ((target_month > self.recurrence_start_month or 
                        (target_month == self.recurrence_start_month and target_day >= self.recurrence_start_day)) and
                       (target_month < self.recurrence_end_month or 
                        (target_month == self.recurrence_end_month and target_day <= self.recurrence_end_day)))
            
            # Cross-year case (e.g., Dec 15 - Feb 15)
            else:
                return ((target_month > self.recurrence_start_month or 
                        (target_month == self.recurrence_start_month and target_day >= self.recurrence_start_day)) or
                       (target_month < self.recurrence_end_month or 
                        (target_month == self.recurrence_end_month and target_day <= self.recurrence_end_day)))
        
        # Non-recurring: check specific date range
        return self.start_date <= target_date <= self.end_date
    
    def apply_adjustment(self, base_price):
        """Apply the seasonal adjustment to a base price"""
        if self.adjustment_type == 'percentage':
            return base_price * (1 + self.adjustment_value / 100)
        elif self.adjustment_type == 'fixed':
            return base_price + self.adjustment_value
        elif self.adjustment_type == 'override':
            return self.adjustment_value
        return base_price
    
    def clean(self):
        """Validate seasonal pricing data"""
        from django.core.exceptions import ValidationError
        super().clean()
        
        # Validate date ranges
        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError(_('Start date must be before or equal to end date'))
        
        # Validate recurring fields
        if self.is_recurring:
            required_fields = [
                self.recurrence_start_month, self.recurrence_start_day,
                self.recurrence_end_month, self.recurrence_end_day
            ]
            if any(field is None for field in required_fields):
                raise ValidationError(_('All recurrence fields are required when is_recurring is True'))
            
            # Validate month ranges
            for month in [self.recurrence_start_month, self.recurrence_end_month]:
                if month < 1 or month > 12:
                    raise ValidationError(_('Month must be between 1 and 12'))
            
            # Validate day ranges
            for day in [self.recurrence_start_day, self.recurrence_end_day]:
                if day < 1 or day > 31:
                    raise ValidationError(_('Day must be between 1 and 31'))


class ServicePricing(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Comprehensive service pricing that combines operations, parts, and contextual factors
    """
    PRICING_STRATEGY_CHOICES = [
        ('standard', _('Standard Pricing')),
        ('premium', _('Premium Service')),
        ('economy', _('Economy Service')),
        ('express', _('Express/Rush Service')),
        ('warranty', _('Warranty Work')),
        ('insurance', _('Insurance Claim')),
        ('fleet', _('Fleet Customer')),
        ('vip', _('VIP Customer')),
    ]
    
    name = models.CharField(
        _("Service Pricing Name"),
        max_length=200,
        help_text=_("Descriptive name for this pricing configuration")
    )
    pricing_strategy = models.CharField(
        _("Pricing Strategy"),
        max_length=20,
        choices=PRICING_STRATEGY_CHOICES,
        default='standard'
    )
    
    # Service definition
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',
        on_delete=models.CASCADE,
        related_name="service_pricing",
        verbose_name=_("Operation Type")
    )
    maintenance_schedule = models.ForeignKey(
        'work_orders.MaintenanceSchedule',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="service_pricing",
        verbose_name=_("Maintenance Schedule")
    )
    
    # Vehicle targeting
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="service_pricing",
        verbose_name=_("Vehicle Make")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="service_pricing",
        verbose_name=_("Vehicle Model")
    )
    year_from = models.PositiveIntegerField(
        _("Year From"),
        null=True, blank=True,
        help_text=_("Start year for vehicle compatibility")
    )
    year_to = models.PositiveIntegerField(
        _("Year To"),
        null=True, blank=True,
        help_text=_("End year for vehicle compatibility (leave blank for current)")
    )
    
    # Location targeting
    franchise = models.ForeignKey(
        'setup.Franchise',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="service_pricing",
        verbose_name=_("Franchise")
    )
    company = models.ForeignKey(
        'setup.Company',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="service_pricing",
        verbose_name=_("Company")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="service_pricing",
        verbose_name=_("Service Center")
    )
    
    # Pricing components
    base_service_price = models.DecimalField(
        _("Base Service Price"),
        max_digits=15,
        decimal_places=2,
        help_text=_("Base price for the service")
    )
    labor_rate_per_hour = models.DecimalField(
        _("Labor Rate per Hour"),
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text=_("Hourly labor rate for this service type")
    )
    estimated_labor_hours = models.DecimalField(
        _("Estimated Labor Hours"),
        max_digits=8,
        decimal_places=2,
        default=0,
        help_text=_("Estimated labor hours required")
    )
    
    # Parts pricing modifiers
    parts_markup_percentage = models.DecimalField(
        _("Parts Markup Percentage"),
        max_digits=8,
        decimal_places=4,
        default=0,
        help_text=_("Markup percentage applied to parts cost")
    )
    parts_handling_fee = models.DecimalField(
        _("Parts Handling Fee"),
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text=_("Fixed fee for parts handling and procurement")
    )
    
    # Service modifiers
    complexity_multiplier = models.DecimalField(
        _("Complexity Multiplier"),
        max_digits=5,
        decimal_places=2,
        default=1.0,
        help_text=_("Multiplier for service complexity (1.0 = standard)")
    )
    urgency_multiplier = models.DecimalField(
        _("Urgency Multiplier"),
        max_digits=5,
        decimal_places=2,
        default=1.0,
        help_text=_("Multiplier for urgent/express service (1.0 = standard)")
    )
    customer_tier_discount = models.DecimalField(
        _("Customer Tier Discount %"),
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text=_("Discount percentage for customer tier (0-100)")
    )
    
    # Validity period
    valid_from = models.DateField(
        _("Valid From"),
        help_text=_("Start date for this pricing")
    )
    valid_to = models.DateField(
        _("Valid To"),
        null=True, blank=True,
        help_text=_("End date for this pricing (leave blank for indefinite)")
    )
    
    # Automation settings
    auto_apply_seasonal = models.BooleanField(
        _("Auto Apply Seasonal Pricing"),
        default=True,
        help_text=_("Automatically apply seasonal pricing adjustments")
    )
    auto_calculate_parts = models.BooleanField(
        _("Auto Calculate Parts Cost"),
        default=True,
        help_text=_("Automatically calculate parts cost based on required materials")
    )
    
    is_active = models.BooleanField(_("Is Active"), default=True)
    priority = models.PositiveIntegerField(
        _("Priority"),
        default=10,
        help_text=_("Higher numbers = higher priority when multiple rules apply")
    )
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Service Pricing")
        verbose_name_plural = _("Service Pricings")
        ordering = ['-priority', 'operation_type__name']
        unique_together = [
            ['tenant_id', 'operation_type', 'vehicle_make', 'vehicle_model', 
             'year_from', 'year_to', 'service_center', 'pricing_strategy']
        ]
        indexes = [
            models.Index(fields=['tenant_id', 'operation_type', 'is_active']),
            models.Index(fields=['tenant_id', 'valid_from', 'valid_to']),
        ]
    
    def __str__(self):
        vehicle_info = ""
        if self.vehicle_make:
            vehicle_info = f" - {self.vehicle_make.name}"
            if self.vehicle_model:
                vehicle_info += f" {self.vehicle_model.name}"
        
        return f"{self.operation_type.name}{vehicle_info} ({self.get_pricing_strategy_display()})"
    
    def calculate_total_price(self, parts_cost=0, actual_labor_hours=None, apply_seasonal=True, target_date=None):
        """
        Calculate total service price including all components and modifiers
        """
        from django.utils import timezone
        
        if target_date is None:
            target_date = timezone.now().date()
        
        # Base calculations
        labor_hours = actual_labor_hours or self.estimated_labor_hours
        labor_cost = labor_hours * self.labor_rate_per_hour
        service_cost = self.base_service_price + labor_cost
        
        # Apply complexity and urgency multipliers
        service_cost *= self.complexity_multiplier * self.urgency_multiplier
        
        # Calculate parts total with markup and handling
        parts_total = parts_cost * (1 + self.parts_markup_percentage / 100) + self.parts_handling_fee
        
        # Subtotal before discounts
        subtotal = service_cost + parts_total
        
        # Apply seasonal pricing if enabled
        if apply_seasonal and self.auto_apply_seasonal:
            seasonal_adjustments = SeasonalPricing.objects.filter(
                tenant_id=self.tenant_id,
                is_active=True,
                pricing_type__in=['operation', 'both']
            ).filter(
                models.Q(operation_type=self.operation_type) |
                models.Q(operation_type__isnull=True)
            ).filter(
                models.Q(service_center=self.service_center) |
                models.Q(service_center__isnull=True)
            ).order_by('-priority')
            
            for adjustment in seasonal_adjustments:
                if adjustment.is_active_for_date(target_date):
                    subtotal = adjustment.apply_adjustment(subtotal)
                    break  # Apply only the highest priority adjustment
        
        return {
            'base_service_price': self.base_service_price,
            'labor_cost': labor_cost,
            'labor_hours': labor_hours,
            'parts_cost': parts_cost,
            'parts_markup': parts_cost * self.parts_markup_percentage / 100,
            'parts_handling_fee': self.parts_handling_fee,
            'parts_total': parts_total,
            'service_subtotal': service_cost,
            'customer_discount': subtotal * self.customer_tier_discount / 100 if self.customer_tier_discount > 0 else 0,
            'total_price': subtotal,
            'calculation_date': target_date
        }
    
    def is_valid_for_date(self, target_date=None):
        """Check if pricing is valid for a given date"""
        from django.utils import timezone
        
        if target_date is None:
            target_date = timezone.now().date()
        
        if not self.is_active:
            return False
        
        if target_date < self.valid_from:
            return False
        
        if self.valid_to and target_date > self.valid_to:
            return False
        
        return True
    
    def clean(self):
        """Validate service pricing data"""
        from django.core.exceptions import ValidationError
        super().clean()
        
        # Validate date ranges
        if self.valid_from and self.valid_to and self.valid_from > self.valid_to:
            raise ValidationError(_('Valid from date must be before or equal to valid to date'))
        
        # Validate year ranges
        if self.year_from and self.year_to and self.year_from > self.year_to:
            raise ValidationError(_('Year from must be before or equal to year to'))
        
        # Validate multipliers
        if self.complexity_multiplier <= 0:
            raise ValidationError(_('Complexity multiplier must be greater than 0'))
        
        if self.urgency_multiplier <= 0:
            raise ValidationError(_('Urgency multiplier must be greater than 0'))
        
        # Validate discount percentage
        if self.customer_tier_discount < 0 or self.customer_tier_discount > 100:
            raise ValidationError(_('Customer tier discount must be between 0 and 100'))


class PricingRule(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Advanced rule-based pricing with conditional logic
    """
    RULE_TYPE_CHOICES = [
        ('discount', _('Discount Rule')),
        ('surcharge', _('Surcharge Rule')),
        ('override', _('Price Override Rule')),
        ('bundle', _('Bundle Pricing Rule')),
    ]
    
    CONDITION_TYPE_CHOICES = [
        ('vehicle_age', _('Vehicle Age')),
        ('mileage', _('Vehicle Mileage')),
        ('customer_type', _('Customer Type')),
        ('order_value', _('Order Value')),
        ('service_history', _('Service History')),
        ('time_of_day', _('Time of Day')),
        ('day_of_week', _('Day of Week')),
        ('parts_quantity', _('Parts Quantity')),
        ('custom', _('Custom Condition')),
    ]
    
    name = models.CharField(
        _("Rule Name"),
        max_length=200,
        help_text=_("Descriptive name for this pricing rule")
    )
    rule_type = models.CharField(
        _("Rule Type"),
        max_length=20,
        choices=RULE_TYPE_CHOICES,
        default='discount'
    )
    
    # Rule conditions
    condition_type = models.CharField(
        _("Condition Type"),
        max_length=20,
        choices=CONDITION_TYPE_CHOICES,
        default='order_value'
    )
    condition_operator = models.CharField(
        _("Condition Operator"),
        max_length=20,
        choices=[
            ('gt', _('Greater Than')),
            ('gte', _('Greater Than or Equal')),
            ('lt', _('Less Than')),
            ('lte', _('Less Than or Equal')),
            ('eq', _('Equal To')),
            ('ne', _('Not Equal To')),
            ('in', _('In Range')),
            ('contains', _('Contains')),
        ],
        default='gte'
    )
    condition_value = models.CharField(
        _("Condition Value"),
        max_length=200,
        help_text=_("Value to compare against (can be number, text, or JSON)")
    )
    condition_value_max = models.CharField(
        _("Condition Max Value"),
        max_length=200,
        blank=True,
        help_text=_("Maximum value for range conditions")
    )
    
    # Rule action
    action_type = models.CharField(
        _("Action Type"),
        max_length=20,
        choices=[
            ('percentage', _('Percentage Adjustment')),
            ('fixed', _('Fixed Amount Adjustment')),
            ('override', _('Price Override')),
            ('multiply', _('Multiply by Factor')),
        ],
        default='percentage'
    )
    action_value = models.DecimalField(
        _("Action Value"),
        max_digits=15,
        decimal_places=4,
        help_text=_("Percentage, amount, or multiplier to apply")
    )
    
    # Targeting
    applies_to = models.CharField(
        _("Applies To"),
        max_length=20,
        choices=[
            ('total', _('Total Price')),
            ('labor', _('Labor Cost Only')),
            ('parts', _('Parts Cost Only')),
            ('service', _('Service Fee Only')),
        ],
        default='total'
    )
    
    # Location and service targeting
    franchise = models.ForeignKey(
        'setup.Franchise',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="pricing_rules",
        verbose_name=_("Franchise")
    )
    company = models.ForeignKey(
        'setup.Company',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="pricing_rules",
        verbose_name=_("Company")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="pricing_rules",
        verbose_name=_("Service Center")
    )
    
    # Validity and priority
    valid_from = models.DateField(_("Valid From"))
    valid_to = models.DateField(
        _("Valid To"),
        null=True, blank=True,
        help_text=_("Leave blank for indefinite validity")
    )
    is_active = models.BooleanField(_("Is Active"), default=True)
    priority = models.PositiveIntegerField(
        _("Priority"),
        default=10,
        help_text=_("Higher numbers = higher priority")
    )
    
    # Usage tracking
    usage_count = models.PositiveIntegerField(_("Usage Count"), default=0)
    max_usage = models.PositiveIntegerField(
        _("Max Usage"),
        null=True, blank=True,
        help_text=_("Maximum number of times this rule can be applied")
    )
    
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Pricing Rule")
        verbose_name_plural = _("Pricing Rules")
        ordering = ['-priority', 'name']
        indexes = [
            models.Index(fields=['tenant_id', 'is_active', 'priority']),
            models.Index(fields=['tenant_id', 'valid_from', 'valid_to']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_rule_type_display()})"
    
    def evaluate_condition(self, context):
        """
        Evaluate if the rule condition is met given a context
        
        Args:
            context: Dictionary containing relevant data for evaluation
        
        Returns:
            Boolean indicating if condition is met
        """
        import json
        from decimal import Decimal
        
        # Get the value from context
        context_value = context.get(self.condition_type)
        if context_value is None:
            return False
        
        try:
            # Parse condition value
            if self.condition_type in ['vehicle_age', 'mileage', 'order_value', 'parts_quantity']:
                condition_val = Decimal(str(self.condition_value))
                context_val = Decimal(str(context_value))
                
                if self.condition_operator == 'gt':
                    return context_val > condition_val
                elif self.condition_operator == 'gte':
                    return context_val >= condition_val
                elif self.condition_operator == 'lt':
                    return context_val < condition_val
                elif self.condition_operator == 'lte':
                    return context_val <= condition_val
                elif self.condition_operator == 'eq':
                    return context_val == condition_val
                elif self.condition_operator == 'ne':
                    return context_val != condition_val
                elif self.condition_operator == 'in' and self.condition_value_max:
                    max_val = Decimal(str(self.condition_value_max))
                    return condition_val <= context_val <= max_val
                    
            elif self.condition_type in ['customer_type', 'time_of_day', 'day_of_week']:
                if self.condition_operator == 'eq':
                    return str(context_value).lower() == str(self.condition_value).lower()
                elif self.condition_operator == 'ne':
                    return str(context_value).lower() != str(self.condition_value).lower()
                elif self.condition_operator == 'contains':
                    return str(self.condition_value).lower() in str(context_value).lower()
                elif self.condition_operator == 'in':
                    # Condition value should be a JSON list
                    valid_values = json.loads(self.condition_value)
                    return str(context_value) in valid_values
                    
        except (ValueError, json.JSONDecodeError, TypeError):
            return False
        
        return False
    
    def apply_action(self, price, component='total'):
        """
        Apply the rule action to a price
        
        Args:
            price: The price to modify
            component: Which component this applies to
            
        Returns:
            Modified price
        """
        if self.applies_to != 'total' and self.applies_to != component:
            return price
        
        if self.action_type == 'percentage':
            return price * (1 + self.action_value / 100)
        elif self.action_type == 'fixed':
            return price + self.action_value
        elif self.action_type == 'override':
            return self.action_value
        elif self.action_type == 'multiply':
            return price * self.action_value
        
        return price
    
    def is_valid_for_date(self, target_date=None):
        """Check if rule is valid for a given date"""
        from django.utils import timezone
        
        if target_date is None:
            target_date = timezone.now().date()
        
        if not self.is_active:
            return False
        
        if target_date < self.valid_from:
            return False
        
        if self.valid_to and target_date > self.valid_to:
            return False
        
        if self.max_usage and self.usage_count >= self.max_usage:
            return False
        
        return True
    
    def increment_usage(self):
        """Increment the usage counter"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class PricingHistory(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Historical pricing data for analytics and auditing
    """
    # Reference to work order or sales order
    work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="pricing_history",
        verbose_name=_("Work Order")
    )
    sales_order = models.ForeignKey(
        'sales.SalesOrder',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="pricing_history",
        verbose_name=_("Sales Order")
    )
    
    # Service and customer context
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="pricing_history",
        verbose_name=_("Operation Type")
    )
    customer = models.ForeignKey(
        'setup.Customer',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="pricing_history",
        verbose_name=_("Customer")
    )
    vehicle = models.ForeignKey(
        'setup.Vehicle',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="pricing_history",
        verbose_name=_("Vehicle")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="pricing_history",
        verbose_name=_("Service Center")
    )
    
    # Pricing breakdown
    base_service_price = models.DecimalField(
        _("Base Service Price"),
        max_digits=15,
        decimal_places=2,
        default=0
    )
    labor_cost = models.DecimalField(
        _("Labor Cost"),
        max_digits=15,
        decimal_places=2,
        default=0
    )
    parts_cost = models.DecimalField(
        _("Parts Cost"),
        max_digits=15,
        decimal_places=2,
        default=0
    )
    total_price = models.DecimalField(
        _("Total Price"),
        max_digits=15,
        decimal_places=2
    )
    
    # Applied adjustments
    seasonal_adjustment = models.DecimalField(
        _("Seasonal Adjustment"),
        max_digits=15,
        decimal_places=2,
        default=0
    )
    rules_adjustment = models.DecimalField(
        _("Rules Adjustment"),
        max_digits=15,
        decimal_places=2,
        default=0
    )
    discount_amount = models.DecimalField(
        _("Discount Amount"),
        max_digits=15,
        decimal_places=2,
        default=0
    )
    
    # Metadata
    pricing_strategy = models.CharField(
        _("Pricing Strategy"),
        max_length=50,
        blank=True
    )
    calculation_method = models.CharField(
        _("Calculation Method"),
        max_length=100,
        blank=True,
        help_text=_("How the price was calculated")
    )
    applied_rules = models.JSONField(
        _("Applied Rules"),
        default=list,
        blank=True,
        help_text=_("List of pricing rules that were applied")
    )
    calculation_context = models.JSONField(
        _("Calculation Context"),
        default=dict,
        blank=True,
        help_text=_("Context data used for pricing calculation")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Pricing History")
        verbose_name_plural = _("Pricing History")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['tenant_id', 'created_at']),
            models.Index(fields=['tenant_id', 'work_order']),
            models.Index(fields=['tenant_id', 'operation_type']),
            models.Index(fields=['tenant_id', 'service_center', 'created_at']),
        ]
    
    def __str__(self):
        ref = self.work_order.work_order_number if self.work_order else f"SO-{self.sales_order.order_number}" if self.sales_order else "Unknown"
        return f"Pricing for {ref} - ${self.total_price}"
    
    @property
    def profit_margin(self):
        """Calculate profit margin if cost data is available"""
        if self.total_price > 0 and (self.labor_cost + self.parts_cost) > 0:
            cost = self.labor_cost + self.parts_cost
            return ((self.total_price - cost) / self.total_price) * 100
        return None
    
    @classmethod
    def create_from_calculation(cls, tenant_id, calculation_result, **kwargs):
        """
        Create pricing history record from calculation result
        
        Args:
            tenant_id: Tenant ID
            calculation_result: Dictionary from ServicePricing.calculate_total_price()
            **kwargs: Additional fields for the history record
        """
        return cls.objects.create(
            tenant_id=tenant_id,
            base_service_price=calculation_result.get('base_service_price', 0),
            labor_cost=calculation_result.get('labor_cost', 0),
            parts_cost=calculation_result.get('parts_cost', 0),
            total_price=calculation_result.get('total_price', 0),
            discount_amount=calculation_result.get('customer_discount', 0),
            calculation_context=calculation_result,
            **kwargs
        )
