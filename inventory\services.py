from django.db import transaction, models
from django.utils import timezone
from decimal import Decimal
from typing import List, Dict, Optional, Tuple, Any
from django.utils.translation import gettext_lazy as _
import logging

from .models import Item, ItemBatch, Movement, MovementType, ServicePricing, SeasonalPricing, PricingRule, PricingHistory, OperationPricing, PartPricing
from work_orders.models import WorkOrder, WorkOrderMaterial
from purchases.models import PurchaseOrder, PurchaseOrderItem, Supplier

logger = logging.getLogger(__name__)


class InventoryAllocationService:
    """Service for handling inventory allocation and reservation"""
    
    @staticmethod
    def get_user_accessible_warehouses(user, service_center=None):
        """Get warehouses accessible to the user based on their role and service center"""
        from warehouse.models import Location
        from setup.models import ServiceCenter
        
        # Start with no warehouses
        accessible_warehouses = Location.objects.none()
        
        # Superuser can access all warehouses
        if user.is_superuser:
            accessible_warehouses = Location.objects.filter(is_active=True)
        elif hasattr(user, 'user_roles'):
            user_roles = user.user_roles.filter(is_active=True)
            primary_role = None
            
            # Find primary role
            for user_role in user_roles:
                if user_role.is_primary:
                    primary_role = user_role
                    break
            
            # If no primary role found, use the first active role
            if not primary_role and user_roles.exists():
                primary_role = user_roles.first()
            
            if primary_role:
                warehouse_ids = set()
                
                # Get warehouses based on role scope
                if primary_role.service_center:
                    # Service center role: only their warehouses
                    sc = primary_role.service_center
                    if sc.primary_warehouse:
                        warehouse_ids.add(sc.primary_warehouse.id)
                    warehouse_ids.update(sc.secondary_warehouses.values_list('id', flat=True))
                    
                elif primary_role.company:
                    # Company role: all warehouses from their service centers
                    company_scs = ServiceCenter.objects.filter(
                        company=primary_role.company,
                        is_active=True
                    )
                    for sc in company_scs:
                        if sc.primary_warehouse:
                            warehouse_ids.add(sc.primary_warehouse.id)
                        warehouse_ids.update(sc.secondary_warehouses.values_list('id', flat=True))
                        
                elif primary_role.franchise:
                    # Franchise role: all warehouses from their companies' service centers
                    franchise_scs = ServiceCenter.objects.filter(
                        company__franchise=primary_role.franchise,
                        is_active=True
                    )
                    for sc in franchise_scs:
                        if sc.primary_warehouse:
                            warehouse_ids.add(sc.primary_warehouse.id)
                        warehouse_ids.update(sc.secondary_warehouses.values_list('id', flat=True))
                
                # If specific service center is provided, filter further
                if service_center:
                    sc_warehouse_ids = set()
                    if service_center.primary_warehouse:
                        sc_warehouse_ids.add(service_center.primary_warehouse.id)
                    sc_warehouse_ids.update(service_center.secondary_warehouses.values_list('id', flat=True))
                    warehouse_ids = warehouse_ids.intersection(sc_warehouse_ids)
                
                accessible_warehouses = Location.objects.filter(
                    id__in=warehouse_ids,
                    is_active=True
                )
        
        return accessible_warehouses
    
    @staticmethod
    def allocate_stock_for_work_order(work_order: WorkOrder, user=None) -> Dict:
        """
        Allocate/reserve stock for a work order
        Returns allocation status and missing items
        """
        allocation_result = {
            'success': True,
            'allocated_items': [],
            'missing_items': [],
            'total_allocated': 0,
            'requires_purchase': False
        }
        
        with transaction.atomic():
            # Get accessible warehouses for the user
            accessible_warehouses = None
            if user:
                accessible_warehouses = InventoryAllocationService.get_user_accessible_warehouses(
                    user, work_order.service_center
                )
            
            for material in work_order.materials.all():
                item = material.item
                required_qty = material.quantity
                
                # Check available stock (including batches) in accessible warehouses
                batch_filter = {
                    'item': item,
                    'status': 'active', 
                    'current_quantity__gt': 0,
                    'tenant_id': work_order.tenant_id
                }
                
                # Filter by accessible warehouses if user context is provided
                if accessible_warehouses is not None:
                    from warehouse.models import ItemLocation
                    # Get item locations in accessible warehouses
                    accessible_item_locations = ItemLocation.objects.filter(
                        item=item,
                        location__in=accessible_warehouses,
                        quantity__gt=0
                    )
                    
                    # If we have specific warehouse locations, filter batches accordingly
                    if accessible_item_locations.exists():
                        warehouse_batch_ids = []
                        for item_loc in accessible_item_locations:
                            # Get batches in this warehouse location
                            location_batches = ItemBatch.objects.filter(
                                item=item,
                                warehouse_location=item_loc.location.id,
                                status='active',
                                current_quantity__gt=0
                            )
                            warehouse_batch_ids.extend(location_batches.values_list('id', flat=True))
                        
                        if warehouse_batch_ids:
                            batch_filter['id__in'] = warehouse_batch_ids
                        else:
                            # No batches in accessible warehouses
                            batch_filter['id__in'] = []
                
                available_batches = ItemBatch.objects.filter(**batch_filter).order_by('expiry_date', 'received_date')  # FIFO with expiry priority
                
                total_available = sum(batch.available_quantity for batch in available_batches)
                
                if total_available >= required_qty:
                    # Allocate from batches
                    remaining_to_allocate = required_qty
                    allocated_batches = []
                    
                    for batch in available_batches:
                        if remaining_to_allocate <= 0:
                            break
                            
                        allocate_from_batch = min(remaining_to_allocate, batch.available_quantity)
                        
                        # Reserve quantity in batch
                        batch.reserved_quantity += allocate_from_batch
                        batch.save()
                        
                        allocated_batches.append({
                            'batch_id': batch.id,
                            'batch_number': batch.batch_number,
                            'quantity': allocate_from_batch
                        })
                        
                        remaining_to_allocate -= allocate_from_batch
                    
                    allocation_result['allocated_items'].append({
                        'item': item,
                        'required_quantity': required_qty,
                        'allocated_quantity': required_qty,
                        'batches': allocated_batches
                    })
                    allocation_result['total_allocated'] += 1
                    
                else:
                    # Partial or no allocation possible
                    allocation_result['missing_items'].append({
                        'item': item,
                        'required_quantity': required_qty,
                        'available_quantity': total_available,
                        'shortage': required_qty - total_available
                    })
                    allocation_result['requires_purchase'] = True
                    allocation_result['success'] = False
        
        return allocation_result
    
    @staticmethod
    def release_allocation(work_order: WorkOrder) -> bool:
        """Release allocated stock when work order is cancelled"""
        try:
            with transaction.atomic():
                for material in work_order.materials.all():
                    # Find and release reserved quantities
                    allocated_batches = ItemBatch.objects.filter(
                        item=material.item,
                        reserved_quantity__gt=0,
                        tenant_id=work_order.tenant_id
                    )
                    
                    for batch in allocated_batches:
                        # Release reservation (this is simplified - in real app you'd track specific reservations)
                        batch.reserved_quantity = max(0, batch.reserved_quantity - material.quantity)
                        batch.save()
            return True
        except Exception:
            return False


class SmartSuggestionService:
    """Service for providing intelligent suggestions"""
    
    @staticmethod
    def suggest_parts_for_operation(operation_type: str, vehicle_make: str = None, vehicle_model: str = None, user=None, service_center=None) -> List[Dict]:
        """Suggest parts based on operation type and vehicle"""
        try:
            from work_orders.models import OperationCompatibility
            from setup.models import VehicleMake, VehicleModel
            
            # Get compatible items for this operation
            compatibilities = OperationCompatibility.objects.filter(
                operation_type__name__icontains=operation_type
            ).select_related('item')
            
            # Get accessible warehouses for filtering stock info
            accessible_warehouses = None
            if user:
                accessible_warehouses = InventoryAllocationService.get_user_accessible_warehouses(
                    user, service_center
                )
            
            suggestions = []
            for comp in compatibilities:
                item = comp.item
                
                # Calculate available stock in accessible warehouses
                available_stock = item.quantity  # Default to total stock
                warehouse_info = []
                
                if accessible_warehouses is not None:
                    from warehouse.models import ItemLocation
                    # Get stock in accessible warehouses
                    accessible_stock = ItemLocation.objects.filter(
                        item=item,
                        location__in=accessible_warehouses
                    ).aggregate(
                        total=models.Sum('quantity')
                    )['total'] or 0
                    
                    available_stock = float(accessible_stock)
                    
                    # Get warehouse breakdown
                    warehouse_locations = ItemLocation.objects.filter(
                        item=item,
                        location__in=accessible_warehouses,
                        quantity__gt=0
                    ).select_related('location')
                    
                    for item_loc in warehouse_locations:
                        warehouse_info.append({
                            'warehouse_id': str(item_loc.location.id),
                            'warehouse_name': item_loc.location.name,
                            'quantity': float(item_loc.quantity),
                            'is_primary': item_loc.location == service_center.primary_warehouse if service_center else False
                        })
                
                suggestions.append({
                    'item_id': str(item.id),
                    'item_name': item.name,
                    'item_sku': item.sku,
                    'suggested_quantity': float(comp.default_quantity),
                    'is_required': comp.is_required,
                    'is_common': comp.is_common,
                    'current_stock': available_stock,
                    'total_stock': float(item.quantity),  # Global stock for reference
                    'unit_price': float(item.unit_price) if item.unit_price else 0,
                    'availability_status': 'available' if available_stock > comp.default_quantity else 'low_stock',
                    'warehouse_availability': warehouse_info
                })
            
            return sorted(suggestions, key=lambda x: (x['is_required'], x['is_common']), reverse=True)
            
        except Exception as e:
            print(f"Error in suggest_parts_for_operation: {str(e)}")
            return []
    
    @staticmethod
    def get_alternative_parts(item_id: str) -> List[Dict]:
        """Get alternative/substitute parts"""
        try:
            from inventory.models import Item
            
            original_item = Item.objects.get(id=item_id)
            
            # Find similar items by classification or category
            alternatives = Item.objects.filter(
                classification=original_item.classification,
                is_active=True
            ).exclude(id=item_id)[:5]
            
            alt_list = []
            for alt in alternatives:
                alt_list.append({
                    'item_id': str(alt.id),
                    'item_name': alt.name,
                    'item_sku': alt.sku,
                    'current_stock': float(alt.quantity),
                    'unit_price': float(alt.unit_price) if alt.unit_price else 0,
                    'compatibility_score': 0.8  # Simplified scoring
                })
            
            return alt_list
        except Exception:
            return []


class AutoPurchaseService:
    """Service for automated purchase order creation"""
    
    @staticmethod
    def create_purchase_orders_for_shortages(missing_items: List[Dict], tenant_id) -> List[Dict]:
        """Create purchase orders for missing items"""
        purchase_orders = []
        
        try:
            with transaction.atomic():
                # Group items by preferred supplier
                supplier_groups = {}
                
                for missing_item in missing_items:
                    item = missing_item['item']
                    shortage_qty = missing_item['shortage']
                    
                    # Get preferred supplier (simplified - you might have supplier preferences)
                    preferred_supplier = Supplier.objects.filter(
                        tenant_id=tenant_id,
                        is_active=True
                    ).first()
                    
                    if preferred_supplier:
                        if preferred_supplier.id not in supplier_groups:
                            supplier_groups[preferred_supplier.id] = {
                                'supplier': preferred_supplier,
                                'items': []
                            }
                        
                        supplier_groups[preferred_supplier.id]['items'].append({
                            'item': item,
                            'quantity': shortage_qty
                        })
                
                # Create purchase orders
                for supplier_id, group in supplier_groups.items():
                    supplier = group['supplier']
                    
                    # Generate PO number
                    po_number = f"AUTO-PO-{timezone.now().strftime('%Y%m%d%H%M%S')}"
                    
                    po = PurchaseOrder.objects.create(
                        tenant_id=tenant_id,
                        order_number=po_number,
                        supplier=supplier,
                        order_date=timezone.now().date(),
                        status='draft',
                        notes='Auto-generated for work order requirements'
                    )
                    
                    total_amount = Decimal('0')
                    
                    for item_data in group['items']:
                        item = item_data['item']
                        quantity = item_data['quantity']
                        
                        # Use last purchase price or estimated price
                        unit_price = item.unit_price * Decimal('0.8') if item.unit_price else Decimal('10')
                        
                        PurchaseOrderItem.objects.create(
                            purchase_order=po,
                            item=item,
                            quantity=quantity,
                            unit_price=unit_price,
                            tenant_id=tenant_id
                        )
                        
                        total_amount += quantity * unit_price
                    
                    po.total_amount = total_amount
                    po.save()
                    
                    purchase_orders.append({
                        'purchase_order': po,
                        'total_amount': float(total_amount),
                        'item_count': len(group['items'])
                    })
        
        except Exception as e:
            print(f"Error creating auto purchase orders: {str(e)}")
        
        return purchase_orders


class WorkOrderWorkflowService:
    """Service for managing work order workflow automation"""
    
    @staticmethod
    def process_work_order_creation(work_order: WorkOrder) -> Dict:
        """Process complete work order creation with automation"""
        result = {
            'success': True,
            'allocation_result': None,
            'purchase_orders': [],
            'notifications': [],
            'errors': []
        }
        
        try:
            # Step 1: Allocate stock
            allocation_result = InventoryAllocationService.allocate_stock_for_work_order(work_order)
            result['allocation_result'] = allocation_result
            
            # Step 2: Create purchase orders for missing items if needed
            if allocation_result['requires_purchase']:
                purchase_orders = AutoPurchaseService.create_purchase_orders_for_shortages(
                    allocation_result['missing_items'],
                    work_order.tenant_id
                )
                result['purchase_orders'] = purchase_orders
                
                # Add notification about purchase orders
                result['notifications'].append({
                    'type': 'warning',
                    'message': f'Created {len(purchase_orders)} purchase orders for missing items'
                })
            
            # Step 3: Update work order status if fully allocated
            if allocation_result['success']:
                work_order.status = 'ready_to_start'
                work_order.save()
                
                result['notifications'].append({
                    'type': 'success', 
                    'message': 'Work order ready to start - all materials allocated'
                })
            else:
                result['notifications'].append({
                    'type': 'info',
                    'message': f'Work order created - waiting for {len(allocation_result["missing_items"])} items'
                })
        
        except Exception as e:
            result['success'] = False
            result['errors'].append(str(e))
        
        return result
    
    @staticmethod
    def consume_materials(work_order: WorkOrder, material_consumptions: List[Dict]) -> Dict:
        """Process material consumption with batch tracking"""
        result = {
            'success': True,
            'consumed_items': [],
            'movements_created': [],
            'errors': []
        }
        
        try:
            with transaction.atomic():
                for consumption in material_consumptions:
                    material_id = consumption['material_id']
                    consumed_quantity = consumption['quantity']
                    batch_id = consumption.get('batch_id')
                    
                    material = WorkOrderMaterial.objects.get(id=material_id)
                    
                    if batch_id:
                        # Consume from specific batch
                        batch = ItemBatch.objects.get(id=batch_id)
                        
                        if batch.current_quantity >= consumed_quantity:
                            # Update batch quantities
                            batch.current_quantity -= consumed_quantity
                            batch.reserved_quantity = max(0, batch.reserved_quantity - consumed_quantity)
                            batch.save()
                            
                            # Create movement record
                            movement = Movement.objects.create(
                                tenant_id=work_order.tenant_id,
                                item=material.item,
                                movement_type='sale',
                                quantity=-consumed_quantity,
                                reference_number=work_order.work_order_number,
                                notes=f'Consumed in work order {work_order.work_order_number}'
                            )
                            
                            result['movements_created'].append(movement)
                            result['consumed_items'].append({
                                'material': material,
                                'quantity': consumed_quantity,
                                'batch': batch
                            })
                        else:
                            result['errors'].append(f'Insufficient quantity in batch {batch.batch_number}')
                    
                    # Mark material as consumed
                    material.is_consumed = True
                    material.save()
        
        except Exception as e:
            result['success'] = False
            result['errors'].append(str(e))
        
        return result 


class DynamicPricingEngine:
    """
    Advanced pricing engine that calculates dynamic prices based on multiple factors:
    - Vehicle type, make, model, and year
    - Service center location and franchise
    - Seasonal and time-based adjustments
    - Customer tier and service strategy
    - Rule-based conditional pricing
    """
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.calculation_log = []
    
    def calculate_service_price(
        self,
        operation_type_id: str,
        vehicle=None,
        service_center=None,
        customer=None,
        parts_list: List[Dict] = None,
        pricing_strategy: str = 'standard',
        actual_labor_hours: Optional[float] = None,
        target_date=None,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive service pricing
        
        Args:
            operation_type_id: Work order operation type
            vehicle: Vehicle instance
            service_center: Service center instance
            customer: Customer instance
            parts_list: List of parts with quantities [{'item_id': str, 'quantity': float}]
            pricing_strategy: Pricing strategy ('standard', 'premium', 'economy', etc.)
            actual_labor_hours: Actual labor hours (if different from estimate)
            target_date: Date for pricing calculation
            context: Additional context for rule evaluation
            
        Returns:
            Dictionary with pricing breakdown and totals
        """
        if target_date is None:
            target_date = timezone.now().date()
        
        if context is None:
            context = {}
        
        self.calculation_log = []
        
        try:
            # 1. Find the best matching service pricing
            service_pricing = self._find_best_service_pricing(
                operation_type_id, vehicle, service_center, pricing_strategy, target_date
            )
            
            if not service_pricing:
                # Fallback to legacy operation pricing
                return self._fallback_to_legacy_pricing(
                    operation_type_id, vehicle, service_center, parts_list, target_date
                )
            
            # 2. Calculate parts cost
            parts_cost = 0
            parts_breakdown = []
            
            if parts_list and service_pricing.auto_calculate_parts:
                parts_cost, parts_breakdown = self._calculate_parts_cost(
                    parts_list, vehicle, service_center, operation_type_id, target_date
                )
            
            # 3. Calculate base service price using the service pricing model
            base_calculation = service_pricing.calculate_total_price(
                parts_cost=parts_cost,
                actual_labor_hours=actual_labor_hours,
                apply_seasonal=True,
                target_date=target_date
            )
            
            # 4. Build context for rule evaluation
            pricing_context = self._build_pricing_context(
                vehicle, customer, service_center, base_calculation, context
            )
            
            # 5. Apply pricing rules
            rules_applied = []
            final_calculation = self._apply_pricing_rules(
                base_calculation, pricing_context, service_center, operation_type_id, 
                target_date, rules_applied
            )
            
            # 6. Prepare final result
            result = {
                'pricing_strategy': pricing_strategy,
                'service_pricing_id': str(service_pricing.id),
                'base_calculation': base_calculation,
                'final_calculation': final_calculation,
                'parts_breakdown': parts_breakdown,
                'rules_applied': rules_applied,
                'calculation_log': self.calculation_log,
                'calculation_date': target_date,
                'context': pricing_context,
                'success': True
            }
            
            # 7. Create pricing history record
            self._create_pricing_history(result, operation_type_id, vehicle, customer, service_center)
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating service price: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'calculation_log': self.calculation_log
            }
    
    def _find_best_service_pricing(
        self, operation_type_id: str, vehicle, service_center, pricing_strategy: str, target_date
    ) -> Optional[ServicePricing]:
        """Find the best matching service pricing configuration"""
        
        query = ServicePricing.objects.filter(
            tenant_id=self.tenant_id,
            operation_type_id=operation_type_id,
            is_active=True,
            pricing_strategy=pricing_strategy
        ).filter(
            models.Q(valid_from__lte=target_date) &
            (models.Q(valid_to__isnull=True) | models.Q(valid_to__gte=target_date))
        )
        
        # Apply location filters in order of specificity
        location_queries = []
        
        if service_center:
            location_queries.append(
                query.filter(service_center=service_center)
            )
            if service_center.company:
                location_queries.append(
                    query.filter(company=service_center.company, service_center__isnull=True)
                )
                if service_center.company.franchise:
                    location_queries.append(
                        query.filter(
                            franchise=service_center.company.franchise,
                            company__isnull=True,
                            service_center__isnull=True
                        )
                    )
        
        # Add query for no location restrictions
        location_queries.append(
            query.filter(
                franchise__isnull=True,
                company__isnull=True,
                service_center__isnull=True
            )
        )
        
        # Apply vehicle filters for each location query
        for location_query in location_queries:
            vehicle_queries = []
            
            if vehicle:
                # Try to get vehicle make/model objects
                vehicle_make_obj = None
                vehicle_model_obj = None
                
                # First try to use standard_make/standard_model if available
                if hasattr(vehicle, 'standard_make') and vehicle.standard_make:
                    vehicle_make_obj = vehicle.standard_make
                elif hasattr(vehicle, 'make') and vehicle.make:
                    # Fall back to looking up by name
                    try:
                        from setup.models import VehicleMake
                        vehicle_make_obj = VehicleMake.objects.filter(
                            tenant_id=self.tenant_id, name=vehicle.make
                        ).first()
                    except:
                        pass
                
                if hasattr(vehicle, 'standard_model') and vehicle.standard_model:
                    vehicle_model_obj = vehicle.standard_model
                elif hasattr(vehicle, 'model') and vehicle.model and vehicle_make_obj:
                    # Fall back to looking up by name
                    try:
                        from setup.models import VehicleModel
                        vehicle_model_obj = VehicleModel.objects.filter(
                            tenant_id=self.tenant_id, 
                            make=vehicle_make_obj,
                            name=vehicle.model
                        ).first()
                    except:
                        pass
                
                # Exact vehicle make and model match
                if vehicle_make_obj and vehicle_model_obj:
                    vehicle_queries.append(
                        location_query.filter(
                            vehicle_make=vehicle_make_obj,
                            vehicle_model=vehicle_model_obj
                        ).filter(
                            models.Q(year_from__isnull=True) | models.Q(year_from__lte=vehicle.year or 9999)
                        ).filter(
                            models.Q(year_to__isnull=True) | models.Q(year_to__gte=vehicle.year or 1900)
                        )
                    )
                
                # Vehicle make only match
                if vehicle_make_obj:
                    vehicle_queries.append(
                        location_query.filter(
                            vehicle_make=vehicle_make_obj,
                            vehicle_model__isnull=True
                        )
                    )
            
            # No vehicle restrictions
            vehicle_queries.append(
                location_query.filter(
                    vehicle_make__isnull=True,
                    vehicle_model__isnull=True
                )
            )
            
            # Try each vehicle query and return the first match
            for vehicle_query in vehicle_queries:
                pricing = vehicle_query.order_by('-priority').first()
                if pricing:
                    self.calculation_log.append(
                        f"Found service pricing: {pricing.name} (Priority: {pricing.priority})"
                    )
                    return pricing
        
        self.calculation_log.append("No matching service pricing found")
        return None
    
    def _calculate_parts_cost(
        self, parts_list: List[Dict], vehicle, service_center, operation_type_id, target_date
    ) -> tuple[float, List[Dict]]:
        """Calculate total parts cost with dynamic pricing"""
        
        total_cost = 0
        parts_breakdown = []
        
        for part_info in parts_list:
            item_id = part_info.get('item_id')
            quantity = Decimal(str(part_info.get('quantity', 1)))
            
            try:
                item = Item.objects.get(tenant_id=self.tenant_id, id=item_id)
                
                # Get dynamic part pricing
                part_price = self._get_part_price(
                    item, vehicle, service_center, operation_type_id, target_date
                )
                
                part_total = part_price * quantity
                total_cost += float(part_total)
                
                parts_breakdown.append({
                    'item_id': item_id,
                    'item_name': item.name,
                    'item_sku': item.sku,
                    'quantity': float(quantity),
                    'unit_price': float(part_price),
                    'total_price': float(part_total)
                })
                
                self.calculation_log.append(
                    f"Part: {item.name} - Qty: {quantity} × ${part_price} = ${part_total}"
                )
                
            except Item.DoesNotExist:
                self.calculation_log.append(f"Item not found: {item_id}")
                continue
        
        return total_cost, parts_breakdown
    
    def _get_part_price(
        self, item: Item, vehicle, service_center, operation_type_id, target_date
    ) -> Decimal:
        """Get dynamic price for a specific part"""
        
        # Try to find specific part pricing - first get the operation type object
        operation_type = None
        if operation_type_id:
            try:
                from work_orders.models import WorkOrderType
                operation_type = WorkOrderType.objects.get(id=operation_type_id, tenant_id=self.tenant_id)
            except WorkOrderType.DoesNotExist:
                pass
        
        part_pricing = PartPricing.get_price_for_operation(
            item=item,
            operation_type=operation_type,
            vehicle=vehicle,
            service_center=service_center
        )
        
        if part_pricing:
            price = part_pricing
            self.calculation_log.append(f"Using dynamic part pricing for {item.name}: ${price}")
        else:
            # Fallback to item's unit price
            price = item.unit_price
            self.calculation_log.append(f"Using base unit price for {item.name}: ${price}")
        
        # Apply seasonal pricing for parts
        seasonal_adjustments = SeasonalPricing.objects.filter(
            tenant_id=self.tenant_id,
            is_active=True,
            pricing_type__in=['part', 'both']
        ).filter(
            models.Q(specific_item=item) |
            models.Q(item_classification=item.classification) |
            models.Q(specific_item__isnull=True, item_classification__isnull=True)
        ).filter(
            models.Q(service_center=service_center) |
            models.Q(service_center__isnull=True)
        ).order_by('-priority')
        
        for adjustment in seasonal_adjustments:
            if adjustment.is_active_for_date(target_date):
                adjusted_price = adjustment.apply_adjustment(price)
                self.calculation_log.append(
                    f"Applied seasonal adjustment '{adjustment.name}': ${price} → ${adjusted_price}"
                )
                return adjusted_price
        
        return price
    
    def _build_pricing_context(
        self, vehicle, customer, service_center, base_calculation, additional_context
    ) -> Dict[str, Any]:
        """Build context for pricing rule evaluation"""
        
        context = {
            'order_value': base_calculation.get('total_price', 0),
            'parts_cost': base_calculation.get('parts_cost', 0),
            'labor_cost': base_calculation.get('labor_cost', 0),
            'parts_quantity': sum(
                part.get('quantity', 0) for part in base_calculation.get('parts_breakdown', [])
            ),
            'time_of_day': timezone.now().strftime('%H:%M'),
            'day_of_week': timezone.now().strftime('%A').lower(),
        }
        
        if vehicle:
            current_year = timezone.now().year
            vehicle_year = getattr(vehicle, 'year', current_year)
            context.update({
                'vehicle_age': current_year - vehicle_year,
                'mileage': getattr(vehicle, 'current_mileage', 0),
                'vehicle_make': getattr(vehicle, 'make', ''),
                'vehicle_model': getattr(vehicle, 'model', ''),
            })
        
        if customer:
            context.update({
                'customer_type': getattr(customer, 'customer_type', 'individual'),
                'customer_tier': getattr(customer, 'tier', 'standard'),
            })
        
        # Add any additional context
        context.update(additional_context)
        
        return context
    
    def _apply_pricing_rules(
        self, base_calculation, context, service_center, operation_type_id, target_date, rules_applied
    ) -> Dict[str, Any]:
        """Apply pricing rules to the base calculation"""
        
        # Get applicable pricing rules
        rules = PricingRule.objects.filter(
            tenant_id=self.tenant_id,
            is_active=True
        ).filter(
            models.Q(service_center=service_center) |
            models.Q(service_center__isnull=True)
        ).filter(
            models.Q(operation_type_id=operation_type_id) |
            models.Q(operation_type__isnull=True)
        ).filter(
            models.Q(valid_from__lte=target_date) &
            (models.Q(valid_to__isnull=True) | models.Q(valid_to__gte=target_date))
        ).order_by('-priority')
        
        # Start with base calculation
        result = base_calculation.copy()
        
        for rule in rules:
            if not rule.is_valid_for_date(target_date):
                continue
            
            # Evaluate rule condition
            if rule.evaluate_condition(context):
                # Apply rule to appropriate component
                component_map = {
                    'total': 'total_price',
                    'labor': 'labor_cost',
                    'parts': 'parts_cost',
                    'service': 'service_subtotal'
                }
                
                component_key = component_map.get(rule.applies_to, 'total_price')
                original_value = result.get(component_key, 0)
                
                if original_value > 0:
                    new_value = rule.apply_action(original_value, rule.applies_to)
                    adjustment = new_value - original_value
                    
                    result[component_key] = new_value
                    
                    # Update total if we modified a component
                    if rule.applies_to != 'total':
                        result['total_price'] = (
                            result.get('service_subtotal', 0) + 
                            result.get('parts_cost', 0)
                        )
                    
                    # Track the rule application
                    rule_info = {
                        'rule_id': str(rule.id),
                        'rule_name': rule.name,
                        'rule_type': rule.rule_type,
                        'applies_to': rule.applies_to,
                        'adjustment': float(adjustment),
                        'original_value': float(original_value),
                        'new_value': float(new_value)
                    }
                    rules_applied.append(rule_info)
                    
                    self.calculation_log.append(
                        f"Applied rule '{rule.name}': {rule.applies_to} ${original_value} → ${new_value}"
                    )
                    
                    # Increment rule usage
                    rule.increment_usage()
        
        return result
    
    def _fallback_to_legacy_pricing(
        self, operation_type_id, vehicle, service_center, parts_list, target_date
    ) -> Dict[str, Any]:
        """Fallback to legacy OperationPricing when ServicePricing is not available"""
        
        self.calculation_log.append("Using legacy pricing fallback")
        
        # Find legacy operation pricing
        operation_pricing = None
        if vehicle and service_center:
            try:
                # Get vehicle make object
                vehicle_make_obj = None
                if hasattr(vehicle, 'standard_make') and vehicle.standard_make:
                    vehicle_make_obj = vehicle.standard_make
                elif hasattr(vehicle, 'make') and vehicle.make:
                    try:
                        from setup.models import VehicleMake
                        vehicle_make_obj = VehicleMake.objects.filter(
                            tenant_id=self.tenant_id, name=vehicle.make
                        ).first()
                    except:
                        pass
                
                # Query for operation pricing
                query = OperationPricing.objects.filter(
                    tenant_id=self.tenant_id,
                    operation_type_id=operation_type_id,
                    is_active=True
                ).filter(
                    models.Q(service_center=service_center) |
                    models.Q(service_center__isnull=True)
                )
                
                if vehicle_make_obj:
                    query = query.filter(
                        models.Q(vehicle_make=vehicle_make_obj) |
                        models.Q(vehicle_make__isnull=True)
                    )
                else:
                    query = query.filter(vehicle_make__isnull=True)
                
                operation_pricing = query.first()
            except:
                pass
        
        if operation_pricing:
            labor_cost = operation_pricing.total_price
            self.calculation_log.append(f"Found legacy operation pricing: ${labor_cost}")
        else:
            # Use basic fallback
            labor_cost = 100  # Default labor cost
            self.calculation_log.append(f"Using default labor cost: ${labor_cost}")
        
        # Calculate parts cost
        parts_cost = 0
        parts_breakdown = []
        
        if parts_list:
            parts_cost, parts_breakdown = self._calculate_parts_cost(
                parts_list, vehicle, service_center, operation_type_id, target_date
            )
        
        total_price = labor_cost + parts_cost
        
        return {
            'pricing_strategy': 'legacy',
            'service_pricing_id': None,
            'base_calculation': {
                'base_service_price': labor_cost,
                'labor_cost': labor_cost,
                'parts_cost': parts_cost,
                'total_price': total_price,
                'calculation_date': target_date
            },
            'final_calculation': {
                'base_service_price': labor_cost,
                'labor_cost': labor_cost,
                'parts_cost': parts_cost,
                'total_price': total_price
            },
            'parts_breakdown': parts_breakdown,
            'rules_applied': [],
            'calculation_log': self.calculation_log,
            'calculation_date': target_date,
            'success': True,
            'is_legacy': True
        }
    
    def _create_pricing_history(
        self, result, operation_type_id, vehicle, customer, service_center
    ):
        """Create a pricing history record for audit purposes"""
        
        try:
            PricingHistory.create_from_calculation(
                tenant_id=self.tenant_id,
                calculation_result=result['final_calculation'],
                operation_type_id=operation_type_id,
                vehicle=vehicle,
                customer=customer,
                service_center=service_center,
                pricing_strategy=result.get('pricing_strategy'),
                calculation_method='dynamic_pricing_engine',
                applied_rules=[rule['rule_name'] for rule in result['rules_applied']]
            )
            self.calculation_log.append("Created pricing history record")
        except Exception as e:
            logger.error(f"Failed to create pricing history: {str(e)}")
            self.calculation_log.append(f"Failed to create pricing history: {str(e)}")


class InventoryIntegrationService:
    """
    Service for real-time inventory integration with work orders
    """
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
    
    def validate_stock_availability(
        self, parts_list: List[Dict], warehouse_location=None
    ) -> Dict[str, Any]:
        """
        Validate if required parts are available in stock
        
        Args:
            parts_list: List of parts [{'item_id': str, 'quantity': float}]
            warehouse_location: Specific warehouse to check
            
        Returns:
            Dictionary with availability status and details
        """
        
        availability_report = {
            'all_available': True,
            'items': [],
            'missing_items': [],
            'low_stock_items': [],
            'total_items': len(parts_list)
        }
        
        for part_info in parts_list:
            item_id = part_info.get('item_id')
            required_quantity = Decimal(str(part_info.get('quantity', 1)))
            
            try:
                item = Item.objects.get(tenant_id=self.tenant_id, id=item_id)
                
                # Check current stock
                current_stock = item.current_stock
                available = current_stock >= required_quantity
                
                item_status = {
                    'item_id': item_id,
                    'item_name': item.name,
                    'item_sku': item.sku,
                    'required_quantity': float(required_quantity),
                    'current_stock': float(current_stock),
                    'available': available,
                    'shortage': float(max(0, required_quantity - current_stock))
                }
                
                availability_report['items'].append(item_status)
                
                if not available:
                    availability_report['all_available'] = False
                    availability_report['missing_items'].append(item_status)
                
                # Check if item is low stock
                if current_stock <= item.min_stock_level:
                    availability_report['low_stock_items'].append(item_status)
                
            except Item.DoesNotExist:
                item_status = {
                    'item_id': item_id,
                    'item_name': 'Unknown Item',
                    'required_quantity': float(required_quantity),
                    'available': False,
                    'error': 'Item not found'
                }
                availability_report['items'].append(item_status)
                availability_report['missing_items'].append(item_status)
                availability_report['all_available'] = False
        
        return availability_report
    
    def create_parts_request(
        self, work_order, missing_items: List[Dict], priority: str = 'medium'
    ) -> Dict[str, Any]:
        """
        Create automated parts request for missing inventory
        
        Args:
            work_order: WorkOrder instance
            missing_items: List of missing items from validate_stock_availability
            priority: Request priority ('low', 'medium', 'high', 'critical')
            
        Returns:
            Dictionary with request creation results
        """
        
        from notifications.services import NotificationService
        from work_orders.models import WorkOrderTransferRequest
        
        created_requests = []
        
        for item_info in missing_items:
            try:
                # Create transfer request if warehouse exists
                if work_order.service_center and hasattr(work_order.service_center, 'primary_warehouse'):
                    item = Item.objects.get(tenant_id=self.tenant_id, id=item_info['item_id'])
                    
                    # Generate transfer number
                    import uuid
                    transfer_number = f"WO-{work_order.work_order_number}-{uuid.uuid4().hex[:8].upper()}"
                    
                    transfer_request = WorkOrderTransferRequest.objects.create(
                        tenant_id=self.tenant_id,
                        work_order=work_order,
                        transfer_number=transfer_number,
                        item=item,
                        source_warehouse=None,  # To be assigned by warehouse staff
                        destination_warehouse=work_order.service_center.primary_warehouse,
                        requested_quantity=item_info['required_quantity'],
                        status='pending',
                        priority=priority,
                        requested_by=work_order.assigned_technician,
                        notes=f"Auto-generated request for work order {work_order.work_order_number}"
                    )
                    
                    created_requests.append({
                        'transfer_request_id': str(transfer_request.id),
                        'item_name': item.name,
                        'quantity': item_info['required_quantity'],
                        'status': 'created'
                    })
                    
                    # Send notification to warehouse staff
                    notification_service = NotificationService(self.tenant_id)
                    notification_service.notify_warehouse_staff_parts_request(
                        transfer_request, work_order
                    )
                
            except Exception as e:
                created_requests.append({
                    'item_name': item_info.get('item_name', 'Unknown'),
                    'quantity': item_info['required_quantity'],
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'success': len([r for r in created_requests if r['status'] == 'created']) > 0,
            'total_requests': len(created_requests),
            'successful_requests': len([r for r in created_requests if r['status'] == 'created']),
            'failed_requests': len([r for r in created_requests if r['status'] == 'failed']),
            'requests': created_requests
        } 


class AutomatedPartsRequestService:
    """
    Service for proactive automated parts request management
    """
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
    
    def monitor_and_create_requests(self, service_center=None, check_low_stock=True, 
                                   check_expiring_items=True, check_work_order_forecasts=True):
        """
        Monitor inventory levels and create automated parts requests
        
        Args:
            service_center: Specific service center to monitor (None for all)
            check_low_stock: Check for low stock items
            check_expiring_items: Check for expiring items that need replacement
            check_work_order_forecasts: Check upcoming work orders for parts needs
            
        Returns:
            Dictionary with monitoring results and created requests
        """
        results = {
            'low_stock_requests': [],
            'expiring_items_requests': [],
            'forecast_requests': [],
            'total_requests_created': 0,
            'monitoring_summary': {}
        }
        
        try:
            if check_low_stock:
                low_stock_result = self._check_low_stock_items(service_center)
                results['low_stock_requests'] = low_stock_result
                results['total_requests_created'] += len(low_stock_result)
            
            if check_expiring_items:
                expiring_result = self._check_expiring_items(service_center)
                results['expiring_items_requests'] = expiring_result
                results['total_requests_created'] += len(expiring_result)
            
            if check_work_order_forecasts:
                forecast_result = self._forecast_work_order_needs(service_center)
                results['forecast_requests'] = forecast_result
                results['total_requests_created'] += len(forecast_result)
            
            # Generate monitoring summary
            results['monitoring_summary'] = self._generate_monitoring_summary(results)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in automated parts monitoring: {str(e)}")
            return {
                'error': str(e),
                'total_requests_created': 0
            }
    
    def _check_low_stock_items(self, service_center=None):
        """Check for items below minimum stock level and create requests"""
        
        # Find items with stock below minimum level
        low_stock_items = Item.objects.filter(
            tenant_id=self.tenant_id,
            quantity__lte=models.F('min_stock_level'),
            min_stock_level__gt=0
        )
        
        # TODO: Add service center specific filtering when warehouse relationships are established
        
        created_requests = []
        
        for item in low_stock_items:
            try:
                # Calculate reorder quantity (simple algorithm - could be enhanced)
                current_stock = item.current_stock
                min_level = item.min_stock_level
                reorder_quantity = max(min_level * 2 - current_stock, min_level)
                
                # Create purchase order request or transfer request
                request_result = self._create_automatic_request(
                    item=item,
                    quantity=reorder_quantity,
                    request_type='low_stock',
                    service_center=service_center,
                    notes=f"Automatic reorder: Current stock ({current_stock}) below minimum ({min_level})"
                )
                
                if request_result['success']:
                    created_requests.append({
                        'item_id': str(item.id),
                        'item_name': item.name,
                        'item_sku': item.sku,
                        'current_stock': float(current_stock),
                        'min_stock_level': float(min_level),
                        'reorder_quantity': float(reorder_quantity),
                        'request_type': 'low_stock',
                        'request_id': request_result.get('request_id'),
                        'status': 'created'
                    })
                
            except Exception as e:
                logger.error(f"Error creating low stock request for item {item.sku}: {str(e)}")
                created_requests.append({
                    'item_id': str(item.id),
                    'item_name': item.name,
                    'error': str(e),
                    'status': 'failed'
                })
        
        return created_requests
    
    def _check_expiring_items(self, service_center=None, days_ahead=30):
        """Check for items expiring soon and create replacement requests"""
        
        from django.utils import timezone
        from datetime import timedelta
        
        expiring_threshold = timezone.now().date() + timedelta(days=days_ahead)
        
        # Find items with expiring batches
        expiring_items = Item.objects.filter(
            tenant_id=self.tenant_id,
            requires_expiry_tracking=True,
            batches__expiry_date__lte=expiring_threshold,
            batches__status='active'
        ).distinct()
        
        created_requests = []
        
        for item in expiring_items:
            try:
                # Calculate total expiring quantity
                expiring_batches = item.batches.filter(
                    expiry_date__lte=expiring_threshold,
                    status='active'
                )
                
                total_expiring = sum(batch.current_quantity for batch in expiring_batches)
                
                if total_expiring > 0:
                    # Create replacement request
                    request_result = self._create_automatic_request(
                        item=item,
                        quantity=total_expiring,
                        request_type='expiring_replacement',
                        service_center=service_center,
                        notes=f"Replacement for {total_expiring} units expiring within {days_ahead} days"
                    )
                    
                    if request_result['success']:
                        created_requests.append({
                            'item_id': str(item.id),
                            'item_name': item.name,
                            'item_sku': item.sku,
                            'expiring_quantity': float(total_expiring),
                            'expiry_date': expiring_threshold.isoformat(),
                            'request_type': 'expiring_replacement',
                            'request_id': request_result.get('request_id'),
                            'status': 'created'
                        })
                
            except Exception as e:
                logger.error(f"Error creating expiring items request for {item.sku}: {str(e)}")
                created_requests.append({
                    'item_id': str(item.id),
                    'item_name': item.name,
                    'error': str(e),
                    'status': 'failed'
                })
        
        return created_requests
    
    def _forecast_work_order_needs(self, service_center=None, days_ahead=7):
        """Forecast parts needs based on upcoming work orders"""
        
        from django.utils import timezone
        from datetime import timedelta
        from work_orders.models import WorkOrder
        
        forecast_date = timezone.now().date() + timedelta(days=days_ahead)
        
        # Find work orders scheduled in the next week
        upcoming_work_orders = WorkOrder.objects.filter(
            tenant_id=self.tenant_id,
            status__in=['planned', 'in_progress'],
            planned_start_date__lte=forecast_date
        )
        
        if service_center:
            upcoming_work_orders = upcoming_work_orders.filter(service_center=service_center)
        
        # Aggregate parts requirements
        parts_forecast = {}
        
        for work_order in upcoming_work_orders:
            for material in work_order.materials.all():
                item_id = str(material.item.id)
                if item_id not in parts_forecast:
                    parts_forecast[item_id] = {
                        'item': material.item,
                        'total_required': 0,
                        'work_orders': []
                    }
                
                parts_forecast[item_id]['total_required'] += material.quantity
                parts_forecast[item_id]['work_orders'].append(work_order.work_order_number)
        
        created_requests = []
        
        for item_id, forecast_data in parts_forecast.items():
            try:
                item = forecast_data['item']
                required_quantity = forecast_data['total_required']
                current_stock = item.current_stock
                
                # Check if current stock is insufficient
                if current_stock < required_quantity:
                    shortage = required_quantity - current_stock
                    
                    request_result = self._create_automatic_request(
                        item=item,
                        quantity=shortage,
                        request_type='forecast_shortage',
                        service_center=service_center,
                        notes=f"Forecast shortage for upcoming work orders: {', '.join(forecast_data['work_orders'])}"
                    )
                    
                    if request_result['success']:
                        created_requests.append({
                            'item_id': item_id,
                            'item_name': item.name,
                            'item_sku': item.sku,
                            'current_stock': float(current_stock),
                            'forecasted_need': float(required_quantity),
                            'shortage': float(shortage),
                            'work_orders': forecast_data['work_orders'],
                            'request_type': 'forecast_shortage',
                            'request_id': request_result.get('request_id'),
                            'status': 'created'
                        })
                
            except Exception as e:
                logger.error(f"Error creating forecast request for item {item_id}: {str(e)}")
                created_requests.append({
                    'item_id': item_id,
                    'error': str(e),
                    'status': 'failed'
                })
        
        return created_requests
    
    def _create_automatic_request(self, item, quantity, request_type, service_center=None, notes=""):
        """Create an automatic parts request (purchase order or transfer request)"""
        
        try:
            # For now, create a simple purchase order request
            # In a full implementation, this could choose between transfer requests
            # and purchase orders based on business rules
            
            from purchases.models import PurchaseOrder, PurchaseOrderItem
            import uuid
            
            # Generate PO number
            po_number = f"AUTO-{request_type.upper()}-{uuid.uuid4().hex[:8].upper()}"
            
            # Find a default supplier for the item
            # TODO: Implement supplier selection logic
            default_supplier = None
            try:
                from purchases.models import Supplier
                default_supplier = Supplier.objects.filter(
                    tenant_id=self.tenant_id,
                    is_active=True
                ).first()
            except:
                pass
            
            if not default_supplier:
                return {
                    'success': False,
                    'error': 'No active supplier found for automatic purchase order'
                }
            
            # Create purchase order
            purchase_order = PurchaseOrder.objects.create(
                tenant_id=self.tenant_id,
                order_number=po_number,
                supplier=default_supplier,
                status='pending',
                is_automatic=True,
                notes=f"Automatic request ({request_type}): {notes}",
                service_center=service_center
            )
            
            # Create purchase order item
            PurchaseOrderItem.objects.create(
                tenant_id=self.tenant_id,
                purchase_order=purchase_order,
                item=item,
                quantity=quantity,
                unit_price=item.unit_price or 0,
                notes=f"Auto-generated for {request_type}"
            )
            
            # Send notification to procurement team
            try:
                from notifications.services import NotificationService
                notification_service = NotificationService(self.tenant_id)
                
                # Create notification for procurement staff
                notification_service.create_notification(
                    title=f"Automatic Parts Request Created",
                    message=f"Automatic purchase order {po_number} created for {item.name} (Qty: {quantity})",
                    notification_type="parts_request_auto",
                    action_url=f"/purchases/orders/{purchase_order.id}/",
                    metadata={
                        'purchase_order_id': str(purchase_order.id),
                        'item_id': str(item.id),
                        'request_type': request_type,
                        'quantity': float(quantity)
                    }
                )
            except Exception as e:
                logger.warning(f"Failed to send automatic request notification: {str(e)}")
            
            return {
                'success': True,
                'request_id': str(purchase_order.id),
                'request_number': po_number,
                'request_type': 'purchase_order'
            }
            
        except Exception as e:
            logger.error(f"Error creating automatic request for {item.sku}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_monitoring_summary(self, results):
        """Generate summary of monitoring results"""
        
        total_items_monitored = (
            len(results.get('low_stock_requests', [])) +
            len(results.get('expiring_items_requests', [])) +
            len(results.get('forecast_requests', []))
        )
        
        successful_requests = sum([
            len([r for r in results.get('low_stock_requests', []) if r.get('status') == 'created']),
            len([r for r in results.get('expiring_items_requests', []) if r.get('status') == 'created']),
            len([r for r in results.get('forecast_requests', []) if r.get('status') == 'created'])
        ])
        
        failed_requests = total_items_monitored - successful_requests
        
        return {
            'total_items_monitored': total_items_monitored,
            'successful_requests': successful_requests,
            'failed_requests': failed_requests,
            'monitoring_timestamp': timezone.now().isoformat(),
            'categories': {
                'low_stock': len(results.get('low_stock_requests', [])),
                'expiring_items': len(results.get('expiring_items_requests', [])),
                'forecast_shortages': len(results.get('forecast_requests', []))
            }
        }
    
    @classmethod
    def run_automatic_monitoring(cls, tenant_id: str, service_center=None):
        """
        Class method to run automatic monitoring for scheduled tasks
        
        Args:
            tenant_id: Tenant ID to monitor
            service_center: Optional service center to limit monitoring
            
        Returns:
            Monitoring results
        """
        service = cls(tenant_id)
        return service.monitor_and_create_requests(service_center)


class StockLevelOptimizationService:
    """
    Service for optimizing stock levels based on historical data and demand patterns
    """
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
    
    def analyze_demand_patterns(self, item, days_back=90):
        """
        Analyze demand patterns for an item over the specified period
        
        Args:
            item: Item instance
            days_back: Number of days to look back for analysis
            
        Returns:
            Dictionary with demand analysis
        """
        from django.utils import timezone
        from datetime import timedelta
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days_back)
        
        # Get movement history for the item
        movements = Movement.objects.filter(
            tenant_id=self.tenant_id,
            item=item,
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        ).order_by('created_at')
        
        # Analyze outbound movements (consumption)
        outbound_movements = movements.filter(
            models.Q(movement_type_ref__is_outbound=True) |
            models.Q(movement_type='sale')  # Legacy support
        )
        
        total_consumption = sum(movement.quantity for movement in outbound_movements)
        daily_average = total_consumption / days_back if days_back > 0 else 0
        
        # Calculate monthly consumption pattern
        monthly_consumption = {}
        for movement in outbound_movements:
            month_key = movement.created_at.strftime('%Y-%m')
            if month_key not in monthly_consumption:
                monthly_consumption[month_key] = 0
            monthly_consumption[month_key] += movement.quantity
        
        # Calculate variability (simple standard deviation)
        if len(monthly_consumption) > 1:
            avg_monthly = sum(monthly_consumption.values()) / len(monthly_consumption)
            variance = sum((consumption - avg_monthly) ** 2 for consumption in monthly_consumption.values()) / len(monthly_consumption)
            std_deviation = variance ** 0.5
        else:
            std_deviation = 0
        
        return {
            'item_id': str(item.id),
            'item_name': item.name,
            'analysis_period_days': days_back,
            'total_consumption': float(total_consumption),
            'daily_average_consumption': float(daily_average),
            'monthly_consumption_pattern': {k: float(v) for k, v in monthly_consumption.items()},
            'consumption_variability': float(std_deviation),
            'current_stock': float(item.current_stock),
            'current_min_level': float(item.min_stock_level),
            'recommended_adjustments': self._calculate_recommended_levels(
                daily_average, std_deviation, item.min_stock_level
            )
        }
    
    def _calculate_recommended_levels(self, daily_average, std_deviation, current_min_level):
        """Calculate recommended stock levels based on demand analysis"""
        
        # Simple algorithm - could be enhanced with more sophisticated models
        safety_factor = 1.5  # Safety multiplier
        lead_time_days = 7    # Assumed lead time
        
        # Calculate recommended minimum level
        # Formula: (Daily Average * Lead Time) + (Safety Factor * Standard Deviation)
        recommended_min = (daily_average * lead_time_days) + (safety_factor * std_deviation)
        
        # Calculate recommended reorder point
        reorder_point = recommended_min * 1.2  # 20% buffer above minimum
        
        # Calculate recommended maximum level (to prevent overstocking)
        max_level = recommended_min * 3  # Maximum 3x minimum level
        
        return {
            'recommended_min_level': float(max(recommended_min, 1)),  # At least 1 unit
            'recommended_reorder_point': float(max(reorder_point, 2)),
            'recommended_max_level': float(max(max_level, 5)),
            'current_min_level': float(current_min_level),
            'adjustment_needed': abs(float(current_min_level) - float(recommended_min)) > 1,
            'adjustment_direction': 'increase' if recommended_min > current_min_level else 'decrease'
        }
    
    def optimize_stock_levels_for_service_center(self, service_center=None, auto_apply=False):
        """
        Optimize stock levels for all items in a service center
        
        Args:
            service_center: Service center to optimize (None for all)
            auto_apply: Whether to automatically apply recommendations
            
        Returns:
            Dictionary with optimization results
        """
        # Get items that have movement history
        items_with_movement = Item.objects.filter(
            tenant_id=self.tenant_id,
            movements__isnull=False
        ).distinct()
        
        # TODO: Filter by service center when warehouse relationships are established
        
        optimization_results = []
        
        for item in items_with_movement:
            try:
                analysis = self.analyze_demand_patterns(item)
                
                if analysis['recommended_adjustments']['adjustment_needed']:
                    optimization_results.append({
                        'item_analysis': analysis,
                        'action_taken': None
                    })
                    
                    # Auto-apply changes if requested
                    if auto_apply:
                        new_min_level = analysis['recommended_adjustments']['recommended_min_level']
                        item.min_stock_level = new_min_level
                        item.save(update_fields=['min_stock_level'])
                        
                        optimization_results[-1]['action_taken'] = f"Updated min_stock_level to {new_min_level}"
                        
                        logger.info(f"Auto-optimized stock level for {item.sku}: {item.min_stock_level} -> {new_min_level}")
                
            except Exception as e:
                logger.error(f"Error optimizing stock levels for item {item.sku}: {str(e)}")
                optimization_results.append({
                    'item_id': str(item.id),
                    'item_name': item.name,
                    'error': str(e)
                })
        
        return {
            'total_items_analyzed': len(items_with_movement),
            'items_needing_optimization': len(optimization_results),
            'auto_apply_enabled': auto_apply,
            'optimization_results': optimization_results,
            'optimization_timestamp': timezone.now().isoformat()
        } 