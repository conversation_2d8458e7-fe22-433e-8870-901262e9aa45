from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from inventory.models import Item, Movement, MovementType
from django.db.models import F
from notifications.services import NotificationService, send_webhook, create_notification
from user_roles.models import UserRole
from django.contrib.auth.models import User
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Item)
def item_saved(sender, instance, created, **kwargs):
    """Handle item creation and updates with notifications"""
    try:
        if created:
            # Notify relevant staff about new item
            _notify_new_item_created(instance)
        else:
            # Check if stock levels changed
            if hasattr(instance, '_previous_stock'):
                if instance.current_stock != instance._previous_stock:
                    _notify_stock_level_change(instance, instance._previous_stock)
                    
            # Additional change notifications can be added here
                
    except Exception as e:
        logger.error(f"Error in item_saved signal: {e}")


@receiver(post_save, sender=Movement)
def movement_saved(sender, instance, created, **kwargs):
    """
    Handle post-save signal for Movement model.
    Log movements and create notifications for significant events.
    """
    if created:
        if instance.movement_type_ref:
            movement_name = instance.movement_type_ref.name
        else:
            movement_name = instance.get_movement_type_display() or "Unknown"
            
        logger.info(
            f"New movement: {movement_name} of {instance.quantity} "
            f"{instance.item.name} ({instance.item.sku})"
        )


@receiver(post_save, sender=Item)
def trigger_item_webhooks(sender, instance, created, **kwargs):
    """
    Trigger webhooks when an item is created or updated
    """
    event_type = 'item_created' if created else 'item_updated'
    
    # Prepare payload
    payload = {
        'id': str(instance.id),
        'name': instance.name,
        'sku': instance.sku,
        'description': instance.description,
        'category': instance.category,
        'quantity': instance.quantity,
        'unit_price': float(instance.unit_price) if instance.unit_price else None,
        'min_stock_level': instance.min_stock_level,
        'is_low_stock': instance.is_low_stock,
    }
    
    # Send webhook
    send_webhook(event_type, payload, instance.tenant_id)
    
    # Create notification for low stock
    if instance.is_low_stock:
        create_notification(
            tenant_id=instance.tenant_id,
            title=f"Low Stock Alert: {instance.name}",
            message=f"Item {instance.name} (SKU: {instance.sku}) is below the minimum stock level. Current quantity: {instance.quantity}",
            level='warning',
            object_type='Item',
            object_id=str(instance.id)
        )
        
        # Also send a webhook for low stock
        send_webhook('stock_low', payload, instance.tenant_id)


@receiver(post_save, sender=Movement)
def trigger_movement_webhooks(sender, instance, created, **kwargs):
    """
    Trigger webhooks when a movement is created
    """
    if not created:
        return  # Only trigger on creation, not updates
    
    # Determine movement type name 
    if instance.movement_type_ref:
        movement_type_name = instance.movement_type_ref.name
        movement_type_code = instance.movement_type_ref.code
    else:
        movement_type_name = instance.get_movement_type_display() or "Unknown"
        movement_type_code = instance.movement_type or "unknown"
    
    # Prepare payload
    payload = {
        'id': str(instance.id),
        'item_id': str(instance.item.id) if instance.item else None,
        'item_name': instance.item.name if instance.item else None,
        'item_sku': instance.item.sku if instance.item else None,
        'quantity': instance.quantity,
        'movement_type': movement_type_name,
        'movement_type_code': movement_type_code,
        'reference': instance.reference,
        'notes': instance.notes,
        'created_at': instance.created_at.isoformat() if instance.created_at else None,
        'is_inbound': instance.is_inbound(),
        'is_outbound': instance.is_outbound(),
    }
    
    # Send webhook
    send_webhook('movement_created', payload, instance.tenant_id)
    
    # Also send a specific event based on movement type
    event_type = f"movement_{movement_type_code}"
    send_webhook(event_type, payload, instance.tenant_id)


@receiver(post_save, sender=MovementType)
def movement_type_saved(sender, instance, created, **kwargs):
    """
    Handle post-save signal for MovementType model.
    Log when new movement types are created.
    """
    if created:
        logger.info(
            f"New movement type created: {instance.name} (code: {instance.code}), "
            f"inbound: {instance.is_inbound}, outbound: {instance.is_outbound}"
        ) 


# ==================== WORK ORDER INTEGRATION SIGNALS ====================

@receiver(post_save, sender='work_orders.WorkOrderMaterial')
def work_order_material_consumed(sender, instance, created, **kwargs):
    """
    Handle work order material consumption - update inventory when materials are used
    """
    if not created:
        return
    
    try:
        # Check if this is an actual consumption (not just allocation)
        if hasattr(instance, 'consumed_quantity') and instance.consumed_quantity > 0:
            # Create inventory movement for consumed materials
            Movement.objects.create(
                item=instance.item,
                movement_type=MovementType.objects.get_or_create(
                    name="Work Order Consumption",
                    type="OUT",
                    defaults={"description": "Material consumed by work order"}
                )[0],
                quantity=-abs(instance.consumed_quantity),
                reference_number=f"WO-{instance.work_order.number}",
                notes=f"Material consumed by work order {instance.work_order.number}",
                tenant_id=instance.tenant_id
            )
            
            # Update item stock levels
            instance.item.current_stock = F('current_stock') - abs(instance.consumed_quantity)
            instance.item.save(update_fields=['current_stock'])
            
            # Notify inventory managers if stock is getting low
            item = instance.item
            if item.current_stock <= item.min_stock_level:
                _notify_low_stock_with_actions(item, instance.tenant_id)
                
            logger.info(f"Work order material consumed: {instance.item.name} - {instance.consumed_quantity}")
            
    except Exception as e:
        logger.error(f"Error processing work order material consumption: {e}")


@receiver(post_save, sender='work_orders.WorkOrder')
def work_order_status_changed(sender, instance, created, **kwargs):
    """
    Handle work order status changes that affect inventory
    """
    if created:
        return
    
    try:
        # Check if work order was completed
        if (hasattr(instance, '_original_status') and 
            instance._original_status != 'completed' and 
            instance.status == 'completed'):
            
            # Mark all materials as consumed
            from work_orders.models import WorkOrderMaterial
            materials = WorkOrderMaterial.objects.filter(
                work_order=instance,
                tenant_id=instance.tenant_id
            )
            
            for material in materials:
                if not getattr(material, 'is_consumed', False):
                    # Mark as consumed and trigger consumption signal
                    material.is_consumed = True
                    material._changed_consumption = True
                    material.save()
            
            logger.info(f"Work order {instance.work_order_number} completed - materials marked as consumed")
            
    except Exception as e:
        logger.error(f"Error handling work order completion: {str(e)}")


# ==================== EXISTING SIGNALS ==================== 

@receiver(pre_save, sender=Item)
def item_pre_save(sender, instance, **kwargs):
    """Track previous stock levels before saving"""
    if instance.pk:
        try:
            previous_item = sender.objects.get(pk=instance.pk)
            instance._previous_stock = previous_item.current_stock
        except sender.DoesNotExist:
            instance._previous_stock = 0


@receiver(post_save, sender=Movement)
def movement_created(sender, instance, created, **kwargs):
    """Handle inventory movement notifications"""
    if not created:
        return
        
    try:
        # Notify based on movement type
        if instance.movement_type.type == 'OUT':
            _notify_stock_out_movement(instance)
        elif instance.movement_type.type == 'IN':
            _notify_stock_in_movement(instance)
        elif instance.movement_type.type == 'TRANSFER':
            _notify_transfer_movement(instance)
            
        # Check for automatic reorder if stock is low
        item = instance.item
        if item.current_stock <= item.reorder_point:
            _notify_reorder_required(item, instance.tenant_id)
            
    except Exception as e:
        logger.error(f"Error in movement_created signal: {e}")


# ==================== NOTIFICATION HELPER FUNCTIONS ====================

def _notify_low_stock_with_actions(item, tenant_id):
    """Notify about low stock with approve/deny actions"""
    try:
        # Get inventory managers and purchasing staff
        managers = _get_users_by_roles([
            'inventory_manager', 'warehouse_manager', 'service_center_manager'
        ], tenant_id)
        
        for manager in managers:
            # Create notification with action items
            NotificationService.create_notification(
                recipient=manager,
                notification_type_code='inventory_low_stock',
                title=_("Low Stock Alert - {item_name}").format(item_name=item.name),
                message=_("Item {item_name} is running low. Current stock: {current}, Minimum: {minimum}").format(
                    item_name=item.name, 
                    current=item.current_stock, 
                    minimum=item.min_stock_level
                ),
                priority='high',
                action_required=True,
                action_url=reverse('inventory:item_detail', kwargs={'pk': item.pk}),
                action_text=_("View Item"),
                related_object_type='item',
                related_object_id=str(item.id),
                tenant_id=tenant_id
            )
            
            # Create action item for reorder approval
            NotificationService.create_action_item(
                assigned_to=manager,
                action_type='approve_reorder',
                title=_("Approve Reorder for {item_name}").format(item_name=item.name),
                description=_("Low stock detected. Current: {current}, Min: {minimum}. Approve automatic reorder?").format(
                    current=item.current_stock, 
                    minimum=item.min_stock_level
                ),
                priority='high',
                action_url=reverse('inventory:approve_reorder', kwargs={'item_id': item.pk}),
                related_object_type='item',
                related_object_id=str(item.id),
                metadata={
                    'current_stock': float(item.current_stock),
                    'min_stock': float(item.min_stock_level),
                    'suggested_quantity': float(item.reorder_quantity or 0),
                    'actions': [
                        {'type': 'approve', 'label': str(_('Approve Reorder')), 'class': 'bg-green-600 text-white'},
                        {'type': 'deny', 'label': str(_('Deny')), 'class': 'bg-red-600 text-white'},
                        {'type': 'modify', 'label': str(_('Modify Quantity')), 'class': 'bg-blue-600 text-white'}
                    ]
                },
                tenant_id=tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_low_stock_with_actions: {e}")


def _notify_new_item_created(item):
    """Notify about new item creation with approval actions"""
    try:
        # Get inventory managers for approval
        managers = _get_users_by_roles([
            'inventory_manager', 'warehouse_manager'
        ], item.tenant_id)
        
        for manager in managers:
            NotificationService.create_action_item(
                assigned_to=manager,
                action_type='approve_new_item',
                title=_("New Item Requires Approval - {item_name}").format(item_name=item.name),
                description=_("New inventory item created. Please review and approve for active use."),
                priority='medium',
                action_url=reverse('inventory:item_detail', kwargs={'pk': item.pk}),
                related_object_type='item',
                related_object_id=str(item.id),
                metadata={
                    'item_name': item.name,
                    'item_code': getattr(item, 'code', ''),
                    'category': item.category.name if item.category else '',
                    'actions': [
                        {'type': 'approve', 'label': str(_('Approve Item')), 'class': 'bg-green-600 text-white'},
                        {'type': 'deny', 'label': str(_('Reject Item')), 'class': 'bg-red-600 text-white'},
                        {'type': 'review', 'label': str(_('Needs Review')), 'class': 'bg-yellow-600 text-white'}
                    ]
                },
                tenant_id=item.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_new_item_created: {e}")


def _notify_stock_level_change(item, previous_stock):
    """Notify about significant stock level changes"""
    try:
        stock_change = item.current_stock - previous_stock
        
        # Only notify for significant changes (more than 10% or critical thresholds)
        if abs(stock_change) >= (previous_stock * 0.1) or item.current_stock <= item.min_stock_level:
            
            managers = _get_users_by_roles([
                'inventory_manager', 'warehouse_manager'
            ], item.tenant_id)
            
            change_type = "increased" if stock_change > 0 else "decreased"
            priority = "high" if item.current_stock <= item.min_stock_level else "medium"
            
            for manager in managers:
                NotificationService.create_notification(
                    recipient=manager,
                    notification_type_code='inventory_stock_change',
                    title=_("Stock Level Changed - {item_name}").format(item_name=item.name),
                    message=_("Stock {change_type} from {previous} to {current} units. Change: {change:+}").format(
                        change_type=change_type, 
                        previous=previous_stock, 
                        current=item.current_stock, 
                        change=stock_change
                    ),
                    priority=priority,
                    action_required=priority == "high",
                    action_url=reverse('inventory:item_detail', kwargs={'pk': item.pk}),
                    action_text=_("View Details"),
                    related_object_type='item',
                    related_object_id=str(item.id),
                    tenant_id=item.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_stock_level_change: {e}")


def _notify_stock_out_movement(movement):
    """Notify about stock OUT movements with approval for large quantities"""
    try:
        # For large outgoing quantities, require approval
        if movement.quantity <= -100:  # Large outgoing movement
            
            managers = _get_users_by_roles([
                'inventory_manager', 'warehouse_manager'
            ], movement.tenant_id)
            
            for manager in managers:
                NotificationService.create_action_item(
                    assigned_to=manager,
                    action_type='approve_large_movement',
                    title=_("Large Stock Movement Requires Approval"),
                    description=_("Large outgoing movement of {quantity} units for item {item_name}. Reference: {reference}").format(
                        quantity=abs(movement.quantity), 
                        item_name=movement.item.name, 
                        reference=movement.reference_number or "N/A"
                    ),
                    priority='high',
                    action_url=reverse('inventory:movement_detail', kwargs={'pk': movement.pk}),
                    related_object_type='movement',
                    related_object_id=str(movement.id),
                    metadata={
                        'quantity': float(movement.quantity),
                        'item_name': movement.item.name,
                        'reference': movement.reference_number or "N/A",
                        'actions': [
                            {'type': 'approve', 'label': str(_('Approve Movement')), 'class': 'bg-green-600 text-white'},
                            {'type': 'deny', 'label': str(_('Reject Movement')), 'class': 'bg-red-600 text-white'},
                            {'type': 'investigate', 'label': str(_('Investigate')), 'class': 'bg-orange-600 text-white'}
                        ]
                    },
                    tenant_id=movement.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_stock_out_movement: {e}")


def _notify_reorder_required(item, tenant_id):
    """Notify about automatic reorder requirements"""
    try:
        # Get purchasing staff
        purchasing_staff = _get_users_by_roles([
            'purchasing_manager', 'inventory_manager'
        ], tenant_id)
        
        for staff in purchasing_staff:
            NotificationService.create_action_item(
                assigned_to=staff,
                action_type='create_purchase_order',
                title=_("Automatic Reorder Required - {item_name}").format(item_name=item.name),
                description=_("Item has reached reorder point. Current: {current}, Reorder Point: {reorder_point}").format(
                    current=item.current_stock, 
                    reorder_point=item.reorder_point
                ),
                priority='high',
                action_url=reverse('purchases:create_from_reorder', kwargs={'item_id': item.pk}),
                related_object_type='item',
                related_object_id=str(item.id),
                metadata={
                    'current_stock': float(item.current_stock),
                    'reorder_point': float(item.reorder_point),
                    'suggested_quantity': float(item.reorder_quantity or 50),
                    'actions': [
                        {'type': 'create_po', 'label': str(_('Create Purchase Order')), 'class': 'bg-green-600 text-white'},
                        {'type': 'adjust_levels', 'label': str(_('Adjust Reorder Level')), 'class': 'bg-blue-600 text-white'},
                        {'type': 'skip', 'label': str(_('Skip This Time')), 'class': 'bg-gray-600 text-white'}
                    ]
                },
                tenant_id=tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_reorder_required: {e}")


def _get_users_by_roles(role_codes, tenant_id):
    """Helper function to get users by role codes"""
    try:
        user_roles = UserRole.objects.filter(
            role__code__in=role_codes,
            tenant_id=tenant_id,
            is_active=True
        ).select_related('user', 'role')
        
        return [ur.user for ur in user_roles if ur.user.is_active]
    except Exception as e:
        logger.error(f"Error getting users by roles: {e}")
        return []


def _notify_stock_in_movement(movement):
    """Notify about stock IN movements"""
    try:
        # Notify inventory staff about incoming stock
        managers = _get_users_by_roles([
            'inventory_manager', 'warehouse_manager'
        ], movement.tenant_id)
        
        for manager in managers:
            NotificationService.create_notification(
                recipient=manager,
                notification_type_code='inventory_stock_in',
                title=_("Stock Received - {item_name}").format(item_name=movement.item.name),
                message=_("Received {quantity} units of {item_name}. Reference: {reference}").format(
                    quantity=abs(movement.quantity), 
                    item_name=movement.item.name,
                    reference=movement.reference_number or "N/A"
                ),
                priority='low',
                action_required=False,
                action_url=reverse('inventory:movement_detail', kwargs={'pk': movement.pk}),
                action_text=_("View Movement"),
                related_object_type='movement',
                related_object_id=str(movement.id),
                tenant_id=movement.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_stock_in_movement: {e}")


def _notify_transfer_movement(movement):
    """Notify about stock transfer movements"""
    try:
        # Notify warehouse staff about transfers
        managers = _get_users_by_roles([
            'warehouse_manager', 'inventory_manager'
        ], movement.tenant_id)
        
        for manager in managers:
            NotificationService.create_notification(
                recipient=manager,
                notification_type_code='inventory_transfer',
                title=_("Stock Transfer - {item_name}").format(item_name=movement.item.name),
                message=_("Transfer of {quantity} units of {item_name}. Reference: {reference}").format(
                    quantity=abs(movement.quantity),
                    item_name=movement.item.name, 
                    reference=movement.reference_number or "N/A"
                ),
                priority='medium',
                action_required=False,
                action_url=reverse('inventory:movement_detail', kwargs={'pk': movement.pk}),
                action_text=_("View Transfer"),
                related_object_type='movement',
                related_object_id=str(movement.id),
                tenant_id=movement.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_transfer_movement: {e}") 