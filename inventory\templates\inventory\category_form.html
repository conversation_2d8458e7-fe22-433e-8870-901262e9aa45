{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "إضافة فئة" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">{% trans "إضافة فئة جديدة" %}</h1>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "اسم الفئة" %}
                    </label>
                    <input type="text" name="name" id="id_name" required
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="{% trans 'أدخل اسم الفئة' %}">
                </div>
                
                <div>
                    <label for="id_code" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "رمز الفئة" %}
                    </label>
                    <input type="text" name="code" id="id_code"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="{% trans 'رمز مختصر للفئة (اختياري)' %}">
                </div>
                
                <div class="md:col-span-2">
                    <label for="id_parent" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الفئة الأب" %}
                    </label>
                    <select name="parent" id="id_parent" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">{% trans "لا توجد فئة أب (فئة رئيسية)" %}</option>
                        <!-- Options will be populated by the view -->
                    </select>
                </div>
            </div>
            
            <div>
                <label for="id_description" class="block text-sm font-medium text-gray-700 mb-2">
                    {% trans "الوصف" %}
                </label>
                <textarea name="description" id="id_description" rows="3" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2"
                          placeholder="{% trans 'وصف الفئة (اختياري)' %}"></textarea>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" name="is_active" id="id_is_active" checked
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="id_is_active" class="mr-2 block text-sm text-gray-900">
                    {% trans "فئة نشطة" %}
                </label>
            </div>
            
            <div class="flex justify-end space-x-4">
                <a href="/core/supply-chain/inventory/" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors">
                    {% trans "إلغاء" %}
                </a>
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors">
                    {% trans "حفظ" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 