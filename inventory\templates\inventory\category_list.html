{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title|default:"الفئات" }}{% endblock %}

{% block extra_css %}
<style>
    .category-card {
        transition: all 0.3s ease;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
    }
    .category-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .category-level-indicator {
        width: 4px;
        border-radius: 2px;
    }
    .level-0 { background-color: #3B82F6; }
    .level-1 { background-color: #10B981; }
    .level-2 { background-color: #F59E0B; }
    .level-3 { background-color: #EF4444; }
    .category-hierarchy {
        border-right: 2px solid #E5E7EB;
        position: relative;
    }
    .category-hierarchy::before {
        content: '';
        position: absolute;
        top: 0;
        right: -1px;
        width: 2px;
        height: 100%;
        background: linear-gradient(to bottom, #3B82F6, #10B981);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        <i class="fas fa-layer-group text-blue-600 ml-3"></i>
                        {% trans "فئات المخزون" %}
                    </h1>
                    <p class="mt-2 text-lg text-gray-600">
                        {% trans "إدارة تصنيفات الأصناف والمنتجات" %}
                    </p>
                </div>
                <div class="flex items-center space-x-4 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                    <a href="{% url 'inventory:category_create' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        {% trans "إضافة فئة جديدة" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        {% if categories %}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                                <i class="fas fa-layer-group text-blue-600"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    {% trans "إجمالي الفئات" %}
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ categories.count }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                                <i class="fas fa-sitemap text-green-600"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    {% trans "الفئات الرئيسية" %}
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ categories|length|add:"-3" }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                                <i class="fas fa-boxes text-purple-600"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    {% trans "الأصناف المصنفة" %}
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ total_items|default:0 }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                                <i class="fas fa-check-circle text-yellow-600"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    {% trans "الفئات النشطة" %}
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ categories.count }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Categories List -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">
                        {% trans "قائمة الفئات" %}
                    </h3>
                    <div class="flex items-center space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                        <span class="text-sm text-gray-500">
                            {% if categories %}
                                {% blocktrans count counter=categories.count %}
                                    فئة واحدة
                                {% plural %}
                                    {{ counter }} فئة
                                {% endblocktrans %}
                            {% else %}
                                {% trans "لا توجد فئات" %}
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>

            {% if categories %}
                <div class="divide-y divide-gray-200">
                    {% for category in categories %}
                    <div class="category-card hover:bg-gray-50 transition-colors">
                        <div class="flex items-center px-6 py-4">
                            <!-- Level Indicator -->
                            <div class="category-level-indicator level-{{ category.level }} h-12 ml-4"></div>
                            
                            <!-- Category Info -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                        <!-- Hierarchy Indication -->
                                        {% if category.level > 0 %}
                                            <div class="flex items-center text-gray-400">
                                                {% for i in "x"|rjust:category.level %}
                                                    <i class="fas fa-arrow-left text-xs mx-1"></i>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        
                                        <!-- Category Icon -->
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white">
                                            {% if category.icon %}
                                                <i class="{{ category.icon }}"></i>
                                            {% else %}
                                                <i class="fas fa-folder"></i>
                                            {% endif %}
                                        </div>
                                        
                                        <!-- Category Details -->
                                        <div>
                                            <h4 class="text-lg font-semibold text-gray-900">
                                                {{ category.name }}
                                            </h4>
                                            <p class="text-sm text-gray-500">
                                                {% trans "الرمز:" %} {{ category.code }}
                                                {% if category.parent %}
                                                    • {% trans "تحت:" %} {{ category.parent.name }}
                                                {% endif %}
                                            </p>
                                            {% if category.description %}
                                                <p class="text-sm text-gray-600 mt-1">
                                                    {{ category.description|truncatechars:100 }}
                                                </p>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <!-- Category Stats & Actions -->
                                    <div class="flex items-center space-x-4 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                        <!-- Items Count -->
                                        <div class="text-center">
                                            <div class="text-lg font-bold text-blue-600">
                                                {{ category.items.count|default:0 }}
                                            </div>
                                            <div class="text-xs text-gray-500">{% trans "صنف" %}</div>
                                        </div>
                                        
                                        <!-- Level Badge -->
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {% if category.level == 0 %}bg-blue-100 text-blue-800{% elif category.level == 1 %}bg-green-100 text-green-800{% elif category.level == 2 %}bg-yellow-100 text-yellow-800{% else %}bg-red-100 text-red-800{% endif %}">
                                            {% trans "المستوى" %} {{ category.level }}
                                        </span>
                                        
                                        <!-- Actions -->
                                        <div class="flex items-center space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                            <a href="#" 
                                               class="text-blue-600 hover:text-blue-900 transition-colors"
                                               title="{% trans 'تعديل' %}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="#" 
                                               class="text-green-600 hover:text-green-900 transition-colors"
                                               title="{% trans 'عرض الأصناف' %}">
                                                <i class="fas fa-boxes"></i>
                                            </a>
                                            <a href="#" 
                                               class="text-red-600 hover:text-red-900 transition-colors"
                                               title="{% trans 'حذف' %}">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- Empty State -->
                <div class="text-center py-12">
                    <div class="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-layer-group text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        {% trans "لا توجد فئات حتى الآن" %}
                    </h3>
                    <p class="text-gray-500 mb-6">
                        {% trans "ابدأ بإنشاء فئة جديدة لتنظيم أصنافك" %}
                    </p>
                    <a href="{% url 'inventory:category_create' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        {% trans "إضافة فئة جديدة" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects for category cards
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('shadow-md');
        });
        card.addEventListener('mouseleave', function() {
            this.classList.remove('shadow-md');
        });
    });
});
</script>
{% endblock %} 