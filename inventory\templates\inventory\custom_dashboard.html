{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Inventory Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .card {
        @apply bg-white rounded-lg shadow-md transition-all duration-200;
    }
    .card:hover {
        @apply shadow-lg;
    }
    .hover-shadow {
        transition: box-shadow 0.3s ease;
    }
    .hover-shadow:hover {
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .hover-scale {
        transition: transform 0.3s ease;
    }
    .hover-scale:hover {
        transform: scale(1.02);
    }
    .animate-fade-in {
        animation: fadeIn 0.5s ease-in;
    }
    @keyframes fadeIn {
        0% { opacity: 0; transform: translateY(10px); }
        100% { opacity: 1; transform: translateY(0); }
    }
    .bg-primary-500 { @apply bg-blue-500; }
    .text-primary-600 { @apply text-blue-600; }
    .hover\:text-primary-500:hover { @apply text-blue-500; }
    .bg-accent-500 { @apply bg-purple-500; }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">{% trans "Inventory Dashboard" %}</h1>
        <p class="text-gray-600">{% trans "Manage your inventory items, stock levels, and movements" %}</p>
    </div>

    <!-- Breadcrumbs -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
            <li class="inline-flex items-center">
                <a href="/core/supply-chain/inventory/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                    {% trans "Dashboard" %}
                </a>
            </li>
            <li class="flex items-center">
                <i class="fas fa-chevron-right text-gray-400 mx-2 {% if LANGUAGE_CODE == 'ar' %}rotate-180{% endif %}"></i>
                <span class="text-gray-700">{% trans "Inventory" %}</span>
            </li>
        </ol>
    </nav>

    <!-- Stats Cards Row -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <!-- Total Items Card -->
        <div class="card hover-shadow hover-scale animate-fade-in">
            <div class="p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">{% trans "Total Items" %}</p>
                        <p class="text-3xl font-bold text-gray-800 mt-1">{{ total_items }}</p>
                    </div>
                    <div class="p-3 rounded-full bg-primary-500 text-white">
                        <i class="fas fa-boxes w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{{ inventory_item_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
                        {% trans "View all items" %} 
                        <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                        <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Low Stock Items Card -->
        <div class="card hover-shadow hover-scale animate-fade-in">
            <div class="p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">{% trans "Low Stock Items" %}</p>
                        <p class="text-3xl font-bold text-gray-800 mt-1">{{ low_stock_count }}</p>
                    </div>
                    <div class="p-3 rounded-full {% if low_stock_count > 0 %}bg-yellow-400{% else %}bg-green-500{% endif %} text-white">
                        <i class="fas fa-exclamation-triangle w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{{ inventory_low_stock_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
                        {% trans "View low stock items" %} 
                        <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                        <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Stock Movements Card -->
        <div class="card hover-shadow hover-scale animate-fade-in">
            <div class="p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">{% trans "Stock Movements" %}</p>
                        <p class="text-3xl font-bold text-gray-800 mt-1">{{ total_movements }}</p>
                        <p class="text-sm text-gray-500 mt-1">{% trans "Last 30 days" %}</p>
                    </div>
                    <div class="p-3 rounded-full bg-blue-400 text-white">
                        <i class="fas fa-exchange-alt w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{{ inventory_movement_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
                        {% trans "View all movements" %} 
                        <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                        <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Card -->
        <div class="card hover-shadow hover-scale animate-fade-in">
            <div class="p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">{% trans "Quick Actions" %}</p>
                        <p class="text-sm text-gray-700 mt-1">{% trans "Manage your inventory" %}</p>
                    </div>
                    <div class="p-3 rounded-full bg-accent-500 text-white">
                        <i class="fas fa-bolt w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4 space-y-2">
                    <a href="{{ inventory_item_create_url }}" class="block text-sm font-medium text-primary-600 hover:text-primary-500">
                        {% trans "Add new item" %} 
                        <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                        <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
                    </a>
                    <a href="{{ inventory_movement_create_url }}" class="block text-sm font-medium text-primary-600 hover:text-primary-500">
                        {% trans "Record movement" %} 
                        <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                        <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
                    </a>
                    <a href="{{ inventory_scan_barcode_url }}" class="block text-sm font-medium text-primary-600 hover:text-primary-500">
                        {% trans "Scan barcode" %} 
                        <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                        <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Items Section -->
    <div class="mt-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-900">{% trans "Recent Items" %}</h2>
            <a href="{{ inventory_item_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
                {% trans "View all" %} 
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
            </a>
        </div>
        
        <div class="overflow-hidden bg-white rounded-lg shadow">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Item Code" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Name" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Category" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Current Stock" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} text-gray-500 uppercase">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for item in recent_items %}
                    <tr class="hover:bg-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ item.sku }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ item.name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ item.category|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                                {% if item.current_stock < item.min_stock_level %}
                                    text-red-800 bg-red-100
                                {% elif item.current_stock <= item.min_stock_level|add:"5" %}
                                    text-yellow-800 bg-yellow-100
                                {% else %}
                                    text-blue-800 bg-blue-100
                                {% endif %}">
                                {{ item.current_stock }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm font-medium text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} whitespace-nowrap">
                            <a href="{{ item.get_absolute_url }}" class="text-primary-600 hover:text-primary-900 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                                {% trans "View" %}
                            </a>
                            <a href="{{ item.get_edit_url }}" class="text-primary-600 hover:text-primary-900">
                                {% trans "Edit" %}
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-sm text-gray-500 text-center">
                            {% trans "No items found" %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Movements Section -->
    <div class="mt-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-900">{% trans "Recent Movements" %}</h2>
            <a href="{{ inventory_movement_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
                {% trans "View all" %} 
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
            </a>
        </div>
        
        <div class="overflow-hidden bg-white rounded-lg shadow">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Item" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Type" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Quantity" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} text-gray-500 uppercase">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for movement in recent_movements %}
                    <tr class="hover:bg-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ movement.created_at|date:"Y-m-d H:i" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ movement.item.name }}</div>
                            <div class="text-xs text-gray-500">{{ movement.item.sku }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                                {% if movement.is_inbound %}
                                    text-blue-800 bg-blue-100
                                {% elif movement.is_outbound %}
                                    text-red-800 bg-red-100
                                {% else %}
                                    text-gray-800 bg-gray-100
                                {% endif %}">
                                {{ movement.get_movement_name }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium 
                                {% if movement.is_inbound %}
                                    text-blue-600
                                {% elif movement.is_outbound %}
                                    text-red-600
                                {% endif %}">
                                {{ movement.quantity }}
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm font-medium text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} whitespace-nowrap">
                            <a href="#" class="text-primary-600 hover:text-primary-900">
                                {% trans "View" %}
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-sm text-gray-500 text-center">
                            {% trans "No movements found" %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Operation Compatibilities Section -->
    <div class="mt-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-900">{% trans "Common Operations" %}</h2>
            <a href="{{ inventory_item_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
                {% trans "View all items" %} 
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if LANGUAGE_CODE == 'ar' %}hidden{% endif %}"></i>
                <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %} {% if not LANGUAGE_CODE == 'ar' %}hidden{% endif %} {% if LANGUAGE_CODE == 'ar' %}inline-block{% endif %}"></i>
            </a>
        </div>
        
        <div class="overflow-hidden bg-white rounded-lg shadow">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Operation" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Item" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Description" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Duration" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-gray-500 uppercase">
                            {% trans "Stock" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} text-gray-500 uppercase">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for compat in operation_compatibilities %}
                    <tr class="hover:bg-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ compat.operation_type.name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ compat.item.name }}</div>
                            <div class="text-xs text-gray-500">{{ compat.item.sku }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ compat.get_operation_description_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ compat.duration_minutes_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
                                {% if compat.item.current_stock < compat.item.min_stock_level %}
                                    text-red-800 bg-red-100
                                {% elif compat.item.current_stock <= compat.item.min_stock_level|add:"5" %}
                                    text-yellow-800 bg-yellow-100
                                {% else %}
                                    text-blue-800 bg-blue-100
                                {% endif %}">
                                {{ compat.item.current_stock }} {{ compat.item.unit_of_measurement.symbol|default:"" }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm font-medium text-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} whitespace-nowrap">
                            <a href="{% url 'work_orders:work_order_create' %}?operation_type_id={{ compat.operation_type.id }}&item_id={{ compat.item.id }}&duration={{ compat.duration_minutes }}&description={{ compat.operation_description }}" class="text-primary-600 hover:text-primary-900 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                                {% trans "Create Work Order" %}
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-sm text-gray-500 text-center">
                            {% trans "No operation compatibilities found" %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Stock Alerts Section -->
    {% if low_stock_items %}
    <div class="mt-8">
        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %}">
                    <h3 class="text-sm font-medium text-red-800">
                        {% trans "Low Stock Alerts" %}
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc {% if LANGUAGE_CODE == 'ar' %}pr-5{% else %}pl-5{% endif %} space-y-1">
                            {% for item in low_stock_items %}
                            <li>
                                {{ item.name }} ({{ item.sku }}): {{ item.current_stock }} {% trans "remaining" %} 
                                ({% trans "Min" %}: {{ item.min_stock_level }})
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %} 