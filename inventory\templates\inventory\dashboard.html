{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .quick-action {
        transition: all 0.3s ease;
    }
    .quick-action:hover {
        transform: translateY(-2px);
    }
    .loading-spinner {
        border: 2px solid #f3f4f6;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        width: 1rem;
        height: 1rem;
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">


    <!-- UPDATED: Added slow-moving items card - v2.0 -->
    <!-- Statistics Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
        <!-- Total Items Card -->
        <div class="dashboard-card bg-white rounded-lg shadow p-6 transition-all duration-200 cursor-pointer hover:bg-gray-50" onclick="window.location.href='{% url 'inventory:item_list' %}'">
            <div class="flex items-center">
                <div class="p-3 rounded-lg bg-blue-500 text-white {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                    <i class="fas fa-boxes text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">{% trans "إجمالي الأصناف" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_items|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Low Stock Items Card -->
        <div class="dashboard-card bg-white rounded-lg shadow p-6 transition-all duration-200 cursor-pointer hover:bg-gray-50" onclick="window.location.href='{% url 'inventory:low_stock_items' %}'">
            <div class="flex items-center">
                <div class="p-3 rounded-lg bg-red-500 text-white {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">{% trans "مخزون منخفض" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ low_stock_count|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Slow Moving Items Card -->
        <div class="dashboard-card bg-white rounded-lg shadow p-6 transition-all duration-200 cursor-pointer hover:bg-gray-50" onclick="window.location.href='{% url 'inventory:slow_moving_items' %}'">
            <div class="flex items-center">
                <div class="p-3 rounded-lg bg-orange-500 text-white {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                    <i class="fas fa-clock text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">{% trans "الأصناف الراكدة" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ slow_moving_items|length|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Categories Card -->
        <div class="dashboard-card bg-white rounded-lg shadow p-6 transition-all duration-200 cursor-pointer hover:bg-gray-50" onclick="window.location.href='{% url 'inventory:category_list' %}'">
            <div class="flex items-center">
                <div class="p-3 rounded-lg bg-green-500 text-white {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                    <i class="fas fa-tags text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">{% trans "الفئات" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_categories|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Stock Value Card -->
        <div class="dashboard-card bg-white rounded-lg shadow p-6 transition-all duration-200 cursor-pointer hover:bg-gray-50" onclick="window.location.href='{% url 'inventory:stock_valuation_report' %}'">
            <div class="flex items-center">
                <div class="p-3 rounded-lg bg-yellow-500 text-white {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                    <i class="fas fa-dollar-sign text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">{% trans "قيمة المخزون" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_stock_value|floatformat:2|default:"0.00" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-bolt text-blue-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            {% trans "الإجراءات السريعة" %}
        </h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="{% url 'inventory:item_create' %}" class="quick-action bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-4 text-center transition-all duration-200">
                <i class="fas fa-plus text-2xl mb-2"></i>
                <p class="text-sm font-medium">{% trans "إضافة صنف" %}</p>
            </a>
            <a href="{% url 'inventory:stock_movement_create' %}" class="quick-action bg-green-600 hover:bg-green-700 text-white rounded-lg p-4 text-center transition-all duration-200">
                <i class="fas fa-layer-group text-2xl mb-2"></i>
                <p class="text-sm font-medium">{% trans "حركة مخزون" %}</p>
            </a>
            <a href="{% url 'inventory:category_create' %}" class="quick-action bg-purple-600 hover:bg-purple-700 text-white rounded-lg p-4 text-center transition-all duration-200">
                <i class="fas fa-tag text-2xl mb-2"></i>
                <p class="text-sm font-medium">{% trans "إضافة فئة" %}</p>
            </a>
            <button id="quick-stock-adjustment" class="quick-action bg-orange-600 hover:bg-orange-700 text-white rounded-lg p-4 text-center transition-all duration-200">
                <i class="fas fa-adjust text-2xl mb-2"></i>
                <p class="text-sm font-medium">{% trans "تعديل سريع" %}</p>
            </button>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Stock Movements -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-history text-blue-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "حركات المخزون الأخيرة" %}
                    </h3>
                    <a href="{% url 'inventory:stock_movement_list' %}" class="text-blue-600 hover:text-blue-800 text-sm">
                        {% trans "عرض الكل" %} <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_movements %}
                    <div class="space-y-4">
                        {% for movement in recent_movements %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-2 rounded-lg 
                                    {% if movement.movement_type == 'purchase' or movement.movement_type == 'adjustment_in' %}bg-green-100 text-green-600
                                    {% elif movement.movement_type == 'sale' or movement.movement_type == 'adjustment_out' %}bg-red-100 text-red-600
                                    {% else %}bg-blue-100 text-blue-600{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                                    <i class="fas fa-
                                        {% if movement.movement_type == 'purchase' %}shopping-cart
                                        {% elif movement.movement_type == 'sale' %}cash-register
                                        {% elif movement.movement_type == 'adjustment_in' %}plus
                                        {% elif movement.movement_type == 'adjustment_out' %}minus
                                        {% else %}exchange-alt{% endif %} text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ movement.item.name }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ movement.get_movement_type_display }} - 
                                        {% if movement.movement_type == 'purchase' or movement.movement_type == 'adjustment_in' %}+{% elif movement.movement_type == 'sale' or movement.movement_type == 'adjustment_out' %}-{% endif %}{{ movement.quantity }}
                                        {{ movement.item.unit_of_measure.symbol|default:"" }}
                                    </p>
                                </div>
                            </div>
                            <div class="text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %}">
                                <p class="text-xs text-gray-500">{{ movement.created_at|date:"d/m/Y" }}</p>
                                <p class="text-xs text-gray-500">{{ movement.created_at|time:"H:i" }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i class="fas fa-history text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">{% trans "لا توجد حركات مخزون حديثة" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Top Categories & Low Stock Alerts -->
        <div class="space-y-6">
            <!-- Top Categories -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-pie text-green-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "أهم الفئات" %}
                    </h3>
                </div>
                <div class="p-6">
                    {% if top_categories %}
                        <div class="space-y-3">
                            {% for category in top_categories %}
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></div>
                                    <span class="text-sm font-medium text-gray-900">{{ category.name }}</span>
                                </div>
                                <span class="text-sm text-gray-500">{{ category.item_count }} {% trans "صنف" %}</span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-tags text-2xl text-gray-300 mb-2"></i>
                            <p class="text-gray-500 text-sm">{% trans "لا توجد فئات" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Low Stock Alerts -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "تنبيهات المخزون" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div id="low-stock-alerts">
                        <div class="text-center py-4">
                            <div class="loading-spinner mx-auto"></div>
                            <p class="text-gray-500 text-sm mt-2">{% trans "جاري التحميل..." %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stock Adjustment Modal -->
    <div id="quick-adjustment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">{% trans "تعديل المخزون السريع" %}</h3>
                    <button id="close-adjustment-modal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="quick-adjustment-form">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "الصنف" %}</label>
                            <select id="adjustment-item" class="form-select w-full rounded-md border-gray-300">
                                <option value="">{% trans "اختر الصنف" %}</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "نوع التعديل" %}</label>
                            <select id="adjustment-type" class="form-select w-full rounded-md border-gray-300">
                                <option value="adjustment_in">{% trans "إضافة للمخزون" %}</option>
                                <option value="adjustment_out">{% trans "خصم من المخزون" %}</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "الكمية" %}</label>
                            <input type="number" id="adjustment-quantity" class="form-input w-full rounded-md border-gray-300" step="0.01" min="0.01">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "السبب" %}</label>
                            <textarea id="adjustment-reason" class="form-textarea w-full rounded-md border-gray-300" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="flex justify-end mt-6 space-x-3 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                        <button type="button" id="cancel-adjustment" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            {% trans "إلغاء" %}
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            {% trans "تطبيق التعديل" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load low stock alerts
    loadLowStockAlerts();
    
    // Initialize quick stock adjustment modal
    initQuickStockAdjustment();
});

function loadLowStockAlerts() {
    fetch('{% url "inventory:api_low_stock_items" %}')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('low-stock-alerts');
            
            if (data.success && data.items.length > 0) {
                let html = '<div class="space-y-2">';
                data.items.forEach(item => {
                    html += `
                        <div class="flex items-center justify-between p-2 bg-red-50 border border-red-200 rounded-md">
                            <div>
                                <p class="text-sm font-medium text-red-800">${item.name}</p>
                                <p class="text-xs text-red-600">${item.sku}</p>
                            </div>
                            <div class="text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %}">
                                <p class="text-xs text-red-600">{% trans "متوفر" %}: ${item.current_stock}</p>
                                <p class="text-xs text-red-500">{% trans "الحد الأدنى" %}: ${item.minimum_stock_level}</p>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-2xl text-green-400 mb-2"></i>
                        <p class="text-gray-500 text-sm">{% trans "جميع الأصناف متوفرة" %}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading low stock alerts:', error);
            document.getElementById('low-stock-alerts').innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-2"></i>
                    <p class="text-red-500 text-sm">{% trans "خطأ في تحميل التنبيهات" %}</p>
                </div>
            `;
        });
}

function initQuickStockAdjustment() {
    const modal = document.getElementById('quick-adjustment-modal');
    const openBtn = document.getElementById('quick-stock-adjustment');
    const closeBtn = document.getElementById('close-adjustment-modal');
    const cancelBtn = document.getElementById('cancel-adjustment');
    const form = document.getElementById('quick-adjustment-form');
    
    // Open modal
    openBtn.addEventListener('click', function() {
        modal.classList.remove('hidden');
        loadItemsForAdjustment();
    });
    
    // Close modal
    function closeModal() {
        modal.classList.add('hidden');
        form.reset();
    }
    
    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    
    // Close on background click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        submitQuickAdjustment();
    });
}

function loadItemsForAdjustment() {
    const itemSelect = document.getElementById('adjustment-item');
    
    fetch('{% url "inventory:api_search_items" %}?term=')
        .then(response => response.json())
        .then(data => {
            itemSelect.innerHTML = '<option value="">{% trans "اختر الصنف" %}</option>';
            
            if (data.results) {
                data.results.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.name} (${item.sku}) - {% trans "متوفر" %}: ${item.current_stock}`;
                    itemSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading items:', error);
            // Show notification if notification system is available
            if (window.notificationSystem) {
                window.notificationSystem.show('{% trans "خطأ في تحميل الأصناف" %}', 'error');
            }
        });
}

function submitQuickAdjustment() {
    const formData = {
        item_id: document.getElementById('adjustment-item').value,
        adjustment_type: document.getElementById('adjustment-type').value,
        quantity: document.getElementById('adjustment-quantity').value,
        reason: document.getElementById('adjustment-reason').value
    };
    
    // Validate form
    if (!formData.item_id || !formData.quantity || !formData.reason) {
        if (window.notificationSystem) {
            window.notificationSystem.show('{% trans "يرجى ملء جميع الحقول المطلوبة" %}', 'error');
        } else {
            alert('{% trans "يرجى ملء جميع الحقول المطلوبة" %}');
        }
        return;
    }
    
    fetch('{% url "inventory:api_quick_stock_adjustment" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (window.notificationSystem) {
                window.notificationSystem.show(data.message, 'success');
            } else {
                alert(data.message);
            }
            document.getElementById('quick-adjustment-modal').classList.add('hidden');
            document.getElementById('quick-adjustment-form').reset();
            
            // Reload the page to update statistics
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            if (window.notificationSystem) {
                window.notificationSystem.show(data.error, 'error');
            } else {
                alert(data.error);
            }
        }
    })
    .catch(error => {
        console.error('Error submitting adjustment:', error);
        if (window.notificationSystem) {
            window.notificationSystem.show('{% trans "حدث خطأ في تطبيق التعديل" %}', 'error');
        } else {
            alert('{% trans "حدث خطأ في تطبيق التعديل" %}');
        }
    });
}
</script>
{% endblock %} 