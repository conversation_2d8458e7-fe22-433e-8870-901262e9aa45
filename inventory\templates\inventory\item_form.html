{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "إضافة صنف" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">{% trans "إضافة صنف جديد" %}</h1>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "اسم الصنف" %}
                    </label>
                    <input type="text" name="name" id="id_name" required
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="{% trans 'أدخل اسم الصنف' %}">
                </div>
                
                <div>
                    <label for="id_part_number" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "رقم القطعة" %}
                    </label>
                    <input type="text" name="part_number" id="id_part_number"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="{% trans 'رقم القطعة (اختياري)' %}">
                </div>
                
                <div>
                    <label for="id_category" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الفئة" %}
                    </label>
                    <select name="category" id="id_category" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">{% trans "اختر فئة" %}</option>
                        <!-- Options will be populated by the view -->
                    </select>
                </div>
                
                <div>
                    <label for="id_unit" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الوحدة" %}
                    </label>
                    <input type="text" name="unit" id="id_unit"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="{% trans 'مثل: قطعة، كيلو، لتر' %}">
                </div>
                
                <div>
                    <label for="id_minimum_stock" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الحد الأدنى للمخزون" %}
                    </label>
                    <input type="number" name="minimum_stock" id="id_minimum_stock" min="0"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="0">
                </div>
                
                <div>
                    <label for="id_cost" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "التكلفة" %}
                    </label>
                    <input type="number" name="cost" id="id_cost" min="0" step="0.01"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="0.00">
                </div>
            </div>
            
            <div>
                <label for="id_description" class="block text-sm font-medium text-gray-700 mb-2">
                    {% trans "الوصف" %}
                </label>
                <textarea name="description" id="id_description" rows="3" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2"
                          placeholder="{% trans 'وصف الصنف (اختياري)' %}"></textarea>
            </div>
            
            <div class="flex justify-end space-x-4">
                <a href="/core/supply-chain/inventory/" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors">
                    {% trans "إلغاء" %}
                </a>
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors">
                    {% trans "حفظ" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 