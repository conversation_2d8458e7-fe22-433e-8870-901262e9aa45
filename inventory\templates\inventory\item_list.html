{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .form-input, .form-select {
        @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">


    <!-- Search and Filter Section -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
        <form method="get" class="grid grid-cols-1 md:grid-cols-5 gap-3 items-end">
            <!-- Search -->
            <div class="md:col-span-2">
                <div class="relative">
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="{% trans 'ابحث بالاسم أو الرمز أو الباركود' %}"
                           class="w-full {% if LANGUAGE_CODE == 'ar' %}pr-10 pl-3{% else %}pl-10 pr-3{% endif %} py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                    <div class="absolute inset-y-0 {% if LANGUAGE_CODE == 'ar' %}right-0 pr-3{% else %}left-0 pl-3{% endif %} flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <!-- Category Filter -->
            <div>
                <select id="category" name="category" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                    <option value="">{% trans "جميع الفئات" %}</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Stock Level Filter -->
            <div>
                <select id="stock_level" name="stock_level" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                    <option value="">{% trans "جميع الأصناف" %}</option>
                    <option value="in_stock" {% if current_stock_filter == "in_stock" %}selected{% endif %}>{% trans "متوفر" %}</option>
                    <option value="low" {% if current_stock_filter == "low" %}selected{% endif %}>{% trans "مخزون منخفض" %}</option>
                    <option value="out_of_stock" {% if current_stock_filter == "out_of_stock" %}selected{% endif %}>{% trans "نفد المخزون" %}</option>
                </select>
            </div>
            
            <!-- Actions -->
            <div class="flex gap-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm transition-colors">
                    <i class="fas fa-search"></i>
                </button>
                <a href="{% url 'inventory:item_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-md text-sm transition-colors">
                    <i class="fas fa-times"></i>
                </a>
                <a href="{% url 'inventory:item_create' %}" class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm transition-colors">
                    <i class="fas fa-plus"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- Items Grid/List -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">
                    {% trans "الأصناف" %}
                    {% if items %}
                        <span class="text-sm font-normal text-gray-500">({{ paginator.count }} {% trans "صنف" %})</span>
                    {% endif %}
                </h3>
                <div class="flex items-center space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                    <!-- View Toggle -->
                    <div class="flex rounded-md shadow-sm">
                        <button id="grid-view" class="bg-blue-600 text-white px-3 py-2 {% if LANGUAGE_CODE == 'ar' %}rounded-r-md{% else %}rounded-l-md{% endif %} text-sm transition-colors duration-200">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button id="list-view" class="bg-gray-200 text-gray-700 px-3 py-2 {% if LANGUAGE_CODE == 'ar' %}rounded-l-md{% else %}rounded-r-md{% endif %} text-sm transition-colors duration-200">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Items Content -->
        <div class="p-6">
            {% if items %}
                <!-- Grid View (Default) -->
                <div id="items-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {% for item in items %}
                    <div class="dashboard-card bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-all duration-200">
                        <!-- Item Header -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                                    <i class="fas fa-box text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="text-sm font-semibold text-gray-900 truncate">{{ item.name }}</h4>
                                    <p class="text-xs text-gray-500">{{ item.sku }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                {% if not item.is_active %}
                                    <span class="w-2 h-2 bg-red-500 rounded-full" title="{% trans 'غير نشط' %}"></span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Stock Status -->
                        <div class="mb-3">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-xs text-gray-600">{% trans "المخزون الحالي" %}</span>
                                <span class="text-xs font-medium 
                                    {% if item.current_stock <= item.minimum_stock_level %}text-red-600
                                    {% elif item.current_stock <= item.minimum_stock_level|add:5 %}text-yellow-600
                                    {% else %}text-green-600{% endif %}">
                                    {{ item.current_stock }} {{ item.unit_of_measure.symbol|default:"" }}
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                {% widthratio item.current_stock item.minimum_stock_level|add:20 100 as stock_percentage %}
                                <div class="h-2 rounded-full
                                    {% if item.current_stock <= item.minimum_stock_level %}bg-red-500
                                    {% elif item.current_stock <= item.minimum_stock_level|add:5 %}bg-yellow-500
                                    {% else %}bg-green-500{% endif %}"
                                    style="width: {{ stock_percentage|default:0 }}%"></div>
                            </div>
                        </div>
                        
                        <!-- Category & Price -->
                        <div class="space-y-2 mb-4">
                            {% if item.category %}
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-600">{% trans "الفئة" %}</span>
                                <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full">{{ item.category.name }}</span>
                            </div>
                            {% endif %}
                            {% if item.selling_price %}
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-600">{% trans "السعر" %}</span>
                                <span class="text-xs font-medium text-gray-900">{{ item.selling_price|floatformat:2 }} {% trans "ج.م" %}</span>
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                            <a href="{% url 'inventory:item_detail' item.pk %}" 
                               class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-2 rounded-md text-center transition-colors duration-200">
                                <i class="fas fa-eye {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                {% trans "عرض" %}
                            </a>
                            <a href="{% url 'inventory:item_edit' item.pk %}" 
                               class="flex-1 bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-2 rounded-md text-center transition-colors duration-200">
                                <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                {% trans "تعديل" %}
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- List View (Hidden by default) -->
                <div id="items-table" class="hidden overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الصنف" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الفئة" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "المخزون الحالي" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الحد الأدنى" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "السعر" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الحالة" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الإجراءات" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for item in items %}
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                                            <i class="fas fa-box text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                                            <div class="text-sm text-gray-500">{{ item.sku }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if item.category %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ item.category.name }}
                                    </span>
                                    {% else %}
                                    <span class="text-gray-400">-</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if item.current_stock <= item.minimum_stock_level %}bg-red-100 text-red-800
                                        {% elif item.current_stock <= item.minimum_stock_level|add:5 %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-green-100 text-green-800{% endif %}">
                                        {{ item.current_stock }} {{ item.unit_of_measure.symbol|default:"" }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ item.minimum_stock_level }} {{ item.unit_of_measure.symbol|default:"" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {% if item.selling_price %}
                                        {{ item.selling_price|floatformat:2 }} {% trans "ج.م" %}
                                    {% else %}
                                        <span class="text-gray-400">-</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if item.is_active %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {% trans "نشط" %}
                                    </span>
                                    {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        {% trans "غير نشط" %}
                                    </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                        <a href="{% url 'inventory:item_detail' item.pk %}" class="text-blue-600 hover:text-blue-900">
                                            {% trans "عرض" %}
                                        </a>
                                        <a href="{% url 'inventory:item_edit' item.pk %}" class="text-indigo-600 hover:text-indigo-900">
                                            {% trans "تعديل" %}
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center">
                        <p class="text-sm text-gray-700">
                            {% trans "عرض" %}
                            <span class="font-medium">{{ page_obj.start_index }}</span>
                            {% trans "إلى" %}
                            <span class="font-medium">{{ page_obj.end_index }}</span>
                            {% trans "من" %}
                            <span class="font-medium">{{ paginator.count }}</span>
                            {% trans "نتيجة" %}
                        </p>
                    </div>
                    
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if current_category %}category={{ current_category }}&{% endif %}{% if current_stock_filter %}stock_level={{ current_stock_filter }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                               class="relative inline-flex items-center px-2 py-2 {% if LANGUAGE_CODE == 'ar' %}rounded-l-md{% else %}rounded-r-md{% endif %} border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}"></i>
                            </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if current_category %}category={{ current_category }}&{% endif %}{% if current_stock_filter %}stock_level={{ current_stock_filter }}&{% endif %}page={{ num }}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if current_category %}category={{ current_category }}&{% endif %}{% if current_stock_filter %}stock_level={{ current_stock_filter }}&{% endif %}page={{ page_obj.next_page_number }}" 
                               class="relative inline-flex items-center px-2 py-2 {% if LANGUAGE_CODE == 'ar' %}rounded-r-md{% else %}rounded-l-md{% endif %} border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %}"></i>
                            </a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <!-- Empty State -->
                <div class="text-center py-12">
                    <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-boxes text-3xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد أصناف" %}</h3>
                    <p class="text-gray-500 mb-6">
                        {% if search_query or current_category or current_stock_filter %}
                            {% trans "لم يتم العثور على أصناف تطابق معايير البحث" %}
                        {% else %}
                            {% trans "ابدأ بإضافة أول صنف للمخزون" %}
                        {% endif %}
                    </p>
                    <a href="{% url 'inventory:item_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md transition-colors duration-200 inline-flex items-center">
                        <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "إضافة صنف جديد" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View toggle functionality
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');
    const itemsGrid = document.getElementById('items-grid');
    const itemsTable = document.getElementById('items-table');
    
    // Load saved view preference
    const savedView = localStorage.getItem('inventory-view') || 'grid';
    if (savedView === 'list') {
        switchToListView();
    }
    
    gridViewBtn.addEventListener('click', function() {
        switchToGridView();
        localStorage.setItem('inventory-view', 'grid');
    });
    
    listViewBtn.addEventListener('click', function() {
        switchToListView();
        localStorage.setItem('inventory-view', 'list');
    });
    
    function switchToGridView() {
        gridViewBtn.classList.remove('bg-gray-200', 'text-gray-700');
        gridViewBtn.classList.add('bg-blue-600', 'text-white');
        listViewBtn.classList.remove('bg-blue-600', 'text-white');
        listViewBtn.classList.add('bg-gray-200', 'text-gray-700');
        
        itemsGrid.classList.remove('hidden');
        itemsTable.classList.add('hidden');
    }
    
    function switchToListView() {
        listViewBtn.classList.remove('bg-gray-200', 'text-gray-700');
        listViewBtn.classList.add('bg-blue-600', 'text-white');
        gridViewBtn.classList.remove('bg-blue-600', 'text-white');
        gridViewBtn.classList.add('bg-gray-200', 'text-gray-700');
        
        itemsTable.classList.remove('hidden');
        itemsGrid.classList.add('hidden');
    }
    
    // Auto-submit form on filter change
    const categorySelect = document.getElementById('category');
    const stockLevelSelect = document.getElementById('stock_level');
    
    categorySelect.addEventListener('change', function() {
        this.form.submit();
    });
    
    stockLevelSelect.addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
{% endblock %} 