{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .urgency-critical { border-left: 4px solid #dc2626; background-color: #fef2f2; }
    .urgency-high { border-left: 4px solid #f59e0b; background-color: #fffbeb; }
    .urgency-low { border-left: 4px solid #10b981; background-color: #f0fdf4; }
    .batch-tag {
        display: inline-block;
        padding: 2px 6px;
        background-color: #e5e7eb;
        color: #374151;
        border-radius: 4px;
        font-size: 11px;
        margin: 1px;
    }
    .table-header-icon {
        margin-right: 8px;
        color: #6b7280;
    }
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
    }
    .status-critical {
        background-color: #fef2f2;
        color: #dc2626;
    }
    .status-high {
        background-color: #fffbeb;
        color: #f59e0b;
    }
    .status-low {
        background-color: #f0fdf4;
        color: #10b981;
    }
    .action-btn {
        padding: 6px 12px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s;
    }
    .action-btn:hover {
        transform: translateY(-1px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header with Back Button -->
    <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-4 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                <a href="/core/supply-chain/inventory/" class="flex items-center text-gray-600 hover:text-gray-800 transition-colors">
                    <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                    {% trans "العودة" %}
                </a>
                <div class="border-r border-gray-300 h-6"></div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></i>
                        {{ page_title }}
                    </h1>
                    <p class="text-gray-600 mt-1">{{ page_subtitle }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-3 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                <a href="{% url 'inventory:item_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                    {% trans "إضافة صنف" %}
                </a>
            </div>
        </div>

        <!-- Horizontal Separator -->
        <hr class="border-gray-200 mb-6">
        
        <!-- Additional vertical separator line -->
        <div class="border-b border-gray-200 mb-4"></div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg bg-red-100 text-red-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{% trans "إجمالي المنخفض" %}</p>
                        <p class="text-xl font-bold text-gray-900">{{ low_stock_items|length }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg bg-orange-100 text-orange-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                        <i class="fas fa-exclamation"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{% trans "عالي الأولوية" %}</p>
                        <p class="text-xl font-bold text-gray-900">
                            {% for item_data in low_stock_items %}{% if item_data.urgency_level >= 2 %}1{% else %}0{% endif %}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg bg-gray-100 text-gray-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{% trans "نفد المخزون" %}</p>
                        <p class="text-xl font-bold text-gray-900">
                            {% for item_data in low_stock_items %}{% if item_data.urgency_level == 3 %}1{% else %}0{% endif %}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg bg-blue-100 text-blue-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{% trans "دفعات نشطة" %}</p>
                        <p class="text-xl font-bold text-gray-900">
                            {% for item_data in low_stock_items %}{{ item_data.active_batches|length|add:"0" }}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    {% if low_stock_items %}
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-table table-header-icon"></i>
                    {% trans "الأصناف منخفضة المخزون" %}
                </h3>
            </div>
            
            <!-- Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-cube table-header-icon"></i>{% trans "الصنف" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-tags table-header-icon"></i>{% trans "الفئة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-boxes table-header-icon"></i>{% trans "المخزون الحالي" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-chart-line table-header-icon"></i>{% trans "الحد الأدنى" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-dollar-sign table-header-icon"></i>{% trans "السعر" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-info-circle table-header-icon"></i>{% trans "الحالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-cogs table-header-icon"></i>{% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item_data in low_stock_items %}
                        <tr class="hover:bg-gray-50 transition-colors
                            {% if item_data.urgency_level == 3 %}bg-red-50
                            {% elif item_data.urgency_level == 2 %}bg-orange-50{% endif %}">
                            
                            <!-- Item Name & SKU -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                            <i class="fas fa-cube text-blue-600"></i>
                                        </div>
                                    </div>
                                    <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                                        <div class="text-sm font-medium text-gray-900">{{ item_data.item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ item_data.item.sku }}</div>
                                    </div>
                                </div>
                            </td>
                            
                            <!-- Category -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {% if item_data.item.classification %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-tag {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                            {{ item_data.item.classification.name }}
                                        </span>
                                    {% else %}
                                        <span class="text-gray-400">-</span>
                                    {% endif %}
                                </div>
                            </td>
                            
                            <!-- Current Stock -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ item_data.item.quantity }}
                                    {% if item_data.item.unit_of_measurement %}
                                        <span class="text-gray-500">{{ item_data.item.unit_of_measurement.symbol }}</span>
                                    {% endif %}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {% trans "النقص:" %} {{ item_data.shortage }}
                                </div>
                            </td>
                            
                            <!-- Min Level -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ item_data.item.min_stock_level }}</div>
                            </td>
                            
                            <!-- Price -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {% if item_data.item.unit_price %}
                                        {{ item_data.item.unit_price|floatformat:2 }}
                                        <span class="text-gray-500 text-xs">ج.م</span>
                                    {% else %}
                                        <span class="text-gray-400">-</span>
                                    {% endif %}
                                </div>
                            </td>
                            
                            <!-- Status -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if item_data.urgency_level == 3 %}
                                    <span class="status-badge status-critical">
                                        <i class="fas fa-times-circle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                        {% trans "نفد المخزون" %}
                                    </span>
                                {% elif item_data.urgency_level == 2 %}
                                    <span class="status-badge status-high">
                                        <i class="fas fa-exclamation-triangle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                        {% trans "عالي الأولوية" %}
                                    </span>
                                {% else %}
                                    <span class="status-badge status-low">
                                        <i class="fas fa-info-circle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                        {% trans "منخفض" %}
                                    </span>
                                {% endif %}
                            </td>
                            
                            <!-- Actions -->
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                    <a href="{% url 'inventory:item_detail' item_data.item.pk %}" 
                                       class="action-btn bg-blue-100 text-blue-600 hover:bg-blue-200" 
                                       title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:item_edit' item_data.item.pk %}" 
                                       class="action-btn bg-green-100 text-green-600 hover:bg-green-200" 
                                       title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:stock_movement_create' %}?item={{ item_data.item.pk }}" 
                                       class="action-btn bg-purple-100 text-purple-600 hover:bg-purple-200" 
                                       title="{% trans 'إضافة مخزون' %}">
                                        <i class="fas fa-plus-circle"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- Batch Information Row (Expandable) -->
                        {% if item_data.active_batches %}
                        <tr class="bg-gray-50">
                            <td colspan="7" class="px-6 py-3">
                                <div class="text-sm">
                                    <span class="font-medium text-gray-700">
                                        <i class="fas fa-layer-group {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                                        {% trans "الدفعات النشطة:" %}
                                    </span>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        {% for batch in item_data.active_batches %}
                                            <span class="batch-tag">
                                                {{ batch.batch_number }} 
                                                ({{ batch.current_quantity }}
                                                {% if item_data.item.unit_of_measurement %}
                                                    {{ item_data.item.unit_of_measurement.symbol }}
                                                {% endif %})
                                            </span>
                                        {% endfor %}
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endif %}
                        
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Bottom separator -->
            <div class="border-t border-gray-200 bg-gray-50 px-6 py-2">
                <div class="text-sm text-gray-500 text-center">
                    <i class="fas fa-info-circle mr-2"></i>
                    {% trans "إجمالي الأصناف المعروضة:" %} {{ low_stock_items|length }}
                </div>
            </div>
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm border p-12 text-center">
            <div class="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-check-circle text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "جميع الأصناف بمستوى مخزون مناسب" %}</h3>
            <p class="text-gray-500 mb-6">{% trans "لا توجد أصناف تحتاج إعادة تموين في الوقت الحالي" %}</p>
            <a href="{% url 'inventory:item_list' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-list {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "عرض جميع الأصناف" %}
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function requestRestockQuote(itemId, shortage) {
    // Implement restock quote functionality
    alert('طلب عرض سعر للصنف: ' + itemId + ' بكمية: ' + shortage);
    // You can implement this to redirect to purchases module or open a modal
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Low stock items page loaded');
});
</script>
{% endblock %} 