{% extends 'core/base.html' %}
{% load i18n %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Create Movement" %}{% endblock %}

{% block extra_head %}
<style>
    .movement-type-select {
        margin-bottom: 20px;
    }
    .movement-type-card {
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.2s;
    }
    .movement-type-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    .movement-type-card.selected {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    .movement-type-icon {
        font-size: 2em;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h2>{% trans "Create Inventory Movement" %}</h2>
                </div>
                <div class="card-body">
                    <form method="post" id="movement-form">
                        {% csrf_token %}
                        
                        <!-- Movement Type Selection -->
                        <div class="mb-4">
                            <h4>{% trans "Select Movement Type" %}</h4>
                            <input type="hidden" name="movement_type_ref" id="id_movement_type_ref">
                            <input type="hidden" name="movement_type" id="id_movement_type">
                            
                            <ul class="nav nav-tabs" id="movementTypeTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="inbound-tab" data-bs-toggle="tab" data-bs-target="#inbound" type="button" role="tab">
                                        <i class="fas fa-arrow-down"></i> {% trans "Inbound" %}
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="outbound-tab" data-bs-toggle="tab" data-bs-target="#outbound" type="button" role="tab">
                                        <i class="fas fa-arrow-up"></i> {% trans "Outbound" %}
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="transfer-tab" data-bs-toggle="tab" data-bs-target="#transfer" type="button" role="tab">
                                        <i class="fas fa-exchange-alt"></i> {% trans "Transfer" %}
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="legacy-tab" data-bs-toggle="tab" data-bs-target="#legacy" type="button" role="tab">
                                        <i class="fas fa-history"></i> {% trans "Legacy Types" %}
                                    </button>
                                </li>
                            </ul>
                            
                            <div class="tab-content mt-3" id="movementTypeContent">
                                <!-- Inbound Types -->
                                <div class="tab-pane fade show active" id="inbound" role="tabpanel">
                                    <div class="row">
                                        {% for type in movement_types_inbound %}
                                        <div class="col-md-3 mb-3">
                                            <div class="card movement-type-card" data-type-id="{{ type.id }}" data-type-code="{{ type.code }}">
                                                <div class="card-body text-center">
                                                    <div class="movement-type-icon">
                                                        <i class="fas fa-{{ type.icon|default:'box' }}" style="color: {{ type.color|default:'#6c757d' }}"></i>
                                                    </div>
                                                    <h5 class="card-title">{{ type.name }}</h5>
                                                    <small class="text-muted">{{ type.description|truncatechars:60 }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        {% empty %}
                                        <div class="col-12">
                                            <div class="alert alert-info">{% trans "No inbound movement types defined. Please configure them in settings." %}</div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                
                                <!-- Outbound Types -->
                                <div class="tab-pane fade" id="outbound" role="tabpanel">
                                    <div class="row">
                                        {% for type in movement_types_outbound %}
                                        <div class="col-md-3 mb-3">
                                            <div class="card movement-type-card" data-type-id="{{ type.id }}" data-type-code="{{ type.code }}">
                                                <div class="card-body text-center">
                                                    <div class="movement-type-icon">
                                                        <i class="fas fa-{{ type.icon|default:'box' }}" style="color: {{ type.color|default:'#6c757d' }}"></i>
                                                    </div>
                                                    <h5 class="card-title">{{ type.name }}</h5>
                                                    <small class="text-muted">{{ type.description|truncatechars:60 }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        {% empty %}
                                        <div class="col-12">
                                            <div class="alert alert-info">{% trans "No outbound movement types defined. Please configure them in settings." %}</div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                
                                <!-- Transfer Types -->
                                <div class="tab-pane fade" id="transfer" role="tabpanel">
                                    <div class="row">
                                        {% for type in movement_types_transfer %}
                                        <div class="col-md-3 mb-3">
                                            <div class="card movement-type-card" data-type-id="{{ type.id }}" data-type-code="{{ type.code }}">
                                                <div class="card-body text-center">
                                                    <div class="movement-type-icon">
                                                        <i class="fas fa-{{ type.icon|default:'box' }}" style="color: {{ type.color|default:'#6c757d' }}"></i>
                                                    </div>
                                                    <h5 class="card-title">{{ type.name }}</h5>
                                                    <small class="text-muted">{{ type.description|truncatechars:60 }}</small>
                                                </div>
                                            </div>
                                        </div>
                                        {% empty %}
                                        <div class="col-12">
                                            <div class="alert alert-info">{% trans "No transfer movement types defined. Please configure them in settings." %}</div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                
                                <!-- Legacy Types -->
                                <div class="tab-pane fade" id="legacy" role="tabpanel">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="legacy_movement_type">{% trans "Legacy Movement Type" %}</label>
                                                <select class="form-select" id="legacy_movement_type">
                                                    <option value="">-- {% trans "Select a type" %} --</option>
                                                    <option value="purchase">{% trans "Purchase" %}</option>
                                                    <option value="sale">{% trans "Sale" %}</option>
                                                    <option value="adjustment">{% trans "Adjustment" %}</option>
                                                    <option value="transfer">{% trans "Transfer" %}</option>
                                                    <option value="return">{% trans "Return" %}</option>
                                                </select>
                                                <small class="text-muted">{% trans "Legacy movement types are kept for backward compatibility" %}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="selected-type-info" class="alert alert-info mt-3" style="display: none;">
                                <h5 id="selected-type-title"></h5>
                                <p id="selected-type-description"></p>
                            </div>
                        </div>
                        
                        <!-- Item Selection -->
                        <div class="form-row">
                            <div class="col-md-6 mb-3">
                                {{ form.item|as_crispy_field }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.quantity|as_crispy_field }}
                            </div>
                            <div class="col-md-3 mb-3">
                                {{ form.unit_of_measurement|as_crispy_field }}
                            </div>
                        </div>
                        
                        <!-- Reference and Notes -->
                        <div class="form-row">
                            <div class="col-md-6 mb-3">
                                {{ form.reference|as_crispy_field }}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.notes|as_crispy_field }}
                            </div>
                        </div>
                        
                        <!-- Hidden fields -->
                        {{ form.tenant_id|as_crispy_field }}
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'inventory:movement_list' %}" class="btn btn-secondary">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary" id="submit-btn" disabled>
                                {% trans "Create Movement" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(function() {
        // Handle movement type selection
        $('.movement-type-card').click(function() {
            // Clear other selections
            $('.movement-type-card').removeClass('selected');
            $(this).addClass('selected');
            
            // Set the selected type
            var typeId = $(this).data('type-id');
            var typeCode = $(this).data('type-code');
            $('#id_movement_type_ref').val(typeId);
            $('#id_movement_type').val('');
            
            // Update UI
            $('#selected-type-title').text($(this).find('.card-title').text());
            $('#selected-type-description').text($(this).find('small').text());
            $('#selected-type-info').show();
            
            // Enable submit button
            $('#submit-btn').prop('disabled', false);
        });
        
        // Handle legacy type selection
        $('#legacy_movement_type').change(function() {
            var legacyType = $(this).val();
            if (legacyType) {
                // Clear modern type selection
                $('.movement-type-card').removeClass('selected');
                $('#id_movement_type_ref').val('');
                $('#id_movement_type').val(legacyType);
                
                // Update UI
                $('#selected-type-title').text($(this).find('option:selected').text());
                $('#selected-type-description').text('{% trans "Legacy movement type" %}');
                $('#selected-type-info').show();
                
                // Enable submit button
                $('#submit-btn').prop('disabled', false);
            } else {
                $('#id_movement_type').val('');
                $('#selected-type-info').hide();
                $('#submit-btn').prop('disabled', true);
            }
        });
        
        // Validate form before submission
        $('#movement-form').submit(function(e) {
            var hasMovementType = $('#id_movement_type_ref').val() || $('#id_movement_type').val();
            if (!hasMovementType) {
                e.preventDefault();
                alert('{% trans "Please select a movement type" %}');
                return false;
            }
            return true;
        });
    });
</script>
{% endblock %} 