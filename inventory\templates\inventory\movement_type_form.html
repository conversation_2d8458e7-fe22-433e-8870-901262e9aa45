{% extends 'core/base.html' %}
{% load i18n %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
{% if form.instance.pk %}
{% trans "Edit Movement Type" %}: {{ form.instance.name }}
{% else %}
{% trans "Create Movement Type" %}
{% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h2>
                        {% if form.instance.pk %}
                        {% trans "Edit Movement Type" %}: {{ form.instance.name }}
                        {% else %}
                        {% trans "Create Movement Type" %}
                        {% endif %}
                    </h2>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                {% if not form.instance.pk %}
                                {{ form.code|as_crispy_field }}
                                {% endif %}
                                {{ form.name|as_crispy_field }}
                                {{ form.description|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>{% trans "Movement Direction" %}</label>
                                    <div class="row">
                                        <div class="col">
                                            {{ form.is_inbound|as_crispy_field }}
                                        </div>
                                        <div class="col">
                                            {{ form.is_outbound|as_crispy_field }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>{% trans "Requirements" %}</label>
                                    <div class="row">
                                        <div class="col">
                                            {{ form.requires_reference|as_crispy_field }}
                                        </div>
                                        <div class="col">
                                            {{ form.requires_approval|as_crispy_field }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                {{ form.icon|as_crispy_field }}
                                <small class="form-text text-muted">{% trans "Font Awesome icon name (e.g. 'tag', 'shopping-cart', etc.)" %}</small>
                            </div>
                            <div class="col-md-4">
                                {{ form.color|as_crispy_field }}
                                <small class="form-text text-muted">{% trans "Hex color code (e.g. '#007bff')" %}</small>
                            </div>
                            <div class="col-md-4">
                                {{ form.sequence|as_crispy_field }}
                            </div>
                        </div>
                        
                        {{ form.is_active|as_crispy_field }}
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'inventory:movement_type_list' %}" class="btn btn-secondary">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                {% if form.instance.pk %}
                                {% trans "Update Movement Type" %}
                                {% else %}
                                {% trans "Create Movement Type" %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            {% if form.instance.pk %}
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h3>{% trans "Usage Information" %}</h3>
                </div>
                <div class="card-body">
                    <p>{% trans "This movement type is used to track inventory movements." %}</p>
                    <dl class="row">
                        <dt class="col-sm-4">{% trans "Movement Count" %}</dt>
                        <dd class="col-sm-8">{{ form.instance.movements.count }}</dd>
                        
                        <dt class="col-sm-4">{% trans "Code" %}</dt>
                        <dd class="col-sm-8"><code>{{ form.instance.code }}</code></dd>
                        
                        <dt class="col-sm-4">{% trans "Workflow" %}</dt>
                        <dd class="col-sm-8">
                            {% if form.instance.is_inbound and form.instance.is_outbound %}
                            <span class="badge bg-info">{% trans "Transfer" %}</span>
                            {% elif form.instance.is_inbound %}
                            <span class="badge bg-success">{% trans "Inbound" %}</span>
                            {% elif form.instance.is_outbound %}
                            <span class="badge bg-danger">{% trans "Outbound" %}</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 