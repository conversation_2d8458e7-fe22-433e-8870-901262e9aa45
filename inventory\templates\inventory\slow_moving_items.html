{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .table-header-icon {
        margin-right: 8px;
        color: #6b7280;
    }
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
    }
    .status-critical { background-color: #fef2f2; color: #dc2626; }
    .status-warning { background-color: #fffbeb; color: #f59e0b; }
    .status-info { background-color: #dbeafe; color: #1e40af; }
    .action-btn {
        padding: 6px 12px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s;
    }
    .action-btn:hover {
        transform: translateY(-1px);
    }
    .days-indicator {
        font-family: 'Courier New', monospace;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header with Back Button -->
    <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-4 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                <a href="/core/supply-chain/inventory/" class="flex items-center text-gray-600 hover:text-gray-800 transition-colors">
                    <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                    {% trans "العودة" %}
                </a>
                <div class="border-r border-gray-300 h-6"></div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-clock text-orange-500 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></i>
                        {{ page_title }}
                    </h1>
                    <p class="text-gray-600 mt-1">{{ page_subtitle }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-3 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                <button onclick="generateSlowMovingReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-file-export {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                    {% trans "تصدير التقرير" %}
                </button>
            </div>
        </div>
        
        <!-- Horizontal Separator -->
        <hr class="border-gray-200 mb-6">
        
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg bg-orange-100 text-orange-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{% trans "إجمالي الأصناف الراكدة" %}</p>
                        <p class="text-xl font-bold text-gray-900">{{ slow_moving_items|length }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg bg-red-100 text-red-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{% trans "أصناف منتهية الصلاحية" %}</p>
                        <p class="text-xl font-bold text-gray-900">
                            {% for item_data in slow_moving_items %}{% if item_data.expiring_batches %}1{% else %}0{% endif %}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg bg-yellow-100 text-yellow-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{% trans "قيمة المخزون الراكد" %}</p>
                        <p class="text-xl font-bold text-gray-900">
                            {% for item_data in slow_moving_items %}{{ item_data.total_batch_value|add:"0"|floatformat:0 }}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                    <div class="p-2 rounded-lg bg-blue-100 text-blue-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">{% trans "إجمالي الدفعات" %}</p>
                        <p class="text-xl font-bold text-gray-900">
                            {% for item_data in slow_moving_items %}{{ item_data.active_batches_count|add:"0" }}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slow Moving Items Table -->
    {% if slow_moving_items %}
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-table table-header-icon"></i>
                    {% trans "تفاصيل الأصناف الراكدة" %}
                </h3>
            </div>
            
            <!-- Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-cube table-header-icon"></i>{% trans "الصنف" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-tags table-header-icon"></i>{% trans "الفئة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-boxes table-header-icon"></i>{% trans "المخزون الحالي" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-calendar table-header-icon"></i>{% trans "آخر حركة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-dollar-sign table-header-icon"></i>{% trans "القيمة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-layer-group table-header-icon"></i>{% trans "الدفعات" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-info-circle table-header-icon"></i>{% trans "الحالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-cogs table-header-icon"></i>{% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item_data in slow_moving_items %}
                        <tr class="hover:bg-gray-50 transition-colors
                            {% if item_data.days_since_last_movement > 180 %}bg-red-50
                            {% elif item_data.days_since_last_movement > 90 %}bg-orange-50{% endif %}">
                            
                            <!-- Item Name & SKU -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-lg bg-orange-100 flex items-center justify-center">
                                            <i class="fas fa-clock text-orange-600"></i>
                                        </div>
                                    </div>
                                    <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                                        <div class="text-sm font-medium text-gray-900">{{ item_data.item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ item_data.item.sku }}</div>
                                    </div>
                                </div>
                            </td>
                            
                            <!-- Category -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {% if item_data.item.classification %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-tag {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                            {{ item_data.item.classification.name }}
                                        </span>
                                    {% else %}
                                        <span class="text-gray-400">-</span>
                                    {% endif %}
                                </div>
                            </td>
                            
                            <!-- Current Stock -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ item_data.item.quantity }}
                                    {% if item_data.item.unit_of_measurement %}
                                        <span class="text-gray-500">{{ item_data.item.unit_of_measurement.symbol }}</span>
                                    {% endif %}
                                </div>
                                <div class="text-xs text-gray-500">
                                    {% trans "الحد الأدنى:" %} {{ item_data.item.min_stock_level }}
                                </div>
                            </td>
                            
                            <!-- Last Movement -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if item_data.last_movement %}
                                    <div class="text-sm font-medium text-gray-900 days-indicator">
                                        {{ item_data.days_since_last_movement }} {% trans "يوم" %}
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {{ item_data.last_movement.created_at|date:"d/m/Y" }}
                                    </div>
                                {% else %}
                                    <span class="text-gray-400">-</span>
                                {% endif %}
                            </td>
                            
                            <!-- Value -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ item_data.total_batch_value|floatformat:2|default:"0.00" }}
                                    <span class="text-gray-500 text-xs">ج.م</span>
                                </div>
                            </td>
                            
                            <!-- Batches -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-center">
                                    <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">
                                        {{ item_data.active_batches_count }}
                                    </span>
                                    {% if item_data.expiring_batches %}
                                        <div class="text-xs text-red-600 mt-1">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            {% trans "منتهية" %}
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            
                            <!-- Status -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if item_data.days_since_last_movement %}
                                    {% if item_data.days_since_last_movement > 180 %}
                                        <span class="status-badge status-critical">
                                            <i class="fas fa-exclamation-triangle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                            {% trans "حرج" %}
                                        </span>
                                    {% elif item_data.days_since_last_movement > 90 %}
                                        <span class="status-badge status-warning">
                                            <i class="fas fa-exclamation {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                            {% trans "تحذير" %}
                                        </span>
                                    {% else %}
                                        <span class="status-badge status-info">
                                            <i class="fas fa-info-circle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                            {% trans "مراقبة" %}
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="status-badge status-info">
                                        <i class="fas fa-question-circle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                        {% trans "غير محدد" %}
                                    </span>
                                {% endif %}
                            </td>
                            
                            <!-- Actions -->
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                    <a href="{% url 'inventory:item_detail' item_data.item.pk %}" 
                                       class="action-btn bg-blue-100 text-blue-600 hover:bg-blue-200" 
                                       title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button onclick="applyDiscount('{{ item_data.item.pk }}')" 
                                            class="action-btn bg-yellow-100 text-yellow-600 hover:bg-yellow-200" 
                                            title="{% trans 'تطبيق خصم' %}">
                                        <i class="fas fa-percent"></i>
                                    </button>
                                    <button onclick="createPromotion('{{ item_data.item.pk }}')" 
                                            class="action-btn bg-green-100 text-green-600 hover:bg-green-200" 
                                            title="{% trans 'إنشاء عرض' %}">
                                        <i class="fas fa-bullhorn"></i>
                                    </button>
                                    <a href="{% url 'inventory:stock_movement_create' %}?item={{ item_data.item.pk }}" 
                                       class="action-btn bg-purple-100 text-purple-600 hover:bg-purple-200" 
                                       title="{% trans 'حركة مخزون' %}">
                                        <i class="fas fa-exchange-alt"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- Configuration Details Row (Expandable) -->
                        {% if item_data.config %}
                        <tr class="bg-gray-50">
                            <td colspan="8" class="px-6 py-3">
                                <div class="text-sm">
                                    <span class="font-medium text-gray-700">
                                        <i class="fas fa-cog {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                                        {% trans "إعدادات الصنف:" %}
                                    </span>
                                    <div class="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                                        <div>
                                            <span class="text-gray-500">{% trans "أيام عدم الحركة:" %}</span>
                                            <span class="font-medium">{{ item_data.config.days_without_movement }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">{% trans "خصم تلقائي:" %}</span>
                                            <span class="font-medium">{{ item_data.config.auto_discount_percentage|default:"0" }}%</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">{% trans "حد السرعة:" %}</span>
                                            <span class="font-medium">{{ item_data.config.movement_velocity_threshold|default:"0" }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">{% trans "تفعيل التنبيهات:" %}</span>
                                            <span class="font-medium">
                                                {% if item_data.config.enable_notifications %}
                                                    <i class="fas fa-check text-green-600"></i>
                                                {% else %}
                                                    <i class="fas fa-times text-red-600"></i>
                                                {% endif %}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endif %}
                        
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm border p-12 text-center">
            <div class="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-check-circle text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد أصناف راكدة" %}</h3>
            <p class="text-gray-500 mb-6">{% trans "جميع الأصناف تتحرك بشكل طبيعي في المخزون" %}</p>
            <a href="{% url 'inventory:item_list' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-list {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "عرض جميع الأصناف" %}
            </a>
        </div>
    {% endif %}
</div>

<!-- JavaScript for Actions -->
<script>
function generateSlowMovingReport() {
    const table = document.querySelector('table');
    if (!table) {
        alert('{% trans "لا توجد بيانات لتصديرها" %}');
        return;
    }
    
    let csv = [];
    
    // Headers
    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
    csv.push(headers.join(','));
    
    // Data rows (excluding config rows)
    const rows = table.querySelectorAll('tbody tr:not(.bg-gray-50)');
    rows.forEach(row => {
        const cells = Array.from(row.querySelectorAll('td')).map(td => {
            let text = td.textContent.trim();
            return `"${text.replace(/"/g, '""')}"`;
        });
        csv.push(cells.join(','));
    });
    
    // Download
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'slow_moving_items_report.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function applyDiscount(itemId) {
    const discount = prompt('{% trans "أدخل نسبة الخصم (%):" %}');
    if (discount && !isNaN(discount) && discount > 0 && discount <= 100) {
        // Here you would normally send an AJAX request to apply the discount
        alert(`{% trans "سيتم تطبيق خصم" %} ${discount}% {% trans "على الصنف" %} ${itemId}`);
    }
}

function createPromotion(itemId) {
    const promotion = prompt('{% trans "أدخل تفاصيل العرض:" %}');
    if (promotion) {
        // Here you would normally send an AJAX request to create a promotion
        alert(`{% trans "سيتم إنشاء عرض للصنف" %} ${itemId}: ${promotion}`);
    }
}
</script>
{% endblock %} 