{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "إضافة حركة مخزن" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">{% trans "إضافة حركة مخزن" %}</h1>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="id_item" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الصنف" %}
                    </label>
                    <select name="item" id="id_item" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">{% trans "اختر صنف" %}</option>
                        <!-- Options will be populated by the view -->
                    </select>
                </div>
                
                <div>
                    <label for="id_movement_type" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "نوع الحركة" %}
                    </label>
                    <select name="movement_type" id="id_movement_type" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">{% trans "اختر نوع الحركة" %}</option>
                        <option value="in">{% trans "استلام" %}</option>
                        <option value="out">{% trans "صرف" %}</option>
                    </select>
                </div>
                
                <div>
                    <label for="id_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الكمية" %}
                    </label>
                    <input type="number" name="quantity" id="id_quantity" min="1" step="1" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                
                <div>
                    <label for="id_location" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الموقع" %}
                    </label>
                    <select name="location" id="id_location" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">{% trans "اختر موقع" %}</option>
                        <!-- Options will be populated by the view -->
                    </select>
                </div>
            </div>
            
            <div>
                <label for="id_notes" class="block text-sm font-medium text-gray-700 mb-2">
                    {% trans "ملاحظات" %}
                </label>
                <textarea name="notes" id="id_notes" rows="3" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2"
                          placeholder="{% trans 'ملاحظات إضافية (اختيارية)' %}"></textarea>
            </div>
            
            <div class="flex justify-end space-x-4">
                <a href="/core/supply-chain/inventory/" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors">
                    {% trans "إلغاء" %}
                </a>
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors">
                    {% trans "حفظ" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 