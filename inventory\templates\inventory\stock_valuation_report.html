{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .valuation-table {
        border-collapse: collapse;
        width: 100%;
    }
    .valuation-table th,
    .valuation-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }
    .valuation-table th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #374151;
    }
    .value-bar {
        height: 4px;
        background-color: #e5e7eb;
        border-radius: 2px;
        overflow: hidden;
    }
    .value-fill {
        height: 100%;
        background-color: #3b82f6;
        transition: width 0.3s ease;
    }
    .method-badge {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 500;
    }
    .method-fifo { background-color: #dbeafe; color: #1e40af; }
    .method-lifo { background-color: #fef3c7; color: #92400e; }
    .method-avg { background-color: #d1fae5; color: #065f46; }
    .method-standard { background-color: #f3e8ff; color: #6b21a8; }
    .table-header-icon {
        margin-right: 8px;
        color: #6b7280;
    }
    .valuation-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
    }
    .action-btn {
        padding: 6px 12px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.2s;
    }
    .action-btn:hover {
        transform: translateY(-1px);
    }
    .value-cell {
        font-family: 'Courier New', monospace;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header with Back Button -->
    <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-4 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                <a href="/core/supply-chain/inventory/" class="flex items-center text-gray-600 hover:text-gray-800 transition-colors">
                    <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                    {% trans "العودة" %}
                </a>
                <div class="border-r border-gray-300 h-6"></div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-chart-bar text-green-600 {% if LANGUAGE_CODE == 'ar' %}ml-3{% else %}mr-3{% endif %}"></i>
                        {{ page_title }}
                    </h1>
                    <p class="text-gray-600 mt-1">{{ page_subtitle }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-3 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-download {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                    {% trans "تصدير التقرير" %}
                </button>
            </div>
        </div>

        <!-- Horizontal Separator -->
        <hr class="border-gray-200 mb-6">

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-blue-100 text-blue-600 {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 font-medium">{% trans "إجمالي القيمة" %}</p>
                        <p class="text-2xl font-bold text-gray-900 value-cell">{{ total_value|floatformat:2 }}</p>
                        <p class="text-xs text-gray-500">ج.م</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-purple-100 text-purple-600 {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                        <i class="fas fa-boxes text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 font-medium">{% trans "إجمالي الكمية" %}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_quantity|floatformat:0 }}</p>
                        <p class="text-xs text-gray-500">{% trans "وحدة" %}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-green-100 text-green-600 {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                        <i class="fas fa-cube text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 font-medium">{% trans "عدد الأصناف" %}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_items }}</p>
                        <p class="text-xs text-gray-500">{% trans "صنف" %}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-yellow-100 text-yellow-600 {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %}">
                        <i class="fas fa-calculator text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 font-medium">{% trans "متوسط القيمة" %}</p>
                        <p class="text-2xl font-bold text-gray-900 value-cell">{{ avg_item_value|floatformat:2 }}</p>
                        <p class="text-xs text-gray-500">ج.م</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Valuation Table -->
    {% if valuation_data %}
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-table table-header-icon"></i>
                    {% trans "تفاصيل تقييم المخزون" %}
                </h3>
            </div>
            
            <!-- Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-cube table-header-icon"></i>{% trans "الصنف" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-tags table-header-icon"></i>{% trans "الفئة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-boxes table-header-icon"></i>{% trans "الكمية" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-calculator table-header-icon"></i>{% trans "متوسط التكلفة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-dollar-sign table-header-icon"></i>{% trans "إجمالي القيمة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-layer-group table-header-icon"></i>{% trans "الدفعات" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-chart-line table-header-icon"></i>{% trans "طريقة التقييم" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-percent table-header-icon"></i>{% trans "النسبة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <i class="fas fa-cogs table-header-icon"></i>{% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item_data in valuation_data %}
                        <tr class="hover:bg-gray-50 transition-colors">
                            
                            <!-- Item Name & SKU -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                            <i class="fas fa-cube text-blue-600"></i>
                                        </div>
                                    </div>
                                    <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                                        <div class="text-sm font-medium text-gray-900">{{ item_data.item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ item_data.item.sku }}</div>
                                    </div>
                                </div>
                            </td>
                            
                            <!-- Category -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {% if item_data.item.classification %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-tag {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                            {{ item_data.item.classification.name }}
                                        </span>
                                    {% else %}
                                        <span class="text-gray-400">-</span>
                                    {% endif %}
                                </div>
                            </td>
                            
                            <!-- Quantity -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ item_data.quantity|floatformat:0 }}
                                    {% if item_data.item.unit_of_measurement %}
                                        <span class="text-gray-500">{{ item_data.item.unit_of_measurement.symbol }}</span>
                                    {% endif %}
                                </div>
                            </td>
                            
                            <!-- Average Cost -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 value-cell">
                                    {{ item_data.avg_cost|floatformat:2 }}
                                    <span class="text-gray-500 text-xs">ج.م</span>
                                </div>
                            </td>
                            
                            <!-- Total Value -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-green-600 value-cell">
                                    {{ item_data.total_value|floatformat:2 }}
                                    <span class="text-gray-500 text-xs">ج.م</span>
                                </div>
                            </td>
                            
                            <!-- Batch Count -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-center">
                                    <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">
                                        {{ item_data.batch_count }}
                                    </span>
                                </div>
                            </td>
                            
                            <!-- Valuation Method -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="valuation-badge 
                                    {% if item_data.valuation_method == 'FIFO' %}method-fifo
                                    {% elif item_data.valuation_method == 'LIFO' %}method-lifo
                                    {% else %}method-average{% endif %}">
                                    <i class="fas fa-chart-line {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                                    {{ item_data.valuation_method }}
                                </span>
                            </td>
                            
                            <!-- Percentage -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {% widthratio item_data.total_value total_value 100 %}%
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {% widthratio item_data.total_value total_value 100 %}%"></div>
                                </div>
                            </td>
                            
                            <!-- Actions -->
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
                                    <a href="{% url 'inventory:item_detail' item_data.item.pk %}" 
                                       class="action-btn bg-blue-100 text-blue-600 hover:bg-blue-200" 
                                       title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:item_edit' item_data.item.pk %}" 
                                       class="action-btn bg-green-100 text-green-600 hover:bg-green-200" 
                                       title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="showBatchDetails('{{ item_data.item.pk }}')" 
                                            class="action-btn bg-purple-100 text-purple-600 hover:bg-purple-200" 
                                            title="{% trans 'تفاصيل الدفعات' %}">
                                        <i class="fas fa-layer-group"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <!-- Table Footer with Totals -->
                    <tfoot class="bg-gray-50">
                        <tr class="font-semibold">
                            <td colspan="2" class="px-6 py-4 text-sm text-gray-900">
                                <i class="fas fa-calculator {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                                {% trans "الإجمالي" %}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">{{ total_quantity|floatformat:0 }}</td>
                            <td class="px-6 py-4 text-sm text-gray-900">-</td>
                            <td class="px-6 py-4 text-sm text-green-600 value-cell">{{ total_value|floatformat:2 }} ج.م</td>
                            <td class="px-6 py-4 text-sm text-gray-900">-</td>
                            <td class="px-6 py-4 text-sm text-gray-900">-</td>
                            <td class="px-6 py-4 text-sm text-gray-900">100%</td>
                            <td class="px-6 py-4"></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm border p-12 text-center">
            <div class="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-chart-bar text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد بيانات تقييم" %}</h3>
            <p class="text-gray-500 mb-6">{% trans "لا توجد أصناف لإنشاء تقرير تقييم مخزون" %}</p>
            <a href="{% url 'inventory:item_create' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "إضافة صنف جديد" %}
            </a>
        </div>
    {% endif %}
</div>

<!-- JavaScript for Export and Batch Details -->
<script>
function exportReport() {
    // Create CSV content
    const table = document.querySelector('table');
    let csv = [];
    
    // Headers
    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
    csv.push(headers.join(','));
    
    // Data rows
    const rows = table.querySelectorAll('tbody tr:not(.bg-gray-50)');
    rows.forEach(row => {
        const cells = Array.from(row.querySelectorAll('td')).map(td => {
            // Extract text content, handling special cases
            let text = td.textContent.trim();
            return `"${text.replace(/"/g, '""')}"`;
        });
        csv.push(cells.join(','));
    });
    
    // Download
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'stock_valuation_report.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showBatchDetails(itemId) {
    // This would open a modal or navigate to batch details
    // For now, just show an alert
    alert('{% trans "سيتم عرض تفاصيل الدفعات للصنف" %} ' + itemId);
}
</script>
{% endblock %} 