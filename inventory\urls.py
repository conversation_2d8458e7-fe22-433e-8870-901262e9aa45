from django.urls import path
from . import views

app_name = 'inventory'

urlpatterns = [
    # Dashboard
    path('', views.inventory_dashboard, name='dashboard'),
    path('test/', views.test_dashboard, name='test_dashboard'),

    
    # Item Management
    path('items/', views.ItemListView.as_view(), name='item_list'),
    path('items/create/', views.ItemCreateView.as_view(), name='item_create'),
    path('items/<uuid:pk>/', views.ItemDetailView.as_view(), name='item_detail'),
    path('items/<uuid:pk>/edit/', views.ItemUpdateView.as_view(), name='item_edit'),
    
    # Stock Movements
    path('movements/', views.StockMovementListView.as_view(), name='stock_movement_list'),
    path('movements/create/', views.create_stock_movement, name='stock_movement_create'),
    
    # Categories
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('categories/create/', views.CategoryCreateView.as_view(), name='category_create'),
    
    # Slow Moving Items
    path('slow-moving/', views.SlowMovingItemsView.as_view(), name='slow_moving_items'),
    
    # Low Stock Items
    path('low-stock/', views.LowStockItemsView.as_view(), name='low_stock_items'),
    
    # Stock Valuation Report
    path('stock-valuation/', views.StockValuationReportView.as_view(), name='stock_valuation_report'),
    
    # Vehicle Compatibility
    path('compatibility/', views.vehicle_compatibility_manager, name='compatibility_manager'),
    
    # API Endpoints
    path('api/search-items/', views.api_search_items, name='api_search_items'),
    path('api/items/<uuid:item_id>/stock-info/', views.api_item_stock_info, name='api_item_stock_info'),
    path('api/quick-stock-adjustment/', views.api_quick_stock_adjustment, name='api_quick_stock_adjustment'),
    path('api/low-stock-items/', views.api_low_stock_items, name='api_low_stock_items'),
    path('api/items/', views.api_items_list, name='api_items_list'),
    
    # API Aliases for modal compatibility
    path('api/stock-adjustment/', views.api_quick_stock_adjustment, name='stock_adjustment'),
] 