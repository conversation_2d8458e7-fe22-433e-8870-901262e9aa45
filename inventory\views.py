from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy, reverse
from django.utils.translation import gettext_lazy as _
from feature_flags.middleware import requires_module, requires_feature
from inventory.models import Item, Movement, UnitOfMeasurement, UnitConversion, MovementType, OperationCompatibility
from django.db.models import Count, Sum, Avg, F, Q
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.core.exceptions import ValidationError
from core.views import TenantCreateView, TenantListView, TenantDetailView, TenantUpdateView
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.contrib.auth.decorators import permission_required
import json
from datetime import datetime, timedelta
from django.utils import timezone
from decimal import Decimal

# Import forms
from .forms import ItemForm, CategoryForm, StockMovementForm, QuickStockAdjustmentForm, ItemSearchForm

# Import models
from .models import Item, ItemClassification, Movement, UnitOfMeasurement, VehicleCompatibility, ItemBatch, SlowMovingStockConfig


@requires_module('inventory')
class DashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'inventory/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        tenant_id = getattr(self.request, 'tenant_id', None)
        queryset = Item.objects.all()
        
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
        
        # Get basic stats
        context['total_items'] = queryset.count()
        context['low_stock_count'] = queryset.filter(
            quantity__lt=F('min_stock_level')
        ).count()
        
        # Recent items
        context['recent_items'] = queryset.order_by('-created_at')[:5]
        
        # Add low stock items for alerts section
        context['low_stock_items'] = queryset.filter(
            quantity__lt=F('min_stock_level')
        ).order_by('quantity')[:5]
        
        # Operation compatibilities for common operations
        compat_queryset = OperationCompatibility.objects.all()
        if tenant_id:
            compat_queryset = compat_queryset.for_tenant(tenant_id)
        
        # Filter to show common operations first, then required ones
        context['operation_compatibilities'] = compat_queryset.filter(
            Q(is_common=True) | Q(is_required=True)
        ).select_related('item', 'operation_type', 'item__unit_of_measurement').order_by('-is_common', '-is_required')[:10]
        
        # Movement stats if available
        movement_queryset = Movement.objects.all()
        if tenant_id:
            movement_queryset = movement_queryset.for_tenant(tenant_id)
        
        context['total_movements'] = movement_queryset.count()
        context['recent_movements'] = movement_queryset.order_by('-created_at')[:5]
        
        # Units of measurement stats
        unit_queryset = UnitOfMeasurement.objects.all()
        if tenant_id:
            unit_queryset = unit_queryset.for_tenant(tenant_id)
        
        context['total_units'] = unit_queryset.count()
        context['recent_units'] = unit_queryset.order_by('-created_at')[:5]
        
        # Add URLs for the template
        context['inventory_item_list_url'] = reverse('inventory:item_list')
        context['inventory_low_stock_url'] = reverse('inventory:item_list') + '?low_stock=1'
        context['inventory_movement_list_url'] = reverse('inventory:movement_list')
        context['inventory_item_create_url'] = reverse('inventory:item_create')
        context['inventory_movement_create_url'] = reverse('inventory:movement_create')
        context['inventory_scan_barcode_url'] = reverse('inventory:scan_barcode')
        
        context['title'] = _('Inventory Dashboard')
        
        if tenant_id:
            # Get dashboard statistics
            items = Item.objects.filter(tenant_id=tenant_id)
            
            context.update({
                'total_categories': ItemClassification.objects.filter(tenant_id=tenant_id, is_active=True).count(),
                'total_stock_value': items.aggregate(
                    total_value=Sum(F('quantity') * F('unit_price'))
                )['total_value'] or 0,
                
                # Top categories by item count
                'top_categories': ItemClassification.objects.filter(
                    tenant_id=tenant_id, 
                    is_active=True
                ).annotate(
                    item_count=Count('items')
                ).filter(item_count__gt=0).order_by('-item_count')[:5],
                
                # Slow-moving stock
                'slow_moving_items': self.get_slow_moving_items(tenant_id),
                
                # Batch statistics
                'batch_stats': self.get_batch_statistics(tenant_id),
                
                # Expiring batches
                'expiring_soon_batches': self.get_expiring_batches(tenant_id),
            })
        else:
            context.update({
                'total_categories': 0,
                'total_stock_value': 0,
                'top_categories': [],
                'slow_moving_items': [],
                'batch_stats': {},
                'expiring_soon_batches': [],
            })
        
        return context
    
    def get_slow_moving_items(self, tenant_id):
        """Get items that are considered slow-moving based on configuration"""
        slow_moving_items = []
        
        # Get all active items
        items = Item.objects.filter(tenant_id=tenant_id)
        
        for item in items:
            if item.is_slow_moving():
                config = item.get_slow_moving_config()
                if config:
                    # Get the last movement date
                    last_movement = item.movements.order_by('-created_at').first()
                    days_since_last_movement = None
                    
                    if last_movement:
                        days_since_last_movement = (timezone.now().date() - last_movement.created_at.date()).days
                    
                    slow_moving_items.append({
                        'item': item,
                        'config': config,
                        'days_since_last_movement': days_since_last_movement,
                        'last_movement': last_movement,
                    })
        
        return sorted(slow_moving_items, key=lambda x: x['days_since_last_movement'] or 0, reverse=True)[:10]
    
    def get_batch_statistics(self, tenant_id):
        """Get batch-related statistics"""
        from django.db.models import Sum, Count
        
        batches = ItemBatch.objects.filter(tenant_id=tenant_id)
        
        return {
            'total_batches': batches.count(),
            'active_batches': batches.filter(status='active').count(),
            'expired_batches': batches.filter(status='expired').count(),
            'total_batch_value': batches.filter(status='active').aggregate(
                total_value=Sum(F('current_quantity') * F('purchase_price'))
            )['total_value'] or 0,
        }
    
    def get_expiring_batches(self, tenant_id, days_ahead=30):
        """Get batches expiring within specified days"""
        if not days_ahead:
            days_ahead = 30
            
        cutoff_date = timezone.now().date() + timedelta(days=days_ahead)
        
        return ItemBatch.objects.filter(
            tenant_id=tenant_id,
            status='active',
            expiry_date__lte=cutoff_date,
            expiry_date__gte=timezone.now().date()
        ).select_related('item').order_by('expiry_date')[:10]


@requires_module('inventory')
class ItemListView(TenantListView):
    model = Item
    template_name = 'inventory/item_list.html'
    context_object_name = 'items'
    
    def get_queryset(self):
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        queryset = Item.objects.all()
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
            
        # Only select related fields that are actual foreign keys
        queryset = queryset.select_related('classification', 'unit_of_measurement').order_by('name')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(sku__icontains=search_query) |
                Q(description__icontains=search_query)
            )
        
        # Category filter (using the CharField category, not classification)
        category = self.request.GET.get('category', '')
        if category:
            queryset = queryset.filter(category=category)
        
        # Stock level filter
        stock_filter = self.request.GET.get('stock_level', '')
        if stock_filter == 'low':
            queryset = queryset.filter(quantity__lte=F('min_stock_level'))
        elif stock_filter == 'out_of_stock':
            queryset = queryset.filter(quantity=0)
        elif stock_filter == 'in_stock':
            queryset = queryset.filter(quantity__gt=0)
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available units for reference
        tenant_id = getattr(self.request, 'tenant_id', None)
        units_queryset = UnitOfMeasurement.objects.all()
        
        if tenant_id:
            units_queryset = units_queryset.for_tenant(tenant_id)
            
        context['units'] = units_queryset
        return context


@requires_module('inventory')
class ItemDetailView(TenantDetailView):
    model = Item
    template_name = 'inventory/item_detail.html'
    context_object_name = 'item'
    
    def get_object(self, queryset=None):
        """
        Get object filtered by tenant ID if available
        """
        if queryset is None:
            queryset = self.get_queryset()
            
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
            
        return get_object_or_404(queryset, pk=self.kwargs['pk'])
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available units for conversion
        tenant_id = getattr(self.request, 'tenant_id', None)
        units_queryset = UnitOfMeasurement.objects.all()
        
        if tenant_id:
            units_queryset = units_queryset.for_tenant(tenant_id)
            
        context['units'] = units_queryset
        
        # Get item quantity in different units
        item = self.get_object()
        if item.unit_of_measurement:
            converted_quantities = []
            
            for unit in units_queryset:
                if unit != item.unit_of_measurement:
                    try:
                        quantity = item.get_quantity_in_unit(unit)
                        converted_quantities.append({
                            'unit': unit,
                            'quantity': quantity
                        })
                    except ValueError:
                        # No conversion path exists
                        pass
                        
            context['converted_quantities'] = converted_quantities
            
        return context


@requires_module('inventory')
class ItemCreateView(TenantCreateView):
    model = Item
    template_name = 'inventory/item_form.html'
    fields = ['sku', 'name', 'description', 'quantity', 'unit_of_measurement', 'unit_price', 'min_stock_level', 'attributes']
    success_url = reverse_lazy('inventory:item_list')
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Filter units by tenant
        if tenant_id and 'unit_of_measurement' in form.fields:
            form.fields['unit_of_measurement'].queryset = UnitOfMeasurement.objects.for_tenant(tenant_id)
            
        return form
    
    def form_valid(self, form):
        """
        Set tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        return super().form_valid(form)


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementListView(TenantListView):
    """
    This view requires both the inventory module and the
    inventory_advanced feature to be active
    """
    model = Movement
    template_name = 'inventory/movement_list.html'
    context_object_name = 'movements'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Movement.objects.for_tenant(tenant_id)
        return Movement.objects.all()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available units for reference
        tenant_id = getattr(self.request, 'tenant_id', None)
        units_queryset = UnitOfMeasurement.objects.all()
        
        if tenant_id:
            units_queryset = units_queryset.for_tenant(tenant_id)
            
        context['units'] = units_queryset
        return context


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementCreateView(TenantCreateView):
    model = Movement
    template_name = 'inventory/movement_form.html'
    fields = ['item', 'quantity', 'unit_of_measurement', 'movement_type_ref', 'reference', 'notes']
    success_url = reverse_lazy('inventory:movement_list')
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Filter related models by tenant
        if tenant_id:
            if 'item' in form.fields:
                form.fields['item'].queryset = Item.objects.for_tenant(tenant_id)
                
            if 'unit_of_measurement' in form.fields:
                form.fields['unit_of_measurement'].queryset = UnitOfMeasurement.objects.for_tenant(tenant_id)
                
            if 'movement_type_ref' in form.fields:
                form.fields['movement_type_ref'].queryset = MovementType.objects.for_tenant(tenant_id)
                
        return form
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get movement types for the tenant
        movement_types = MovementType.objects.filter(is_active=True)
        if tenant_id:
            movement_types = movement_types.for_tenant(tenant_id)
            
        # Split movement types by category
        context['movement_types_inbound'] = movement_types.filter(is_inbound=True, is_outbound=False)
        context['movement_types_outbound'] = movement_types.filter(is_inbound=False, is_outbound=True)
        context['movement_types_transfer'] = movement_types.filter(is_inbound=True, is_outbound=True)
        
        return context
    
    def form_valid(self, form):
        """
        Set tenant ID if available and validate movement type
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        
        # Get movement type data from request
        movement_type_ref_id = self.request.POST.get('movement_type_ref')
        legacy_movement_type = self.request.POST.get('movement_type')
        
        # Validate that at least one movement type is set
        if not movement_type_ref_id and not legacy_movement_type:
            form.add_error(None, _("Please select a movement type"))
            return self.form_invalid(form)
        
        # Set the appropriate movement type
        if movement_type_ref_id:
            try:
                movement_type = MovementType.objects.get(pk=movement_type_ref_id)
                if tenant_id and movement_type.tenant_id != tenant_id:
                    form.add_error(None, _("Invalid movement type selected"))
                    return self.form_invalid(form)
                form.instance.movement_type_ref = movement_type
            except MovementType.DoesNotExist:
                form.add_error(None, _("Selected movement type does not exist"))
                return self.form_invalid(form)
        else:
            # Use legacy movement type
            form.instance.movement_type = legacy_movement_type
        
        return super().form_valid(form)


@requires_module('inventory')
@requires_feature('barcode_scanning')
def scan_barcode(request):
    """
    This view requires both the inventory module and the
    barcode_scanning feature to be active
    """
    if request.method == 'POST':
        barcode = request.POST.get('barcode')
        tenant_id = getattr(request, 'tenant_id', None)
        
        try:
            if tenant_id:
                item = Item.objects.get_for_tenant(tenant_id, attributes__barcode=barcode)
            else:
                item = Item.objects.get(attributes__barcode=barcode)
                
            return redirect('inventory:item_detail', pk=item.pk)
        except Item.DoesNotExist:
            # Handle not found
            return render(request, 'inventory/scan_barcode.html', {
                'error': _('Barcode not found'),
                'barcode': barcode
            })
            
    return render(request, 'inventory/scan_barcode.html')


@requires_module('inventory')
class UnitOfMeasurementListView(TenantListView):
    model = UnitOfMeasurement
    template_name = 'inventory/unit_list.html'
    context_object_name = 'units'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return UnitOfMeasurement.objects.for_tenant(tenant_id)
        return UnitOfMeasurement.objects.all()


@requires_module('inventory')
class UnitOfMeasurementDetailView(TenantDetailView):
    model = UnitOfMeasurement
    template_name = 'inventory/unit_detail.html'
    context_object_name = 'unit'
    
    def get_object(self, queryset=None):
        """
        Get object filtered by tenant ID if available
        """
        if queryset is None:
            queryset = self.get_queryset()
            
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
            
        return get_object_or_404(queryset, pk=self.kwargs['pk'])
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        unit = self.get_object()
        
        # Get conversions from this unit
        context['conversions_from'] = unit.conversions_from.all()
        
        # Get conversions to this unit
        context['conversions_to'] = unit.conversions_to.all()
        
        # Get items using this unit
        context['items'] = unit.items.all()
        
        return context


@requires_module('inventory')
class UnitOfMeasurementCreateView(TenantCreateView):
    model = UnitOfMeasurement
    template_name = 'inventory/unit_form.html'
    fields = ['name', 'symbol', 'description', 'is_base_unit']
    success_url = reverse_lazy('inventory:unit_list')
    
    def form_valid(self, form):
        """
        Set tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        return super().form_valid(form)


@requires_module('inventory')
class UnitConversionListView(TenantListView):
    model = UnitConversion
    template_name = 'inventory/conversion_list.html'
    context_object_name = 'conversions'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return UnitConversion.objects.for_tenant(tenant_id)
        return UnitConversion.objects.all()


@requires_module('inventory')
class UnitConversionCreateView(TenantCreateView):
    model = UnitConversion
    template_name = 'inventory/conversion_form.html'
    fields = ['from_unit', 'to_unit', 'conversion_factor']
    success_url = reverse_lazy('inventory:conversion_list')
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Filter units by tenant
        if tenant_id:
            if 'from_unit' in form.fields:
                form.fields['from_unit'].queryset = UnitOfMeasurement.objects.for_tenant(tenant_id)
                
            if 'to_unit' in form.fields:
                form.fields['to_unit'].queryset = UnitOfMeasurement.objects.for_tenant(tenant_id)
            
        return form
    
    def form_valid(self, form):
        """
        Set tenant ID if available and validate conversion
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
            
        # Validate that from_unit and to_unit are different
        from_unit = form.cleaned_data.get('from_unit')
        to_unit = form.cleaned_data.get('to_unit')
        
        if from_unit == to_unit:
            messages.error(self.request, _("From unit and to unit must be different"))
            return self.form_invalid(form)
            
        return super().form_valid(form)


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementTypeListView(TenantListView):
    """
    View for listing and managing movement types
    """
    model = MovementType
    template_name = 'inventory/movement_type_list.html'
    context_object_name = 'movement_types'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        queryset = MovementType.objects.all()
        
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
            
        # Sort by sequence and name
        return queryset.order_by('sequence', 'name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['inbound_types'] = self.get_queryset().filter(is_inbound=True, is_outbound=False)
        context['outbound_types'] = self.get_queryset().filter(is_inbound=False, is_outbound=True)
        context['transfer_types'] = self.get_queryset().filter(is_inbound=True, is_outbound=True)
        return context


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementTypeCreateView(TenantCreateView):
    """
    View for creating a new movement type
    """
    model = MovementType
    template_name = 'inventory/movement_type_form.html'
    fields = ['code', 'name', 'description', 'is_inbound', 'is_outbound', 
              'icon', 'color', 'requires_reference', 'requires_approval', 
              'sequence', 'is_active']
    success_url = reverse_lazy('inventory:movement_type_list')
    
    def form_valid(self, form):
        """
        Set tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        return super().form_valid(form)


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementTypeUpdateView(TenantUpdateView):
    """
    View for updating an existing movement type
    """
    model = MovementType
    template_name = 'inventory/movement_type_form.html'
    fields = ['name', 'description', 'is_inbound', 'is_outbound', 
              'icon', 'color', 'requires_reference', 'requires_approval', 
              'sequence', 'is_active']
    success_url = reverse_lazy('inventory:movement_type_list')
    
    def get_object(self, queryset=None):
        """
        Get object filtered by tenant ID if available
        """
        if queryset is None:
            queryset = self.get_queryset()
            
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
            
        return get_object_or_404(queryset, pk=self.kwargs['pk'])


@login_required
def inventory_dashboard(request):
    """Main inventory dashboard with statistics and quick actions"""
    context = {
        'page_title': _('لوحة تحكم المخزون'),
        'page_subtitle': _('نظرة عامة على المخزون والحركات'),
    }
    
    try:
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(request, 'tenant_id', None)
        
        # Base querysets
        item_queryset = Item.objects.all()
        category_queryset = ItemClassification.objects.all()
        movement_queryset = Movement.objects.all()
        
        if tenant_id:
            item_queryset = item_queryset.filter(tenant_id=tenant_id)
            category_queryset = category_queryset.filter(tenant_id=tenant_id)
            movement_queryset = movement_queryset.filter(tenant_id=tenant_id)
        
        # Get basic statistics
        total_items = item_queryset.count()
        total_categories = category_queryset.count()
        
        # Low stock items (where current stock <= minimum stock)
        low_stock_count = item_queryset.filter(
            quantity__lte=F('min_stock_level')
        ).count()
        
        # Get slow-moving items - temporarily simplified for testing
        slow_moving_items = []
        try:
            for item in item_queryset[:3]:  # Test with first 3 items
                if hasattr(item, 'is_slow_moving'):
                    if item.is_slow_moving():
                        slow_moving_items.append(item)
                else:
                    # Temporarily add some items for testing
                    slow_moving_items.append(item)
        except Exception as e:
            print(f"DEBUG: Error in slow-moving check: {e}")
            # Add 2 items for testing regardless
            slow_moving_items = list(item_queryset[:2])
        
        # Debug: For now, let's add a placeholder for testing
        print(f"DEBUG: Found {len(slow_moving_items)} slow-moving items")
        
        # Recent stock movements
        recent_movements = movement_queryset.select_related('item').order_by('-created_at')[:10]
        
        # Top categories by item count
        top_categories = category_queryset.annotate(
            item_count=Count('items')
        ).order_by('-item_count')[:5]
        
        # Stock value (simplified calculation)
        total_stock_value = item_queryset.aggregate(
            total_value=Sum(F('quantity') * F('unit_price'))
        )['total_value'] or 0
        
        context.update({
            'total_items': total_items,
            'total_categories': total_categories,
            'low_stock_count': low_stock_count,
            'slow_moving_items': slow_moving_items,
            'total_stock_value': total_stock_value,
            'recent_movements': recent_movements,
            'top_categories': top_categories,
        })
        
    except Exception as e:
        messages.error(request, _('حدث خطأ في تحميل بيانات المخزون'))
        
    return render(request, 'inventory/dashboard.html', context)


class ItemListView(LoginRequiredMixin, ListView):
    """List view for inventory items with search and filtering"""
    model = Item
    template_name = 'inventory/item_list.html'
    context_object_name = 'items'
    paginate_by = 20
    
    def get_queryset(self):
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        queryset = Item.objects.all()
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
            
        queryset = queryset.select_related('classification', 'unit_of_measurement').order_by('name')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(sku__icontains=search_query) |
                Q(barcode__icontains=search_query) |
                Q(description__icontains=search_query)
            )
        
        # Category filter
        category_id = self.request.GET.get('category', '')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        
        # Stock level filter
        stock_filter = self.request.GET.get('stock_level', '')
        if stock_filter == 'low':
            queryset = queryset.filter(quantity__lte=F('min_stock_level'))
        elif stock_filter == 'out_of_stock':
            queryset = queryset.filter(quantity=0)
        elif stock_filter == 'in_stock':
            queryset = queryset.filter(quantity__gt=0)
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('قائمة الأصناف'),
            'page_subtitle': _('إدارة أصناف المخزون'),
            'search_query': self.request.GET.get('search', ''),
            'categories': ItemClassification.objects.filter(tenant_id=getattr(self.request, 'tenant_id', None)) if getattr(self.request, 'tenant_id', None) else ItemClassification.objects.all(),
            'current_category': self.request.GET.get('category', ''),
            'current_stock_filter': self.request.GET.get('stock_level', ''),
        })
        return context


class ItemDetailView(LoginRequiredMixin, DetailView):
    """Detail view for a single inventory item"""
    model = Item
    template_name = 'inventory/item_detail.html'
    context_object_name = 'item'
    
    def get_queryset(self):
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        queryset = Item.objects.all()
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item = self.get_object()
        
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get recent stock movements for this item
        movement_queryset = Movement.objects.filter(item=item)
        if tenant_id:
            movement_queryset = movement_queryset.filter(tenant_id=tenant_id)
        recent_movements = movement_queryset.order_by('-created_at')[:20]
        
        # Get compatible vehicles
        vehicle_queryset = VehicleModelPart.objects.filter(item=item)
        if tenant_id:
            vehicle_queryset = vehicle_queryset.filter(tenant_id=tenant_id)
        compatible_vehicles = vehicle_queryset.select_related('vehicle_model', 'vehicle_make')
        
        # Get compatible operations
        operation_queryset = OperationCompatibility.objects.filter(item=item)
        if tenant_id:
            operation_queryset = operation_queryset.filter(tenant_id=tenant_id)
        compatible_operations = operation_queryset.distinct()
        
        context.update({
            'page_title': item.name,
            'page_subtitle': _('تفاصيل الصنف'),
            'recent_movements': recent_movements,
            'compatible_vehicles': compatible_vehicles,
            'compatible_operations': compatible_operations,
        })
        return context


class ItemCreateView(LoginRequiredMixin, CreateView):
    """Create view for new inventory items"""
    model = Item
    form_class = ItemForm
    template_name = 'inventory/item_form.html'
    success_url = reverse_lazy('inventory:item_list')
    
    def form_valid(self, form):
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        form.instance.created_by = self.request.user
        messages.success(self.request, _('تم إنشاء الصنف بنجاح'))
        return super().form_valid(form)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة صنف جديد'),
            'page_subtitle': _('إنشاء صنف جديد في المخزون'),
            'form_mode': 'create',
        })
        return context


class ItemUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for existing inventory items"""
    model = Item
    form_class = ItemForm
    template_name = 'inventory/item_form.html'
    
    def get_queryset(self):
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        queryset = Item.objects.all()
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
        return queryset
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث الصنف بنجاح'))
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse('inventory:item_detail', kwargs={'pk': self.object.pk})
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل الصنف'),
            'page_subtitle': self.object.name,
            'form_mode': 'update',
        })
        return context


class StockMovementListView(LoginRequiredMixin, ListView):
    """List view for stock movements"""
    model = Movement
    template_name = 'inventory/stock_movement_list.html'
    context_object_name = 'movements'
    paginate_by = 50
    
    def get_queryset(self):
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        queryset = Movement.objects.all()
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
            
        queryset = queryset.select_related('item', 'unit_of_measurement').order_by('-created_at')
        
        # Filter by item
        item_id = self.request.GET.get('item', '')
        if item_id:
            queryset = queryset.filter(item_id=item_id)
        
        # Filter by movement type
        movement_type = self.request.GET.get('movement_type', '')
        if movement_type:
            queryset = queryset.filter(movement_type=movement_type)
        
        # Additional filters can be added here if needed
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('حركة المخزون'),
            'page_subtitle': _('سجل جميع حركات المخزون'),
            'items': Item.objects.filter(tenant_id=getattr(self.request, 'tenant_id', None)) if getattr(self.request, 'tenant_id', None) else Item.objects.all(),
            'movement_types': Movement.LEGACY_MOVEMENT_TYPES,
            'current_filters': {
                'item': self.request.GET.get('item', ''),
                'movement_type': self.request.GET.get('movement_type', ''),
            },
        })
        return context


@login_required
def create_stock_movement(request):
    """Create a new stock movement"""
    if request.method == 'POST':
        form = StockMovementForm(request.POST)
        if form.is_valid():
            movement = form.save(commit=False)
            # Get tenant_id from request (set by middleware)
            tenant_id = getattr(request, 'tenant_id', None)
            if tenant_id:
                movement.tenant_id = tenant_id
            movement.created_by = request.user
            movement.save()
            
            # Note: Stock level updates are handled in the Movement model's save method
            # No need to manually update item stock here
            
            messages.success(request, _('تم إنشاء حركة المخزون بنجاح'))
            return redirect('inventory:stock_movement_list')
    else:
        form = StockMovementForm()
    
    context = {
        'form': form,
        'page_title': _('إضافة حركة مخزون'),
        'page_subtitle': _('تسجيل حركة جديدة للمخزون'),
    }
    return render(request, 'inventory/stock_movement_form.html', context)


@login_required
def api_search_items(request):
    """API endpoint for searching items (used in autocomplete)"""
    term = request.GET.get('term', '')
    
    if len(term) < 2:
        return JsonResponse({'results': []})
    
    # Get tenant_id from request (set by middleware) 
    tenant_id = getattr(request, 'tenant_id', None)
    
    items = Item.objects.all()
    if tenant_id:
        items = items.filter(tenant_id=tenant_id)
    
    items = items.filter(
        Q(name__icontains=term) |
        Q(sku__icontains=term) |
        Q(barcode__icontains=term)
    )[:20]
    
    results = []
    for item in items:
        results.append({
            'id': str(item.id),
            'text': f"{item.name} ({item.sku})",
            'name': item.name,
            'sku': item.sku,
            'current_stock': float(item.quantity),
            'unit_of_measure': item.unit_of_measurement.name if item.unit_of_measurement else '',
        })
    
    return JsonResponse({'results': results})


@login_required
def api_item_stock_info(request, item_id):
    """API endpoint to get current stock information for an item"""
    try:
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(request, 'tenant_id', None)
        
        if tenant_id:
            item = Item.objects.get(id=item_id, tenant_id=tenant_id)
        else:
            item = Item.objects.get(id=item_id)
        return JsonResponse({
            'success': True,
            'item': {
                'id': str(item.id),
                'name': item.name,
                'sku': item.sku,
                'current_stock': float(item.quantity),
                'minimum_stock_level': float(item.min_stock_level),
                'unit_cost': float(item.unit_price) if item.unit_price else 0,
                'unit_of_measure': {
                    'id': str(item.unit_of_measurement.id) if item.unit_of_measurement else None,
                    'name': item.unit_of_measurement.name if item.unit_of_measurement else '',
                    'symbol': item.unit_of_measurement.symbol if item.unit_of_measurement else '',
                },
                'classification': {
                    'id': str(item.classification.id) if item.classification else None,
                    'name': item.classification.name if item.classification else '',
                } if item.classification else None,
            }
        })
    except Item.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': _('الصنف غير موجود')
        }, status=404)


@login_required
def api_quick_stock_adjustment(request):
    """API endpoint for quick stock adjustments"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': _('طريقة الطلب غير صحيحة')})
    
    try:
        data = json.loads(request.body)
        item_id = data.get('item_id')
        quantity = float(data.get('quantity', 0))
        adjustment_type = data.get('adjustment_type', 'adjustment_in')
        reason = data.get('reason', '')
        
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(request, 'tenant_id', None)
        
        if tenant_id:
            item = Item.objects.get(id=item_id, tenant_id=tenant_id)
        else:
            item = Item.objects.get(id=item_id)
        
        # Create stock movement
        movement = Movement.objects.create(
            tenant_id=tenant_id,
            item=item,
            movement_type=adjustment_type,
            quantity=quantity,
            notes=reason
        )
        
        # Stock is automatically updated in the Movement model's save method
        item.refresh_from_db()  # Refresh to get updated quantity
        
        return JsonResponse({
            'success': True,
            'message': _('تم تعديل المخزون بنجاح'),
            'new_stock': float(item.quantity),
        })
        
    except (Item.DoesNotExist, ValueError, json.JSONDecodeError) as e:
        return JsonResponse({
            'success': False,
            'error': _('حدث خطأ في تعديل المخزون')
        })


@login_required
def api_low_stock_items(request):
    """API endpoint for low stock items"""
    # Get tenant_id from request (set by middleware)
    tenant_id = getattr(request, 'tenant_id', None)
    
    # Get items with low stock
    items = Item.objects.all()
    if tenant_id:
        items = items.filter(tenant_id=tenant_id)
    
    # Filter items with low stock
    low_stock_items = items.filter(quantity__lte=F('min_stock_level'))
    
    items_data = []
    for item in low_stock_items:
        items_data.append({
            'id': str(item.id),
            'name': item.name,
            'sku': item.sku,
            'current_stock': float(item.quantity),
            'min_stock': float(item.min_stock_level) if item.min_stock_level else 0,
            'unit': item.unit_of_measurement.symbol if item.unit_of_measurement else '',
        })
    
    return JsonResponse({
        'success': True,
        'items': items_data
    })


@login_required
def api_items_list(request):
    """API endpoint to get list of items"""
    # Get tenant_id from request (set by middleware)
    tenant_id = getattr(request, 'tenant_id', None)
    
    # Get active items
    items = Item.objects.all()
    if tenant_id:
        items = items.filter(tenant_id=tenant_id)
    
    items = items.select_related('unit_of_measurement', 'classification').order_by('name')
    
    # Optional search parameter
    search = request.GET.get('search', '')
    if search:
        items = items.filter(
            Q(name__icontains=search) |
            Q(sku__icontains=search) |
            Q(description__icontains=search)
        )
    
    # Limit results
    limit = int(request.GET.get('limit', 100))
    items = items[:limit]
    
    items_data = []
    for item in items:
        items_data.append({
            'id': str(item.id),
            'name': item.name,
            'sku': item.sku,
            'description': item.description or '',
            'quantity': float(item.quantity),
            'unit': item.unit_of_measurement.symbol if item.unit_of_measurement else '',
            'unit_price': float(item.unit_price) if item.unit_price else 0,
            'min_stock_level': float(item.min_stock_level) if item.min_stock_level else 0,
            'classification': item.classification.name if item.classification else '',
        })
    
    return JsonResponse({
        'success': True,
        'items': items_data
    })


class CategoryListView(LoginRequiredMixin, ListView):
    """List view for inventory categories"""
    model = ItemClassification
    template_name = 'inventory/category_list.html'
    context_object_name = 'categories'
    
    def get_queryset(self):
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        queryset = ItemClassification.objects.all()
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
            
        return queryset.annotate(
            item_count=Count('items')
        ).order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('فئات المخزون'),
            'page_subtitle': _('إدارة فئات وتصنيفات الأصناف'),
        })
        return context


class CategoryCreateView(LoginRequiredMixin, CreateView):
    """Create view for new categories"""
    model = ItemClassification
    form_class = CategoryForm
    template_name = 'inventory/category_form.html'
    success_url = reverse_lazy('inventory:category_list')
    
    def form_valid(self, form):
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        messages.success(self.request, _('تم إنشاء الفئة بنجاح'))
        return super().form_valid(form)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة فئة جديدة'),
            'page_subtitle': _('إنشاء فئة جديدة للأصناف'),
            'form_mode': 'create',
        })
        return context


@login_required
def vehicle_compatibility_manager(request):
    """Manage vehicle-part compatibility"""
    context = {
        'page_title': _('إدارة توافق المركبات'),
        'page_subtitle': _('ربط الأصناف بموديلات المركبات'),
    }
    
    if request.method == 'POST':
        # Handle compatibility updates
        try:
            data = json.loads(request.body)
            # Process compatibility data
            messages.success(request, _('تم تحديث التوافق بنجاح'))
        except Exception as e:
            messages.error(request, _('حدث خطأ في تحديث التوافق'))
    
    return render(request, 'inventory/compatibility_manager.html', context)


@login_required
def test_dashboard(request):
    """Test view to verify dashboard_base.html template works correctly"""
    context = {
        'page_title': _('Test Dashboard'),
        'page_subtitle': _('Testing template structure'),
    }
    return render(request, 'inventory/test_dashboard.html', context)


@requires_module('inventory')
class SlowMovingItemsView(LoginRequiredMixin, TemplateView):
    """View for displaying detailed slow-moving items"""
    template_name = 'inventory/slow_moving_items.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get detailed slow-moving items
        slow_moving_items = []
        
        if tenant_id:
            # Get all active items for this tenant
            items = Item.objects.filter(tenant_id=tenant_id)
            
            for item in items:
                if item.is_slow_moving():
                    config = item.get_slow_moving_config()
                    if config:
                        # Get the last movement date
                        last_movement = item.movements.order_by('-created_at').first()
                        days_since_last_movement = None
                        
                        if last_movement:
                            days_since_last_movement = (timezone.now().date() - last_movement.created_at.date()).days
                        
                        # Get batches expiring soon
                        expiring_batches = item.batches.filter(
                            status='active',
                            expiry_date__lte=timezone.now().date() + timedelta(days=60)
                        ).order_by('expiry_date')
                        
                        # Calculate total batch value
                        total_batch_value = sum(
                            batch.current_quantity * batch.purchase_price 
                            for batch in item.batches.filter(status='active')
                        )
                        
                        slow_moving_items.append({
                            'item': item,
                            'config': config,
                            'days_since_last_movement': days_since_last_movement,
                            'last_movement': last_movement,
                            'expiring_batches': expiring_batches,
                            'total_batch_value': total_batch_value,
                            'active_batches_count': item.batches.filter(status='active').count(),
                        })
        
        # Sort by days since last movement (highest first)
        slow_moving_items.sort(key=lambda x: x['days_since_last_movement'] or 0, reverse=True)
        
        context['slow_moving_items'] = slow_moving_items
        context['title'] = _('الأصناف الراكدة')
        context['page_title'] = _('الأصناف الراكدة')
        context['page_subtitle'] = _('عرض تفصيلي للأصناف بطيئة الحركة')
        
        return context


@requires_module('inventory')
class LowStockItemsView(LoginRequiredMixin, TemplateView):
    """View for displaying low stock items with batch details"""
    template_name = 'inventory/low_stock_items.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get low stock items with details
        low_stock_items = []
        
        if tenant_id:
            items = Item.objects.filter(
                tenant_id=tenant_id,
                quantity__lte=F('min_stock_level')
            ).select_related('classification', 'unit_of_measurement')
            
            for item in items:
                # Get batch information
                active_batches = item.batches.filter(status='active')
                total_batch_quantity = sum(batch.current_quantity for batch in active_batches)
                
                # Get recent movements
                recent_movement = item.movements.order_by('-created_at').first()
                
                low_stock_items.append({
                    'item': item,
                    'shortage': max(0, item.min_stock_level - item.quantity),
                    'active_batches': active_batches,
                    'total_batch_quantity': total_batch_quantity,
                    'recent_movement': recent_movement,
                    'urgency_level': self.get_urgency_level(item),
                })
        
        # Sort by urgency (most urgent first)
        low_stock_items.sort(key=lambda x: x['urgency_level'], reverse=True)
        
        context['low_stock_items'] = low_stock_items
        context['title'] = _('مخزون منخفض')
        context['page_title'] = _('مخزون منخفض')
        context['page_subtitle'] = _('الأصناف التي تحتاج إعادة تموين')
        
        return context
    
    def get_urgency_level(self, item):
        """Calculate urgency level based on current stock vs minimum"""
        # Safety check to ensure we're working with proper numeric types
        try:
            current_qty = Decimal(str(item.quantity)) if item.quantity is not None else Decimal('0')
            min_level = Decimal(str(item.min_stock_level)) if item.min_stock_level is not None else Decimal('0')
            
            if current_qty <= 0:
                return 3  # Critical - out of stock
            elif current_qty <= min_level * Decimal('0.5'):
                return 2  # High urgency
            else:
                return 1  # Low urgency
        except (TypeError, ValueError) as e:
            # Fallback in case of any type conversion issues
            return 1  # Default to low urgency


@requires_module('inventory')
class StockValuationReportView(LoginRequiredMixin, TemplateView):
    """View for displaying stock valuation report with batch pricing"""
    template_name = 'inventory/stock_valuation_report.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get stock valuation data
        valuation_data = []
        total_value = 0
        total_quantity = 0
        
        if tenant_id:
            items = Item.objects.filter(tenant_id=tenant_id).select_related(
                'classification', 'unit_of_measurement'
            )
            
            for item in items:
                # Calculate value using batch pricing
                batches = item.batches.filter(status='active')
                batch_value = sum(
                    batch.current_quantity * batch.purchase_price 
                    for batch in batches
                )
                
                # Fallback to item price if no batches
                item_value = item.quantity * (item.unit_price or 0)
                final_value = batch_value if batch_value > 0 else item_value
                
                # Get average cost per unit
                avg_cost = final_value / item.quantity if item.quantity > 0 else 0
                
                # Get valuation method - we'll use a simple fallback for now
                try:
                    valuation_method = item.get_valuation_method()
                except AttributeError:
                    valuation_method = 'FIFO'  # Default fallback
                
                valuation_data.append({
                    'item': item,
                    'quantity': item.quantity,
                    'avg_cost': avg_cost,
                    'total_value': final_value,
                    'batch_count': batches.count(),
                    'valuation_method': valuation_method,
                })
                
                total_value += final_value
                total_quantity += item.quantity
        
        # Sort by value (highest first)
        valuation_data.sort(key=lambda x: x['total_value'], reverse=True)
        
        # Calculate summary statistics
        avg_item_value = total_value / len(valuation_data) if valuation_data else 0
        
        context.update({
            'valuation_data': valuation_data,
            'total_value': total_value,
            'total_quantity': total_quantity,
            'avg_item_value': avg_item_value,
            'total_items': len(valuation_data),
            'title': _('تقرير تقييم المخزون'),
            'page_title': _('تقرير تقييم المخزون'),
            'page_subtitle': _('تقييم شامل لقيمة المخزون بأسعار الدفعات'),
        })
        
        return context



