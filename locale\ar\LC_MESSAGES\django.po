# Arabic translations for After-Sales project.
# Copyright (C) 2025 After-Sales
msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 14:24+0300\n"
"PO-Revision-Date: 2025-05-07 12:00+0200\n"
"Last-Translator: Translator <<EMAIL>>\n"
"Language-Team: Arabic <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: .\app_settings\admin.py:16 .\app_settings\admin.py:36
#: .\app_settings\models.py:13 .\app_settings\models.py:32
msgid "Value"
msgstr ""

#: .\app_settings\admin.py:20 .\app_settings\admin.py:40
#: .\app_settings\admin.py:60 .\inventory\admin.py:140 .\inventory\admin.py:159
#: .\inventory\admin.py:186 .\inventory\admin.py:221 .\inventory\admin.py:253
#: .\purchases\admin.py:34 .\purchases\admin.py:59 .\purchases\admin.py:80
#: .\purchases\admin.py:106 .\purchases\admin.py:123 .\reports\admin.py:45
#: .\reports\admin.py:69 .\reports\admin.py:96 .\reports\admin.py:117
#: .\sales\admin.py:31 .\sales\admin.py:59 .\sales\admin.py:80
#: .\sales\admin.py:106 .\sales\admin.py:123 .\warehouse\admin.py:32
#: .\warehouse\admin.py:50 .\warehouse\admin.py:71 .\warehouse\admin.py:92
#: .\warehouse\admin.py:112
msgid "Metadata"
msgstr ""

#: .\app_settings\admin.py:57 .\setup\admin.py:48 .\setup\admin.py:76
#: .\setup\admin.py:125
msgid "Contact Information"
msgstr ""

#: .\app_settings\models.py:11 .\app_settings\models.py:30
#: .\feature_flags\models.py:11 .\feature_flags\models.py:45
#: .\inventory\models.py:22 .\inventory\models.py:84
#: .\inventory\templates\inventory\dashboard.html:109
#: .\notifications\models.py:59 .\purchases\models.py:12 .\reports\models.py:19
#: .\reports\models.py:85 .\sales\models.py:12
#: .\setup\templates\setup\dashboard.html:240
#: .\setup\templates\setup\dashboard.html:284
#: .\setup\templates\setup\dashboard.html:328 .\warehouse\models.py:13
#: .\work_orders\models.py:9 .\work_orders\models.py:27
msgid "Name"
msgstr "الاسم"

#: .\app_settings\models.py:12 .\app_settings\models.py:31
#: .\feature_flags\models.py:12 .\feature_flags\models.py:46
#: .\franchise_setup\models.py:14 .\franchise_setup\models.py:208
#: .\inventory\models.py:24 .\inventory\models.py:85 .\inventory\models.py:194
#: .\notifications\models.py:61 .\reports\models.py:20 .\reports\models.py:86
#: .\setup\models.py:12 .\setup\models.py:118 .\setup\models.py:256
#: .\user_roles\models.py:30 .\work_orders\models.py:10
#: .\work_orders\models.py:28 .\work_orders\models.py:114
#: .\work_orders\models.py:159
msgid "Description"
msgstr ""

#: .\app_settings\models.py:18
#, fuzzy
#| msgid "Settings"
msgid "Tenant Setting"
msgstr "الإعدادات"

#: .\app_settings\models.py:19
#, fuzzy
#| msgid "Settings"
msgid "Tenant Settings"
msgstr "الإعدادات"

#: .\app_settings\models.py:35
#, fuzzy
#| msgid "Settings"
msgid "System Setting"
msgstr "الإعدادات"

#: .\app_settings\models.py:36
#, fuzzy
#| msgid "Settings"
msgid "System Settings"
msgstr "الإعدادات"

#: .\app_settings\models.py:46 .\setup\models.py:74
#, fuzzy
#| msgid "Companies"
msgid "Company Name"
msgstr "الشركات"

#: .\app_settings\models.py:47 .\setup\models.py:34 .\setup\models.py:76
msgid "Logo"
msgstr ""

#: .\app_settings\models.py:48
#, fuzzy
#| msgid "Contact"
msgid "Contact Email"
msgstr "جهة الاتصال"

#: .\app_settings\models.py:49
#, fuzzy
#| msgid "Contact"
msgid "Contact Phone"
msgstr "جهة الاتصال"

#: .\app_settings\models.py:50 .\purchases\admin.py:31 .\purchases\models.py:15
#: .\sales\admin.py:28 .\sales\models.py:15 .\setup\models.py:37
#: .\setup\models.py:79 .\setup\models.py:152 .\warehouse\admin.py:29
#: .\warehouse\models.py:15
msgid "Address"
msgstr ""

#: .\app_settings\models.py:51 .\setup\models.py:44 .\setup\models.py:86
msgid "Website"
msgstr ""

#: .\app_settings\models.py:53
msgid "Subscription Tier"
msgstr ""

#: .\app_settings\models.py:56
msgid "Free"
msgstr ""

#: .\app_settings\models.py:57
msgid "Basic"
msgstr ""

#: .\app_settings\models.py:58
msgid "Professional"
msgstr ""

#: .\app_settings\models.py:59
msgid "Enterprise"
msgstr ""

#: .\app_settings\models.py:67
msgid "Tenant Profile"
msgstr ""

#: .\app_settings\models.py:68
msgid "Tenant Profiles"
msgstr ""

#: .\core\models\common.py:11
#, fuzzy
#| msgid "Create Companies"
msgid "Created at"
msgstr "إنشاء الشركات"

#: .\core\models\common.py:12
msgid "Updated at"
msgstr ""

#: .\core\models\common.py:24
msgid "ID"
msgstr ""

#: .\core\models\common.py:39
msgid "Tenant ID"
msgstr ""

#: .\core\templates\base.html:7 .\core\templates\base.html:179
msgid "Inventory Management System"
msgstr "نظام ما بعد البيع"

#: .\core\templates\base.html:63
msgid "Aftersails System"
msgstr "نظام ما بعد البيع"

#: .\core\templates\base.html:70 .\core\templates\base.html:135
#: .\core\templates\core\base.html:81 .\inventory\apps.py:8
#: .\reports\models.py:12 .\user_roles\admin.py:45 .\user_roles\models.py:195
msgid "Inventory"
msgstr "المخزون"

#: .\core\templates\base.html:75 .\core\templates\base.html:140
#: .\user_roles\admin.py:41 .\user_roles\models.py:193
msgid "Setup"
msgstr "الإعداد"

#: .\core\templates\base.html:80 .\core\templates\base.html:145
#: .\core\templates\core\base.html:95 .\reports\models.py:31
#: .\user_roles\admin.py:53 .\user_roles\models.py:199
msgid "Reports"
msgstr "التقارير"

#: .\core\templates\base.html:105 .\reports\models.py:93
#: .\reports\models.py:116
msgid "Dashboard"
msgstr "لوحة التحكم"

#: .\core\templates\base.html:110 .\user_roles\admin.py:55
#: .\user_roles\models.py:200
msgid "Settings"
msgstr "الإعدادات"

#: .\core\templates\base.html:115 .\core\templates\core\base.html:61
msgid "Sign out"
msgstr "تسجيل الخروج"

#: .\core\templates\core\base.html:9
msgid "After-Sales Franchise Management System"
msgstr "نظام إدارة امتياز ما بعد البيع"

#: .\core\templates\core\base.html:41 .\core\templates\core\base.html:143
#: .\core\templates\core\base.html:151 .\core\templates\core\login.html:14
#, fuzzy
#| msgid "Aftersails System"
msgid "After-Sales System"
msgstr "نظام ما بعد البيع"

#: .\core\templates\core\base.html:57
msgid "Profile"
msgstr ""

#: .\core\templates\core\base.html:58
#, fuzzy
#| msgid "Dashboard"
msgid "Admin Dashboard"
msgstr "لوحة التحكم"

#: .\core\templates\core\base.html:65
msgid "Log in"
msgstr ""

#: .\core\templates\core\base.html:76
msgid "Home"
msgstr "الرئيسية"

#: .\core\templates\core\base.html:87 .\inventory\models.py:98
msgid "Items"
msgstr ""

#: .\core\templates\core\base.html:88
#: .\inventory\templates\inventory\dashboard.html:59
msgid "Stock Movements"
msgstr ""

#: .\core\templates\core\base.html:101
#, fuzzy
#| msgid "Reports"
msgid "Sales Report"
msgstr "التقارير"

#: .\core\templates\core\base.html:102
#: .\inventory\templates\inventory\dashboard.html:9 .\inventory\views.py:51
#, fuzzy
#| msgid "Dashboard"
msgid "Inventory Dashboard"
msgstr "لوحة التحكم"

#: .\core\templates\core\base.html:145
#, fuzzy
#| msgid "Welcome to the After-Sales Franchise Management System"
msgid "A modern, modular system for after-sales franchise management."
msgstr "مرحبًا بك في نظام إدارة امتياز ما بعد البيع"

#: .\core\templates\core\base.html:147
msgid "About"
msgstr ""

#: .\core\templates\core\base.html:148
msgid "Modules"
msgstr ""

#: .\core\templates\core\base.html:149
#: .\setup\templates\setup\dashboard.html:243
msgid "Contact"
msgstr "جهة الاتصال"

#: .\core\templates\core\base.html:151
msgid "All Rights Reserved."
msgstr ""

#: .\core\templates\core\login.html:13
#, fuzzy
#| msgid "Aftersails System"
msgid "After-Sales System Logo"
msgstr "نظام ما بعد البيع"

#: .\core\templates\core\login.html:17 .\core\views.py:19
msgid "Welcome to the After-Sales Franchise Management System"
msgstr "مرحبًا بك في نظام إدارة امتياز ما بعد البيع"

#: .\core\templates\core\login.html:25 .\core\templates\core\login.html:26
msgid "Username"
msgstr "اسم المستخدم"

#: .\core\templates\core\login.html:30
msgid "Password"
msgstr "كلمة المرور"

#: .\core\templates\core\login.html:40
msgid "Remember me"
msgstr "تذكرني"

#: .\core\templates\core\login.html:43
msgid "Forgot password?"
msgstr "نسيت كلمة المرور؟"

#: .\core\templates\core\login.html:46
msgid "Sign in"
msgstr "تسجيل الدخول"

#: .\core\templates\core\login.html:50
msgid "Error!"
msgstr "خطأ!"

#: .\core\templates\core\login.html:50
msgid "Please correct the errors below."
msgstr "يرجى تصحيح الأخطاء أدناه."

#: .\core\views.py:15
#, fuzzy
#| msgid "You have been logged in successfully!"
msgid "Language switched successfully!"
msgstr "تم تسجيل الدخول بنجاح!"

#: .\core\views.py:18
msgid "Language Demo"
msgstr ""

#: .\core\views.py:20
msgid ""
"This page demonstrates language switching and RTL/LTR support via Tailwind "
"CSS."
msgstr ""

#: .\core\views.py:22
msgid "Multi-tenant architecture"
msgstr ""

#: .\core\views.py:23
msgid "Role-based access control"
msgstr ""

#: .\core\views.py:24
msgid "Feature flags for module toggling"
msgstr ""

#: .\core\views.py:25
msgid "Comprehensive reporting system"
msgstr ""

#: .\core\views.py:26
msgid "Document management"
msgstr ""

#: .\core\views.py:27
msgid "Internationalization support (LTR/RTL)"
msgstr ""

#: .\core\views.py:38
#, fuzzy
#| msgid "You have been logged in successfully!"
msgid "You have been successfully logged out."
msgstr "تم تسجيل الدخول بنجاح!"

#: .\core\views.py:55
msgid "Please complete the initial setup"
msgstr ""

#: .\core\views.py:77
msgid "You have been logged in successfully!"
msgstr "تم تسجيل الدخول بنجاح!"

#: .\core\views.py:80 .\core\views.py:82
msgid "Invalid username or password."
msgstr "اسم المستخدم أو كلمة المرور غير صحيحة."

#: .\core\views.py:87
msgid "Login"
msgstr "تسجيل الدخول"

#: .\feature_flags\management\commands\create_default_flags.py:7
msgid "Create default module and feature flags"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:14
#, fuzzy
#| msgid "Inventory Management System"
msgid "Inventory management module"
msgstr "نظام ما بعد البيع"

#: .\feature_flags\management\commands\create_default_flags.py:19
msgid "Advanced inventory features like stock movements"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:24
msgid "Barcode scanning for inventory items"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:31
msgid "Warehouse and location management"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:36
msgid "Multiple warehouse locations"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:41
msgid "Bin location tracking within warehouses"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:48
msgid "Sales orders and invoicing"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:53
msgid "Create and manage sales orders"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:58
msgid "Process sales returns"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:65
msgid "Purchase orders and vendor management"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:70
msgid "Create and manage purchase orders"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:75
msgid "Manage vendors and suppliers"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:82
msgid "Reporting and analytics"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:87
msgid "Inventory valuation and stock reports"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:92
msgid "Sales performance reports"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:99
msgid "API access for integration with other systems"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:104
msgid "Read-only API access"
msgstr ""

#: .\feature_flags\management\commands\create_default_flags.py:109
msgid "Write API access"
msgstr ""

#: .\feature_flags\models.py:13 .\feature_flags\models.py:28
#: .\feature_flags\models.py:48 .\feature_flags\models.py:63
#: .\franchise_setup\models.py:85 .\notifications\models.py:62
#: .\purchases\models.py:16 .\sales\models.py:16 .\warehouse\models.py:16
#, fuzzy
#| msgid "Actions"
msgid "Active"
msgstr "الإجراءات"

#: .\feature_flags\models.py:16
msgid "Module Flag"
msgstr ""

#: .\feature_flags\models.py:17
msgid "Module Flags"
msgstr ""

#: .\feature_flags\models.py:33
msgid "Tenant Module Flag"
msgstr ""

#: .\feature_flags\models.py:34
msgid "Tenant Module Flags"
msgstr ""

#: .\feature_flags\models.py:51
msgid "Feature Flag"
msgstr ""

#: .\feature_flags\models.py:52
msgid "Feature Flags"
msgstr ""

#: .\feature_flags\models.py:68
msgid "Tenant Feature Flag"
msgstr ""

#: .\feature_flags\models.py:69
msgid "Tenant Feature Flags"
msgstr ""

#: .\franchise_setup\admin.py:28
msgid "Agreement Defaults"
msgstr ""

#: .\franchise_setup\admin.py:31
msgid "Financial Defaults"
msgstr ""

#: .\franchise_setup\admin.py:35
msgid "Operational Defaults"
msgstr ""

#: .\franchise_setup\admin.py:39 .\franchise_setup\admin.py:74
#: .\franchise_setup\admin.py:146 .\franchise_setup\models.py:298
#, fuzzy
#| msgid "Location"
msgid "Documentation"
msgstr "الموقع"

#: .\franchise_setup\admin.py:62
msgid "Agreement Dates"
msgstr ""

#: .\franchise_setup\admin.py:65
msgid "Territory Information"
msgstr ""

#: .\franchise_setup\admin.py:68
msgid "Agreement Details"
msgstr ""

#: .\franchise_setup\admin.py:71 .\franchise_setup\models.py:82
#: .\franchise_setup\models.py:271 .\purchases\models.py:49
#: .\reports\models.py:55 .\sales\models.py:49 .\warehouse\models.py:69
#: .\work_orders\models.py:116
#: .\work_orders\templates\work_orders\work_order_list.html:40
#, fuzzy
#| msgid "Setup Status"
msgid "Status"
msgstr "حالة الإعداد"

#: .\franchise_setup\admin.py:77 .\franchise_setup\models.py:96
#: .\franchise_setup\models.py:184 .\franchise_setup\models.py:299
#: .\inventory\models.py:259 .\purchases\models.py:50 .\purchases\models.py:127
#: .\sales\models.py:51 .\sales\models.py:130 .\setup\models.py:52
#: .\setup\models.py:94 .\setup\models.py:174 .\setup\models.py:225
#: .\setup\models.py:268 .\warehouse\models.py:70 .\warehouse\models.py:96
#: .\warehouse\models.py:125 .\work_orders\models.py:37
#: .\work_orders\models.py:69 .\work_orders\models.py:136
#: .\work_orders\models.py:163 .\work_orders\models.py:193
msgid "Notes"
msgstr ""

#: .\franchise_setup\admin.py:95
msgid "Revenue Metrics"
msgstr ""

#: .\franchise_setup\admin.py:98
msgid "Performance Metrics"
msgstr ""

#: .\franchise_setup\admin.py:101 .\franchise_setup\admin.py:143
#, fuzzy
#| msgid "Location"
msgid "Verification"
msgstr "الموقع"

#: .\franchise_setup\admin.py:104 .\inventory\admin.py:218
#: .\purchases\admin.py:56 .\purchases\admin.py:103 .\sales\admin.py:56
#: .\sales\admin.py:103 .\setup\admin.py:26 .\setup\admin.py:54
#: .\setup\admin.py:82 .\setup\admin.py:97 .\setup\admin.py:131
#: .\setup\admin.py:153 .\setup\admin.py:174 .\warehouse\admin.py:68
#: .\warehouse\admin.py:89 .\warehouse\admin.py:109 .\work_orders\admin.py:27
#: .\work_orders\admin.py:64 .\work_orders\admin.py:79
msgid "Additional Information"
msgstr ""

#: .\franchise_setup\admin.py:120
msgid "Requirement Details"
msgstr ""

#: .\franchise_setup\admin.py:123
msgid "Validation"
msgstr ""

#: .\franchise_setup\admin.py:126
msgid "Specifications"
msgstr ""

#: .\franchise_setup\admin.py:149
msgid "Resolution (For Non-Compliance)"
msgstr ""

#: .\franchise_setup\models.py:13
msgid "Template Name"
msgstr ""

#: .\franchise_setup\models.py:15 .\franchise_setup\models.py:81
#: .\setup\models.py:17 .\setup\models.py:53 .\setup\models.py:95
#: .\setup\models.py:121 .\setup\models.py:175 .\user_roles\models.py:31
#: .\user_roles\models.py:111 .\work_orders\models.py:13
#: .\work_orders\models.py:36
msgid "Is Active"
msgstr ""

#: .\franchise_setup\models.py:18
msgid "Default Term (Years)"
msgstr ""

#: .\franchise_setup\models.py:19
msgid "Default Renewal Options"
msgstr ""

#: .\franchise_setup\models.py:20
msgid "Default Territory Definition"
msgstr ""

#: .\franchise_setup\models.py:23
msgid "Default Initial Fee"
msgstr ""

#: .\franchise_setup\models.py:24
msgid "Default Royalty Percentage"
msgstr ""

#: .\franchise_setup\models.py:25
msgid "Default Marketing Fee Percentage"
msgstr ""

#: .\franchise_setup\models.py:26
msgid "Default Minimum Performance"
msgstr ""

#: .\franchise_setup\models.py:29
msgid "Default Training Requirements"
msgstr ""

#: .\franchise_setup\models.py:30
msgid "Default Operational Standards"
msgstr ""

#: .\franchise_setup\models.py:31
msgid "Default Technology Requirements"
msgstr ""

#: .\franchise_setup\models.py:34 .\franchise_setup\models.py:61
msgid "Agreement Template"
msgstr ""

#: .\franchise_setup\models.py:35
msgid "Operations Manual Template"
msgstr ""

#: .\franchise_setup\models.py:38 .\franchise_setup\models.py:205
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Template"
msgstr "الامتيازات"

#: .\franchise_setup\models.py:39 .\franchise_setup\views.py:241
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Templates"
msgstr "الامتيازات"

#: .\franchise_setup\models.py:54 .\franchise_setup\models.py:153
#: .\franchise_setup\models.py:261 .\setup\models.py:57 .\setup\models.py:72
#: .\setup\templates\setup\dashboard.html:287
#, fuzzy
#| msgid "Franchises"
msgid "Franchise"
msgstr "الامتيازات"

#: .\franchise_setup\models.py:63
msgid "Agreement Name"
msgstr ""

#: .\franchise_setup\models.py:66
#, fuzzy
#| msgid "Start"
msgid "Start Date"
msgstr "ابدأ"

#: .\franchise_setup\models.py:67
msgid "End Date"
msgstr ""

#: .\franchise_setup\models.py:68
#, fuzzy
#| msgid "Sign out"
msgid "Signed Date"
msgstr "تسجيل الخروج"

#: .\franchise_setup\models.py:71
msgid "Territory Definition"
msgstr ""

#: .\franchise_setup\models.py:72
msgid "Territory Exclusivity"
msgstr ""

#: .\franchise_setup\models.py:75
msgid "Term (Years)"
msgstr ""

#: .\franchise_setup\models.py:76
msgid "Renewal Options"
msgstr ""

#: .\franchise_setup\models.py:77
msgid "Renewal Terms"
msgstr ""

#: .\franchise_setup\models.py:78
msgid "Termination Conditions"
msgstr ""

#: .\franchise_setup\models.py:83 .\purchases\models.py:33 .\sales\models.py:33
#: .\warehouse\models.py:53 .\work_orders\models.py:92
#: .\work_orders\templates\work_orders\work_order_list.html:72
msgid "Draft"
msgstr ""

#: .\franchise_setup\models.py:84
msgid "Pending Approval"
msgstr ""

#: .\franchise_setup\models.py:86
msgid "Pending Renewal"
msgstr ""

#: .\franchise_setup\models.py:87
msgid "Terminated"
msgstr ""

#: .\franchise_setup\models.py:88
msgid "Expired"
msgstr ""

#: .\franchise_setup\models.py:92
msgid "Agreement Document"
msgstr ""

#: .\franchise_setup\models.py:93
msgid "Additional Documents"
msgstr ""

#: .\franchise_setup\models.py:99 .\franchise_setup\models.py:115
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Agreement"
msgstr "الامتيازات"

#: .\franchise_setup\models.py:100 .\franchise_setup\views.py:91
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Agreements"
msgstr "الامتيازات"

#: .\franchise_setup\models.py:119
msgid "Initial Fee"
msgstr ""

#: .\franchise_setup\models.py:120
msgid "Royalty Percentage"
msgstr ""

#: .\franchise_setup\models.py:121
msgid "Marketing Fee Percentage"
msgstr ""

#: .\franchise_setup\models.py:122
msgid "Technology Fee"
msgstr ""

#: .\franchise_setup\models.py:123
msgid "Technology Fee Frequency"
msgstr ""

#: .\franchise_setup\models.py:124 .\franchise_setup\models.py:234
msgid "Monthly"
msgstr ""

#: .\franchise_setup\models.py:125 .\franchise_setup\models.py:235
msgid "Quarterly"
msgstr ""

#: .\franchise_setup\models.py:126 .\franchise_setup\models.py:237
msgid "Annually"
msgstr ""

#: .\franchise_setup\models.py:127
msgid "One Time"
msgstr ""

#: .\franchise_setup\models.py:131
msgid "Payment Terms"
msgstr ""

#: .\franchise_setup\models.py:132
msgid "Late Payment Penalty"
msgstr ""

#: .\franchise_setup\models.py:135
msgid "Additional Fees"
msgstr ""

#: .\franchise_setup\models.py:138
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Fee"
msgstr "الامتيازات"

#: .\franchise_setup\models.py:139
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Fees"
msgstr "الامتيازات"

#: .\franchise_setup\models.py:155 .\setup\models.py:203
msgid "Year"
msgstr ""

#: .\franchise_setup\models.py:156
msgid "Quarter"
msgstr ""

#: .\franchise_setup\models.py:157
msgid "Q1"
msgstr ""

#: .\franchise_setup\models.py:158
msgid "Q2"
msgstr ""

#: .\franchise_setup\models.py:159
msgid "Q3"
msgstr ""

#: .\franchise_setup\models.py:160
msgid "Q4"
msgstr ""

#: .\franchise_setup\models.py:164
msgid "Total Revenue"
msgstr ""

#: .\franchise_setup\models.py:165
msgid "Royalty Amount"
msgstr ""

#: .\franchise_setup\models.py:166
msgid "Marketing Fee Amount"
msgstr ""

#: .\franchise_setup\models.py:169
msgid "Work Order Count"
msgstr ""

#: .\franchise_setup\models.py:170
msgid "Customer Satisfaction"
msgstr ""

#: .\franchise_setup\models.py:173
msgid "Is Verified"
msgstr ""

#: .\franchise_setup\models.py:179 .\franchise_setup\models.py:286
msgid "Verified By"
msgstr ""

#: .\franchise_setup\models.py:181
msgid "Verified Date"
msgstr ""

#: .\franchise_setup\models.py:185
msgid "Detailed Breakdown"
msgstr ""

#: .\franchise_setup\models.py:188
msgid "Revenue Share"
msgstr ""

#: .\franchise_setup\models.py:189 .\franchise_setup\views.py:214
msgid "Revenue Shares"
msgstr ""

#: .\franchise_setup\models.py:207
msgid "Requirement Name"
msgstr ""

#: .\franchise_setup\models.py:211
msgid "Requirement Type"
msgstr ""

#: .\franchise_setup\models.py:212
msgid "Operational Standard"
msgstr ""

#: .\franchise_setup\models.py:213
msgid "Training Requirement"
msgstr ""

#: .\franchise_setup\models.py:214
msgid "Reporting Requirement"
msgstr ""

#: .\franchise_setup\models.py:215
msgid "Facility Requirement"
msgstr ""

#: .\franchise_setup\models.py:216
msgid "Staffing Requirement"
msgstr ""

#: .\franchise_setup\models.py:217
msgid "Technology Requirement"
msgstr ""

#: .\franchise_setup\models.py:218
msgid "Marketing Requirement"
msgstr ""

#: .\franchise_setup\models.py:219
msgid "Customer Service Standard"
msgstr ""

#: .\franchise_setup\models.py:220 .\franchise_setup\models.py:231
#: .\franchise_setup\models.py:294 .\inventory\models.py:185
msgid "Other"
msgstr ""

#: .\franchise_setup\models.py:224
msgid "Is Mandatory"
msgstr ""

#: .\franchise_setup\models.py:225
msgid "Validation Method"
msgstr ""

#: .\franchise_setup\models.py:226 .\franchise_setup\models.py:289
msgid "Self Reporting"
msgstr ""

#: .\franchise_setup\models.py:227 .\franchise_setup\models.py:290
msgid "Physical Inspection"
msgstr ""

#: .\franchise_setup\models.py:228 .\franchise_setup\models.py:291
msgid "Documentation Review"
msgstr ""

#: .\franchise_setup\models.py:229 .\franchise_setup\models.py:292
msgid "Audit"
msgstr ""

#: .\franchise_setup\models.py:230 .\franchise_setup\models.py:293
msgid "Customer Feedback"
msgstr ""

#: .\franchise_setup\models.py:233
msgid "Validation Frequency"
msgstr ""

#: .\franchise_setup\models.py:236
msgid "Semi-Annually"
msgstr ""

#: .\franchise_setup\models.py:238
msgid "Initial Setup Only"
msgstr ""

#: .\franchise_setup\models.py:242
msgid "Detailed Specifications"
msgstr ""

#: .\franchise_setup\models.py:245
msgid "Franchise Requirement"
msgstr ""

#: .\franchise_setup\models.py:246
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Requirements"
msgstr "الامتيازات"

#: .\franchise_setup\models.py:267
msgid "Requirement"
msgstr ""

#: .\franchise_setup\models.py:272
#, fuzzy
#| msgid "Companies"
msgid "Compliant"
msgstr "الشركات"

#: .\franchise_setup\models.py:273
msgid "Non-Compliant"
msgstr ""

#: .\franchise_setup\models.py:274
msgid "Pending Verification"
msgstr ""

#: .\franchise_setup\models.py:275
msgid "Exempt"
msgstr ""

#: .\franchise_setup\models.py:276 .\work_orders\models.py:94
#: .\work_orders\templates\work_orders\work_order_list.html:80
msgid "In Progress"
msgstr ""

#: .\franchise_setup\models.py:280
msgid "Verification Date"
msgstr ""

#: .\franchise_setup\models.py:288
msgid "Verification Method"
msgstr ""

#: .\franchise_setup\models.py:302
msgid "Resolution Plan"
msgstr ""

#: .\franchise_setup\models.py:303
msgid "Resolution Deadline"
msgstr ""

#: .\franchise_setup\models.py:306 .\franchise_setup\views.py:166
#, fuzzy
#| msgid "Create Companies"
msgid "Franchise Compliance"
msgstr "إنشاء الشركات"

#: .\franchise_setup\models.py:307
msgid "Franchise Compliance Records"
msgstr ""

#: .\franchise_setup\views.py:58
#, fuzzy
#| msgid "After-Sales Franchise Management System"
msgid "Franchise Management Dashboard"
msgstr "نظام إدارة امتياز ما بعد البيع"

#: .\franchise_setup\views.py:125
msgid "Agreement: "
msgstr ""

#: .\franchise_setup\views.py:265
msgid "Template: "
msgstr ""

#: .\franchise_setup\views.py:292
msgid "Invalid status provided"
msgstr ""

#: .\inventory\admin.py:31 .\inventory\admin.py:250
msgid "Preview"
msgstr ""

#: .\inventory\admin.py:38
msgid "Conversion to another unit"
msgstr ""

#: .\inventory\admin.py:39
msgid "Conversions to other units"
msgstr ""

#: .\inventory\admin.py:179
msgid "Stock Information"
msgstr ""

#: .\inventory\admin.py:182
msgid "Dynamic Attributes"
msgstr ""

#: .\inventory\admin.py:194
msgid "Low Stock"
msgstr ""

#: .\inventory\admin.py:201 .\inventory\admin.py:231
msgid "Unit"
msgstr ""

#: .\inventory\admin.py:247
msgid "Access Control"
msgstr ""

#: .\inventory\admin.py:285 .\inventory\models.py:193
msgid "File"
msgstr ""

#: .\inventory\models.py:23
msgid "Symbol"
msgstr ""

#: .\inventory\models.py:25
msgid "Is Base Unit"
msgstr ""

#: .\inventory\models.py:26
msgid "If checked, this unit will be used as a reference for conversions"
msgstr ""

#: .\inventory\models.py:31 .\inventory\models.py:88 .\inventory\models.py:255
msgid "Unit of Measurement"
msgstr ""

#: .\inventory\models.py:32
msgid "Units of Measurement"
msgstr ""

#: .\inventory\models.py:44
msgid "From Unit"
msgstr ""

#: .\inventory\models.py:46
msgid "To Unit"
msgstr ""

#: .\inventory\models.py:47
msgid "Conversion Factor"
msgstr ""

#: .\inventory\models.py:48
msgid ""
"Multiply quantity in 'from_unit' by this factor to get quantity in 'to_unit'"
msgstr ""

#: .\inventory\models.py:53
msgid "Unit Conversion"
msgstr ""

#: .\inventory\models.py:54
msgid "Unit Conversions"
msgstr ""

#: .\inventory\models.py:65 .\inventory\views.py:400
msgid "From unit and to unit must be different"
msgstr ""

#: .\inventory\models.py:68
msgid "Both units must belong to the same tenant"
msgstr ""

#: .\inventory\models.py:83 .\inventory\templates\inventory\dashboard.html:106
msgid "SKU"
msgstr ""

#: .\inventory\models.py:86 .\inventory\models.py:253
#: .\inventory\templates\inventory\dashboard.html:175 .\purchases\models.py:87
#: .\sales\models.py:88 .\sales\models.py:159 .\warehouse\models.py:35
#: .\warehouse\models.py:95 .\work_orders\models.py:65
#: .\work_orders\models.py:190
msgid "Quantity"
msgstr ""

#: .\inventory\models.py:90 .\purchases\models.py:88 .\sales\models.py:89
msgid "Unit Price"
msgstr ""

#: .\inventory\models.py:91
msgid "Minimum Stock Level"
msgstr ""

#: .\inventory\models.py:92
msgid "Attributes"
msgstr ""

#: .\inventory\models.py:97 .\inventory\models.py:189
#: .\inventory\templates\inventory\dashboard.html:169 .\purchases\models.py:85
#: .\sales\models.py:86 .\warehouse\models.py:93 .\work_orders\models.py:188
msgid "Item"
msgstr ""

#: .\inventory\models.py:125
msgid "Item does not have a unit of measurement defined"
msgstr ""

#: .\inventory\models.py:156 .\inventory\models.py:311 .\inventory\views.py:250
msgid "No conversion path exists between {} and {}"
msgstr ""

#: .\inventory\models.py:180
#, fuzzy
#| msgid "Username"
msgid "User Manual"
msgstr "اسم المستخدم"

#: .\inventory\models.py:181
msgid "Technical Specification"
msgstr ""

#: .\inventory\models.py:182
msgid "Certification"
msgstr ""

#: .\inventory\models.py:183
msgid "Warranty Information"
msgstr ""

#: .\inventory\models.py:184
msgid "Product Image"
msgstr ""

#: .\inventory\models.py:190 .\notifications\models.py:18
#: .\reports\models.py:126
msgid "Title"
msgstr ""

#: .\inventory\models.py:191
msgid "Document Type"
msgstr ""

#: .\inventory\models.py:195
msgid "Public"
msgstr ""

#: .\inventory\models.py:196
msgid "If checked, this document will be visible to all users"
msgstr ""

#: .\inventory\models.py:201
msgid "Item Document"
msgstr ""

#: .\inventory\models.py:202
msgid "Item Documents"
msgstr ""

#: .\inventory\models.py:245
msgid "Purchase"
msgstr ""

#: .\inventory\models.py:246
msgid "Sale"
msgstr ""

#: .\inventory\models.py:247
msgid "Adjustment"
msgstr ""

#: .\inventory\models.py:248 .\warehouse\models.py:130
msgid "Transfer"
msgstr ""

#: .\inventory\models.py:249
msgid "Return"
msgstr ""

#: .\inventory\models.py:257
msgid "Movement Type"
msgstr ""

#: .\inventory\models.py:258 .\warehouse\models.py:60 .\warehouse\models.py:124
msgid "Reference"
msgstr ""

#: .\inventory\models.py:264
msgid "Movement"
msgstr ""

#: .\inventory\models.py:265
msgid "Movements"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:11
#, fuzzy
#| msgid "Sign out"
msgid "Sign Out"
msgstr "تسجيل الخروج"

#: .\inventory\templates\inventory\dashboard.html:21
msgid "Total Items"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:31
msgid "View all items"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:39
msgid "Low Stock Items"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:51
#, fuzzy
#| msgid "Inventory"
msgid "Manage inventory"
msgstr "المخزون"

#: .\inventory\templates\inventory\dashboard.html:69
msgid "View all movements"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:77
#, fuzzy
#| msgid "Actions"
msgid "Quick Actions"
msgstr "الإجراءات"

#: .\inventory\templates\inventory\dashboard.html:78
#, fuzzy
#| msgid "Go to Inventory"
msgid "Manage your inventory"
msgstr "الذهاب إلى المخزون"

#: .\inventory\templates\inventory\dashboard.html:88
msgid "Add new item"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:91
msgid "Scan barcode"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:99
#, fuzzy
#| msgid "Recent Entries"
msgid "Recent Items"
msgstr "الإدخالات الأخيرة"

#: .\inventory\templates\inventory\dashboard.html:112
msgid "Stock"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:115
#: .\setup\templates\setup\dashboard.html:246
#: .\setup\templates\setup\dashboard.html:290
#: .\setup\templates\setup\dashboard.html:337
#: .\work_orders\templates\work_orders\work_order_list.html:52
msgid "Actions"
msgstr "الإجراءات"

#: .\inventory\templates\inventory\dashboard.html:140
#: .\setup\templates\setup\dashboard.html:262
#: .\setup\templates\setup\dashboard.html:306
#: .\setup\templates\setup\dashboard.html:356 .\user_roles\models.py:204
msgid "View"
msgstr "عرض"

#: .\inventory\templates\inventory\dashboard.html:147
msgid "No items found"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:159
#, fuzzy
#| msgid "Recent Entries"
msgid "Recent Movements"
msgstr "الإدخالات الأخيرة"

#: .\inventory\templates\inventory\dashboard.html:166
msgid "Date"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:172
#: .\work_orders\templates\work_orders\work_order_list.html:37
msgid "Type"
msgstr ""

#: .\inventory\templates\inventory\dashboard.html:217
msgid "No movements found"
msgstr ""

#: .\inventory\views.py:278
msgid "Barcode not found"
msgstr ""

#: .\notifications\admin.py:25
msgid "Security"
msgstr ""

#: .\notifications\admin.py:29
#, fuzzy
#| msgid "Actions"
msgid "Subscriptions"
msgstr "الإجراءات"

#: .\notifications\models.py:12
#, fuzzy
#| msgid "Location"
msgid "Information"
msgstr "الموقع"

#: .\notifications\models.py:13 .\notifications\models.py:90
msgid "Success"
msgstr ""

#: .\notifications\models.py:14
msgid "Warning"
msgstr ""

#: .\notifications\models.py:15
#, fuzzy
#| msgid "Error!"
msgid "Error"
msgstr "خطأ!"

#: .\notifications\models.py:19
msgid "Message"
msgstr ""

#: .\notifications\models.py:20
msgid "Level"
msgstr ""

#: .\notifications\models.py:21
msgid "Read"
msgstr ""

#: .\notifications\models.py:22
msgid "Read At"
msgstr ""

#: .\notifications\models.py:23
msgid "Object Type"
msgstr ""

#: .\notifications\models.py:24
msgid "Object ID"
msgstr ""

#: .\notifications\models.py:29
#, fuzzy
#| msgid "Location"
msgid "Notification"
msgstr "الموقع"

#: .\notifications\models.py:30
#, fuzzy
#| msgid "Location"
msgid "Notifications"
msgstr "الموقع"

#: .\notifications\models.py:60
msgid "URL"
msgstr ""

#: .\notifications\models.py:63
msgid "Secret Key"
msgstr ""

#: .\notifications\models.py:64
msgid "Used for webhook signature validation"
msgstr ""

#: .\notifications\models.py:67
msgid "Item Created"
msgstr ""

#: .\notifications\models.py:68
msgid "Item Updated"
msgstr ""

#: .\notifications\models.py:69
msgid "Stock Low"
msgstr ""

#: .\notifications\models.py:70
msgid "Movement Created"
msgstr ""

#: .\notifications\models.py:73
msgid "Webhook Endpoint"
msgstr ""

#: .\notifications\models.py:74
msgid "Webhook Endpoints"
msgstr ""

#: .\notifications\models.py:85
msgid "Endpoint"
msgstr ""

#: .\notifications\models.py:86
msgid "Event Type"
msgstr ""

#: .\notifications\models.py:87
msgid "Payload"
msgstr ""

#: .\notifications\models.py:88
#, fuzzy
#| msgid "Setup Status"
msgid "Response Status"
msgstr "حالة الإعداد"

#: .\notifications\models.py:89
msgid "Response Body"
msgstr ""

#: .\notifications\models.py:91
msgid "Attempts"
msgstr ""

#: .\notifications\models.py:94
msgid "Webhook Delivery"
msgstr ""

#: .\notifications\models.py:95
msgid "Webhook Deliveries"
msgstr ""

#: .\project\settings.py:163
msgid "Arabic"
msgstr ""

#: .\project\settings.py:164
msgid "English"
msgstr ""

#: .\purchases\admin.py:53 .\sales\admin.py:53 .\setup\admin.py:171
#: .\work_orders\admin.py:61
msgid "Financial"
msgstr ""

#: .\purchases\admin.py:77 .\sales\admin.py:77
msgid "Pricing"
msgstr ""

#: .\purchases\admin.py:88 .\sales\admin.py:88
msgid "Total Price"
msgstr ""

#: .\purchases\models.py:13 .\sales\models.py:13 .\setup\models.py:43
#: .\setup\models.py:85 .\setup\models.py:160
msgid "Email"
msgstr ""

#: .\purchases\models.py:14 .\sales\models.py:14 .\setup\models.py:42
#: .\setup\models.py:84 .\setup\models.py:159
msgid "Phone"
msgstr ""

#: .\purchases\models.py:21 .\purchases\models.py:45
msgid "Supplier"
msgstr ""

#: .\purchases\models.py:22
msgid "Suppliers"
msgstr ""

#: .\purchases\models.py:34
msgid "Sent"
msgstr ""

#: .\purchases\models.py:35 .\sales\models.py:34
msgid "Confirmed"
msgstr ""

#: .\purchases\models.py:36
msgid "Received"
msgstr ""

#: .\purchases\models.py:37 .\sales\models.py:37 .\warehouse\models.py:57
#: .\work_orders\models.py:97
#: .\work_orders\templates\work_orders\work_order_list.html:92
msgid "Cancelled"
msgstr ""

#: .\purchases\models.py:40
msgid "PO Number"
msgstr ""

#: .\purchases\models.py:47 .\sales\models.py:48
msgid "Order Date"
msgstr ""

#: .\purchases\models.py:48
msgid "Expected Delivery Date"
msgstr ""

#: .\purchases\models.py:51 .\sales\models.py:52
msgid "Total Amount"
msgstr ""

#: .\purchases\models.py:56 .\purchases\models.py:79 .\purchases\models.py:124
msgid "Purchase Order"
msgstr ""

#: .\purchases\models.py:57
msgid "Purchase Orders"
msgstr ""

#: .\purchases\models.py:93 .\purchases\models.py:154
msgid "Purchase Order Item"
msgstr ""

#: .\purchases\models.py:94
msgid "Purchase Order Items"
msgstr ""

#: .\purchases\models.py:119
msgid "Receipt Number"
msgstr ""

#: .\purchases\models.py:126
#, fuzzy
#| msgid "Recent Entries"
msgid "Receipt Date"
msgstr "الإدخالات الأخيرة"

#: .\purchases\models.py:132 .\purchases\models.py:148
msgid "Purchase Receipt"
msgstr ""

#: .\purchases\models.py:133
msgid "Purchase Receipts"
msgstr ""

#: .\purchases\models.py:156
msgid "Quantity Received"
msgstr ""

#: .\purchases\models.py:161
msgid "Purchase Receipt Item"
msgstr ""

#: .\purchases\models.py:162
msgid "Purchase Receipt Items"
msgstr ""

#: .\reports\admin.py:18 .\reports\admin.py:79
#, fuzzy
#| msgid "Location"
msgid "Duration"
msgstr "الموقع"

#: .\reports\admin.py:38
msgid "Query and Parameters"
msgstr ""

#: .\reports\admin.py:41 .\work_orders\admin.py:55
msgid "Scheduling"
msgstr ""

#: .\reports\admin.py:63
msgid "Timing"
msgstr ""

#: .\reports\admin.py:66
msgid "Results"
msgstr ""

#: .\reports\admin.py:93 .\reports\models.py:87
msgid "Layout"
msgstr ""

#: .\reports\admin.py:114
msgid "Data Source"
msgstr ""

#: .\reports\models.py:13 .\user_roles\admin.py:49 .\user_roles\models.py:197
msgid "Sales"
msgstr ""

#: .\reports\models.py:14 .\user_roles\admin.py:51 .\user_roles\models.py:198
msgid "Purchases"
msgstr ""

#: .\reports\models.py:15 .\user_roles\admin.py:47 .\user_roles\models.py:196
msgid "Warehouse"
msgstr ""

#: .\reports\models.py:16 .\reports\models.py:109
msgid "Custom"
msgstr ""

#: .\reports\models.py:21
#, fuzzy
#| msgid "Reports"
msgid "Report Type"
msgstr "التقارير"

#: .\reports\models.py:22
msgid "Parameters"
msgstr ""

#: .\reports\models.py:23
msgid "Query"
msgstr ""

#: .\reports\models.py:24
msgid "Scheduled"
msgstr ""

#: .\reports\models.py:25
msgid "Schedule"
msgstr ""

#: .\reports\models.py:30 .\reports\models.py:52 .\reports\models.py:122
#, fuzzy
#| msgid "Reports"
msgid "Report"
msgstr "التقارير"

#: .\reports\models.py:42 .\warehouse\models.py:54
msgid "Pending"
msgstr ""

#: .\reports\models.py:43
msgid "Running"
msgstr ""

#: .\reports\models.py:44 .\warehouse\models.py:56 .\work_orders\models.py:96
#: .\work_orders\templates\work_orders\work_order_list.html:88
msgid "Completed"
msgstr ""

#: .\reports\models.py:45
msgid "Failed"
msgstr ""

#: .\reports\models.py:54
msgid "Parameters Used"
msgstr ""

#: .\reports\models.py:56
#, fuzzy
#| msgid "Start"
msgid "Start Time"
msgstr "ابدأ"

#: .\reports\models.py:57
msgid "End Time"
msgstr ""

#: .\reports\models.py:58
msgid "Result File"
msgstr ""

#: .\reports\models.py:59
msgid "Error Message"
msgstr ""

#: .\reports\models.py:64
msgid "Report Execution"
msgstr ""

#: .\reports\models.py:65
msgid "Report Executions"
msgstr ""

#: .\reports\models.py:88
msgid "Default"
msgstr ""

#: .\reports\models.py:94
#, fuzzy
#| msgid "Dashboard"
msgid "Dashboards"
msgstr "لوحة التحكم"

#: .\reports\models.py:105
msgid "Chart"
msgstr ""

#: .\reports\models.py:106
msgid "Number"
msgstr ""

#: .\reports\models.py:107
msgid "Table"
msgstr ""

#: .\reports\models.py:108
msgid "Text"
msgstr ""

#: .\reports\models.py:127
msgid "Widget Type"
msgstr ""

#: .\reports\models.py:128
msgid "Configuration"
msgstr ""

#: .\reports\models.py:129
#, fuzzy
#| msgid "Location"
msgid "Position"
msgstr "الموقع"

#: .\reports\models.py:134
#, fuzzy
#| msgid "Dashboard"
msgid "Dashboard Widget"
msgstr "لوحة التحكم"

#: .\reports\models.py:135
#, fuzzy
#| msgid "Dashboard"
msgid "Dashboard Widgets"
msgstr "لوحة التحكم"

#: .\reports\views.py:26
msgid "Advanced reporting feature is not enabled."
msgstr ""

#: .\reports\views.py:143
msgid "Error executing report: {}"
msgstr ""

#: .\reports\views.py:185
msgid "Error loading result file: {}"
msgstr ""

#: .\reports\views.py:225
msgid "Error loading dashboard data: {}"
msgstr ""

#: .\sales\admin.py:50
msgid "Shipping"
msgstr ""

#: .\sales\models.py:21 .\sales\models.py:46
#: .\work_orders\templates\work_orders\work_order_list.html:49
msgid "Customer"
msgstr ""

#: .\sales\models.py:22
msgid "Customers"
msgstr ""

#: .\sales\models.py:35
msgid "Shipped"
msgstr ""

#: .\sales\models.py:36
msgid "Delivered"
msgstr ""

#: .\sales\models.py:38
msgid "Returned"
msgstr ""

#: .\sales\models.py:41
msgid "Order Number"
msgstr ""

#: .\sales\models.py:50
msgid "Shipping Address"
msgstr ""

#: .\sales\models.py:57 .\sales\models.py:80 .\sales\models.py:126
msgid "Sales Order"
msgstr ""

#: .\sales\models.py:58
msgid "Sales Orders"
msgstr ""

#: .\sales\models.py:90
msgid "Discount"
msgstr ""

#: .\sales\models.py:95 .\sales\models.py:157
msgid "Sales Order Item"
msgstr ""

#: .\sales\models.py:96
msgid "Sales Order Items"
msgstr ""

#: .\sales\models.py:121
msgid "Return Number"
msgstr ""

#: .\sales\models.py:128
msgid "Return Date"
msgstr ""

#: .\sales\models.py:129
msgid "Reason"
msgstr ""

#: .\sales\models.py:135 .\sales\models.py:151
msgid "Sales Return"
msgstr ""

#: .\sales\models.py:136
msgid "Sales Returns"
msgstr ""

#: .\sales\models.py:164
msgid "Sales Return Item"
msgstr ""

#: .\sales\models.py:165
msgid "Sales Return Items"
msgstr ""

#: .\setup\admin.py:23
msgid "SLA Details"
msgstr ""

#: .\setup\admin.py:51 .\setup\admin.py:79
msgid "Business Information"
msgstr ""

#: .\setup\admin.py:128
msgid "Operational Information"
msgstr ""

#: .\setup\admin.py:144
msgid "Vehicle Information"
msgstr ""

#: .\setup\admin.py:147
msgid "Owner Information"
msgstr ""

#: .\setup\admin.py:150
msgid "Service Information"
msgstr ""

#: .\setup\admin.py:168
#, fuzzy
#| msgid "Service Centers"
msgid "Service Details"
msgstr "مراكز الخدمة"

#: .\setup\apps.py:8 .\setup\templates\setup\dashboard.html:12
msgid "Organization Setup"
msgstr "إعداد المؤسسة"

#: .\setup\models.py:11
#, fuzzy
#| msgid "Service Centers"
msgid "Service Level Name"
msgstr "مراكز الخدمة"

#: .\setup\models.py:13 .\work_orders\models.py:115
#: .\work_orders\templates\work_orders\work_order_list.html:43
msgid "Priority"
msgstr ""

#: .\setup\models.py:14
msgid "Response Time (Hours)"
msgstr ""

#: .\setup\models.py:15
msgid "Resolution Time (Hours)"
msgstr ""

#: .\setup\models.py:16
msgid "Support Hours"
msgstr ""

#: .\setup\models.py:18 .\setup\models.py:54 .\setup\models.py:103
#: .\setup\models.py:122 .\setup\models.py:183 .\setup\models.py:226
#: .\setup\models.py:269 .\work_orders\models.py:12 .\work_orders\models.py:38
#: .\work_orders\models.py:70 .\work_orders\models.py:137
#: .\work_orders\models.py:164 .\work_orders\models.py:194
msgid "Custom Attributes"
msgstr ""

#: .\setup\models.py:21 .\setup\models.py:101 .\setup\models.py:181
#, fuzzy
#| msgid "Service Centers"
msgid "Service Level"
msgstr "مراكز الخدمة"

#: .\setup\models.py:22
#, fuzzy
#| msgid "Service Centers"
msgid "Service Levels"
msgstr "مراكز الخدمة"

#: .\setup\models.py:32
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Name"
msgstr "الامتيازات"

#: .\setup\models.py:33
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Code"
msgstr "الامتيازات"

#: .\setup\models.py:38 .\setup\models.py:80 .\setup\models.py:153
msgid "City"
msgstr ""

#: .\setup\models.py:39 .\setup\models.py:81 .\setup\models.py:154
msgid "State/Province"
msgstr ""

#: .\setup\models.py:40 .\setup\models.py:82 .\setup\models.py:155
msgid "Country"
msgstr ""

#: .\setup\models.py:41 .\setup\models.py:83 .\setup\models.py:156
msgid "Postal Code"
msgstr ""

#: .\setup\models.py:47 .\setup\models.py:89
msgid "Tax ID"
msgstr ""

#: .\setup\models.py:48 .\setup\models.py:90
msgid "Registration Number"
msgstr ""

#: .\setup\models.py:49 .\setup\models.py:91
msgid "Founding Date"
msgstr ""

#: .\setup\models.py:58 .\setup\templates\setup\dashboard.html:27
#: .\setup\templates\setup\dashboard.html:233 .\setup\views.py:47
msgid "Franchises"
msgstr "الامتيازات"

#: .\setup\models.py:75
#, fuzzy
#| msgid "Companies"
msgid "Company Code"
msgstr "الشركات"

#: .\setup\models.py:106 .\setup\models.py:140
#: .\setup\templates\setup\dashboard.html:331
#, fuzzy
#| msgid "Companies"
msgid "Company"
msgstr "الشركات"

#: .\setup\models.py:107 .\setup\templates\setup\dashboard.html:51
#: .\setup\templates\setup\dashboard.html:277 .\setup\views.py:77
msgid "Companies"
msgstr "الشركات"

#: .\setup\models.py:117
#, fuzzy
#| msgid "Name"
msgid "Type Name"
msgstr "الاسم"

#: .\setup\models.py:119
msgid "Maximum Capacity"
msgstr ""

#: .\setup\models.py:119
msgid "Maximum number of work orders per day"
msgstr ""

#: .\setup\models.py:120 .\work_orders\models.py:11
msgid "Color Code"
msgstr ""

#: .\setup\models.py:120 .\work_orders\models.py:11
msgid "For UI display"
msgstr ""

#: .\setup\models.py:125
#, fuzzy
#| msgid "Service Centers"
msgid "Service Center Type"
msgstr "مراكز الخدمة"

#: .\setup\models.py:126
#, fuzzy
#| msgid "Service Centers"
msgid "Service Center Types"
msgstr "مراكز الخدمة"

#: .\setup\models.py:142
#, fuzzy
#| msgid "Service Centers"
msgid "Service Center Name"
msgstr "مراكز الخدمة"

#: .\setup\models.py:143
#, fuzzy
#| msgid "Service Centers"
msgid "Service Center Code"
msgstr "مراكز الخدمة"

#: .\setup\models.py:148
msgid "Center Type"
msgstr ""

#: .\setup\models.py:157
msgid "Latitude"
msgstr ""

#: .\setup\models.py:158
msgid "Longitude"
msgstr ""

#: .\setup\models.py:168
msgid "Manager"
msgstr ""

#: .\setup\models.py:170
msgid "Opening Hours"
msgstr ""

#: .\setup\models.py:171
msgid "Daily Capacity"
msgstr ""

#: .\setup\models.py:171
msgid "Maximum work orders per day"
msgstr ""

#: .\setup\models.py:188 .\setup\models.py:219 .\setup\models.py:252
#, fuzzy
#| msgid "Service Centers"
msgid "Service Center"
msgstr "مراكز الخدمة"

#: .\setup\models.py:189 .\setup\templates\setup\dashboard.html:75
#: .\setup\templates\setup\dashboard.html:321 .\setup\views.py:106
msgid "Service Centers"
msgstr "مراكز الخدمة"

#: .\setup\models.py:201
msgid "Make"
msgstr ""

#: .\setup\models.py:202
msgid "Model"
msgstr ""

#: .\setup\models.py:204
msgid "VIN"
msgstr ""

#: .\setup\models.py:205
msgid "License Plate"
msgstr ""

#: .\setup\models.py:206
msgid "Color"
msgstr ""

#: .\setup\models.py:209
msgid "Owner Name"
msgstr ""

#: .\setup\models.py:210
msgid "Owner Phone"
msgstr ""

#: .\setup\models.py:211
msgid "Owner Email"
msgstr ""

#: .\setup\models.py:223
msgid "Purchase Date"
msgstr ""

#: .\setup\models.py:224
msgid "Warranty End Date"
msgstr ""

#: .\setup\models.py:231 .\setup\models.py:246
msgid "Vehicle"
msgstr ""

#: .\setup\models.py:232 .\setup\views.py:142
msgid "Vehicles"
msgstr ""

#: .\setup\models.py:254
#, fuzzy
#| msgid "Service Centers"
msgid "Service Date"
msgstr "مراكز الخدمة"

#: .\setup\models.py:255
msgid "Odometer Reading"
msgstr ""

#: .\setup\models.py:257 .\work_orders\models.py:100
#: .\work_orders\templates\work_orders\work_order_list.html:34
msgid "Work Order #"
msgstr ""

#: .\setup\models.py:260
#, fuzzy
#| msgid "Service Centers"
msgid "Services Performed"
msgstr "مراكز الخدمة"

#: .\setup\models.py:261
msgid "Parts Used"
msgstr ""

#: .\setup\models.py:262 .\user_roles\models.py:18
msgid "Technician"
msgstr ""

#: .\setup\models.py:265
msgid "Total Cost"
msgstr ""

#: .\setup\models.py:274 .\setup\models.py:275
#, fuzzy
#| msgid "Service Centers"
msgid "Service History"
msgstr "مراكز الخدمة"

#: .\setup\templates\setup\dashboard.html:20
msgid "Setup Status"
msgstr "حالة الإعداد"

#: .\setup\templates\setup\dashboard.html:39
msgid "Manage franchises"
msgstr "إدارة الامتيازات"

#: .\setup\templates\setup\dashboard.html:42
msgid "Create your first franchise"
msgstr "إنشاء أول امتياز لك"

#: .\setup\templates\setup\dashboard.html:63
msgid "Manage companies"
msgstr "إدارة الشركات"

#: .\setup\templates\setup\dashboard.html:66
msgid "Create your first company"
msgstr "إنشاء أول شركة لك"

#: .\setup\templates\setup\dashboard.html:87
msgid "Manage service centers"
msgstr "إدارة مراكز الخدمة"

#: .\setup\templates\setup\dashboard.html:90
msgid "Create your first service center"
msgstr "إنشاء أول مركز خدمة لك"

#: .\setup\templates\setup\dashboard.html:104
msgid ""
"Your system is not set up yet. Please create at least one franchise, "
"company, or service center to get started."
msgstr ""
"لم يتم إعداد النظام الخاص بك بعد. يرجى إنشاء امتياز واحد على الأقل أو شركة "
"أو مركز خدمة للبدء."

#: .\setup\templates\setup\dashboard.html:116
msgid "Quick Setup Guide"
msgstr "دليل الإعداد السريع"

#: .\setup\templates\setup\dashboard.html:130
msgid "Create a Franchise"
msgstr "إنشاء امتياز"

#: .\setup\templates\setup\dashboard.html:133
msgid ""
"Set up your main franchise with contact information and business details."
msgstr "قم بإعداد الامتياز الرئيسي الخاص بك مع معلومات الاتصال وتفاصيل العمل."

#: .\setup\templates\setup\dashboard.html:155
msgid "Create Companies"
msgstr "إنشاء الشركات"

#: .\setup\templates\setup\dashboard.html:158
msgid ""
"Add companies that operate under your franchise with specific business units."
msgstr "أضف الشركات التي تعمل تحت الامتياز الخاص بك مع وحدات أعمال محددة."

#: .\setup\templates\setup\dashboard.html:179
msgid "Add Service Centers"
msgstr "إضافة مراكز الخدمة"

#: .\setup\templates\setup\dashboard.html:182
msgid ""
"Create service centers with locations, contact information and service "
"capabilities."
msgstr "إنشاء مراكز خدمة مع مواقع ومعلومات اتصال وقدرات خدمة."

#: .\setup\templates\setup\dashboard.html:204
msgid "Go to Inventory"
msgstr "الذهاب إلى المخزون"

#: .\setup\templates\setup\dashboard.html:207
msgid "Start managing your inventory items, stock levels, and movements."
msgstr "ابدأ في إدارة عناصر المخزون ومستويات المخزون والحركات."

#: .\setup\templates\setup\dashboard.html:226
msgid "Recent Entries"
msgstr "الإدخالات الأخيرة"

#: .\setup\templates\setup\dashboard.html:334 .\warehouse\models.py:21
msgid "Location"
msgstr "الموقع"

#: .\setup\views.py:14
#, fuzzy
#| msgid "Organization Setup"
msgid "Organization Setup Dashboard"
msgstr "إعداد المؤسسة"

#: .\setup\views.py:174
#, python-brace-format
msgid "Service History for {0}"
msgstr ""

#: .\user_roles\admin.py:23 .\user_roles\admin.py:59
msgid "Module Access"
msgstr ""

#: .\user_roles\admin.py:30
msgid "Group Association"
msgstr ""

#: .\user_roles\admin.py:43 .\user_roles\models.py:194
#: .\work_orders\models.py:143
#: .\work_orders\templates\work_orders\work_order_list.html:10
#: .\work_orders\views.py:26
msgid "Work Orders"
msgstr ""

#: .\user_roles\admin.py:57
msgid "None"
msgstr ""

#: .\user_roles\admin.py:71
msgid "Role Scope"
msgstr ""

#: .\user_roles\admin.py:73
msgid "Set the scope based on the role type. Only one should be set."
msgstr ""

#: .\user_roles\admin.py:86 .\user_roles\admin.py:87 .\user_roles\admin.py:88
msgid "System administrators do not need a scope"
msgstr ""

#: .\user_roles\admin.py:90
msgid "Required for Franchise Administrators"
msgstr ""

#: .\user_roles\admin.py:91 .\user_roles\admin.py:92
msgid "Leave blank for Franchise Administrators"
msgstr ""

#: .\user_roles\admin.py:94 .\user_roles\admin.py:96
msgid "Leave blank for Company Administrators"
msgstr ""

#: .\user_roles\admin.py:95
msgid "Required for Company Administrators"
msgstr ""

#: .\user_roles\admin.py:98 .\user_roles\admin.py:99
msgid "Leave blank for Service Center roles"
msgstr ""

#: .\user_roles\admin.py:100
#, fuzzy
#| msgid "Add Service Centers"
msgid "Required for Service Center roles"
msgstr "إضافة مراكز الخدمة"

#: .\user_roles\admin.py:113
msgid "System-wide"
msgstr ""

#: .\user_roles\admin.py:115
msgid "Scope"
msgstr ""

#: .\user_roles\apps.py:8
msgid "User Roles & Permissions"
msgstr ""

#: .\user_roles\middleware.py:45
msgid "You don't have permission to perform this action."
msgstr ""

#: .\user_roles\middleware.py:46
msgid "You don't have permission to access this page."
msgstr ""

#: .\user_roles\middleware.py:54
msgid "You need a role to access this page."
msgstr ""

#: .\user_roles\middleware.py:55
msgid "You need to be assigned a role to access this page."
msgstr ""

#: .\user_roles\middleware.py:133 .\user_roles\middleware.py:142
msgid "You don't have permission to access this module"
msgstr ""

#: .\user_roles\middleware.py:135
#, python-brace-format
msgid "You don't have permission to access the {module} module"
msgstr ""

#: .\user_roles\middleware.py:144
#, python-brace-format
msgid "You need to be assigned a role to access the {module} module"
msgstr ""

#: .\user_roles\models.py:13
msgid "System Administrator"
msgstr ""

#: .\user_roles\models.py:14
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Administrator"
msgstr "الامتيازات"

#: .\user_roles\models.py:15
msgid "Company Administrator"
msgstr ""

#: .\user_roles\models.py:16
#, fuzzy
#| msgid "Service Centers"
msgid "Service Center Manager"
msgstr "مراكز الخدمة"

#: .\user_roles\models.py:17
#, fuzzy
#| msgid "Service Centers"
msgid "Service Advisor"
msgstr "مراكز الخدمة"

#: .\user_roles\models.py:19
#, fuzzy
#| msgid "Inventory Management System"
msgid "Inventory Manager"
msgstr "نظام ما بعد البيع" 

#: .\user_roles\models.py:20
msgid "Parts Clerk"
msgstr ""

#: .\user_roles\models.py:21
msgid "Accountant"
msgstr ""

#: .\user_roles\models.py:22
#, fuzzy
#| msgid "Actions"
msgid "Receptionist"
msgstr "الإجراءات"

#: .\user_roles\models.py:23
msgid "Customer Service"
msgstr ""

#: .\user_roles\models.py:24
msgid "Read Only User"
msgstr ""

#: .\user_roles\models.py:27
#, fuzzy
#| msgid "Name"
msgid "Role Name"
msgstr "الاسم"

#: .\user_roles\models.py:28
msgid "Role Code"
msgstr ""

#: .\user_roles\models.py:29
msgid "Role Type"
msgstr ""

#: .\user_roles\models.py:38
msgid "Permission Group"
msgstr ""

#: .\user_roles\models.py:42
msgid "Can Access Setup"
msgstr ""

#: .\user_roles\models.py:43
msgid "Can Access Work Orders"
msgstr ""

#: .\user_roles\models.py:44
#, fuzzy
#| msgid "Go to Inventory"
msgid "Can Access Inventory"
msgstr "الذهاب إلى المخزون"

#: .\user_roles\models.py:45
msgid "Can Access Warehouse"
msgstr ""

#: .\user_roles\models.py:46
msgid "Can Access Sales"
msgstr ""

#: .\user_roles\models.py:47
msgid "Can Access Purchases"
msgstr ""

#: .\user_roles\models.py:48
msgid "Can Access Reports"
msgstr ""

#: .\user_roles\models.py:49
msgid "Can Access Settings"
msgstr ""

#: .\user_roles\models.py:52 .\user_roles\models.py:84
#: .\user_roles\models.py:216
msgid "Role"
msgstr ""

#: .\user_roles\models.py:53
msgid "Roles"
msgstr ""

#: .\user_roles\models.py:78
#, fuzzy
#| msgid "Username"
msgid "User"
msgstr "اسم المستخدم"

#: .\user_roles\models.py:93
#, fuzzy
#| msgid "Franchises"
msgid "Franchise Scope"
msgstr "الامتيازات"

#: .\user_roles\models.py:100
#, fuzzy
#| msgid "Companies"
msgid "Company Scope"
msgstr "الشركات"

#: .\user_roles\models.py:107
#, fuzzy
#| msgid "Service Centers"
msgid "Service Center Scope"
msgstr "مراكز الخدمة"

#: .\user_roles\models.py:110
msgid "Is Primary Role"
msgstr ""

#: .\user_roles\models.py:116
msgid "User Role"
msgstr ""

#: .\user_roles\models.py:117
msgid "User Roles"
msgstr ""

#: .\user_roles\models.py:143
msgid "System administrators should not have a scope."
msgstr ""

#: .\user_roles\models.py:148
msgid "Franchise administrators must have a franchise scope."
msgstr ""

#: .\user_roles\models.py:153
msgid "Company administrators must have a company scope."
msgstr ""

#: .\user_roles\models.py:168
msgid "Service center roles must have a service center scope."
msgstr ""

#: .\user_roles\models.py:205
msgid "Add"
msgstr ""

#: .\user_roles\models.py:206
msgid "Change"
msgstr ""

#: .\user_roles\models.py:207
msgid "Delete"
msgstr ""

#: .\user_roles\models.py:208
msgid "Approve"
msgstr ""

#: .\user_roles\models.py:209
#, fuzzy
#| msgid "Reports"
msgid "Generate Reports"
msgstr "التقارير"

#: .\user_roles\models.py:218
msgid "Module"
msgstr ""

#: .\user_roles\models.py:219
#, fuzzy
#| msgid "Actions"
msgid "Action"
msgstr "الإجراءات"

#: .\user_roles\models.py:222
msgid "Module Permission"
msgstr ""

#: .\user_roles\models.py:223
msgid "Module Permissions"
msgstr ""

#: .\warehouse\models.py:14
msgid "Code"
msgstr ""

#: .\warehouse\models.py:22
#, fuzzy
#| msgid "Location"
msgid "Locations"
msgstr "الموقع"

#: .\warehouse\models.py:40
#, fuzzy
#| msgid "Location"
msgid "Item Location"
msgstr "الموقع"

#: .\warehouse\models.py:41
#, fuzzy
#| msgid "Location"
msgid "Item Locations"
msgstr "الموقع"

#: .\warehouse\models.py:55
msgid "In Transit"
msgstr ""

#: .\warehouse\models.py:63 .\warehouse\models.py:117
#, fuzzy
#| msgid "Location"
msgid "Source Location"
msgstr "الموقع"

#: .\warehouse\models.py:67 .\warehouse\models.py:121
msgid "Destination Location"
msgstr ""

#: .\warehouse\models.py:71 .\warehouse\models.py:123
msgid "Items Count"
msgstr ""

#: .\warehouse\models.py:71
msgid "Total number of items in this transfer"
msgstr ""

#: .\warehouse\models.py:76 .\warehouse\models.py:89
msgid "Transfer Order"
msgstr ""

#: .\warehouse\models.py:77
msgid "Transfer Orders"
msgstr ""

#: .\warehouse\models.py:101
msgid "Transfer Order Item"
msgstr ""

#: .\warehouse\models.py:102
msgid "Transfer Order Items"
msgstr ""

#: .\warehouse\models.py:131
msgid "Transfers"
msgstr ""

#: .\work_orders\admin.py:58
msgid "Customer/Service Information"
msgstr ""

#: .\work_orders\apps.py:8
msgid "Work Orders & Manufacturing"
msgstr ""

#: .\work_orders\models.py:18 .\work_orders\models.py:105
msgid "Work Order Type"
msgstr ""

#: .\work_orders\models.py:19
msgid "Work Order Types"
msgstr ""

#: .\work_orders\models.py:33
msgid "Finished Item"
msgstr ""

#: .\work_orders\models.py:35
msgid "Version"
msgstr ""

#: .\work_orders\models.py:43 .\work_orders\models.py:57
#: .\work_orders\models.py:112
msgid "Bill of Materials"
msgstr ""

#: .\work_orders\models.py:44 .\work_orders\views.py:95
msgid "Bills of Materials"
msgstr ""

#: .\work_orders\models.py:63
msgid "Component Item"
msgstr ""

#: .\work_orders\models.py:66 .\work_orders\models.py:191
msgid "Unit of Measure"
msgstr ""

#: .\work_orders\models.py:67
msgid "Is Optional"
msgstr ""

#: .\work_orders\models.py:68 .\work_orders\models.py:157
msgid "Sequence"
msgstr ""

#: .\work_orders\models.py:75
msgid "BOM Item"
msgstr ""

#: .\work_orders\models.py:76
msgid "BOM Items"
msgstr ""

#: .\work_orders\models.py:85
#: .\work_orders\templates\work_orders\work_order_list.html:99
msgid "Low"
msgstr ""

#: .\work_orders\models.py:86
#: .\work_orders\templates\work_orders\work_order_list.html:103
msgid "Medium"
msgstr ""

#: .\work_orders\models.py:87
#: .\work_orders\templates\work_orders\work_order_list.html:107
msgid "High"
msgstr ""

#: .\work_orders\models.py:88
#: .\work_orders\templates\work_orders\work_order_list.html:111
msgid "Critical"
msgstr ""

#: .\work_orders\models.py:93
#: .\work_orders\templates\work_orders\work_order_list.html:76
msgid "Planned"
msgstr ""

#: .\work_orders\models.py:95
#: .\work_orders\templates\work_orders\work_order_list.html:84
msgid "On Hold"
msgstr ""

#: .\work_orders\models.py:119
msgid "Planned Start Date"
msgstr ""

#: .\work_orders\models.py:120
msgid "Planned End Date"
msgstr ""

#: .\work_orders\models.py:121
msgid "Actual Start Date"
msgstr ""

#: .\work_orders\models.py:122
msgid "Actual End Date"
msgstr ""

#: .\work_orders\models.py:125
msgid "Customer Name"
msgstr ""

#: .\work_orders\models.py:126
msgid "Customer Phone"
msgstr ""

#: .\work_orders\models.py:127
msgid "Customer Email"
msgstr ""

#: .\work_orders\models.py:128
msgid "Item Serial #"
msgstr ""

#: .\work_orders\models.py:129
msgid "Under Warranty"
msgstr ""

#: .\work_orders\models.py:132
msgid "Estimated Cost"
msgstr ""

#: .\work_orders\models.py:133
msgid "Actual Cost"
msgstr ""

#: .\work_orders\models.py:142 .\work_orders\models.py:155
#: .\work_orders\models.py:182
msgid "Work Order"
msgstr ""

#: .\work_orders\models.py:158
msgid "Operation Name"
msgstr ""

#: .\work_orders\models.py:160
msgid "Duration (minutes)"
msgstr ""

#: .\work_orders\models.py:161
msgid "Is Completed"
msgstr ""

#: .\work_orders\models.py:162
msgid "Completed At"
msgstr ""

#: .\work_orders\models.py:169
msgid "Work Order Operation"
msgstr ""

#: .\work_orders\models.py:170
msgid "Work Order Operations"
msgstr ""

#: .\work_orders\models.py:192
msgid "Is Consumed"
msgstr ""

#: .\work_orders\models.py:199
msgid "Work Order Material"
msgstr ""

#: .\work_orders\models.py:200
msgid "Work Order Materials"
msgstr ""

#: .\work_orders\templates\work_orders\work_order_list.html:12
#: .\work_orders\views.py:57
msgid "Create Work Order"
msgstr ""

#: .\work_orders\templates\work_orders\work_order_list.html:19
msgid "All"
msgstr ""

#: .\work_orders\templates\work_orders\work_order_list.html:46
msgid "Planned Start"
msgstr ""

#: .\work_orders\templates\work_orders\work_order_list.html:123
msgid "Edit"
msgstr ""

#: .\work_orders\templates\work_orders\work_order_list.html:130
msgid "No work orders found."
msgstr ""

#: .\work_orders\templates\work_orders\work_order_list.html:144
msgid "Previous"
msgstr ""

#: .\work_orders\templates\work_orders\work_order_list.html:154
msgid "Next"
msgstr ""

#: .\work_orders\views.py:38
#, python-brace-format
msgid "Work Order #{0}"
msgstr ""

#: .\work_orders\views.py:61
msgid "Work order created successfully"
msgstr ""

#: .\work_orders\views.py:77
msgid "Update Work Order"
msgstr ""

#: .\work_orders\views.py:84
msgid "Work order updated successfully"
msgstr ""

#~ msgid "Go"
#~ msgstr "اذهب"

#~ msgid "Inventory System"
#~ msgstr "نظام المخزون"
