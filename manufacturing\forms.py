from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.contrib.auth.models import User
from django.db import models

from .models import (
    ProductionLine, BillOfMaterials, BOMComponent, ProductionOrder,
    ProductionOrderOperation, QualityCheck, MaterialConsumption, ProductionReport
)
from inventory.models import Item


class ProductionLineForm(forms.ModelForm):
    """Form for creating and updating production lines"""
    
    class Meta:
        model = ProductionLine
        fields = ['name', 'description', 'capacity_per_hour', 'location', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter production line name')
            }),
            'description': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter description')
            }),
            'capacity_per_hour': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01'
            }),
            'location': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter location')
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2'
            })
        }
        labels = {
            'name': _('Production Line Name'),
            'description': _('Description'),
            'capacity_per_hour': _('Capacity per Hour'),
            'location': _('Location'),
            'is_active': _('Active')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)


class BillOfMaterialsForm(forms.ModelForm):
    """Form for creating and updating Bills of Materials"""
    
    class Meta:
        model = BillOfMaterials
        fields = ['name', 'product', 'version', 'quantity_produced', 'production_time_minutes', 'is_active', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter BOM name')
            }),
            'product': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'version': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('e.g., 1.0, 2.1')
            }),
            'quantity_produced': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01'
            }),
            'production_time_minutes': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'min': '1'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter notes')
            })
        }
        labels = {
            'name': _('BOM Name'),
            'product': _('Product'),
            'version': _('Version'),
            'quantity_produced': _('Quantity Produced'),
            'production_time_minutes': _('Production Time (Minutes)'),
            'is_active': _('Active'),
            'notes': _('Notes')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Filter products by tenant
        if self.tenant_id:
            self.fields['product'].queryset = Item.objects.filter(
                tenant_id=self.tenant_id,
                is_active=True
            )
    
    def clean(self):
        cleaned_data = super().clean()
        product = cleaned_data.get('product')
        version = cleaned_data.get('version')
        
        if product and version and self.tenant_id:
            # Check for duplicate BOM version for this product
            existing_bom = BillOfMaterials.objects.filter(
                product=product,
                version=version,
                tenant_id=self.tenant_id
            )
            if self.instance.pk:
                existing_bom = existing_bom.exclude(pk=self.instance.pk)
            
            if existing_bom.exists():
                raise ValidationError({
                    'version': _('A BOM with this version already exists for this product.')
                })
        
        return cleaned_data


class BOMComponentForm(forms.ModelForm):
    """Form for adding components to BOMs"""
    
    class Meta:
        model = BOMComponent
        fields = ['material', 'quantity_required', 'unit_cost', 'notes']
        widgets = {
            'material': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'quantity_required': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01'
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 2,
                'placeholder': _('Enter notes')
            })
        }
        labels = {
            'material': _('Material'),
            'quantity_required': _('Quantity Required'),
            'unit_cost': _('Unit Cost'),
            'notes': _('Notes')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        self.bom = kwargs.pop('bom', None)
        super().__init__(*args, **kwargs)
        
        # Filter materials by tenant
        if self.tenant_id:
            self.fields['material'].queryset = Item.objects.filter(
                tenant_id=self.tenant_id,
                is_active=True
            )
    
    def clean(self):
        cleaned_data = super().clean()
        material = cleaned_data.get('material')
        bom = self.bom or self.instance.bom
        
        if material and bom:
            # Check if material already exists in this BOM
            existing_component = BOMComponent.objects.filter(
                bom=bom,
                material=material
            )
            if self.instance.pk:
                existing_component = existing_component.exclude(pk=self.instance.pk)
            
            if existing_component.exists():
                raise ValidationError({
                    'material': _('This material is already in the BOM.')
                })
        
        return cleaned_data


class ProductionOrderForm(forms.ModelForm):
    """Form for creating and updating production orders"""
    
    class Meta:
        model = ProductionOrder
        fields = [
            'order_number', 'bom', 'production_line', 'quantity_to_produce',
            'planned_start_date', 'planned_end_date', 'status', 'priority', 'notes'
        ]
        widgets = {
            'order_number': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter order number')
            }),
            'bom': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'production_line': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'quantity_to_produce': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01'
            }),
            'planned_start_date': forms.DateTimeInput(attrs={
                'type': 'datetime-local',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'planned_end_date': forms.DateTimeInput(attrs={
                'type': 'datetime-local',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'status': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'priority': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter notes')
            })
        }
        labels = {
            'order_number': _('Order Number'),
            'bom': _('Bill of Materials'),
            'production_line': _('Production Line'),
            'quantity_to_produce': _('Quantity to Produce'),
            'planned_start_date': _('Planned Start Date'),
            'planned_end_date': _('Planned End Date'),
            'status': _('Status'),
            'priority': _('Priority'),
            'notes': _('Notes')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Set default dates
        if not self.instance.pk:
            now = timezone.now()
            if not self.initial.get('planned_start_date'):
                self.initial['planned_start_date'] = now
            if not self.initial.get('planned_end_date'):
                self.initial['planned_end_date'] = now + timezone.timedelta(hours=8)
        
        # Filter by tenant
        if self.tenant_id:
            self.fields['bom'].queryset = BillOfMaterials.objects.filter(
                tenant_id=self.tenant_id,
                is_active=True
            )
            self.fields['production_line'].queryset = ProductionLine.objects.filter(
                tenant_id=self.tenant_id,
                is_active=True
            )
        
        # Generate default order number if creating new
        if not self.instance.pk and not self.initial.get('order_number'):
            self.initial['order_number'] = self.generate_order_number()
    
    def generate_order_number(self):
        """Generate a unique order number"""
        import uuid
        timestamp = timezone.now().strftime('%Y%m%d')
        short_uuid = str(uuid.uuid4())[:8].upper()
        return f"PO-{timestamp}-{short_uuid}"
    
    def clean_order_number(self):
        order_number = self.cleaned_data.get('order_number')
        if order_number:
            # Check for duplicate order numbers within the same tenant
            queryset = ProductionOrder.objects.filter(order_number=order_number, tenant_id=self.tenant_id)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise ValidationError(_('A production order with this number already exists.'))
        return order_number
    
    def clean(self):
        cleaned_data = super().clean()
        planned_start_date = cleaned_data.get('planned_start_date')
        planned_end_date = cleaned_data.get('planned_end_date')
        
        if planned_start_date and planned_end_date:
            if planned_end_date <= planned_start_date:
                raise ValidationError({
                    'planned_end_date': _('Planned end date must be after start date.')
                })
        
        return cleaned_data


class ManufacturingReportForm(forms.Form):
    """Form for generating manufacturing reports"""
    
    REPORT_TYPE_CHOICES = [
        ('production_summary', _('Production Summary')),
        ('efficiency_report', _('Efficiency Report')),
        ('quality_report', _('Quality Report')),
        ('material_consumption', _('Material Consumption')),
        ('bom_analysis', _('BOM Analysis')),
    ]
    
    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Report Type')
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('From Date')
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('To Date')
    )
    
    production_line = forms.ModelChoiceField(
        queryset=ProductionLine.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Production Line'),
        empty_label=_('All Production Lines')
    )

    def __init__(self, *args, **kwargs):
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Set default date range (last 30 days)
        if not self.initial.get('date_to'):
            self.initial['date_to'] = timezone.now().date()
        if not self.initial.get('date_from'):
            self.initial['date_from'] = timezone.now().date() - timezone.timedelta(days=30)
        
        # Filter production lines by tenant
        if tenant_id:
            self.fields['production_line'].queryset = ProductionLine.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('name')
    
    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_('From date cannot be after to date.'))
        
        return cleaned_data
