from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet
from inventory.models import Item


class ProductionLine(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Production line model for manufacturing
    """
    name = models.CharField(_("Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    capacity_per_hour = models.DecimalField(_("Capacity per Hour"), max_digits=10, decimal_places=2, default=1)
    is_active = models.BooleanField(_("Active"), default=True)
    location = models.CharField(_("Location"), max_length=255, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Production Line")
        verbose_name_plural = _("Production Lines")
        
    def __str__(self):
        return self.name


class BillOfMaterials(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Bill of Materials model - recipe/formula for manufacturing products
    """
    name = models.CharField(_("BOM Name"), max_length=255)
    product = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name='boms',
        verbose_name=_("Product")
    )
    version = models.CharField(_("Version"), max_length=50, default="1.0")
    quantity_produced = models.DecimalField(_("Quantity Produced"), max_digits=10, decimal_places=2, default=1)
    production_time_minutes = models.IntegerField(_("Production Time (Minutes)"), default=60)
    is_active = models.BooleanField(_("Active"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Bill of Materials")
        verbose_name_plural = _("Bills of Materials")
        unique_together = [['product', 'version', 'tenant_id']]
        
    def __str__(self):
        return f"{self.name} - {self.product.name} v{self.version}"
    
    @property
    def total_material_cost(self):
        """Calculate total cost of materials"""
        return sum(component.total_cost for component in self.components.all())
    
    @property
    def estimated_unit_cost(self):
        """Calculate estimated unit cost including materials"""
        if self.quantity_produced > 0:
            return self.total_material_cost / self.quantity_produced
        return 0


class BOMComponent(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Components/materials required for a BOM
    """
    bom = models.ForeignKey(
        BillOfMaterials,
        on_delete=models.CASCADE,
        related_name='components',
        verbose_name=_("Bill of Materials")
    )
    material = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name='bom_components',
        verbose_name=_("Material")
    )
    quantity_required = models.DecimalField(_("Quantity Required"), max_digits=10, decimal_places=2)
    unit_cost = models.DecimalField(_("Unit Cost"), max_digits=10, decimal_places=2, default=0)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("BOM Component")
        verbose_name_plural = _("BOM Components")
        unique_together = [['bom', 'material']]
        
    def __str__(self):
        return f"{self.quantity_required} x {self.material.name}"
    
    @property
    def total_cost(self):
        """Calculate total cost for this component"""
        return self.quantity_required * self.unit_cost


class ProductionOrder(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Production order model for manufacturing jobs
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('planned', _('Planned')),
        ('released', _('Released')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    )
    
    PRIORITY_CHOICES = (
        ('low', _('Low')),
        ('normal', _('Normal')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    )
    
    order_number = models.CharField(_("Order Number"), max_length=50, unique=True)
    bom = models.ForeignKey(
        BillOfMaterials,
        on_delete=models.PROTECT,
        related_name='production_orders',
        verbose_name=_("Bill of Materials")
    )
    production_line = models.ForeignKey(
        ProductionLine,
        on_delete=models.PROTECT,
        related_name='production_orders',
        verbose_name=_("Production Line"),
        null=True,
        blank=True
    )
    quantity_to_produce = models.DecimalField(_("Quantity to Produce"), max_digits=10, decimal_places=2)
    quantity_produced = models.DecimalField(_("Quantity Produced"), max_digits=10, decimal_places=2, default=0)
    planned_start_date = models.DateTimeField(_("Planned Start Date"))
    planned_end_date = models.DateTimeField(_("Planned End Date"))
    actual_start_date = models.DateTimeField(_("Actual Start Date"), null=True, blank=True)
    actual_end_date = models.DateTimeField(_("Actual End Date"), null=True, blank=True)
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='draft')
    priority = models.CharField(_("Priority"), max_length=20, choices=PRIORITY_CHOICES, default='normal')
    notes = models.TextField(_("Notes"), blank=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_production_orders',
        verbose_name=_("Created By")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Production Order")
        verbose_name_plural = _("Production Orders")
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.order_number} - {self.bom.product.name}"
    
    @property
    def completion_percentage(self):
        """Calculate completion percentage"""
        if self.quantity_to_produce > 0:
            return min((self.quantity_produced / self.quantity_to_produce) * 100, 100)
        return 0
    
    @property
    def is_overdue(self):
        """Check if production is overdue"""
        from django.utils import timezone
        if self.status not in ['completed', 'cancelled']:
            return timezone.now() > self.planned_end_date
        return False


class ProductionOrderOperation(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Operations/steps within a production order
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('skipped', _('Skipped')),
    )
    
    production_order = models.ForeignKey(
        ProductionOrder,
        on_delete=models.CASCADE,
        related_name='operations',
        verbose_name=_("Production Order")
    )
    sequence = models.IntegerField(_("Sequence"))
    name = models.CharField(_("Operation Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    estimated_duration_minutes = models.IntegerField(_("Estimated Duration (Minutes)"), default=30)
    actual_duration_minutes = models.IntegerField(_("Actual Duration (Minutes)"), null=True, blank=True)
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='pending')
    start_time = models.DateTimeField(_("Start Time"), null=True, blank=True)
    end_time = models.DateTimeField(_("End Time"), null=True, blank=True)
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        related_name='assigned_operations',
        verbose_name=_("Assigned To"),
        null=True,
        blank=True
    )
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Production Operation")
        verbose_name_plural = _("Production Operations")
        ordering = ['sequence']
        unique_together = [['production_order', 'sequence']]
        
    def __str__(self):
        return f"{self.production_order.order_number} - {self.sequence}. {self.name}"


class QualityCheck(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Quality control checks during production
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('passed', _('Passed')),
        ('failed', _('Failed')),
        ('rework', _('Rework Required')),
    )
    
    production_order = models.ForeignKey(
        ProductionOrder,
        on_delete=models.CASCADE,
        related_name='quality_checks',
        verbose_name=_("Production Order")
    )
    check_name = models.CharField(_("Check Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    check_point = models.CharField(_("Check Point"), max_length=100, help_text=_("e.g., 'Start', 'Middle', 'End'"))
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='pending')
    checked_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='quality_checks',
        verbose_name=_("Checked By"),
        null=True,
        blank=True
    )
    check_date = models.DateTimeField(_("Check Date"), null=True, blank=True)
    result_notes = models.TextField(_("Result Notes"), blank=True)
    corrective_action = models.TextField(_("Corrective Action"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Quality Check")
        verbose_name_plural = _("Quality Checks")
        
    def __str__(self):
        return f"{self.check_name} - {self.production_order.order_number}"


class MaterialConsumption(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Track actual material consumption during production
    """
    production_order = models.ForeignKey(
        ProductionOrder,
        on_delete=models.CASCADE,
        related_name='material_consumption',
        verbose_name=_("Production Order")
    )
    material = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name='production_consumption',
        verbose_name=_("Material")
    )
    planned_quantity = models.DecimalField(_("Planned Quantity"), max_digits=10, decimal_places=2)
    consumed_quantity = models.DecimalField(_("Consumed Quantity"), max_digits=10, decimal_places=2)
    consumption_date = models.DateTimeField(_("Consumption Date"))
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Material Consumption")
        verbose_name_plural = _("Material Consumption")
        
    def __str__(self):
        return f"{self.consumed_quantity} x {self.material.name}"
    
    @property
    def variance(self):
        """Calculate variance between planned and actual consumption"""
        return self.consumed_quantity - self.planned_quantity
    
    @property
    def variance_percentage(self):
        """Calculate variance as percentage"""
        if self.planned_quantity > 0:
            return (self.variance / self.planned_quantity) * 100
        return 0


class ProductionReport(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Daily/shift production reports
    """
    report_date = models.DateField(_("Report Date"))
    shift = models.CharField(_("Shift"), max_length=50, default="Day")
    production_line = models.ForeignKey(
        ProductionLine,
        on_delete=models.CASCADE,
        related_name='production_reports',
        verbose_name=_("Production Line")
    )
    planned_production = models.DecimalField(_("Planned Production"), max_digits=10, decimal_places=2, default=0)
    actual_production = models.DecimalField(_("Actual Production"), max_digits=10, decimal_places=2, default=0)
    efficiency_percentage = models.DecimalField(_("Efficiency %"), max_digits=5, decimal_places=2, default=0)
    downtime_minutes = models.IntegerField(_("Downtime (Minutes)"), default=0)
    downtime_reason = models.TextField(_("Downtime Reason"), blank=True)
    quality_issues = models.TextField(_("Quality Issues"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='production_reports',
        verbose_name=_("Created By")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Production Report")
        verbose_name_plural = _("Production Reports")
        unique_together = [['report_date', 'shift', 'production_line', 'tenant_id']]
        ordering = ['-report_date', 'shift']
        
    def __str__(self):
        return f"{self.production_line.name} - {self.report_date} ({self.shift})"
