from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.db.models import Q, Sum, Count, Avg, F
from django.utils import timezone
from django.core.paginator import Paginator
import datetime

from .models import (
    ProductionLine, BillOfMaterials, BOMComponent, ProductionOrder,
    ProductionOrderOperation, QualityCheck, MaterialConsumption, ProductionReport
)
from .forms import (
    ProductionLineForm, BillOfMaterialsForm, BOMComponentForm, ProductionOrderForm,
    ManufacturingReportForm
)
from inventory.models import Item


class ManufacturingDashboardView(LoginRequiredMixin, ListView):
    """Dashboard view for manufacturing module"""
    model = ProductionOrder
    template_name = 'manufacturing/dashboard.html'
    context_object_name = 'recent_orders'
    paginate_by = 10
    
    def get_queryset(self):
        """Get recent production orders"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ProductionOrder.objects.filter(tenant_id=tenant_id).order_by('-created_at')[:10]
        return ProductionOrder.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            # Calculate comprehensive statistics
            now = timezone.now()
            today = now.date()
            this_month = now.replace(day=1)
            
            # Basic counts
            context['total_production_lines'] = ProductionLine.objects.filter(
                tenant_id=tenant_id, 
                is_active=True
            ).count()
            context['total_boms'] = BillOfMaterials.objects.filter(
                tenant_id=tenant_id, 
                is_active=True
            ).count()
            
            # Production order statistics
            orders = ProductionOrder.objects.filter(tenant_id=tenant_id)
            context['total_orders'] = orders.count()
            context['draft_orders'] = orders.filter(status='draft').count()
            context['active_orders'] = orders.filter(status__in=['planned', 'released', 'in_progress']).count()
            context['completed_orders'] = orders.filter(status='completed').count()
            context['overdue_orders'] = orders.filter(
                planned_end_date__lt=now,
                status__in=['planned', 'released', 'in_progress']
            ).count()
            
            # Monthly production statistics
            this_month_orders = orders.filter(created_at__gte=this_month)
            context['this_month_orders'] = this_month_orders.count()
            context['this_month_completed'] = this_month_orders.filter(status='completed').count()
            
            # Today's production
            today_orders = orders.filter(
                planned_start_date__date=today,
                status__in=['planned', 'released', 'in_progress']
            )
            context['today_production'] = today_orders.count()
            
            # Production efficiency (average)
            completed_orders = orders.filter(status='completed')
            context['avg_efficiency'] = completed_orders.aggregate(
                avg_efficiency=Avg(
                    F('quantity_produced') * 100.0 / F('quantity_to_produce')
                )
            )['avg_efficiency'] or 0
            
            # Top production lines by output
            context['top_production_lines'] = ProductionLine.objects.filter(
                tenant_id=tenant_id,
                production_orders__isnull=False
            ).annotate(
                total_orders=Count('production_orders'),
                completed_orders=Count('production_orders', filter=Q(production_orders__status='completed'))
            ).order_by('-total_orders')[:5]
            
            # Recent quality checks
            context['recent_quality_checks'] = QualityCheck.objects.filter(
                tenant_id=tenant_id
            ).order_by('-check_date')[:5]
            
            # Material consumption variance
            consumption_data = MaterialConsumption.objects.filter(
                tenant_id=tenant_id,
                consumption_date__gte=this_month
            ).aggregate(
                total_planned=Sum('planned_quantity'),
                total_consumed=Sum('consumed_quantity')
            )
            
            planned = consumption_data['total_planned'] or 0
            consumed = consumption_data['total_consumed'] or 0
            context['material_variance'] = ((consumed - planned) / planned * 100) if planned > 0 else 0
            
        context['missing_tenant'] = tenant_id is None
        return context


# Production Line Views

class ProductionLineListView(LoginRequiredMixin, ListView):
    """View to list all production lines"""
    model = ProductionLine
    template_name = 'manufacturing/production_line_list.html'
    context_object_name = 'production_lines'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant and search"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return ProductionLine.objects.none()
            
        queryset = ProductionLine.objects.filter(tenant_id=tenant_id)
        
        # Apply search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(location__icontains=search)
            )
        
        # Filter by active status
        is_active = self.request.GET.get('is_active')
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        elif is_active == 'false':
            queryset = queryset.filter(is_active=False)
            
        return queryset.order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['current_search'] = self.request.GET.get('search', '')
        context['current_is_active'] = self.request.GET.get('is_active', '')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class ProductionLineDetailView(LoginRequiredMixin, DetailView):
    """View to display production line details"""
    model = ProductionLine
    template_name = 'manufacturing/production_line_detail.html'
    context_object_name = 'production_line'
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ProductionLine.objects.filter(tenant_id=tenant_id)
        return ProductionLine.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get production orders for this line
        orders = ProductionOrder.objects.filter(production_line=self.object).order_by('-created_at')
        
        paginator = Paginator(orders, 10)
        page_number = self.request.GET.get('page')
        context['production_orders'] = paginator.get_page(page_number)
        
        # Calculate statistics
        context['total_orders'] = orders.count()
        context['completed_orders'] = orders.filter(status='completed').count()
        context['active_orders'] = orders.filter(status__in=['planned', 'released', 'in_progress']).count()
        
        # Recent activity
        context['recent_orders'] = orders[:5]
        
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class ProductionLineCreateView(LoginRequiredMixin, CreateView):
    """View to create a new production line"""
    model = ProductionLine
    form_class = ProductionLineForm
    template_name = 'manufacturing/production_line_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('manufacturing:production_line_list')
            
        form.instance.tenant_id = tenant_id
        response = super().form_valid(form)
        messages.success(self.request, _('Production line created successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:production_line_detail', kwargs={'pk': self.object.pk})


class ProductionLineUpdateView(LoginRequiredMixin, UpdateView):
    """View to update an existing production line"""
    model = ProductionLine
    form_class = ProductionLineForm
    template_name = 'manufacturing/production_line_form.html'
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ProductionLine.objects.filter(tenant_id=tenant_id)
        return ProductionLine.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Production line updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:production_line_detail', kwargs={'pk': self.object.pk})


class ProductionLineDeleteView(LoginRequiredMixin, DeleteView):
    """View to delete a production line"""
    model = ProductionLine
    template_name = 'manufacturing/production_line_confirm_delete.html'
    success_url = reverse_lazy('manufacturing:production_line_list')
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ProductionLine.objects.filter(tenant_id=tenant_id)
        return ProductionLine.objects.none()
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Production line deleted successfully.'))
        return super().delete(request, *args, **kwargs)


# Bill of Materials Views

class BillOfMaterialsListView(LoginRequiredMixin, ListView):
    """View to list all BOMs"""
    model = BillOfMaterials
    template_name = 'manufacturing/bom_list.html'
    context_object_name = 'boms'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant and search"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return BillOfMaterials.objects.none()
            
        queryset = BillOfMaterials.objects.filter(tenant_id=tenant_id).select_related('product')
        
        # Apply search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(product__name__icontains=search) |
                Q(version__icontains=search)
            )
        
        # Filter by active status
        is_active = self.request.GET.get('is_active')
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        elif is_active == 'false':
            queryset = queryset.filter(is_active=False)
            
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['current_search'] = self.request.GET.get('search', '')
        context['current_is_active'] = self.request.GET.get('is_active', '')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class BillOfMaterialsDetailView(LoginRequiredMixin, DetailView):
    """View to display BOM details"""
    model = BillOfMaterials
    template_name = 'manufacturing/bom_detail.html'
    context_object_name = 'bom'
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return BillOfMaterials.objects.filter(tenant_id=tenant_id).select_related('product')
        return BillOfMaterials.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['components'] = self.object.components.select_related('material').all()
        context['production_orders'] = self.object.production_orders.all().order_by('-created_at')[:10]
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class BillOfMaterialsCreateView(LoginRequiredMixin, CreateView):
    """View to create a new BOM"""
    model = BillOfMaterials
    form_class = BillOfMaterialsForm
    template_name = 'manufacturing/bom_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('manufacturing:bom_list')
            
        form.instance.tenant_id = tenant_id
        response = super().form_valid(form)
        messages.success(self.request, _('Bill of Materials created successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:bom_detail', kwargs={'pk': self.object.pk})


class BillOfMaterialsUpdateView(LoginRequiredMixin, UpdateView):
    """View to update an existing BOM"""
    model = BillOfMaterials
    form_class = BillOfMaterialsForm
    template_name = 'manufacturing/bom_form.html'
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return BillOfMaterials.objects.filter(tenant_id=tenant_id)
        return BillOfMaterials.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Bill of Materials updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:bom_detail', kwargs={'pk': self.object.pk})


class BillOfMaterialsDeleteView(LoginRequiredMixin, DeleteView):
    """View to delete a BOM"""
    model = BillOfMaterials
    template_name = 'manufacturing/bom_confirm_delete.html'
    success_url = reverse_lazy('manufacturing:bom_list')
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return BillOfMaterials.objects.filter(tenant_id=tenant_id)
        return BillOfMaterials.objects.none()
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Bill of Materials deleted successfully.'))
        return super().delete(request, *args, **kwargs)


# BOM Component Views

class BOMComponentCreateView(LoginRequiredMixin, CreateView):
    """View to add components to BOMs"""
    model = BOMComponent
    form_class = BOMComponentForm
    template_name = 'manufacturing/bom_component_form.html'
    
    def get_bom(self):
        """Get the BOM"""
        return get_object_or_404(
            BillOfMaterials,
            pk=self.kwargs['bom_pk'],
            tenant_id=getattr(self.request, 'tenant_id', None)
        )
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        kwargs['bom'] = self.get_bom()
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['bom'] = self.get_bom()
        context['title'] = _('Add Component to BOM')
        return context
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('manufacturing:bom_list')
            
        form.instance.tenant_id = tenant_id
        form.instance.bom = self.get_bom()
        response = super().form_valid(form)
        messages.success(self.request, _('Component added to BOM successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:bom_detail', kwargs={'pk': self.get_bom().pk})


class BOMComponentUpdateView(LoginRequiredMixin, UpdateView):
    """View to update BOM components"""
    model = BOMComponent
    form_class = BOMComponentForm
    template_name = 'manufacturing/bom_component_form.html'
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return BOMComponent.objects.filter(tenant_id=tenant_id)
        return BOMComponent.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['bom'] = self.object.bom
        context['title'] = _('Update BOM Component')
        return context
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('BOM component updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:bom_detail', kwargs={'pk': self.object.bom.pk})


class BOMComponentDeleteView(LoginRequiredMixin, DeleteView):
    """View to delete BOM components"""
    model = BOMComponent
    template_name = 'manufacturing/bom_component_confirm_delete.html'
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return BOMComponent.objects.filter(tenant_id=tenant_id)
        return BOMComponent.objects.none()
    
    def delete(self, request, *args, **kwargs):
        bom = self.get_object().bom
        messages.success(request, _('BOM component deleted successfully.'))
        response = super().delete(request, *args, **kwargs)
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:bom_detail', kwargs={'pk': self.get_object().bom.pk})


# Production Order Views

class ProductionOrderListView(LoginRequiredMixin, ListView):
    """View to list all production orders"""
    model = ProductionOrder
    template_name = 'manufacturing/production_order_list.html'
    context_object_name = 'production_orders'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant and search"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return ProductionOrder.objects.none()
            
        queryset = ProductionOrder.objects.filter(tenant_id=tenant_id).select_related('bom', 'production_line')
        
        # Apply search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(order_number__icontains=search) |
                Q(bom__name__icontains=search) |
                Q(bom__product__name__icontains=search)
            )
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Filter by priority
        priority = self.request.GET.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)
        
        # Date range filter
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(planned_start_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(planned_start_date__lte=date_to)
            
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = ProductionOrder.STATUS_CHOICES
        context['priority_choices'] = ProductionOrder.PRIORITY_CHOICES
        context['current_search'] = self.request.GET.get('search', '')
        context['current_status'] = self.request.GET.get('status', '')
        context['current_priority'] = self.request.GET.get('priority', '')
        context['current_date_from'] = self.request.GET.get('date_from', '')
        context['current_date_to'] = self.request.GET.get('date_to', '')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class ProductionOrderDetailView(LoginRequiredMixin, DetailView):
    """View to display production order details"""
    model = ProductionOrder
    template_name = 'manufacturing/production_order_detail.html'
    context_object_name = 'production_order'
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ProductionOrder.objects.filter(tenant_id=tenant_id).select_related('bom', 'production_line')
        return ProductionOrder.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['operations'] = self.object.operations.all().order_by('sequence')
        context['quality_checks'] = self.object.quality_checks.all().order_by('-check_date')
        context['material_consumption'] = self.object.material_consumption.all()
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class ProductionOrderCreateView(LoginRequiredMixin, CreateView):
    """View to create a new production order"""
    model = ProductionOrder
    form_class = ProductionOrderForm
    template_name = 'manufacturing/production_order_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('manufacturing:production_order_list')
            
        form.instance.tenant_id = tenant_id
        form.instance.created_by = self.request.user
        response = super().form_valid(form)
        messages.success(self.request, _('Production order created successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:production_order_detail', kwargs={'pk': self.object.pk})


class ProductionOrderUpdateView(LoginRequiredMixin, UpdateView):
    """View to update an existing production order"""
    model = ProductionOrder
    form_class = ProductionOrderForm
    template_name = 'manufacturing/production_order_form.html'
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ProductionOrder.objects.filter(tenant_id=tenant_id)
        return ProductionOrder.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Production order updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('manufacturing:production_order_detail', kwargs={'pk': self.object.pk})


class ProductionOrderDeleteView(LoginRequiredMixin, DeleteView):
    """View to delete a production order"""
    model = ProductionOrder
    template_name = 'manufacturing/production_order_confirm_delete.html'
    success_url = reverse_lazy('manufacturing:production_order_list')
    
    def get_queryset(self):
        """Filter by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ProductionOrder.objects.filter(tenant_id=tenant_id)
        return ProductionOrder.objects.none()
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Production order deleted successfully.'))
        return super().delete(request, *args, **kwargs)


# Manufacturing Report Views

class ManufacturingReportView(LoginRequiredMixin, ListView):
    """View for manufacturing reports"""
    template_name = 'manufacturing/manufacturing_reports.html'
    context_object_name = 'results'
    paginate_by = 50
    
    def get_queryset(self):
        """Generate report data based on form parameters"""
        return []  # Will be populated based on form data
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        form = ManufacturingReportForm(
            data=self.request.GET or None,
            tenant_id=tenant_id
        )
        context['form'] = form
        
        if form.is_valid() and tenant_id:
            report_type = form.cleaned_data['report_type']
            date_from = form.cleaned_data.get('date_from')
            date_to = form.cleaned_data.get('date_to')
            production_line = form.cleaned_data.get('production_line')
            
            # Generate report based on type
            if report_type == 'production_summary':
                context['results'] = self.get_production_summary(tenant_id, date_from, date_to, production_line)
            elif report_type == 'efficiency_report':
                context['results'] = self.get_efficiency_report(tenant_id, date_from, date_to, production_line)
            elif report_type == 'quality_report':
                context['results'] = self.get_quality_report(tenant_id, date_from, date_to, production_line)
            elif report_type == 'material_consumption':
                context['results'] = self.get_material_consumption_report(tenant_id, date_from, date_to)
            elif report_type == 'bom_analysis':
                context['results'] = self.get_bom_analysis(tenant_id)
            
            context['report_type'] = report_type
            context['report_generated'] = True
        
        context['missing_tenant'] = tenant_id is None
        return context
    
    def get_production_summary(self, tenant_id, date_from, date_to, production_line):
        """Generate production summary report"""
        queryset = ProductionOrder.objects.filter(tenant_id=tenant_id)
        
        if date_from:
            queryset = queryset.filter(planned_start_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(planned_start_date__lte=date_to)
        if production_line:
            queryset = queryset.filter(production_line=production_line)
        
        return queryset.select_related('bom', 'production_line').order_by('-created_at')
    
    def get_efficiency_report(self, tenant_id, date_from, date_to, production_line):
        """Generate efficiency report"""
        queryset = ProductionOrder.objects.filter(
            tenant_id=tenant_id,
            status='completed'
        )
        
        if date_from:
            queryset = queryset.filter(actual_end_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(actual_end_date__lte=date_to)
        if production_line:
            queryset = queryset.filter(production_line=production_line)
        
        # Add efficiency calculation
        queryset = queryset.annotate(
            efficiency=F('quantity_produced') * 100.0 / F('quantity_to_produce')
        )
        
        return queryset.select_related('bom', 'production_line').order_by('-efficiency')
    
    def get_quality_report(self, tenant_id, date_from, date_to, production_line):
        """Generate quality report"""
        queryset = QualityCheck.objects.filter(tenant_id=tenant_id)
        
        if date_from:
            queryset = queryset.filter(check_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(check_date__lte=date_to)
        if production_line:
            queryset = queryset.filter(production_order__production_line=production_line)
        
        return queryset.select_related('production_order', 'checked_by').order_by('-check_date')
    
    def get_material_consumption_report(self, tenant_id, date_from, date_to):
        """Generate material consumption report"""
        queryset = MaterialConsumption.objects.filter(tenant_id=tenant_id)
        
        if date_from:
            queryset = queryset.filter(consumption_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(consumption_date__lte=date_to)
        
        # Add variance calculation
        queryset = queryset.annotate(
            variance=F('consumed_quantity') - F('planned_quantity'),
            variance_percent=F('variance') * 100.0 / F('planned_quantity')
        )
        
        return queryset.select_related('material', 'production_order').order_by('-consumption_date')
    
    def get_bom_analysis(self, tenant_id):
        """Generate BOM analysis report"""
        queryset = BillOfMaterials.objects.filter(
            tenant_id=tenant_id,
            is_active=True
        )
        
        # Add production order count and total cost
        queryset = queryset.annotate(
            production_count=Count('production_orders'),
            total_material_cost=Sum('components__quantity_required') * Sum('components__unit_cost')
        )
        
        return queryset.select_related('product').order_by('-production_count')


# API Views

def api_search_items(request):
    """API endpoint to search items for BOMs"""
    tenant_id = getattr(request, 'tenant_id', None)
    if not tenant_id:
        return JsonResponse({'error': 'Tenant ID missing'}, status=400)
    
    query = request.GET.get('q', '')
    items = Item.objects.filter(
        tenant_id=tenant_id,
        is_active=True,
        name__icontains=query
    )[:10]
    
    results = [{
        'id': str(item.id),
        'name': item.name,
        'sku': item.sku,
        'unit_price': float(item.unit_price),
        'current_stock': float(item.quantity)
    } for item in items]
    
    return JsonResponse({'results': results})


def api_get_bom_components(request, bom_id):
    """API endpoint to get BOM components"""
    tenant_id = getattr(request, 'tenant_id', None)
    if not tenant_id:
        return JsonResponse({'error': 'Tenant ID missing'}, status=400)
    
    try:
        bom = BillOfMaterials.objects.get(id=bom_id, tenant_id=tenant_id)
        components = [{
            'id': str(component.id),
            'material_name': component.material.name,
            'quantity_required': float(component.quantity_required),
            'unit_cost': float(component.unit_cost),
            'total_cost': float(component.total_cost)
        } for component in bom.components.all()]
        
        return JsonResponse({
            'bom_id': str(bom.id),
            'components': components,
            'total_cost': float(bom.total_material_cost)
        })
    except BillOfMaterials.DoesNotExist:
        return JsonResponse({'error': 'BOM not found'}, status=404)


def api_update_production_status(request, order_id):
    """API endpoint to update production order status"""
    tenant_id = getattr(request, 'tenant_id', None)
    if not tenant_id:
        return JsonResponse({'error': 'Tenant ID missing'}, status=400)
    
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST allowed'}, status=405)
    
    try:
        order = ProductionOrder.objects.get(id=order_id, tenant_id=tenant_id)
        new_status = request.POST.get('status')
        
        if new_status in dict(ProductionOrder.STATUS_CHOICES):
            order.status = new_status
            
            # Update timestamps based on status
            now = timezone.now()
            if new_status == 'in_progress' and not order.actual_start_date:
                order.actual_start_date = now
            elif new_status == 'completed' and not order.actual_end_date:
                order.actual_end_date = now
            
            order.save()
            
            return JsonResponse({
                'success': True,
                'order_id': str(order.id),
                'new_status': new_status
            })
        else:
            return JsonResponse({'error': 'Invalid status'}, status=400)
            
    except ProductionOrder.DoesNotExist:
        return JsonResponse({'error': 'Production order not found'}, status=404)
