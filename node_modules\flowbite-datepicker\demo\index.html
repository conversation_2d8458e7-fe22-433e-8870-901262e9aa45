<!DOCTYPE html>
<html class="dark">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Vanilla JS Datepicker Demo</title>
    <link rel="stylesheet" href="./../dist/css/datepicker.min.css" />
    <style type="text/css">
      pre {
        background-color: #f5f5f5;
        padding: 1rem;
        overflow-x: auto;
      }
      .section {
        padding: 1.5rem;
      }
      .field {
        margin-bottom: 1em;
      }
      .has-addons {
        display: flex;
      }
      .has-addons .control a {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
      }
      .label {
        font-weight: 600;
      }

      aside {
        position: fixed;
        top: 0;
        bottom: 0;
        right: -300px;
        width: 300px;
        overflow: auto;
        background-color: #fff;
        box-shadow: inset 1px 1px rgba(0, 0, 0, 10%);
        transition: right 0.3s;
      }
      .open aside {
        right: 0;
      }
      aside hr {
        margin-top: 0.5rem;
      }
      .code-wrap {
        position: relative;
      }
      .code-wrap pre:not(.is-active) {
        height: 0;
        overflow: hidden;
        opacity: 0.5;
      }
      .code-wrap .collapse-button {
        position: absolute;
        top: 0;
        right: 0;
        left: auto;
        cursor: pointer;
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
      }
      label.checkbox {
        display: inline-block;
      }
      .help {
        margin: 0;
      }
      .help.is-danger {
        color: #f14668;
      }

      .toggle-btn {
        position: fixed;
        top: 0.75rem;
        right: 0.75rem;
        width: 1.5rem;
        background-color: #fff;
        line-height: 1.5rem;
        border: 1px solid rgba(0, 0, 0, 10%);
        border-radius: 2px;
        box-shadow: 1px 1px rgba(0, 0, 0, 10%);
        cursor: pointer;
      }
      .toggle-btn::before {
        content: '\25c0';
        padding-left: 0.25rem;
      }
      .open .toggle-btn::before {
        content: '\25b6';
      }

      .has-background-info {
        background-color: cyan;
      }
      .has-text-success {
        color: green;
      }

      @media (min-width: 481px) {
        main {
          margin-right: 38.1966%;
        }
        aside {
          right: 0;
          width: 38.1966%;
        }
        .toggle-btn {
          display: none;
        }
      }
    </style>
  </head>
  <body class="dark:bg-gray-900">
    <main>
      <section class="container section is-fluid">
        <div class="columns">
          <div class="column">
            <p>Vanilla JS Datepicker</p>
            <h1 class="title">Demo</h1>
            <div id="sandbox"></div>
          </div>
        </div>
        <div class="columns">
          <div class="column">
            <p><small>Style: <a href="index.html">Bulma</a> | <a href="bs4.html">Bootstrap</a> | <a href="foundation.html">Foundation</a> | <em>Plain CSS</em></small></p>
            <p><small>* This page uses <a href="https://wikiki.github.io/elements/tooltip/" target="_blank">bulma-tooltip</a> for tooltips.</small></p>
          </div>
        </div>
      </section>
    </main>

    <aside>
      <section class="section">
        <div class="tile is-parent is-vertical">
          <div class="tile is-child">
            <h4 class="subtitle">Type</h4>
            <form id="types">
              <div class="field">
                <div class="control">
                  <label class="radio">
                    <input type="radio" name="type" value="input" checked>
                    Input
                  </label>
                  <label class="radio">
                    <input type="radio" name="type" value="inline">
                    Inline
                  </label>
                  <label class="radio">
                    <input type="radio" name="type" value="range">
                    Range
                  </label>
                </div>
              </div>
            </form>
          </div>
          <hr>
          <div class="tile is-child">
            <h4 class="subtitle">Options</h4>
            <form id="options">

              <div class="field">
                <div class="control tooltip" data-tooltip="Only effective in range picker">
                  <label class="label checkbox">
                    <input type="checkbox" name="allowOneSidedRange" value="true">
                    allowOneSidedRange
                  </label>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="autohide" value="true">
                    autohide
                  </label>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="beforeShowDay" value="true">
                    beforeShowDay
                  </label>
                </div>
                <div class="code-wrap">
                  <pre id="code-beforeShowDay"></pre>
                  <div class="collapse-button" data-target="code-beforeShowDay">show/hide</div>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="beforeShowMonth" value="true">
                    beforeShowMonth
                  </label>
                </div>
                <div class="code-wrap">
                  <pre id="code-beforeShowMonth"></pre>
                  <div class="collapse-button" data-target="code-beforeShowMonth">show/hide</div>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="beforeShowYear" value="true">
                    beforeShowYear
                  </label>
                </div>
                <div class="code-wrap">
                  <pre id="code-beforeShowYear"></pre>
                  <div class="collapse-button" data-target="code-beforeShowYear">show/hide</div>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="beforeShowDecade" value="true">
                    beforeShowDecade
                  </label>
                </div>
                <div class="code-wrap">
                  <pre id="code-beforeShowDecade"></pre>
                  <div class="collapse-button" data-target="code-beforeShowDecade">show/hide</div>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="calendarWeeks" value="true">
                    calendarWeeks
                  </label>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="clearBtn" value="true">
                    clearBtn
                  </label>
                </div>
              </div>

              <div class="field">
                <label class="label">dateDelimiter</label>
                <div class="control">
                  <input type="text" class="input" name="dateDelimiter" placeholder=",">
                </div>
              </div>

              <div class="field">
                <label class="label">datesDisabled</label>
                <div class="control tooltip" data-tooltip="enter in JSON format">
                  <textarea class="input" name="datesDisabled" placeholder="[]"></textarea>
                </div>
              </div>

              <div class="field">
                <label class="label">daysOfWeekDisabled</label>
                <div class="control">
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekDisabled" value="0"> 0
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekDisabled" value="1"> 1
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekDisabled" value="2"> 2
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekDisabled" value="3"> 3
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekDisabled" value="4"> 4
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekDisabled" value="5"> 5
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekDisabled" value="6"> 6
                  </label>&nbsp;
                </div>
              </div>

              <div class="field">
                <label class="label">daysOfWeekHighlighted</label>
                <div class="control">
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekHighlighted" value="0"> 0
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekHighlighted" value="1"> 1
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekHighlighted" value="2"> 2
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekHighlighted" value="3"> 3
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekHighlighted" value="4"> 4
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekHighlighted" value="5"> 5
                  </label>&nbsp;
                  <label class="checkbox">
                    <input type="checkbox" name="daysOfWeekHighlighted" value="6"> 6
                  </label>&nbsp;
                </div>
              </div>

              <div class="field">
                <label class="label">defaultViewDate</label>
                <div class="control">
                  <input type="text" class="input" name="defaultViewDate" placeholder="today">
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="disableTouchKeyboard" value="true">
                    disableTouchKeyboard
                  </label>
                </div>
              </div>

              <div class="field">
                <label class="label">format</label>
                <div class="control">
                  <input type="text" class="input" name="format" placeholder="mm/dd/yyyy">
                </div>
              </div>

              <div class="field">
                <label class="label">language</label>
                <div class="control">
                  <div class="select">
                    <select name="language" >
                      <option value="en">en – English (US)</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="field">
                <label class="label">maxDate</label>
                <div class="control">
                  <input type="text" class="input" name="maxDate" placeholder="null">
                </div>
              </div>

              <div class="field">
                <label class="label">maxNumberOfDates</label>
                <div class="control tooltip" data-tooltip="Not effective in range picker">
                  <input type="text" class="input" name="maxNumberOfDates" placeholder="1">
                </div>
              </div>

              <div class="field">
                <label class="label">maxView</label>
                <div class="control">
                  <div class="select">
                    <select name="maxView">
                      <option value="0">0 – days</option>
                      <option value="1">1 – months</option>
                      <option value="2">2 – years</option>
                      <option value="3" selected>3 – decades</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="field">
                <label class="label">minDate</label>
                <div class="control">
                  <input type="text" class="input" name="minDate" placeholder="null">
                </div>
              </div>

              <div class="field">
                <label class="label">nextArrow</label>
                <div class="control">
                  <textarea class="input" name="nextArrow" placeholder="»"></textarea>
                </div>
              </div>

              <div class="field">
                <label class="label">orientation</label>
                <div class="control">
                  <div class="select">
                    <select name="orientation" >
                      <option value="auto">auto</option>
                      <option value="top auto">top auto</option>
                      <option value="bottom auto">bottom auto</option>
                      <option value="auto left">auto left</option>
                      <option value="top left">top left</option>
                      <option value="bottom left">bottom left</option>
                      <option value="auto right">auto right</option>
                      <option value="top right">top right</option>
                      <option value="bottom right">bottom right</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="field">
                <label class="label">pickLevel</label>
                <div class="control">
                  <div class="select">
                    <select name="pickLevel">
                      <option value="0">0 – date</option>
                      <option value="1">1 – month</option>
                      <option value="2">2 – year</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="field">
                <label class="label">prevArrow</label>
                <div class="control">
                  <textarea class="input" name="prevArrow" placeholder="«"></textarea>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="showDaysOfWeek" value="true" checked>
                    showDaysOfWeek
                  </label>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="showOnClick" value="true" checked>
                    showOnClick
                  </label>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="showOnFocus" value="true" checked>
                    showOnFocus
                  </label>
                </div>
              </div>

              <div class="field">
                <label class="label">startView</label>
                <div class="control">
                  <div class="select">
                    <select name="startView">
                      <option value="0">0 – days</option>
                      <option value="1">1 – months</option>
                      <option value="2">2 – years</option>
                      <option value="3">3 – decades</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="field">
                <label class="label">title</label>
                <div class="control">
                  <input type="text" class="input" name="title">
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="todayBtn" value="true">
                    todayBtn
                  </label>
                </div>
              </div>

              <div class="field">
                <label class="label">todayBtnMode</label>
                <div class="control">
                  <div class="select">
                    <select name="todayBtnMode">
                      <option value="0">0 – focus</option>
                      <option value="1">1 – select</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="todayHighlight" value="true">
                    todayHighlight
                  </label>
                </div>
              </div>

              <div class="field">
                <div class="control">
                  <label class="label checkbox">
                    <input type="checkbox" name="updateOnBlur" value="true" checked>
                    updateOnBlur
                  </label>
                </div>
              </div>

              <div class="field">
                <label class="label">weekStart</label>
                <div class="control">
                  <input type="text" class="input" name="weekStart" placeholder="0">
                </div>
              </div>

            </form>
          </div>
          <hr>
          <div class="tile is-child">
            <h4 class="subtitle">Text direction</h4>
            <form id="direction">
              <div class="field">
                <div class="control">
                  <label class="radio">
                    <input type="radio" name="direction" value="ltr" checked>
                    LTR
                  </label>
                  <label class="radio">
                    <input type="radio" name="direction" value="rtl">
                    RTL
                  </label>
                </div>
              </div>
            </form>
          </div>
        </div>
      </section>
    </aside>

    <div class="toggle-btn"></div>
    <script src="../dist/js/datepicker-full.js"></script>
    <script src="./live-demo.js"></script>
    <script>
      /*global initialize onChangeType onChnageDirection onChangeInputOption onChangeInputOption onChangeTextareaOption onClickCheckboxOptions switchPicker */
      initialize();

      document.getElementById('types').querySelectorAll('input').forEach((el) => {
        el.addEventListener('change', onChangeType);
      });

      document.getElementById('direction').querySelectorAll('input').forEach((el) => {
        el.addEventListener('change', onChnageDirection);
      });

      const optsForm = document.getElementById('options');
      optsForm.querySelectorAll('input[type=text], input[type=radio], select').forEach((el) => {
        el.addEventListener('change', onChangeInputOption);
      });
      optsForm.querySelectorAll('textarea').forEach((el) => {
        el.addEventListener('change', onChangeTextareaOption);
      });
      optsForm.querySelectorAll('.checkbox').forEach((el) => {
        el.addEventListener('click', onClickCheckboxOptions);
      });

      switchPicker('input');
    </script>
  </body>
</html>