/**
 * Estonian translation for bootstrap-datepicker
 * <PERSON><PERSON> <https: //github.com/anroots>
 * Fixes by <PERSON><PERSON><PERSON> <<https: //github.com/ragulka>
 */
export default {
  et: {
    days: ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
    daysShort: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
    daysMin: ["P", "E", "T", "K", "N", "R", "L"],
    months: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "Oktoober", "November", "Detsember"],
    monthsShort: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sept", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"],
    today: "<PERSON>äna",
    clear: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    weekStart: 1,
    format: "dd.mm.yyyy"
  }
};
