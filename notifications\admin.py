from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    Notification, NotificationType, ActionItem, NotificationPreference,
    EmailTemplate, EmailAction, EmailNotificationLog
)
from core.admin import TenantAdminMixin


@admin.register(NotificationType)
class NotificationTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'type', 'is_active')
    search_fields = ('name', 'code', 'description')
    list_filter = ('type', 'is_active')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Notification)
class NotificationAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('title', 'recipient', 'notification_type', 'priority', 'is_read', 'created_at', 'tenant_id')
    search_fields = ('title', 'message', 'recipient__username')
    list_filter = ('priority', 'is_read', 'action_required', 'notification_type', 'created_at', 'tenant_id')
    readonly_fields = ('created_at', 'read_at', 'dismissed_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (None, {
            'fields': ('recipient', 'notification_type', 'title', 'message', 'priority')
        }),
        (_('Action'), {
            'fields': ('action_required', 'action_url', 'action_text'),
            'classes': ('collapse',),
        }),
        (_('Related Object'), {
            'fields': ('related_object_type', 'related_object_id'),
            'classes': ('collapse',),
        }),
        (_('Status'), {
            'fields': ('is_read', 'is_dismissed', 'read_at', 'dismissed_at'),
        }),
    )


@admin.register(ActionItem)
class ActionItemAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('title', 'assigned_to', 'action_type', 'priority', 'status', 'due_date', 'created_at', 'tenant_id')
    search_fields = ('title', 'description', 'assigned_to__username')
    list_filter = ('action_type', 'priority', 'status', 'due_date', 'created_at', 'tenant_id')
    readonly_fields = ('created_at', 'completed_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (None, {
            'fields': ('assigned_to', 'action_type', 'title', 'description', 'priority', 'status')
        }),
        (_('Timing'), {
            'fields': ('due_date', 'completed_at', 'completed_by'),
        }),
        (_('Related Object'), {
            'fields': ('related_object_type', 'related_object_id'),
            'classes': ('collapse',),
        }),
        (_('Action'), {
            'fields': ('action_url', 'metadata'),
            'classes': ('collapse',),
        }),
    )


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('user', 'email_enabled', 'browser_enabled', 'sound_enabled', 'email_frequency', 'tenant_id')
    search_fields = ('user__username', 'user__email')
    list_filter = ('email_enabled', 'browser_enabled', 'sound_enabled', 'email_frequency', 'tenant_id')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('user',)
        }),
        (_('Email Settings'), {
            'fields': ('email_enabled', 'email_frequency'),
        }),
        (_('Browser Settings'), {
            'fields': ('browser_enabled', 'sound_enabled'),
        }),
        (_('Notification Types'), {
            'fields': ('notification_types',),
        }),
    )


@admin.register(EmailTemplate)
class EmailTemplateAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'template_type', 'from_status', 'to_status', 'is_active', 'priority', 'tenant_id')
    search_fields = ('name', 'subject_template', 'body_template')
    list_filter = ('template_type', 'is_active', 'include_actions', 'priority', 'tenant_id')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'template_type', 'is_active', 'priority')
        }),
        (_('Status Configuration'), {
            'fields': ('from_status', 'to_status'),
            'classes': ('collapse',),
        }),
        (_('Role Targeting'), {
            'fields': ('target_roles',),
            'classes': ('collapse',),
        }),
        (_('Email Content'), {
            'fields': ('subject_template', 'body_template'),
        }),
        (_('Email Actions'), {
            'fields': ('include_actions', 'action_buttons'),
            'classes': ('collapse',),
        }),
    )


@admin.register(EmailAction)
class EmailActionAdmin(admin.ModelAdmin):
    list_display = ('action_label', 'user', 'action_type', 'is_used', 'expires_at', 'created_at')
    search_fields = ('action_label', 'user__username', 'token')
    list_filter = ('action_type', 'is_used', 'requires_login', 'expires_at', 'created_at')
    readonly_fields = ('token', 'created_at', 'used_at')
    
    fieldsets = (
        (None, {
            'fields': ('user', 'action_type', 'action_label', 'action_url')
        }),
        (_('Related Object'), {
            'fields': ('related_object_type', 'related_object_id'),
        }),
        (_('Settings'), {
            'fields': ('requires_login', 'expires_at'),
        }),
        (_('Status'), {
            'fields': ('is_used', 'used_at'),
        }),
        (_('Token & Metadata'), {
            'fields': ('token', 'metadata'),
            'classes': ('collapse',),
        }),
    )


@admin.register(EmailNotificationLog)
class EmailNotificationLogAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('subject', 'recipient', 'to_email', 'status', 'sent_at', 'tenant_id')
    search_fields = ('subject', 'recipient__username', 'to_email', 'body')
    list_filter = ('status', 'sent_at', 'opened_at', 'clicked_at', 'tenant_id')
    readonly_fields = ('created_at', 'sent_at', 'opened_at', 'clicked_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (None, {
            'fields': ('recipient', 'email_template', 'to_email')
        }),
        (_('Email Content'), {
            'fields': ('subject', 'body'),
        }),
        (_('Status'), {
            'fields': ('status', 'sent_at', 'opened_at', 'clicked_at'),
        }),
        (_('Error Tracking'), {
            'fields': ('error_message', 'retry_count'),
            'classes': ('collapse',),
        }),
        (_('Related Objects'), {
            'fields': ('related_notification', 'related_object_type', 'related_object_id'),
            'classes': ('collapse',),
        }),
        (_('Email Actions'), {
            'fields': ('email_actions',),
            'classes': ('collapse',),
        }),
    )
