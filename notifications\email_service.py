import uuid
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from django.core.mail import send_mail
from django.template import Template, Context
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth.models import User
from django.conf import settings
from django.utils.translation import gettext as _

from .models import (
    EmailTemplate, EmailAction, EmailNotificationLog, 
    Notification, NotificationPreference
)
from user_roles.models import UserRole

logger = logging.getLogger(__name__)


class EmailNotificationService:
    """Service for sending email notifications with action capabilities"""
    
    def __init__(self):
        self.from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
        self.base_url = getattr(settings, 'BASE_URL', 'http://localhost:4000')
    
    def send_status_change_email(self, 
                               template_type: str,
                               recipients: List[User],
                               context: Dict[str, Any],
                               from_status: str = None,
                               to_status: str = None,
                               related_object_type: str = None,
                               related_object_id: str = None,
                               tenant_id: str = None) -> List[EmailNotificationLog]:
        """
        Send email notifications for status changes
        
        Args:
            template_type: Type of email template to use
            recipients: List of users to send email to
            context: Template context variables
            from_status: Previous status
            to_status: New status
            related_object_type: Type of related object (e.g., 'work_order')
            related_object_id: ID of related object
            tenant_id: Tenant ID for multi-tenant filtering
        
        Returns:
            List of EmailNotificationLog instances
        """
        email_logs = []
        
        # Get appropriate email template
        template = self._get_email_template(
            template_type, from_status, to_status, tenant_id
        )
        
        if not template:
            logger.warning(f"No email template found for {template_type}")
            return email_logs
        
        for recipient in recipients:
            # Check user preferences
            if not self._should_send_email(recipient, template_type):
                continue
            
            # Filter by role if template has role restrictions
            if template.target_roles and not self._user_has_target_role(recipient, template.target_roles):
                continue
            
            try:
                # Prepare context with user-specific data
                email_context = self._prepare_email_context(context, recipient, template)
                
                # Render email content
                subject = template.render_subject(email_context)
                body = template.render_body(email_context)
                
                # Create email actions if enabled
                email_actions = []
                if template.include_actions and template.action_buttons:
                    email_actions = self._create_email_actions(
                        recipient, template.action_buttons, 
                        related_object_type, related_object_id, email_context
                    )
                    
                    # Add action buttons to email body
                    body = self._add_action_buttons_to_body(body, email_actions)
                
                # Create email log entry
                email_log = EmailNotificationLog.objects.create(
                    recipient=recipient,
                    email_template=template,
                    subject=subject,
                    body=body,
                    to_email=recipient.email,
                    related_object_type=related_object_type,
                    related_object_id=related_object_id,
                    tenant_id=tenant_id
                )
                
                # Associate email actions
                if email_actions:
                    email_log.email_actions.set(email_actions)
                
                # Send email
                success = self._send_email(recipient.email, subject, body)
                
                if success:
                    email_log.status = 'sent'
                    email_log.sent_at = timezone.now()
                else:
                    email_log.status = 'failed'
                    email_log.error_message = 'Failed to send email'
                
                email_log.save()
                email_logs.append(email_log)
                
            except Exception as e:
                logger.error(f"Error sending email to {recipient.email}: {str(e)}")
                # Create failed log entry
                email_log = EmailNotificationLog.objects.create(
                    recipient=recipient,
                    email_template=template,
                    subject=f"Failed: {template_type}",
                    body="",
                    to_email=recipient.email,
                    status='failed',
                    error_message=str(e),
                    related_object_type=related_object_type,
                    related_object_id=related_object_id,
                    tenant_id=tenant_id
                )
                email_logs.append(email_log)
        
        return email_logs
    
    def send_role_based_notification(self,
                                   template_type: str,
                                   role_codes: List[str],
                                   context: Dict[str, Any],
                                   tenant_id: str = None,
                                   **kwargs) -> List[EmailNotificationLog]:
        """
        Send email notifications to users with specific roles
        
        Args:
            template_type: Type of email template
            role_codes: List of role codes to target
            context: Template context
            tenant_id: Tenant ID for filtering
            **kwargs: Additional arguments for send_status_change_email
        
        Returns:
            List of EmailNotificationLog instances
        """
        # Get users with target roles
        recipients = self._get_users_by_roles(role_codes, tenant_id)
        
        return self.send_status_change_email(
            template_type=template_type,
            recipients=recipients,
            context=context,
            tenant_id=tenant_id,
            **kwargs
        )
    
    def process_email_action(self, token: str, user: User = None) -> Dict[str, Any]:
        """
        Process an email action when user clicks on action button
        
        Args:
            token: Unique action token
            user: User performing the action (if logged in)
        
        Returns:
            Dictionary with action result
        """
        try:
            action = EmailAction.objects.get(token=token)
            
            # Check if action is expired or already used
            if action.is_used:
                return {'success': False, 'message': _('This action has already been used')}
            
            if action.is_expired():
                return {'success': False, 'message': _('This action has expired')}
            
            # Check if user authentication is required
            if action.requires_login and not user:
                return {
                    'success': False, 
                    'message': _('Login required'),
                    'redirect_url': f"{reverse('core:login')}?next={action.action_url}"
                }
            
            # Check if user has permission (if logged in)
            if user and action.user != user:
                return {'success': False, 'message': _('You do not have permission to perform this action')}
            
            # Mark action as used
            action.mark_used()
            
            # Return success with redirect URL
            return {
                'success': True,
                'message': _('Action processed successfully'),
                'redirect_url': action.action_url,
                'action_type': action.action_type,
                'metadata': action.metadata
            }
            
        except EmailAction.DoesNotExist:
            return {'success': False, 'message': _('Invalid action token')}
        except Exception as e:
            logger.error(f"Error processing email action {token}: {str(e)}")
            return {'success': False, 'message': _('An error occurred processing the action')}
    
    def _get_email_template(self, template_type: str, from_status: str = None, 
                          to_status: str = None, tenant_id: str = None) -> Optional[EmailTemplate]:
        """Get the most appropriate email template"""
        queryset = EmailTemplate.objects.filter(
            template_type=template_type,
            is_active=True
        )
        
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
        
        # Try to find exact match first
        if from_status and to_status:
            exact_match = queryset.filter(
                from_status=from_status,
                to_status=to_status
            ).order_by('-priority').first()
            if exact_match:
                return exact_match
        
        # Try to find template with matching to_status
        if to_status:
            to_match = queryset.filter(
                to_status=to_status,
                from_status__in=['', None]
            ).order_by('-priority').first()
            if to_match:
                return to_match
        
        # Fall back to general template
        return queryset.filter(
            from_status__in=['', None],
            to_status__in=['', None]
        ).order_by('-priority').first()
    
    def _should_send_email(self, user: User, template_type: str) -> bool:
        """Check if user should receive email notifications"""
        try:
            prefs = user.notification_preferences
            return prefs.email_enabled
        except:
            # Default to True if no preferences set
            return True
    
    def _user_has_target_role(self, user: User, target_roles: List[str]) -> bool:
        """Check if user has any of the target roles"""
        if not target_roles:
            return True
        
        try:
            user_roles = UserRole.objects.filter(user=user).values_list('role__code', flat=True)
            return any(role in target_roles for role in user_roles)
        except:
            return True  # Default to True if role check fails
    
    def _get_users_by_roles(self, role_codes: List[str], tenant_id: str = None) -> List[User]:
        """Get users with specific roles"""
        queryset = User.objects.filter(
            user_roles__role__code__in=role_codes,
            is_active=True
        ).distinct()
        
        if tenant_id:
            queryset = queryset.filter(user_roles__tenant_id=tenant_id)
        
        return list(queryset)
    
    def _prepare_email_context(self, context: Dict[str, Any], user: User, template: EmailTemplate) -> Dict[str, Any]:
        """Prepare email context with user-specific data"""
        email_context = context.copy()
        email_context.update({
            'user': user,
            'user_name': user.get_full_name() or user.username,
            'base_url': self.base_url,
            'site_name': getattr(settings, 'SITE_NAME', 'AfterSails'),
            'current_date': timezone.now().strftime('%Y-%m-%d'),
            'current_time': timezone.now().strftime('%H:%M'),
        })
        return email_context
    
    def _create_email_actions(self, user: User, action_buttons: List[Dict], 
                            related_object_type: str, related_object_id: str,
                            context: Dict[str, Any]) -> List[EmailAction]:
        """Create email action instances for action buttons"""
        actions = []
        
        for button_config in action_buttons:
            try:
                # Generate unique token
                token = str(uuid.uuid4())
                
                # Create action URL
                action_url = self._build_action_url(
                    button_config.get('url_pattern', ''),
                    context,
                    related_object_type,
                    related_object_id
                )
                
                # Set expiry (default 7 days)
                expires_at = timezone.now() + timedelta(
                    days=button_config.get('expires_days', 7)
                )
                
                action = EmailAction.objects.create(
                    token=token,
                    action_type=button_config['type'],
                    action_label=button_config['label'],
                    action_url=action_url,
                    related_object_type=related_object_type,
                    related_object_id=related_object_id,
                    user=user,
                    requires_login=button_config.get('requires_login', True),
                    expires_at=expires_at,
                    metadata=button_config.get('metadata', {})
                )
                actions.append(action)
                
            except Exception as e:
                logger.error(f"Error creating email action: {str(e)}")
        
        return actions
    
    def _build_action_url(self, url_pattern: str, context: Dict[str, Any],
                         related_object_type: str, related_object_id: str) -> str:
        """Build action URL from pattern and context"""
        try:
            # Replace placeholders in URL pattern
            url = url_pattern.format(
                object_type=related_object_type,
                object_id=related_object_id,
                **context
            )
            
            # Ensure absolute URL
            if not url.startswith('http'):
                url = f"{self.base_url}{url}"
            
            return url
        except:
            # Fallback to base URL
            return self.base_url
    
    def _add_action_buttons_to_body(self, body: str, actions: List[EmailAction]) -> str:
        """Add action buttons to email body"""
        if not actions:
            return body
        
        # Create action buttons HTML
        buttons_html = '<div style="margin: 20px 0; text-align: center;">'
        
        for action in actions:
            action_url = f"{self.base_url}/notifications/email-action/{action.token}/"
            
            # Style based on action type
            button_style = self._get_button_style(action.action_type)
            
            buttons_html += f'''
            <a href="{action_url}" 
               style="{button_style}"
               target="_blank">
                {action.action_label}
            </a>
            '''
        
        buttons_html += '</div>'
        
        # Insert buttons before closing body tag or at the end
        if '</body>' in body:
            body = body.replace('</body>', f'{buttons_html}</body>')
        else:
            body += buttons_html
        
        return body
    
    def _get_button_style(self, action_type: str) -> str:
        """Get CSS style for action button based on type"""
        base_style = (
            "display: inline-block; "
            "padding: 10px 20px; "
            "margin: 5px; "
            "text-decoration: none; "
            "border-radius: 5px; "
            "font-weight: bold; "
            "color: white; "
        )
        
        color_styles = {
            'approve': 'background-color: #28a745;',
            'reject': 'background-color: #dc3545;',
            'assign': 'background-color: #007bff;',
            'complete': 'background-color: #28a745;',
            'cancel': 'background-color: #6c757d;',
            'view': 'background-color: #17a2b8;',
            'edit': 'background-color: #ffc107; color: black;',
            'acknowledge': 'background-color: #6f42c1;',
        }
        
        return base_style + color_styles.get(action_type, 'background-color: #6c757d;')
    
    def _send_email(self, to_email: str, subject: str, body: str) -> bool:
        """Send email using Django's email backend"""
        try:
            send_mail(
                subject=subject,
                message='',  # Plain text version (empty for HTML-only)
                html_message=body,
                from_email=self.from_email,
                recipient_list=[to_email],
                fail_silently=False
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return False


# Convenience functions for common email notifications

def send_work_order_status_email(work_order, from_status: str, to_status: str, tenant_id: str = None):
    """Send work order status change email"""
    service = EmailNotificationService()
    
    # Get relevant users based on status change
    recipients = []
    
    if to_status == 'assigned' and work_order.assigned_technician:
        recipients.append(work_order.assigned_technician)
    elif to_status in ['completed', 'cancelled']:
        # Notify service advisors and managers
        recipients.extend(service._get_users_by_roles(['service_advisor', 'center_manager'], tenant_id))
    
    context = {
        'work_order': work_order,
        'work_order_id': work_order.id,
        'customer_name': getattr(work_order, 'customer_name', ''),
        'vehicle_info': getattr(work_order, 'vehicle_info', ''),
        'from_status': from_status,
        'to_status': to_status,
    }
    
    return service.send_status_change_email(
        template_type='work_order_status',
        recipients=recipients,
        context=context,
        from_status=from_status,
        to_status=to_status,
        related_object_type='work_order',
        related_object_id=str(work_order.id),
        tenant_id=tenant_id
    )


def send_purchase_approval_email(purchase_order, tenant_id: str = None):
    """Send purchase order approval request email"""
    service = EmailNotificationService()
    
    # Get users who can approve purchases
    recipients = service._get_users_by_roles(['center_manager', 'franchise_admin'], tenant_id)
    
    context = {
        'purchase_order': purchase_order,
        'purchase_order_id': purchase_order.id,
        'supplier_name': getattr(purchase_order, 'supplier_name', ''),
        'total_amount': getattr(purchase_order, 'total_amount', 0),
    }
    
    return service.send_status_change_email(
        template_type='purchase_order_approval',
        recipients=recipients,
        context=context,
        related_object_type='purchase_order',
        related_object_id=str(purchase_order.id),
        tenant_id=tenant_id
    )


def send_inventory_low_stock_email(item, tenant_id: str = None):
    """Send low stock alert email"""
    service = EmailNotificationService()
    
    # Get warehouse and inventory managers
    recipients = service._get_users_by_roles(['parts_clerk', 'center_manager'], tenant_id)
    
    context = {
        'item': item,
        'item_name': getattr(item, 'name', ''),
        'current_stock': getattr(item, 'current_stock', 0),
        'minimum_stock': getattr(item, 'minimum_stock', 0),
    }
    
    return service.send_status_change_email(
        template_type='inventory_low_stock',
        recipients=recipients,
        context=context,
        related_object_type='inventory_item',
        related_object_id=str(item.id),
        tenant_id=tenant_id
    ) 