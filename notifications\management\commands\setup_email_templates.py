from django.core.management.base import BaseCommand
from django.utils.translation import gettext as _
from notifications.models import EmailTemplate
import uuid


class Command(BaseCommand):
    help = 'Set up default email templates for all status changes and roles'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            help='Tenant ID to create templates for (optional)',
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id')
        
        self.stdout.write('Setting up default email templates...')
        
        # Work Order Templates
        self.create_work_order_templates(tenant_id)
        
        # Purchase Order Templates
        self.create_purchase_templates(tenant_id)
        
        # Inventory Templates
        self.create_inventory_templates(tenant_id)
        
        # Sales Templates
        self.create_sales_templates(tenant_id)
        
        # Warehouse Templates
        self.create_warehouse_templates(tenant_id)
        
        # General Templates
        self.create_general_templates(tenant_id)
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up email templates!')
        )

    def create_work_order_templates(self, tenant_id):
        """Create work order email templates"""
        
        # Work Order Assigned
        self.create_template(
            name="Work Order Assigned",
            template_type="work_order_assigned",
            to_status="assigned",
            target_roles=["technician"],
            subject_template="Work Order #{{ work_order_id }} Assigned to You",
            body_template="""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">Work Order Assigned</h2>
                    
                    <p>Dear {{ user_name }},</p>
                    
                    <p>A work order has been assigned to you:</p>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h3>Work Order Details</h3>
                        <p><strong>Work Order ID:</strong> #{{ work_order_id }}</p>
                        <p><strong>Customer:</strong> {{ customer_name }}</p>
                        <p><strong>Vehicle:</strong> {{ vehicle_info }}</p>
                        <p><strong>Status:</strong> {{ to_status }}</p>
                    </div>
                    
                    <p>Please start working on this order and update the status accordingly.</p>
                    
                    <p style="color: #666; font-size: 12px;">
                        This is an automated notification from {{ site_name }}.
                    </p>
                </div>
            </body>
            </html>
            """,
            include_actions=True,
            action_buttons=[
                {
                    "type": "view",
                    "label": "View Work Order",
                    "url_pattern": "/work-orders/{work_order_id}/",
                    "requires_login": True,
                    "expires_days": 30
                }
            ],
            tenant_id=tenant_id
        )
        
        # Work Order Completed
        self.create_template(
            name="Work Order Completed",
            template_type="work_order_completed",
            to_status="completed",
            target_roles=["service_advisor", "center_manager", "cashier"],
            subject_template="Work Order #{{ work_order_id }} Completed - Invoice Ready",
            body_template="""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">Work Order Completed</h2>
                    
                    <p>Dear {{ user_name }},</p>
                    
                    <p>Work order has been completed and is ready for invoicing:</p>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h3>Work Order Details</h3>
                        <p><strong>Work Order ID:</strong> #{{ work_order_id }}</p>
                        <p><strong>Customer:</strong> {{ customer_name }}</p>
                        <p><strong>Vehicle:</strong> {{ vehicle_info }}</p>
                        <p><strong>Status:</strong> {{ to_status }}</p>
                        <p><strong>Completed:</strong> {{ current_date }} {{ current_time }}</p>
                    </div>
                    
                    <p>Please generate the invoice and process payment.</p>
                    
                    <p style="color: #666; font-size: 12px;">
                        This is an automated notification from {{ site_name }}.
                    </p>
                </div>
            </body>
            </html>
            """,
            include_actions=True,
            action_buttons=[
                {
                    "type": "approve",
                    "label": "Generate Invoice",
                    "url_pattern": "/work-orders/{work_order_id}/invoice/",
                    "requires_login": True,
                    "expires_days": 7
                }
            ],
            tenant_id=tenant_id
        )

    def create_purchase_templates(self, tenant_id):
        """Create purchase order email templates"""
        
        # Purchase Order Approval
        self.create_template(
            name="Purchase Order Approval Required",
            template_type="purchase_order_approval",
            target_roles=["center_manager", "franchise_admin"],
            subject_template="Purchase Order #{{ purchase_order_id }} Requires Approval",
            body_template="""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #ffc107;">Purchase Order Approval Required</h2>
                    
                    <p>Dear {{ user_name }},</p>
                    
                    <p>A purchase order requires your approval:</p>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h3>Purchase Order Details</h3>
                        <p><strong>PO Number:</strong> #{{ purchase_order_id }}</p>
                        <p><strong>Supplier:</strong> {{ supplier_name }}</p>
                        <p><strong>Total Amount:</strong> ${{ total_amount }}</p>
                        <p><strong>Created:</strong> {{ current_date }} {{ current_time }}</p>
                    </div>
                    
                    <p>Please review and approve or reject this purchase order.</p>
                    
                    <p style="color: #666; font-size: 12px;">
                        This is an automated notification from {{ site_name }}.
                    </p>
                </div>
            </body>
            </html>
            """,
            include_actions=True,
            action_buttons=[
                {
                    "type": "approve",
                    "label": "Approve",
                    "url_pattern": "/purchases/{purchase_order_id}/approve/",
                    "requires_login": True,
                    "expires_days": 7
                },
                {
                    "type": "reject",
                    "label": "Reject",
                    "url_pattern": "/purchases/{purchase_order_id}/reject/",
                    "requires_login": True,
                    "expires_days": 7
                }
            ],
            tenant_id=tenant_id
        )

    def create_inventory_templates(self, tenant_id):
        """Create inventory email templates"""
        
        # Low Stock Alert
        self.create_template(
            name="Low Stock Alert",
            template_type="inventory_low_stock",
            target_roles=["parts_clerk", "center_manager"],
            subject_template="Low Stock Alert: {{ item_name }}",
            body_template="""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #dc3545;">Low Stock Alert</h2>
                    
                    <p>Dear {{ user_name }},</p>
                    
                    <p>An inventory item is running low on stock:</p>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h3>Item Details</h3>
                        <p><strong>Item:</strong> {{ item_name }}</p>
                        <p><strong>Current Stock:</strong> {{ current_stock }}</p>
                        <p><strong>Minimum Stock:</strong> {{ minimum_stock }}</p>
                        <p><strong>Alert Date:</strong> {{ current_date }} {{ current_time }}</p>
                    </div>
                    
                    <p>Please consider reordering this item to maintain adequate stock levels.</p>
                    
                    <p style="color: #666; font-size: 12px;">
                        This is an automated notification from {{ site_name }}.
                    </p>
                </div>
            </body>
            </html>
            """,
            tenant_id=tenant_id
        )

    def create_sales_templates(self, tenant_id):
        """Create sales email templates"""
        
        # Invoice Generated
        self.create_template(
            name="Invoice Generated",
            template_type="invoice_generated",
            target_roles=["cashier", "service_advisor"],
            subject_template="Invoice Generated - Ready for Payment",
            body_template="""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">Invoice Generated</h2>
                    
                    <p>Dear {{ user_name }},</p>
                    
                    <p>An invoice has been generated and is ready for payment processing:</p>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h3>Invoice Details</h3>
                        <p><strong>Invoice Number:</strong> #{{ object_id }}</p>
                        <p><strong>Customer:</strong> {{ customer_name }}</p>
                        <p><strong>Amount:</strong> ${{ total_amount }}</p>
                        <p><strong>Generated:</strong> {{ current_date }} {{ current_time }}</p>
                    </div>
                    
                    <p>Please process payment and update the invoice status.</p>
                    
                    <p style="color: #666; font-size: 12px;">
                        This is an automated notification from {{ site_name }}.
                    </p>
                </div>
            </body>
            </html>
            """,
            tenant_id=tenant_id
        )

    def create_warehouse_templates(self, tenant_id):
        """Create warehouse email templates"""
        
        # Transfer Request
        self.create_template(
            name="Transfer Request Approval",
            template_type="transfer_request_approval",
            target_roles=["center_manager", "parts_clerk"],
            subject_template="Transfer Request Requires Approval",
            body_template="""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #ffc107;">Transfer Request Approval</h2>
                    
                    <p>Dear {{ user_name }},</p>
                    
                    <p>A transfer request requires your approval:</p>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h3>Transfer Details</h3>
                        <p><strong>Transfer ID:</strong> #{{ object_id }}</p>
                        <p><strong>From:</strong> {{ from_location }}</p>
                        <p><strong>To:</strong> {{ to_location }}</p>
                        <p><strong>Items:</strong> {{ item_count }} items</p>
                        <p><strong>Requested:</strong> {{ current_date }} {{ current_time }}</p>
                    </div>
                    
                    <p>Please review and approve or reject this transfer request.</p>
                    
                    <p style="color: #666; font-size: 12px;">
                        This is an automated notification from {{ site_name }}.
                    </p>
                </div>
            </body>
            </html>
            """,
            include_actions=True,
            action_buttons=[
                {
                    "type": "approve",
                    "label": "Approve Transfer",
                    "url_pattern": "/warehouse/transfers/{object_id}/approve/",
                    "requires_login": True,
                    "expires_days": 7
                },
                {
                    "type": "reject",
                    "label": "Reject Transfer",
                    "url_pattern": "/warehouse/transfers/{object_id}/reject/",
                    "requires_login": True,
                    "expires_days": 7
                }
            ],
            tenant_id=tenant_id
        )

    def create_general_templates(self, tenant_id):
        """Create general email templates"""
        
        # Task Assignment
        self.create_template(
            name="Task Assignment",
            template_type="task_assignment",
            target_roles=[],  # All roles
            subject_template="New Task Assigned: {{ task_title }}",
            body_template="""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #007bff;">New Task Assigned</h2>
                    
                    <p>Dear {{ user_name }},</p>
                    
                    <p>A new task has been assigned to you:</p>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h3>Task Details</h3>
                        <p><strong>Task:</strong> {{ task_title }}</p>
                        <p><strong>Description:</strong> {{ task_description }}</p>
                        <p><strong>Priority:</strong> {{ priority }}</p>
                        <p><strong>Due Date:</strong> {{ due_date }}</p>
                        <p><strong>Assigned:</strong> {{ current_date }} {{ current_time }}</p>
                    </div>
                    
                    <p>Please complete this task by the due date.</p>
                    
                    <p style="color: #666; font-size: 12px;">
                        This is an automated notification from {{ site_name }}.
                    </p>
                </div>
            </body>
            </html>
            """,
            tenant_id=tenant_id
        )

    def create_template(self, name, template_type, subject_template, body_template, 
                       target_roles=None, from_status=None, to_status=None, 
                       include_actions=False, action_buttons=None, tenant_id=None):
        """Helper method to create email template"""
        
        template, created = EmailTemplate.objects.get_or_create(
            name=name,
            template_type=template_type,
            from_status=from_status or '',
            to_status=to_status or '',
            tenant_id=tenant_id,
            defaults={
                'subject_template': subject_template,
                'body_template': body_template,
                'target_roles': target_roles or [],
                'include_actions': include_actions,
                'action_buttons': action_buttons or [],
                'is_active': True,
                'priority': 0,
            }
        )
        
        if created:
            self.stdout.write(f'  ✓ Created template: {name}')
        else:
            self.stdout.write(f'  - Template already exists: {name}')
        
        return template