from django.core.management.base import BaseCommand
from notifications.services import WorkflowNotificationService


class Command(BaseCommand):
    help = 'Sets up the notification system with default notification types'

    def handle(self, *args, **options):
        self.stdout.write('Setting up notification system...')
        
        # Create default notification types
        WorkflowNotificationService.setup_default_notification_types()
        
        self.stdout.write(self.style.SUCCESS('✅ Notification system setup completed!'))
        self.stdout.write('Default notification types have been created.') 