from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils.translation import gettext as _
from notifications.email_service import EmailNotificationService
from notifications.models import EmailTemplate


class Command(BaseCommand):
    help = 'Test the email notification system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=str,
            help='Username to send test email to',
            required=True
        )
        parser.add_argument(
            '--template-type',
            type=str,
            help='Email template type to test',
            default='work_order_assigned'
        )

    def handle(self, *args, **options):
        username = options['user']
        template_type = options['template_type']
        
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User "{username}" not found')
            )
            return
        
        if not user.email:
            self.stdout.write(
                self.style.ERROR(f'User "{username}" has no email address')
            )
            return
        
        self.stdout.write(f'Testing email notification system...')
        self.stdout.write(f'User: {user.username} ({user.email})')
        self.stdout.write(f'Template Type: {template_type}')
        
        # Initialize email service
        email_service = EmailNotificationService()
        
        # Prepare test context
        context = {
            'work_order_id': '12345',
            'customer_name': 'John Doe',
            'vehicle_info': '2020 Toyota Camry',
            'from_status': 'pending',
            'to_status': 'assigned',
        }
        
        # Send test email
        try:
            email_logs = email_service.send_status_change_email(
                template_type=template_type,
                recipients=[user],
                context=context,
                from_status='pending',
                to_status='assigned',
                related_object_type='work_order',
                related_object_id='12345',
                tenant_id=getattr(user, 'tenant_id', None)
            )
            
            if email_logs:
                log = email_logs[0]
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Email sent successfully!')
                )
                self.stdout.write(f'  Subject: {log.subject}')
                self.stdout.write(f'  Status: {log.status}')
                self.stdout.write(f'  To: {log.to_email}')
                
                if log.email_actions.exists():
                    self.stdout.write(f'  Email Actions: {log.email_actions.count()}')
                    for action in log.email_actions.all():
                        self.stdout.write(f'    - {action.action_label} ({action.token})')
                
                if log.status == 'failed':
                    self.stdout.write(
                        self.style.ERROR(f'  Error: {log.error_message}')
                    )
            else:
                self.stdout.write(
                    self.style.WARNING('No email was sent (possibly no template found or user preferences disabled)')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error sending email: {str(e)}')
            )
        
        # Show available templates
        self.stdout.write('\nAvailable email templates:')
        templates = EmailTemplate.objects.filter(is_active=True)
        for template in templates:
            self.stdout.write(f'  - {template.template_type}: {template.name}')
        
        self.stdout.write(
            self.style.SUCCESS('\nTest completed!')
        ) 