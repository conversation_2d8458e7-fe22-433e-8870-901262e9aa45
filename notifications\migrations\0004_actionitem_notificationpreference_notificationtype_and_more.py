# Generated by Django 4.2.20 on 2025-06-14 07:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("notifications", "0003_alter_notification_tenant_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ActionItem",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("approve_purchase", "Approve Purchase Order"),
                            ("generate_invoice", "Generate Invoice"),
                            ("approve_parts_request", "Approve Parts Request"),
                            ("process_transfer", "Process Transfer"),
                            ("assign_technician", "Assign Technician"),
                            ("update_work_order", "Update Work Order Status"),
                            ("review_inventory", "Review Inventory"),
                        ],
                        max_length=50,
                        verbose_name="Action Type",
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="Title")),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=20,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "due_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Due Date"
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Completed At"
                    ),
                ),
                (
                    "related_object_type",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Related Object Type"
                    ),
                ),
                (
                    "related_object_id",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Related Object ID"
                    ),
                ),
                (
                    "action_url",
                    models.URLField(blank=True, null=True, verbose_name="Action URL"),
                ),
                (
                    "metadata",
                    models.JSONField(blank=True, default=dict, verbose_name="Metadata"),
                ),
            ],
            options={
                "verbose_name": "Action Item",
                "verbose_name_plural": "Action Items",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="NotificationPreference",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "email_enabled",
                    models.BooleanField(
                        default=True, verbose_name="Email Notifications"
                    ),
                ),
                (
                    "email_frequency",
                    models.CharField(
                        choices=[
                            ("immediate", "Immediate"),
                            ("hourly", "Hourly"),
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                        ],
                        default="immediate",
                        max_length=20,
                        verbose_name="Email Frequency",
                    ),
                ),
                (
                    "browser_enabled",
                    models.BooleanField(
                        default=True, verbose_name="Browser Notifications"
                    ),
                ),
                (
                    "sound_enabled",
                    models.BooleanField(
                        default=True, verbose_name="Sound Notifications"
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Preference",
                "verbose_name_plural": "Notification Preferences",
            },
        ),
        migrations.CreateModel(
            name="NotificationType",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Name")),
                (
                    "code",
                    models.CharField(max_length=50, unique=True, verbose_name="Code"),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("work_order_status", "Work Order Status Change"),
                            ("inventory_low_stock", "Low Stock Alert"),
                            ("purchase_approval", "Purchase Order Approval"),
                            ("invoice_ready", "Invoice Ready for Generation"),
                            ("parts_request", "Parts Request"),
                            ("transfer_request", "Transfer Request"),
                            ("task_assignment", "Task Assignment"),
                            ("system_alert", "System Alert"),
                        ],
                        max_length=50,
                        verbose_name="Type",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "icon",
                    models.CharField(
                        default="fas fa-bell", max_length=50, verbose_name="Icon Class"
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="blue", max_length=50, verbose_name="Color Class"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
            ],
            options={
                "verbose_name": "Notification Type",
                "verbose_name_plural": "Notification Types",
                "ordering": ["name"],
            },
        ),
        migrations.RemoveField(
            model_name="notification",
            name="level",
        ),
        migrations.RemoveField(
            model_name="notification",
            name="object_id",
        ),
        migrations.RemoveField(
            model_name="notification",
            name="object_type",
        ),
        migrations.AddField(
            model_name="notification",
            name="action_required",
            field=models.BooleanField(default=False, verbose_name="Action Required"),
        ),
        migrations.AddField(
            model_name="notification",
            name="action_text",
            field=models.CharField(
                blank=True, max_length=100, verbose_name="Action Text"
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="action_url",
            field=models.URLField(blank=True, null=True, verbose_name="Action URL"),
        ),
        migrations.AddField(
            model_name="notification",
            name="dismissed_at",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="Dismissed At"
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="is_dismissed",
            field=models.BooleanField(default=False, verbose_name="Is Dismissed"),
        ),
        migrations.AddField(
            model_name="notification",
            name="priority",
            field=models.CharField(
                choices=[
                    ("low", "Low"),
                    ("medium", "Medium"),
                    ("high", "High"),
                    ("urgent", "Urgent"),
                ],
                default="medium",
                max_length=20,
                verbose_name="Priority",
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="recipient",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notifications",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Recipient",
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="related_object_id",
            field=models.CharField(
                blank=True, max_length=100, verbose_name="Related Object ID"
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="related_object_type",
            field=models.CharField(
                blank=True, max_length=50, verbose_name="Related Object Type"
            ),
        ),
        migrations.AlterField(
            model_name="notification",
            name="is_read",
            field=models.BooleanField(default=False, verbose_name="Is Read"),
        ),
        migrations.AlterField(
            model_name="notification",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="notification",
            name="title",
            field=models.CharField(max_length=200, verbose_name="Title"),
        ),
        migrations.AlterField(
            model_name="webhookdelivery",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="webhookendpoint",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["recipient", "is_read"], name="notificatio_recipie_4e3567_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["tenant_id", "created_at"],
                name="notificatio_tenant__b302ab_idx",
            ),
        ),
        migrations.AddField(
            model_name="notificationpreference",
            name="notification_types",
            field=models.ManyToManyField(
                blank=True,
                to="notifications.notificationtype",
                verbose_name="Notification Types",
            ),
        ),
        migrations.AddField(
            model_name="notificationpreference",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notification_preferences",
                to=settings.AUTH_USER_MODEL,
                verbose_name="User",
            ),
        ),
        migrations.AddField(
            model_name="actionitem",
            name="assigned_to",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="action_items",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Assigned To",
            ),
        ),
        migrations.AddField(
            model_name="actionitem",
            name="completed_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="completed_actions",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Completed By",
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="notification_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notifications",
                to="notifications.notificationtype",
                verbose_name="Notification Type",
            ),
        ),
        migrations.AddIndex(
            model_name="actionitem",
            index=models.Index(
                fields=["assigned_to", "status"], name="notificatio_assigne_229138_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="actionitem",
            index=models.Index(
                fields=["tenant_id", "due_date"], name="notificatio_tenant__eb6959_idx"
            ),
        ),
    ]
