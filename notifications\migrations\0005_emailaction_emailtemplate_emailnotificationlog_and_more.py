# Generated by Django 4.2.20 on 2025-06-14 07:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        (
            "notifications",
            "0004_actionitem_notificationpreference_notificationtype_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailAction",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="Action Token"
                    ),
                ),
                (
                    "action_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("approve", "Approve"),
                            ("reject", "Reject"),
                            ("assign", "Assign"),
                            ("complete", "Complete"),
                            ("cancel", "Cancel"),
                            ("view", "View Details"),
                            ("edit", "Edit"),
                            ("acknowledge", "Acknowledge"),
                        ],
                        max_length=20,
                        verbose_name="Action Type",
                    ),
                ),
                (
                    "action_label",
                    models.CharField(max_length=100, verbose_name="Action Label"),
                ),
                (
                    "action_url",
                    models.URLField(max_length=500, verbose_name="Action URL"),
                ),
                (
                    "related_object_type",
                    models.CharField(max_length=50, verbose_name="Related Object Type"),
                ),
                (
                    "related_object_id",
                    models.CharField(max_length=100, verbose_name="Related Object ID"),
                ),
                (
                    "requires_login",
                    models.BooleanField(default=True, verbose_name="Requires Login"),
                ),
                ("is_used", models.BooleanField(default=False, verbose_name="Is Used")),
                (
                    "used_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="Used At"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Expires At"
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(blank=True, default=dict, verbose_name="Metadata"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_actions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Action",
                "verbose_name_plural": "Email Actions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="EmailTemplate",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=200, verbose_name="Template Name"),
                ),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("work_order_created", "Work Order Created"),
                            ("work_order_assigned", "Work Order Assigned"),
                            ("work_order_in_progress", "Work Order In Progress"),
                            ("work_order_completed", "Work Order Completed"),
                            ("work_order_cancelled", "Work Order Cancelled"),
                            ("work_order_parts_needed", "Work Order Parts Needed"),
                            ("inventory_low_stock", "Inventory Low Stock"),
                            ("inventory_out_of_stock", "Inventory Out of Stock"),
                            ("inventory_reorder_point", "Inventory Reorder Point"),
                            ("purchase_order_created", "Purchase Order Created"),
                            ("purchase_order_approved", "Purchase Order Approved"),
                            ("purchase_order_rejected", "Purchase Order Rejected"),
                            ("purchase_order_received", "Purchase Order Received"),
                            ("sales_order_created", "Sales Order Created"),
                            ("sales_order_confirmed", "Sales Order Confirmed"),
                            ("sales_order_shipped", "Sales Order Shipped"),
                            ("sales_order_delivered", "Sales Order Delivered"),
                            ("invoice_generated", "Invoice Generated"),
                            ("payment_received", "Payment Received"),
                            ("transfer_request_created", "Transfer Request Created"),
                            ("transfer_request_approved", "Transfer Request Approved"),
                            ("transfer_request_rejected", "Transfer Request Rejected"),
                            ("transfer_completed", "Transfer Completed"),
                            ("report_generated", "Report Generated"),
                            ("report_scheduled", "Report Scheduled"),
                            ("task_assignment", "Task Assignment"),
                            ("approval_required", "Approval Required"),
                            ("system_alert", "System Alert"),
                        ],
                        max_length=50,
                        verbose_name="Template Type",
                    ),
                ),
                (
                    "target_roles",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of role codes that should receive this email",
                        verbose_name="Target Roles",
                    ),
                ),
                (
                    "subject_template",
                    models.CharField(
                        help_text="Use Django template syntax with context variables",
                        max_length=500,
                        verbose_name="Subject Template",
                    ),
                ),
                (
                    "body_template",
                    models.TextField(
                        help_text="HTML email body with Django template syntax",
                        verbose_name="Body Template",
                    ),
                ),
                (
                    "from_status",
                    models.CharField(
                        blank=True,
                        help_text="Previous status (leave blank for any)",
                        max_length=50,
                        verbose_name="From Status",
                    ),
                ),
                (
                    "to_status",
                    models.CharField(
                        blank=True,
                        help_text="New status (leave blank for any)",
                        max_length=50,
                        verbose_name="To Status",
                    ),
                ),
                (
                    "include_actions",
                    models.BooleanField(
                        default=False, verbose_name="Include Email Actions"
                    ),
                ),
                (
                    "action_buttons",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of action buttons to include in email",
                        verbose_name="Action Buttons",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "priority",
                    models.IntegerField(
                        default=0,
                        help_text="Higher priority templates override lower ones",
                        verbose_name="Priority",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Template",
                "verbose_name_plural": "Email Templates",
                "ordering": ["-priority", "name"],
                "unique_together": {
                    ("template_type", "from_status", "to_status", "tenant_id")
                },
            },
        ),
        migrations.CreateModel(
            name="EmailNotificationLog",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                ("subject", models.CharField(max_length=500, verbose_name="Subject")),
                ("body", models.TextField(verbose_name="Body")),
                (
                    "to_email",
                    models.EmailField(max_length=254, verbose_name="To Email"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                            ("bounced", "Bounced"),
                            ("opened", "Opened"),
                            ("clicked", "Clicked"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "sent_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="Sent At"),
                ),
                (
                    "opened_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Opened At"
                    ),
                ),
                (
                    "clicked_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Clicked At"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, verbose_name="Error Message"),
                ),
                (
                    "retry_count",
                    models.IntegerField(default=0, verbose_name="Retry Count"),
                ),
                (
                    "related_object_type",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Related Object Type"
                    ),
                ),
                (
                    "related_object_id",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Related Object ID"
                    ),
                ),
                (
                    "email_actions",
                    models.ManyToManyField(
                        blank=True,
                        related_name="email_logs",
                        to="notifications.emailaction",
                    ),
                ),
                (
                    "email_template",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="notifications.emailtemplate",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "related_notification",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="notifications.notification",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Notification Log",
                "verbose_name_plural": "Email Notification Logs",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["recipient", "status"],
                        name="notificatio_recipie_7a33a4_idx",
                    ),
                    models.Index(
                        fields=["tenant_id", "sent_at"],
                        name="notificatio_tenant__89fcbe_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="emailaction",
            index=models.Index(fields=["token"], name="notificatio_token_a12005_idx"),
        ),
        migrations.AddIndex(
            model_name="emailaction",
            index=models.Index(
                fields=["user", "is_used"], name="notificatio_user_id_a49c04_idx"
            ),
        ),
    ]
