from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet


class NotificationType(TimeStampedModel, UUIDPrimaryKeyModel):
    """Types of notifications in the system"""
    NOTIFICATION_TYPES = [
        ('work_order_status', _('Work Order Status Change')),
        ('inventory_low_stock', _('Low Stock Alert')),
        ('purchase_approval', _('Purchase Order Approval')),
        ('invoice_ready', _('Invoice Ready for Generation')),
        ('parts_request', _('Parts Request')),
        ('transfer_request', _('Transfer Request')),
        ('task_assignment', _('Task Assignment')),
        ('system_alert', _('System Alert')),
    ]
    
    name = models.CharField(_("Name"), max_length=100)
    code = models.CharField(_("Code"), max_length=50, unique=True)
    type = models.CharField(_("Type"), max_length=50, choices=NOTIFICATION_TYPES)
    description = models.TextField(_("Description"), blank=True)
    icon = models.CharField(_("Icon Class"), max_length=50, default='fas fa-bell')
    color = models.CharField(_("Color Class"), max_length=50, default='blue')
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Notification Type")
        verbose_name_plural = _("Notification Types")
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Notification(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Individual notifications for users"""
    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    ]
    
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name=_("Recipient"),
        null=True, blank=True  # Temporarily nullable for migration
    )
    notification_type = models.ForeignKey(
        NotificationType,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name=_("Notification Type"),
        null=True, blank=True  # Temporarily nullable for migration
    )
    title = models.CharField(_("Title"), max_length=200)
    message = models.TextField(_("Message"))
    priority = models.CharField(_("Priority"), max_length=20, choices=PRIORITY_CHOICES, default='medium')
    
    # Action-related fields
    action_required = models.BooleanField(_("Action Required"), default=False)
    action_url = models.URLField(_("Action URL"), blank=True, null=True)
    action_text = models.CharField(_("Action Text"), max_length=100, blank=True)
    
    # Related object information
    related_object_type = models.CharField(_("Related Object Type"), max_length=50, blank=True)
    related_object_id = models.CharField(_("Related Object ID"), max_length=100, blank=True)
    
    # Status fields
    is_read = models.BooleanField(_("Is Read"), default=False)
    is_dismissed = models.BooleanField(_("Is Dismissed"), default=False)
    read_at = models.DateTimeField(_("Read At"), null=True, blank=True)
    dismissed_at = models.DateTimeField(_("Dismissed At"), null=True, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Notification")
        verbose_name_plural = _("Notifications")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['tenant_id', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.recipient.username}"
    
    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = models.timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
    
    def dismiss(self):
        """Dismiss notification"""
        if not self.is_dismissed:
            self.is_dismissed = True
            self.dismissed_at = models.timezone.now()
            self.save(update_fields=['is_dismissed', 'dismissed_at'])


class ActionItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Action items that require user intervention"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]
    
    ACTION_TYPES = [
        ('approve_purchase', _('Approve Purchase Order')),
        ('generate_invoice', _('Generate Invoice')),
        ('approve_parts_request', _('Approve Parts Request')),
        ('process_transfer', _('Process Transfer')),
        ('assign_technician', _('Assign Technician')),
        ('update_work_order', _('Update Work Order Status')),
        ('review_inventory', _('Review Inventory')),
    ]
    
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='action_items',
        verbose_name=_("Assigned To")
    )
    action_type = models.CharField(_("Action Type"), max_length=50, choices=ACTION_TYPES)
    title = models.CharField(_("Title"), max_length=200)
    description = models.TextField(_("Description"))
    priority = models.CharField(_("Priority"), max_length=20, choices=Notification.PRIORITY_CHOICES, default='medium')
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Due date and completion tracking
    due_date = models.DateTimeField(_("Due Date"), null=True, blank=True)
    completed_at = models.DateTimeField(_("Completed At"), null=True, blank=True)
    completed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='completed_actions',
        verbose_name=_("Completed By")
    )
    
    # Related object information
    related_object_type = models.CharField(_("Related Object Type"), max_length=50, blank=True)
    related_object_id = models.CharField(_("Related Object ID"), max_length=100, blank=True)
    
    # Action URL and metadata
    action_url = models.URLField(_("Action URL"), blank=True, null=True)
    metadata = models.JSONField(_("Metadata"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Action Item")
        verbose_name_plural = _("Action Items")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['assigned_to', 'status']),
            models.Index(fields=['tenant_id', 'due_date']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.assigned_to.username}"
    
    def mark_completed(self, completed_by=None):
        """Mark action item as completed"""
        self.status = 'completed'
        self.completed_at = models.timezone.now()
        if completed_by:
            self.completed_by = completed_by
        self.save(update_fields=['status', 'completed_at', 'completed_by'])


class NotificationPreference(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """User preferences for notifications"""
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='notification_preferences',
        verbose_name=_("User")
    )
    
    # Email notifications
    email_enabled = models.BooleanField(_("Email Notifications"), default=True)
    email_frequency = models.CharField(
        _("Email Frequency"),
        max_length=20,
        choices=[
            ('immediate', _('Immediate')),
            ('hourly', _('Hourly')),
            ('daily', _('Daily')),
            ('weekly', _('Weekly')),
        ],
        default='immediate'
    )
    
    # In-app notifications
    browser_enabled = models.BooleanField(_("Browser Notifications"), default=True)
    sound_enabled = models.BooleanField(_("Sound Notifications"), default=True)
    
    # Notification types to receive
    notification_types = models.ManyToManyField(
        NotificationType,
        blank=True,
        verbose_name=_("Notification Types")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Notification Preference")
        verbose_name_plural = _("Notification Preferences")
    
    def __str__(self):
        return f"Preferences for {self.user.username}"


class WebhookEndpoint(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Configuration for webhook endpoints to receive notifications about events
    """
    name = models.CharField(_("Name"), max_length=100)
    url = models.URLField(_("URL"), max_length=500)
    description = models.TextField(_("Description"), blank=True)
    is_active = models.BooleanField(_("Active"), default=True)
    secret_key = models.CharField(_("Secret Key"), max_length=255, blank=True, 
                                help_text=_("Used for webhook signature validation"))
    
    # Event subscriptions
    subscribe_item_created = models.BooleanField(_("Item Created"), default=True)
    subscribe_item_updated = models.BooleanField(_("Item Updated"), default=True)
    subscribe_stock_low = models.BooleanField(_("Stock Low"), default=True)
    subscribe_movement_created = models.BooleanField(_("Movement Created"), default=True)
    
    class Meta:
        verbose_name = _("Webhook Endpoint")
        verbose_name_plural = _("Webhook Endpoints")
    
    def __str__(self):
        return self.name


class WebhookDelivery(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Record of webhook delivery attempts
    """
    endpoint = models.ForeignKey(WebhookEndpoint, on_delete=models.CASCADE, 
                              related_name='deliveries', verbose_name=_("Endpoint"))
    event_type = models.CharField(_("Event Type"), max_length=100)
    payload = models.JSONField(_("Payload"))
    response_status = models.IntegerField(_("Response Status"), null=True, blank=True)
    response_body = models.TextField(_("Response Body"), blank=True)
    is_success = models.BooleanField(_("Success"), default=False)
    attempts = models.IntegerField(_("Attempts"), default=0)
    
    class Meta:
        verbose_name = _("Webhook Delivery")
        verbose_name_plural = _("Webhook Deliveries")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.event_type} - {self.endpoint.name} - {self.created_at}"


class EmailTemplate(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Email templates for different notification types and status changes"""
    
    # Template types based on apps and their status changes
    TEMPLATE_TYPES = [
        # Work Orders
        ('work_order_created', _('Work Order Created')),
        ('work_order_assigned', _('Work Order Assigned')),
        ('work_order_in_progress', _('Work Order In Progress')),
        ('work_order_completed', _('Work Order Completed')),
        ('work_order_cancelled', _('Work Order Cancelled')),
        ('work_order_parts_needed', _('Work Order Parts Needed')),
        
        # Inventory
        ('inventory_low_stock', _('Inventory Low Stock')),
        ('inventory_out_of_stock', _('Inventory Out of Stock')),
        ('inventory_reorder_point', _('Inventory Reorder Point')),
        
        # Purchases
        ('purchase_order_created', _('Purchase Order Created')),
        ('purchase_order_approved', _('Purchase Order Approved')),
        ('purchase_order_rejected', _('Purchase Order Rejected')),
        ('purchase_order_received', _('Purchase Order Received')),
        
        # Sales
        ('sales_order_created', _('Sales Order Created')),
        ('sales_order_confirmed', _('Sales Order Confirmed')),
        ('sales_order_shipped', _('Sales Order Shipped')),
        ('sales_order_delivered', _('Sales Order Delivered')),
        ('invoice_generated', _('Invoice Generated')),
        ('payment_received', _('Payment Received')),
        
        # Warehouse
        ('transfer_request_created', _('Transfer Request Created')),
        ('transfer_request_approved', _('Transfer Request Approved')),
        ('transfer_request_rejected', _('Transfer Request Rejected')),
        ('transfer_completed', _('Transfer Completed')),
        
        # Reports
        ('report_generated', _('Report Generated')),
        ('report_scheduled', _('Report Scheduled')),
        
        # General
        ('task_assignment', _('Task Assignment')),
        ('approval_required', _('Approval Required')),
        ('system_alert', _('System Alert')),
    ]
    
    name = models.CharField(_("Template Name"), max_length=200)
    template_type = models.CharField(_("Template Type"), max_length=50, choices=TEMPLATE_TYPES)
    
    # Role-based targeting
    target_roles = models.JSONField(_("Target Roles"), default=list, blank=True,
                                   help_text=_("List of role codes that should receive this email"))
    
    # Email content
    subject_template = models.CharField(_("Subject Template"), max_length=500,
                                       help_text=_("Use Django template syntax with context variables"))
    body_template = models.TextField(_("Body Template"),
                                    help_text=_("HTML email body with Django template syntax"))
    
    # Status transition configuration
    from_status = models.CharField(_("From Status"), max_length=50, blank=True,
                                  help_text=_("Previous status (leave blank for any)"))
    to_status = models.CharField(_("To Status"), max_length=50, blank=True,
                                help_text=_("New status (leave blank for any)"))
    
    # Email actions configuration
    include_actions = models.BooleanField(_("Include Email Actions"), default=False)
    action_buttons = models.JSONField(_("Action Buttons"), default=list, blank=True,
                                     help_text=_("List of action buttons to include in email"))
    
    # Template settings
    is_active = models.BooleanField(_("Is Active"), default=True)
    priority = models.IntegerField(_("Priority"), default=0,
                                  help_text=_("Higher priority templates override lower ones"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Email Template")
        verbose_name_plural = _("Email Templates")
        ordering = ['-priority', 'name']
        unique_together = ['template_type', 'from_status', 'to_status', 'tenant_id']
    
    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"
    
    def render_subject(self, context):
        """Render the subject template with context"""
        from django.template import Template, Context
        template = Template(self.subject_template)
        return template.render(Context(context))
    
    def render_body(self, context):
        """Render the body template with context"""
        from django.template import Template, Context
        template = Template(self.body_template)
        return template.render(Context(context))


class EmailAction(TimeStampedModel, UUIDPrimaryKeyModel):
    """Email actions that can be taken directly from email"""
    
    ACTION_TYPES = [
        ('approve', _('Approve')),
        ('reject', _('Reject')),
        ('assign', _('Assign')),
        ('complete', _('Complete')),
        ('cancel', _('Cancel')),
        ('view', _('View Details')),
        ('edit', _('Edit')),
        ('acknowledge', _('Acknowledge')),
    ]
    
    # Unique token for email action links
    token = models.CharField(_("Action Token"), max_length=100, unique=True)
    
    # Action configuration
    action_type = models.CharField(_("Action Type"), max_length=20, choices=ACTION_TYPES)
    action_label = models.CharField(_("Action Label"), max_length=100)
    action_url = models.URLField(_("Action URL"), max_length=500)
    
    # Related objects
    related_object_type = models.CharField(_("Related Object Type"), max_length=50)
    related_object_id = models.CharField(_("Related Object ID"), max_length=100)
    
    # User and permissions
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='email_actions')
    requires_login = models.BooleanField(_("Requires Login"), default=True)
    
    # Status and expiry
    is_used = models.BooleanField(_("Is Used"), default=False)
    used_at = models.DateTimeField(_("Used At"), null=True, blank=True)
    expires_at = models.DateTimeField(_("Expires At"), null=True, blank=True)
    
    # Additional data
    metadata = models.JSONField(_("Metadata"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Email Action")
        verbose_name_plural = _("Email Actions")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user', 'is_used']),
        ]
    
    def __str__(self):
        return f"{self.action_label} - {self.user.username}"
    
    def is_expired(self):
        """Check if the action has expired"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
    
    def mark_used(self):
        """Mark the action as used"""
        from django.utils import timezone
        self.is_used = True
        self.used_at = timezone.now()
        self.save(update_fields=['is_used', 'used_at'])


class EmailNotificationLog(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Log of sent email notifications"""
    
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('sent', _('Sent')),
        ('failed', _('Failed')),
        ('bounced', _('Bounced')),
        ('opened', _('Opened')),
        ('clicked', _('Clicked')),
    ]
    
    # Email details
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='email_logs')
    email_template = models.ForeignKey(EmailTemplate, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Email content
    subject = models.CharField(_("Subject"), max_length=500)
    body = models.TextField(_("Body"))
    to_email = models.EmailField(_("To Email"))
    
    # Status tracking
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='pending')
    sent_at = models.DateTimeField(_("Sent At"), null=True, blank=True)
    opened_at = models.DateTimeField(_("Opened At"), null=True, blank=True)
    clicked_at = models.DateTimeField(_("Clicked At"), null=True, blank=True)
    
    # Error tracking
    error_message = models.TextField(_("Error Message"), blank=True)
    retry_count = models.IntegerField(_("Retry Count"), default=0)
    
    # Related objects
    related_notification = models.ForeignKey(Notification, on_delete=models.SET_NULL, null=True, blank=True)
    related_object_type = models.CharField(_("Related Object Type"), max_length=50, blank=True)
    related_object_id = models.CharField(_("Related Object ID"), max_length=100, blank=True)
    
    # Email actions included
    email_actions = models.ManyToManyField(EmailAction, blank=True, related_name='email_logs')
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Email Notification Log")
        verbose_name_plural = _("Email Notification Logs")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'status']),
            models.Index(fields=['tenant_id', 'sent_at']),
        ]
    
    def __str__(self):
        return f"{self.subject} - {self.to_email}"
