from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from django.utils import timezone
from .models import Notification, NotificationType, ActionItem, EmailNotificationLog
from user_roles.models import User<PERSON>ole
from typing import List, Optional, Dict, Any
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
import requests
from django.db import transaction

logger = logging.getLogger(__name__)


class NotificationService:
    """Enhanced notification service with action items and comprehensive tracking"""
    
    @classmethod
    def create_notification(cls, recipient, notification_type_code, title, message, 
                           priority='medium', action_required=False, action_url=None, 
                           action_text=None, related_object_type=None, related_object_id=None, 
                           tenant_id=None, metadata=None, send_email=True):
        """Create a new notification with enhanced options"""
        try:
            # Get or create notification type
            notification_type, created = NotificationType.objects.get_or_create(
                code=notification_type_code,
                defaults={
                    'name': notification_type_code.replace('_', ' ').title(),
                    'description': f'Notification type for {notification_type_code}'
                }
            )
            
            # Create the notification
            notification = Notification.objects.create(
                recipient=recipient,
                notification_type=notification_type,
                title=title,
                message=message,
                priority=priority,
                action_required=action_required,
                action_url=action_url,
                action_text=action_text or '',  # Ensure not None
                related_object_type=related_object_type or '',
                related_object_id=related_object_id or '',
                tenant_id=tenant_id
            )
            
            # Store metadata separately if provided (for email templates)
            if metadata:
                notification._metadata = metadata
            
            # Send email notification if requested and recipient has email
            if send_email and recipient.email:
                cls._send_email_notification(notification, metadata)
        
            logger.info(f"Created notification {notification.id} for {recipient.username}")
            return notification
        
        except Exception as e:
            logger.error(f"Error creating notification: {e}")
            return None

    @classmethod
    def create_action_item(cls, assigned_to, action_type, title, description, 
                          priority='medium', action_url=None, related_object_type=None, 
                          related_object_id=None, metadata=None, tenant_id=None, 
                          due_date=None):
        """Create a new action item that requires user action"""
        try:
            from .models import ActionItem  # Import here to avoid circular imports
            
            action_item = ActionItem.objects.create(
                assigned_to=assigned_to,
                action_type=action_type,
                title=title,
                description=description,
                priority=priority,
                action_url=action_url,
                related_object_type=related_object_type,
                related_object_id=related_object_id,
                metadata=metadata or {},
                tenant_id=tenant_id,
                due_date=due_date,
                status='pending'
            )
            
            logger.info(f'Created action item {action_item.id} for {assigned_to.username}')
            return action_item
            
        except Exception as e:
            logger.error(f"Error creating action item: {e}")
            return None

    @classmethod
    def handle_action(cls, action_item_id, action_type, user, item_action_type=None):
        """Handle action item responses (approve, deny, etc.)"""
        try:
            from .models import ActionItem
            
            with transaction.atomic():
                action_item = ActionItem.objects.select_for_update().get(id=action_item_id)
                
                # Update action item status
                if action_type in ['approve', 'approve_reorder', 'approve_new_item', 
                                 'approve_transfer', 'approve_purchase_order', 'create_po']:
                    action_item.status = 'approved'
                    action_item.resolved_by = user
                    action_item.resolved_at = timezone.now()
                    
                    # Handle specific approvals
                    success, message = cls._handle_approval_action(action_item, action_type, user)
                    
                elif action_type in ['deny', 'reject', 'reject_qc']:
                    action_item.status = 'rejected'
                    action_item.resolved_by = user
                    action_item.resolved_at = timezone.now()
                    
                    success, message = cls._handle_rejection_action(action_item, action_type, user)
                    
                elif action_type in ['modify', 'reduce_qty', 'adjust_levels']:
                    action_item.status = 'modified'
                    action_item.resolved_by = user
                    action_item.resolved_at = timezone.now()
                    
                    success, message = cls._handle_modification_action(action_item, action_type, user)
                    
                else:
                    # Custom action handling
                    success, message = cls._handle_custom_action(action_item, action_type, user)
                
                action_item.save()
                
                # Create follow-up notification if needed
                if success:
                    cls._create_action_follow_up_notification(action_item, action_type, user)
                
                return success, message
                
        except Exception as e:
            logger.error(f"Error handling action {action_type} for item {action_item_id}: {e}")
            return False, f"Error processing action: {str(e)}"

    @classmethod
    def _handle_approval_action(cls, action_item, action_type, user):
        """Handle approval actions"""
        try:
            if action_type == 'approve_reorder':
                # Trigger reorder process
                return cls._trigger_reorder(action_item, user)
                
            elif action_type == 'approve_new_item':
                # Approve new inventory item
                return cls._approve_new_item(action_item, user)
                
            elif action_type == 'approve_transfer':
                # Approve transfer order
                return cls._approve_transfer_order(action_item, user)
                
            elif action_type == 'approve_purchase_order':
                # Approve purchase order
                return cls._approve_purchase_order(action_item, user)
                
            elif action_type == 'create_po':
                # Create purchase order from reorder
                return cls._create_purchase_order_from_reorder(action_item, user)
                
            else:
                return True, "Action approved successfully"
                
        except Exception as e:
            logger.error(f"Error in approval action {action_type}: {e}")
            return False, f"Error processing approval: {str(e)}"

    @classmethod
    def _handle_rejection_action(cls, action_item, action_type, user):
        """Handle rejection actions"""
        try:
            # Common rejection handling
            related_object = cls._get_related_object(action_item)
            
            if related_object and hasattr(related_object, 'status'):
                related_object.status = 'rejected'
                related_object.save()
                
            return True, "Action rejected successfully"
            
        except Exception as e:
            logger.error(f"Error in rejection action {action_type}: {e}")
            return False, f"Error processing rejection: {str(e)}"

    @classmethod
    def _handle_modification_action(cls, action_item, action_type, user):
        """Handle modification actions"""
        try:
            if action_type == 'reduce_qty':
                # Handle quantity reduction
                return cls._reduce_quantity(action_item, user)
                
            elif action_type == 'adjust_levels':
                # Handle level adjustments
                return cls._adjust_levels(action_item, user)
                
            else:
                return True, "Modification action completed"
                
        except Exception as e:
            logger.error(f"Error in modification action {action_type}: {e}")
            return False, f"Error processing modification: {str(e)}"

    @classmethod
    def _handle_custom_action(cls, action_item, action_type, user):
        """Handle custom actions based on action type"""
        try:
            action_handlers = {
                'complete_receiving': cls._complete_receiving,
                'generate_invoice': cls._generate_invoice,
                'send_email': cls._send_supplier_email,
                'mark_sent': cls._mark_po_sent,
                'resolve_hold': cls._resolve_work_order_hold,
                'escalate': cls._escalate_issue,
                'execute_transfer': cls._execute_transfer,
                'accept': cls._accept_assignment,
                'decline': cls._decline_assignment,
            }
            
            handler = action_handlers.get(action_type)
            if handler:
                return handler(action_item, user)
            else:
                action_item.status = 'completed'
                return True, f"Custom action '{action_type}' completed"
                
        except Exception as e:
            logger.error(f"Error in custom action {action_type}: {e}")
            return False, f"Error processing custom action: {str(e)}"

    @classmethod
    def _get_related_object(cls, action_item):
        """Get the related object for an action item"""
        try:
            if not action_item.related_object_type or not action_item.related_object_id:
                return None
                
            from django.apps import apps
            
            # Map common object types to their models
            model_map = {
                'item': 'inventory.Item',
                'purchase_order': 'purchases.PurchaseOrder',
                'transfer_order': 'warehouse.TransferOrder',
                'work_order': 'work_orders.WorkOrder',
                'movement': 'inventory.Movement',
            }
            
            model_path = model_map.get(action_item.related_object_type)
            if model_path:
                app_label, model_name = model_path.split('.')
                model = apps.get_model(app_label, model_name)
                return model.objects.get(id=action_item.related_object_id)
                
            return None
            
        except Exception as e:
            logger.error(f"Error getting related object: {e}")
            return None

    @classmethod
    def _trigger_reorder(cls, action_item, user):
        """Trigger automatic reorder process"""
        try:
            item = cls._get_related_object(action_item)
            if not item:
                return False, "Item not found"
                
            # Create purchase order for reorder
            from purchases.models import PurchaseOrder, PurchaseOrderItem
            
            # Get default supplier for item (implement based on your model structure)
            supplier = getattr(item, 'preferred_supplier', None)
            if not supplier:
                return False, "No supplier configured for this item"
                
            po = PurchaseOrder.objects.create(
                supplier=supplier,
                status='pending',
                requested_by=user,
                notes=f"Automatic reorder triggered for {item.name}",
                tenant_id=action_item.tenant_id
            )
            
            PurchaseOrderItem.objects.create(
                purchase_order=po,
                item=item,
                quantity=action_item.metadata.get('suggested_quantity', item.reorder_quantity or 50),
                tenant_id=action_item.tenant_id
            )
            
            return True, f"Purchase order {po.id} created for reorder"
            
        except Exception as e:
            logger.error(f"Error triggering reorder: {e}")
            return False, "Failed to create reorder"

    @classmethod
    def _approve_new_item(cls, action_item, user):
        """Approve new inventory item"""
        try:
            item = cls._get_related_object(action_item)
            if not item:
                return False, "Item not found"
                
            # Mark item as approved (implement based on your model structure)
            if hasattr(item, 'is_approved'):
                item.is_approved = True
                item.approved_by = user
                item.approved_at = timezone.now()
                item.save()
                
            return True, f"Item {item.name} approved successfully"
            
        except Exception as e:
            logger.error(f"Error approving item: {e}")
            return False, "Failed to approve item"

    @classmethod
    def _approve_transfer_order(cls, action_item, user):
        """Approve transfer order"""
        try:
            transfer_order = cls._get_related_object(action_item)
            if not transfer_order:
                return False, "Transfer order not found"
                
            transfer_order.status = 'approved'
            transfer_order.approved_by = user
            transfer_order.approved_at = timezone.now()
            transfer_order.save()
            
            return True, f"Transfer order approved successfully"
            
        except Exception as e:
            logger.error(f"Error approving transfer order: {e}")
            return False, "Failed to approve transfer order"

    @classmethod
    def _approve_purchase_order(cls, action_item, user):
        """Approve purchase order"""
        try:
            purchase_order = cls._get_related_object(action_item)
            if not purchase_order:
                return False, "Purchase order not found"
                
            purchase_order.status = 'approved'
            purchase_order.approved_by = user
            purchase_order.approved_at = timezone.now()
            purchase_order.save()
            
            return True, f"Purchase order approved successfully"
            
        except Exception as e:
            logger.error(f"Error approving purchase order: {e}")
            return False, "Failed to approve purchase order"

    @classmethod
    def _create_action_follow_up_notification(cls, action_item, action_type, user):
        """Create follow-up notifications after actions are completed"""
        try:
            # Notify the original requester about the action result
            if hasattr(action_item, 'created_by') and action_item.created_by != user:
                cls.create_notification(
                    recipient=action_item.created_by,
                    notification_type_code='action_completed',
                    title=f"Action Completed: {action_item.title}",
                    message=f"Your request has been {action_item.status} by {user.get_full_name()}",
                    priority='low',
                    tenant_id=action_item.tenant_id
                )
                
        except Exception as e:
            logger.error(f"Error creating follow-up notification: {e}")

    @classmethod
    def get_user_notifications(cls, user, limit=20, unread_only=False):
        """Get notifications for a user"""
        try:
            queryset = Notification.objects.filter(
                recipient=user,
                tenant_id=user.userprofile.tenant_id if hasattr(user, 'userprofile') else None
            ).order_by('-created_at')
            
            if unread_only:
                queryset = queryset.filter(is_read=False)
                
            return queryset[:limit]
            
        except Exception as e:
            logger.error(f"Error getting user notifications: {e}")
            return []
    
    @classmethod
    def get_user_action_items(cls, user, limit=20, status='pending'):
        """Get action items for a user"""
        try:
            from .models import ActionItem
            
            queryset = ActionItem.objects.filter(
                assigned_to=user,
                tenant_id=user.userprofile.tenant_id if hasattr(user, 'userprofile') else None
            ).order_by('-created_at')
            
            if status:
                queryset = queryset.filter(status=status)
            
            return queryset[:limit]
            
        except Exception as e:
            logger.error(f"Error getting user action items: {e}")
            return []

    @classmethod
    def mark_notification_read(cls, notification_id, user):
        """Mark a notification as read"""
        try:
            notification = Notification.objects.get(id=notification_id, recipient=user)
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save()
            return True
            
        except Notification.DoesNotExist:
            return False
        except Exception as e:
            logger.error(f"Error marking notification as read: {e}")
            return False

    @classmethod
    def mark_all_notifications_read(cls, user):
        """Mark all notifications as read for a user"""
        try:
            Notification.objects.filter(
                recipient=user,
                is_read=False
            ).update(
                is_read=True,
                read_at=timezone.now()
            )
            return True
            
        except Exception as e:
            logger.error(f"Error marking all notifications as read: {e}")
            return False
    
    @classmethod
    def _send_email_notification(cls, notification, metadata=None):
        """Send email notification using appropriate template"""
        try:
            # Map notification types to email templates
            template_mapping = {
                'work_order_created': 'notifications/emails/work_order_created.html',
                'work_order_status_change': 'notifications/emails/work_order_status_change.html',
                'work_order_status': 'notifications/emails/work_order_status_change.html',
                'parts_request': 'notifications/emails/parts_request.html',
                'work_order_assigned': 'notifications/emails/work_order_status_change.html',
                'work_order_started': 'notifications/emails/work_order_status_change.html',
                'work_order_completed': 'notifications/emails/work_order_status_change.html',
            }
            
            # Get the template for this notification type
            template_name = template_mapping.get(
                notification.notification_type.code, 
                'notifications/emails/work_order_status_change.html'
            )
            
            # Prepare template context
            context = {
                'notification': notification,
                'recipient': notification.recipient,
                'title': notification.title,
                'message': notification.message,
                'action_url': notification.action_url,
                'metadata': metadata or {},
            }
            
            # Add work order specific context if available
            if hasattr(notification, '_metadata') and notification._metadata:
                context.update(notification._metadata)
            
            # Get work order from related object if available
            if notification.related_object_type == 'work_order' and notification.related_object_id:
                try:
                    from work_orders.models import WorkOrder, WorkOrderMaterial
                    work_order = WorkOrder.objects.get(id=notification.related_object_id)
                    context['work_order'] = work_order
                    
                    # Add materials if this is a parts request
                    if 'parts_request' in notification.notification_type.code:
                        materials = WorkOrderMaterial.objects.filter(work_order=work_order)
                        context['materials'] = materials
                        
                except Exception as e:
                    logger.warning(f"Could not load work order for notification {notification.id}: {e}")
            
            # Render email template
            html_content = render_to_string(template_name, context)
            text_content = strip_tags(html_content)
            
            # Send email
            success = send_notification_email(
                to_email=notification.recipient.email,
                subject=notification.title,
                message=text_content,
                html_message=html_content,
                tenant_id=notification.tenant_id
            )
            
            if success:
                logger.info(f"Sent email notification to {notification.recipient.email}")
            else:
                logger.error(f"Failed to send email notification to {notification.recipient.email}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error sending email notification: {e}")
            return False


class WorkflowNotificationService:
    """Service for workflow-specific notifications"""
    
    @staticmethod
    def setup_default_notification_types():
        """Create default notification types"""
        default_types = [
            {
                'name': 'Work Order Status Change',
                'code': 'work_order_status',
                'type': 'work_order_status',
                'icon': 'fas fa-tasks',
                'color': 'blue'
            },
            {
                'name': 'Low Stock Alert',
                'code': 'inventory_low_stock',
                'type': 'inventory_low_stock',
                'icon': 'fas fa-exclamation-triangle',
                'color': 'red'
            },
            {
                'name': 'Purchase Approval Required',
                'code': 'purchase_approval',
                'type': 'purchase_approval',
                'icon': 'fas fa-shopping-cart',
                'color': 'orange'
            },
            {
                'name': 'Invoice Ready',
                'code': 'invoice_ready',
                'type': 'invoice_ready',
                'icon': 'fas fa-file-invoice',
                'color': 'green'
            },
            {
                'name': 'Parts Request',
                'code': 'parts_request',
                'type': 'parts_request',
                'icon': 'fas fa-cogs',
                'color': 'purple'
            },
            {
                'name': 'Transfer Request',
                'code': 'transfer_request',
                'type': 'transfer_request',
                'icon': 'fas fa-exchange-alt',
                'color': 'indigo'
            },
            {
                'name': 'Task Assignment',
                'code': 'task_assignment',
                'type': 'task_assignment',
                'icon': 'fas fa-user-check',
                'color': 'teal'
            },
            {
                'name': 'System Alert',
                'code': 'system_alert',
                'type': 'system_alert',
                'icon': 'fas fa-bell',
                'color': 'gray'
            },
        ]
        
        for type_data in default_types:
            NotificationType.objects.get_or_create(
                code=type_data['code'],
                defaults=type_data
            ) 


# Existing email service methods remain the same
def send_notification_email(to_email, subject, message, html_message=None, tenant_id=None):
    """
    Send notification email using Django's email backend
    """
    user = None
    user_tenant_id = tenant_id
    
    try:
        from django.contrib.auth.models import User
        
        # Try to find user by email
        try:
            user = User.objects.get(email=to_email)
            user_tenant_id = tenant_id or getattr(user, 'tenant_id', None)
        except User.DoesNotExist:
            user = None
            user_tenant_id = tenant_id
        
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[to_email],
            html_message=html_message,
            fail_silently=False
        )
        
        # Log the email with tenant_id
        if user_tenant_id:
            EmailNotificationLog.objects.create(
                recipient=user,
                to_email=to_email,
                subject=subject,
                body=message,
                status='sent',
                sent_at=timezone.now(),
                tenant_id=user_tenant_id
            )
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to send email to {to_email}: {str(e)}")
        
        # Log the failed email with tenant_id
        if user_tenant_id:
            EmailNotificationLog.objects.create(
                recipient=user,
                to_email=to_email,
                subject=subject,
                body=message,
                status='failed',
                error_message=str(e),
                tenant_id=user_tenant_id
            )
        
        return False


def send_webhook(event_type, payload, tenant_id=None):
    """
    Send webhook notification to configured endpoints
    """
    try:
        # Get webhook endpoints for the tenant
        webhook_endpoints = getattr(settings, 'WEBHOOK_ENDPOINTS', {})
        
        if not webhook_endpoints:
            logger.warning("No webhook endpoints configured")
            return False
            
        webhook_data = {
            'event_type': event_type,
            'timestamp': timezone.now().isoformat(),
            'tenant_id': str(tenant_id) if tenant_id else None,
            'payload': payload
        }
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Service-Management-System/1.0'
        }
        
        # Add authentication if configured
        webhook_secret = getattr(settings, 'WEBHOOK_SECRET', None)
        if webhook_secret:
            headers['X-Webhook-Secret'] = webhook_secret
            
        success_count = 0
        
        for endpoint_name, endpoint_url in webhook_endpoints.items():
            try:
                response = requests.post(
                    endpoint_url,
                    json=webhook_data,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code in [200, 201, 202]:
                    success_count += 1
                    logger.info(f"Successfully sent webhook to {endpoint_name}")
                else:
                    logger.warning(f"Webhook to {endpoint_name} returned status {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Failed to send webhook to {endpoint_name}: {str(e)}")
                
        return success_count > 0
        
    except Exception as e:
        logger.error(f"Error sending webhook for event {event_type}: {str(e)}")
        return False


def create_notification(tenant_id, title, message, level='info', object_type=None, object_id=None, attributes=None):
    """
    Create a system notification (deprecated - use NotificationService.create_notification instead)
    """
    try:
        # This is kept for backward compatibility
        logger.info(f"Creating notification: {title} - {message}")
        
        # You could store these in a database table or send to external service
        notification_data = {
            'tenant_id': str(tenant_id) if tenant_id else None,
            'title': title,
            'message': message,
            'level': level,
            'object_type': object_type,
            'object_id': str(object_id) if object_id else None,
            'attributes': attributes or {},
            'created_at': timezone.now().isoformat()
        }
        
        # Send as webhook
        return send_webhook('notification_created', notification_data, tenant_id)
        
    except Exception as e:
        logger.error(f"Error creating notification: {str(e)}")
        return False 