{% extends 'core/base.html' %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md">
        <!-- Header -->
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-envelope text-blue-600 mr-2"></i>
                    {% trans 'Email Logs' %}
                </h1>
                <div class="flex space-x-2">
                    <a href="{% url 'notifications:notification_center' %}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        {% trans 'Back to Notifications' %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <form method="get" class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <label for="status" class="text-sm font-medium text-gray-700">{% trans 'Status:' %}</label>
                    <select name="status" id="status" class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                        <option value="">{% trans 'All' %}</option>
                        <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>{% trans 'Pending' %}</option>
                        <option value="sent" {% if current_status == 'sent' %}selected{% endif %}>{% trans 'Sent' %}</option>
                        <option value="failed" {% if current_status == 'failed' %}selected{% endif %}>{% trans 'Failed' %}</option>
                        <option value="opened" {% if current_status == 'opened' %}selected{% endif %}>{% trans 'Opened' %}</option>
                    </select>
                </div>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded-md text-sm transition-colors">
                    {% trans 'Filter' %}
                </button>
            </form>
        </div>

        <!-- Email Logs List -->
        <div class="divide-y divide-gray-200">
            {% for log in email_logs %}
            <div class="px-6 py-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <h3 class="text-lg font-medium text-gray-900">{{ log.subject }}</h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if log.status == 'sent' %}bg-green-100 text-green-800
                                {% elif log.status == 'failed' %}bg-red-100 text-red-800
                                {% elif log.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif log.status == 'opened' %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {% if log.status == 'sent' %}
                                    <i class="fas fa-check-circle mr-1"></i>
                                {% elif log.status == 'failed' %}
                                    <i class="fas fa-times-circle mr-1"></i>
                                {% elif log.status == 'pending' %}
                                    <i class="fas fa-clock mr-1"></i>
                                {% elif log.status == 'opened' %}
                                    <i class="fas fa-envelope-open mr-1"></i>
                                {% endif %}
                                {{ log.get_status_display }}
                            </span>
                        </div>
                        
                        <div class="text-sm text-gray-600 space-y-1">
                            <p><strong>{% trans 'To:' %}</strong> {{ log.to_email }}</p>
                            <p><strong>{% trans 'Template:' %}</strong> 
                                {% if log.email_template %}
                                    {{ log.email_template.name }}
                                {% else %}
                                    {% trans 'Custom' %}
                                {% endif %}
                            </p>
                            <p><strong>{% trans 'Created:' %}</strong> {{ log.created_at|date:"Y-m-d H:i" }}</p>
                            {% if log.sent_at %}
                            <p><strong>{% trans 'Sent:' %}</strong> {{ log.sent_at|date:"Y-m-d H:i" }}</p>
                            {% endif %}
                            {% if log.opened_at %}
                            <p><strong>{% trans 'Opened:' %}</strong> {{ log.opened_at|date:"Y-m-d H:i" }}</p>
                            {% endif %}
                            {% if log.error_message %}
                            <p class="text-red-600"><strong>{% trans 'Error:' %}</strong> {{ log.error_message }}</p>
                            {% endif %}
                        </div>
                        
                        {% if log.email_actions.exists %}
                        <div class="mt-3">
                            <p class="text-sm font-medium text-gray-700 mb-2">{% trans 'Email Actions:' %}</p>
                            <div class="flex flex-wrap gap-2">
                                {% for action in log.email_actions.all %}
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium
                                    {% if action.is_used %}bg-green-100 text-green-800
                                    {% elif action.is_expired %}bg-red-100 text-red-800
                                    {% else %}bg-blue-100 text-blue-800{% endif %}">
                                    {{ action.action_label }}
                                    {% if action.is_used %}
                                        <i class="fas fa-check ml-1"></i>
                                    {% elif action.is_expired %}
                                        <i class="fas fa-times ml-1"></i>
                                    {% endif %}
                                </span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="flex items-center space-x-2 ml-4">
                        {% if log.status == 'failed' and log.retry_count < 3 %}
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            <i class="fas fa-redo mr-1"></i>
                            {% trans 'Retry' %}
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="px-6 py-8 text-center">
                <i class="fas fa-envelope text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500 text-lg">{% trans 'No email logs found' %}</p>
                <p class="text-gray-400 text-sm">{% trans 'Email logs will appear here when emails are sent' %}</p>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if email_logs.has_other_pages %}
        <div class="px-6 py-4 border-t border-gray-200">
            <nav class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if email_logs.has_previous %}
                    <a href="?page={{ email_logs.previous_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans 'Previous' %}
                    </a>
                    {% endif %}
                    {% if email_logs.has_next %}
                    <a href="?page={{ email_logs.next_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans 'Next' %}
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {% trans 'Showing' %}
                            <span class="font-medium">{{ email_logs.start_index }}</span>
                            {% trans 'to' %}
                            <span class="font-medium">{{ email_logs.end_index }}</span>
                            {% trans 'of' %}
                            <span class="font-medium">{{ email_logs.paginator.count }}</span>
                            {% trans 'results' %}
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if email_logs.has_previous %}
                            <a href="?page={{ email_logs.previous_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            {% endif %}
                            
                            {% for num in email_logs.paginator.page_range %}
                                {% if email_logs.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                                {% else %}
                                <a href="?page={{ num }}{% if current_status %}&status={{ current_status }}{% endif %}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if email_logs.has_next %}
                            <a href="?page={{ email_logs.next_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}