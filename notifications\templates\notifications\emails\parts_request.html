<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب قطع غيار</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
            color: #1e293b;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }
        .content {
            padding: 30px;
        }
        .work-order-info {
            background-color: #fef2f2;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #dc2626;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #475569;
        }
        .info-value {
            color: #1e293b;
        }
        .parts-list {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .parts-list h3 {
            margin-top: 0;
            color: #dc2626;
        }
        .part-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .part-item:last-child {
            border-bottom: none;
        }
        .part-name {
            font-weight: 600;
        }
        .part-quantity {
            color: #dc2626;
            font-weight: 600;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }
        .footer {
            background-color: #f8fafc;
            padding: 20px 30px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        .urgent {
            background-color: #fef2f2;
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .urgent h3 {
            color: #dc2626;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 طلب قطع غيار عاجل</h1>
        </div>
        
        <div class="content">
            <p>مرحباً {{ recipient.get_full_name|default:recipient.username }}،</p>
            
            <div class="urgent">
                <h3>طلب قطع غيار عاجل</h3>
                <p>تم تحويل أمر العمل إلى حالة "قيد التنفيذ" ويحتاج إلى قطع غيار من المستودع.</p>
            </div>
            
            <div class="work-order-info">
                <div class="info-row">
                    <span class="info-label">رقم أمر العمل:</span>
                    <span class="info-value">{{ work_order.work_order_number|default:work_order.id }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">العميل:</span>
                    <span class="info-value">{{ work_order.customer.full_name|default:'غير محدد' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">المركبة:</span>
                    <span class="info-value">
                        {% if work_order.vehicle %}
                            {{ work_order.vehicle.make }} {{ work_order.vehicle.model }} ({{ work_order.vehicle.year }})
                        {% else %}
                            غير محدد
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الفني المسؤول:</span>
                    <span class="info-value">{{ work_order.assigned_technician.get_full_name|default:'غير محدد' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الأولوية:</span>
                    <span class="info-value">
                        {% if work_order.priority == 'high' %}عالية 🔴
                        {% elif work_order.priority == 'medium' %}متوسطة 🟡
                        {% elif work_order.priority == 'low' %}منخفضة 🟢
                        {% else %}{{ work_order.priority }}
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">تاريخ بدء العمل:</span>
                    <span class="info-value">{{ work_order.actual_start_date|date:"Y-m-d H:i"|default:'الآن' }}</span>
                </div>
            </div>
            
            {% if materials %}
            <div class="parts-list">
                <h3>قطع الغيار المطلوبة:</h3>
                {% for material in materials %}
                <div class="part-item">
                    <div class="part-name">{{ material.item.name }}</div>
                    <div class="part-quantity">الكمية: {{ material.quantity }}</div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% if message %}
            <div style="background-color: #eff6ff; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <strong>ملاحظات إضافية:</strong>
                <p>{{ message }}</p>
            </div>
            {% endif %}
            
            <div style="background-color: #fef3c7; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <strong>الإجراءات المطلوبة:</strong>
                <ul>
                    <li>التحقق من توفر القطع في المستودع</li>
                    <li>حجز القطع المطلوبة</li>
                    <li>إشعار الفني بجاهزية القطع</li>
                    <li>في حالة عدم التوفر، إنشاء أمر شراء عاجل</li>
                </ul>
            </div>
            
            {% if action_url %}
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ action_url }}" class="action-button">إدارة طلب القطع</a>
            </div>
            {% endif %}
            
            <p><strong>يرجى التعامل مع هذا الطلب بشكل عاجل لتجنب تأخير العمل.</strong></p>
        </div>
        
        <div class="footer">
            <p>تم إرسال هذا البريد الإلكتروني تلقائياً من نظام إدارة الصيانة</p>
            <p>© 2025 نظام إدارة الصيانة. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html> 