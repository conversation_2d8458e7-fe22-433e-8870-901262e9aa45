<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمر عمل جديد</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
            color: #1e293b;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }
        .content {
            padding: 30px;
        }
        .work-order-info {
            background-color: #f0fdf4;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #059669;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #475569;
        }
        .info-value {
            color: #1e293b;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }
        .footer {
            background-color: #f8fafc;
            padding: 20px 30px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        .priority-high { border-left: 4px solid #dc2626; }
        .priority-medium { border-left: 4px solid #d97706; }
        .priority-low { border-left: 4px solid #059669; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ أمر عمل جديد</h1>
        </div>
        
        <div class="content">
            <p>مرحباً {{ recipient.get_full_name|default:recipient.username }}،</p>
            
            <p>تم إنشاء أمر عمل جديد في النظام:</p>
            
            <div class="work-order-info priority-{{ work_order.priority|default:'medium' }}">
                <div class="info-row">
                    <span class="info-label">رقم أمر العمل:</span>
                    <span class="info-value">{{ work_order.work_order_number|default:work_order.id }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">العميل:</span>
                    <span class="info-value">{{ work_order.customer.full_name|default:'غير محدد' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">المركبة:</span>
                    <span class="info-value">
                        {% if work_order.vehicle %}
                            {{ work_order.vehicle.make }} {{ work_order.vehicle.model }} ({{ work_order.vehicle.year }})
                        {% else %}
                            غير محدد
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الأولوية:</span>
                    <span class="info-value">
                        {% if work_order.priority == 'high' %}عالية 🔴
                        {% elif work_order.priority == 'medium' %}متوسطة 🟡
                        {% elif work_order.priority == 'low' %}منخفضة 🟢
                        {% else %}{{ work_order.priority }}
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">
                        {% if work_order.status == 'planned' %}قيد التخطيط
                        {% elif work_order.status == 'in_progress' %}قيد التنفيذ
                        {% elif work_order.status == 'completed' %}مكتمل
                        {% elif work_order.status == 'on_hold' %}متوقف
                        {% elif work_order.status == 'cancelled' %}ملغي
                        {% else %}{{ work_order.status }}
                        {% endif %}
                    </span>
                </div>
                
                {% if work_order.estimated_cost %}
                <div class="info-row">
                    <span class="info-label">التكلفة المقدرة:</span>
                    <span class="info-value">{{ work_order.estimated_cost }} ريال</span>
                </div>
                {% endif %}
                
                <div class="info-row">
                    <span class="info-label">تاريخ الإنشاء:</span>
                    <span class="info-value">{{ work_order.created_at|date:"Y-m-d H:i" }}</span>
                </div>
                
                {% if work_order.description %}
                <div class="info-row">
                    <span class="info-label">وصف العمل:</span>
                    <span class="info-value">{{ work_order.description }}</span>
                </div>
                {% endif %}
            </div>
            
            {% if message %}
            <div style="background-color: #eff6ff; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <strong>تفاصيل إضافية:</strong>
                <p>{{ message }}</p>
            </div>
            {% endif %}
            
            {% if action_url %}
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ action_url }}" class="action-button">عرض تفاصيل أمر العمل</a>
            </div>
            {% endif %}
            
            <p>يرجى متابعة أمر العمل واتخاذ الإجراء المناسب.</p>
        </div>
        
        <div class="footer">
            <p>تم إرسال هذا البريد الإلكتروني تلقائياً من نظام إدارة الصيانة</p>
            <p>© 2025 نظام إدارة الصيانة. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html> 