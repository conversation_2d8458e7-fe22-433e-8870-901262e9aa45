<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تغيير حالة أمر العمل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
            color: #1e293b;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }
        .content {
            padding: 30px;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            margin: 10px 0;
        }
        .status-planned { background-color: #e0e7ff; color: #3730a3; }
        .status-in_progress { background-color: #fef3c7; color: #92400e; }
        .status-completed { background-color: #d1fae5; color: #065f46; }
        .status-on_hold { background-color: #fee2e2; color: #991b1b; }
        .status-cancelled { background-color: #f3f4f6; color: #374151; }
        .work-order-info {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #475569;
        }
        .info-value {
            color: #1e293b;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        .footer {
            background-color: #f8fafc;
            padding: 20px 30px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        .priority-high { border-left: 4px solid #dc2626; }
        .priority-medium { border-left: 4px solid #d97706; }
        .priority-low { border-left: 4px solid #059669; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>تغيير حالة أمر العمل</h1>
        </div>
        
        <div class="content">
            <p>مرحباً {{ recipient.get_full_name|default:recipient.username }}،</p>
            
            <p>تم تغيير حالة أمر العمل الخاص بك:</p>
            
            <div class="work-order-info priority-{{ priority|default:'medium' }}">
                <div class="info-row">
                    <span class="info-label">رقم أمر العمل:</span>
                    <span class="info-value">{{ work_order.work_order_number|default:work_order.id }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">العميل:</span>
                    <span class="info-value">{{ work_order.customer.full_name|default:'غير محدد' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">المركبة:</span>
                    <span class="info-value">
                        {% if work_order.vehicle %}
                            {{ work_order.vehicle.make }} {{ work_order.vehicle.model }} ({{ work_order.vehicle.year }})
                        {% else %}
                            غير محدد
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الحالة السابقة:</span>
                    <span class="status-badge status-{{ old_status }}">
                        {% if old_status == 'planned' %}قيد التخطيط
                        {% elif old_status == 'in_progress' %}قيد التنفيذ
                        {% elif old_status == 'completed' %}مكتمل
                        {% elif old_status == 'on_hold' %}متوقف
                        {% elif old_status == 'cancelled' %}ملغي
                        {% else %}{{ old_status }}
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">الحالة الحالية:</span>
                    <span class="status-badge status-{{ new_status }}">
                        {% if new_status == 'planned' %}قيد التخطيط
                        {% elif new_status == 'in_progress' %}قيد التنفيذ
                        {% elif new_status == 'completed' %}مكتمل
                        {% elif new_status == 'on_hold' %}متوقف
                        {% elif new_status == 'cancelled' %}ملغي
                        {% else %}{{ new_status }}
                        {% endif %}
                    </span>
                </div>
                
                {% if work_order.assigned_technician %}
                <div class="info-row">
                    <span class="info-label">الفني المسؤول:</span>
                    <span class="info-value">{{ work_order.assigned_technician.get_full_name }}</span>
                </div>
                {% endif %}
                
                {% if work_order.estimated_cost %}
                <div class="info-row">
                    <span class="info-label">التكلفة المقدرة:</span>
                    <span class="info-value">{{ work_order.estimated_cost }} ريال</span>
                </div>
                {% endif %}
                
                <div class="info-row">
                    <span class="info-label">تاريخ التحديث:</span>
                    <span class="info-value">{{ work_order.updated_at|date:"Y-m-d H:i" }}</span>
                </div>
            </div>
            
            {% if message %}
            <div style="background-color: #eff6ff; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <strong>تفاصيل إضافية:</strong>
                <p>{{ message }}</p>
            </div>
            {% endif %}
            
            {% if action_url %}
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ action_url }}" class="action-button">عرض تفاصيل أمر العمل</a>
            </div>
            {% endif %}
            
            <p>شكراً لك على استخدام نظامنا.</p>
        </div>
        
        <div class="footer">
            <p>تم إرسال هذا البريد الإلكتروني تلقائياً من نظام إدارة الصيانة</p>
            <p>© 2025 نظام إدارة الصيانة. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html> 