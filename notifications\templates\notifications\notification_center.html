{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .notification-item {
        transition: all 0.3s ease;
    }
    .notification-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .notification-unread {
        border-left: 4px solid #3b82f6;
        background: linear-gradient(to right, #eff6ff, #ffffff);
    }
    .priority-urgent {
        border-left-color: #dc2626 !important;
    }
    .priority-high {
        border-left-color: #ea580c !important;
    }
    .priority-medium {
        border-left-color: #ca8a04 !important;
    }
    .priority-low {
        border-left-color: #16a34a !important;
    }
    .action-item {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 1px solid #cbd5e1;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">
                <i class="fas fa-bell text-blue-600 mr-3"></i>
                {% trans "مركز الإشعارات" %}
            </h1>
            <p class="text-gray-600">{% trans "إدارة الإشعارات والصيانه المطلوبة" %}</p>
        </div>
        
        <div class="flex space-x-3 mt-4 md:mt-0">
            <button onclick="markAllAsRead()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-check-double mr-2"></i>
                {% trans "تحديد الكل كمقروء" %}
            </button>
            <a href="{% url 'notifications:action_center' %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-tasks mr-2"></i>
                {% trans "مركز الصيانه" %}
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-bell text-blue-600 text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{% trans "إشعارات غير مقروءة" %}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ unread_count }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 border-l-4 border-orange-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-orange-600 text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{% trans "مهام معلقة" %}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ total_pending_actions }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 border-l-4 border-yellow-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-spinner text-yellow-600 text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{% trans "مهام قيد التنفيذ" %}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ total_in_progress_actions }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{% trans "إجمالي الإشعارات" %}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ notifications.paginator.count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    {% if pending_actions or in_progress_actions %}
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-tasks text-green-600 mr-2"></i>
                {% trans "الصيانه السريعة" %}
            </h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Pending Actions -->
                {% if pending_actions %}
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-clock text-orange-500 mr-2"></i>
                        {% trans "مهام معلقة" %}
                    </h3>
                    <div class="space-y-3">
                        {% for action in pending_actions %}
                        <div class="action-item p-4 rounded-lg">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900">{{ action.title }}</h4>
                                    <p class="text-sm text-gray-600 mt-1">{{ action.description|truncatechars:80 }}</p>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span class="priority-badge priority-{{ action.priority }} px-2 py-1 rounded">
                                            {{ action.get_priority_display }}
                                        </span>
                                        <span class="mx-2">•</span>
                                        <span>{{ action.created_at|timesince }} {% trans "ago" %}</span>
                                    </div>
                                </div>
                                {% if action.action_url %}
                                <a href="{{ action.action_url }}" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
                                    {% trans "عرض" %}
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- In Progress Actions -->
                {% if in_progress_actions %}
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-spinner text-yellow-500 mr-2"></i>
                        {% trans "مهام قيد التنفيذ" %}
                    </h3>
                    <div class="space-y-3">
                        {% for action in in_progress_actions %}
                        <div class="action-item p-4 rounded-lg">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900">{{ action.title }}</h4>
                                    <p class="text-sm text-gray-600 mt-1">{{ action.description|truncatechars:80 }}</p>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span class="priority-badge priority-{{ action.priority }} px-2 py-1 rounded">
                                            {{ action.get_priority_display }}
                                        </span>
                                        <span class="mx-2">•</span>
                                        <span>{{ action.created_at|timesince }} {% trans "ago" %}</span>
                                    </div>
                                </div>
                                {% if action.action_url %}
                                <a href="{{ action.action_url }}" class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm transition-colors">
                                    {% trans "متابعة" %}
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Notifications List -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-list text-blue-600 mr-2"></i>
                {% trans "جميع الإشعارات" %}
            </h2>
        </div>
        
        <div class="divide-y divide-gray-200">
            {% for notification in notifications %}
            <div class="notification-item p-6 {% if not notification.is_read %}notification-unread priority-{{ notification.priority }}{% endif %}" 
                 data-notification-id="{{ notification.id }}">
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 rounded-full bg-{{ notification.notification_type.color }}-100 flex items-center justify-center">
                                <i class="{{ notification.notification_type.icon }} text-{{ notification.notification_type.color }}-600"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2">
                                <h3 class="text-lg font-medium text-gray-900">{{ notification.title }}</h3>
                                {% if not notification.is_read %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {% trans "جديد" %}
                                </span>
                                {% endif %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ notification.notification_type.color }}-100 text-{{ notification.notification_type.color }}-800">
                                    {{ notification.get_priority_display }}
                                </span>
                            </div>
                            <p class="mt-1 text-gray-600">{{ notification.message }}</p>
                            <div class="mt-2 flex items-center text-sm text-gray-500">
                                <span>{{ notification.notification_type.name }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ notification.created_at|timesince }} {% trans "ago" %}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        {% if notification.action_required and notification.action_url %}
                        <a href="{{ notification.action_url }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                            {{ notification.action_text|default:_("اتخاذ إجراء") }}
                        </a>
                        {% endif %}
                        
                        {% if not notification.is_read %}
                        <button onclick="markAsRead('{{ notification.id }}')" 
                                class="text-gray-400 hover:text-blue-600 transition-colors" 
                                title="{% trans 'تحديد كمقروء' %}">
                            <i class="fas fa-check"></i>
                        </button>
                        {% endif %}
                        
                        <button onclick="dismissNotification('{{ notification.id }}')" 
                                class="text-gray-400 hover:text-red-600 transition-colors" 
                                title="{% trans 'إخفاء' %}">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="p-12 text-center">
                <i class="fas fa-bell-slash text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد إشعارات" %}</h3>
                <p class="text-gray-500">{% trans "لم يتم العثور على أي إشعارات حتى الآن" %}</p>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if notifications.has_other_pages %}
        <div class="px-6 py-4 border-t border-gray-200">
            <nav class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if notifications.has_previous %}
                    <a href="?page={{ notifications.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "السابق" %}
                    </a>
                    {% endif %}
                    {% if notifications.has_next %}
                    <a href="?page={{ notifications.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "التالي" %}
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {% trans "عرض" %}
                            <span class="font-medium">{{ notifications.start_index }}</span>
                            {% trans "إلى" %}
                            <span class="font-medium">{{ notifications.end_index }}</span>
                            {% trans "من" %}
                            <span class="font-medium">{{ notifications.paginator.count }}</span>
                            {% trans "نتيجة" %}
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if notifications.has_previous %}
                            <a href="?page={{ notifications.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            {% endif %}
                            
                            {% for num in notifications.paginator.page_range %}
                                {% if num == notifications.number %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                                {% else %}
                                <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if notifications.has_next %}
                            <a href="?page={{ notifications.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function markAsRead(notificationId) {
    fetch(`{% url 'notifications:mark_read' '00000000-0000-0000-0000-000000000000' %}`.replace('00000000-0000-0000-0000-000000000000', notificationId), {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationElement.classList.remove('notification-unread');
            notificationElement.querySelector('.fa-check').parentElement.remove();
            location.reload(); // Refresh to update counts
        }
    })
    .catch(error => console.error('Error:', error));
}

function dismissNotification(notificationId) {
    fetch(`{% url 'notifications:dismiss' '00000000-0000-0000-0000-000000000000' %}`.replace('00000000-0000-0000-0000-000000000000', notificationId), {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationElement.style.opacity = '0';
            setTimeout(() => {
                notificationElement.remove();
            }, 300);
        }
    })
    .catch(error => console.error('Error:', error));
}

function markAllAsRead() {
    fetch('{% url "notifications:mark_all_read" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}
</script>
{% endblock %} 