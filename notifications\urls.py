from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # Main views
    path('', views.notification_center, name='notification_center'),
    path('actions/', views.action_center, name='action_center'),
    path('preferences/', views.notification_preferences, name='preferences'),
    
    # API endpoints
    path('api/notifications/', views.get_notifications_api, name='api_notifications'),
    path('api/actions/', views.get_action_items_api, name='api_actions'),
    path('api/action-items/', views.get_action_items_api, name='api_action_items'),  # Alternative endpoint
    
    # Actions
    path('mark-read/<uuid:notification_id>/', views.mark_notification_read, name='mark_read'),
    path('dismiss/<uuid:notification_id>/', views.dismiss_notification, name='dismiss'),
    path('mark-all-read/', views.mark_all_notifications_read, name='mark_all_read'),
    path('update-action/<uuid:action_id>/', views.update_action_status, name='update_action'),
    
    # Email actions
    path('email-action/<str:token>/', views.email_action, name='email_action'),
    path('email-logs/', views.email_logs, name='email_logs'),
] 