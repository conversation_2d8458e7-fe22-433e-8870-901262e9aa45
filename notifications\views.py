from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Notification, ActionItem, NotificationPreference, EmailAction
from .services import NotificationService
from .email_service import EmailNotificationService
import json


@login_required
def notification_center(request):
    """Main notification center view"""
    # Get user's notifications
    notifications = NotificationService.get_user_notifications(request.user)
    unread_notifications = NotificationService.get_user_notifications(request.user, unread_only=True)
    
    # Get user's action items
    pending_actions = NotificationService.get_user_action_items(request.user, status='pending')
    in_progress_actions = NotificationService.get_user_action_items(request.user, status='in_progress')
    
    # Pagination for notifications
    paginator = Paginator(notifications, 20)
    page_number = request.GET.get('page')
    page_notifications = paginator.get_page(page_number)
    
    context = {
        'page_title': _('Notification Center'),
        'notifications': page_notifications,
        'unread_count': unread_notifications.count(),
        'pending_actions': pending_actions[:10],  # Show first 10
        'in_progress_actions': in_progress_actions[:10],
        'total_pending_actions': pending_actions.count(),
        'total_in_progress_actions': in_progress_actions.count(),
    }
    
    return render(request, 'notifications/notification_center.html', context)


@login_required
def action_center(request):
    """Action center view for managing tasks"""
    status_filter = request.GET.get('status', 'pending')
    
    # Get user's action items based on filter
    action_items = NotificationService.get_user_action_items(request.user, status=status_filter)
    
    # Pagination
    paginator = Paginator(action_items, 15)
    page_number = request.GET.get('page')
    page_actions = paginator.get_page(page_number)
    
    # Get counts for each status
    status_counts = {
        'pending': NotificationService.get_user_action_items(request.user, status='pending').count(),
        'in_progress': NotificationService.get_user_action_items(request.user, status='in_progress').count(),
        'completed': NotificationService.get_user_action_items(request.user, status='completed').count(),
    }
    
    context = {
        'page_title': _('Action Center'),
        'action_items': page_actions,
        'current_status': status_filter,
        'status_counts': status_counts,
    }
    
    return render(request, 'notifications/action_center.html', context)


@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """Mark a notification as read"""
    try:
        notification = get_object_or_404(
            Notification, 
            id=notification_id, 
            recipient=request.user
        )
        notification.mark_as_read()
        
        return JsonResponse({
            'success': True,
            'message': _('Notification marked as read')
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=400)


@login_required
@require_http_methods(["POST"])
def dismiss_notification(request, notification_id):
    """Dismiss a notification"""
    try:
        notification = get_object_or_404(
            Notification, 
            id=notification_id, 
            recipient=request.user
        )
        notification.dismiss()
        
        return JsonResponse({
            'success': True,
            'message': _('Notification dismissed')
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=400)


@login_required
@require_http_methods(["POST"])
def mark_all_notifications_read(request):
    """Mark all notifications as read"""
    try:
        NotificationService.mark_notifications_as_read(request.user)
        
        return JsonResponse({
            'success': True,
            'message': _('All notifications marked as read')
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=400)


@login_required
@require_http_methods(["POST"])
def update_action_status(request, action_id):
    """Update action item status"""
    try:
        action_item = get_object_or_404(
            ActionItem, 
            id=action_id, 
            assigned_to=request.user
        )
        
        data = json.loads(request.body)
        new_status = data.get('status')
        
        if new_status not in ['pending', 'in_progress', 'completed', 'cancelled']:
            return JsonResponse({
                'success': False,
                'message': _('Invalid status')
            }, status=400)
        
        action_item.status = new_status
        
        if new_status == 'completed':
            action_item.mark_completed(completed_by=request.user)
        else:
            action_item.save(update_fields=['status'])
        
        return JsonResponse({
            'success': True,
            'message': _('Action status updated'),
            'new_status': new_status
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        }, status=400)


@login_required
def get_notifications_api(request):
    """API endpoint to get user notifications"""
    unread_only = request.GET.get('unread_only', 'false').lower() == 'true'
    limit = int(request.GET.get('limit', 10))
    
    notifications = NotificationService.get_user_notifications(
        request.user, 
        unread_only=unread_only
    )[:limit]
    
    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': str(notification.id),
            'title': notification.title,
            'message': notification.message,
            'priority': notification.priority,
            'is_read': notification.is_read,
            'action_required': notification.action_required,
            'action_url': notification.action_url,
            'action_text': notification.action_text,
            'created_at': notification.created_at.isoformat(),
            'notification_type': {
                'name': notification.notification_type.name,
                'icon': notification.notification_type.icon,
                'color': notification.notification_type.color,
            }
        })
    
    return JsonResponse({
        'notifications': notifications_data,
        'unread_count': NotificationService.get_user_notifications(request.user, unread_only=True).count()
    })


@login_required
def get_action_items_api(request):
    """API endpoint to get user action items"""
    status = request.GET.get('status', 'pending')
    limit = int(request.GET.get('limit', 10))
    
    action_items = NotificationService.get_user_action_items(
        request.user, 
        status=status
    )[:limit]
    
    actions_data = []
    for action in action_items:
        actions_data.append({
            'id': str(action.id),
            'title': action.title,
            'description': action.description,
            'action_type': action.action_type,
            'priority': action.priority,
            'status': action.status,
            'due_date': action.due_date.isoformat() if action.due_date else None,
            'action_url': action.action_url,
            'created_at': action.created_at.isoformat(),
        })
    
    return JsonResponse({
        'action_items': actions_data,
        'total_count': NotificationService.get_user_action_items(request.user, status=status).count()
    })


@login_required
def notification_preferences(request):
    """Manage notification preferences"""
    try:
        preferences = request.user.notification_preferences
    except NotificationPreference.DoesNotExist:
        tenant_id = getattr(request, 'tenant_id', None)
        preferences = NotificationPreference.objects.create(
            user=request.user,
            tenant_id=tenant_id
        )
    
    if request.method == 'POST':
        # Update preferences
        preferences.email_enabled = request.POST.get('email_enabled') == 'on'
        preferences.browser_enabled = request.POST.get('browser_enabled') == 'on'
        preferences.sound_enabled = request.POST.get('sound_enabled') == 'on'
        preferences.email_frequency = request.POST.get('email_frequency', 'immediate')
        preferences.save()
        
        messages.success(request, _('Notification preferences updated successfully'))
        return redirect('notifications:preferences')
    
    context = {
        'page_title': _('Notification Preferences'),
        'preferences': preferences,
    }
    
    return render(request, 'notifications/preferences.html', context)


def email_action(request, token):
    """Handle email action clicks"""
    email_service = EmailNotificationService()
    
    # Process the email action
    result = email_service.process_email_action(
        token=token,
        user=request.user if request.user.is_authenticated else None
    )
    
    if result['success']:
        if request.user.is_authenticated:
            messages.success(request, result['message'])
        
        # Redirect to the action URL
        return redirect(result['redirect_url'])
    else:
        if request.user.is_authenticated:
            messages.error(request, result['message'])
        
        # If login required, redirect to login
        if 'redirect_url' in result:
            return redirect(result['redirect_url'])
        
        # Otherwise redirect to home
        return redirect('core:main_dashboard')


@login_required
def email_logs(request):
    """View email notification logs"""
    logs = request.user.email_logs.all().order_by('-created_at')
    
    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        logs = logs.filter(status=status_filter)
    
    # Pagination
    paginator = Paginator(logs, 20)
    page_number = request.GET.get('page')
    page_logs = paginator.get_page(page_number)
    
    context = {
        'page_title': _('Email Logs'),
        'email_logs': page_logs,
        'current_status': status_filter,
    }
    
    return render(request, 'notifications/email_logs.html', context)
