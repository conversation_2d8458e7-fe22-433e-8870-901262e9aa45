#!/bin/bash

# Add tenant_id to all existing models
# Usage: ./add_tenant_id.sh [tenant_id]

# Default tenant ID is 1 if not provided
TENANT_ID=${1:-1}

echo "Adding tenant_id to all models with default tenant ID: $TENANT_ID"

# First run in dry-run mode to see what would be changed
echo "Performing dry run..."
python manage.py add_tenant_id_to_models --tenant_id=$TENANT_ID --dry-run

# Ask for confirmation
read -p "Do you want to proceed with the changes? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    # Run the actual command
    echo "Applying changes..."
    python manage.py add_tenant_id_to_models --tenant_id=$TENANT_ID
    echo "Tenant ID added to all models."
else
    echo "Operation cancelled." 