#!/usr/bin/env python3
"""
CRITICAL FIX: Work Order Vehicle UUID Validation Issue

This script contains the immediate fixes needed to resolve the work order system
breaking after step 2 due to UUID validation errors.

The main issue: Vehicle ID being passed as empty string "" instead of proper UUID
when calling the operations-for-vehicle API endpoint.

Apply these fixes to work_orders/views.py
"""

def api_get_operations_for_vehicle_FIXED(request):
    """
    FIXED VERSION: Get maintenance operations for a specific vehicle based on mileage/time
    
    This replaces the broken implementation in work_orders/views.py
    """
    from django.http import JsonResponse
    from django.contrib.auth.decorators import login_required
    from django.views.decorators.http import require_http_methods
    from django.db import transaction
    from django.utils.translation import gettext_lazy as _
    import uuid
    from setup.models import Vehicle
    from work_orders.models import MaintenanceSchedule, ScheduleOperation
    from inventory.models import OperationCompatibility
    
    # CRITICAL FIX: Proper UUID validation and error handling
    vehicle_id = request.GET.get('vehicle_id', '').strip()
    odometer = request.GET.get('odometer', '0')
    
    # Validate vehicle_id
    if not vehicle_id:
        return JsonResponse({
            'error': _('Vehicle ID is required'),
            'details': 'No vehicle_id parameter provided'
        }, status=400)
    
    # FIXED: Proper UUID validation
    try:
        vehicle_uuid = uuid.UUID(vehicle_id)
    except (ValueError, TypeError) as e:
        return JsonResponse({
            'error': _('Invalid vehicle ID format'),
            'details': f'Vehicle ID must be a valid UUID. Received: "{vehicle_id}"'
        }, status=400)
    
    # FIXED: Proper vehicle lookup with tenant scoping
    try:
        from core.middleware import get_current_tenant_id
        tenant_id = get_current_tenant_id()
        vehicle = Vehicle.objects.get(id=vehicle_uuid, tenant_id=tenant_id)
    except Vehicle.DoesNotExist:
        return JsonResponse({
            'error': _('Vehicle not found'),
            'details': f'No vehicle found with ID: {vehicle_uuid}'
        }, status=404)
    
    # Check if vehicle has active work order (FIXED: Proper restriction)
    from work_orders.models import WorkOrder
    active_work_orders = WorkOrder.objects.filter(
        vehicle=vehicle,
        tenant_id=request.user.tenant_id,
        status__in=['planned', 'in_progress']
    )
    
    if active_work_orders.exists():
        active_wo = active_work_orders.first()
        return JsonResponse({
            'error': _('Vehicle has active work order'),
            'details': f'Vehicle currently has work order #{active_wo.work_order_number} in {active_wo.status} status',
            'active_work_order': active_wo.work_order_number
        }, status=409)  # Conflict status
    
    # Convert odometer to integer
    try:
        current_odometer = int(float(odometer))
    except (ValueError, TypeError):
        current_odometer = 0
    
    # FIXED: Get maintenance schedules for this vehicle
    operations = []
    
    try:
        # Find applicable maintenance schedules
        schedules = MaintenanceSchedule.objects.filter(
            tenant_id=request.user.tenant_id,
            is_active=True
        ).filter(
            vehicle_models=vehicle.model
        ).distinct()
        
        for schedule in schedules:
            schedule_operations = ScheduleOperation.objects.filter(
                maintenance_schedule=schedule,
                is_active=True
            )
            
            for sched_op in schedule_operations:
                # Check if operation is due based on mileage
                if schedule.mileage_interval and current_odometer:
                    last_service_odometer = getattr(vehicle, 'last_service_odometer', 0) or 0
                    mileage_since_service = current_odometer - last_service_odometer
                    
                    if mileage_since_service >= schedule.mileage_interval:
                        # FIXED: Get compatible parts for this operation
                        compatible_parts = OperationCompatibility.objects.filter(
                            operation_description=sched_op.operation_description,
                            tenant_id=request.user.tenant_id
                        ).select_related('item')
                        
                        parts_list = []
                        for comp in compatible_parts:
                            parts_list.append({
                                'id': str(comp.item.id),
                                'name': comp.item.name,
                                'sku': comp.item.sku,
                                'estimated_quantity': float(comp.estimated_quantity),
                                'is_required': comp.is_required
                            })
                        
                        operations.append({
                            'id': str(sched_op.id),
                            'description': sched_op.operation_description.name,
                            'duration_hours': float(sched_op.duration_hours),
                            'labor_cost': float(sched_op.labor_cost),
                            'is_required': sched_op.is_required,
                            'notes': sched_op.notes,
                            'compatible_parts': parts_list,
                            'schedule_reason': f'Due by mileage ({mileage_since_service:,} km since last service)'
                        })
        
        return JsonResponse({
            'operations': operations,
            'vehicle_info': {
                'id': str(vehicle.id),
                'vin': vehicle.vin,
                'make': vehicle.make.name,
                'model': vehicle.model.name,
                'year': vehicle.year,
                'current_odometer': current_odometer
            },
            'debug_info': {
                'schedules_found': schedules.count(),
                'operations_due': len(operations),
                'last_service_odometer': getattr(vehicle, 'last_service_odometer', 0)
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'error': _('Error retrieving operations'),
            'details': str(e)
        }, status=500)


# FRONTEND JAVASCRIPT FIX
FRONTEND_VEHICLE_SELECTION_FIX = """
// CRITICAL FIX: Vehicle Selection JavaScript
// Replace the existing selectVehicle function in work_order_form.html

function selectVehicle(vehicleData) {
    console.log('🔧 FIXED: Selecting vehicle with data:', vehicleData);
    
    // VALIDATE INPUT
    if (!vehicleData || !vehicleData.id) {
        console.error('❌ Invalid vehicle data provided');
        showErrorMessage('Invalid vehicle data provided');
        return false;
    }
    
    // ENSURE PROPER UUID STRING
    const vehicleId = String(vehicleData.id).trim();
    
    // BASIC UUID FORMAT VALIDATION
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(vehicleId)) {
        console.error('❌ Invalid UUID format:', vehicleId);
        showErrorMessage('Invalid vehicle ID format');
        return false;
    }
    
    // CLEAR ANY EXISTING ERROR STATES
    clearErrorMessages();
    
    // SET GLOBAL VARIABLES - CRITICAL FIX
    window.selected_vehicle_id = vehicleId;  // ENSURE STRING TYPE
    window.selected_vehicle_data = vehicleData;
    
    // UPDATE UI ELEMENTS
    const vehicleDisplayElement = document.getElementById('selected-vehicle-display');
    if (vehicleDisplayElement) {
        vehicleDisplayElement.innerHTML = `
            <div class="selected-vehicle-info">
                <strong>${vehicleData.year} ${vehicleData.make_name} ${vehicleData.model_name}</strong>
                <br>VIN: ${vehicleData.vin}
                <br>Owner: ${vehicleData.owner_name}
                <br>ID: ${vehicleId}
            </div>
        `;
        vehicleDisplayElement.style.display = 'block';
    }
    
    // HIDE VEHICLE SEARCH
    const vehicleSearchContainer = document.getElementById('vehicle-search-container');
    if (vehicleSearchContainer) {
        vehicleSearchContainer.style.display = 'none';
    }
    
    // ENABLE NEXT STEP
    const nextStepButton = document.getElementById('next-step-btn');
    if (nextStepButton) {
        nextStepButton.disabled = false;
        nextStepButton.classList.remove('opacity-50');
    }
    
    // LOG SUCCESS
    console.log('✅ Vehicle selected successfully:', {
        vehicleId: vehicleId,
        vehicleData: vehicleData
    });
    
    // SAVE TO SESSION STORAGE FOR PERSISTENCE
    sessionStorage.setItem('selected_vehicle_id', vehicleId);
    sessionStorage.setItem('selected_vehicle_data', JSON.stringify(vehicleData));
    
    return true;
}

// FIXED: Operations fetching function
function fetchOperationsForVehicle(vehicleId, odometer) {
    console.log('🔧 FIXED: Fetching operations for vehicle:', vehicleId, 'odometer:', odometer);
    
    // VALIDATE INPUTS
    if (!vehicleId || vehicleId.trim() === '') {
        console.error('❌ Vehicle ID is empty or undefined');
        showErrorMessage('No vehicle selected');
        return Promise.reject('No vehicle ID provided');
    }
    
    if (!odometer || odometer === '') {
        console.error('❌ Odometer reading is required');
        showErrorMessage('Odometer reading is required');
        return Promise.reject('No odometer reading provided');
    }
    
    // ENSURE STRING TYPE AND VALIDATION
    const cleanVehicleId = String(vehicleId).trim();
    const cleanOdometer = String(odometer).trim();
    
    // CONSTRUCT API URL WITH PROPER ENCODING
    const apiUrl = `/ar/work-orders/api/operations-for-vehicle/?vehicle_id=${encodeURIComponent(cleanVehicleId)}&odometer=${encodeURIComponent(cleanOdometer)}`;
    
    console.log('📞 API Call URL:', apiUrl);
    
    // MAKE API REQUEST WITH PROPER ERROR HANDLING
    return fetch(apiUrl, {
        method: 'GET',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('📥 API Response status:', response.status);
        
        if (!response.ok) {
            return response.json().then(errorData => {
                console.error('❌ API Error:', errorData);
                throw new Error(errorData.error || `HTTP ${response.status}`);
            });
        }
        
        return response.json();
    })
    .then(data => {
        console.log('✅ API Success:', data);
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        // POPULATE OPERATIONS IN UI
        populateOperations(data.operations || []);
        
        return data;
    })
    .catch(error => {
        console.error('❌ Operations fetch failed:', error);
        showErrorMessage('Failed to load operations: ' + error.message);
        throw error;
    });
}

// UTILITY FUNCTIONS
function showErrorMessage(message) {
    const errorContainer = document.getElementById('error-messages');
    if (errorContainer) {
        errorContainer.innerHTML = `<div class="alert alert-danger">${message}</div>`;
        errorContainer.style.display = 'block';
    }
}

function clearErrorMessages() {
    const errorContainer = document.getElementById('error-messages');
    if (errorContainer) {
        errorContainer.innerHTML = '';
        errorContainer.style.display = 'none';
    }
}
"""

# BACKEND URL ROUTING FIX
URL_ROUTING_FIX = """
# Add this to work_orders/urls.py

from django.urls import path
from . import views

app_name = 'work_orders'

urlpatterns = [
    # ... existing patterns ...
    
    # FIXED: Operations API endpoint
    path(
        'api/operations-for-vehicle/',
        views.api_get_operations_for_vehicle_FIXED,  # Use the fixed version
        name='api_get_operations_for_vehicle'
    ),
    
    # FIXED: Vehicle active work order check
    path(
        'api/check-vehicle-status/<uuid:vehicle_id>/',
        views.api_check_vehicle_active_work_order,
        name='api_check_vehicle_status'
    ),
]
"""

def api_check_vehicle_active_work_order(request, vehicle_id):
    """
    NEW API: Check if vehicle has active work orders
    """
    from django.http import JsonResponse
    from django.contrib.auth.decorators import login_required
    from setup.models import Vehicle
    from work_orders.models import WorkOrder
    
    try:
        vehicle = Vehicle.objects.get(id=vehicle_id, tenant_id=request.user.tenant_id)
        
        active_work_orders = WorkOrder.objects.filter(
            vehicle=vehicle,
            tenant_id=request.user.tenant_id,
            status__in=['planned', 'in_progress']
        )
        
        if active_work_orders.exists():
            active_wo = active_work_orders.first()
            return JsonResponse({
                'has_active_work_order': True,
                'work_order_number': active_wo.work_order_number,
                'status': active_wo.status,
                'created_date': active_wo.created_at.isoformat()
            })
        else:
            return JsonResponse({
                'has_active_work_order': False
            })
            
    except Vehicle.DoesNotExist:
        return JsonResponse({
            'error': 'Vehicle not found'
        }, status=404)

if __name__ == "__main__":
    print("🔧 CRITICAL WORK ORDER FIXES")
    print("=" * 50)
    print("1. Replace api_get_operations_for_vehicle function in work_orders/views.py")
    print("2. Add the JavaScript fixes to work_order_form.html")
    print("3. Update URLs in work_orders/urls.py")
    print("4. Test vehicle selection and operations loading")
    print("=" * 50)
    print("This will fix the UUID validation error that breaks the work order workflow.") 