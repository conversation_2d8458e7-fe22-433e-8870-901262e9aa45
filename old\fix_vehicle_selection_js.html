<!-- 
Critical JavaScript Fix for Vehicle Selection UUID Issue
This needs to be integrated into work_orders/templates/work_orders/work_order_form.html

The main issue is that selected_vehicle_id is being set to empty string instead of proper UUID
-->

<script>
// ========== CRITICAL FIX FOR VEHICLE SELECTION ==========

// Fixed vehicle selection handler
function selectVehicle(vehicleData) {
    console.log('🔧 FIXED: Selecting vehicle with data:', vehicleData);
    
    // Validate vehicle data
    if (!vehicleData || !vehicleData.id) {
        console.error('❌ Invalid vehicle data provided to selectVehicle');
        return false;
    }
    
    // Ensure we have a valid UUID string
    const vehicleId = String(vehicleData.id).trim();
    
    // Basic UUID format validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(vehicleId)) {
        console.error('❌ Invalid UUID format for vehicle ID:', vehicleId);
        return false;
    }
    
    console.log('✅ Valid vehicle UUID:', vehicleId);
    
    // Set the hidden field value properly
    const selectedVehicleIdInput = document.getElementById('selected_vehicle_id');
    if (selectedVehicleIdInput) {
        selectedVehicleIdInput.value = vehicleId;
        console.log('✅ Set selected_vehicle_id to:', vehicleId);
    } else {
        console.error('❌ Could not find selected_vehicle_id input field');
        return false;
    }
    
    // Update the vehicle info display
    const selectedVehicleInfo = document.getElementById('selected-vehicle-info');
    if (selectedVehicleInfo) {
        selectedVehicleInfo.style.display = 'block';
        
        // Update vehicle details in the display
        const makeModel = `${vehicleData.make || ''} ${vehicleData.model || ''}`.trim();
        const year = vehicleData.year ? ` (${vehicleData.year})` : '';
        const plateOrVin = vehicleData.license_plate || vehicleData.vin || 'غير محدد';
        
        // Update display elements
        const makeModelSpan = selectedVehicleInfo.querySelector('#selected-vehicle-make-model');
        const yearSpan = selectedVehicleInfo.querySelector('#selected-vehicle-year');  
        const plateSpan = selectedVehicleInfo.querySelector('#selected-vehicle-plate');
        
        if (makeModelSpan) makeModelSpan.textContent = makeModel;
        if (yearSpan) yearSpan.textContent = year;
        if (plateSpan) plateSpan.textContent = plateOrVin;
        
        console.log('✅ Updated vehicle display');
    }
    
    // Clear vehicle search results
    const searchResults = document.getElementById('vehicle-search-results');
    if (searchResults) {
        searchResults.innerHTML = '';
        searchResults.style.display = 'none';
    }
    
    // Clear search input
    const searchInput = document.getElementById('vehicle_search');
    if (searchInput) {
        searchInput.value = '';
    }
    
    // Store vehicle data for later use
    window.selectedVehicleData = vehicleData;
    
    // Save form data to session storage
    saveFormDataToSession();
    
    console.log('🎉 Vehicle selection completed successfully');
    return true;
}

// Fixed function to get current vehicle ID safely
function getCurrentVehicleId() {
    const vehicleIdInput = document.getElementById('selected_vehicle_id');
    const vehicleId = vehicleIdInput ? vehicleIdInput.value : '';
    
    // Return null for empty or invalid UUIDs instead of empty string
    if (!vehicleId || vehicleId.trim() === '' || vehicleId === 'null' || vehicleId === 'undefined') {
        console.log('ℹ️ No valid vehicle ID available');
        return null;
    }
    
    const cleanId = vehicleId.trim();
    
    // Basic UUID validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(cleanId)) {
        console.warn('⚠️ Invalid UUID format in getCurrentVehicleId:', cleanId);
        return null;
    }
    
    return cleanId;
}

// Fixed vehicle search result click handler
function handleVehicleSearchResult(vehicle) {
    console.log('🔧 FIXED: Handling vehicle search result click');
    
    // Ensure vehicle has valid ID
    if (!vehicle.id) {
        console.error('❌ Vehicle search result missing ID');
        return;
    }
    
    // Select the vehicle using our fixed function
    if (selectVehicle(vehicle)) {
        // If selection was successful, trigger any dependent updates
        const currentOdometer = document.getElementById('current_odometer')?.value || 0;
        
        // Only call dependent functions if we have valid data
        if (getCurrentVehicleId()) {
            // Trigger maintenance history loading if needed
            loadMaintenanceHistory(vehicle.id);
            
            // Trigger operations loading if odometer is available
            if (currentOdometer > 0) {
                setTimeout(() => {
                    fetchOperationsForVehicle(vehicle.id, currentOdometer);
                }, 500);
            }
        }
    }
}

// Fixed fetchOperationsForVehicle function with better error handling
function fetchOperationsForVehicle(vehicleId, odometer) {
    console.log('🔧 FIXED: fetchOperationsForVehicle called');
    
    // Use getCurrentVehicleId for consistency if vehicleId not provided
    const validVehicleId = vehicleId || getCurrentVehicleId();
    
    if (!validVehicleId) {
        console.warn('⚠️ No valid vehicle ID available for fetchOperationsForVehicle');
        // Don't make API call with invalid ID, but don't show error either
        // Instead, try to use make/model from form
        const make = document.getElementById('new_vehicle_make')?.value;
        const model = document.getElementById('new_vehicle_model')?.value;
        
        if (!make || !model) {
            console.log('ℹ️ No vehicle ID or make/model available for operations');
            return;
        }
    }
    
    // Ensure odometer is valid
    const odometerValue = parseInt(odometer) || 0;
    if (odometerValue <= 0) {
        console.log('ℹ️ Invalid odometer reading for fetchOperationsForVehicle');
        return;
    }
    
    console.log('✅ Proceeding with operations fetch:', {
        vehicleId: validVehicleId,
        odometer: odometerValue
    });
    
    // Show loading indicator
    const operationsLoadingEl = document.getElementById('operations-loading');
    if (operationsLoadingEl) {
        operationsLoadingEl.classList.remove('hidden');
    }
    
    // Build API URL with proper encoding
    let apiUrl = "{% url 'work_orders:api_operations_for_vehicle' %}?";
    
    // Only include vehicle_id if we have a valid one
    if (validVehicleId) {
        apiUrl += `vehicle_id=${encodeURIComponent(validVehicleId)}&`;
    }
    
    // Always include odometer
    apiUrl += `odometer=${encodeURIComponent(odometerValue)}`;
    
    // Add make/model as backup
    const make = document.getElementById('new_vehicle_make')?.value;
    const model = document.getElementById('new_vehicle_model')?.value;
    
    if (make) {
        apiUrl += `&make=${encodeURIComponent(make)}`;
    }
    if (model) {
        apiUrl += `&model=${encodeURIComponent(model)}`;
    }
    
    console.log('🔗 API URL:', apiUrl);
    
    // Make the API call
    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide loading indicator
            if (operationsLoadingEl) {
                operationsLoadingEl.classList.add('hidden');
            }
            
            console.log('✅ Operations API response:', data);
            
            if (data.success) {
                processOperationsData(data);
            } else {
                console.error('❌ Operations API returned error:', data.error);
                showNotification('خطأ في جلب العمليات: ' + (data.message || data.error), 'error');
            }
        })
        .catch(error => {
            // Hide loading indicator
            if (operationsLoadingEl) {
                operationsLoadingEl.classList.add('hidden');
            }
            
            console.error('❌ Error fetching operations:', error);
            showNotification('خطأ في الاتصال بالخادم لجلب العمليات', 'error');
        });
}

// Helper function to process operations data
function processOperationsData(data) {
    console.log('📊 Processing operations data...');
    
    // Store operation durations and prices for later use
    if (data.scheduled_operations) {
        data.scheduled_operations.forEach(op => {
            if (op.name && op.duration_hours) {
                window.operationDurations = window.operationDurations || {};
                window.operationDurations[op.name] = op.duration_hours;
            }
            if (op.name && op.price) {
                window.operationPrices = window.operationPrices || {};
                window.operationPrices[op.name] = op.price;
            }
        });
    }
    
    if (data.custom_operations) {
        data.custom_operations.forEach(op => {
            if (op.name && op.duration_hours) {
                window.operationDurations = window.operationDurations || {};
                window.operationDurations[op.name] = op.duration_hours;
            }
            if (op.name && op.price) {
                window.operationPrices = window.operationPrices || {};
                window.operationPrices[op.name] = op.price;
            }
        });
    }
    
    // Update UI with operations data
    updateOperationsDisplay(data);
    
    console.log('✅ Operations data processed successfully');
}

// Helper function to update operations display
function updateOperationsDisplay(data) {
    const operationsListEl = document.getElementById('operations-list');
    const noOperationsMessageEl = document.getElementById('no-operations-message');
    
    if (!operationsListEl) {
        console.warn('⚠️ Operations list element not found');
        return;
    }
    
    // Clear existing operations
    operationsListEl.innerHTML = '';
    
    let hasOperations = false;
    
    // Add scheduled operations
    if (data.scheduled_operations && data.scheduled_operations.length > 0) {
        hasOperations = true;
        data.scheduled_operations.forEach(operation => {
            addOperationToList(operation, 'scheduled');
        });
    }
    
    // Add custom operations
    if (data.custom_operations && data.custom_operations.length > 0) {
        hasOperations = true;
        data.custom_operations.forEach(operation => {
            addOperationToList(operation, 'custom');
        });
    }
    
    // Show/hide no operations message
    if (noOperationsMessageEl) {
        if (hasOperations) {
            noOperationsMessageEl.classList.add('hidden');
        } else {
            noOperationsMessageEl.classList.remove('hidden');
        }
    }
}

// Helper function to add operation to list
function addOperationToList(operation, type) {
    const operationsListEl = document.getElementById('operations-list');
    const template = document.getElementById('operation-item-template');
    
    if (!template || !operationsListEl) {
        console.warn('⚠️ Missing template or operations list element');
        return;
    }
    
    // Clone template
    const newOperation = document.importNode(template.content, true).firstElementChild;
    
    // Set operation data
    const nameEl = newOperation.querySelector('.operation-name');
    const durationEl = newOperation.querySelector('.operation-duration');
    const priceEl = newOperation.querySelector('.operation-price');
    
    if (nameEl) nameEl.textContent = operation.name || '';
    if (durationEl) durationEl.textContent = operation.duration_hours || '1';
    if (priceEl) priceEl.textContent = operation.price || '0';
    
    // Add to list
    operationsListEl.appendChild(newOperation);
}

// Initialize fixed handlers when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 FIXED: Initializing vehicle selection handlers');
    
    // Make sure selected_vehicle_id field exists
    const selectedVehicleIdInput = document.getElementById('selected_vehicle_id');
    if (!selectedVehicleIdInput) {
        console.error('❌ selected_vehicle_id input field not found in DOM');
    } else {
        console.log('✅ selected_vehicle_id field found');
    }
    
    // Initialize helper functions in global scope
    window.selectVehicle = selectVehicle;
    window.getCurrentVehicleId = getCurrentVehicleId;
    window.handleVehicleSearchResult = handleVehicleSearchResult;
    window.fetchOperationsForVehicle = fetchOperationsForVehicle;
    
    console.log('✅ Fixed vehicle selection handlers initialized');
});

// ========== END OF CRITICAL FIX ==========
</script>

<!-- 
INTEGRATION INSTRUCTIONS:

1. Find the existing vehicle selection JavaScript in work_orders/templates/work_orders/work_order_form.html
2. Replace the existing selectVehicle, getCurrentVehicleId, and fetchOperationsForVehicle functions with the fixed versions above
3. Make sure the selected_vehicle_id hidden input field exists in the HTML form
4. Test the vehicle selection workflow to ensure UUIDs are properly handled

The key changes:
- Proper UUID validation before making API calls
- Return null instead of empty string for invalid UUIDs
- Better error handling and logging
- Consistent vehicle ID handling across all functions
--> 