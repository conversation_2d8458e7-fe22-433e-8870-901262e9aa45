# Demo Data Population Scripts

This directory contains scripts to populate the database with Egyptian-themed demo data for the inventory management system, based on Hyundai Elantra vehicles shown in the application.

## Scripts Overview

1. **demo_data.py**: Creates basic inventory items, units of measurement, vehicle makes and models, and customers with Egyptian details
2. **create_operation_compatibilities.py**: Creates operation compatibilities linking inventory items to vehicle maintenance operations
3. **create_pricing.py**: Creates pricing data for operations and parts specific to Hyundai Elantra vehicles
4. **run_all.py**: Convenience script to run all the above scripts in sequence

## How to Run

You can run the scripts individually or all at once:

### Run All Scripts

To populate all demo data in one go:

```
python populate/run_all.py
```

### Run Individual Scripts

If you want to run scripts individually:

```
python populate/demo_data.py
python populate/create_operation_compatibilities.py
python populate/create_pricing.py
```

## Data Created

The scripts will create:

- **Inventory Items**: Engine oil, oil filters, brake pads, and headlight assemblies for Hyundai Elantra
- **Vehicles**: 4 Hyundai Elantra vehicles from 2007 with Egyptian license plates
- **Customers**: Egyptian customer data with Arabic names
- **Service Centers**: Cairo-based service center
- **Operation Compatibilities**: Linking parts to operations like oil changes and brake services
- **Pricing Data**: Service and part pricing specific to the Egyptian market

## Notes

- All data is created with a consistent tenant ID (`a385e9a3-0e6b-4e44-aa87-6a6510d6c2b7`)
- The scripts check for existing data to avoid duplicates
- Scripts must be run in order (as handled by run_all.py)

## Troubleshooting

If you encounter errors:

1. Make sure Django settings are properly configured
2. Ensure the database is accessible and migrations have been applied
3. Run scripts in the correct sequence (demo_data.py must run first)
4. Check console output for specific error messages

## Extending

To add more demo data:

1. Use the existing scripts as templates
2. Maintain the same tenant ID (use the `create_tenant_id()` function)
3. Follow the pattern of checking for existing data before creating new records 