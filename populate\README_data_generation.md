# Aftersails Demo Data Generation

This directory contains scripts to generate demo data for the Aftersails Vehicle Service Management System, specifically tailored for the Egyptian market.

## Available Scripts

- `generate_setup_data.py` - Creates basic organizational data (franchises, companies, service centers), customers, and vehicles.
- `generate_operation_compatibilities.py` - Creates inventory data, item classifications, and operation compatibilities.
- `generate_warehouse_data.py` - Creates warehouse data including locations, bin locations, and inventory distributions.
- `generate_work_orders_data.py` - Creates work orders, bills of materials, and maintenance schedules.
- `generate_billing_data.py` - Creates billing-related data including insurance, warranties, and customer preferences.
- `generate_complete_demo_data.py` - Runs all the above scripts in sequence.

## Usage

You can run any of the scripts individually:

```bash
python generate_setup_data.py
python generate_operation_compatibilities.py
python generate_warehouse_data.py
python generate_work_orders_data.py
python generate_billing_data.py
```

Or run the complete data generation process with:

```bash
python generate_complete_demo_data.py
```

## Generated Data

The scripts create the following data with Egyptian-specific content:

### Setup Data
- **Franchises**: Egyptian automotive service groups
- **Companies**: Egyptian automotive service companies
- **Service Centers**: Service centers in major Egyptian cities
- **Customers**: With Egyptian names, phone numbers, and addresses
- **Vehicles**: With Egyptian makes/models and license plate formats

### Inventory Data
- **Item Classifications**: Categories for inventory items in Arabic
- **Items**: Automotive parts and materials with Arabic names
- **Work Order Types**: Common service types in Arabic
- **Operation Compatibilities**: Links between work order types and items

### Warehouse Data
- **Location Types**: Different types of warehouse locations
- **Locations**: Main warehouse and service center warehouses
- **Bin Locations**: Storage locations within warehouses
- **Item Locations**: Distribution of items across locations
- **Transfer Orders**: Movement of items between locations

### Work Orders Data
- **Bills of Materials**: Sets of components for specific assemblies or service packages
- **Maintenance Schedules**: Standard maintenance plans with intervals
- **Schedule Operations**: Steps within maintenance schedules
- **Work Orders**: Service records with multiple operations
- **Work Order Operations**: Individual steps in work orders
- **Work Order Materials**: Items used in work orders

### Billing Data
- **Insurance Companies**: Egyptian insurance companies
- **Warranty Types**: Different warranty offerings
- **Discount Types**: Various discount types
- **Payment Methods**: Common payment methods in Egypt
- **Customer Preferences**: Customer settings and preferences
- **Insurance Policies**: Vehicle insurance policies
- **Vehicle Warranties**: Vehicle warranty information

## Data Relationships

The generated data forms a comprehensive network of related information:
- Service centers have associated warehouses
- Inventory items are distributed across warehouses and bin locations
- Work orders are linked to service centers, vehicles, and customers
- Maintenance schedules are associated with vehicle makes and models
- Billing data connects to customers, vehicles, and service records

## Data Limitations

Due to database constraints, certain data cannot be generated without prerequisites:

- **Invoices and Payments**: Require Work Orders, which must be created manually or through the application
- **Work Orders**: Require manual creation with operations and materials

## Egyptian-Specific Features

- **Names**: Egyptian first and last names
- **Addresses**: Egyptian cities and address formats
- **Phone Numbers**: Egyptian phone number formats (e.g., 01x-xxxx-xxxx)
- **Vehicle Information**: Common Egyptian vehicle makes and models
- **License Plates**: Egyptian license plate format (3 numbers, 3 letters)
- **Arabic Text**: Service descriptions, item names, location names, and other content in Arabic

## Troubleshooting

If you encounter errors during script execution:

1. **Database Consistency**: Ensure that the database structure matches the current model definitions
2. **Tenant IDs**: The scripts attempt to maintain tenant ID consistency. If relationships fail, it may be due to mismatched tenant IDs
3. **Missing Work Orders**: Invoices require work orders. The invoice creation will be skipped if no work orders exist
4. **Import Errors**: Make sure you are running the scripts from the project root directory where `manage.py` is located

## Contributing

To update these scripts or add new data generators:

1. Follow the existing pattern of creating a class with generation methods
2. Use Django's transaction management to ensure data consistency
3. Add proper error handling to deal with constraint failures
4. Document any Egyptian-specific content you add
5. Test thoroughly with a clean database 