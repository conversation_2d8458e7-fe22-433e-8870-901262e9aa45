# Aftersails Demo Data Generator

This document describes the demo data generation scripts for the Aftersails Vehicle Service Management System with Egyptian market-specific content.

## Available Scripts

### simple_demo_generator.py

The primary script for generating demo data with Egyptian market specifics. This script generates:

- Units of Measurement with Arabic names (قطعة, لتر, كيلوجرام, متر, مجموعة)
- Item Classifications for vehicle parts (قطع محرك, قطع كهربائية, قطع هيكل, قطع فرامل, زيوت وسوائل)
- A Franchise with Egyptian details
- Inventory Items with Arabic names and Egyptian vehicle make specifics
- Customers with Egyptian profiles

The script is designed to work with different model structures through introspection and field detection.

## Usage

To generate demo data, run:

```
python simple_demo_generator.py
```

This will create a complete set of basic demo data for the inventory and setup modules.

## Features

- **Tenant-aware**: Creates data with a unique tenant ID to support multi-tenant environments
- **Model Compatibility**: Uses model introspection to adapt to different field structures
- **Error Handling**: Robust error handling to continue even if some models have issues
- **Localized Content**: Egyptian-specific data including:
  - Arabic names for parts and classifications
  - Egyptian car makes and models
  - Egyptian cities and locations
  - Arabic names for customers
  - Egyptian-style contact information

## Limitations

- **Vehicle Creation**: Currently skips vehicle creation due to model compatibility issues
- **Relationship Handling**: Some models may have specific relationship requirements not detected by the script

## Customization

You can customize the generated data by modifying the script:

1. To change the number of items created, edit the counts in the `create_sample_data()` function
2. To add more Egyptian-specific data, edit the relevant data lists in the generator functions
3. To support additional models, create new generator functions following the same pattern

## Troubleshooting

If you encounter issues:

1. Check model compatibility - the script relies on introspection
2. Ensure all required Django apps are installed and migrated
3. Review the script output for specific error messages

## Technical Details

The script:

1. Sets up the Django environment
2. Creates a tenant ID for all data
3. Generates units of measurement with Arabic names
4. Creates item classifications for vehicle parts
5. Establishes a franchise with Egyptian details
6. Generates inventory items with proper categorization
7. Creates customer records with Egyptian profiles

## Model Compatibility

The script adapts to different model structures by:

- Using `hasattr()` to check for field existence
- Trying multiple field name variations 
- Providing fallback mechanisms when fields don't exist
- Using model introspection to identify field types 