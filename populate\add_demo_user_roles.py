#!/usr/bin/env python
"""
Add demo user roles data to the application.
This script directly populates the database with user roles for demo purposes.
It's designed to be run after other setup data has been created.
"""
import os
import sys
import django
import uuid

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.contrib.auth.models import User
from user_roles.models import Role, UserRole, ModulePermission
from setup.models import ServiceCenter
from django.db import connection

def get_tenant_ids():
    """Get unique tenant IDs from existing ServiceCenter records."""
    with connection.cursor() as cursor:
        cursor.execute("SELECT DISTINCT tenant_id FROM setup_servicecenter")
        tenant_ids = [row[0] for row in cursor.fetchall()]
    return tenant_ids

def create_basic_user_roles():
    """Create basic user roles for existing service centers."""
    print("\n===== Creating Basic User Roles =====")
    
    # Check prerequisites
    user_count = User.objects.count()
    role_count = Role.objects.count()
    service_center_count = ServiceCenter.objects.count()
    existing_user_role_count = UserRole.objects.count()
    
    print(f"Prerequisites check:")
    print(f"- Users: {user_count}")
    print(f"- Roles: {role_count}")
    print(f"- Service Centers: {service_center_count}")
    print(f"- Existing User Roles: {existing_user_role_count}")
    
    if service_center_count == 0:
        print("\n❌ ERROR: No service centers found. Please run add_service_center_data.py first.")
        return False
        
    if user_count == 0:
        print("\n❌ ERROR: No users found. Creating a test user...")
        User.objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="password123",
            first_name="Admin",
            last_name="User"
        )
        print("✅ Created admin user")
        user_count = User.objects.count()
        print(f"- Users now: {user_count}")
    
    # Create roles if needed
    if role_count == 0:
        print("\nℹ️ No roles found. Creating default roles...")
        Role.create_default_roles()
        role_count = Role.objects.count()
        print(f"- Roles now: {role_count}")
    
    # Get tenant IDs
    tenant_ids = get_tenant_ids()
    if not tenant_ids:
        print("\n❌ ERROR: No tenant IDs found. Service centers must have tenant IDs.")
        return False
        
    print(f"- Found {len(tenant_ids)} tenant IDs")
    
    # Clear existing user roles if any
    if existing_user_role_count > 0:
        print(f"\nRemoving {existing_user_role_count} existing user roles...")
        UserRole.objects.all().delete()
        print("✅ Existing user roles removed")
    
    # Get all users, roles, and service centers
    users = User.objects.all()
    roles = Role.objects.all()
    service_centers = ServiceCenter.objects.all()
    
    # Map roles by code for easier access
    roles_by_code = {role.code: role for role in roles}
    
    # Create user roles
    created_roles = []
    
    # Create system admin roles
    for tenant_id in tenant_ids:
        if 'system_admin' in roles_by_code and users.exists():
            # Use first user as system admin
            admin_user = users.first()
            
            system_admin_role = UserRole.objects.create(
                tenant_id=tenant_id,
                user=admin_user,
                role=roles_by_code['system_admin'],
                is_primary=True,
                is_active=True
            )
            created_roles.append(system_admin_role)
            print(f"Created system admin role for tenant {tenant_id}")
    
    # Create service center roles
    service_center_count = service_centers.count()
    users_count = users.count()
    user_idx = 0
    
    for service_center in service_centers:
        # Get or use user
        user = users[user_idx % users_count]
        user_idx += 1
        
        # Determine service center size
        size = getattr(service_center, 'size', 'medium')
        
        # Get suggested roles
        suggested_roles = Role.get_suggested_roles_by_center_size(size)
        
        # Create a service center manager role
        manager_role_code = suggested_roles.get('manager', 'service_center_manager')
        if manager_role_code in roles_by_code:
            UserRole.objects.create(
                tenant_id=service_center.tenant_id,
                user=user,
                role=roles_by_code[manager_role_code],
                service_center=service_center,
                is_primary=True,
                is_active=True
            )
            created_roles.append("manager role")
            
        # Create a technician role
        if 'technician' in roles_by_code:
            # Get next user
            user = users[(user_idx) % users_count]
            user_idx += 1
            
            UserRole.objects.create(
                tenant_id=service_center.tenant_id,
                user=user,
                role=roles_by_code['technician'],
                service_center=service_center,
                is_primary=True,
                is_active=True
            )
            created_roles.append("technician role")
    
    # Print summary
    new_user_role_count = UserRole.objects.count()
    print(f"\n✅ Successfully created user roles")
    print(f"- Current user roles: {new_user_role_count}")
    
    return True

def run_with_diagnostics():
    """Run the user roles generator with diagnostics."""
    print("\n===== User Roles Demo Data Generator =====")
    
    try:
        create_basic_user_roles()
        return True
    except Exception as e:
        print(f"\n❌ Error generating user roles: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    run_with_diagnostics() 