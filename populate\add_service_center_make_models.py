import os
import sys
import django
import random

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from django.utils import timezone

try:
    from setup.models import ServiceCenter, ServiceCenterMakeModel, Vehicle
except ImportError as e:
    print(f"Could not import setup models: {e}")
    print("Please check app structure and model names.")
    sys.exit(1)


class ServiceCenterMakeModelGenerator:
    """Generate ServiceCenterMakeModel records to associate service centers with vehicle makes/models"""
    
    def __init__(self):
        print("ServiceCenterMakeModel Generator initialized")
        
    def run(self):
        """Run the service center make model generator"""
        print("Starting ServiceCenterMakeModel generation...")
        
        try:
            self.generate_service_center_make_models()
        except Exception as e:
            print(f"Error generating service center make models: {e}")
        
        print("\nServiceCenterMakeModel generation complete!")
    
    def generate_service_center_make_models(self):
        """Generate ServiceCenterMakeModel records"""
        # Get all service centers
        service_centers = list(ServiceCenter.objects.all())
        
        if not service_centers:
            print("No service centers found. Cannot generate make/model associations.")
            return 0
            
        # Get distinct makes and models from vehicles
        vehicle_makes = set()
        make_models = {}
        
        # Get vehicle makes and models
        for vehicle in Vehicle.objects.all():
            make = vehicle.make
            model = vehicle.model
            
            if make:
                vehicle_makes.add(make)
                
                if make not in make_models:
                    make_models[make] = set()
                
                if model:
                    make_models[make].add(model)
        
        if not vehicle_makes:
            print("No vehicle makes found. Cannot generate make/model associations.")
            return 0
        
        print(f"Found {len(service_centers)} service centers and {len(vehicle_makes)} vehicle makes")
        
        # Delete existing records
        ServiceCenterMakeModel.objects.all().delete()
        print("Cleared existing ServiceCenterMakeModel records")
        
        # Create new associations between service centers and makes/models
        created_count = 0
        
        # Default tenant ID - using the same tenant ID as the service centers
        default_tenant_id = None
        if service_centers:
            default_tenant_id = service_centers[0].tenant_id
            print(f"Using tenant_id: {default_tenant_id}")
        
        with transaction.atomic():
            # Loop through each service center
            for service_center in service_centers:
                # Use the service center's tenant ID
                tenant_id = service_center.tenant_id
                
                # Determine how many makes this service center will support (1-5)
                num_makes = min(random.randint(1, 5), len(vehicle_makes))
                
                # Randomly select makes for this service center
                selected_makes = random.sample(list(vehicle_makes), num_makes)
                
                for make in selected_makes:
                    # For each make, decide if we want to specify models or support all models
                    specify_models = random.choice([True, False])
                    
                    if specify_models and make in make_models and make_models[make]:
                        # Select specific models for this make
                        models_for_make = list(make_models[make])
                        num_models = min(random.randint(1, len(models_for_make)), len(models_for_make))
                        selected_models = random.sample(models_for_make, num_models)
                        
                        # Create a record for each model
                        for model in selected_models:
                            ServiceCenterMakeModel.objects.create(
                                tenant_id=tenant_id,
                                service_center=service_center,
                                make=make,
                                model=model,
                                is_active=True,
                                notes=f"Egyptian authorized service for {make} {model}"
                            )
                            created_count += 1
                            
                            if created_count % 50 == 0:
                                print(f"Created {created_count} service center make/model associations")
                    else:
                        # This service center supports all models for this make
                        ServiceCenterMakeModel.objects.create(
                            tenant_id=tenant_id,
                            service_center=service_center,
                            make=make,
                            model="*",  # Asterisk indicates all models
                            is_active=True,
                            notes=f"Egyptian authorized service for all {make} vehicles"
                        )
                        created_count += 1
                        
                        if created_count % 50 == 0:
                            print(f"Created {created_count} service center make/model associations")
        
        print(f"Successfully created {created_count} service center make/model associations")
        return created_count


if __name__ == "__main__":
    generator = ServiceCenterMakeModelGenerator()
    generator.run() 