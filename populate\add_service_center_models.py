import os
import sys
import django
import random

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from setup.models import ServiceCenter, ServiceCenterMakeModel

# Egyptian vehicle data
EGYPTIAN_MAKES_MODELS = [
    {"make": "تويوتا", "models": ["كورولا", "لاند كروزر", "كامري", "هايلكس", "فورتشنر", "راف 4", "يارس"]},
    {"make": "هيونداي", "models": ["النترا", "اكسنت", "توسان", "كريتا", "سوناتا", "فيرنا"]},
    {"make": "نيسان", "models": ["صني", "سنترا", "قشقاي", "جوك", "اكس تريل", "باترول"]},
    {"make": "شيفروليه", "models": ["أفيو", "أوبترا", "لانوس", "كروز", "كابتيفا", "سبارك"]},
    {"make": "كيا", "models": ["سبورتاج", "سيراتو", "بيكانتو", "ريو", "سورينتو", "كارنز"]},
    {"make": "رينو", "models": ["لوجان", "داستر", "ميجان", "كادجار", "ستيب واي", "سانديرو"]},
    {"make": "بي إم دبليو", "models": ["الفئة الثالثة", "الفئة الخامسة", "الفئة السابعة", "اكس1", "اكس3", "اكس5"]},
    {"make": "مرسيدس", "models": ["الفئة C", "الفئة E", "الفئة S", "GLA", "GLC", "GLE"]},
    {"make": "أودي", "models": ["A3", "A4", "A6", "Q2", "Q3", "Q5"]},
    {"make": "سوزوكي", "models": ["سويفت", "التو", "جيمني", "فيتارا", "اسبريسو", "ارتيجا"]},
    {"make": "ميتسوبيشي", "models": ["لانسر", "اوتلاندر", "باجيرو", "اكليبس", "اتراج", "سبيس ستار"]},
    {"make": "بيجو", "models": ["301", "208", "2008", "3008", "5008", "508"]},
    {"make": "فيات", "models": ["تيبو", "500", "دوبلو", "بونتو", "لينيا", "كوبو"]},
    {"make": "لادا", "models": ["جرانتا", "نيفا", "كالينا", "فيستا", "لارجوس", "ريو"]},
    {"make": "بروتون", "models": ["ساجا", "بريف", "اكزورا", "جين 2", "واجا", "سوبريما"]},
    {"make": "MG", "models": ["MG5", "MG6", "ZS", "HS", "RX5", "RX8"]},
    {"make": "BYD", "models": ["F3", "S6", "L3", "F0", "Tang", "Qin"]}
]

class ServiceCenterMakeModelGenerator:
    def __init__(self):
        print("Service Center Makes & Models generator initialized")
    
    def generate_data(self):
        """Generate service center makes and models data"""
        print("\nGenerating service center makes and models data...")
        
        # Get all service centers
        service_centers = list(ServiceCenter.objects.all())
        if not service_centers:
            print("No service centers found in the database. Please add service centers first.")
            return
        
        total_associations = 0
        
        # For each service center, assign some random makes and models
        for service_center in service_centers:
            # Skip if all vehicle makes are already served
            if service_center.serves_all_vehicle_makes:
                print(f"Service center {service_center.name} already serves all makes. Skipping.")
                continue
                
            # Decide how many makes this service center supports (3-8)
            num_makes = random.randint(3, 8)
            makes_to_add = random.sample(EGYPTIAN_MAKES_MODELS, num_makes)
            
            for make_data in makes_to_add:
                make = make_data["make"]
                
                # For each make, decide if the center supports all models or just some
                supports_all_models = random.choice([True, False])
                
                if supports_all_models:
                    # Create one entry with empty model to indicate all models are supported
                    ServiceCenterMakeModel.objects.get_or_create(
                        service_center=service_center,
                        make=make,
                        model="",  # Empty model means all models are supported
                        defaults={
                            "is_active": True,
                            "notes": "جميع الموديلات مدعومة"
                        }
                    )
                    total_associations += 1
                    print(f"Added {make} (all models) to {service_center.name}")
                else:
                    # Select a random subset of models to support
                    models = make_data["models"]
                    num_models = random.randint(1, len(models))
                    selected_models = random.sample(models, num_models)
                    
                    for model in selected_models:
                        ServiceCenterMakeModel.objects.get_or_create(
                            service_center=service_center,
                            make=make,
                            model=model,
                            defaults={
                                "is_active": True,
                                "notes": f"تمت إضافة {model} للخدمة"
                            }
                        )
                        total_associations += 1
                        print(f"Added {make} {model} to {service_center.name}")
            
        print(f"\nSuccessfully added {total_associations} make/model associations to service centers")
    
    # Add entry point method for the generator script to call
    def run(self):
        print("Starting Service Center Makes & Models data generation...")
        with transaction.atomic():
            self.generate_data()
        print("\nService Center Makes & Models data generation complete!")

if __name__ == "__main__":
    generator = ServiceCenterMakeModelGenerator()
    generator.run() 
 