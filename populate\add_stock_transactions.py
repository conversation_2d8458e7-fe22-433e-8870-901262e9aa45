import os
import sys
import django
import random
from datetime import datetime, timedelta

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from django.utils import timezone

# Import models from correct modules
try:
    from inventory.models import Item, Movement
    from warehouse.models import Location
except ImportError as e:
    print(f"Could not import inventory models: {e}")
    print("Please check app structure and model names.")
    sys.exit(1)
    
class StockTransactionGenerator:
    def __init__(self):
        print("Stock Transaction data generator initialized")
    
    def generate_transactions(self, count=200):
        """
        Generate inventory movement transactions
        """
        print(f"\nGenerating {count} stock transactions...")
        
        # Get items
        items = list(Item.objects.all())
        if not items:
            print("No items found. Please ensure Item table has data.")
            return
        
        # Get locations
        locations = list(Location.objects.all())
        if not locations:
            print("No locations found. Please ensure Location table has data.")
            return
        
        # Movement types with corresponding movement_type_ref (if available)
        movement_types = [
            'purchase',
            'sale',
            'adjustment',
            'transfer',
            'return'
        ]
        
        # Reference prefixes by movement type
        reference_prefixes = {
            'purchase': 'PO-',
            'sale': 'SO-',
            'adjustment': 'ADJ-',
            'transfer': 'TR-',
            'return': 'RET-'
        }
        
        # Create movements
        movements_created = 0
        
        for i in range(count):
            # Select random item and movement type
            item = random.choice(items)
            movement_type = random.choice(movement_types)
            
            # Determine if this is an inbound or outbound movement
            is_inbound = movement_type in ['purchase', 'return', 'adjustment']
            
            # Generate quantity (positive for inbound, negative for outbound)
            base_quantity = random.randint(1, 50)
            quantity = base_quantity if is_inbound else -base_quantity
            
            # Generate reference
            reference_prefix = reference_prefixes.get(movement_type, 'REF-')
            reference = f"{reference_prefix}{random.randint(10000, 99999)}"
            
            # Generate random date within the last year
            days_ago = random.randint(1, 365)
            movement_date = timezone.now() - timedelta(days=days_ago)
            
            # Optional notes
            notes_options = [
                "تم استلام البضاعة بواسطة محمد علي",
                "تم تسليم البضاعة للعميل",
                "تعديل المخزون بعد الجرد",
                "إرجاع بضاعة تالفة",
                "تحويل بين المخازن",
                "",  # Empty notes sometimes
            ]
            notes = random.choice(notes_options)
            
            # Create the movement record
            try:
                movement = Movement.objects.create(
                    item=item,
                    quantity=quantity,
                    movement_type=movement_type,
                    reference=reference,
                    notes=notes,
                    created_at=movement_date,
                    updated_at=movement_date
                )
                
                movements_created += 1
                if movements_created % 20 == 0:
                    print(f"Created {movements_created} movements...")
                    
            except Exception as e:
                print(f"Error creating movement {i+1}: {e}")
        
        print(f"Successfully created {movements_created} stock movements")
    
    def run(self):
        print("Starting Stock Transaction data generation...")
        with transaction.atomic():
            self.generate_transactions()
        print("\nStock Transaction data generation complete!")

if __name__ == "__main__":
    generator = StockTransactionGenerator()
    generator.run() 
 