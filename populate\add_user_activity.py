import os
import sys
import django
import random
from datetime import datetime, timedelta

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from django.utils import timezone
from django.contrib.auth.models import User

class UserActivityGenerator:
    def __init__(self):
        print("User Activity data generator initialized")
    
    def generate_user_activities(self, count=200):
        """
        Generate sample user activity records (without saving to database)
        """
        print(f"\nGenerating {count} user activity samples...")
        
        # Get users
        users = list(User.objects.all())
        if not users:
            print("No users found. Please ensure User table has data.")
            return
        
        # Actions
        actions = [
            "تسجيل الدخول",
            "تسجيل الخروج",
            "إنشاء عميل",
            "تعديل عميل",
            "إنشاء مركبة",
            "تعديل مركبة",
            "إنشاء طلب عمل",
            "تعديل طلب عمل",
            "إنشاء فاتورة",
            "دفع فاتورة",
            "إضافة مخزون",
            "تعديل مخزون",
            "طباعة تقرير",
            "تصدير بيانات",
            "تعديل إعدادات النظام",
            "إرسال إشعار للعميل"
        ]
        
        # Object types
        object_types = [
            "Customer",
            "Vehicle",
            "WorkOrder",
            "Invoice",
            "Payment",
            "Item",
            "ServiceCenter",
            "Report",
            "System",
            "Notification"
        ]
        
        # Generate activities
        activities_created = 0
        
        for i in range(count):
            # Select random user
            user = random.choice(users)
            
            # Select random action and object type
            action = random.choice(actions)
            object_type = random.choice(object_types)
            
            # Generate random object ID
            object_id = str(random.randint(1000, 9999))
            
            # Generate description based on action and object type
            description = f"{user.username} قام بـ {action} للكائن {object_type} ذو المعرف {object_id}"
            
            # Generate random IP address
            ip_parts = [str(random.randint(1, 255)) for _ in range(4)]
            ip_address = ".".join(ip_parts)
            
            # Generate random date within the last month
            days_ago = random.randint(0, 30)
            timestamp = timezone.now() - timedelta(days=days_ago, hours=random.randint(0, 23), minutes=random.randint(0, 59))
            
            # Generate metadata
            metadata = {
                "browser": random.choice(["Chrome", "Firefox", "Safari", "Edge"]),
                "os": random.choice(["Windows", "MacOS", "Linux", "iOS", "Android"]),
                "success": random.choice([True, True, True, False]),  # 75% success rate
                "duration_ms": random.randint(50, 5000)
            }
            
            # Just print sample data since we can't save to the database
            if i < 5 or i % 50 == 0:  # Show a few examples
                print(f"Activity sample {i+1}: User {user.username} - {action} - {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            
            activities_created += 1
            
            if activities_created % 50 == 0:
                print(f"Generated {activities_created} user activity samples...")
        
        print(f"\nSuccessfully generated {activities_created} user activity samples")
        print("NOTE: These records were not saved to the database since UserActivity model doesn't exist.")
        print("If you need to track user activities, consider creating a custom UserActivity model in your project.")
    
    def run(self):
        print("Starting User Activity data generation...")
        self.generate_user_activities()
        print("\nUser Activity data generation complete!")

if __name__ == "__main__":
    generator = UserActivityGenerator()
    generator.run() 
 