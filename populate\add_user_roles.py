import os
import sys
import django
import random
from django.db import transaction
from faker import Faker

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from django.contrib.auth.models import User, Group, Permission
from user_roles.models import Role, UserRole, ModulePermission
from setup.models import ServiceCenter, Franchise, Company

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale

class UserRolesGenerator:
    """Generate user roles and assign users to roles."""
    
    def __init__(self):
        self.tenants = self._get_tenant_ids()
        self.service_centers = {}
        self.franchises = {}
        self.companies = {}
        self.users = list(User.objects.all())
        
        for tenant_id in self.tenants:
            self.service_centers[tenant_id] = list(ServiceCenter.objects.filter(tenant_id=tenant_id))
            self.franchises[tenant_id] = list(Franchise.objects.filter(tenant_id=tenant_id))
            self.companies[tenant_id] = list(Company.objects.filter(tenant_id=tenant_id))
            
        print(f"Initialized UserRolesGenerator with {len(self.tenants)} tenants")
        print(f"Found {len(self.users)} users")
        for tenant_id in self.tenants:
            print(f"Tenant {tenant_id}: {len(self.service_centers.get(tenant_id, []))} service centers, "
                  f"{len(self.franchises.get(tenant_id, []))} franchises, "
                  f"{len(self.companies.get(tenant_id, []))} companies")
    
    def _get_tenant_ids(self):
        """Get unique tenant IDs from existing records."""
        tenant_ids = ServiceCenter.objects.values_list('tenant_id', flat=True).distinct()
        return tenant_ids
    
    def setup_default_roles(self):
        """Set up default roles if they don't exist."""
        print("Setting up default roles...")
        
        if Role.objects.exists():
            print("Roles already exist, skipping default role creation")
            return list(Role.objects.all())
        
        # Call the built-in method to create default roles
        Role.create_default_roles()
        
        roles = list(Role.objects.all())
        print(f"Created {len(roles)} default roles")
        
        # Set up module permissions for all roles
        self._setup_module_permissions(roles)
        
        return roles
    
    def _setup_module_permissions(self, roles):
        """Set up module permissions for all roles."""
        print("Setting up module permissions...")
        
        module_permissions_created = 0
        
        # Define modules and actions
        modules = [
            'setup', 'work_orders', 'inventory', 'warehouse',
            'sales', 'purchases', 'reports', 'settings'
        ]
        
        actions = ['view', 'add', 'change', 'delete', 'approve', 'report']
        
        # Create permissions for each role based on its type
        for role in roles:
            # Admin roles get full access
            if role.role_type in ['system_admin', 'franchise_admin', 'company_admin']:
                # Full access to all modules and actions
                for module in modules:
                    for action in actions:
                        ModulePermission.objects.get_or_create(
                            role=role,
                            module=module,
                            action=action
                        )
                        module_permissions_created += 1
            
            # Manager roles get full access to their modules but limited settings access
            elif 'manager' in role.role_type:
                for module in modules:
                    if module == 'settings':
                        # Only view for settings
                        ModulePermission.objects.get_or_create(
                            role=role,
                            module=module,
                            action='view'
                        )
                        module_permissions_created += 1
                    else:
                        # Full access to other modules
                        for action in actions:
                            if role.role_type.startswith('small_center') and module in ['warehouse', 'purchases']:
                                # Small center manager gets limited permissions for some modules
                                if action in ['view', 'add', 'change']:
                                    ModulePermission.objects.get_or_create(
                                        role=role,
                                        module=module,
                                        action=action
                                    )
                                    module_permissions_created += 1
                            else:
                                ModulePermission.objects.get_or_create(
                                    role=role,
                                    module=module,
                                    action=action
                                )
                                module_permissions_created += 1
            
            # Other roles get specific permissions based on their type
            else:
                if role.role_type == 'service_advisor':
                    # Service advisors can view, add, change and report
                    for module in ['work_orders', 'inventory', 'reports']:
                        for action in ['view', 'add', 'change', 'report']:
                            ModulePermission.objects.get_or_create(
                                role=role,
                                module=module,
                                action=action
                            )
                            module_permissions_created += 1
                
                elif role.role_type == 'technician':
                    # Technicians have limited permissions
                    for module in ['work_orders', 'inventory']:
                        for action in ['view', 'change']:  # Can update work order status
                            ModulePermission.objects.get_or_create(
                                role=role,
                                module=module,
                                action=action
                            )
                            module_permissions_created += 1
                
                elif role.role_type == 'parts_clerk':
                    # Parts clerks manage inventory and warehouse
                    for module in ['inventory', 'warehouse', 'sales', 'purchases', 'reports']:
                        for action in ['view', 'add', 'change', 'report']:
                            ModulePermission.objects.get_or_create(
                                role=role,
                                module=module,
                                action=action
                            )
                            module_permissions_created += 1
                
                elif role.role_type == 'cashier':
                    # Cashiers handle sales
                    for module in ['sales', 'work_orders', 'reports']:
                        if module == 'work_orders':
                            # View-only for work orders
                            ModulePermission.objects.get_or_create(
                                role=role,
                                module=module,
                                action='view'
                            )
                            module_permissions_created += 1
                        else:
                            for action in ['view', 'add', 'change', 'report']:
                                ModulePermission.objects.get_or_create(
                                    role=role,
                                    module=module,
                                    action=action
                                )
                                module_permissions_created += 1
        
        print(f"Created {module_permissions_created} module permissions")
    
    def assign_users_to_roles(self):
        """Assign existing users to appropriate roles."""
        print("Assigning users to roles...")
        
        # Get all roles
        roles = Role.objects.all()
        if not roles:
            print("No roles found, please run setup_default_roles() first")
            return []
        
        # Get roles by code for easier access
        roles_by_code = {role.code: role for role in roles}
        
        created_user_roles = []
        
        for tenant_id in self.tenants:
            franchises = self.franchises.get(tenant_id, [])
            companies = self.companies.get(tenant_id, [])
            service_centers = self.service_centers.get(tenant_id, [])
            
            if not service_centers:
                print(f"No service centers found for tenant {tenant_id}, skipping")
                continue
            
            # Assign system_admin role
            if self.users and 'system_admin' in roles_by_code:
                # Take first user as system admin if possible
                admin_user = self.users[0] if len(self.users) > 0 else None
                
                if admin_user:
                    system_admin_role = UserRole.objects.create(
                        tenant_id=tenant_id,
                        user=admin_user,
                        role=roles_by_code['system_admin'],
                        is_primary=True,
                        is_active=True
                    )
                    created_user_roles.append(system_admin_role)
                    print(f"Assigned system admin role to {admin_user.username}")
            
            # Assign franchise_admin roles
            if franchises and 'franchise_admin' in roles_by_code:
                for franchise in franchises:
                    # Get a random user that's not already a system admin
                    available_users = [u for u in self.users if u != admin_user] if 'admin_user' in locals() else self.users
                    if available_users:
                        franchise_admin_user = random.choice(available_users)
                        
                        franchise_admin_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=franchise_admin_user,
                            role=roles_by_code['franchise_admin'],
                            franchise=franchise,
                            is_primary=True,
                            is_active=True
                        )
                        created_user_roles.append(franchise_admin_role)
                        print(f"Assigned franchise admin role for {franchise.name} to {franchise_admin_user.username}")
            
            # Assign company_admin roles
            if companies and 'company_admin' in roles_by_code:
                for company in companies:
                    # Get a random user
                    if self.users:
                        company_admin_user = random.choice(self.users)
                        
                        company_admin_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=company_admin_user,
                            role=roles_by_code['company_admin'],
                            company=company,
                            is_primary=True,
                            is_active=True
                        )
                        created_user_roles.append(company_admin_role)
                        print(f"Assigned company admin role for {company.name} to {company_admin_user.username}")
            
            # Assign service center roles
            for service_center in service_centers:
                # Determine center size based on service center attributes or random
                if hasattr(service_center, 'size'):
                    size = service_center.size
                else:
                    # Randomly assign size if not defined
                    size = random.choice(['small', 'medium', 'large'])
                
                # Get suggested roles for this size
                suggested_roles = Role.get_suggested_roles_by_center_size(size)
                
                # Assign roles based on center size
                for job_function, role_code in suggested_roles.items():
                    if role_code in roles_by_code and self.users:
                        user = random.choice(self.users)
                        
                        # Create user role
                        user_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=user,
                            role=roles_by_code[role_code],
                            service_center=service_center,
                            is_primary=True,
                            is_active=True
                        )
                        created_user_roles.append(user_role)
                        print(f"Assigned {role_code} role at {service_center.name} to {user.username}")
                
                # For medium and large centers, assign multiple technicians
                if size in ['medium', 'large'] and 'technician' in roles_by_code and len(self.users) >= 3:
                    num_techs = 5 if size == 'large' else 3
                    techs = random.sample(self.users, min(num_techs, len(self.users)))
                    
                    for tech in techs:
                        # Skip if this user is already a primary technician
                        if UserRole.objects.filter(user=tech, role__code='technician', is_primary=True).exists():
                            continue
                            
                        tech_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=tech,
                            role=roles_by_code['technician'],
                            service_center=service_center,
                            is_primary=False,  # Secondary role
                            is_active=True
                        )
                        created_user_roles.append(tech_role)
        
        print(f"Created {len(created_user_roles)} user role assignments")
        return created_user_roles
    
    def create_additional_demo_roles(self):
        """Create additional demo user roles for more realistic scenarios."""
        print("Creating additional demo user roles for better testing...")
        
        if not self.users or len(self.users) < 5:
            print("Not enough users to create additional demo roles, skipping")
            return []
            
        # Get all roles
        roles = Role.objects.all()
        if not roles:
            print("No roles found, please run setup_default_roles() first")
            return []
            
        # Get roles by code for easier access
        roles_by_code = {role.code: role for role in roles}
        
        created_user_roles = []
        
        # For each tenant, create some cross-service-center roles to simulate
        # staff that work at multiple locations
        for tenant_id in self.tenants:
            service_centers = self.service_centers.get(tenant_id, [])
            
            if len(service_centers) < 2:
                continue
                
            # Create users with multiple service center technician roles
            multi_location_techs = random.sample(self.users, min(3, len(self.users)))
            
            for user in multi_location_techs:
                # Get 2-3 random service centers
                centers = random.sample(service_centers, min(random.randint(2, 3), len(service_centers)))
                
                # First center gets the primary role
                primary_center = centers[0]
                
                # Check if user already has a technician role at any center
                has_tech_role = UserRole.objects.filter(
                    tenant_id=tenant_id,
                    user=user,
                    role__code='technician'
                ).exists()
                
                if not has_tech_role and 'technician' in roles_by_code:
                    primary_role = UserRole.objects.create(
                        tenant_id=tenant_id,
                        user=user,
                        role=roles_by_code['technician'],
                        service_center=primary_center,
                        is_primary=True,
                        is_active=True
                    )
                    created_user_roles.append(primary_role)
                    print(f"Created primary technician role for {user.username} at {primary_center.name}")
                
                # Secondary centers get non-primary roles
                for center in centers[1:]:
                    if 'technician' in roles_by_code:
                        secondary_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=user,
                            role=roles_by_code['technician'],
                            service_center=center,
                            is_primary=False,
                            is_active=True
                        )
                        created_user_roles.append(secondary_role)
                        print(f"Created secondary technician role for {user.username} at {center.name}")
            
            # Create some part-time service advisors (showing different patterns of role assignment)
            if 'service_advisor' in roles_by_code and len(self.users) >= 3:
                part_time_advisors = random.sample(self.users, min(2, len(self.users)))
                
                for user in part_time_advisors:
                    # Get 1-2 random service centers
                    centers = random.sample(service_centers, min(random.randint(1, 2), len(service_centers)))
                    
                    for center in centers:
                        # Check if user already has a role at this center
                        has_role_at_center = UserRole.objects.filter(
                            tenant_id=tenant_id,
                            user=user,
                            service_center=center
                        ).exists()
                        
                        if not has_role_at_center:
                            # 50% chance to be primary if user has no other primary roles
                            is_primary = False
                            if not UserRole.objects.filter(user=user, is_primary=True).exists():
                                is_primary = random.choice([True, False])
                                
                            advisor_role = UserRole.objects.create(
                                tenant_id=tenant_id,
                                user=user,
                                role=roles_by_code['service_advisor'],
                                service_center=center,
                                is_primary=is_primary,
                                is_active=True
                            )
                            created_user_roles.append(advisor_role)
                            role_type = "primary" if is_primary else "secondary"
                            print(f"Created {role_type} service advisor role for {user.username} at {center.name}")
            
            # Create some staff with multiple roles at the same location
            if len(service_centers) > 0 and len(self.users) >= 3:
                multi_role_users = random.sample(self.users, min(3, len(self.users)))
                
                for user in multi_role_users:
                    # Select a random service center
                    center = random.choice(service_centers)
                    
                    # Select complementary roles (e.g., parts_clerk + cashier)
                    role_pairs = [
                        ('parts_clerk', 'cashier'),
                        ('service_advisor', 'cashier'),
                        ('medium_center_advisor', 'medium_center_cashier'),
                        ('medium_center_parts', 'medium_center_cashier')
                    ]
                    
                    selected_pair = random.choice(role_pairs)
                    
                    # Determine which role is primary (randomly)
                    is_first_primary = random.choice([True, False])
                    
                    # Create the first role if it exists
                    if selected_pair[0] in roles_by_code:
                        # Check if user already has this role at this center
                        has_role_already = UserRole.objects.filter(
                            tenant_id=tenant_id,
                            user=user,
                            role=roles_by_code[selected_pair[0]],
                            service_center=center
                        ).exists()
                        
                        if not has_role_already:
                            first_role = UserRole.objects.create(
                                tenant_id=tenant_id,
                                user=user,
                                role=roles_by_code[selected_pair[0]],
                                service_center=center,
                                is_primary=is_first_primary,
                                is_active=True
                            )
                            created_user_roles.append(first_role)
                            role_type = "primary" if is_first_primary else "secondary"
                            print(f"Created {role_type} {selected_pair[0]} role for {user.username} at {center.name}")
                    
                    # Create the second role if it exists
                    if selected_pair[1] in roles_by_code:
                        # Check if user already has this role at this center
                        has_role_already = UserRole.objects.filter(
                            tenant_id=tenant_id,
                            user=user,
                            role=roles_by_code[selected_pair[1]],
                            service_center=center
                        ).exists()
                        
                        if not has_role_already:
                            second_role = UserRole.objects.create(
                                tenant_id=tenant_id,
                                user=user,
                                role=roles_by_code[selected_pair[1]],
                                service_center=center,
                                is_primary=not is_first_primary,
                                is_active=True
                            )
                            created_user_roles.append(second_role)
                            role_type = "primary" if not is_first_primary else "secondary"
                            print(f"Created {role_type} {selected_pair[1]} role for {user.username} at {center.name}")
        
        # Create some inactive roles for testing status filtering
        if self.users:
            for _ in range(min(5, len(self.users))):
                user = random.choice(self.users)
                if 'service_advisor' in roles_by_code:
                    for tenant_id in self.tenants:
                        service_centers = self.service_centers.get(tenant_id, [])
                        if service_centers:
                            center = random.choice(service_centers)
                            
                            # Create inactive role
                            inactive_role = UserRole.objects.create(
                                tenant_id=tenant_id,
                                user=user,
                                role=roles_by_code['service_advisor'],
                                service_center=center,
                                is_primary=False,
                                is_active=False  # Inactive role
                            )
                            created_user_roles.append(inactive_role)
                            print(f"Created inactive service advisor role for {user.username} at {center.name}")
        
        print(f"Created {len(created_user_roles)} additional user role assignments")
        return created_user_roles
    
    def create_demo_users(self, count=10):
        """Create demo users if needed."""
        print("Checking for existing users...")
        
        existing_users = User.objects.count()
        
        if existing_users >= count:
            print(f"Already have {existing_users} users, skipping user creation")
            return list(User.objects.all())
        
        # Create additional users
        users_to_create = count - existing_users
        print(f"Creating {users_to_create} additional demo users...")
        
        created_users = []
        
        for i in range(users_to_create):
            first_name = fake.first_name()
            last_name = fake.last_name()
            username = f"{first_name.lower()}.{last_name.lower()}"
            
            # Ensure username is unique
            suffix = 1
            temp_username = username
            while User.objects.filter(username=temp_username).exists():
                temp_username = f"{username}{suffix}"
                suffix += 1
                
            username = temp_username
            
            # Create user
            user = User.objects.create_user(
                username=username,
                email=f"{username}@example.com",
                password="password123",  # Simple password for demo
                first_name=first_name,
                last_name=last_name
            )
            created_users.append(user)
            print(f"Created user: {username}")
        
        # Update the users list
        self.users = list(User.objects.all())
        
        print(f"Created {len(created_users)} new users, total users: {User.objects.count()}")
        return created_users
    
    @transaction.atomic
    def run(self):
        """Run the user roles generator."""
        print("Starting User Roles Generator...")
        
        # Check if we have service centers
        if not ServiceCenter.objects.exists():
            print("❌ No service centers found. Please run add_service_center_data.py first.")
            return
        
        # Create demo users if needed
        self.create_demo_users(20)  # Increased number for more role combinations
        
        # Set up default roles and permissions
        self.setup_default_roles()
        
        # Assign users to roles
        self.assign_users_to_roles()
        
        # Create additional demo roles for testing
        self.create_additional_demo_roles()
        
        print("✅ User roles generation completed!")


def main():
    """Execute the user roles generator."""
    generator = UserRolesGenerator()
    generator.run()


if __name__ == "__main__":
    main() 