import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models
from user_roles.models import Role, ModulePermission

# Get all roles
roles = Role.objects.all().order_by('name')

print(f"Total roles: {roles.count()}")
print("\nAll roles:")
for role in roles:
    print(f"- {role.name} ({role.role_type})")
    
    # Get module permissions for this role
    permissions = ModulePermission.objects.filter(role=role).order_by('module', 'action')
    if permissions.exists():
        print("  Permissions:")
        for perm in permissions:
            print(f"  - {perm.module}: {perm.action}")
    
    print()

print("Role creation complete.") 