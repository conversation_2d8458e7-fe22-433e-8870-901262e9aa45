import os
import sys
import django

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models
from setup.models import Customer, Vehicle, ServiceCenter
from inventory.models import Item

def check_tenant_ids():
    """Check tenant IDs across different models"""
    
    # Check customers
    customers = list(Customer.objects.all())
    customer_tenant_ids = set()
    if customers:
        print(f"Found {len(customers)} customers")
        for customer in customers[:3]:
            tenant_id = customer.tenant_id
            customer_tenant_ids.add(tenant_id)
            print(f"  - Customer: {customer}, Tenant ID: {tenant_id}")
    else:
        print("No customers found")
    
    # Check vehicles
    vehicles = list(Vehicle.objects.all())
    vehicle_tenant_ids = set()
    if vehicles:
        print(f"Found {len(vehicles)} vehicles")
        for vehicle in vehicles[:3]:
            tenant_id = vehicle.tenant_id
            vehicle_tenant_ids.add(tenant_id)
            print(f"  - Vehicle: {vehicle}, Tenant ID: {tenant_id}")
    else:
        print("No vehicles found")
    
    # Check service centers
    service_centers = list(ServiceCenter.objects.all())
    service_center_tenant_ids = set()
    if service_centers:
        print(f"Found {len(service_centers)} service centers")
        for service_center in service_centers[:3]:
            tenant_id = service_center.tenant_id
            service_center_tenant_ids.add(tenant_id)
            print(f"  - Service Center: {service_center}, Tenant ID: {tenant_id}")
    else:
        print("No service centers found")
    
    # Check inventory items
    items = list(Item.objects.all())
    item_tenant_ids = set()
    if items:
        print(f"Found {len(items)} inventory items")
        for item in items[:3]:
            tenant_id = item.tenant_id
            item_tenant_ids.add(tenant_id)
            print(f"  - Item: {item}, Tenant ID: {tenant_id}")
    else:
        print("No inventory items found")
    
    # Check tenant ID consistency
    all_tenant_ids = customer_tenant_ids | vehicle_tenant_ids | service_center_tenant_ids | item_tenant_ids
    print(f"\nFound {len(all_tenant_ids)} unique tenant IDs across all models:")
    for tenant_id in all_tenant_ids:
        print(f"  - {tenant_id}")
    
    if len(all_tenant_ids) > 1:
        print("\nWarning: Multiple tenant IDs found, which may cause issues with cross-model relationships")
    
if __name__ == "__main__":
    check_tenant_ids() 