import os
import sys
import django
import uuid

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models after Django setup
from inventory.models import (
    Item, UnitOfMeasurement, UnitConversion, ItemClassification,
    Movement, MovementType, VehicleCompatibility, OperationCompatibility,
    VehicleOperationCompatibility, OperationPricing, PartPricing
)
from setup.models import (
    VehicleMake, VehicleModel, Vehicle, Customer, ServiceCenter,
    ServiceCenterType, Company, Franchise
)
from work_orders.models import WorkOrderType, MaintenanceSchedule

def create_tenant_id():
    """Create a consistent tenant ID for demo data"""
    return uuid.UUID('a385e9a3-0e6b-4e44-aa87-6a6510d6c2b7')

def clear_demo_data():
    tenant_id = create_tenant_id()
    print(f"Clearing demo data with tenant_id: {tenant_id}")
    
    # Clear all data in reverse order of dependencies
    print("Clearing movements...")
    Movement.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing part pricing...")
    PartPricing.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing operation pricing...")
    OperationPricing.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing vehicle operation compatibilities...")
    VehicleOperationCompatibility.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing operation compatibilities...")
    OperationCompatibility.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing vehicle compatibilities...")
    VehicleCompatibility.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing inventory items...")
    Item.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing movement types...")
    MovementType.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing item classifications...")
    ItemClassification.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing unit conversions...")
    UnitConversion.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing units of measurement...")
    UnitOfMeasurement.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing work order types and maintenance schedules...")
    WorkOrderType.objects.filter(tenant_id=tenant_id).delete()
    MaintenanceSchedule.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing vehicles...")
    Vehicle.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing vehicle makes and models...")
    VehicleModel.objects.filter(tenant_id=tenant_id).delete()
    VehicleMake.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing customers...")
    Customer.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing service centers...")
    ServiceCenter.objects.filter(tenant_id=tenant_id).delete()
    
    print("Clearing service center types...")
    ServiceCenterType.objects.all().delete()
    
    print("Clearing companies...")
    Company.objects.all().delete()
    
    print("Clearing franchises...")
    Franchise.objects.all().delete()
    
    print("تم مسح البيانات بنجاح!")

if __name__ == "__main__":
    clear_demo_data() 