import os
import sys
import django
import uuid
from decimal import Decimal

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models after Django setup
from inventory.models import (
    Item, OperationCompatibility, VehicleOperationCompatibility
)
from setup.models import VehicleMake, VehicleModel
from work_orders.models import WorkOrderType, MaintenanceSchedule

def create_tenant_id():
    """Create a consistent tenant ID for demo data"""
    return uuid.UUID('a385e9a3-0e6b-4e44-aa87-6a6510d6c2b7')

def create_operation_compatibilities():
    tenant_id = create_tenant_id()
    print(f"Creating operation compatibilities with tenant_id: {tenant_id}")
    
    # Get or create WorkOrderTypes
    print("Creating WorkOrderTypes...")
    oil_change_type, created = WorkOrderType.objects.get_or_create(
        tenant_id=tenant_id,
        name="تغيير زيت",
        defaults={
            'description': "خدمة تغيير زيت وفلتر المحرك",
            'color_code': "#4CAF50",
            'is_active': True,
        }
    )
    
    brake_service_type, created = WorkOrderType.objects.get_or_create(
        tenant_id=tenant_id,
        name="صيانة الفرامل",
        defaults={
            'description': "خدمة فحص وإصلاح نظام الفرامل",
            'color_code': "#2196F3",
            'is_active': True,
        }
    )
    
    headlight_replace_type, created = WorkOrderType.objects.get_or_create(
        tenant_id=tenant_id,
        name="استبدال الفانوس",
        defaults={
            'description': "خدمة استبدال لمبة أو فانوس كامل",
            'color_code': "#FFC107",
            'is_active': True,
        }
    )
    
    # Get or create maintenance schedule
    print("Creating MaintenanceSchedule...")
    elantra_schedule, created = MaintenanceSchedule.objects.get_or_create(
        tenant_id=tenant_id,
        name="جدول صيانة هيونداي إلنترا",
        defaults={
            'description': "جدول الصيانة القياسي لهيونداي إلنترا",
            'interval_type': 'mileage',
            'mileage_interval': 10000,
            'vehicle_make': "هيونداي",
            'vehicle_model': "إلنترا",
            'is_active': True,
        }
    )
    
    # Get items from demo data
    print("Fetching inventory items...")
    try:
        engine_oil = Item.objects.get(tenant_id=tenant_id, sku="OIL-5W30-1L")
        oil_filter = Item.objects.get(tenant_id=tenant_id, sku="FLTR-OIL-HYU")
        brake_pads = Item.objects.get(tenant_id=tenant_id, sku="BRK-PAD-ELAN")
        headlight = Item.objects.get(tenant_id=tenant_id, sku="LIGHT-ELAN-FR")
    except Item.DoesNotExist:
        print("Error: Required inventory items not found. Run demo_data.py first!")
        return
    
    # Get vehicle make and model
    print("Fetching vehicle make and model...")
    try:
        hyundai = VehicleMake.objects.get(tenant_id=tenant_id, name="هيونداي")
        elantra = VehicleModel.objects.get(tenant_id=tenant_id, name="إلنترا", make=hyundai)
    except (VehicleMake.DoesNotExist, VehicleModel.DoesNotExist):
        print("Error: Required vehicle make/model not found. Run demo_data.py first!")
        return
    
    # Create operation compatibilities
    print("Creating operation compatibilities...")
    
    # Oil Change Operation
    oil_change_compat, created = OperationCompatibility.objects.get_or_create(
        tenant_id=tenant_id,
        item=engine_oil,
        operation_type=oil_change_type,
        defaults={
            'is_required': True,
            'is_common': True,
            'maintenance_schedule': elantra_schedule,
            'operation_description': 'oil_change',
            'duration_minutes': 30,
        }
    )
    
    filter_change_compat, created = OperationCompatibility.objects.get_or_create(
        tenant_id=tenant_id,
        item=oil_filter,
        operation_type=oil_change_type,
        defaults={
            'is_required': True,
            'is_common': True,
            'maintenance_schedule': elantra_schedule,
            'operation_description': 'oil_change',
            'duration_minutes': 30,
        }
    )
    
    # Brake Service Operation
    brake_service_compat, created = OperationCompatibility.objects.get_or_create(
        tenant_id=tenant_id,
        item=brake_pads,
        operation_type=brake_service_type,
        defaults={
            'is_required': True,
            'is_common': True,
            'maintenance_schedule': elantra_schedule,
            'operation_description': 'brake_service',
            'duration_minutes': 60,
        }
    )
    
    # Headlight Replacement Operation
    headlight_replace_compat, created = OperationCompatibility.objects.get_or_create(
        tenant_id=tenant_id,
        item=headlight,
        operation_type=headlight_replace_type,
        defaults={
            'is_required': True,
            'is_common': True,
            'maintenance_schedule': elantra_schedule,
            'operation_description': 'other',
            'duration_minutes': 45,
        }
    )
    
    # Create Vehicle Operation Compatibilities
    print("Creating vehicle operation compatibilities...")
    
    # Link oil change to Hyundai Elantra
    VehicleOperationCompatibility.objects.get_or_create(
        tenant_id=tenant_id,
        operation_compatibility=oil_change_compat,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        year_from=2006,
        year_to=2010,
        defaults={
            'duration_minutes': 25,  # Specific for Elantra
        }
    )
    
    VehicleOperationCompatibility.objects.get_or_create(
        tenant_id=tenant_id,
        operation_compatibility=filter_change_compat,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        year_from=2006,
        year_to=2010,
        defaults={
            'duration_minutes': 15,  # Specific for Elantra
        }
    )
    
    # Link brake service to Hyundai Elantra
    VehicleOperationCompatibility.objects.get_or_create(
        tenant_id=tenant_id,
        operation_compatibility=brake_service_compat,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        year_from=2006,
        year_to=2010,
        defaults={
            'duration_minutes': 50,  # Specific for Elantra
        }
    )
    
    # Link headlight replacement to Hyundai Elantra
    VehicleOperationCompatibility.objects.get_or_create(
        tenant_id=tenant_id,
        operation_compatibility=headlight_replace_compat,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        year_from=2006,
        year_to=2010,
        defaults={
            'duration_minutes': 30,  # Specific for Elantra
        }
    )
    
    print("تم إنشاء توافقات العمليات بنجاح!")

if __name__ == "__main__":
    create_operation_compatibilities() 