import os
import sys
import django
import uuid
from decimal import Decimal
from datetime import date, timedelta

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models after Django setup
from inventory.models import (
    Item, OperationPricing, PartPricing
)
from setup.models import VehicleMake, VehicleModel, ServiceCenter, Company, Franchise
from work_orders.models import WorkOrderType

def create_tenant_id():
    """Create a consistent tenant ID for demo data"""
    return uuid.UUID('a385e9a3-0e6b-4e44-aa87-6a6510d6c2b7')

def create_pricing_data():
    tenant_id = create_tenant_id()
    print(f"Creating pricing data with tenant_id: {tenant_id}")
    
    # Get existing objects
    print("Fetching existing objects...")
    try:
        # Vehicle data
        hyundai = VehicleMake.objects.get(tenant_id=tenant_id, name="Hyundai")
        elantra = VehicleModel.objects.get(tenant_id=tenant_id, name="Elantra", vehicle_make=hyundai)
        
        # Service location
        service_center = ServiceCenter.objects.get(tenant_id=tenant_id, name="Cairo Auto Service Center")
        
        # Company and franchise (create if not exist)
        franchise = Franchise.objects.get_or_create(
            tenant_id=tenant_id,
            name="Egyptian Auto Services",
            code="EAS",
            defaults={
                'address': "14 Pyramids Road, Giza, Egypt",
                'is_active': True
            }
        )[0]
        
        company = Company.objects.get_or_create(
            tenant_id=tenant_id,
            name="Cairo Auto Group",
            code="CAG",
            franchise=franchise,
            defaults={
                'address': "25 Nile Street, Cairo, Egypt",
                'is_active': True
            }
        )[0]
        
        # Update service center with company
        service_center.company = company
        service_center.save()
        
        # Work order types
        oil_change_type = WorkOrderType.objects.get(tenant_id=tenant_id, code="OIL-CHANGE")
        brake_service_type = WorkOrderType.objects.get(tenant_id=tenant_id, code="BRAKE-SVC")
        headlight_replace_type = WorkOrderType.objects.get(tenant_id=tenant_id, code="LIGHT-REPL")
        
        # Inventory items
        engine_oil = Item.objects.get(tenant_id=tenant_id, sku="OIL-5W30-1L")
        oil_filter = Item.objects.get(tenant_id=tenant_id, sku="FLTR-OIL-HYU")
        brake_pads = Item.objects.get(tenant_id=tenant_id, sku="BRK-PAD-ELAN")
        headlight = Item.objects.get(tenant_id=tenant_id, sku="LIGHT-ELAN-FR")
        
    except (VehicleMake.DoesNotExist, VehicleModel.DoesNotExist, 
            ServiceCenter.DoesNotExist, WorkOrderType.DoesNotExist, 
            Item.DoesNotExist) as e:
        print(f"Error: Required objects not found - {e}")
        print("Make sure to run demo_data.py and create_operation_compatibilities.py first!")
        return
    
    # Create Operation Pricing data
    print("Creating Operation Pricing data...")
    
    # Oil Change Operation Pricing
    OperationPricing.objects.get_or_create(
        tenant_id=tenant_id,
        operation_type=oil_change_type,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        service_center=service_center,
        year_from=2006,
        year_to=2010,
        defaults={
            'base_price': Decimal('150.00'),
            'labor_hours': Decimal('1.0'),
            'labor_rate': Decimal('50.00'),
            'is_active': True,
            'notes': "Standard oil change service for Hyundai Elantra"
        }
    )
    
    # Brake Service Operation Pricing
    OperationPricing.objects.get_or_create(
        tenant_id=tenant_id,
        operation_type=brake_service_type,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        service_center=service_center,
        year_from=2006,
        year_to=2010,
        defaults={
            'base_price': Decimal('300.00'),
            'labor_hours': Decimal('2.0'),
            'labor_rate': Decimal('60.00'),
            'is_active': True,
            'notes': "Front brake service for Hyundai Elantra"
        }
    )
    
    # Headlight Replacement Operation Pricing
    OperationPricing.objects.get_or_create(
        tenant_id=tenant_id,
        operation_type=headlight_replace_type,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        service_center=service_center,
        year_from=2006,
        year_to=2010,
        defaults={
            'base_price': Decimal('120.00'),
            'labor_hours': Decimal('0.75'),
            'labor_rate': Decimal('50.00'),
            'is_active': True,
            'notes': "Headlight replacement service for Hyundai Elantra"
        }
    )
    
    # Create franchise level pricing (lower prices)
    OperationPricing.objects.get_or_create(
        tenant_id=tenant_id,
        operation_type=oil_change_type,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        franchise=franchise,
        company=None,
        service_center=None,
        year_from=2006,
        year_to=2010,
        defaults={
            'base_price': Decimal('140.00'),
            'labor_hours': Decimal('1.0'),
            'labor_rate': Decimal('45.00'),
            'is_active': True,
            'notes': "Franchise standard pricing for oil change service"
        }
    )
    
    # Create Part Pricing data
    print("Creating Part Pricing data...")
    
    # Current date for valid_from
    today = date.today()
    
    # Engine Oil Pricing - service center specific
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=engine_oil,
        service_center=service_center,
        valid_from=today,
        defaults={
            'price': Decimal('32.99'),
            'is_special_pricing': False,
            'is_active': True,
            'notes': "Standard pricing for engine oil at Cairo Auto Service Center"
        }
    )
    
    # Engine Oil Pricing - special promotion
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=engine_oil,
        service_center=service_center,
        valid_from=today,
        valid_to=today + timedelta(days=30),
        is_special_pricing=True,
        defaults={
            'price': Decimal('28.50'),
            'is_active': True,
            'notes': "30-day promotional pricing for engine oil"
        }
    )
    
    # Oil Filter Pricing
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=oil_filter,
        service_center=service_center,
        valid_from=today,
        defaults={
            'price': Decimal('15.99'),
            'is_special_pricing': False,
            'is_active': True,
            'notes': "Standard pricing for oil filter"
        }
    )
    
    # Brake Pads Pricing
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=brake_pads,
        service_center=service_center,
        valid_from=today,
        defaults={
            'price': Decimal('55.00'),
            'is_special_pricing': False,
            'is_active': True,
            'notes': "Standard pricing for brake pads"
        }
    )
    
    # Headlight Assembly Pricing
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=headlight,
        service_center=service_center,
        valid_from=today,
        defaults={
            'price': Decimal('110.00'),
            'is_special_pricing': False,
            'is_active': True,
            'notes': "Standard pricing for headlight assembly"
        }
    )
    
    # Create operation-specific part pricing
    # Oil Filter specific pricing for Oil Change operation
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=oil_filter,
        service_center=service_center,
        operation_type=oil_change_type,
        valid_from=today,
        defaults={
            'price': Decimal('14.99'),  # Slightly cheaper when part of an oil change
            'is_special_pricing': False,
            'is_active': True,
            'notes': "Special pricing when used in oil change operation"
        }
    )
    
    # Brake pads specific pricing for Brake Service operation
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=brake_pads,
        service_center=service_center,
        operation_type=brake_service_type,
        valid_from=today,
        defaults={
            'price': Decimal('52.50'),  # Slightly cheaper when part of a brake service
            'is_special_pricing': False,
            'is_active': True,
            'notes': "Special pricing when used in brake service operation"
        }
    )
    
    # Add vehicle-specific pricing
    # Special pricing for Hyundai Elantra brake pads
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=brake_pads,
        service_center=service_center,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        valid_from=today,
        defaults={
            'price': Decimal('50.00'),  # Special price for Elantra specifically
            'is_special_pricing': False,
            'is_active': True,
            'notes': "Elantra-specific pricing for brake pads"
        }
    )
    
    print("Pricing data created successfully!")

if __name__ == "__main__":
    create_pricing_data() 