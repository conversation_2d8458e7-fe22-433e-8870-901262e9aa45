import os
import sys
import django
import inspect

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models
from setup.models import Vehicle, Customer, ServiceCenter

def debug_vehicle_model():
    print("Debugging Vehicle model...")
    
    # Check for 'owner_name' in attributes
    attrs = dir(Vehicle)
    print("'owner_name' in dir(Vehicle):", 'owner_name' in attrs)
    
    # Check vehicle creation process
    try:
        # Create a basic vehicle to examine what happens
        vehicle = Vehicle()
        
        # Examine what attributes can be set
        print("\nExamining Vehicle attributes:")
        for attr in ['make', 'model', 'year', 'owner', 'owner_name', 'tenant_id', 'attributes']:
            has_attr = hasattr(vehicle, attr)
            print(f"Has attribute '{attr}': {has_attr}")
            
        # Check if Vehicle model or any parent classes override __setattr__
        print("\nChecking for __setattr__ in class hierarchy:")
        for cls in Vehicle.__mro__:
            if '__setattr__' in cls.__dict__:
                print(f"Found __setattr__ in {cls.__name__}")
                print(inspect.getsource(cls.__setattr__))
            else:
                print(f"No __setattr__ in {cls.__name__}")
                
        # Check all save-related methods
        print("\nExamining save methods:")
        for method_name in ['save', 'save_base', '_save_table', '_save_parents']:
            if hasattr(Vehicle, method_name) and callable(getattr(Vehicle, method_name)):
                method = getattr(Vehicle, method_name)
                if not method.__qualname__.startswith('Model.'):  # Only show if overridden
                    print(f"\nMethod: {method_name}")
                    try:
                        print(inspect.getsource(method))
                    except (TypeError, OSError):
                        print(f"Could not get source for {method_name}")
        
        # Try to create a real vehicle
        print("\nAttempting to create a vehicle:")
        customer = Customer.objects.first()
        service_center = ServiceCenter.objects.first()
        
        if not customer:
            print("No customers found in the database")
        else:
            print(f"Using customer: {customer}")
            
        if not service_center:
            print("No service centers found in the database")
        else:
            print(f"Using service center: {service_center}")
            
        v = Vehicle(
            tenant_id="12345678-1234-1234-1234-123456789012",
            make="Test Make",
            model="Test Model",
            year=2023,
            owner=customer,
            service_center=service_center,
            attributes={}
        )
        
        # Try setting each attribute separately to see where it fails
        print("\nTesting attribute setting:")
        try:
            v = Vehicle()
            v.tenant_id = "12345678-1234-1234-1234-123456789012"
            print("Set tenant_id: OK")
            v.make = "Test Make"
            print("Set make: OK")
            v.model = "Test Model"
            print("Set model: OK")
            v.year = 2023
            print("Set year: OK")
            v.owner = customer  # This might fail
            print("Set owner: OK")
            # Trying to set owner_name to see what happens
            try:
                v.owner_name = "Test Owner"
                print("Set owner_name: OK")
            except Exception as e:
                print(f"Error setting owner_name: {e}")
            v.service_center = service_center
            print("Set service_center: OK")
            v.attributes = {}
            print("Set attributes: OK")
        except Exception as e:
            print(f"Error during attribute setting: {e}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    
if __name__ == "__main__":
    debug_vehicle_model() 