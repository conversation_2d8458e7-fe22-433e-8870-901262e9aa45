import os
import sys
import django
import uuid
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models after Django setup
from django.contrib.auth.models import User
from inventory.models import (
    Item, UnitOfMeasurement, UnitConversion, ItemClassification,
    Movement, MovementType, VehicleCompatibility, OperationCompatibility
)
from setup.models import (
    VehicleMake, VehicleModel, Vehicle, Customer, ServiceCenter, 
    ServiceCenterType, Company, Franchise
)

def create_tenant_id():
    """Create a consistent tenant ID for demo data"""
    return uuid.UUID('a385e9a3-0e6b-4e44-aa87-6a6510d6c2b7')

def create_demo_data():
    tenant_id = create_tenant_id()
    print(f"Creating demo data with tenant_id: {tenant_id}")
    
    # Create Units of Measurement
    print("Creating Units of Measurement...")
    pieces = UnitOfMeasurement.objects.create(
        tenant_id=tenant_id,
        name="قطعة",
        symbol="pcs",
        is_base_unit=True,
        description="Individual items"
    )
    
    liters = UnitOfMeasurement.objects.create(
        tenant_id=tenant_id,
        name="لتر",
        symbol="L",
        is_base_unit=True,
        description="Volume measurement"
    )
    
    # Create Unit Conversions
    print("Creating Unit Conversions...")
    UnitConversion.objects.create(
        tenant_id=tenant_id,
        from_unit=liters,
        to_unit=pieces,
        conversion_factor=Decimal('1.0')
    )
    
    # Create Item Classifications
    print("Creating Item Classifications...")
    parts_classification = ItemClassification.objects.create(
        tenant_id=tenant_id,
        name="قطع غيار",
        code="PARTS",
        description="قطع غيار السيارات",
        level=0,
        is_active=True
    )
    
    engine_parts = ItemClassification.objects.create(
        tenant_id=tenant_id,
        name="قطع غيار المحرك",
        code="ENGINE",
        description="قطع غيار المحرك",
        parent=parts_classification,
        level=1,
        is_active=True
    )
    
    body_parts = ItemClassification.objects.create(
        tenant_id=tenant_id,
        name="قطع غيار الهيكل",
        code="BODY",
        description="قطع غيار هيكل السيارة",
        parent=parts_classification,
        level=1,
        is_active=True
    )
    
    fluids = ItemClassification.objects.create(
        tenant_id=tenant_id,
        name="سوائل",
        code="FLUIDS",
        description="سوائل السيارة",
        parent=parts_classification,
        level=1,
        is_active=True
    )
    
    # Create Movement Types
    print("Creating Movement Types...")
    purchase = MovementType.objects.create(
        tenant_id=tenant_id,
        code="PURCHASE",
        name="شراء",
        is_inbound=True,
        is_outbound=False,
        requires_reference=True,
        is_active=True,
        color="#28a745",
        icon="shopping-cart"
    )
    
    sale = MovementType.objects.create(
        tenant_id=tenant_id,
        code="SALE",
        name="بيع",
        is_inbound=False,
        is_outbound=True,
        requires_reference=True,
        is_active=True,
        color="#dc3545",
        icon="tag"
    )
    
    # Create Vehicle Makes and Models
    print("Creating Vehicle Makes and Models...")
    hyundai = VehicleMake.objects.create(
        tenant_id=tenant_id,
        name="هيونداي",
        country_of_origin="كوريا الجنوبية",
        is_active=True
    )
    
    elantra = VehicleModel.objects.create(
        tenant_id=tenant_id,
        make=hyundai,
        name="إلنترا",
        is_active=True
    )
    
    # Create Franchise and Company first
    print("Creating Franchise and Company...")
    franchise = Franchise.objects.create(
        name="أفترسيلز",
        code="AFTERSALES",
        notes="شركة أفترسيلز لخدمات السيارات",
        is_active=True
    )
    
    company = Company.objects.create(
        franchise=franchise,
        name="أفترسيلز مصر",
        code="AFTERSALES-EG",
        notes="فرع مصر لشركة أفترسيلز",
        is_active=True
    )
    
    # Create ServiceCenterType
    print("Creating Service Center Types...")
    center_type = ServiceCenterType.objects.create(
        name="مركز خدمة سيارات",
        description="مركز خدمة وصيانة للسيارات",
        max_capacity=100,
        is_active=True
    )
    
    # Create Service Centers
    print("Creating Service Centers...")
    cairo_center = ServiceCenter.objects.create(
        tenant_id=tenant_id,
        name="مركز خدمة القاهرة",
        code="CAIRO-SC",
        center_type=center_type,
        company=company,
        address="123 شارع التحرير، القاهرة",
        is_active=True,
        serves_all_vehicle_makes=True
    )
    
    # Create Customers (with Egyptian names)
    print("Creating Customers...")
    customer1 = Customer.objects.create(
        tenant_id=tenant_id,
        first_name="بكر",
        last_name="نصر",
        phone="01012345678",
        email="<EMAIL>",
        address="45 طريق النصر، القاهرة",
        is_active=True
    )
    
    # Create Vehicles from the screenshot
    print("Creating Vehicles...")
    for i in range(1, 5):
        vin = "asd46578" if i < 3 else "asda87874789789asd" if i == 3 else "ش54ج6س4ش4ج65ي64"
            
        Vehicle.objects.create(
            tenant_id=tenant_id,
            make="هيونداي",
            model="إلنترا",
            year=2007,
            license_plate="شبكة1321",
            vin=vin,
            owner=customer1,
            service_center=cairo_center
        )
    
    # Create Inventory Items
    print("Creating Inventory Items...")
    # Engine Oil
    engine_oil = Item.objects.create(
        tenant_id=tenant_id,
        sku="OIL-5W30-1L",
        name="زيت محرك 5W30",
        description="زيت محرك عالي الجودة 5W30",
        quantity=50,
        unit_of_measurement=liters,
        unit_price=Decimal('25.99'),
        min_stock_level=10,
        category="consumable",
        classification=fluids
    )
    
    # Oil Filter
    oil_filter = Item.objects.create(
        tenant_id=tenant_id,
        sku="FLTR-OIL-HYU",
        name="فلتر زيت هيونداي",
        description="فلتر زيت متوافق مع سيارات هيونداي",
        quantity=30,
        unit_of_measurement=pieces,
        unit_price=Decimal('12.50'),
        min_stock_level=5,
        category="part",
        classification=engine_parts
    )
    
    # Brake Pads
    brake_pads = Item.objects.create(
        tenant_id=tenant_id,
        sku="BRK-PAD-ELAN",
        name="تيل فرامل أمامي لهيونداي إلنترا",
        description="تيل فرامل أمامي لهيونداي إلنترا 2006-2010",
        quantity=20,
        unit_of_measurement=pieces,
        unit_price=Decimal('45.00'),
        min_stock_level=4,
        category="part",
        classification=parts_classification
    )
    
    # Headlight Assembly
    headlight = Item.objects.create(
        tenant_id=tenant_id,
        sku="LIGHT-ELAN-FR",
        name="فانوس أمامي لهيونداي إلنترا",
        description="فانوس أمامي لهيونداي إلنترا 2006-2010",
        quantity=8,
        unit_of_measurement=pieces,
        unit_price=Decimal('89.99'),
        min_stock_level=2,
        category="part",
        classification=body_parts
    )
    
    # Create Vehicle Compatibilities
    print("Creating Vehicle Compatibilities...")
    for item in [oil_filter, brake_pads, headlight]:
        VehicleCompatibility.objects.create(
            tenant_id=tenant_id,
            item=item,
            make="هيونداي",
            model="إلنترا",
            year_from=2006,
            year_to=2010
        )
    
    # Create Movements
    print("Creating Movements...")
    # Purchase movements
    for item in [engine_oil, oil_filter, brake_pads, headlight]:
        Movement.objects.create(
            tenant_id=tenant_id,
            item=item,
            quantity=item.quantity,
            unit_of_measurement=item.unit_of_measurement,
            movement_type_ref=purchase,
            reference="INV-2023-001",
            notes="المخزون الأولي"
        )
    
    # Sale movements (using some items)
    Movement.objects.create(
        tenant_id=tenant_id,
        item=engine_oil,
        quantity=-2,
        unit_of_measurement=liters,
        movement_type_ref=sale,
        reference="SO-2023-001",
        notes="خدمة تغيير زيت لهيونداي إلنترا"
    )
    
    Movement.objects.create(
        tenant_id=tenant_id,
        item=oil_filter,
        quantity=-1,
        unit_of_measurement=pieces,
        movement_type_ref=sale,
        reference="SO-2023-001",
        notes="خدمة تغيير زيت لهيونداي إلنترا"
    )
    
    print("تم إنشاء بيانات العرض بنجاح!")

if __name__ == "__main__":
    create_demo_data() 