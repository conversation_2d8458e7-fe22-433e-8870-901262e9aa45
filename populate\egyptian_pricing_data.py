import os
import sys
import django
import uuid
from decimal import Decimal
from datetime import datetime, timedelta, date

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models after Django setup
from setup.models import VehicleMake, VehicleModel, Franchise, Company, ServiceCenter
from work_orders.models import WorkOrderType, MaintenanceSchedule, ScheduleOperation
from inventory.models import (
    Item, OperationPricing, PartPricing, OperationCompatibility
)

def create_tenant_id():
    """Create a consistent tenant ID for demo data"""
    return uuid.UUID('a385e9a3-0e6b-4e44-aa87-6a6510d6c2b7')

def create_egyptian_pricing_data():
    tenant_id = create_tenant_id()
    print(f"Creating Egyptian pricing data with tenant_id: {tenant_id}")
    
    # Get existing data
    print("Fetching existing data...")
    try:
        # Vehicle Makes/Models
        hyundai = VehicleMake.objects.get(tenant_id=tenant_id, name="هيونداي")
        elantra = VehicleModel.objects.get(tenant_id=tenant_id, name="إلنترا", make=hyundai)
        
        # Items
        engine_oil = Item.objects.get(tenant_id=tenant_id, sku="OIL-5W30-1L")
        oil_filter = Item.objects.get(tenant_id=tenant_id, sku="FLTR-OIL-HYU")
        brake_pads = Item.objects.get(tenant_id=tenant_id, sku="BRK-PAD-ELAN")
        headlight = Item.objects.get(tenant_id=tenant_id, sku="LIGHT-ELAN-FR")
        
        # WorkOrderTypes
        oil_change_type = WorkOrderType.objects.get(tenant_id=tenant_id, name="تغيير زيت")
        brake_service_type = WorkOrderType.objects.get(tenant_id=tenant_id, name="صيانة الفرامل")
        headlight_replace_type = WorkOrderType.objects.get(tenant_id=tenant_id, name="استبدال الفانوس")
        
        # Company/Franchise
        franchise = Franchise.objects.get(name="أفترسيلز")
        company = Company.objects.get(name="أفترسيلز مصر")
        cairo_center = ServiceCenter.objects.get(tenant_id=tenant_id, name="مركز خدمة القاهرة")
        
    except (VehicleMake.DoesNotExist, VehicleModel.DoesNotExist, 
            Item.DoesNotExist, WorkOrderType.DoesNotExist,
            Franchise.DoesNotExist, Company.DoesNotExist,
            ServiceCenter.DoesNotExist) as e:
        print(f"Error: Required data not found: {e}")
        print("Please run demo_data.py and create_operation_compatibilities.py first.")
        return
    
    # 1. Create additional Maintenance Schedules
    print("Creating additional maintenance schedules...")
    
    # 5,000 km Service
    minor_service = MaintenanceSchedule.objects.get_or_create(
        tenant_id=tenant_id,
        name="خدمة 5,000 كم - هيونداي إلنترا",
        defaults={
            'description': "خدمة صيانة بسيطة كل 5,000 كم",
            'interval_type': 'mileage',
            'mileage_interval': 5000,
            'vehicle_make': "هيونداي",
            'vehicle_model': "إلنترا",
            'is_active': True
        }
    )[0]
    
    # Add operations to minor service
    ScheduleOperation.objects.get_or_create(
        tenant_id=tenant_id,
        maintenance_schedule=minor_service,
        name="فحص وتغيير زيت المحرك",
        defaults={
            'description': "تغيير زيت المحرك والفلتر",
            'duration_minutes': 30,
            'sequence': 1,
            'is_required': True,
            'operation_type': oil_change_type
        }
    )
    
    # 30,000 km Service
    major_service = MaintenanceSchedule.objects.get_or_create(
        tenant_id=tenant_id,
        name="خدمة 30,000 كم - هيونداي إلنترا",
        defaults={
            'description': "خدمة صيانة رئيسية كل 30,000 كم",
            'interval_type': 'mileage',
            'mileage_interval': 30000,
            'vehicle_make': "هيونداي",
            'vehicle_model': "إلنترا",
            'is_active': True
        }
    )[0]
    
    # Add operations to major service
    ScheduleOperation.objects.get_or_create(
        tenant_id=tenant_id,
        maintenance_schedule=major_service,
        name="فحص وتغيير زيت المحرك",
        defaults={
            'description': "تغيير زيت المحرك والفلتر",
            'duration_minutes': 30,
            'sequence': 1,
            'is_required': True,
            'operation_type': oil_change_type
        }
    )
    
    ScheduleOperation.objects.get_or_create(
        tenant_id=tenant_id,
        maintenance_schedule=major_service,
        name="فحص وتغيير تيل الفرامل",
        defaults={
            'description': "فحص وتغيير تيل الفرامل الأمامي إذا لزم الأمر",
            'duration_minutes': 45,
            'sequence': 2,
            'is_required': True,
            'operation_type': brake_service_type
        }
    )
    
    # Annual Service
    annual_service = MaintenanceSchedule.objects.get_or_create(
        tenant_id=tenant_id,
        name="الخدمة السنوية - هيونداي إلنترا",
        defaults={
            'description': "خدمة الصيانة السنوية لسيارات هيونداي إلنترا",
            'interval_type': 'time',
            'time_interval_months': 12,
            'vehicle_make': "هيونداي",
            'vehicle_model': "إلنترا",
            'is_active': True
        }
    )[0]
    
    # Add operations to annual service
    ScheduleOperation.objects.get_or_create(
        tenant_id=tenant_id,
        maintenance_schedule=annual_service,
        name="فحص شامل للسيارة",
        defaults={
            'description': "فحص شامل لجميع أنظمة السيارة",
            'duration_minutes': 90,
            'sequence': 1,
            'is_required': True,
        }
    )
    
    ScheduleOperation.objects.get_or_create(
        tenant_id=tenant_id,
        maintenance_schedule=annual_service,
        name="فحص وتغيير زيت المحرك",
        defaults={
            'description': "تغيير زيت المحرك والفلتر",
            'duration_minutes': 30,
            'sequence': 2,
            'is_required': True,
            'operation_type': oil_change_type
        }
    )
    
    ScheduleOperation.objects.get_or_create(
        tenant_id=tenant_id,
        maintenance_schedule=annual_service,
        name="فحص وتغيير تيل الفرامل",
        defaults={
            'description': "فحص وتغيير تيل الفرامل الأمامي إذا لزم الأمر",
            'duration_minutes': 45,
            'sequence': 3,
            'is_required': True,
            'operation_type': brake_service_type
        }
    )
    
    # 2. Create Operation Pricings
    print("Creating operation pricings...")
    
    # Oil Change Operation Pricing
    OperationPricing.objects.get_or_create(
        tenant_id=tenant_id,
        operation_type=oil_change_type,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        service_center=cairo_center,
        defaults={
            'base_price': Decimal('150.00'),
            'labor_hours': Decimal('0.5'),
            'labor_rate': Decimal('100.00'),
            'is_active': True,
            'notes': "سعر خدمة تغيير الزيت لسيارات هيونداي إلنترا في القاهرة"
        }
    )
    
    # Brake Service Operation Pricing
    OperationPricing.objects.get_or_create(
        tenant_id=tenant_id,
        operation_type=brake_service_type,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        service_center=cairo_center,
        defaults={
            'base_price': Decimal('200.00'),
            'labor_hours': Decimal('1.0'),
            'labor_rate': Decimal('100.00'),
            'is_active': True,
            'notes': "سعر خدمة الفرامل لسيارات هيونداي إلنترا في القاهرة"
        }
    )
    
    # Headlight Replacement Operation Pricing
    OperationPricing.objects.get_or_create(
        tenant_id=tenant_id,
        operation_type=headlight_replace_type,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        service_center=cairo_center,
        defaults={
            'base_price': Decimal('100.00'),
            'labor_hours': Decimal('0.75'),
            'labor_rate': Decimal('100.00'),
            'is_active': True,
            'notes': "سعر خدمة تغيير الفانوس لسيارات هيونداي إلنترا في القاهرة"
        }
    )
    
    # Company-wide prices
    OperationPricing.objects.get_or_create(
        tenant_id=tenant_id,
        operation_type=oil_change_type,
        vehicle_make=hyundai,
        vehicle_model=None,
        company=company,
        defaults={
            'base_price': Decimal('170.00'),
            'labor_hours': Decimal('0.5'),
            'labor_rate': Decimal('110.00'),
            'is_active': True,
            'notes': "سعر خدمة تغيير الزيت لجميع سيارات هيونداي في مصر"
        }
    )
    
    # 3. Create Part Pricings
    print("Creating part pricings...")
    
    # Engine Oil Pricing
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=engine_oil,
        service_center=cairo_center,
        defaults={
            'price': Decimal('75.99'),
            'is_active': True,
            'notes': "سعر زيت المحرك في مركز خدمة القاهرة",
            'valid_from': date.today(),
            'valid_to': date.today() + timedelta(days=365)
        }
    )
    
    # Oil Filter Pricing
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=oil_filter,
        service_center=cairo_center,
        defaults={
            'price': Decimal('35.50'),
            'is_active': True,
            'notes': "سعر فلتر الزيت في مركز خدمة القاهرة",
            'valid_from': date.today(),
            'valid_to': date.today() + timedelta(days=365)
        }
    )
    
    # Brake Pads Pricing
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=brake_pads,
        service_center=cairo_center,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        defaults={
            'price': Decimal('280.00'),
            'is_active': True,
            'notes': "سعر تيل الفرامل الأمامي لهيونداي إلنترا",
            'valid_from': date.today(),
            'valid_to': date.today() + timedelta(days=365)
        }
    )
    
    # Headlight Pricing
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=headlight,
        service_center=cairo_center,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        defaults={
            'price': Decimal('450.00'),
            'is_active': True,
            'notes': "سعر الفانوس الأمامي لهيونداي إلنترا",
            'valid_from': date.today(),
            'valid_to': date.today() + timedelta(days=365)
        }
    )
    
    # Special offers
    PartPricing.objects.get_or_create(
        tenant_id=tenant_id,
        item=brake_pads,
        service_center=cairo_center,
        vehicle_make=hyundai,
        vehicle_model=elantra,
        operation_type=brake_service_type,
        defaults={
            'price': Decimal('240.00'),
            'is_active': True,
            'notes': "عرض خاص على تيل الفرامل عند إجراء خدمة الفرامل الكاملة",
            'is_special_pricing': True,
            'valid_from': date.today(),
            'valid_to': date.today() + timedelta(days=30)
        }
    )
    
    print("تم إنشاء بيانات التسعير المصرية بنجاح!")

if __name__ == "__main__":
    create_egyptian_pricing_data() 