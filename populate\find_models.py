import os
import sys
import django
import importlib

# Set up Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# List of model classes to find
models_to_find = [
    'Franchise',
    'Company', 
    'ServiceCenter',
    'Vehicle',
    'VehicleBrand',
    'VehicleModel',
    'ServiceHistory',
    'VehicleHistory',
    'UserActivity',
    'StockTransaction',
    'Warehouse',
    'SparePart'
]

# List of app directories to search in
app_dirs = [
    'core',
    'setup',
    'inventory',
    'warehouse',
    'website',
    'billing',
    'work_orders',
    'user_roles',
    'sales',
    'purchases',
    'franchise_setup',
]

def find_models():
    """Find each model in the Django project and print its import path"""
    print("Searching for models in the project...")
    print("-" * 60)
    
    # Dictionary to store results (model_name -> import_path)
    model_paths = {}
    
    # Examine each app directory
    for app_dir in app_dirs:
        # Skip if directory doesn't exist
        if not os.path.isdir(app_dir):
            continue
            
        # Check if models.py exists
        models_path = os.path.join(app_dir, 'models.py')
        models_dir = os.path.join(app_dir, 'models')
        
        # Check models.py
        if os.path.isfile(models_path):
            try:
                # Try to import the models module
                module_name = f"{app_dir}.models"
                module = importlib.import_module(module_name)
                
                # Check for each model
                for model_name in models_to_find:
                    if hasattr(module, model_name):
                        model_paths[model_name] = module_name
                        print(f"Found {model_name} in {module_name}")
            except Exception as e:
                print(f"Error importing {app_dir}.models: {e}")
        
        # Check models directory
        elif os.path.isdir(models_dir):
            # Look through .py files in models directory
            for file in os.listdir(models_dir):
                if file.endswith('.py') and file != '__init__.py':
                    model_module = file[:-3]  # Remove .py extension
                    try:
                        # Try to import the model module
                        module_name = f"{app_dir}.models.{model_module}"
                        module = importlib.import_module(module_name)
                        
                        # Check for each model
                        for model_name in models_to_find:
                            if hasattr(module, model_name):
                                model_paths[model_name] = module_name
                                print(f"Found {model_name} in {module_name}")
                    except Exception as e:
                        print(f"Error importing {module_name}: {e}")
    
    # Check for models we didn't find
    not_found = set(models_to_find) - set(model_paths.keys())
    if not_found:
        print("\nModels not found:")
        for model in not_found:
            print(f"- {model}")
    
    # Generate import statements
    print("\nModel import statements:")
    for model_name, module_path in model_paths.items():
        print(f"from {module_path} import {model_name}")

if __name__ == "__main__":
    find_models() 