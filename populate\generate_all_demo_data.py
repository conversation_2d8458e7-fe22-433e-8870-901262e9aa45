""" Master script to run all demo data generation for the Aftersails system"""

import os
import sys
import importlib
import traceback
import django

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

def run_generator(generator_name, generator_path):
    """Run a specific generator module"""
    print(f"\n\n{'='*40}")
    print(f"Running {generator_name}...")
    print(f"{'='*40}\n")
    
    try:
        # Import the module
        module = importlib.import_module(generator_path)
        
        # Look for main() function
        if hasattr(module, 'main'):
            module.main()
        else:
            print(f"No main() function found in {generator_path}")
            
        return True
    except Exception as e:
        print(f"Error running {generator_name}: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run all demo data generators in sequence"""
    print("\n🚀 Starting Aftersails Demo Data Generation 🚀\n")
    
    generators = [
        ("Demo Data Generator", "generate_demo_data"),
        ("Vehicle Operation Compatibility Generator", "add_vehicle_operation_compatibilities")
        # Add other generators if needed:
        # ("Additional Generator", "path.to.generator"),
    ]
    
    successes = 0
    failures = 0
    
    for name, path in generators:
        if run_generator(name, path):
            successes += 1
        else:
            failures += 1
    
    print("\n\n🏁 Demo Data Generation Summary 🏁")
    print(f"Successful generators: {successes}")
    print(f"Failed generators: {failures}")
    print("\nDemo data generation process complete!")

if __name__ == "__main__":
    main() 