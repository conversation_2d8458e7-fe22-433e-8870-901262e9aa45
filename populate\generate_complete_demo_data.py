#!/usr/bin/env python
"""
Generate complete demo data for the Aftersails Vehicle Service Management System
Tailored for the Egyptian market.
"""
import os
import sys
import django
import uuid
from django.db import transaction

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from generate_setup_data import SetupDataGenerator
from generate_operation_compatibilities import InventoryDataGenerator
from generate_billing_data import BillingDataGenerator
from generate_warehouse_data import WarehouseDataGenerator
from generate_work_orders_data import WorkOrdersDataGenerator
from generate_user_roles_data import UserRolesDataGenerator

def main():
    """Run the complete data generation process"""
    tenant_id = str(uuid.uuid4())
    print(f"Using tenant ID: {tenant_id}")
    
    print("\n===== Step 1: Generating Basic Setup Data =====")
    setup_generator = SetupDataGenerator()
    setup_generator.run()
    
    print("\n===== Step 2: Generating Inventory Data =====")
    inventory_generator = InventoryDataGenerator()
    inventory_generator.run()
    
    print("\n===== Step 3: Generating Warehouse Data =====")
    warehouse_generator = WarehouseDataGenerator()
    warehouse_generator.run()
    
    print("\n===== Step 4: Generating Work Orders Data =====")
    work_orders_generator = WorkOrdersDataGenerator()
    work_orders_generator.run()
    
    print("\n===== Step 5: Generating Billing Data =====")
    billing_generator = BillingDataGenerator()
    billing_generator.run()
    
    print("\n===== Step 6: Generating User Roles Data =====")
    user_roles_generator = UserRolesDataGenerator()
    # Auto-clean data for complete generation
    user_roles_generator.generate_user_roles_data(clean_existing=True)
    
    print("\n===== Demo Data Generation Complete =====")
    print("You now have sample data for:")
    print("- Organizations (Franchises, Companies, Service Centers)")
    print("- Customers and Vehicles with Egyptian specifics")
    print("- Inventory items and classifications with Arabic names")
    print("- Operation Compatibilities and Work Order Types")
    print("- Warehouse locations, bin locations, and inventory distributions")
    print("- Transfer orders between locations")
    print("- Bills of Materials and Maintenance Schedules")
    print("- Work Orders with operations and materials")
    print("- Billing data (Insurance, Warranties, Discounts, Payment Methods)")
    print("- Customer Preferences")
    print("- User roles and permissions with organizational hierarchy")
    
    print("\nNote: Could not generate Invoices because they require Work Orders. To complete the system,")
    print("you would need to create Work Orders first using the admin interface or API.")

if __name__ == "__main__":
    main() 