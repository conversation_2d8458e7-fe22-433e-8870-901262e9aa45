import os
import sys
import django
import random
from django.db import transaction
from faker import Faker
from tqdm import tqdm

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from django.contrib.auth.models import User, Group, Permission
from user_roles.models import Role, UserRole, ModulePermission
from setup.models import ServiceCenter, Franchise, Company

# Initialize Faker
fake = Faker()

class UserRolesDataGenerator:
    """Generate comprehensive user roles data for testing."""
    
    def __init__(self):
        # Get tenant IDs
        self.tenants = self._get_tenant_ids()
        if not self.tenants:
            print("❌ No tenants found. Please run populate_organization_tables.py first.")
            sys.exit(1)
            
        # Get service centers, franchises, and companies by tenant
        self.service_centers = {}
        self.franchises = {}
        self.companies = {}
        
        for tenant_id in self.tenants:
            self.service_centers[tenant_id] = list(ServiceCenter.objects.filter(tenant_id=tenant_id))
            self.franchises[tenant_id] = list(Franchise.objects.filter(tenant_id=tenant_id))
            self.companies[tenant_id] = list(Company.objects.filter(tenant_id=tenant_id))
            
        # Get existing users or create new ones if needed
        self.users = list(User.objects.all())
        if len(self.users) < 20:
            self._create_test_users(20 - len(self.users))
            self.users = list(User.objects.all())
            
        # Ensure roles exist
        self.roles = list(Role.objects.all())
        if not self.roles:
            self._create_default_roles()
            self.roles = list(Role.objects.all())
            
        # Create a roles by code lookup
        self.roles_by_code = {role.code: role for role in self.roles}
        
        # Print initialization summary
        print(f"Initialized UserRolesDataGenerator with {len(self.tenants)} tenants")
        print(f"Found {len(self.users)} users")
        print(f"Found {len(self.roles)} roles")
        for tenant_id in self.tenants:
            print(f"Tenant {tenant_id}: {len(self.service_centers.get(tenant_id, []))} service centers, "
                  f"{len(self.franchises.get(tenant_id, []))} franchises, "
                  f"{len(self.companies.get(tenant_id, []))} companies")
    
    def _get_tenant_ids(self):
        """Get unique tenant IDs from existing records."""
        tenant_ids = ServiceCenter.objects.values_list('tenant_id', flat=True).distinct()
        return list(tenant_ids)
    
    def _create_test_users(self, count):
        """Create test users if needed."""
        print(f"Creating {count} additional users...")
        
        for i in range(count):
            first_name = fake.first_name()
            last_name = fake.last_name()
            username = f"user_{first_name.lower()}_{last_name.lower()}"
            
            # Ensure username is unique
            suffix = 1
            temp_username = username
            while User.objects.filter(username=temp_username).exists():
                temp_username = f"{username}{suffix}"
                suffix += 1
                
            username = temp_username
            
            # Create user
            User.objects.create_user(
                username=username,
                email=f"{username}@example.com",
                password="password123",
                first_name=first_name,
                last_name=last_name
            )
        
        print(f"Created {count} users, total users: {User.objects.count()}")
    
    def _create_default_roles(self):
        """Create default roles if they don't exist."""
        print("Creating default roles...")
        Role.create_default_roles()
        
        # Set up module permissions
        self._setup_module_permissions()
    
    def _setup_module_permissions(self):
        """Set up module permissions for all roles."""
        print("Setting up module permissions...")
        
        # Define modules and actions
        modules = [
            'setup', 'work_orders', 'inventory', 'warehouse',
            'sales', 'purchases', 'reports', 'settings'
        ]
        
        actions = ['view', 'add', 'change', 'delete', 'approve', 'report']
        
        # Create basic permissions for each role based on its access flags
        roles = Role.objects.all()
        for role in tqdm(roles, desc="Setting up role permissions"):
            # Map role access flags to modules
            access_modules = []
            if role.can_access_setup:
                access_modules.append('setup')
            if role.can_access_work_orders:
                access_modules.append('work_orders')
            if role.can_access_inventory:
                access_modules.append('inventory')
            if role.can_access_warehouse:
                access_modules.append('warehouse')
            if role.can_access_sales:
                access_modules.append('sales')
            if role.can_access_purchases:
                access_modules.append('purchases')
            if role.can_access_reports:
                access_modules.append('reports')
            if role.can_access_settings:
                access_modules.append('settings')
                
            # For each module the role can access, create appropriate permissions
            for module in access_modules:
                # Admin roles get all actions
                if role.role_type in ['system_admin', 'franchise_admin', 'company_admin']:
                    for action in actions:
                        ModulePermission.objects.get_or_create(
                            role=role,
                            module=module,
                            action=action
                        )
                # Manager roles get most actions but limited settings
                elif 'manager' in role.role_type:
                    for action in actions:
                        if module == 'settings' and action != 'view':
                            continue
                        ModulePermission.objects.get_or_create(
                            role=role,
                            module=module,
                            action=action
                        )
                # Other roles get view access plus appropriate actions
                else:
                    # Always create view permission
                    ModulePermission.objects.get_or_create(
                        role=role,
                        module=module,
                        action='view'
                    )
                    
                    # Add other appropriate permissions based on role type
                    if role.role_type in ['service_advisor', 'small_center_advisor', 'medium_center_advisor']:
                        for action in ['add', 'change', 'report']:
                            ModulePermission.objects.get_or_create(
                                role=role,
                                module=module,
                                action=action
                            )
                    elif role.role_type == 'technician':
                        if module == 'work_orders':
                            ModulePermission.objects.get_or_create(
                                role=role,
                                module=module,
                                action='change'
                            )

    def clean_existing_user_roles(self, force=False):
        """Clean existing user roles if requested."""
        if force:
            count = UserRole.objects.count()
            UserRole.objects.all().delete()
            print(f"Deleted {count} existing UserRole records")
            return True
            
        confirm = input("Do you want to delete all existing UserRole records? (yes/no): ")
        if confirm.lower() == 'yes':
            count = UserRole.objects.count()
            UserRole.objects.all().delete()
            print(f"Deleted {count} existing UserRole records")
            return True
        else:
            print("Keeping existing UserRole records")
            return False

    def assign_system_admin_roles(self):
        """Assign system admin roles to users."""
        print("Assigning system admin roles...")
        
        # Get or create system admin role
        system_admin_role = self.roles_by_code.get('system_admin')
        if not system_admin_role:
            print("⚠ System admin role not found, skipping")
            return []
            
        created_roles = []
        
        # Assign to first user for each tenant
        for tenant_id in self.tenants:
            # Check if a system admin already exists for this tenant
            existing = UserRole.objects.filter(
                tenant_id=tenant_id,
                role=system_admin_role
            ).exists()
            
            if not existing and self.users:
                admin_user = self.users[0]  # Use first user
                
                # Create system admin role
                user_role = UserRole.objects.create(
                    tenant_id=tenant_id,
                    user=admin_user,
                    role=system_admin_role,
                    is_primary=True,
                    is_active=True
                )
                created_roles.append(user_role)
                
        print(f"Created {len(created_roles)} system admin role assignments")
        return created_roles

    def assign_franchise_roles(self):
        """Assign franchise-level roles to users."""
        print("Assigning franchise roles...")
        
        # Get franchise admin role
        franchise_admin_role = self.roles_by_code.get('franchise_admin')
        if not franchise_admin_role:
            print("⚠ Franchise admin role not found, skipping")
            return []
            
        created_roles = []
        
        # For each tenant, assign franchise admins
        for tenant_id in self.tenants:
            franchises = self.franchises.get(tenant_id, [])
            
            if not franchises:
                continue
                
            # Assign franchise admin to each franchise
            for franchise in franchises:
                # Skip if franchise already has an admin
                existing = UserRole.objects.filter(
                    tenant_id=tenant_id,
                    franchise=franchise,
                    role=franchise_admin_role
                ).exists()
                
                if not existing and self.users:
                    # Choose a random user
                    admin_user = random.choice(self.users)
                    
                    # Create franchise admin role
                    user_role = UserRole.objects.create(
                        tenant_id=tenant_id,
                        user=admin_user,
                        role=franchise_admin_role,
                        franchise=franchise,
                        is_primary=True,
                        is_active=True
                    )
                    created_roles.append(user_role)
                    
        print(f"Created {len(created_roles)} franchise role assignments")
        return created_roles

    def assign_company_roles(self):
        """Assign company-level roles to users."""
        print("Assigning company roles...")
        
        # Get company admin role
        company_admin_role = self.roles_by_code.get('company_admin')
        regional_manager_role = self.roles_by_code.get('regional_manager')
        
        if not company_admin_role:
            print("⚠ Company admin role not found, skipping")
            return []
            
        created_roles = []
        
        # For each tenant, assign company admins
        for tenant_id in self.tenants:
            companies = self.companies.get(tenant_id, [])
            
            if not companies:
                continue
                
            # Assign company admin to each company
            for company in companies:
                # Skip if company already has an admin
                existing = UserRole.objects.filter(
                    tenant_id=tenant_id,
                    company=company,
                    role=company_admin_role
                ).exists()
                
                if not existing and self.users:
                    # Choose a random user
                    admin_user = random.choice(self.users)
                    
                    # Create company admin role
                    user_role = UserRole.objects.create(
                        tenant_id=tenant_id,
                        user=admin_user,
                        role=company_admin_role,
                        company=company,
                        is_primary=True,
                        is_active=True
                    )
                    created_roles.append(user_role)
                    
                # Also assign regional manager if role exists
                if regional_manager_role and self.users:
                    existing = UserRole.objects.filter(
                        tenant_id=tenant_id,
                        company=company,
                        role=regional_manager_role
                    ).exists()
                    
                    if not existing:
                        # Choose a different random user
                        manager_user = random.choice([u for u in self.users if u != admin_user]) if 'admin_user' in locals() else random.choice(self.users)
                        
                        # Create regional manager role
                        user_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=manager_user,
                            role=regional_manager_role,
                            company=company,
                            is_primary=True,
                            is_active=True
                        )
                        created_roles.append(user_role)
                    
        print(f"Created {len(created_roles)} company role assignments")
        return created_roles

    def assign_service_center_roles(self):
        """Assign service center roles based on center size."""
        print("Assigning service center roles...")
        created_roles = []
        
        # For each tenant, assign roles to service centers
        for tenant_id in self.tenants:
            service_centers = self.service_centers.get(tenant_id, [])
            
            if not service_centers:
                continue
                
            # Process each service center
            for service_center in tqdm(service_centers, desc=f"Processing service centers for tenant {tenant_id}"):
                # Determine center size
                size = getattr(service_center, 'size', None)
                if not size:
                    # Default to medium if size not specified
                    size = random.choice(['small', 'medium', 'large'])
                    
                # Get suggested roles for this center size
                suggested_roles = Role.get_suggested_roles_by_center_size(size)
                
                # Create a pool of available users
                available_users = list(self.users)
                random.shuffle(available_users)
                assigned_users = set()
                
                # Assign each role type
                for job_function, role_code in suggested_roles.items():
                    if role_code not in self.roles_by_code or not available_users:
                        continue
                        
                    # Skip if this role is already assigned at this center
                    existing = UserRole.objects.filter(
                        tenant_id=tenant_id,
                        service_center=service_center,
                        role=self.roles_by_code[role_code]
                    ).exists()
                    
                    if existing:
                        continue
                        
                    # Get a user not yet assigned to this center if possible
                    user = None
                    for u in available_users:
                        if u not in assigned_users:
                            user = u
                            assigned_users.add(u)
                            break
                            
                    # If all users are assigned, just pick one
                    if not user and available_users:
                        user = available_users[0]
                        
                    if user:
                        # Determine if this should be the user's primary role
                        is_primary = not UserRole.objects.filter(
                            user=user,
                            is_primary=True
                        ).exists()
                        
                        # Create the role assignment
                        user_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=user,
                            role=self.roles_by_code[role_code],
                            service_center=service_center,
                            is_primary=is_primary,
                            is_active=True
                        )
                        created_roles.append(user_role)
                
                # For medium and large centers, add multiple technicians
                if size in ['medium', 'large'] and 'technician' in self.roles_by_code:
                    num_techs = 5 if size == 'large' else 3
                    tech_count = 0
                    
                    # Try to assign technicians that don't have other roles at this center
                    for user in available_users:
                        if tech_count >= num_techs:
                            break
                            
                        # Check if user already has a role at this center
                        has_role = UserRole.objects.filter(
                            tenant_id=tenant_id,
                            service_center=service_center,
                            user=user
                        ).exists()
                        
                        if not has_role:
                            # Determine if this should be the user's primary role
                            is_primary = not UserRole.objects.filter(
                                user=user,
                                is_primary=True
                            ).exists()
                            
                            # Create technician role
                            user_role = UserRole.objects.create(
                                tenant_id=tenant_id,
                                user=user,
                                role=self.roles_by_code['technician'],
                                service_center=service_center,
                                is_primary=is_primary,
                                is_active=True
                            )
                            created_roles.append(user_role)
                            tech_count += 1
        
        print(f"Created {len(created_roles)} service center role assignments")
        return created_roles

    def create_multi_location_staff(self):
        """Create staff that work at multiple locations."""
        print("Creating multi-location staff...")
        created_roles = []
        
        # For each tenant with multiple service centers
        for tenant_id in self.tenants:
            service_centers = self.service_centers.get(tenant_id, [])
            
            if len(service_centers) < 2:
                continue
                
            # Create technicians that work at multiple centers
            multi_center_techs = min(3, len(self.users))
            for i in range(multi_center_techs):
                if i >= len(self.users):
                    break
                    
                user = self.users[i]
                
                # Select 2-3 random service centers
                num_centers = random.randint(2, min(3, len(service_centers)))
                centers = random.sample(service_centers, num_centers)
                
                # Assign user as technician to each center
                for j, center in enumerate(centers):
                    # Skip if user already has technician role at this center
                    existing = UserRole.objects.filter(
                        tenant_id=tenant_id,
                        service_center=center,
                        user=user,
                        role__code='technician'
                    ).exists()
                    
                    if existing:
                        continue
                        
                    # First center gets primary role if user has no primary roles
                    is_primary = False
                    if j == 0 and not UserRole.objects.filter(user=user, is_primary=True).exists():
                        is_primary = True
                        
                    # Create technician role
                    user_role = UserRole.objects.create(
                        tenant_id=tenant_id,
                        user=user,
                        role=self.roles_by_code['technician'],
                        service_center=center,
                        is_primary=is_primary,
                        is_active=True
                    )
                    created_roles.append(user_role)
            
            # Create service advisors that work at multiple centers
            if len(self.users) >= 6 and 'service_advisor' in self.roles_by_code:
                multi_center_advisors = min(2, len(self.users) - 3)
                
                for i in range(multi_center_advisors):
                    user_index = i + 3  # Start with user index 3 (after techs)
                    if user_index >= len(self.users):
                        break
                        
                    user = self.users[user_index]
                    
                    # Select 2 random service centers
                    num_centers = min(2, len(service_centers))
                    centers = random.sample(service_centers, num_centers)
                    
                    # Assign user as service advisor to each center
                    for j, center in enumerate(centers):
                        # Skip if user already has service advisor role at this center
                        existing = UserRole.objects.filter(
                            tenant_id=tenant_id,
                            service_center=center,
                            user=user,
                            role__code='service_advisor'
                        ).exists()
                        
                        if existing:
                            continue
                            
                        # First center gets primary role if user has no primary roles
                        is_primary = False
                        if j == 0 and not UserRole.objects.filter(user=user, is_primary=True).exists():
                            is_primary = True
                            
                        # Create service advisor role
                        user_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=user,
                            role=self.roles_by_code['service_advisor'],
                            service_center=center,
                            is_primary=is_primary,
                            is_active=True
                        )
                        created_roles.append(user_role)
        
        print(f"Created {len(created_roles)} multi-location staff role assignments")
        return created_roles

    def create_multi_role_staff(self):
        """Create staff with multiple roles at the same location."""
        print("Creating multi-role staff...")
        created_roles = []
        
        # Define role combinations that make sense together
        role_combinations = [
            ('parts_clerk', 'cashier'),
            ('service_advisor', 'cashier'),
            ('medium_center_advisor', 'medium_center_cashier'),
            ('medium_center_parts', 'medium_center_cashier'),
            ('receptionist', 'cashier'),
            ('parts_clerk', 'receptionist')
        ]
        
        # For each tenant
        for tenant_id in self.tenants:
            service_centers = self.service_centers.get(tenant_id, [])
            
            if not service_centers:
                continue
                
            # Process 2-3 users with multiple roles
            multi_role_count = min(3, len(self.users))
            for i in range(multi_role_count):
                if i >= len(self.users):
                    break
                    
                user = self.users[-(i+1)]  # Start from end of list
                
                # Select a random service center
                center = random.choice(service_centers)
                
                # Select a random role combination
                combo = random.choice(role_combinations)
                
                # Determine which role is primary (randomly)
                is_first_primary = random.choice([True, False])
                
                # Create the first role if it exists
                if combo[0] in self.roles_by_code:
                    # Skip if user already has this role at this center
                    existing = UserRole.objects.filter(
                        tenant_id=tenant_id,
                        service_center=center,
                        user=user,
                        role=self.roles_by_code[combo[0]]
                    ).exists()
                    
                    if not existing:
                        user_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=user,
                            role=self.roles_by_code[combo[0]],
                            service_center=center,
                            is_primary=is_first_primary,
                            is_active=True
                        )
                        created_roles.append(user_role)
                
                # Create the second role if it exists
                if combo[1] in self.roles_by_code:
                    # Skip if user already has this role at this center
                    existing = UserRole.objects.filter(
                        tenant_id=tenant_id,
                        service_center=center,
                        user=user,
                        role=self.roles_by_code[combo[1]]
                    ).exists()
                    
                    if not existing:
                        user_role = UserRole.objects.create(
                            tenant_id=tenant_id,
                            user=user,
                            role=self.roles_by_code[combo[1]],
                            service_center=center,
                            is_primary=not is_first_primary,
                            is_active=True
                        )
                        created_roles.append(user_role)
        
        print(f"Created {len(created_roles)} multi-role staff assignments")
        return created_roles

    def create_inactive_roles(self):
        """Create inactive roles for testing filters."""
        print("Creating inactive roles...")
        created_roles = []
        
        # Create about 5 inactive roles
        inactive_count = min(5, len(self.users))
        
        for i in range(inactive_count):
            if i >= len(self.users):
                break
                
            user = random.choice(self.users)
            
            for tenant_id in self.tenants:
                service_centers = self.service_centers.get(tenant_id, [])
                
                if not service_centers:
                    continue
                    
                # Select a random service center
                center = random.choice(service_centers)
                
                # Select a random role (prefer service_advisor)
                role_code = 'service_advisor'
                if role_code not in self.roles_by_code:
                    role_code = random.choice(list(self.roles_by_code.keys()))
                    
                # Skip if user already has this role at this center
                existing = UserRole.objects.filter(
                    tenant_id=tenant_id,
                    service_center=center,
                    user=user,
                    role=self.roles_by_code[role_code]
                ).exists()
                
                if not existing:
                    user_role = UserRole.objects.create(
                        tenant_id=tenant_id,
                        user=user,
                        role=self.roles_by_code[role_code],
                        service_center=center,
                        is_primary=False,
                        is_active=False  # Inactive
                    )
                    created_roles.append(user_role)
        
        print(f"Created {len(created_roles)} inactive role assignments")
        return created_roles

    @transaction.atomic
    def generate_user_roles_data(self, clean_existing=False):
        """Generate comprehensive user roles data."""
        print("Starting comprehensive user roles data generation...")
        
        # Check prerequisites
        if not ServiceCenter.objects.exists():
            print("❌ No service centers found. Please run add_service_center_data.py first.")
            return
            
        # Clean existing if requested
        if clean_existing:
            self.clean_existing_user_roles(force=True)
            
        # Generate data in stages
        self.assign_system_admin_roles()
        self.assign_franchise_roles()
        self.assign_company_roles()
        self.assign_service_center_roles()
        self.create_multi_location_staff()
        self.create_multi_role_staff()
        self.create_inactive_roles()
        
        # Print summary
        total_roles = UserRole.objects.count()
        active_roles = UserRole.objects.filter(is_active=True).count()
        inactive_roles = UserRole.objects.filter(is_active=False).count()
        primary_roles = UserRole.objects.filter(is_primary=True).count()
        
        print("\n✅ User Roles Data Generation Complete!")
        print(f"Total roles: {total_roles}")
        print(f"Active roles: {active_roles}")
        print(f"Inactive roles: {inactive_roles}")
        print(f"Primary roles: {primary_roles}")
        
        # Print average roles per user
        users_with_roles = User.objects.filter(user_roles__isnull=False).distinct().count()
        if users_with_roles > 0:
            avg_roles = total_roles / users_with_roles
            print(f"Average roles per user: {avg_roles:.2f}")


def main():
    """Execute the user roles data generator."""
    generator = UserRolesDataGenerator()
    
    # Check if running with command line argument
    if len(sys.argv) > 1 and sys.argv[1] == '--force-clean':
        # Force clean in non-interactive mode
        generator.generate_user_roles_data(clean_existing=True)
    else:
        # Ask if we should clean existing data in interactive mode
        clean = input("Do you want to clean existing user roles data? (yes/no): ").lower() == 'yes'
        
        # Generate the data
        generator.generate_user_roles_data(clean_existing=clean)


if __name__ == "__main__":
    main() 