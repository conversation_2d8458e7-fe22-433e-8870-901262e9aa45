---
description: >
  Scaffold or update the Dynamic Inventory SaaS (Django 5 + Tailwind 3 + Flowbite RTL + HTMX).
  Builds both backend and frontend with bilingual (en/ar) UI, feature-flag switches,
  and per-tenant modularity.  Agent-Requested so developers call it explicitly.
globs: []
alwaysApply: false        # invoked manually via @dyninv-scaffold
type: Agent Requested
---

# ⚙️  Backend tasks
- Ensure a Django 5 project named `dyninv` and the apps:
  `core, inventory, warehouse, sales, purchases, reports, settings, feature_flags, notifications`.
- Add **django-waffle**; wire `waffle.middleware.WaffleMiddleware`; migrate and create admin for flags.  
  Use `FLAG_ACTIVE_BY_DEFAULT = False`.  :contentReference[oaicite:1]{index=1}
- In `core/models/common.py` create `TimeStampedModel`, `UUIDPrimaryKeyModel`; all domain models inherit them.
- In `inventory/models.py` create `Item` with `attributes = models.JSONField(default=dict)` for dynamic fields.
- Generate a stock-movement ledger table (`Movement`) with reversible quantity math and signals that auto-update on save/delete.
- Implement a tenant-aware `BaseQuerySet` using `django-currentuser` or a custom middleware; all queries filter by tenant id.

# 🌐  Frontend tasks
- Add Tailwind CLI build; install `flowbite`, `flowbite-plugin`, and `tailwindcss-rtl` plugin.  
  Configure `tailwind.config.js` with both Flowbite and RTL plugins at top of array.  :contentReference[oaicite:2]{index=2}
- Create `static/src/input.css` importing Tailwind base/components/utilities plus Flowbite.
- Generate `templates/base.html` that:
  - Loads the compiled `app.css` and `flowbite.min.js`.
  - Sets `<html lang="{{ LANGUAGE_CODE }}" {{ LANGUAGE_CODE == 'ar' and 'dir=rtl' }}>`.
  - Provides a top nav with language-toggle (`/i18n/setlang/`).
- Use **HTMX** for dynamic fragments: modal drawers for item detail, inline edit forms, infinite-scroll tables.  :contentReference[oaicite:3]{index=3}
- Hover & transition defaults: `class="transition duration-200 ease-out hover:-translate-y-0.5 hover:shadow-lg"` on cards and buttons.

# 🏳️‍🌈  Internationalisation
- Add `LANGUAGES = [('en', 'English'), ('ar', 'العربية')]` in `settings.py`; generate `.po` files.
- Wrap all hard-coded strings with `gettext_lazy`.
- RTL: rely on logical CSS props in Tailwind v3.3+; no separate stylesheets needed.  :contentReference[oaicite:4]{index=4}

# 🔌  Extensibility & APIs
- Scaffold Django REST Framework but behind a waffle flag named `api`.
- Expose `/api/v1/items`, `/api/v1/movements` when `api` flag is on; return `403` otherwise.
- Add outbound webhooks (Django signals → Celery tasks) for `item.created`, `stock.low`.

# 🖼️  UI component standards
- All icons via Heroicons (bundled with Flowbite) for consistency.  :contentReference[oaicite:5]{index=5}
- Datatables use Flowbite’s responsive table/filters pattern with full RTL support.  :contentReference[oaicite:6]{index=6}
- Keep forms single-column on mobile, two-column ≥ `md`.

# 📚  Docs & tests
- Auto-create `README.md` with setup commands, `.env.example`, and feature-flag table.
- Place architecture diagram PNG as `/docs/architecture.png`.
- Generate pytest suite covering: item CRUD, ledger integrity, waffle flags, and API auth.

# 🚀  Automation shortcut
When the user says **“bootstrap”**, **“scaffold”**, or **“regenerate skeleton”** and `@dyninv-scaffold` is in context:
1. Ask if they want to overwrite existing files that differ.  
2. If *yes*, replace conflicting files; otherwise skip and continue.  
3. Run `python manage.py makemigrations && migrate` after writing models.

