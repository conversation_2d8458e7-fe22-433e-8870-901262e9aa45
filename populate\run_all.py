import os
import sys
import subprocess
import time

def run_script(script_name):
    """Run a Python script with proper error handling"""
    print(f"\n=== Running {script_name} ===")
    try:
        subprocess.run([sys.executable, script_name], check=True)
        print(f"✅ {script_name} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running {script_name}: {e}")
        return False

def main():
    # Get the directory of this script
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Move to the script directory
    os.chdir(current_dir)
    
    # List of scripts to run in order
    scripts = [
        "demo_data.py",
        "create_operation_compatibilities.py",
        "create_pricing.py",
    ]
    
    print("====================================================")
    print("  Starting Egyptian-themed Demo Data Population      ")
    print("====================================================")
    
    start_time = time.time()
    
    # Run each script in sequence
    success_count = 0
    for script in scripts:
        if run_script(script):
            success_count += 1
            # Small delay to prevent any race conditions
            time.sleep(1)
        else:
            print(f"\n⚠️ Stopping due to error in {script}")
            break
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n====================================================")
    print(f"  Demo Data Population Completed: {success_count}/{len(scripts)} scripts")
    print(f"  Time taken: {duration:.2f} seconds")
    print("====================================================")
    
    if success_count == len(scripts):
        print("\n✅ All demo data scripts executed successfully!")
        print("You can now check the admin interface for the populated data.")
    else:
        print(f"\n⚠️ {len(scripts) - success_count} scripts failed to execute properly.")
        print("Please check the error messages above and fix any issues.")

if __name__ == "__main__":
    main() 