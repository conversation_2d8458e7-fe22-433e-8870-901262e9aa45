# Aftersails Vehicle Service Management System

## System Architecture Overview

This document provides a comprehensive view of the Aftersails system architecture, application flows, and cross-module interactions. The system is designed for vehicle service centers with support for franchise operations, inventory management, billing, and comprehensive reporting.

## Core Applications

| App | Purpose | Key Components |
|-----|---------|----------------|
| **core** | Base system functionality | Authentication, Middleware, Base Models, Tenant Management |
| **admins** | Admin customizations | Admin Interface Extensions |
| **user_roles** | Role-based access control | Permissions, Role Definitions |
| **feature_flags** | Feature toggles | Module Enabling/Disabling |
| **website** | Public-facing frontend | Landing Pages, Public Information |
| **api** | REST API interface | API Endpoints, Serializers |
| **notifications** | System messaging | Email, SMS, In-App Notifications |

## Business Applications

| App | Purpose | Key Models |
|-----|---------|----------------|
| **setup** | System configuration | Customers, Vehicles, Service Centers |
| **franchise_setup** | Franchise management | Franchises, Territories |
| **work_orders** | Service operations | Work Orders, Operations, Materials |
| **inventory** | Stock tracking | Items, Stock Levels, Dynamic Pricing Rules |
| **warehouse** | Physical storage | Warehouses, Locations, Transfers, Hierarchy |
| **sales** | Customer sales | Orders, Quotations |
| **purchases** | Vendor purchasing | Purchase Orders, Receipts |
| **billing** | Financial operations | Invoices, Payments, Rule Engine, Customer Classification |
| **reports** | Analytics | Reports, Dashboards |
| **app_settings** | Configuration | System Settings |

---

## System Flows

### 1. Vehicle Service Flow

```
   Customer          Vehicle           Work Order          Billing
   Creation  -----→  Registration  ----→ Creation  --------→ Invoice Generation
     ↓                                      ↓                  ↓
   Customer          Part/Operation       Work                Payment
   Information       Selection           Execution            Processing
```

#### Detailed Process:
1. **Customer Intake (setup)**
   - Customer record creation with contact details
   - Preference setup for communication and billing
   - Customer categorization (regular, VIP, corporate)
   - **Dynamic classification based on criteria**

2. **Vehicle Registration (setup)**
   - Vehicle details recording (make, model, year, VIN)
   - Service history association
   - Warranty information tracking

3. **Work Order Creation (work_orders)**
   - Service requests documentation
   - Vehicle diagnostics recording
   - Task assignment to technicians
   - Material and operation planning
   - **External parts/operations procurement**

4. **Service Execution (work_orders)**
   - Recording labor hours
   - Part usage tracking
   - Quality control checks
   - Service status updates

5. **Billing Process (billing)**
   - Invoice generation from work order
   - Application of dynamic rules for discounts
   - Insurance/warranty claims processing
   - Payment receipt handling
   - **Customer classification-based pricing**

#### Cross-Module Dependencies:
- **setup → work_orders**: Customer and vehicle information
- **inventory → work_orders**: Parts availability
- **work_orders → billing**: Service details for invoicing
- **work_orders → notifications**: Status updates to customer
- **billing → reports**: Financial data collection
- **setup → billing**: Customer classification for pricing

---

### 2. Inventory Management Flow

```
   Purchase          Inventory          Warehouse          Parts
   Order     -----→  Receipt      ----→ Storage   --------→ Usage
     ↓                  ↓               ↓                   ↓
   Vendor          Stock Level       Location            Work Order
   Management       Update           Management          Association
```

#### Detailed Process:
1. **Purchase Planning (purchases)**
   - Inventory level monitoring
   - Purchase request creation
   - Vendor selection
   - Pricing negotiation
   - **Buy price recording**

2. **Order Management (purchases)**
   - Purchase order creation
   - Order tracking
   - Delivery scheduling
   - Vendor communication

3. **Inventory Receipt (inventory)**
   - Physical receipt of goods
   - Quality inspection
   - Quantity verification
   - Barcode/SKU assignment
   - **Multiple selling price assignment**

4. **Warehouse Management (warehouse)**
   - Storage location assignment
   - Bin management
   - Inventory organization
   - Stock transfers between locations
   - **Hierarchical warehouse structure**

5. **Inventory Consumption (inventory/work_orders)**
   - Part allocation to work orders
   - Automatic stock level adjustment
   - Threshold alerts
   - Inventory valuation
   - **External procurement for missing parts**

#### Cross-Module Dependencies:
- **inventory → purchases**: Reorder triggers
- **purchases → inventory**: Expected receipts
- **inventory → warehouse**: Storage needs
- **inventory → work_orders**: Parts availability
- **inventory → reports**: Stock valuations
- **service_center → warehouse**: Transfer requests

---

### 3. Financial Management Flow

```
   Work Order        Invoice          Payment          Financial
   Completion  -----→ Creation   ----→ Processing ----→ Reporting
      ↓               ↓               ↓                  ↓
   Service         Discount         Receipt            Profit
   Valuation        Rules          Generation         Analysis
```

#### Detailed Process:
1. **Service Valuation (work_orders)**
   - Labor cost calculation
   - Parts cost aggregation
   - Service package pricing
   - Additional charges calculation
   - **Dynamic pricing application**

2. **Invoice Generation (billing)**
   - Customer billing information retrieval
   - Tax calculation
   - Discount application via rule engine
   - Due date assignment

3. **Dynamic Rule Application (billing)**
   - Customer status evaluation (VIP, corporate)
   - Vehicle-specific promotions
   - Service package discounts
   - Insurance/warranty coverage determination
   - **Customer classification-based discounts**

4. **Payment Processing (billing)**
   - Multiple payment method support
   - Partial payment handling
   - Credit management
   - Receipt generation

5. **Financial Reporting (reports/billing)**
   - Revenue tracking
   - Payment collection monitoring
   - Aging analysis
   - Profit margin calculation
   - **Buy vs. sell price analysis**

#### Cross-Module Dependencies:
- **work_orders → billing**: Service details for invoicing
- **setup → billing**: Customer payment preferences
- **billing → notifications**: Invoice and payment notifications
- **billing → reports**: Financial metrics
- **inventory → billing**: Buy and sell prices

---

### 4. Franchise Management Flow

```
   Franchise         Service Center     Operations         Franchise
   Setup      -----→ Configuration ----→ Monitoring  ----→ Reporting
     ↓                  ↓                   ↓               ↓
   Territory        Staff              Performance       Revenue
   Definition       Management         Tracking         Sharing
```

#### Detailed Process:
1. **Franchise Establishment (franchise_setup)**
   - Franchise partner registration
   - Territory assignment
   - Service offering definition
   - Contract management
   - **Main warehouse assignment**

2. **Service Center Configuration (setup)**
   - Physical location setup
   - Equipment inventory
   - Operating hours
   - Service capability definition
   - **Secondary warehouse setup**

3. **Staff Management (user_roles/setup)**
   - Employee assignment
   - Role definition
   - Permission allocation
   - Training tracking

4. **Operations Monitoring (work_orders/sales)**
   - Service volume tracking
   - Quality assurance
   - Customer satisfaction monitoring
   - Compliance verification
   - **Inventory visibility across locations**

5. **Financial Settlement (billing/reports)**
   - Revenue calculation
   - Franchise fee processing
   - Profit sharing
   - Performance incentives
   - **Pricing policy compliance**

#### Cross-Module Dependencies:
- **franchise_setup → setup**: Service center configuration
- **franchise_setup → user_roles**: Permission templates
- **franchise_setup → reports**: Franchise-specific reporting
- **work_orders → franchise_setup**: Service tracking
- **billing → franchise_setup**: Revenue sharing
- **franchise_setup → warehouse**: Warehouse hierarchy

---

### 5. Customer Relationship Flow

```
   Customer          Vehicle           Service           Follow-up
   Onboarding  -----→ History     ----→ Reminders  ----→ Communication
      ↓               ↓                 ↓                 ↓
   Profile         Service            Scheduled        Satisfaction
   Management      Records            Maintenance       Surveys
```

#### Detailed Process:
1. **Customer Acquisition (website/setup)**
   - Lead capture
   - Initial customer registration
   - Communication preference setup
   - Customer categorization
   - **Classification criteria evaluation**

2. **Vehicle Portfolio Management (setup)**
   - Multiple vehicle association
   - Service history tracking
   - Warranty management
   - Vehicle-specific notifications
   - **Vehicle-specific pricing rules**

3. **Service Scheduling (work_orders/notifications)**
   - Maintenance reminder setup
   - Appointment booking
   - Service preparation
   - Resource allocation

4. **Customer Communication (notifications)**
   - Status updates
   - Invoice delivery
   - Reminder sending
   - Marketing communications
   - **Classification-based communication**

5. **Feedback Collection (setup/reports)**
   - Survey distribution
   - Rating collection
   - Feedback analysis
   - Service improvement
   - **Classification adjustment based on feedback**

#### Cross-Module Dependencies:
- **website → setup**: Customer registration
- **setup → notifications**: Communication preferences
- **work_orders → notifications**: Service updates
- **notifications → reports**: Communication effectiveness
- **billing → notifications**: Invoice and payment notices
- **setup → billing**: Customer classification updates

---

### 6. Reporting and Analytics Flow

```
   Operational       Financial         Customer          Executive
   Data       -----→ Data        ----→ Data       ----→ Dashboards
     ↓               ↓                 ↓                 ↓
   Service        Revenue           Satisfaction      Business
   Metrics        Analysis          Metrics         Intelligence
```

#### Detailed Process:
1. **Data Collection (All Modules → reports)**
   - Operational metrics gathering
   - Financial data aggregation
   - Customer interaction recording
   - System usage statistics
   - **Pricing effectiveness tracking**

2. **Report Generation (reports)**
   - Standard report production
   - Custom report building
   - Scheduled report distribution
   - Export functionality

3. **Performance Analysis (reports)**
   - KPI tracking
   - Trend identification
   - Anomaly detection
   - Comparative analysis
   - **Buy/sell price margin analysis**

4. **Business Intelligence (reports)**
   - Predictive analytics
   - Resource optimization
   - Strategic planning support
   - Market analysis
   - **Classification effectiveness**

5. **Executive Dashboards (reports)**
   - High-level metrics visualization
   - Decision support information
   - Alert highlighting
   - Goal tracking
   - **Pricing strategy effectiveness**

#### Cross-Module Dependencies:
- **All modules → reports**: Data provision
- **reports → notifications**: Report distribution
- **reports → feature_flags**: Performance-based feature enabling
- **reports → billing**: Pricing strategy feedback

---

### 7. Dynamic Billing Rules Flow

```
   Rule            Condition          Effect             Audit
   Definition -----→ Setup       ----→ Application  ----→ Logging
      ↓               ↓                 ↓                 ↓
   Priority        Evaluation        Discount          Performance
   Assignment      Logic            Calculation         Analysis
```

#### Detailed Process:
1. **Rule Definition (billing)**
   - Promotion rule creation
   - Rule categorization (discount, warranty, recall)
   - Priority assignment
   - Validity period setting
   - **Customer classification targeting**

2. **Condition Configuration (billing)**
   - Customer criteria definition
   - Vehicle criteria setting
   - Service/part specifications
   - Order value thresholds
   - **Classification-based conditions**

3. **Effect Specification (billing)**
   - Discount type selection (percentage, fixed)
   - Coverage determination
   - Application scope (item, order)
   - Maximum value constraints
   - **Special pricing models selection**

4. **Rule Evaluation (billing)**
   - Context building from invoice
   - Condition evaluation against context
   - Complex condition group processing
   - Priority-based rule selection
   - **Classification criteria matching**

5. **Rule Application (billing)**
   - Discount calculation
   - Invoice modification
   - Coverage assignment
   - Special pricing application
   - **Dynamic pricing model selection**

6. **Audit Trail (billing)**
   - Rule application logging
   - Modified values recording
   - Approval tracking
   - Exception documentation
   - **Pricing deviation monitoring**

#### Cross-Module Dependencies:
- **setup → billing**: Customer and vehicle data
- **work_orders → billing**: Service details
- **billing → reports**: Discount analytics
- **user_roles → billing**: Approval permissions
- **inventory → billing**: Pricing rules

---

### 8. Dynamic Customer Classification Flow

```
   Classification     Criteria          Automatic         Rule
   Creation     -----→ Definition  ----→ Evaluation  ----→ Association
      ↓                  ↓                 ↓                ↓
   Tier            Scoring            Customer          Pricing/Service
   Definition       Logic            Assignment         Application
```

#### Detailed Process:
1. **Classification Setup (setup/billing)**
   - Customer classification tiers creation
   - Tier benefits definition
   - Progression criteria setup
   - Demotion rules configuration
   - Default classification assignment

2. **Criteria Definition (setup/billing)**
   - Spending thresholds
   - Visit frequency metrics
   - Service type usage
   - Payment history criteria
   - Vehicle value/type criteria
   - Manual override rules

3. **Automatic Classification (billing/setup)**
   - Periodic customer evaluation (daily/weekly/monthly)
   - Score calculation based on criteria
   - Classification assignment/update
   - Change notification to customer
   - History of classification changes

4. **Business Rule Association (billing)**
   - Classification-specific discount rules
   - Service level assignments
   - Payment terms configuration
   - Credit limit determination
   - Special services access

5. **Classification Application (all modules)**
   - Pricing adjustments during invoicing
   - Service priority in scheduling
   - Notification frequency adjustments
   - Specific marketing campaigns
   - Parts availability prioritization

#### Cross-Module Dependencies:
- **setup → billing**: Customer data for classification
- **billing → setup**: Classification updates
- **billing → notifications**: Classification change notifications
- **classification → rule engine**: Dynamic rule application
- **work_orders → classification**: Service history for scoring

---

### 9. Multi-Level Warehouse Management Flow

```
   Main Warehouse       Franchise        Service Center       On-Demand
   Setup         -----→ Allocation  ----→ Access        ----→ Procurement
      ↓                   ↓                 ↓                    ↓
   Inventory        Distribution        Secondary           External
   Management         Control           Warehouse           Sourcing
```

#### Detailed Process:
1. **Warehouse Hierarchy Setup (warehouse/franchise_setup)**
   - Main warehouse configuration
   - Franchise warehouse assignment
   - Service center warehouse relationships
   - Visibility permissions setup
   - Transfer approval workflows

2. **Inventory Allocation (warehouse/inventory)**
   - Stock level allocation to franchises
   - Reserved stock management
   - Minimum stock thresholds by location
   - Automatic rebalancing rules
   - Critical parts prioritization

3. **Transfer Management (warehouse)**
   - Transfer request creation
   - Approval workflow processing
   - Shipping documentation
   - Receipt confirmation
   - Transfer tracking and history
   - Emergency expedite processes

4. **Secondary Warehouse Operations (warehouse/service_center)**
   - Local inventory management
   - Service center-specific stock levels
   - Fast-moving parts identification
   - Local reorder points
   - Usage pattern analysis

5. **External Procurement (work_orders/inventory)**
   - Non-stocked part identification
   - External vendor selection
   - Purchase approval workflow
   - Special pricing recording
   - Emergency procurement tracking
   - Catalog addition consideration

#### Cross-Module Dependencies:
- **franchise_setup → warehouse**: Warehouse hierarchy
- **inventory → warehouse**: Stock management
- **warehouse → work_orders**: Parts availability
- **work_orders → warehouse**: Transfer requests
- **warehouse → reports**: Inventory distribution analysis
- **warehouse → purchases**: Restock requirements

---

### 10. Dynamic Pricing Model Flow

```
   Base Price        Price Rule        Price            Application
   Definition  -----→ Creation    ----→ Calculation  ----→ Point
      ↓                ↓                 ↓                 ↓
   Cost            Condition         Multiple          Profit
   Recording       Definition        Prices           Analysis
```

#### Detailed Process:
1. **Base Price Management (inventory/purchases)**
   - Buy price recording from purchase orders
   - Standard markup configuration
   - Base selling price calculation
   - Cost change tracking
   - Price history maintenance

2. **Pricing Rule Creation (inventory/billing)**
   - Rule creation for specific conditions
   - Priority assignment
   - Validity period setting
   - Target scope definition (items, categories)
   - Approval workflow for special pricing

3. **Condition Configuration (inventory/billing)**
   - Customer type conditions (corporate, retail)
   - Service center specific adjustments
   - Vehicle make/model rules
   - Volume-based pricing tiers
   - Franchise-specific pricing
   - Special case handling (recalls, promotions)

4. **Price Calculation Engine (inventory/billing)**
   - Real-time price determination
   - Multiple price retrieval
   - Rule stacking and prioritization
   - Minimum margin enforcement
   - Special authorization for below-threshold pricing
   - Currency and tax handling

5. **Price Application (work_orders/sales/billing)**
   - Work order price application
   - Quote generation with correct pricing
   - Invoice creation with final pricing
   - Price override with approval
   - Price explanation for customer
   - Margin tracking per transaction

#### Cross-Module Dependencies:
- **purchases → inventory**: Buy price recording
- **inventory → work_orders**: Sell price selection
- **billing → inventory**: Customer pricing rules
- **setup → pricing**: Service center/franchise rules
- **pricing → reports**: Margin and effectiveness analysis
- **franchise_setup → pricing**: Franchise-specific rules

---

## Data Flow Diagram

```
                            ┌──────────────────┐
                            │                  │
                            │  Public Website  │
                            │                  │
                            └────────┬─────────┘
                                     │
                                     ▼
┌──────────────┐            ┌──────────────────┐            ┌──────────────┐
│              │            │                  │            │              │
│  Customers   │◄───────────┤    Setup App     │───────────►│   Vehicles   │
│Classification│            │                  │            │  Type-Based  │
└──────┬───────┘            └────────┬─────────┘            └──────┬───────┘
       │                             │                             │
       │                             ▼                             │
       │                    ┌──────────────────┐                   │
       │                    │                  │                   │
       └──────────────────►│   Work Orders    │◄──────────────────┘
                           │   Ext. Parts     │
                           └─┬──────────┬────┬┘
                             │          │    │
                             ▼          │    ▼
                    ┌──────────────┐    │    ┌──────────────┐
                    │              │    │    │              │
                    │  Inventory   │◄───┘───►│   Billing    │
                    │Multiple Prices│         │Dynamic Rules │
                    └──────┬───────┘         └──────┬───────┘
                           │                        │
                           ▼                        ▼
                    ┌──────────────┐         ┌──────────────┐
                    │ Warehouse    │         │              │
                    │ Hierarchy    │◄────────│   Payments   │
                    │              │         │              │
                    └──────┬───────┘         └──────┬───────┘
                           │                        │
                           │                        │
                           ▼                        ▼
                    ┌──────────────────────────────────────┐
                    │                                      │
                    │            Reports App               │
                    │         Pricing Analysis             │
                    └──────────────────────────────────────┘
```

## Key User Journeys

### 1. New Vehicle Service
- Customer brings vehicle for service → Service advisor creates work order → Technician performs service → Parts pulled from inventory (with customer-specific pricing) → Work order completed → Invoice generated with applicable discounts → Payment processed → Follow-up communication sent

### 2. Inventory Replenishment
- Stock level reaches reorder point → Purchase requisition created → Purchase order sent to vendor → Parts received → Buy price recorded → Multiple sell prices established → Inventory updated → Parts stored in warehouse locations → Available for work orders

### 3. Franchise Performance Review
- Franchise manager logs in → Views operational dashboard → Examines service metrics → Reviews financial performance → Analyzes customer satisfaction → Compares against targets → Identifies improvement areas

### 4. Special Promotion Creation
- Marketing decides on promotion → Admin creates rule in billing system → Sets conditions (vehicle type, service type, date range) → Defines discount effect → Activates rule → System automatically applies to qualifying invoices → Promotion effectiveness tracked in reports

### 5. Vehicle Recall Management
- Manufacturer issues recall → Recall registered in system → Affected vehicles identified → Owners notified → Special pricing rule created (100% coverage) → Vehicles serviced → Warranty claims processed → Recall completion reported

### 6. Customer Classification Change
- Customer spends above threshold → System evaluates classification criteria → Customer upgraded to VIP status → New pricing rules apply automatically → Customer notified → Special offers made available → Service advisor sees classification during next visit

### 7. Inter-Warehouse Transfer
- Service center identifies needed part → Checks main warehouse availability → Creates transfer request → Main warehouse staff receives notification → Approves transfer → Ships part with tracking → Service center receives part → Warehouse records updated

### 8. External Part Procurement
- Work order needs unavailable part → Service advisor marks as external procurement → Purchase requisition created → Vendor selected → Buy & sell prices recorded → Part ordered → Service scheduled → Part received → Work order completed with accurate pricing

### 9. Price Rule Creation
- Admin creates new price rule → Selects conditions (service center, customer type, vehicle models) → Sets pricing formula → Assigns priority → Approves and activates → Pricing engine applies rules at invoice creation → Margins maintained while offering competitive prices

## System Integration Points

### Internal Integrations

| From | To | Purpose |
|------|-----|---------|
| work_orders | inventory | Part consumption |
| work_orders | billing | Invoice generation |
| billing | work_orders | Warranty verification |
| setup | work_orders | Customer/vehicle lookup |
| inventory | purchases | Reorder suggestions |
| warehouse | inventory | Stock location tracking |
| work_orders | reports | Service analytics |
| billing | reports | Financial analytics |
| setup | billing | Customer classification |
| inventory | billing | Dynamic pricing information |
| franchise_setup | warehouse | Warehouse hierarchy |
| warehouse | service_center | Transfer management |
| classification | pricing | Price rule selection |

### External Integration Possibilities

| System Type | Integration Purpose |
|-------------|---------------------|
| Accounting Software | Financial data export |
| CRM Systems | Customer data synchronization |
| Manufacturer Portals | Warranty claims submission |
| Parts Catalogs | Part lookup and ordering |
| Payment Gateways | Payment processing |
| SMS Gateways | Customer notifications |
| Email Services | Customer communications |
| Calendar Systems | Appointment scheduling |
| Vendor Systems | Electronic ordering |
| Logistics Services | Transfer tracking |
| Market Pricing Services | Competitive pricing data |

---

## Conclusion

The Aftersails Vehicle Service Management System provides a comprehensive platform for managing all aspects of automotive service operations. Through tight integration between modules and a focus on business process automation, the system streamlines operations, enhances customer service, and provides valuable business insights through its reporting capabilities.

The dynamic rule engine in the billing module offers exceptional flexibility in pricing, promotions, and coverage determination, allowing business users to implement complex business rules without developer intervention. The addition of dynamic customer classification, multi-level warehouse management, and sophisticated pricing models creates a fully configurable system that can adapt to changing business needs without code modifications.

This system architecture supports single-location operations as well as multi-location franchise businesses with centralized management and reporting, while providing the flexibility needed for different business models and market conditions. 