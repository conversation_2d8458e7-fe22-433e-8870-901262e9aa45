"""
Script to update VehicleOperationCompatibility duration_minutes field from parent OperationCompatibility
This is useful after adding the duration_minutes field to the VehicleOperationCompatibility model
"""

import os
import django
import sys

# Set up Django
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from inventory.models import VehicleOperationCompatibility, OperationCompatibility
from django.db.models import Count
from django.db import transaction

def update_vehicle_operation_durations():
    """
    Update the duration_minutes field in VehicleOperationCompatibility records
    using values from their parent OperationCompatibility records
    """
    # First, count records to process
    total_records = VehicleOperationCompatibility.objects.filter(
        duration_minutes__isnull=True
    ).count()
    
    print(f"Found {total_records} VehicleOperationCompatibility records with empty duration_minutes")
    
    # Get all operation compatibilities with durations set
    operation_compatibilities = OperationCompatibility.objects.filter(
        duration_minutes__isnull=False,
        vehicle_compatibilities__isnull=False
    ).distinct()
    
    print(f"Found {operation_compatibilities.count()} OperationCompatibility records with durations")
    
    # Counter for records updated
    updated_count = 0
    
    # Process in a transaction for efficiency
    with transaction.atomic():
        for op_compat in operation_compatibilities:
            # Get all vehicle compatibilities for this operation that don't have durations yet
            vehicle_ops = VehicleOperationCompatibility.objects.filter(
                operation_compatibility=op_compat,
                duration_minutes__isnull=True
            )
            
            # Skip if no records to update
            if not vehicle_ops.exists():
                continue
                
            # Update all vehicle compatibilities with this operation's duration
            count = vehicle_ops.update(duration_minutes=op_compat.duration_minutes)
            updated_count += count
            
            print(f"Updated {count} vehicle operation compatibilities for operation: {op_compat}")
    
    print(f"Completed! Updated {updated_count} of {total_records} records.")
    
    # Report on remaining records
    remaining = VehicleOperationCompatibility.objects.filter(duration_minutes__isnull=True).count()
    if remaining > 0:
        print(f"There are still {remaining} records with empty duration_minutes")
    else:
        print("All records have duration_minutes values now")
    
if __name__ == '__main__':
    update_vehicle_operation_durations() 