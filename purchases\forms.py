from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.utils import timezone

from .models import Supplier, PurchaseOrder, PurchaseOrderItem, PurchaseReceipt, PurchaseReceiptItem
from inventory.models import Item


class SupplierForm(forms.ModelForm):
    """Form for creating and updating suppliers"""
    
    class Meta:
        model = Supplier
        fields = ['name', 'email', 'phone', 'address', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter supplier name')
            }),
            'email': forms.EmailInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter email address')
            }),
            'phone': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter phone number')
            }),
            'address': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter address')
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2'
            })
        }
        labels = {
            'name': _('Supplier Name'),
            'email': _('Email Address'),
            'phone': _('Phone Number'),
            'address': _('Address'),
            'is_active': _('Active')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # Check for duplicate supplier names within the same tenant
            queryset = Supplier.objects.filter(name__iexact=name, tenant_id=self.tenant_id)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise ValidationError(_('A supplier with this name already exists.'))
        return name


class PurchaseOrderForm(forms.ModelForm):
    """Form for creating and updating purchase orders"""
    
    class Meta:
        model = PurchaseOrder
        fields = ['order_number', 'supplier', 'order_date', 'expected_delivery_date', 'status', 'notes']
        widgets = {
            'order_number': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter PO number')
            }),
            'supplier': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'order_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'expected_delivery_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'status': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter notes')
            })
        }
        labels = {
            'order_number': _('PO Number'),
            'supplier': _('Supplier'),
            'order_date': _('Order Date'),
            'expected_delivery_date': _('Expected Delivery Date'),
            'status': _('Status'),
            'notes': _('Notes')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Set default order date to today
        if not self.instance.pk and not self.initial.get('order_date'):
            self.initial['order_date'] = timezone.now().date()
            
        # Filter suppliers by tenant
        if self.tenant_id:
            self.fields['supplier'].queryset = Supplier.objects.filter(
                tenant_id=self.tenant_id, 
                is_active=True
            )
        
        # Generate default PO number if creating new
        if not self.instance.pk and not self.initial.get('order_number'):
            self.initial['order_number'] = self.generate_po_number()
    
    def generate_po_number(self):
        """Generate a unique PO number"""
        import uuid
        timestamp = timezone.now().strftime('%Y%m%d')
        short_uuid = str(uuid.uuid4())[:8].upper()
        return f"PO-{timestamp}-{short_uuid}"
    
    def clean_order_number(self):
        order_number = self.cleaned_data.get('order_number')
        if order_number:
            # Check for duplicate PO numbers within the same tenant
            queryset = PurchaseOrder.objects.filter(order_number=order_number, tenant_id=self.tenant_id)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise ValidationError(_('A purchase order with this number already exists.'))
        return order_number
    
    def clean(self):
        cleaned_data = super().clean()
        order_date = cleaned_data.get('order_date')
        expected_delivery_date = cleaned_data.get('expected_delivery_date')
        
        if order_date and expected_delivery_date:
            if expected_delivery_date < order_date:
                raise ValidationError({
                    'expected_delivery_date': _('Expected delivery date cannot be before order date.')
                })
        
        return cleaned_data


class PurchaseOrderItemForm(forms.ModelForm):
    """Form for adding items to purchase orders"""
    
    class Meta:
        model = PurchaseOrderItem
        fields = ['item', 'quantity', 'unit_price']
        widgets = {
            'item': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01'
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01'
            })
        }
        labels = {
            'item': _('Item'),
            'quantity': _('Quantity'),
            'unit_price': _('Unit Price')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        self.purchase_order = kwargs.pop('purchase_order', None)
        super().__init__(*args, **kwargs)
        
        # Filter items by tenant
        if self.tenant_id:
            self.fields['item'].queryset = Item.objects.filter(tenant_id=self.tenant_id, is_active=True)
    
    def clean(self):
        cleaned_data = super().clean()
        item = cleaned_data.get('item')
        purchase_order = self.purchase_order or self.instance.purchase_order
        
        if item and purchase_order:
            # Check if item already exists in this purchase order
            existing_item = PurchaseOrderItem.objects.filter(
                purchase_order=purchase_order,
                item=item
            )
            if self.instance.pk:
                existing_item = existing_item.exclude(pk=self.instance.pk)
            
            if existing_item.exists():
                raise ValidationError({
                    'item': _('This item is already in the purchase order.')
                })
        
        return cleaned_data


class PurchaseReceiptForm(forms.ModelForm):
    """Form for creating purchase receipts"""
    
    class Meta:
        model = PurchaseReceipt
        fields = ['receipt_number', 'receipt_date', 'notes']
        widgets = {
            'receipt_number': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter receipt number')
            }),
            'receipt_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter notes')
            })
        }
        labels = {
            'receipt_number': _('Receipt Number'),
            'receipt_date': _('Receipt Date'),
            'notes': _('Notes')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        self.purchase_order = kwargs.pop('purchase_order', None)
        super().__init__(*args, **kwargs)
        
        # Set default receipt date to today
        if not self.instance.pk and not self.initial.get('receipt_date'):
            self.initial['receipt_date'] = timezone.now().date()
            
        # Generate default receipt number if creating new
        if not self.instance.pk and not self.initial.get('receipt_number'):
            self.initial['receipt_number'] = self.generate_receipt_number()
    
    def generate_receipt_number(self):
        """Generate a unique receipt number"""
        import uuid
        timestamp = timezone.now().strftime('%Y%m%d')
        short_uuid = str(uuid.uuid4())[:8].upper()
        return f"REC-{timestamp}-{short_uuid}"
    
    def clean_receipt_number(self):
        receipt_number = self.cleaned_data.get('receipt_number')
        if receipt_number:
            # Check for duplicate receipt numbers within the same tenant
            queryset = PurchaseReceipt.objects.filter(receipt_number=receipt_number, tenant_id=self.tenant_id)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise ValidationError(_('A receipt with this number already exists.'))
        return receipt_number


class PurchaseReceiptItemForm(forms.ModelForm):
    """Form for adding received items to receipts"""
    
    class Meta:
        model = PurchaseReceiptItem
        fields = ['purchase_order_item', 'quantity']
        widgets = {
            'purchase_order_item': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01'
            })
        }
        labels = {
            'purchase_order_item': _('Purchase Order Item'),
            'quantity': _('Quantity Received')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        self.purchase_order = kwargs.pop('purchase_order', None)
        super().__init__(*args, **kwargs)
        
        # Filter purchase order items by the specific purchase order
        if self.purchase_order:
            self.fields['purchase_order_item'].queryset = PurchaseOrderItem.objects.filter(
                purchase_order=self.purchase_order
            )
    
    def clean(self):
        cleaned_data = super().clean()
        purchase_order_item = cleaned_data.get('purchase_order_item')
        quantity = cleaned_data.get('quantity')
        
        if purchase_order_item and quantity:
            # Check that received quantity doesn't exceed ordered quantity
            if quantity > purchase_order_item.quantity:
                raise ValidationError({
                    'quantity': _('Received quantity cannot exceed ordered quantity ({}).').format(
                        purchase_order_item.quantity
                    )
                })
        
        return cleaned_data


class PurchaseReportForm(forms.Form):
    """Form for generating purchase reports"""
    
    REPORT_TYPE_CHOICES = [
        ('purchase_summary', _('Purchase Summary')),
        ('supplier_performance', _('Supplier Performance')),
        ('pending_orders', _('Pending Orders')),
        ('received_orders', _('Received Orders')),
    ]
    
    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Report Type')
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('From Date')
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('To Date')
    )
    
    supplier = forms.ModelChoiceField(
        queryset=Supplier.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Supplier'),
        empty_label=_('All Suppliers')
    )

    def __init__(self, *args, **kwargs):
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Set default date range (last 30 days)
        if not self.initial.get('date_to'):
            self.initial['date_to'] = timezone.now().date()
        if not self.initial.get('date_from'):
            self.initial['date_from'] = timezone.now().date() - timezone.timedelta(days=30)
        
        # Filter suppliers by tenant
        if tenant_id:
            self.fields['supplier'].queryset = Supplier.objects.filter(
                tenant_id=tenant_id, 
                is_active=True
            ).order_by('name')
    
    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_('From date cannot be after to date.'))
        
        return cleaned_data 