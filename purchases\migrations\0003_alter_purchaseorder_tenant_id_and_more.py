# Generated by Django 4.2.20 on 2025-06-15 14:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("purchases", "0002_alter_purchaseorder_tenant_id_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="purchaseorder",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorderitem",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="purchasereceipt",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="purchasereceiptitem",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="supplier",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
    ]
