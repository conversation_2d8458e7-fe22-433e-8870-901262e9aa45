from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from django.db.models import F, Sum
from .models import PurchaseOrder, PurchaseOrderItem, Supplier
from inventory.models import Movement, MovementType
from notifications.services import NotificationService
from user_roles.models import UserRole
import logging

logger = logging.getLogger(__name__)


@receiver(pre_save, sender=PurchaseOrder)
def purchase_order_pre_save(sender, instance, **kwargs):
    """Track original status for purchase orders"""
    if instance.pk:
        try:
            original = PurchaseOrder.objects.get(pk=instance.pk)
            instance._original_status = original.status
            instance._original_total = original.total_amount if hasattr(original, 'total_amount') else 0
        except PurchaseOrder.DoesNotExist:
            instance._original_status = None
            instance._original_total = 0
    else:
        instance._original_status = None
        instance._original_total = 0


@receiver(post_save, sender=PurchaseOrder)
def purchase_order_status_changed(sender, instance, created, **kwargs):
    """Handle purchase order status changes with comprehensive notifications"""
    try:
        if created:
            _notify_purchase_order_created(instance)
        elif hasattr(instance, '_original_status') and instance._original_status != instance.status:
            _notify_purchase_order_status_change(instance, instance._original_status, instance.status)
            
        # Check for high-value orders that need special approval
        if hasattr(instance, 'total_amount') and instance.total_amount > 5000:
            _notify_high_value_purchase_order(instance)
            
    except Exception as e:
        logger.error(f"Error in purchase_order_status_changed signal: {e}")


@receiver(post_save, sender=PurchaseOrderItem)
def purchase_order_item_added(sender, instance, created, **kwargs):
    """Handle purchase order item additions"""
    if created:
        try:
            _notify_purchase_item_added(instance)
            
            # Check for critical items that need expedited approval
            if hasattr(instance.item, 'is_critical') and instance.item.is_critical:
                _notify_critical_item_purchase(instance)
                
        except Exception as e:
            logger.error(f"Error in purchase_order_item_added signal: {e}")


@receiver(post_save, sender=Supplier)
def supplier_created_or_updated(sender, instance, created, **kwargs):
    """Handle supplier creation and updates"""
    try:
        if created:
            _notify_new_supplier_created(instance)
        else:
            # Check if supplier status changed
            if hasattr(instance, '_previous_active') and instance._previous_active != instance.is_active:
                _notify_supplier_status_change(instance)
                
    except Exception as e:
        logger.error(f"Error in supplier_created_or_updated signal: {e}")


@receiver(pre_save, sender=Supplier)
def supplier_pre_save(sender, instance, **kwargs):
    """Track previous supplier status"""
    if instance.pk:
        try:
            previous = sender.objects.get(pk=instance.pk)
            instance._previous_active = getattr(previous, 'is_active', True)
        except sender.DoesNotExist:
            instance._previous_active = True


# ==================== NOTIFICATION HELPER FUNCTIONS ====================

def _notify_purchase_order_created(purchase_order):
    """Notify about new purchase order creation with approval workflow"""
    try:
        # Determine approval level based on order value
        order_total = getattr(purchase_order, 'total_amount', 0)
        
        if order_total > 10000:
            # High-value orders need senior management approval
            approvers = _get_users_by_roles([
                'general_manager', 'service_center_manager', 'purchasing_director'
            ], purchase_order.tenant_id)
            priority = 'urgent'
        elif order_total > 2000:
            # Medium-value orders need manager approval
            approvers = _get_users_by_roles([
                'purchasing_manager', 'inventory_manager', 'service_center_manager'
            ], purchase_order.tenant_id)
            priority = 'high'
        else:
            # Standard orders need supervisor approval
            approvers = _get_users_by_roles([
                'purchasing_supervisor', 'parts_manager'
            ], purchase_order.tenant_id)
            priority = 'medium'
        
        for approver in approvers:
            # Create notification
            NotificationService.create_notification(
                recipient=approver,
                notification_type_code='purchase_order_created',
                title=str(_("New Purchase Order - {po_number}")).format(
                    po_number=getattr(purchase_order, 'order_number', purchase_order.id)
                ),
                message=str(_("Purchase order from {supplier} requires approval. Total: {total}")).format(
                    supplier=purchase_order.supplier.name,
                    total=f"${order_total:,.2f}" if order_total else "TBD"
                ),
                priority=priority,
                action_required=True,
                action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_order.pk}),
                action_text=str(_("Review Purchase Order")),
                related_object_type='purchase_order',
                related_object_id=str(purchase_order.id),
                tenant_id=purchase_order.tenant_id
            )
            
            # Create action item for approval
            NotificationService.create_action_item(
                assigned_to=approver,
                action_type='approve_purchase_order',
                title=str(_("Approve Purchase Order - {supplier}")).format(
                    supplier=purchase_order.supplier.name
                ),
                description=str(_("Review and approve purchase order from {supplier}. Total amount: {total}")).format(
                    supplier=purchase_order.supplier.name,
                    total=f"${order_total:,.2f}" if order_total else "TBD"
                ),
                priority=priority,
                action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_order.pk}),
                related_object_type='purchase_order',
                related_object_id=str(purchase_order.id),
                metadata={
                    'po_number': str(getattr(purchase_order, 'order_number', purchase_order.id)),
                    'supplier_name': purchase_order.supplier.name,
                    'total_amount': float(order_total) if order_total else 0,
                    'approval_level': 'high' if order_total > 10000 else 'medium' if order_total > 2000 else 'standard',
                    'actions': [
                        {'type': 'approve', 'label': str(_('Approve PO')), 'class': 'bg-green-600 text-white'},
                        {'type': 'deny', 'label': str(_('Reject PO')), 'class': 'bg-red-600 text-white'},
                        {'type': 'modify', 'label': str(_('Request Changes')), 'class': 'bg-blue-600 text-white'},
                        {'type': 'escalate', 'label': str(_('Escalate')), 'class': 'bg-purple-600 text-white'}
                    ]
                },
                tenant_id=purchase_order.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_purchase_order_created: {e}")


def _notify_purchase_order_status_change(purchase_order, old_status, new_status):
    """Notify about purchase order status changes"""
    try:
        if new_status == 'approved':
            # Notify purchasing staff to send PO to supplier
            purchasing_staff = _get_users_by_roles([
                'purchasing_clerk', 'purchasing_coordinator'
            ], purchase_order.tenant_id)
            
            for staff_member in purchasing_staff:
                NotificationService.create_action_item(
                    assigned_to=staff_member,
                    action_type='send_purchase_order',
                    title=str(_("Send Approved Purchase Order")).format(),
                    description=str(_("PO approved. Please send to supplier {supplier}")).format(
                        supplier=purchase_order.supplier.name
                    ),
                    priority='high',
                    action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_order.pk}),
                    related_object_type='purchase_order',
                    related_object_id=str(purchase_order.id),
                    metadata={
                        'supplier_name': purchase_order.supplier.name,
                        'supplier_email': getattr(purchase_order.supplier, 'email', ''),
                        'actions': [
                            {'type': 'send_email', 'label': str(_('Send via Email')), 'class': 'bg-blue-600 text-white'},
                            {'type': 'send_fax', 'label': str(_('Send via Fax')), 'class': 'bg-green-600 text-white'},
                            {'type': 'call_supplier', 'label': str(_('Call Supplier')), 'class': 'bg-yellow-600 text-white'},
                            {'type': 'mark_sent', 'label': str(_('Mark as Sent')), 'class': 'bg-gray-600 text-white'}
                        ]
                    },
                    tenant_id=purchase_order.tenant_id
                )
                
        elif new_status == 'received':
            # Notify warehouse to process incoming goods
            warehouse_staff = _get_users_by_roles([
                'warehouse_manager', 'receiving_clerk', 'parts_clerk'
            ], purchase_order.tenant_id)
            
            for staff_member in warehouse_staff:
                NotificationService.create_action_item(
                    assigned_to=staff_member,
                    action_type='process_received_goods',
                    title=str(_("Process Received Purchase Order")).format(),
                    description=str(_("Goods received from {supplier}. Please process and update inventory")).format(
                        supplier=purchase_order.supplier.name
                    ),
                    priority='high',
                    action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_order.pk}),
                    related_object_type='purchase_order',
                    related_object_id=str(purchase_order.id),
                    metadata={
                        'supplier_name': purchase_order.supplier.name,
                        'expected_items': _get_po_items_summary(purchase_order),
                        'actions': [
                            {'type': 'complete_receiving', 'label': str(_('Complete Receiving')), 'class': 'bg-green-600 text-white'},
                            {'type': 'partial_receiving', 'label': str(_('Partial Receiving')), 'class': 'bg-yellow-600 text-white'},
                            {'type': 'report_issue', 'label': str(_('Report Issue')), 'class': 'bg-red-600 text-white'},
                            {'type': 'need_inspection', 'label': str(_('Needs Inspection')), 'class': 'bg-blue-600 text-white'}
                        ]
                    },
                    tenant_id=purchase_order.tenant_id
                )
                
        elif new_status == 'rejected':
            # Notify requester and supplier contact if applicable
            if hasattr(purchase_order, 'requested_by') and purchase_order.requested_by:
                NotificationService.create_notification(
                    recipient=purchase_order.requested_by,
                    notification_type_code='purchase_order_rejected',
                    title=str(_("Purchase Order Rejected")).format(),
                    message=str(_("Your purchase order request has been rejected")).format(),
                    priority='medium',
                    action_required=False,
                    action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_order.pk}),
                    action_text=str(_("View Rejection Details")),
                    related_object_type='purchase_order',
                    related_object_id=str(purchase_order.id),
                    tenant_id=purchase_order.tenant_id
                )
                
        elif new_status == 'cancelled':
            # Notify all involved parties about cancellation
            involved_users = _get_users_by_roles([
                'purchasing_manager', 'inventory_manager', 'warehouse_manager'
            ], purchase_order.tenant_id)
            
            for user in involved_users:
                NotificationService.create_notification(
                    recipient=user,
                    notification_type_code='purchase_order_cancelled',
                    title=str(_("Purchase Order Cancelled")).format(),
                    message=str(_("Purchase order from {supplier} has been cancelled")).format(
                        supplier=purchase_order.supplier.name
                    ),
                    priority='medium',
                    action_required=False,
                    action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_order.pk}),
                    action_text=str(_("View Details")),
                    related_object_type='purchase_order',
                    related_object_id=str(purchase_order.id),
                    tenant_id=purchase_order.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_purchase_order_status_change: {e}")


def _notify_high_value_purchase_order(purchase_order):
    """Notify about high-value purchase orders requiring special approval"""
    try:
        order_total = getattr(purchase_order, 'total_amount', 0)
        
        if order_total > 5000:
            # Get senior management for high-value approval
            senior_managers = _get_users_by_roles([
                'general_manager', 'financial_controller', 'purchasing_director'
            ], purchase_order.tenant_id)
            
            for manager in senior_managers:
                NotificationService.create_action_item(
                    assigned_to=manager,
                    action_type='approve_high_value_purchase',
                    title=str(_("High-Value Purchase Approval Required")).format(),
                    description=str(_("Purchase order exceeds approval threshold. Amount: {amount}")).format(
                        amount=f"${order_total:,.2f}"
                    ),
                    priority='urgent',
                    action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_order.pk}),
                    related_object_type='purchase_order',
                    related_object_id=str(purchase_order.id),
                    metadata={
                        'order_total': float(order_total),
                        'supplier_name': purchase_order.supplier.name,
                        'requires_financial_review': order_total > 15000,
                        'actions': [
                            {'type': 'approve', 'label': str(_('Approve High-Value PO')), 'class': 'bg-green-600 text-white'},
                            {'type': 'deny', 'label': str(_('Reject')), 'class': 'bg-red-600 text-white'},
                            {'type': 'financial_review', 'label': str(_('Financial Review')), 'class': 'bg-purple-600 text-white'},
                            {'type': 'split_order', 'label': str(_('Split Order')), 'class': 'bg-blue-600 text-white'}
                        ]
                    },
                    tenant_id=purchase_order.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_high_value_purchase_order: {e}")


def _notify_purchase_item_added(purchase_item):
    """Notify about items added to purchase orders"""
    try:
        item = purchase_item.item
        item_total = purchase_item.quantity * purchase_item.unit_price if hasattr(purchase_item, 'unit_price') else 0
        
        # Notify for high-value individual items
        if item_total > 1000:
            managers = _get_users_by_roles([
                'purchasing_manager', 'inventory_manager'
            ], purchase_item.tenant_id)
            
            for manager in managers:
                NotificationService.create_notification(
                    recipient=manager,
                    notification_type_code='high_value_item_purchase',
                    title=str(_("High-Value Item Added to PO")).format(),
                    message=str(_("High-value item {item_name} added. Value: {value}")).format(
                        item_name=item.name,
                        value=f"${item_total:,.2f}"
                    ),
                    priority='medium',
                    action_required=False,
                    action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_item.purchase_order.pk}),
                    action_text=str(_("Review Item")),
                    related_object_type='purchase_order_item',
                    related_object_id=str(purchase_item.id),
                    tenant_id=purchase_item.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_purchase_item_added: {e}")


def _notify_critical_item_purchase(purchase_item):
    """Notify about critical item purchases that need expedited approval"""
    try:
        managers = _get_users_by_roles([
            'service_center_manager', 'parts_manager', 'inventory_manager'
        ], purchase_item.tenant_id)
        
        for manager in managers:
            NotificationService.create_action_item(
                assigned_to=manager,
                action_type='expedite_critical_purchase',
                title=str(_("Critical Item Purchase - Expedite")).format(),
                description=str(_("Critical item {item_name} needs expedited approval and processing")).format(
                    item_name=purchase_item.item.name
                ),
                priority='urgent',
                action_url=reverse('purchases:purchase_order_detail', kwargs={'pk': purchase_item.purchase_order.pk}),
                related_object_type='purchase_order_item',
                related_object_id=str(purchase_item.id),
                metadata={
                    'item_name': purchase_item.item.name,
                    'quantity': float(purchase_item.quantity),
                    'is_critical': True,
                    'actions': [
                        {'type': 'expedite', 'label': str(_('Expedite Processing')), 'class': 'bg-red-600 text-white'},
                        {'type': 'priority_supplier', 'label': str(_('Contact Priority Supplier')), 'class': 'bg-orange-600 text-white'},
                        {'type': 'check_alternative', 'label': str(_('Check Alternatives')), 'class': 'bg-blue-600 text-white'}
                    ]
                },
                tenant_id=purchase_item.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_critical_item_purchase: {e}")


def _notify_new_supplier_created(supplier):
    """Notify about new supplier creation requiring setup and approval"""
    try:
        managers = _get_users_by_roles([
            'purchasing_manager', 'vendor_relations_manager', 'general_manager'
        ], supplier.tenant_id)
        
        for manager in managers:
            NotificationService.create_action_item(
                assigned_to=manager,
                action_type='setup_new_supplier',
                title=str(_("New Supplier Setup Required")).format(),
                description=str(_("New supplier {supplier_name} created. Please complete setup and verification")).format(
                    supplier_name=supplier.name
                ),
                priority='medium',
                action_url=reverse('purchases:supplier_detail', kwargs={'pk': supplier.pk}),
                related_object_type='supplier',
                related_object_id=str(supplier.id),
                metadata={
                    'supplier_name': supplier.name,
                    'contact_info': {
                        'email': getattr(supplier, 'email', ''),
                        'phone': getattr(supplier, 'phone', ''),
                        'address': getattr(supplier, 'address', '')
                    },
                    'actions': [
                        {'type': 'verify_credentials', 'label': str(_('Verify Credentials')), 'class': 'bg-blue-600 text-white'},
                        {'type': 'setup_terms', 'label': str(_('Setup Payment Terms')), 'class': 'bg-green-600 text-white'},
                        {'type': 'approve_supplier', 'label': str(_('Approve Supplier')), 'class': 'bg-purple-600 text-white'},
                        {'type': 'request_documents', 'label': str(_('Request Documents')), 'class': 'bg-yellow-600 text-white'}
                    ]
                },
                tenant_id=supplier.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_new_supplier_created: {e}")


def _notify_supplier_status_change(supplier):
    """Notify about supplier status changes"""
    try:
        managers = _get_users_by_roles([
            'purchasing_manager', 'vendor_relations_manager'
        ], supplier.tenant_id)
        
        status_text = "activated" if supplier.is_active else "deactivated"
        
        for manager in managers:
            NotificationService.create_notification(
                recipient=manager,
                notification_type_code='supplier_status_change',
                title=str(_("Supplier Status Changed")).format(),
                message=str(_("Supplier {supplier_name} has been {status}")).format(
                    supplier_name=supplier.name,
                    status=status_text
                ),
                priority='medium' if supplier.is_active else 'high',
                action_required=not supplier.is_active,  # Require action when deactivated
                action_url=reverse('purchases:supplier_detail', kwargs={'pk': supplier.pk}),
                action_text=str(_("Review Supplier")),
                related_object_type='supplier',
                related_object_id=str(supplier.id),
                tenant_id=supplier.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_supplier_status_change: {e}")


def _get_po_items_summary(purchase_order):
    """Get summary of items in purchase order"""
    try:
        items = PurchaseOrderItem.objects.filter(purchase_order=purchase_order)
        return [
            {
                'name': item.item.name,
                'quantity': float(item.quantity),
                'unit_price': float(getattr(item, 'unit_price', 0))
            }
            for item in items
        ]
    except Exception as e:
        logger.error(f"Error getting PO items summary: {e}")
        return []


def _get_users_by_roles(role_codes, tenant_id):
    """Helper function to get users by role codes"""
    try:
        user_roles = UserRole.objects.filter(
            role__code__in=role_codes,
            tenant_id=tenant_id,
            is_active=True
        ).select_related('user', 'role')
        
        return [ur.user for ur in user_roles if ur.user.is_active]
    except Exception as e:
        logger.error(f"Error getting users by roles: {e}")
        return [] 