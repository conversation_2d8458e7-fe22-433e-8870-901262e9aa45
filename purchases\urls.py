from django.urls import path
from . import views

app_name = 'purchases'

urlpatterns = [
    # Dashboard
    path('', views.PurchaseDashboardView.as_view(), name='dashboard'),
    
    # Purchase Orders
    path('orders/', views.PurchaseOrderListView.as_view(), name='purchase_order_list'),
    path('orders/<uuid:pk>/', views.PurchaseOrderDetailView.as_view(), name='purchase_order_detail'),
    path('orders/create/', views.PurchaseOrderCreateView.as_view(), name='purchase_order_create'),
    path('orders/<uuid:pk>/update/', views.PurchaseOrderUpdateView.as_view(), name='purchase_order_update'),
    path('orders/<uuid:pk>/delete/', views.PurchaseOrderDeleteView.as_view(), name='purchase_order_delete'),
    
    # Purchase Order Items
    path('orders/<uuid:purchase_order_pk>/items/add/', views.PurchaseOrderItemCreateView.as_view(), name='purchase_order_item_create'),
    path('items/<uuid:pk>/update/', views.PurchaseOrderItemUpdateView.as_view(), name='purchase_order_item_update'),
    path('items/<uuid:pk>/delete/', views.PurchaseOrderItemDeleteView.as_view(), name='purchase_order_item_delete'),
    
    # Purchase Receipts
    path('orders/<uuid:purchase_order_pk>/receipts/create/', views.PurchaseReceiptCreateView.as_view(), name='purchase_receipt_create'),
    
    # Suppliers
    path('suppliers/', views.SupplierListView.as_view(), name='supplier_list'),
    path('suppliers/<uuid:pk>/', views.SupplierDetailView.as_view(), name='supplier_detail'),
    path('suppliers/create/', views.SupplierCreateView.as_view(), name='supplier_create'),
    path('suppliers/<uuid:pk>/update/', views.SupplierUpdateView.as_view(), name='supplier_update'),
    path('suppliers/<uuid:pk>/delete/', views.SupplierDeleteView.as_view(), name='supplier_delete'),
    
    # Reports
    path('reports/', views.PurchaseReportView.as_view(), name='purchase_reports'),
    
    # API Endpoints
    path('api/search-items/', views.api_search_items, name='api_search_items'),
    path('api/items/<uuid:item_id>/', views.api_get_item_details, name='api_get_item_details'),
    path('api/suppliers/', views.api_suppliers_list, name='api_suppliers_list'),
    
    # API Aliases for modal compatibility
    path('api/purchase-order/create/', views.PurchaseOrderCreateView.as_view(), name='purchase_order_create_api'),
    path('api/supplier/create/', views.SupplierCreateView.as_view(), name='supplier_create'),
] 