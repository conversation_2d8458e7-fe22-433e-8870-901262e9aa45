from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.db.models import Q, Sum, Count, Avg
from django.utils import timezone
from django.core.paginator import Paginator
import datetime

from .models import PurchaseOrder, PurchaseOrderItem, Supplier, PurchaseReceipt, PurchaseReceiptItem
from .forms import (
    SupplierForm, PurchaseOrderForm, PurchaseOrderItemForm, 
    PurchaseReceiptForm, PurchaseReceiptItemForm, PurchaseReportForm
)
from inventory.models import Item


class PurchaseDashboardView(LoginRequiredMixin, ListView):
    """Enhanced dashboard view for purchases module"""
    model = PurchaseOrder
    template_name = 'purchases/dashboard.html'
    context_object_name = 'recent_orders'
    paginate_by = 10
    
    def get_queryset(self):
        """Filter by tenant ID and get recent orders"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrder.objects.filter(tenant_id=tenant_id).order_by('-order_date')[:10]
        return PurchaseOrder.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            # Calculate comprehensive statistics
            now = timezone.now()
            this_month = now.replace(day=1)
            last_month = (this_month - datetime.timedelta(days=1)).replace(day=1)
            
            # Basic counts
            context['total_orders'] = PurchaseOrder.objects.filter(tenant_id=tenant_id).count()
            context['total_suppliers'] = Supplier.objects.filter(tenant_id=tenant_id, is_active=True).count()
            context['suppliers_count'] = context['total_suppliers']  # Alias for template compatibility
            
            # Status-based counts
            context['draft_orders'] = PurchaseOrder.objects.filter(tenant_id=tenant_id, status='draft').count()
            context['pending_orders'] = PurchaseOrder.objects.filter(tenant_id=tenant_id, status='sent').count()
            context['confirmed_orders'] = PurchaseOrder.objects.filter(tenant_id=tenant_id, status='confirmed').count()
            context['received_orders'] = PurchaseOrder.objects.filter(tenant_id=tenant_id, status='received').count()
            
            # Monthly statistics
            this_month_orders = PurchaseOrder.objects.filter(
                tenant_id=tenant_id,
                order_date__gte=this_month
            )
            context['this_month_orders'] = this_month_orders.count()
            context['this_month_value'] = this_month_orders.aggregate(
                total=Sum('total_amount')
            )['total'] or 0
            
            last_month_orders = PurchaseOrder.objects.filter(
                tenant_id=tenant_id,
                order_date__gte=last_month,
                order_date__lt=this_month
            )
            context['last_month_orders'] = last_month_orders.count()
            context['last_month_value'] = last_month_orders.aggregate(
                total=Sum('total_amount')
            )['total'] or 0
            
            # Top suppliers
            context['top_suppliers'] = Supplier.objects.filter(
                tenant_id=tenant_id,
                purchase_orders__isnull=False
            ).annotate(
                order_count=Count('purchase_orders'),
                total_value=Sum('purchase_orders__total_amount')
            ).order_by('-total_value')[:5]
            
            # Overdue deliveries
            context['overdue_deliveries'] = PurchaseOrder.objects.filter(
                tenant_id=tenant_id,
                expected_delivery_date__lt=now.date(),
                status__in=['sent', 'confirmed']
            ).count()
            
            # Recent activities
            context['recent_receipts'] = PurchaseReceipt.objects.filter(
                tenant_id=tenant_id
            ).order_by('-receipt_date')[:5]
            
        context['missing_tenant'] = tenant_id is None
        return context


class PurchaseOrderListView(LoginRequiredMixin, ListView):
    """Enhanced view to list all purchase orders"""
    model = PurchaseOrder
    template_name = 'purchases/purchase_order_list.html'
    context_object_name = 'purchase_orders'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant ID and apply search/filter params"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return PurchaseOrder.objects.none()
            
        queryset = PurchaseOrder.objects.filter(tenant_id=tenant_id).select_related('supplier')
        
        # Apply filters
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
            
        supplier_id = self.request.GET.get('supplier')
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)
            
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(order_number__icontains=search) |
                Q(supplier__name__icontains=search) |
                Q(notes__icontains=search)
            )
            
        # Date range filter
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(order_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(order_date__lte=date_to)
            
        # Default ordering
        return queryset.order_by('-order_date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            context['suppliers'] = Supplier.objects.filter(tenant_id=tenant_id, is_active=True)
            context['status_choices'] = PurchaseOrder.STATUS_CHOICES
            
            # Preserve filter params
            context['current_status'] = self.request.GET.get('status', '')
            context['current_supplier'] = self.request.GET.get('supplier', '')
            context['current_search'] = self.request.GET.get('search', '')
            context['current_date_from'] = self.request.GET.get('date_from', '')
            context['current_date_to'] = self.request.GET.get('date_to', '')
            
            # Calculate totals for current filter
            current_queryset = self.get_queryset()
            context['total_amount'] = current_queryset.aggregate(
                total=Sum('total_amount')
            )['total'] or 0
            
        context['missing_tenant'] = tenant_id is None
        return context


class PurchaseOrderDetailView(LoginRequiredMixin, DetailView):
    """Enhanced view to display purchase order details"""
    model = PurchaseOrder
    template_name = 'purchases/purchase_order_detail.html'
    context_object_name = 'purchase_order'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrder.objects.filter(tenant_id=tenant_id).select_related('supplier')
        return PurchaseOrder.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['items'] = self.object.items.select_related('item').all()
        context['receipts'] = self.object.receipts.all().order_by('-receipt_date')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        
        # Calculate totals
        context['total_ordered'] = sum(item.quantity for item in context['items'])
        context['total_received'] = sum(
            receipt_item.quantity 
            for receipt in context['receipts']
            for receipt_item in receipt.items.all()
        )
        
        return context


class PurchaseOrderCreateView(LoginRequiredMixin, CreateView):
    """Enhanced view to create a new purchase order"""
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'purchases/purchase_order_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        # Removed tenant_id check since we always provide a default tenant_id
        if tenant_id:
            form.instance.tenant_id = tenant_id
        response = super().form_valid(form)
        messages.success(self.request, _('Purchase order created successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('purchases:purchase_order_detail', kwargs={'pk': self.object.pk})


class PurchaseOrderUpdateView(LoginRequiredMixin, UpdateView):
    """Enhanced view to update an existing purchase order"""
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'purchases/purchase_order_form.html'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrder.objects.filter(tenant_id=tenant_id)
        return PurchaseOrder.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Purchase order updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('purchases:purchase_order_detail', kwargs={'pk': self.object.pk})


class PurchaseOrderDeleteView(LoginRequiredMixin, DeleteView):
    """View to delete a purchase order"""
    model = PurchaseOrder
    template_name = 'purchases/purchase_order_confirm_delete.html'
    success_url = reverse_lazy('purchases:purchase_order_list')
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrder.objects.filter(tenant_id=tenant_id)
        return PurchaseOrder.objects.none()
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Purchase order deleted successfully.'))
        return super().delete(request, *args, **kwargs)


# Purchase Order Item Views

class PurchaseOrderItemCreateView(LoginRequiredMixin, CreateView):
    """View to add items to a purchase order"""
    model = PurchaseOrderItem
    form_class = PurchaseOrderItemForm
    template_name = 'purchases/purchase_order_item_form.html'
    
    def get_purchase_order(self):
        """Get the purchase order"""
        return get_object_or_404(
            PurchaseOrder,
            pk=self.kwargs['purchase_order_pk'],
            tenant_id=getattr(self.request, 'tenant_id', None)
        )
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        kwargs['purchase_order'] = self.get_purchase_order()
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['purchase_order'] = self.get_purchase_order()
        context['title'] = _('Add Item to Purchase Order')
        return context
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('purchases:purchase_order_list')
            
        form.instance.tenant_id = tenant_id
        form.instance.purchase_order = self.get_purchase_order()
        response = super().form_valid(form)
        messages.success(self.request, _('Item added to purchase order successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('purchases:purchase_order_detail', kwargs={'pk': self.get_purchase_order().pk})


class PurchaseOrderItemUpdateView(LoginRequiredMixin, UpdateView):
    """View to update purchase order items"""
    model = PurchaseOrderItem
    form_class = PurchaseOrderItemForm
    template_name = 'purchases/purchase_order_item_form.html'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrderItem.objects.filter(tenant_id=tenant_id)
        return PurchaseOrderItem.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['purchase_order'] = self.object.purchase_order
        context['title'] = _('Update Purchase Order Item')
        return context
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Purchase order item updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('purchases:purchase_order_detail', kwargs={'pk': self.object.purchase_order.pk})


class PurchaseOrderItemDeleteView(LoginRequiredMixin, DeleteView):
    """View to delete purchase order items"""
    model = PurchaseOrderItem
    template_name = 'purchases/purchase_order_item_confirm_delete.html'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrderItem.objects.filter(tenant_id=tenant_id)
        return PurchaseOrderItem.objects.none()
    
    def delete(self, request, *args, **kwargs):
        purchase_order = self.get_object().purchase_order
        messages.success(request, _('Purchase order item deleted successfully.'))
        response = super().delete(request, *args, **kwargs)
        return response
    
    def get_success_url(self):
        return reverse('purchases:purchase_order_detail', kwargs={'pk': self.get_object().purchase_order.pk})


# Supplier Views

class SupplierListView(LoginRequiredMixin, ListView):
    """Enhanced view to list all suppliers"""
    model = Supplier
    template_name = 'purchases/supplier_list.html'
    context_object_name = 'suppliers'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant ID and search"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return Supplier.objects.none()
            
        queryset = Supplier.objects.filter(tenant_id=tenant_id)
        
        # Apply search if provided
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(email__icontains=search) |
                Q(phone__icontains=search)
            )
        
        # Filter by active status
        is_active = self.request.GET.get('is_active')
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        elif is_active == 'false':
            queryset = queryset.filter(is_active=False)
                
        return queryset.order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['current_search'] = self.request.GET.get('search', '')
        context['current_is_active'] = self.request.GET.get('is_active', '')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class SupplierDetailView(LoginRequiredMixin, DetailView):
    """Enhanced view to display supplier details"""
    model = Supplier
    template_name = 'purchases/supplier_detail.html'
    context_object_name = 'supplier'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Supplier.objects.filter(tenant_id=tenant_id)
        return Supplier.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get purchase orders with pagination
        purchase_orders = PurchaseOrder.objects.filter(
            supplier=self.object
        ).order_by('-order_date')
        
        paginator = Paginator(purchase_orders, 10)
        page_number = self.request.GET.get('page')
        context['purchase_orders'] = paginator.get_page(page_number)
        
        # Calculate statistics
        context['total_orders'] = purchase_orders.count()
        context['total_value'] = purchase_orders.aggregate(
            total=Sum('total_amount')
        )['total'] or 0
        context['avg_order_value'] = purchase_orders.aggregate(
            avg=Avg('total_amount')
        )['avg'] or 0
        
        # Recent activity
        context['recent_orders'] = purchase_orders[:5]
        
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class SupplierCreateView(LoginRequiredMixin, CreateView):
    """Enhanced view to create a new supplier"""
    model = Supplier
    form_class = SupplierForm
    template_name = 'purchases/supplier_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        # Removed tenant_id check since we always provide a default tenant_id
        if tenant_id:
            form.instance.tenant_id = tenant_id
        response = super().form_valid(form)
        messages.success(self.request, _('Supplier created successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('purchases:supplier_detail', kwargs={'pk': self.object.pk})


class SupplierUpdateView(LoginRequiredMixin, UpdateView):
    """Enhanced view to update an existing supplier"""
    model = Supplier
    form_class = SupplierForm
    template_name = 'purchases/supplier_form.html'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Supplier.objects.filter(tenant_id=tenant_id)
        return Supplier.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Supplier updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('purchases:supplier_detail', kwargs={'pk': self.object.pk})


class SupplierDeleteView(LoginRequiredMixin, DeleteView):
    """View to delete a supplier"""
    model = Supplier
    template_name = 'purchases/supplier_confirm_delete.html'
    success_url = reverse_lazy('purchases:supplier_list')
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Supplier.objects.filter(tenant_id=tenant_id)
        return Supplier.objects.none()
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Supplier deleted successfully.'))
        return super().delete(request, *args, **kwargs)


# Purchase Receipt Views

class PurchaseReceiptCreateView(LoginRequiredMixin, CreateView):
    """View to create purchase receipts"""
    model = PurchaseReceipt
    form_class = PurchaseReceiptForm
    template_name = 'purchases/purchase_receipt_form.html'
    
    def get_purchase_order(self):
        """Get the purchase order"""
        return get_object_or_404(
            PurchaseOrder,
            pk=self.kwargs['purchase_order_pk'],
            tenant_id=getattr(self.request, 'tenant_id', None)
        )
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        kwargs['purchase_order'] = self.get_purchase_order()
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['purchase_order'] = self.get_purchase_order()
        context['title'] = _('Create Receipt for Purchase Order')
        return context
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('purchases:purchase_order_list')
            
        form.instance.tenant_id = tenant_id
        form.instance.purchase_order = self.get_purchase_order()
        response = super().form_valid(form)
        messages.success(self.request, _('Purchase receipt created successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('purchases:purchase_order_detail', kwargs={'pk': self.get_purchase_order().pk})


# Purchase Report Views

class PurchaseReportView(LoginRequiredMixin, ListView):
    """View for purchase reports"""
    template_name = 'purchases/purchase_reports.html'
    context_object_name = 'results'
    paginate_by = 50
    
    def get_queryset(self):
        """Generate report data based on form parameters"""
        return []  # Will be populated based on form data
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        form = PurchaseReportForm(
            data=self.request.GET or None,
            tenant_id=tenant_id
        )
        context['form'] = form
        
        if form.is_valid() and tenant_id:
            report_type = form.cleaned_data['report_type']
            date_from = form.cleaned_data.get('date_from')
            date_to = form.cleaned_data.get('date_to')
            supplier = form.cleaned_data.get('supplier')
            
            # Generate report based on type
            if report_type == 'purchase_summary':
                context['results'] = self.get_purchase_summary(tenant_id, date_from, date_to, supplier)
            elif report_type == 'supplier_performance':
                context['results'] = self.get_supplier_performance(tenant_id, date_from, date_to, supplier)
            elif report_type == 'pending_orders':
                context['results'] = self.get_pending_orders(tenant_id, supplier)
            elif report_type == 'received_orders':
                context['results'] = self.get_received_orders(tenant_id, date_from, date_to, supplier)
            
            context['report_type'] = report_type
            context['report_generated'] = True
        
        context['missing_tenant'] = tenant_id is None
        return context
    
    def get_purchase_summary(self, tenant_id, date_from, date_to, supplier):
        """Generate purchase summary report"""
        queryset = PurchaseOrder.objects.filter(tenant_id=tenant_id)
        
        if date_from:
            queryset = queryset.filter(order_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(order_date__lte=date_to)
        if supplier:
            queryset = queryset.filter(supplier=supplier)
        
        return queryset.select_related('supplier').order_by('-order_date')
    
    def get_supplier_performance(self, tenant_id, date_from, date_to, supplier):
        """Generate supplier performance report"""
        queryset = Supplier.objects.filter(
            tenant_id=tenant_id,
            purchase_orders__isnull=False
        )
        
        if supplier:
            queryset = queryset.filter(id=supplier.id)
        
        # Add aggregations for performance metrics
        queryset = queryset.annotate(
            total_orders=Count('purchase_orders'),
            total_value=Sum('purchase_orders__total_amount'),
            avg_order_value=Avg('purchase_orders__total_amount')
        ).order_by('-total_value')
        
        return queryset
    
    def get_pending_orders(self, tenant_id, supplier):
        """Generate pending orders report"""
        queryset = PurchaseOrder.objects.filter(
            tenant_id=tenant_id,
            status__in=['draft', 'sent', 'confirmed']
        )
        
        if supplier:
            queryset = queryset.filter(supplier=supplier)
        
        return queryset.select_related('supplier').order_by('expected_delivery_date')
    
    def get_received_orders(self, tenant_id, date_from, date_to, supplier):
        """Generate received orders report"""
        queryset = PurchaseOrder.objects.filter(
            tenant_id=tenant_id,
            status='received'
        )
        
        if date_from:
            queryset = queryset.filter(order_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(order_date__lte=date_to)
        if supplier:
            queryset = queryset.filter(supplier=supplier)
        
        return queryset.select_related('supplier').order_by('-order_date')


# API Views

def api_search_items(request):
    """API endpoint to search items for purchase orders"""
    tenant_id = getattr(request, 'tenant_id', None)
    if not tenant_id:
        return JsonResponse({'error': 'Tenant ID missing'}, status=400)
    
    query = request.GET.get('q', '')
    items = Item.objects.filter(
        tenant_id=tenant_id,
        is_active=True,
        name__icontains=query
    )[:10]
    
    results = [{
        'id': str(item.id),
        'name': item.name,
        'sku': item.sku,
        'unit_price': float(item.unit_price),
        'current_stock': float(item.quantity)
    } for item in items]
    
    return JsonResponse({'results': results})


def api_get_item_details(request, item_id):
    """API endpoint to get detailed item information"""
    try:
        tenant_id = getattr(request, 'tenant_id', None)
        
        # Get item and check tenant
        item = Item.objects.get(id=item_id)
        if tenant_id and item.tenant_id != tenant_id:
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        return JsonResponse({
            'success': True,
            'item': {
                'id': str(item.id),
                'sku': item.sku,
                'name': item.name,
                'description': item.description or '',
                'unit_price': float(item.unit_price) if item.unit_price else 0,
                'unit': item.unit_of_measurement.symbol if item.unit_of_measurement else '',
            }
        })
        
    except Item.DoesNotExist:
        return JsonResponse({'error': 'Item not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def api_suppliers_list(request):
    """API endpoint to get list of suppliers"""
    tenant_id = getattr(request, 'tenant_id', None)
    
    if not tenant_id:
        return JsonResponse({'error': 'No tenant access'}, status=403)
    
    # Get active suppliers
    suppliers = Supplier.objects.filter(
        tenant_id=tenant_id,
        is_active=True
    ).order_by('name')
    
    # Optional search parameter
    search = request.GET.get('search', '')
    if search:
        suppliers = suppliers.filter(
            Q(name__icontains=search) |
            Q(supplier_code__icontains=search) |
            Q(contact_person__icontains=search)
        )
    
    # Limit results
    limit = int(request.GET.get('limit', 100))
    suppliers = suppliers[:limit]
    
    suppliers_data = []
    for supplier in suppliers:
        suppliers_data.append({
            'id': str(supplier.id),
            'name': supplier.name,
            'supplier_code': supplier.supplier_code or '',
            'contact_person': supplier.contact_person or '',
            'email': supplier.email or '',
            'phone': supplier.phone or '',
            'address': supplier.address or '',
            'payment_terms': supplier.payment_terms or 30,
            'is_active': supplier.is_active,
        })
    
    return JsonResponse({
        'success': True,
        'suppliers': suppliers_data
    })
