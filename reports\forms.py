from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.utils import timezone
import json

from .models import Report, Dashboard, DashboardWidget
from inventory.models import Item
from sales.models import SalesOrder
from purchases.models import PurchaseOrder


class ReportForm(forms.ModelForm):
    """Form for creating and updating reports"""
    
    class Meta:
        model = Report
        fields = ['name', 'description', 'report_type', 'parameters', 'query', 'is_scheduled', 'schedule']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter report name')
            }),
            'description': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter report description')
            }),
            'report_type': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'parameters': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 5,
                'placeholder': _('Enter parameters as JSON')
            }),
            'query': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 8,
                'placeholder': _('Enter SQL query or report definition')
            }),
            'is_scheduled': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2'
            }),
            'schedule': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter schedule configuration as JSON')
            })
        }
        labels = {
            'name': _('Report Name'),
            'description': _('Description'),
            'report_type': _('Report Type'),
            'parameters': _('Parameters'),
            'query': _('Query'),
            'is_scheduled': _('Scheduled Report'),
            'schedule': _('Schedule Configuration')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Add help text
        self.fields['parameters'].help_text = _('JSON format: {"param_name": "default_value"}')
        self.fields['schedule'].help_text = _('JSON format: {"frequency": "daily", "time": "09:00"}')
    
    def clean_parameters(self):
        parameters = self.cleaned_data.get('parameters')
        if parameters:
            try:
                if isinstance(parameters, str):
                    json.loads(parameters)
            except json.JSONDecodeError:
                raise ValidationError(_('Parameters must be valid JSON'))
        return parameters
    
    def clean_schedule(self):
        schedule = self.cleaned_data.get('schedule')
        if schedule:
            try:
                if isinstance(schedule, str):
                    json.loads(schedule)
            except json.JSONDecodeError:
                raise ValidationError(_('Schedule must be valid JSON'))
        return schedule


class DashboardForm(forms.ModelForm):
    """Form for creating and updating dashboards"""
    
    class Meta:
        model = Dashboard
        fields = ['name', 'description', 'layout', 'is_default']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter dashboard name')
            }),
            'description': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter dashboard description')
            }),
            'layout': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 5,
                'placeholder': _('Enter layout configuration as JSON')
            }),
            'is_default': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2'
            })
        }
        labels = {
            'name': _('Dashboard Name'),
            'description': _('Description'),
            'layout': _('Layout Configuration'),
            'is_default': _('Default Dashboard')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Add help text
        self.fields['layout'].help_text = _('JSON format: {"columns": 3, "grid": "responsive"}')
    
    def clean_layout(self):
        layout = self.cleaned_data.get('layout')
        if layout:
            try:
                if isinstance(layout, str):
                    json.loads(layout)
            except json.JSONDecodeError:
                raise ValidationError(_('Layout must be valid JSON'))
        return layout
    
    def clean(self):
        cleaned_data = super().clean()
        is_default = cleaned_data.get('is_default')
        
        if is_default and self.tenant_id:
            # Ensure only one default dashboard per tenant
            existing_default = Dashboard.objects.filter(
                tenant_id=self.tenant_id,
                is_default=True
            )
            if self.instance.pk:
                existing_default = existing_default.exclude(pk=self.instance.pk)
            
            if existing_default.exists():
                raise ValidationError({
                    'is_default': _('Another dashboard is already set as default. Please unset it first.')
                })
        
        return cleaned_data


class DashboardWidgetForm(forms.ModelForm):
    """Form for creating and updating dashboard widgets"""
    
    class Meta:
        model = DashboardWidget
        fields = ['title', 'widget_type', 'report', 'config', 'position']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter widget title')
            }),
            'widget_type': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'report': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'config': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 5,
                'placeholder': _('Enter widget configuration as JSON')
            }),
            'position': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'min': '0'
            })
        }
        labels = {
            'title': _('Widget Title'),
            'widget_type': _('Widget Type'),
            'report': _('Source Report'),
            'config': _('Configuration'),
            'position': _('Position')
        }

    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        self.dashboard = kwargs.pop('dashboard', None)
        super().__init__(*args, **kwargs)
        
        # Filter reports by tenant
        if self.tenant_id:
            self.fields['report'].queryset = Report.objects.filter(tenant_id=self.tenant_id)
        
        # Add help text
        self.fields['config'].help_text = _('JSON format: {"chart_type": "bar", "color": "#3B82F6"}')
        self.fields['report'].help_text = _('Optional: Select a report to power this widget')
    
    def clean_config(self):
        config = self.cleaned_data.get('config')
        if config:
            try:
                if isinstance(config, str):
                    json.loads(config)
            except json.JSONDecodeError:
                raise ValidationError(_('Configuration must be valid JSON'))
        return config


class BasicReportFilterForm(forms.Form):
    """Form for filtering basic reports"""
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('From Date')
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('To Date')
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default date range (last 30 days)
        if not self.initial.get('date_to'):
            self.initial['date_to'] = timezone.now().date()
        if not self.initial.get('date_from'):
            self.initial['date_from'] = timezone.now().date() - timezone.timedelta(days=30)
    
    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_('From date cannot be after to date.'))
        
        return cleaned_data


class InventoryReportForm(BasicReportFilterForm):
    """Form for inventory reports"""
    
    REPORT_TYPE_CHOICES = [
        ('current_stock', _('Current Stock Levels')),
        ('low_stock', _('Low Stock Items')),
        ('stock_movements', _('Stock Movements')),
        ('valuation', _('Inventory Valuation')),
        ('abc_analysis', _('ABC Analysis')),
    ]
    
    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Report Type')
    )
    
    category = forms.ModelChoiceField(
        queryset=None,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Category'),
        empty_label=_('All Categories')
    )

    def __init__(self, *args, **kwargs):
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Filter categories by tenant
        from inventory.models import ItemClassification
        if tenant_id:
            self.fields['category'].queryset = ItemClassification.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')


class SalesReportForm(BasicReportFilterForm):
    """Form for sales reports"""
    
    REPORT_TYPE_CHOICES = [
        ('sales_summary', _('Sales Summary')),
        ('top_customers', _('Top Customers')),
        ('top_products', _('Top Products')),
        ('sales_trend', _('Sales Trend')),
        ('profit_analysis', _('Profit Analysis')),
    ]
    
    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Report Type')
    )
    
    customer = forms.ModelChoiceField(
        queryset=None,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Customer'),
        empty_label=_('All Customers')
    )

    def __init__(self, *args, **kwargs):
        tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Filter customers by tenant
        from setup.models import Customer
        if tenant_id:
            self.fields['customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id
            ).order_by('first_name', 'last_name')


class ReportExecutionForm(forms.Form):
    """Form for executing reports with parameters"""
    
    EXPORT_FORMAT_CHOICES = [
        ('', _('View Online')),
        ('csv', _('CSV File')),
        ('excel', _('Excel File')),
        ('pdf', _('PDF File')),
    ]
    
    export_format = forms.ChoiceField(
        choices=EXPORT_FORMAT_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        }),
        label=_('Export Format')
    )

    def __init__(self, *args, **kwargs):
        self.report = kwargs.pop('report', None)
        super().__init__(*args, **kwargs)
        
        # Add dynamic parameter fields based on report configuration
        if self.report and self.report.parameters:
            for param_name, param_config in self.report.parameters.items():
                if isinstance(param_config, dict):
                    field_type = param_config.get('type', 'text')
                    field_label = param_config.get('label', param_name)
                    field_required = param_config.get('required', False)
                    field_default = param_config.get('default', '')
                else:
                    field_type = 'text'
                    field_label = param_name
                    field_required = False
                    field_default = param_config
                
                # Create appropriate field based on type
                if field_type == 'date':
                    field = forms.DateField(
                        required=field_required,
                        initial=field_default,
                        widget=forms.DateInput(attrs={
                            'type': 'date',
                            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
                        }),
                        label=field_label
                    )
                elif field_type == 'number':
                    field = forms.IntegerField(
                        required=field_required,
                        initial=field_default,
                        widget=forms.NumberInput(attrs={
                            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
                        }),
                        label=field_label
                    )
                elif field_type == 'choice':
                    choices = param_config.get('choices', [])
                    field = forms.ChoiceField(
                        choices=choices,
                        required=field_required,
                        initial=field_default,
                        widget=forms.Select(attrs={
                            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
                        }),
                        label=field_label
                    )
                else:  # text
                    field = forms.CharField(
                        required=field_required,
                        initial=field_default,
                        widget=forms.TextInput(attrs={
                            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
                        }),
                        label=field_label
                    )
                
                self.fields[f'param_{param_name}'] = field
