# Generated by Django 4.2.20 on 2025-05-07 10:01

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Dashboard',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('layout', models.JSONField(default=dict, verbose_name='Layout')),
                ('is_default', models.BooleanField(default=False, verbose_name='Default')),
            ],
            options={
                'verbose_name': 'Dashboard',
                'verbose_name_plural': 'Dashboards',
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('report_type', models.CharField(choices=[('inventory', 'Inventory'), ('sales', 'Sales'), ('purchases', 'Purchases'), ('warehouse', 'Warehouse'), ('custom', 'Custom')], max_length=20, verbose_name='Report Type')),
                ('parameters', models.JSONField(default=dict, verbose_name='Parameters')),
                ('query', models.TextField(blank=True, verbose_name='Query')),
                ('is_scheduled', models.BooleanField(default=False, verbose_name='Scheduled')),
                ('schedule', models.JSONField(blank=True, default=dict, verbose_name='Schedule')),
            ],
            options={
                'verbose_name': 'Report',
                'verbose_name_plural': 'Reports',
            },
        ),
        migrations.CreateModel(
            name='ReportExecution',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('parameters', models.JSONField(default=dict, verbose_name='Parameters Used')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20, verbose_name='Status')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='Start Time')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='End Time')),
                ('result_file', models.FileField(blank=True, null=True, upload_to='reports', verbose_name='Result File')),
                ('error_message', models.TextField(blank=True, verbose_name='Error Message')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='reports.report', verbose_name='Report')),
            ],
            options={
                'verbose_name': 'Report Execution',
                'verbose_name_plural': 'Report Executions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DashboardWidget',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('widget_type', models.CharField(choices=[('chart', 'Chart'), ('number', 'Number'), ('table', 'Table'), ('text', 'Text'), ('custom', 'Custom')], max_length=20, verbose_name='Widget Type')),
                ('config', models.JSONField(default=dict, verbose_name='Configuration')),
                ('position', models.IntegerField(default=0, verbose_name='Position')),
                ('dashboard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='widgets', to='reports.dashboard', verbose_name='Dashboard')),
                ('report', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='widgets', to='reports.report', verbose_name='Report')),
            ],
            options={
                'verbose_name': 'Dashboard Widget',
                'verbose_name_plural': 'Dashboard Widgets',
                'ordering': ['position'],
            },
        ),
    ]
