# Generated by Django 4.2.20 on 2025-06-15 14:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("reports", "0002_alter_dashboard_tenant_id_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="dashboard",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="dashboardwidget",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="report",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="reportexecution",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
    ]
