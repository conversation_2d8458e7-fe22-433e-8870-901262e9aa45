{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "Inventory Report" %}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 p-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{% trans "تقرير المخزون الأساسي" %}</h1>
                <p class="text-gray-600 mt-1">{% trans "تقرير شامل عن حالة المخزون الحالية" %}</p>
            </div>
            <div class="flex space-x-2">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print ml-2"></i>
                    {% trans "طباعة" %}
                </button>
                <button onclick="exportToExcel()" class="btn btn-secondary">
                    <i class="fas fa-file-excel ml-2"></i>
                    {% trans "تصدير Excel" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">{% trans "المرشحات" %}</h3>
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "التصنيف" %}</label>
                <select name="classification" class="form-select">
                    <option value="">{% trans "جميع التصنيفات" %}</option>
                    {% for classification in classifications %}
                    <option value="{{ classification.id }}" {% if request.GET.classification == classification.id|stringformat:"s" %}selected{% endif %}>
                        {{ classification.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الفئة" %}</label>
                <select name="category" class="form-select">
                    <option value="">{% trans "جميع الفئات" %}</option>
                    <option value="part" {% if request.GET.category == "part" %}selected{% endif %}>{% trans "قطع غيار" %}</option>
                    <option value="consumable" {% if request.GET.category == "consumable" %}selected{% endif %}>{% trans "مواد استهلاكية" %}</option>
                    <option value="tool" {% if request.GET.category == "tool" %}selected{% endif %}>{% trans "أدوات" %}</option>
                    <option value="equipment" {% if request.GET.category == "equipment" %}selected{% endif %}>{% trans "معدات" %}</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "حالة المخزون" %}</label>
                <select name="stock_status" class="form-select">
                    <option value="">{% trans "جميع الحالات" %}</option>
                    <option value="in_stock" {% if request.GET.stock_status == "in_stock" %}selected{% endif %}>{% trans "متوفر" %}</option>
                    <option value="low_stock" {% if request.GET.stock_status == "low_stock" %}selected{% endif %}>{% trans "مخزون منخفض" %}</option>
                    <option value="out_of_stock" {% if request.GET.stock_status == "out_of_stock" %}selected{% endif %}>{% trans "نفد المخزون" %}</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="btn btn-primary w-full">
                    <i class="fas fa-filter ml-2"></i>
                    {% trans "تطبيق المرشحات" %}
                </button>
            </div>
        </form>
    </div>

    <!-- Report Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-boxes text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">{% trans "إجمالي العناصر" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ summary.total_items|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">{% trans "متوفر" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ summary.in_stock|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">{% trans "مخزون منخفض" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ summary.low_stock|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times-circle text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">{% trans "نفد المخزون" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ summary.out_of_stock|default:0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">{% trans "تفاصيل المخزون" %}</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الكود" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "اسم العنصر" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الفئة" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الكمية الحالية" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الحد الأدنى" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "وحدة القياس" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "سعر الوحدة" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "القيمة الإجمالية" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الحالة" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for item in items %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ item.sku }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ item.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ item.get_category_display|default:"-" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ item.quantity|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ item.min_stock_level|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ item.unit_of_measurement.symbol|default:"-" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ item.unit_price|floatformat:2 }} {% trans "ر.س" %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ item.total_value|floatformat:2 }} {% trans "ر.س" %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if item.quantity <= 0 %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    {% trans "نفد المخزون" %}
                                </span>
                            {% elif item.is_low_stock %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    {% trans "مخزون منخفض" %}
                                </span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    {% trans "متوفر" %}
                                </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="px-6 py-12 text-center text-gray-500">
                            <i class="fas fa-box-open text-4xl mb-4"></i>
                            <p class="text-lg">{% trans "لا توجد عناصر مخزون متوفرة" %}</p>
                            <p class="text-sm">{% trans "جرب تغيير المرشحات أو إضافة عناصر جديدة" %}</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "السابق" %}
                    </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "التالي" %}
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {% trans "عرض" %}
                            <span class="font-medium">{{ page_obj.start_index }}</span>
                            {% trans "إلى" %}
                            <span class="font-medium">{{ page_obj.end_index }}</span>
                            {% trans "من" %}
                            <span class="font-medium">{{ page_obj.paginator.count }}</span>
                            {% trans "نتيجة" %}
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
function exportToExcel() {
    // Convert table to CSV and download
    const table = document.querySelector('table');
    let csv = '';
    
    // Add headers
    const headers = [];
    table.querySelectorAll('thead th').forEach(th => {
        headers.push(th.textContent.trim());
    });
    csv += headers.join(',') + '\n';
    
    // Add data rows
    table.querySelectorAll('tbody tr').forEach(tr => {
        const row = [];
        tr.querySelectorAll('td').forEach(td => {
            row.push('"' + td.textContent.trim().replace(/"/g, '""') + '"');
        });
        csv += row.join(',') + '\n';
    });
    
    // Download
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'inventory_report.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

// Print styles
const printStyles = `
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .text-2xl { font-size: 18px !important; }
            .text-lg { font-size: 14px !important; }
            .p-6 { padding: 12px !important; }
            .shadow-sm { box-shadow: none !important; }
        }
    </style>
`;
document.head.insertAdjacentHTML('beforeend', printStyles);
</script>
{% endblock %}