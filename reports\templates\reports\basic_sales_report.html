{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "Sales Report" %}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 p-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{% trans "تقرير المبيعات الأساسي" %}</h1>
                <p class="text-gray-600 mt-1">{% trans "تقرير شامل عن أداء المبيعات" %}</p>
            </div>
            <div class="flex space-x-2">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print ml-2"></i>
                    {% trans "طباعة" %}
                </button>
                <button onclick="exportToExcel()" class="btn btn-secondary">
                    <i class="fas fa-file-excel ml-2"></i>
                    {% trans "تصدير Excel" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">{% trans "المرشحات" %}</h3>
        <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "من تاريخ" %}</label>
                <input type="date" name="date_from" value="{{ request.GET.date_from }}" class="form-input">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "إلى تاريخ" %}</label>
                <input type="date" name="date_to" value="{{ request.GET.date_to }}" class="form-input">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "العميل" %}</label>
                <select name="customer" class="form-select">
                    <option value="">{% trans "جميع العملاء" %}</option>
                    {% for customer in customers %}
                    <option value="{{ customer.id }}" {% if request.GET.customer == customer.id|stringformat:"s" %}selected{% endif %}>
                        {{ customer.company_name|default:customer.first_name }} {{ customer.last_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "حالة الطلب" %}</label>
                <select name="status" class="form-select">
                    <option value="">{% trans "جميع الحالات" %}</option>
                    <option value="draft" {% if request.GET.status == "draft" %}selected{% endif %}>{% trans "مسودة" %}</option>
                    <option value="confirmed" {% if request.GET.status == "confirmed" %}selected{% endif %}>{% trans "مؤكد" %}</option>
                    <option value="shipped" {% if request.GET.status == "shipped" %}selected{% endif %}>{% trans "تم الشحن" %}</option>
                    <option value="delivered" {% if request.GET.status == "delivered" %}selected{% endif %}>{% trans "تم التسليم" %}</option>
                    <option value="cancelled" {% if request.GET.status == "cancelled" %}selected{% endif %}>{% trans "ملغي" %}</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="btn btn-primary w-full">
                    <i class="fas fa-filter ml-2"></i>
                    {% trans "تطبيق المرشحات" %}
                </button>
            </div>
        </form>
    </div>

    <!-- Report Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">{% trans "إجمالي الطلبات" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ summary.total_orders|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">{% trans "إجمالي المبيعات" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ summary.total_revenue|floatformat:2|default:0 }} {% trans "ر.س" %}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">{% trans "متوسط قيمة الطلب" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ summary.avg_order_value|floatformat:2|default:0 }} {% trans "ر.س" %}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">{% trans "عملاء فريدين" %}</p>
                    <p class="text-2xl font-bold text-gray-900">{{ summary.unique_customers|default:0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">{% trans "تفاصيل المبيعات" %}</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "رقم الطلب" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "العميل" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "تاريخ الطلب" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "إجمالي المبلغ" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "عدد العناصر" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الحالة" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "العناصر" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for order in orders %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ order.order_number }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if order.customer.company_name %}
                                {{ order.customer.company_name }}
                            {% else %}
                                {{ order.customer.first_name }} {{ order.customer.last_name }}
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ order.order_date|date:"Y-m-d" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ order.total_amount|floatformat:2 }} {% trans "ر.س" %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ order.items.count }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if order.status == 'draft' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {% trans "مسودة" %}
                                </span>
                            {% elif order.status == 'confirmed' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {% trans "مؤكد" %}
                                </span>
                            {% elif order.status == 'shipped' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    {% trans "تم الشحن" %}
                                </span>
                            {% elif order.status == 'delivered' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    {% trans "تم التسليم" %}
                                </span>
                            {% elif order.status == 'cancelled' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    {% trans "ملغي" %}
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500">
                            <div class="max-w-xs">
                                {% for item in order.items.all|slice:":3" %}
                                    <div class="text-xs">{{ item.quantity|floatformat:0 }}x {{ item.item.name|truncatechars:20 }}</div>
                                {% endfor %}
                                {% if order.items.count > 3 %}
                                    <div class="text-xs text-gray-400">+{{ order.items.count|add:"-3" }} {% trans "أكثر" %}</div>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                            <i class="fas fa-shopping-cart text-4xl mb-4"></i>
                            <p class="text-lg">{% trans "لا توجد مبيعات متوفرة" %}</p>
                            <p class="text-sm">{% trans "جرب تغيير المرشحات أو إضافة طلبات جديدة" %}</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "السابق" %}
                    </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "التالي" %}
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {% trans "عرض" %}
                            <span class="font-medium">{{ page_obj.start_index }}</span>
                            {% trans "إلى" %}
                            <span class="font-medium">{{ page_obj.end_index }}</span>
                            {% trans "من" %}
                            <span class="font-medium">{{ page_obj.paginator.count }}</span>
                            {% trans "نتيجة" %}
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Top Products Section -->
    <div class="bg-white rounded-lg shadow-sm mt-6 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">{% trans "أكثر المنتجات مبيعاً" %}</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "المنتج" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الكمية المباعة" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "إجمالي الإيرادات" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "عدد الطلبات" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for product in top_products %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ product.item__name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ product.total_quantity|floatformat:2 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ product.total_revenue|floatformat:2 }} {% trans "ر.س" %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ product.order_count }}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                            {% trans "لا توجد بيانات منتجات متوفرة" %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    // Convert table to CSV and download
    const table = document.querySelector('table');
    let csv = '';
    
    // Add headers
    const headers = [];
    table.querySelectorAll('thead th').forEach(th => {
        headers.push(th.textContent.trim());
    });
    csv += headers.join(',') + '\n';
    
    // Add data rows
    table.querySelectorAll('tbody tr').forEach(tr => {
        const row = [];
        tr.querySelectorAll('td').forEach(td => {
            row.push('"' + td.textContent.trim().replace(/"/g, '""') + '"');
        });
        csv += row.join(',') + '\n';
    });
    
    // Download
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sales_report.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

// Print styles
const printStyles = `
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .text-2xl { font-size: 18px !important; }
            .text-lg { font-size: 14px !important; }
            .p-6 { padding: 12px !important; }
            .shadow-sm { box-shadow: none !important; }
        }
    </style>
`;
document.head.insertAdjacentHTML('beforeend', printStyles);
</script>
{% endblock %}