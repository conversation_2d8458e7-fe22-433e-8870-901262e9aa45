from django.urls import path
from . import views

app_name = 'reports'
 
urlpatterns = [
    # Basic reports (available without advanced reporting)
    path('', views.ReportListView.as_view(), name='report_list'),
    path('basic/inventory/', views.BasicInventoryReportView.as_view(), name='basic_inventory'),
    path('basic/sales/', views.BasicSalesReportView.as_view(), name='basic_sales'),
    path('basic/', views.BasicInventoryReportView.as_view(), name='basic_reports'),
    
    # Report management (CRUD)
    path('reports/', views.ReportListView.as_view(), name='report_list_full'),
    path('reports/create/', views.ReportCreateView.as_view(), name='report_create'),
    path('reports/<uuid:pk>/', views.ReportDetailView.as_view(), name='report_detail'),
    path('reports/<uuid:pk>/update/', views.ReportUpdateView.as_view(), name='report_update'),
    path('reports/<uuid:pk>/delete/', views.ReportDeleteView.as_view(), name='report_delete'),
    path('reports/<uuid:pk>/execute/', views.ExecuteReportView.as_view(), name='execute_report'),
    
    # Report executions
    path('executions/<uuid:pk>/', views.ReportExecutionDetailView.as_view(), name='report_execution'),
    
    # Dashboards (requires advanced reporting feature) - CRUD
    path('dashboards/', views.DashboardListView.as_view(), name='dashboard_list'),
    path('dashboards/create/', views.DashboardCreateView.as_view(), name='dashboard_create'),
    path('dashboards/<uuid:pk>/', views.DashboardDetailView.as_view(), name='dashboard_detail'),
    path('dashboards/<uuid:pk>/update/', views.DashboardUpdateView.as_view(), name='dashboard_update'),
    path('dashboards/<uuid:pk>/delete/', views.DashboardDeleteView.as_view(), name='dashboard_delete'),
    
    # Dashboard Widgets - CRUD
    path('dashboards/<uuid:dashboard_pk>/widgets/add/', views.DashboardWidgetCreateView.as_view(), name='dashboard_widget_create'),
    path('widgets/<uuid:pk>/update/', views.DashboardWidgetUpdateView.as_view(), name='dashboard_widget_update'),
    path('widgets/<uuid:pk>/delete/', views.DashboardWidgetDeleteView.as_view(), name='dashboard_widget_delete'),
    
    # API Endpoints
    path('api/widgets/<uuid:pk>/data/', views.DashboardWidgetDataView.as_view(), name='widget_data'),
    path('api/reports/<uuid:report_id>/data/', views.api_get_report_data, name='api_report_data'),
    path('api/executions/<uuid:execution_id>/export/', views.api_export_report, name='api_export_report'),
] 