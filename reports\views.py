from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.views.generic.base import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy, reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.db.models import Q, Sum, Count, Avg
from django.core.paginator import Paginator
import json
import pandas as pd
import logging

from feature_flags.services import is_feature_active
from .models import Report, ReportExecution, Dashboard, DashboardWidget
from .forms import (
    ReportForm, DashboardForm, DashboardWidgetForm, BasicReportFilterForm,
    InventoryReportForm, SalesReportForm, ReportExecutionForm
)
from .services import execute_report, get_dashboard_data

logger = logging.getLogger(__name__)


class AdvancedReportingRequiredMixin:
    """Mixin to check if advanced reporting feature is enabled"""
    
    def dispatch(self, request, *args, **kwargs):
        if not is_feature_active('advanced_reporting', request):
            messages.warning(request, _("Advanced reporting feature is not enabled."))
            return redirect('reports:basic_reports')
        return super().dispatch(request, *args, **kwargs)


class ReportListView(LoginRequiredMixin, ListView):
    """Enhanced view to list all available reports"""
    model = Report
    template_name = 'reports/report_list.html'
    context_object_name = 'reports'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter reports by tenant and search"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return Report.objects.none()
            
        queryset = Report.objects.filter(tenant_id=tenant_id)
        
        # Apply search if provided
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(report_type__icontains=search)
            )
        
        # Filter by report type
        report_type = self.request.GET.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)
            
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['has_advanced_reporting'] = is_feature_active('advanced_reporting', self.request)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        context['report_types'] = Report.REPORT_TYPES
        context['current_search'] = self.request.GET.get('search', '')
        context['current_report_type'] = self.request.GET.get('report_type', '')
        return context


class ReportDetailView(LoginRequiredMixin, DetailView):
    """Enhanced view to display a report and its parameters"""
    model = Report
    template_name = 'reports/report_detail.html'
    context_object_name = 'report'
    
    def get_queryset(self):
        """Filter reports by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Report.objects.filter(tenant_id=tenant_id)
        return Report.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['parameters'] = self.object.parameters
        context['executions'] = self.object.executions.order_by('-created_at')[:10]
        context['has_advanced_reporting'] = is_feature_active('advanced_reporting', self.request)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        
        # Calculate execution statistics
        executions = self.object.executions.all()
        context['total_executions'] = executions.count()
        context['successful_executions'] = executions.filter(status='completed').count()
        context['failed_executions'] = executions.filter(status='failed').count()
        
        return context


class ReportCreateView(LoginRequiredMixin, AdvancedReportingRequiredMixin, CreateView):
    """View to create a new report"""
    model = Report
    form_class = ReportForm
    template_name = 'reports/report_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('reports:report_list')
            
        form.instance.tenant_id = tenant_id
        response = super().form_valid(form)
        messages.success(self.request, _('Report created successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('reports:report_detail', kwargs={'pk': self.object.pk})


class ReportUpdateView(LoginRequiredMixin, AdvancedReportingRequiredMixin, UpdateView):
    """View to update an existing report"""
    model = Report
    form_class = ReportForm
    template_name = 'reports/report_form.html'
    
    def get_queryset(self):
        """Filter reports by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Report.objects.filter(tenant_id=tenant_id)
        return Report.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Report updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('reports:report_detail', kwargs={'pk': self.object.pk})


class ReportDeleteView(LoginRequiredMixin, AdvancedReportingRequiredMixin, DeleteView):
    """View to delete a report"""
    model = Report
    template_name = 'reports/report_confirm_delete.html'
    success_url = reverse_lazy('reports:report_list')
    
    def get_queryset(self):
        """Filter reports by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Report.objects.filter(tenant_id=tenant_id)
        return Report.objects.none()
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Report deleted successfully.'))
        return super().delete(request, *args, **kwargs)


class ExecuteReportView(LoginRequiredMixin, View):
    """Enhanced view to execute a report and display results"""
    
    def get(self, request, pk):
        """Display form to set report parameters"""
        tenant_id = getattr(request, 'tenant_id', None)
        # Removed tenant_id check since we always provide a default tenant_id
        
        # Get the report with tenant filtering if tenant_id exists
        if tenant_id:
            report = get_object_or_404(Report, pk=pk, tenant_id=tenant_id)
        else:
            report = get_object_or_404(Report, pk=pk)
        
        form = ReportExecutionForm(report=report)
        
        return render(request, 'reports/execute_report.html', {
            'report': report,
            'form': form,
            'parameters': report.parameters,
            'has_advanced_reporting': is_feature_active('advanced_reporting', request)
        })
    
    def post(self, request, pk):
        """Execute report with provided parameters"""
        tenant_id = getattr(request, 'tenant_id', None)
        # Removed tenant_id check since we always provide a default tenant_id
        
        # Get the report with tenant filtering if tenant_id exists
        if tenant_id:
            report = get_object_or_404(Report, pk=pk, tenant_id=tenant_id)
        else:
            report = get_object_or_404(Report, pk=pk)
        
        form = ReportExecutionForm(data=request.POST, report=report)
        
        if form.is_valid():
            try:
                # Parse parameters from form
                parameters = {}
                for key, value in form.cleaned_data.items():
                    if key.startswith('param_'):
                        param_name = key[6:]  # Remove 'param_' prefix
                        parameters[param_name] = value
                
                # Handle export format
                export_format = form.cleaned_data.get('export_format')
                if export_format:
                    parameters['export_format'] = export_format
                
                # Execute report
                execution, data = execute_report(
                    report_id=pk,
                    parameters=parameters,
                    tenant_id=tenant_id,
                    user=request.user
                )
                
                # Render results
                return render(request, 'reports/report_results.html', {
                    'report': report,
                    'execution': execution,
                    'data': data,
                    'has_advanced_reporting': is_feature_active('advanced_reporting', request)
                })
                
            except Exception as e:
                logger.exception(f"Error executing report: {e}")
                messages.error(request, _("Error executing report: {}").format(str(e)))
                
        return render(request, 'reports/execute_report.html', {
            'report': report,
            'form': form,
            'parameters': report.parameters,
            'has_advanced_reporting': is_feature_active('advanced_reporting', request)
        })


class ReportExecutionDetailView(LoginRequiredMixin, DetailView):
    """Enhanced view to display execution details and results"""
    model = ReportExecution
    template_name = 'reports/execution_detail.html'
    context_object_name = 'execution'
    
    def get_queryset(self):
        """Filter executions by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ReportExecution.objects.filter(tenant_id=tenant_id)
        return ReportExecution.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['report'] = self.object.report
        
        # Try to reconstruct data from the result file if available
        if self.object.result_file:
            try:
                # Determine file type by extension
                filename = self.object.result_file.name
                if filename.endswith('.json'):
                    with open(self.object.result_file.path, 'r') as f:
                        context['data'] = json.load(f)
                elif filename.endswith('.csv'):
                    df = pd.read_csv(self.object.result_file.path)
                    context['data'] = {
                        'title': self.object.report.name,
                        'generated_at': self.object.created_at.isoformat(),
                        'data': df.to_dict('records')
                    }
                elif filename.endswith('.xlsx'):
                    df = pd.read_excel(self.object.result_file.path)
                    context['data'] = {
                        'title': self.object.report.name,
                        'generated_at': self.object.created_at.isoformat(),
                        'data': df.to_dict('records')
                    }
            except Exception as e:
                logger.exception(f"Error loading result file: {e}")
                context['error'] = _("Error loading result file: {}").format(str(e))
        
        context['has_advanced_reporting'] = is_feature_active('advanced_reporting', self.request)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


# Dashboard views (requires advanced reporting feature)

class DashboardListView(LoginRequiredMixin, AdvancedReportingRequiredMixin, ListView):
    """Enhanced view to list all available dashboards"""
    model = Dashboard
    template_name = 'reports/dashboard_list.html'
    context_object_name = 'dashboards'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter dashboards by tenant and search"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return Dashboard.objects.none()
            
        queryset = Dashboard.objects.filter(tenant_id=tenant_id)
        
        # Apply search if provided
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )
            
        return queryset.order_by('-is_default', '-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        context['current_search'] = self.request.GET.get('search', '')
        return context


class DashboardDetailView(LoginRequiredMixin, AdvancedReportingRequiredMixin, DetailView):
    """Enhanced view to display a dashboard with its widgets"""
    model = Dashboard
    template_name = 'reports/dashboard_detail.html'
    context_object_name = 'dashboard'
    
    def get_queryset(self):
        """Filter dashboards by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Dashboard.objects.filter(tenant_id=tenant_id)
        return Dashboard.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get widgets sorted by position
        context['widgets'] = self.object.widgets.all().order_by('position')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        
        tenant_id = getattr(self.request, 'tenant_id', None)
        try:
            # Get initial data for each widget
            for widget in context['widgets']:
                if widget.report:
                    widget.data = get_dashboard_data(
                        widget_id=widget.id,
                        tenant_id=tenant_id
                    )
        except Exception as e:
            logger.exception(f"Error loading dashboard data: {e}")
            messages.error(self.request, _("Error loading dashboard data: {}").format(str(e)))
            
        return context


class DashboardCreateView(LoginRequiredMixin, AdvancedReportingRequiredMixin, CreateView):
    """View to create a new dashboard"""
    model = Dashboard
    form_class = DashboardForm
    template_name = 'reports/dashboard_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('reports:dashboard_list')
            
        form.instance.tenant_id = tenant_id
        response = super().form_valid(form)
        messages.success(self.request, _('Dashboard created successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('reports:dashboard_detail', kwargs={'pk': self.object.pk})


class DashboardUpdateView(LoginRequiredMixin, AdvancedReportingRequiredMixin, UpdateView):
    """View to update an existing dashboard"""
    model = Dashboard
    form_class = DashboardForm
    template_name = 'reports/dashboard_form.html'
    
    def get_queryset(self):
        """Filter dashboards by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Dashboard.objects.filter(tenant_id=tenant_id)
        return Dashboard.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Dashboard updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('reports:dashboard_detail', kwargs={'pk': self.object.pk})


class DashboardDeleteView(LoginRequiredMixin, AdvancedReportingRequiredMixin, DeleteView):
    """View to delete a dashboard"""
    model = Dashboard
    template_name = 'reports/dashboard_confirm_delete.html'
    success_url = reverse_lazy('reports:dashboard_list')
    
    def get_queryset(self):
        """Filter dashboards by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Dashboard.objects.filter(tenant_id=tenant_id)
        return Dashboard.objects.none()
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Dashboard deleted successfully.'))
        return super().delete(request, *args, **kwargs)


class DashboardWidgetDataView(LoginRequiredMixin, AdvancedReportingRequiredMixin, View):
    """Enhanced API view to get data for a dashboard widget"""
    
    def get(self, request, pk):
        """Get data for the specified widget"""
        tenant_id = getattr(request, 'tenant_id', None)
        if not tenant_id:
            return JsonResponse({
                'success': False,
                'error': 'Tenant ID is missing'
            }, status=400)
            
        try:
            widget = get_object_or_404(DashboardWidget, pk=pk, tenant_id=tenant_id)
            data = get_dashboard_data(widget_id=pk, tenant_id=tenant_id)
            
            return JsonResponse({
                'success': True,
                'widget_id': str(widget.id),
                'title': widget.title,
                'data': data
            })
        except Exception as e:
            logger.exception(f"Error generating widget data: {e}")
            return JsonResponse({
                'success': False,
                'widget_id': str(pk),
                'error': str(e)
            }, status=500)


# Dashboard Widget CRUD Views

class DashboardWidgetCreateView(LoginRequiredMixin, AdvancedReportingRequiredMixin, CreateView):
    """View to add widgets to a dashboard"""
    model = DashboardWidget
    form_class = DashboardWidgetForm
    template_name = 'reports/dashboard_widget_form.html'
    
    def get_dashboard(self):
        """Get the dashboard"""
        return get_object_or_404(
            Dashboard,
            pk=self.kwargs['dashboard_pk'],
            tenant_id=getattr(self.request, 'tenant_id', None)
        )
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        kwargs['dashboard'] = self.get_dashboard()
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['dashboard'] = self.get_dashboard()
        context['title'] = _('Add Widget to Dashboard')
        return context
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing."))
            return redirect('reports:dashboard_list')
            
        form.instance.tenant_id = tenant_id
        form.instance.dashboard = self.get_dashboard()
        response = super().form_valid(form)
        messages.success(self.request, _('Widget added to dashboard successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('reports:dashboard_detail', kwargs={'pk': self.get_dashboard().pk})


class DashboardWidgetUpdateView(LoginRequiredMixin, AdvancedReportingRequiredMixin, UpdateView):
    """View to update dashboard widgets"""
    model = DashboardWidget
    form_class = DashboardWidgetForm
    template_name = 'reports/dashboard_widget_form.html'
    
    def get_queryset(self):
        """Filter widgets by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return DashboardWidget.objects.filter(tenant_id=tenant_id)
        return DashboardWidget.objects.none()
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = getattr(self.request, 'tenant_id', None)
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['dashboard'] = self.object.dashboard
        context['title'] = _('Update Dashboard Widget')
        return context
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, _('Dashboard widget updated successfully.'))
        return response
    
    def get_success_url(self):
        return reverse('reports:dashboard_detail', kwargs={'pk': self.object.dashboard.pk})


class DashboardWidgetDeleteView(LoginRequiredMixin, AdvancedReportingRequiredMixin, DeleteView):
    """View to delete dashboard widgets"""
    model = DashboardWidget
    template_name = 'reports/dashboard_widget_confirm_delete.html'
    
    def get_queryset(self):
        """Filter widgets by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return DashboardWidget.objects.filter(tenant_id=tenant_id)
        return DashboardWidget.objects.none()
    
    def delete(self, request, *args, **kwargs):
        dashboard = self.get_object().dashboard
        messages.success(request, _('Dashboard widget deleted successfully.'))
        response = super().delete(request, *args, **kwargs)
        return response
    
    def get_success_url(self):
        return reverse('reports:dashboard_detail', kwargs={'pk': self.get_object().dashboard.pk})


# Basic reports (available without advanced reporting feature)

class BasicInventoryReportView(LoginRequiredMixin, View):
    """Enhanced view for basic inventory report"""
    
    def get(self, request):
        """Display basic inventory report with filtering"""
        tenant_id = getattr(request, 'tenant_id', None)
        
        from inventory.models import Item, Movement, ItemClassification
        
        # Get all items for the tenant
        items_queryset = Item.objects.filter(tenant_id=tenant_id)
        
        # Apply filters
        classification = request.GET.get('classification')
        category = request.GET.get('category')
        stock_status = request.GET.get('stock_status')
        
        if classification:
            items_queryset = items_queryset.filter(classification_id=classification)
        
        if category:
            items_queryset = items_queryset.filter(category=category)
        
        if stock_status:
            if stock_status == 'in_stock':
                items_queryset = items_queryset.filter(quantity__gt=0)
            elif stock_status == 'low_stock':
                items_queryset = [item for item in items_queryset if item.is_low_stock]
            elif stock_status == 'out_of_stock':
                items_queryset = items_queryset.filter(quantity__lte=0)
        
        # Calculate summary statistics
        all_items = Item.objects.filter(tenant_id=tenant_id)
        summary = {
            'total_items': all_items.count(),
            'in_stock': all_items.filter(quantity__gt=0).count(),
            'low_stock': len([item for item in all_items if item.is_low_stock]),
            'out_of_stock': all_items.filter(quantity__lte=0).count(),
        }
        
        # Add total_value to each item
        items = list(items_queryset.select_related('unit_of_measurement', 'classification'))
        for item in items:
            item.total_value = item.quantity * item.unit_price
        
        # Paginate items
        from django.core.paginator import Paginator
        paginator = Paginator(items, 50)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        context = {
            'items': page_obj,
            'summary': summary,
            'classifications': ItemClassification.objects.filter(tenant_id=tenant_id, is_active=True),
            'has_advanced_reporting': is_feature_active('advanced_reporting', request),
            'title': _('Inventory Report'),
            'is_paginated': paginator.num_pages > 1,
            'page_obj': page_obj,
        }
        
        return render(request, 'reports/basic_inventory_report.html', context)


class BasicSalesReportView(LoginRequiredMixin, View):
    """Enhanced view for basic sales report"""
    
    def get(self, request):
        """Display basic sales report with filtering"""
        tenant_id = getattr(request, 'tenant_id', None)
        
        from sales.models import SalesOrder, SalesOrderItem
        from setup.models import Customer
        from django.db.models import F
        from datetime import datetime
        
        # Get all orders for the tenant
        orders_queryset = SalesOrder.objects.filter(tenant_id=tenant_id).select_related('customer')
        
        # Apply filters
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        customer_id = request.GET.get('customer')
        status = request.GET.get('status')
        
        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                orders_queryset = orders_queryset.filter(order_date__gte=date_from)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                orders_queryset = orders_queryset.filter(order_date__lte=date_to)
            except ValueError:
                pass
        
        if customer_id:
            orders_queryset = orders_queryset.filter(customer_id=customer_id)
            
        if status:
            orders_queryset = orders_queryset.filter(status=status)
        
        # Calculate summary statistics
        all_orders = SalesOrder.objects.filter(tenant_id=tenant_id)
        summary = {
            'total_orders': orders_queryset.count(),
            'total_revenue': orders_queryset.aggregate(total=Sum('total_amount'))['total'] or 0,
            'avg_order_value': orders_queryset.aggregate(avg=Avg('total_amount'))['avg'] or 0,
            'unique_customers': orders_queryset.values('customer').distinct().count(),
        }
        
        # Get top products
        top_products = SalesOrderItem.objects.filter(
            sales_order__in=orders_queryset
                ).values('item__name').annotate(
                    total_quantity=Sum('quantity'),
            total_revenue=Sum(F('quantity') * F('unit_price')),
            order_count=Count('sales_order', distinct=True)
                ).order_by('-total_revenue')[:10]
        
        # Paginate orders
        from django.core.paginator import Paginator
        orders = orders_queryset.order_by('-order_date').prefetch_related('items__item')
        paginator = Paginator(orders, 50)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        context = {
            'orders': page_obj,
            'summary': summary,
            'top_products': top_products,
            'customers': Customer.objects.filter(tenant_id=tenant_id, is_active=True),
            'has_advanced_reporting': is_feature_active('advanced_reporting', request),
            'title': _('Sales Report'),
            'is_paginated': paginator.num_pages > 1,
            'page_obj': page_obj,
        }
        
        return render(request, 'reports/basic_sales_report.html', context)


# API Views

def api_get_report_data(request, report_id):
    """API endpoint to get report data"""
    tenant_id = getattr(request, 'tenant_id', None)
    if not tenant_id:
        return JsonResponse({'error': 'Tenant ID missing'}, status=400)
    
    try:
        report = Report.objects.get(id=report_id, tenant_id=tenant_id)
        # This would integrate with the report execution service
        data = {
            'report_id': str(report.id),
            'name': report.name,
            'type': report.report_type,
            'data': []  # Placeholder for actual report data
        }
        return JsonResponse(data)
    except Report.DoesNotExist:
        return JsonResponse({'error': 'Report not found'}, status=404)


def api_export_report(request, execution_id):
    """API endpoint to export report results"""
    tenant_id = getattr(request, 'tenant_id', None)
    if not tenant_id:
        return JsonResponse({'error': 'Tenant ID missing'}, status=400)
    
    try:
        execution = ReportExecution.objects.get(id=execution_id, tenant_id=tenant_id)
        if execution.result_file:
            # Return file download response
            response = HttpResponse(
                execution.result_file.read(),
                content_type='application/octet-stream'
            )
            response['Content-Disposition'] = f'attachment; filename="{execution.report.name}.csv"'
            return response
        else:
            return JsonResponse({'error': 'No result file available'}, status=404)
    except ReportExecution.DoesNotExist:
        return JsonResponse({'error': 'Execution not found'}, status=404)
