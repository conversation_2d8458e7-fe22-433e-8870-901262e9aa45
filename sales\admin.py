from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from sales.models import SalesOrder, SalesOrderItem, SalesReturn, SalesReturnItem
from core.admin import TenantAdminMixin


class SalesOrderItemInline(admin.TabularInline):
    model = SalesOrderItem
    extra = 0
    raw_id_fields = ('item',)


class SalesReturnItemInline(admin.TabularInline):
    model = SalesReturnItem
    extra = 0
    raw_id_fields = ('sales_order_item',)


@admin.register(SalesOrder)
class SalesOrderAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('order_number', 'customer', 'work_order_number', 'service_type', 'order_date', 'status', 'total_amount', 'tenant_id')
    list_filter = ('status', 'service_type', 'order_date', 'service_center')
    search_fields = ('order_number', 'customer__first_name', 'customer__last_name', 'work_order_number', 'vehicle__license_plate', 'notes')
    readonly_fields = ('created_at', 'updated_at', 'total_amount', 'labor_cost', 'parts_cost')
    raw_id_fields = ('customer', 'work_order', 'service_center', 'vehicle', 'technician')
    inlines = [SalesOrderItemInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'order_number', 'customer', 'order_date', 'status')
        }),
        (_('Work Order Integration'), {
            'fields': ('work_order', 'work_order_number', 'service_type', 'service_completion_date')
        }),
        (_('Service Information'), {
            'fields': ('service_center', 'vehicle', 'technician', 'vehicle_odometer', 'vehicle_condition_notes')
        }),
        (_('Shipping'), {
            'fields': ('shipping_address',)
        }),
        (_('Financial'), {
            'fields': ('total_amount', 'labor_cost', 'parts_cost')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SalesOrderItem)
class SalesOrderItemAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('sales_order', 'item', 'item_type', 'quantity', 'unit_price', 'discount', 'total_price', 'tenant_id')
    list_filter = ('item_type', 'sales_order__status', 'sales_order__service_type')
    search_fields = ('sales_order__order_number', 'item__name', 'item__sku', 'operation_description')
    readonly_fields = ('created_at', 'updated_at', 'total_price')
    raw_id_fields = ('sales_order', 'item')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'sales_order', 'item', 'item_type', 'quantity')
        }),
        (_('Work Order Integration'), {
            'fields': ('work_order_operation_id', 'work_order_material_id', 'operation_duration', 'operation_description')
        }),
        (_('Pricing'), {
            'fields': ('unit_price', 'discount', 'total_price')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_price(self, obj):
        return obj.total_price
    total_price.short_description = _("Total Price")


@admin.register(SalesReturn)
class SalesReturnAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('return_number', 'sales_order', 'return_date', 'tenant_id')
    list_filter = ('return_date',)
    search_fields = ('return_number', 'sales_order__order_number', 'reason', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('sales_order',)
    inlines = [SalesReturnItemInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'return_number', 'sales_order', 'return_date')
        }),
        (_('Additional Information'), {
            'fields': ('reason', 'notes')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SalesReturnItem)
class SalesReturnItemAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('sales_return', 'sales_order_item', 'quantity', 'tenant_id')
    search_fields = ('sales_return__return_number', 'sales_order_item__item__name')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('sales_return', 'sales_order_item')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'sales_return', 'sales_order_item', 'quantity')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
