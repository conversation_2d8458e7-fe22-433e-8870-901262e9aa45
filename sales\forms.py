from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date

# Import models
from .models import SalesOrder, SalesOrderItem, SalesReturn, SalesReturnItem
from setup.models import Customer as SetupCustomer
from inventory.models import Item


class SalesOrderForm(forms.ModelForm):
    """Form for creating and updating sales orders"""
    
    class Meta:
        model = SalesOrder
        fields = [
            'customer', 'order_date', 'shipping_address', 'total_amount', 'status', 'notes'
        ]
        widgets = {
            'customer': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'data-search': 'true'
            }),
            'order_date': forms.DateInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'type': 'date'
            }),
            'shipping_address': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter shipping address...')
            }),
            'total_amount': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0',
                'readonly': 'readonly'
            }),
            'status': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter notes...')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            # Filter customers based on user's tenant
            self.fields['customer'].queryset = SetupCustomer.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('first_name', 'last_name')
        
        # Set default values
        if not self.instance.pk:
            self.fields['order_date'].initial = date.today()
        
        # Make some fields optional
        self.fields['notes'].required = False
        self.fields['shipping_address'].required = False
        self.fields['total_amount'].required = False


class SalesOrderItemForm(forms.ModelForm):
    """Form for adding items to a sales order"""
    
    class Meta:
        model = SalesOrderItem
        fields = ['sales_order', 'item', 'quantity', 'unit_price', 'discount']
        widgets = {
            'sales_order': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'item': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'data-search': 'true'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01',
                'placeholder': _('Enter quantity...')
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0',
                'placeholder': _('Enter unit price...')
            }),
            'discount': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0',
                'placeholder': _('Enter discount...')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            # Filter items and sales orders based on user's tenant
            self.fields['item'].queryset = Item.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')
            
            self.fields['sales_order'].queryset = SalesOrder.objects.filter(
                tenant_id=tenant_id
            ).order_by('-created_at')
        
        # Make discount optional
        self.fields['discount'].required = False
    
    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity <= 0:
            raise ValidationError(_('Quantity must be greater than zero'))
        return quantity
    
    def clean_unit_price(self):
        unit_price = self.cleaned_data.get('unit_price')
        if unit_price < 0:
            raise ValidationError(_('Unit price cannot be negative'))
        return unit_price


class SalesReturnForm(forms.ModelForm):
    """Form for creating and updating sales returns"""
    
    class Meta:
        model = SalesReturn
        fields = [
            'sales_order', 'return_date', 'reason', 'notes'
        ]
        widgets = {
            'sales_order': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'return_date': forms.DateInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'type': 'date'
            }),
            'reason': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter return reason...')
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter notes...')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            # Filter sales orders based on user's tenant
            self.fields['sales_order'].queryset = SalesOrder.objects.filter(
                tenant_id=tenant_id,
                status__in=['delivered', 'shipped']  # Only allow returns for delivered/shipped orders
            ).order_by('-created_at')
        
        # Set default values
        if not self.instance.pk:
            self.fields['return_date'].initial = date.today()
        
        # Make notes optional
        self.fields['notes'].required = False


class SalesReturnItemForm(forms.ModelForm):
    """Form for adding items to a sales return"""
    
    class Meta:
        model = SalesReturnItem
        fields = ['sales_return', 'sales_order_item', 'quantity']
        widgets = {
            'sales_return': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'sales_order_item': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01',
                'placeholder': _('Enter return quantity...')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        sales_return = kwargs.pop('sales_return', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            self.fields['sales_return'].queryset = SalesReturn.objects.filter(
                tenant_id=tenant_id
            ).order_by('-created_at')
        
        if sales_return:
            # Filter sales order items to only those from the related sales order
            self.fields['sales_order_item'].queryset = SalesOrderItem.objects.filter(
                sales_order=sales_return.sales_order,
                tenant_id=tenant_id if user else None
            )
    
    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        sales_order_item = self.cleaned_data.get('sales_order_item')
        
        if quantity <= 0:
            raise ValidationError(_('Return quantity must be greater than zero'))
        
        if sales_order_item and quantity > sales_order_item.quantity:
            raise ValidationError(_('Return quantity cannot exceed original order quantity'))
        
        return quantity


class SalesReportForm(forms.Form):
    """Form for generating sales reports"""
    
    REPORT_TYPE_CHOICES = [
        ('summary', _('Summary Report')),
        ('detailed', _('Detailed Report')),
        ('customer', _('Customer Report')),
        ('item', _('Item Report')),
    ]
    
    PERIOD_CHOICES = [
        ('daily', _('Daily')),
        ('weekly', _('Weekly')),
        ('monthly', _('Monthly')),
        ('yearly', _('Yearly')),
        ('custom', _('Custom Period')),
    ]
    
    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        label=_('Report Type'),
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    period = forms.ChoiceField(
        choices=PERIOD_CHOICES,
        label=_('Period'),
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    start_date = forms.DateField(
        required=False,
        label=_('Start Date'),
        widget=forms.DateInput(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'type': 'date'
        })
    )
    
    end_date = forms.DateField(
        required=False,
        label=_('End Date'),
        widget=forms.DateInput(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'type': 'date'
        })
    )
    
    customer = forms.ModelChoiceField(
        queryset=SetupCustomer.objects.none(),
        required=False,
        label=_('Customer'),
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            self.fields['customer'].queryset = SetupCustomer.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('first_name', 'last_name')
    
    def clean(self):
        cleaned_data = super().clean()
        period = cleaned_data.get('period')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if period == 'custom':
            if not start_date or not end_date:
                raise ValidationError(_('Start date and end date are required for custom period'))
            
            if start_date >= end_date:
                raise ValidationError(_('End date must be after start date'))
        
        return cleaned_data


class QuickSalesOrderForm(forms.Form):
    """Form for quick sales order creation"""
    
    customer = forms.ModelChoiceField(
        queryset=SetupCustomer.objects.none(),
        label=_('Customer'),
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'data-search': 'true'
        })
    )
    
    item = forms.ModelChoiceField(
        queryset=Item.objects.none(),
        label=_('Item'),
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'data-search': 'true'
        })
    )
    
    quantity = forms.DecimalField(
        label=_('Quantity'),
        min_value=0.01,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'step': '0.01',
            'placeholder': _('Enter quantity...')
        })
    )
    
    unit_price = forms.DecimalField(
        label=_('Unit Price'),
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'step': '0.01',
            'placeholder': _('Enter unit price...')
        })
    )
    
    discount = forms.DecimalField(
        required=False,
        label=_('Discount'),
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'step': '0.01',
            'placeholder': _('Enter discount...')
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            self.fields['customer'].queryset = SetupCustomer.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('first_name', 'last_name')
            
            self.fields['item'].queryset = Item.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')
    
    def clean(self):
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity')
        unit_price = cleaned_data.get('unit_price')
        discount = cleaned_data.get('discount', 0)
        
        if quantity and unit_price:
            total_amount = (quantity * unit_price) - (discount or 0)
            if total_amount < 0:
                raise ValidationError(_('Total amount cannot be negative'))
        
        return cleaned_data 