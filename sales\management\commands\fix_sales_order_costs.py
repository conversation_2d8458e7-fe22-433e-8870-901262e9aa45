"""
Management command to fix sales order item types and recalculate costs
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from decimal import Decimal

from sales.models import SalesOrder, SalesOrderItem


class Command(BaseCommand):
    help = 'Fix sales order item types and recalculate labor/parts costs'

    def handle(self, *args, **options):
        # Get all sales orders with work orders
        sales_orders = SalesOrder.objects.filter(work_order__isnull=False)
        
        self.stdout.write(f'Found {sales_orders.count()} sales orders with work orders')
        
        updated_count = 0
        
        for sales_order in sales_orders:
            try:
                with transaction.atomic():
                    # Update item types based on item names
                    for item in sales_order.items.all():
                        old_type = item.item_type
                        
                        # Determine item type based on item name
                        if ('labor' in item.item.name.lower() or 
                            'service' in item.item.name.lower() or
                            'hour' in item.item.name.lower() or
                            'work' in item.item.name.lower()):
                            item.item_type = 'labor'
                        elif ('part' in item.item.name.lower() or
                              'spare' in item.item.name.lower() or
                              'component' in item.item.name.lower() or
                              'filter' in item.item.name.lower() or
                              'oil' in item.item.name.lower() or
                              'battery' in item.item.name.lower() or
                              'tire' in item.item.name.lower() or
                              'brake' in item.item.name.lower()):
                            item.item_type = 'parts'
                        else:
                            # Default to parts for physical items
                            item.item_type = 'parts'
                        
                        # Set operation description if empty
                        if not item.operation_description:
                            if item.item_type == 'labor':
                                item.operation_description = f'Labor: {item.item.name}'
                            else:
                                item.operation_description = f'Parts: {item.item.name}'
                        
                        item.save()
                        
                        if old_type != item.item_type:
                            self.stdout.write(f'Updated {item.item.name}: {old_type} -> {item.item_type}')
                    
                    # Recalculate costs using the model's update_total_amount method
                    sales_order.update_total_amount()
                    
                    self.stdout.write(
                        f'Updated {sales_order.order_number}: '
                        f'Labor: {sales_order.labor_cost}, Parts: {sales_order.parts_cost}, '
                        f'Total: {sales_order.total_amount}'
                    )
                    
                    updated_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error updating {sales_order.order_number}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully updated {updated_count} sales orders')
        )
        
        # Show summary
        total_labor_revenue = sum(so.labor_cost for so in sales_orders)
        total_parts_revenue = sum(so.parts_cost for so in sales_orders)
        total_revenue = sum(so.total_amount for so in sales_orders)
        
        self.stdout.write('\n=== Summary ===')
        self.stdout.write(f'Total Labor Revenue: {total_labor_revenue} EGP')
        self.stdout.write(f'Total Parts Revenue: {total_parts_revenue} EGP')
        self.stdout.write(f'Total Work Order Revenue: {total_revenue} EGP') 