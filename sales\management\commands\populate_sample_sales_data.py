"""
Management command to populate sample sales data from work orders
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from decimal import Decimal
import random

from sales.models import SalesOrder, SalesOrderItem
from work_orders.models import WorkOrder
from setup.models import Customer, ServiceCenter, Vehicle
from inventory.models import Item
from work_orders.utils import auto_complete_work_order


class Command(BaseCommand):
    help = 'Populate sample sales data from completed work orders'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-samples',
            action='store_true',
            help='Create sample work orders if none exist',
        )

    def handle(self, *args, **options):
        create_samples = options.get('create_samples', False)
        
        # Check current state
        sales_count = SalesOrder.objects.count()
        work_orders_count = WorkOrder.objects.count()
        completed_wo_count = WorkOrder.objects.filter(status='completed').count()
        
        self.stdout.write(f'Current state:')
        self.stdout.write(f'  Sales Orders: {sales_count}')
        self.stdout.write(f'  Work Orders: {work_orders_count}')
        self.stdout.write(f'  Completed Work Orders: {completed_wo_count}')
        
        # Show existing sales orders
        if sales_count > 0:
            self.stdout.write('\nExisting Sales Orders:')
            for so in SalesOrder.objects.all()[:5]:
                self.stdout.write(
                    f'  {so.order_number} - {so.customer} - WO: {so.work_order_number or "None"}'
                )
        
        # Show work orders
        if work_orders_count > 0:
            self.stdout.write('\nExisting Work Orders:')
            for wo in WorkOrder.objects.all()[:5]:
                self.stdout.write(
                    f'  {wo.work_order_number} - {wo.customer} - Status: {wo.status}'
                )
        
        # Check for completed work orders without sales orders
        completed_work_orders = WorkOrder.objects.filter(status='completed')
        work_orders_needing_sales = []
        
        for wo in completed_work_orders:
            # Check if sales order exists
            existing_sales = SalesOrder.objects.filter(
                work_order=wo
            ).first()
            
            if not existing_sales:
                existing_sales = SalesOrder.objects.filter(
                    work_order_number=wo.work_order_number
                ).first()
            
            if not existing_sales:
                work_orders_needing_sales.append(wo)
        
        self.stdout.write(f'\nWork orders without sales orders: {len(work_orders_needing_sales)}')
        
        # Generate sales orders for completed work orders
        created_count = 0
        for wo in work_orders_needing_sales:
            try:
                self.stdout.write(f'Creating sales order for work order {wo.work_order_number}')
                
                # Use the existing auto-completion system
                from work_orders.utils import generate_sales_order_for_work_order
                result = generate_sales_order_for_work_order(wo)
                
                if result['created']:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'Created sales order {result["sales_order"].order_number}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Sales order already exists: {result["message"]}')
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating sales order for {wo.work_order_number}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'Created {created_count} new sales orders from work orders')
        )
        
        # If requested, create sample data
        if create_samples and work_orders_count == 0:
            self.stdout.write('\nCreating sample work orders and sales orders...')
            self.create_sample_data()
        
        # Final summary
        final_sales_count = SalesOrder.objects.count()
        sales_with_wo = SalesOrder.objects.filter(work_order__isnull=False).count()
        
        self.stdout.write('\n=== Final Summary ===')
        self.stdout.write(f'Total Sales Orders: {final_sales_count}')
        self.stdout.write(f'Sales Orders with Work Order links: {sales_with_wo}')
        
        # Show work order continuation data
        for so in SalesOrder.objects.filter(work_order__isnull=False)[:5]:
            self.stdout.write(f'  {so.order_number} -> WO: {so.work_order_number}')
            self.stdout.write(f'    Service Type: {so.service_type}')
            self.stdout.write(f'    Vehicle: {so.vehicle}')
            self.stdout.write(f'    Service Center: {so.service_center}')
            self.stdout.write(f'    Labor Cost: {so.labor_cost}, Parts Cost: {so.parts_cost}')

    def create_sample_data(self):
        """Create sample work orders and convert them to sales orders"""
        # This would create sample data if needed
        # For now, just inform the user
        self.stdout.write(
            self.style.WARNING('Sample data creation not implemented. Please complete some work orders first.')
        ) 