"""
Management command to update existing sales orders with work order data
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from decimal import Decimal
import re

from sales.models import SalesOrder, SalesOrderItem
from work_orders.models import WorkOrder


class Command(BaseCommand):
    help = 'Update existing sales orders with work order continuation data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant_id',
            type=str,
            help='Update only sales orders for specific tenant ID',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        tenant_id = options.get('tenant_id')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Get sales orders that likely came from work orders
        sales_orders_query = SalesOrder.objects.filter(
            notes__icontains='Work Order:'
        )
        
        if tenant_id:
            sales_orders_query = sales_orders_query.filter(tenant_id=tenant_id)
        
        sales_orders = sales_orders_query.select_related('customer')
        
        self.stdout.write(f'Found {sales_orders.count()} sales orders with work order references')
        
        updated_count = 0
        error_count = 0
        
        for sales_order in sales_orders:
            try:
                # Extract work order number from notes
                work_order_number = self.extract_work_order_number(sales_order.notes)
                
                if work_order_number:
                    # Find the corresponding work order
                    work_order = WorkOrder.objects.filter(
                        tenant_id=sales_order.tenant_id,
                        work_order_number=work_order_number
                    ).first()
                    
                    if work_order:
                        self.stdout.write(f'Updating sales order {sales_order.order_number} with work order {work_order_number}')
                        
                        if not dry_run:
                            self.update_sales_order_with_work_order_data(sales_order, work_order)
                            self.update_sales_order_items_with_work_order_data(sales_order, work_order)
                        
                        updated_count += 1
                    else:
                        self.stdout.write(
                            self.style.WARNING(f'Work order {work_order_number} not found for sales order {sales_order.order_number}')
                        )
                        error_count += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Could not extract work order number from sales order {sales_order.order_number}')
                    )
                    error_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error updating sales order {sales_order.order_number}: {str(e)}')
                )
                error_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'Updated {updated_count} sales orders')
        )
        if error_count > 0:
            self.stdout.write(
                self.style.WARNING(f'{error_count} sales orders had errors')
            )

    def extract_work_order_number(self, notes):
        """Extract work order number from notes"""
        patterns = [
            r'Work Order:\s*([A-Z0-9\-]+)',
            r'WO:\s*([A-Z0-9\-]+)',
            r'work order\s*([A-Z0-9\-]+)',
            r'WO-(\d{8}\d{3})',  # WO-YYYYMMDDXXX format
        ]
        
        for pattern in patterns:
            match = re.search(pattern, notes, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None

    @transaction.atomic
    def update_sales_order_with_work_order_data(self, sales_order, work_order):
        """Update sales order with work order data"""
        
        # Determine service type
        service_type = 'custom'
        if hasattr(work_order, 'operation_category') and work_order.operation_category == 'scheduled':
            service_type = 'maintenance'
        elif hasattr(work_order, 'work_order_type') and work_order.work_order_type:
            if 'maintenance' in work_order.work_order_type.name.lower():
                service_type = 'maintenance'
            elif 'repair' in work_order.work_order_type.name.lower():
                service_type = 'repair'
            elif 'inspection' in work_order.work_order_type.name.lower():
                service_type = 'inspection'
        
        # Update sales order fields
        sales_order.work_order = work_order
        sales_order.work_order_number = work_order.work_order_number
        sales_order.service_center = work_order.service_center
        sales_order.vehicle = work_order.vehicle
        sales_order.service_type = service_type
        sales_order.service_completion_date = work_order.actual_end_date or work_order.updated_at
        sales_order.technician = work_order.assigned_technician
        
        if hasattr(work_order, 'current_odometer'):
            sales_order.vehicle_odometer = work_order.current_odometer
        
        sales_order.vehicle_condition_notes = work_order.notes or ''
        
        # Update notes to include more detailed information
        sales_order.notes = (
            f'Auto-generated from completed Work Order: {work_order.work_order_number}. '
            f'Service performed at {work_order.service_center.name if work_order.service_center else "Service Center"}. '
            f'Vehicle: {work_order.vehicle.license_plate if work_order.vehicle else "N/A"}. '
            f'Description: {work_order.description}'
        )
        
        sales_order.save()

    @transaction.atomic
    def update_sales_order_items_with_work_order_data(self, sales_order, work_order):
        """Update sales order items with work order operation/material references"""
        
        # Get work order operations and materials
        operations = work_order.operations.all()
        materials = work_order.materials.all()
        
        # Get sales order items
        sales_items = sales_order.items.all()
        
        # Update items based on their characteristics
        for item in sales_items:
            updated = False
            
            # Check if this is a labor item (likely from operations)
            if 'labor' in item.item.name.lower() or 'service' in item.item.name.lower():
                # Try to match with an operation
                for operation in operations:
                    if item.quantity == operation.duration_minutes:
                        item.item_type = 'labor'
                        item.work_order_operation_id = operation.id
                        item.operation_duration = operation.duration_minutes
                        item.operation_description = f'{operation.name}: {operation.description or ""}'
                        updated = True
                        break
                
                if not updated:
                    # Default labor item
                    item.item_type = 'labor'
                    item.operation_description = f'Labor: {item.item.name}'
                    updated = True
            
            else:
                # Check if this is a parts/material item
                for material in materials:
                    if (item.item == material.item and 
                        item.quantity == material.quantity):
                        item.item_type = 'parts'
                        item.work_order_material_id = material.id
                        item.operation_description = f'Material: {material.item.name} - {material.notes or ""}'
                        updated = True
                        break
                
                if not updated:
                    # Default parts item
                    item.item_type = 'parts'
                    item.operation_description = f'Parts: {item.item.name}'
                    updated = True
            
            if updated:
                item.save()
        
        # Recalculate labor and parts costs
        sales_order.update_total_amount() 