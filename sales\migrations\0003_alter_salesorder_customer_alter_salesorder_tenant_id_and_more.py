# Generated by Django 4.2.20 on 2025-06-15 14:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0015_company_tenant_id_franchise_tenant_id_and_more"),
        ("sales", "0002_alter_customer_tenant_id_alter_salesorder_tenant_id_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="salesorder",
            name="customer",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="sales_orders",
                to="setup.customer",
                verbose_name="Customer",
            ),
        ),
        migrations.AlterField(
            model_name="salesorder",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="salesorderitem",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="salesreturn",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="salesreturnitem",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.DeleteModel(
            name="Customer",
        ),
    ]
