# Generated by Django 4.2.20 on 2025-06-16 10:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("work_orders", "0010_alter_workorder_status"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("setup", "0016_userrole_userprofile_technicianspecialization_and_more"),
        ("sales", "0003_alter_salesorder_customer_alter_salesorder_tenant_id_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="salesorder",
            name="labor_cost",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total cost for labor/operations",
                max_digits=10,
                verbose_name="Labor Cost",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="parts_cost",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Total cost for parts/materials",
                max_digits=10,
                verbose_name="Parts Cost",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="service_center",
            field=models.ForeignKey(
                blank=True,
                help_text="Service center where work was performed",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="sales_orders",
                to="setup.servicecenter",
                verbose_name="Service Center",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="service_completion_date",
            field=models.DateTimeField(
                blank=True,
                help_text="When the service work was completed",
                null=True,
                verbose_name="Service Completion Date",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="service_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("maintenance", "Maintenance"),
                    ("repair", "Repair"),
                    ("custom", "Custom Service"),
                    ("inspection", "Inspection"),
                    ("parts_only", "Parts Only"),
                ],
                help_text="Type of service provided",
                max_length=50,
                verbose_name="Service Type",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="technician",
            field=models.ForeignKey(
                blank=True,
                help_text="Technician who performed the work",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="technician_sales_orders",
                to=settings.AUTH_USER_MODEL,
                verbose_name="Technician",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="vehicle",
            field=models.ForeignKey(
                blank=True,
                help_text="Vehicle that received service",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="sales_orders",
                to="setup.vehicle",
                verbose_name="Vehicle",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="vehicle_condition_notes",
            field=models.TextField(
                blank=True,
                help_text="Notes about vehicle condition during service",
                verbose_name="Vehicle Condition Notes",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="vehicle_odometer",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Vehicle odometer reading when service was performed",
                null=True,
                verbose_name="Vehicle Odometer (km)",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="work_order",
            field=models.ForeignKey(
                blank=True,
                help_text="The work order this sales order was generated from",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="sales_orders",
                to="work_orders.workorder",
                verbose_name="Work Order",
            ),
        ),
        migrations.AddField(
            model_name="salesorder",
            name="work_order_number",
            field=models.CharField(
                blank=True,
                help_text="Reference to original work order number",
                max_length=50,
                verbose_name="Work Order Number",
            ),
        ),
        migrations.AddField(
            model_name="salesorderitem",
            name="item_type",
            field=models.CharField(
                choices=[
                    ("labor", "Labor/Service"),
                    ("parts", "Parts/Material"),
                    ("fee", "Service Fee"),
                    ("other", "Other"),
                ],
                default="parts",
                help_text="Type of item - labor or parts",
                max_length=20,
                verbose_name="Item Type",
            ),
        ),
        migrations.AddField(
            model_name="salesorderitem",
            name="operation_description",
            field=models.TextField(
                blank=True,
                help_text="Detailed description of work performed",
                verbose_name="Operation Description",
            ),
        ),
        migrations.AddField(
            model_name="salesorderitem",
            name="operation_duration",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Duration of operation for labor items",
                null=True,
                verbose_name="Operation Duration (minutes)",
            ),
        ),
        migrations.AddField(
            model_name="salesorderitem",
            name="work_order_material_id",
            field=models.UUIDField(
                blank=True,
                help_text="Reference to the work order material this item represents",
                null=True,
                verbose_name="Work Order Material ID",
            ),
        ),
        migrations.AddField(
            model_name="salesorderitem",
            name="work_order_operation_id",
            field=models.UUIDField(
                blank=True,
                help_text="Reference to the work order operation this item represents",
                null=True,
                verbose_name="Work Order Operation ID",
            ),
        ),
    ]
