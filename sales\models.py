from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet
from inventory.models import Item



class SalesOrder(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Sales order model - enhanced to continue work order data
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('confirmed', _('Confirmed')),
        ('shipped', _('Shipped')),
        ('delivered', _('Delivered')),
        ('cancelled', _('Cancelled')),
        ('returned', _('Returned')),
    )
    
    order_number = models.CharField(_("Order Number"), max_length=50, unique=True)
    customer = models.ForeignKey(
        'setup.Customer', 
        on_delete=models.PROTECT, 
        related_name='sales_orders',
        verbose_name=_("Customer")
    )
    order_date = models.DateField(_("Order Date"))
    status = models.Char<PERSON><PERSON>(_("Status"), max_length=20, choices=STATUS_CHOICES, default='draft')
    shipping_address = models.TextField(_("Shipping Address"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    total_amount = models.DecimalField(_("Total Amount"), max_digits=10, decimal_places=2, default=0)
    
    # Work Order Integration Fields
    work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        related_name='sales_orders',
        verbose_name=_("Work Order"),
        null=True, blank=True,
        help_text=_("The work order this sales order was generated from")
    )
    
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.SET_NULL,
        related_name='sales_orders',
        verbose_name=_("Service Center"),
        null=True, blank=True,
        help_text=_("Service center where work was performed")
    )
    
    vehicle = models.ForeignKey(
        'setup.Vehicle',
        on_delete=models.SET_NULL,
        related_name='sales_orders',
        verbose_name=_("Vehicle"),
        null=True, blank=True,
        help_text=_("Vehicle that received service")
    )
    
    # Service Information
    service_type = models.CharField(
        _("Service Type"), 
        max_length=50, 
        blank=True,
        choices=[
            ('maintenance', _('Maintenance')),
            ('repair', _('Repair')),
            ('custom', _('Custom Service')),
            ('inspection', _('Inspection')),
            ('parts_only', _('Parts Only')),
        ],
        help_text=_("Type of service provided")
    )
    
    work_order_number = models.CharField(
        _("Work Order Number"), 
        max_length=50, 
        blank=True,
        help_text=_("Reference to original work order number")
    )
    
    service_completion_date = models.DateTimeField(
        _("Service Completion Date"),
        null=True, blank=True,
        help_text=_("When the service work was completed")
    )
    
    technician = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        related_name='technician_sales_orders',
        verbose_name=_("Technician"),
        null=True, blank=True,
        help_text=_("Technician who performed the work")
    )
    
    # Vehicle Information at time of service
    vehicle_odometer = models.PositiveIntegerField(
        _("Vehicle Odometer (km)"), 
        null=True, blank=True,
        help_text=_("Vehicle odometer reading when service was performed")
    )
    
    vehicle_condition_notes = models.TextField(
        _("Vehicle Condition Notes"), 
        blank=True,
        help_text=_("Notes about vehicle condition during service")
    )
    
    # Financial tracking
    labor_cost = models.DecimalField(
        _("Labor Cost"), 
        max_digits=10, 
        decimal_places=2, 
        default=0,
        help_text=_("Total cost for labor/operations")
    )
    
    parts_cost = models.DecimalField(
        _("Parts Cost"), 
        max_digits=10, 
        decimal_places=2, 
        default=0,
        help_text=_("Total cost for parts/materials")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Sales Order")
        verbose_name_plural = _("Sales Orders")
        ordering = ['-order_date']
        
    def __str__(self):
        customer_name = self.customer.company_name or f"{self.customer.first_name} {self.customer.last_name}".strip()
        work_ref = f" (WO: {self.work_order_number})" if self.work_order_number else ""
        return f"{self.order_number} - {customer_name}{work_ref}"
    
    def update_total_amount(self):
        """
        Update the total amount based on order items
        """
        self.total_amount = sum(item.total_price for item in self.items.all())
        
        # Update labor and parts costs
        labor_total = sum(item.total_price for item in self.items.filter(item_type='labor'))
        parts_total = sum(item.total_price for item in self.items.filter(item_type='parts'))
        
        self.labor_cost = labor_total
        self.parts_cost = parts_total
        
        self.save(update_fields=['total_amount', 'labor_cost', 'parts_cost', 'updated_at'])


class SalesOrderItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Sales order item model - enhanced with work order integration
    """
    ITEM_TYPE_CHOICES = [
        ('labor', _('Labor/Service')),
        ('parts', _('Parts/Material')),
        ('fee', _('Service Fee')),
        ('other', _('Other')),
    ]
    
    sales_order = models.ForeignKey(
        SalesOrder, 
        on_delete=models.CASCADE, 
        related_name='items',
        verbose_name=_("Sales Order")
    )
    item = models.ForeignKey(
        Item, 
        on_delete=models.PROTECT, 
        related_name='sales_items',
        verbose_name=_("Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    unit_price = models.DecimalField(_("Unit Price"), max_digits=10, decimal_places=2)
    discount = models.DecimalField(_("Discount"), max_digits=10, decimal_places=2, default=0)
    
    # Work Order Integration
    item_type = models.CharField(
        _("Item Type"), 
        max_length=20, 
        choices=ITEM_TYPE_CHOICES, 
        default='parts',
        help_text=_("Type of item - labor or parts")
    )
    
    work_order_operation_id = models.UUIDField(
        _("Work Order Operation ID"), 
        null=True, blank=True,
        help_text=_("Reference to the work order operation this item represents")
    )
    
    work_order_material_id = models.UUIDField(
        _("Work Order Material ID"), 
        null=True, blank=True,
        help_text=_("Reference to the work order material this item represents")
    )
    
    operation_duration = models.PositiveIntegerField(
        _("Operation Duration (minutes)"), 
        null=True, blank=True,
        help_text=_("Duration of operation for labor items")
    )
    
    operation_description = models.TextField(
        _("Operation Description"), 
        blank=True,
        help_text=_("Detailed description of work performed")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Sales Order Item")
        verbose_name_plural = _("Sales Order Items")
        unique_together = [['sales_order', 'item']]
        
    def __str__(self):
        type_indicator = f"[{self.get_item_type_display()}]" if self.item_type else ""
        return f"{self.quantity} x {self.item.name} {type_indicator} in {self.sales_order.order_number}"
    
    @property
    def total_price(self):
        """
        Calculate total price for this line item
        """
        return (self.quantity * self.unit_price) - self.discount
        
    def save(self, *args, **kwargs):
        """
        Override save method to update sales order total
        """
        super().save(*args, **kwargs)
        self.sales_order.update_total_amount()


class SalesReturn(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Sales return model
    """
    return_number = models.CharField(_("Return Number"), max_length=50, unique=True)
    sales_order = models.ForeignKey(
        SalesOrder, 
        on_delete=models.PROTECT, 
        related_name='returns',
        verbose_name=_("Sales Order")
    )
    return_date = models.DateField(_("Return Date"))
    reason = models.TextField(_("Reason"))
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Sales Return")
        verbose_name_plural = _("Sales Returns")
        ordering = ['-return_date']
        
    def __str__(self):
        return f"{self.return_number} - {self.sales_order.order_number}"


class SalesReturnItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Sales return item model
    """
    sales_return = models.ForeignKey(
        SalesReturn, 
        on_delete=models.CASCADE, 
        related_name='items',
        verbose_name=_("Sales Return")
    )
    sales_order_item = models.ForeignKey(
        SalesOrderItem, 
        on_delete=models.PROTECT, 
        related_name='returns',
        verbose_name=_("Sales Order Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Sales Return Item")
        verbose_name_plural = _("Sales Return Items")
        
    def __str__(self):
        return f"{self.quantity} x {self.sales_order_item.item.name} in {self.sales_return.return_number}"
