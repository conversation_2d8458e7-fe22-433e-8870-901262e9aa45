import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import SalesOrder
from work_orders.services import WorkOrderToSalesService

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=SalesOrder)
def sales_order_pre_save(sender, instance, **kwargs):
    """Signal to handle pre-save actions for sales orders"""
    # Check if this is an existing instance
    if instance.pk:
        try:
            # Get the original instance from database
            original = SalesOrder.objects.get(pk=instance.pk)
            
            # Store original status for comparison
            instance._original_status = original.status
            
        except SalesOrder.DoesNotExist:
            # If it's a new instance, initialize attributes
            instance._original_status = None
    else:
        # If it's a new instance, initialize attributes
        instance._original_status = None

@receiver(post_save, sender=SalesOrder)
def sales_order_post_save(sender, instance, created, **kwargs):
    """Signal to handle post-save actions for sales orders"""
    
    # Auto-create invoice when sales order is created or confirmed
    should_create_invoice = False
    
    if created and instance.status in ['confirmed', 'shipped', 'delivered']:
        # New sales order in a status that requires invoice
        should_create_invoice = True
        logger.info(f"New sales order {instance.order_number} created with status {instance.status}, creating invoice...")
    elif not created and hasattr(instance, '_original_status'):
        # Existing sales order status changed to confirmed/shipped/delivered
        if (instance._original_status not in ['confirmed', 'shipped', 'delivered'] and 
            instance.status in ['confirmed', 'shipped', 'delivered']):
            should_create_invoice = True
            logger.info(f"Sales order {instance.order_number} status changed to {instance.status}, creating invoice...")
    
    if should_create_invoice:
        # Check if auto-creation is enabled
        from django.conf import settings
        if not getattr(settings, 'SALES_ORDER_AUTO_CREATE_INVOICE', True):
            logger.info(f"Auto-invoice creation disabled for sales order {instance.order_number}")
            return
            
        try:
            # Create invoice from sales order
            invoice = WorkOrderToSalesService.create_invoice_from_sales_order(instance)
            
            if invoice:
                logger.info(f"Successfully created invoice {invoice.invoice_number} from sales order {instance.order_number}")
            else:
                logger.error(f"Failed to create invoice from sales order {instance.order_number}")
                
        except Exception as e:
            logger.error(f"Unexpected error creating invoice from sales order {instance.order_number}: {str(e)}") 