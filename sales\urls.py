from django.urls import path
from . import views
from setup.views import CustomerCreateView

app_name = 'sales'

urlpatterns = [
    # Dashboard
    path('', views.sales_dashboard, name='dashboard'),
    
    # Sales Management
    path('sales/', views.SaleListView.as_view(), name='sale_list'),
    path('sales/<uuid:pk>/', views.SaleDetailView.as_view(), name='sale_detail'),
    path('sales/create/', views.SaleCreateView.as_view(), name='sale_create'),
    path('sales/<uuid:pk>/edit/', views.SaleUpdateView.as_view(), name='sale_edit'),
    
    # Sales Order URLs (aliases for backward compatibility)
    path('orders/', views.SaleListView.as_view(), name='sales_order_list'),
    path('orders/<uuid:pk>/', views.SaleDetailView.as_view(), name='sales_order_detail'),
    path('orders/create/', views.SaleCreateView.as_view(), name='sales_order_create'),
    path('orders/<uuid:pk>/edit/', views.SaleUpdateView.as_view(), name='sales_order_update'),
    path('orders/<uuid:pk>/preview/', views.sales_order_preview, name='sales_order_preview'),
    path('orders/<uuid:pk>/edit-form/', views.sales_order_edit, name='sales_order_edit'),
    
    # Customer Management
    path('customers/create/', CustomerCreateView.as_view(), name='customer_create'),
    
    # Quotation Management
    path('quotations/', views.QuotationListView.as_view(), name='quotation_list'),
    path('quotations/create/', views.QuotationCreateView.as_view(), name='quotation_create'),
    
    # Invoice Management
    path('invoices/', views.InvoiceListView.as_view(), name='invoice_list'),
    path('invoices/create/', views.InvoiceCreateView.as_view(), name='invoice_create'),
    
    # NEW: Finished Invoices & Reports
    path('finished-invoices/', views.finished_invoices_list, name='finished_invoices'),
    path('operations-parts-report/', views.operations_parts_report, name='operations_parts_report'),
    path('price-configuration/', views.price_configuration, name='price_configuration'),
    
    # Sub-pages
    path('insurance/', views.insurance_page, name='insurance'),
    path('payments/', views.payments_page, name='payments'),
    path('customer-management/', views.customer_management_page, name='customer_management'),
    path('offers/', views.offers_page, name='offers'),
    path('recent-orders/', views.recent_orders_page, name='recent_orders'),
    
    # API Endpoints
    path('api/add-item/', views.api_add_sale_item, name='api_add_sale_item'),
    path('api/remove-item/', views.api_remove_sale_item, name='api_remove_sale_item'),
    path('api/sales-report/', views.api_sales_report, name='api_sales_report'),
    path('api/operations-parts-summary/', views.api_operations_parts_summary, name='api_operations_parts_summary'),
    path('api/price-list-export/', views.api_price_list_export, name='api_price_list_export'),
    path('api/convert-quotation-to-sale/', views.api_convert_quotation_to_sale, name='api_convert_quotation_to_sale'),
    path('api/work-order-details/<int:work_order_id>/', views.api_work_order_details, name='api_work_order_details'),
    path('api/create-sales-order-from-work-order/', views.api_create_sales_order_from_work_order, name='api_create_sales_order_from_work_order'),
    path('api/get-ready-sales-orders/', views.api_get_ready_sales_orders, name='api_get_ready_sales_orders'),
    
    # New API endpoints for dashboard card details
    path('api/total-orders-details/', views.api_total_orders_details, name='api_total_orders_details'),
    path('api/pending-orders-details/', views.api_pending_orders_details, name='api_pending_orders_details'),
    path('api/completed-orders-details/', views.api_completed_orders_details, name='api_completed_orders_details'),
    path('api/customers-details/', views.api_customers_details, name='api_customers_details'),
    path('api/sales-performance-details/', views.api_sales_performance_details, name='api_sales_performance_details'),
    path('api/work-order-sales-details/', views.api_work_order_sales_details, name='api_work_order_sales_details'),
    
    # Test API
    path('api/test/', views.api_test, name='api_test'),
] 