from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.contrib import messages
from django.utils.html import format_html
from django.urls import reverse
from django.shortcuts import redirect
from core.admin import TenantAdminMixin
from django.urls import path
from django.shortcuts import get_object_or_404
from django.contrib.admin.views.main import ChangeList
from django.http import HttpResponse, JsonResponse
from django.db.models import Q

from .models import (
    ServiceLevel,
    Franchise,
    Company,
    ServiceCenterType,
    ServiceCenter,
    Vehicle,
    ServiceHistory,
    Customer,
    ServiceCenterMakeModel,
    VehicleOwnershipTransfer,
    VehicleMake,
    VehicleModel,
    UserProfile,
    UserRole,
    TechnicianProfile,
    TechnicianSpecialization,
    UserWarehouseAssignment
)

class HierarchicalContextMixin:
    """Mixin to provide hierarchical context awareness for admin views"""
    
    def get_context_level(self, request):
        """Determine the context level from request parameters"""
        if hasattr(request, 'franchise_context') and request.franchise_context:
            return 'franchise', request.franchise_context
        elif hasattr(request, 'company_context') and request.company_context:
            return 'company', request.company_context
        elif hasattr(request, 'service_center_context') and request.service_center_context:
            return 'service_center', request.service_center_context
        return 'global', None
    
    def get_urls(self):
        """Add context-aware URLs"""
        urls = super().get_urls()
        my_urls = [
            path('franchise/<uuid:franchise_id>/', 
                 self.admin_site.admin_view(self.franchise_context_view),
                 name=f'{self.model._meta.app_label}_{self.model._meta.model_name}_franchise_context'),
            path('company/<uuid:company_id>/', 
                 self.admin_site.admin_view(self.company_context_view),
                 name=f'{self.model._meta.app_label}_{self.model._meta.model_name}_company_context'),
            path('service_center/<uuid:service_center_id>/', 
                 self.admin_site.admin_view(self.service_center_context_view),
                 name=f'{self.model._meta.app_label}_{self.model._meta.model_name}_service_center_context'),
        ]
        return my_urls + urls
    
    def franchise_context_view(self, request, franchise_id):
        """Handle franchise context view"""
        franchise = get_object_or_404(Franchise, id=franchise_id)
        request.franchise_context = franchise
        return self.changelist_view(request, extra_context={
            'context_level': 'franchise',
            'context_object': franchise,
            'title': f'{self.model._meta.verbose_name_plural} - {franchise.name}'
        })
    
    def company_context_view(self, request, company_id):
        """Handle company context view"""
        company = get_object_or_404(Company, id=company_id)
        request.company_context = company
        return self.changelist_view(request, extra_context={
            'context_level': 'company',
            'context_object': company,
            'title': f'{self.model._meta.verbose_name_plural} - {company.name}'
        })
    
    def service_center_context_view(self, request, service_center_id):
        """Handle service center context view"""
        service_center = get_object_or_404(ServiceCenter, id=service_center_id)
        request.service_center_context = service_center
        return self.changelist_view(request, extra_context={
            'context_level': 'service_center',
            'context_object': service_center,
            'title': f'{self.model._meta.verbose_name_plural} - {service_center.name}'
        })


class ContextAwareChangeList(ChangeList):
    """Custom ChangeList that respects hierarchical context"""
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        
        # Apply context filtering
        if hasattr(request, 'franchise_context') and request.franchise_context:
            if hasattr(self.model, 'franchise'):
                queryset = queryset.filter(franchise=request.franchise_context)
            elif hasattr(self.model, 'company'):
                queryset = queryset.filter(company__franchise=request.franchise_context)
            elif hasattr(self.model, 'service_center'):
                queryset = queryset.filter(service_center__company__franchise=request.franchise_context)
        
        elif hasattr(request, 'company_context') and request.company_context:
            if hasattr(self.model, 'company'):
                queryset = queryset.filter(company=request.company_context)
            elif hasattr(self.model, 'service_center'):
                queryset = queryset.filter(service_center__company=request.company_context)
        
        elif hasattr(request, 'service_center_context') and request.service_center_context:
            if hasattr(self.model, 'service_center'):
                queryset = queryset.filter(service_center=request.service_center_context)
        
        return queryset


class HierarchicalContextAdmin(HierarchicalContextMixin, admin.ModelAdmin):
    """Base admin class with hierarchical context support"""
    
    def get_changelist(self, request, **kwargs):
        """Use our custom ChangeList"""
        return ContextAwareChangeList
    
    def get_queryset(self, request):
        """Filter queryset based on context"""
        qs = super().get_queryset(request)
        context_level, context_object = self.get_context_level(request)
        
        if context_level == 'franchise' and context_object:
            if hasattr(self.model, 'franchise'):
                qs = qs.filter(franchise=context_object)
            elif hasattr(self.model, 'company'):
                qs = qs.filter(company__franchise=context_object)
            elif hasattr(self.model, 'service_center'):
                qs = qs.filter(service_center__company__franchise=context_object)
        
        elif context_level == 'company' and context_object:
            if hasattr(self.model, 'company'):
                qs = qs.filter(company=context_object)
            elif hasattr(self.model, 'service_center'):
                qs = qs.filter(service_center__company=context_object)
        
        elif context_level == 'service_center' and context_object:
            if hasattr(self.model, 'service_center'):
                qs = qs.filter(service_center=context_object)
        
        return qs
    
    def get_form(self, request, obj=None, **kwargs):
        """Customize form based on context"""
        form = super().get_form(request, obj, **kwargs)
        context_level, context_object = self.get_context_level(request)
        
        # Pre-populate context fields and filter choices
        if context_level == 'franchise' and context_object:
            if 'franchise' in form.base_fields:
                form.base_fields['franchise'].initial = context_object
                form.base_fields['franchise'].queryset = Franchise.objects.filter(id=context_object.id)
            if 'company' in form.base_fields:
                form.base_fields['company'].queryset = Company.objects.filter(franchise=context_object)
        
        elif context_level == 'company' and context_object:
            if 'company' in form.base_fields:
                form.base_fields['company'].initial = context_object
                form.base_fields['company'].queryset = Company.objects.filter(id=context_object.id)
            if 'franchise' in form.base_fields:
                form.base_fields['franchise'].initial = context_object.franchise
                form.base_fields['franchise'].queryset = Franchise.objects.filter(id=context_object.franchise.id)
            if 'service_center' in form.base_fields:
                form.base_fields['service_center'].queryset = ServiceCenter.objects.filter(company=context_object)
        
        elif context_level == 'service_center' and context_object:
            if 'service_center' in form.base_fields:
                form.base_fields['service_center'].initial = context_object
                form.base_fields['service_center'].queryset = ServiceCenter.objects.filter(id=context_object.id)
            if 'company' in form.base_fields:
                form.base_fields['company'].initial = context_object.company
                form.base_fields['company'].queryset = Company.objects.filter(id=context_object.company.id)
            if 'franchise' in form.base_fields:
                form.base_fields['franchise'].initial = context_object.company.franchise
                form.base_fields['franchise'].queryset = Franchise.objects.filter(id=context_object.company.franchise.id)
        
        return form

    def response_add(self, request, obj, post_url_continue=None):
        """Redirect to context view after adding"""
        response = super().response_add(request, obj, post_url_continue)
        context_level, context_object = self.get_context_level(request)
        
        if context_level != 'global' and context_object:
            # Stay in context after adding
            context_url = reverse(f'admin:{self.model._meta.app_label}_{self.model._meta.model_name}_{context_level}_context',
                                args=[context_object.id])
            return redirect(context_url)
        
        return response


@admin.register(Franchise)
class FranchiseAdmin(HierarchicalContextAdmin):
    list_display = ('name', 'code', 'city', 'phone', 'email', 'is_active', 'company_count', 'service_center_count')
    list_filter = ('is_active', 'city', 'country')
    search_fields = ('name', 'code', 'email', 'phone')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'logo', 'is_active')
        }),
        (_('Contact Information'), {
            'fields': ('address', 'city', 'state', 'country', 'postal_code', 'phone', 'email', 'website')
        }),
        (_('Business Information'), {
            'fields': ('tax_id', 'registration_number', 'founding_date')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes'),
            'classes': ('collapse',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def company_count(self, obj):
        count = obj.companies.count()
        if count > 0:
            url = reverse('admin:setup_company_franchise_context', args=[obj.id])
            return format_html('<a href="{}">{} شركة</a>', url, count)
        return count
    company_count.short_description = _('Companies')
    
    def service_center_count(self, obj):
        count = ServiceCenter.objects.filter(company__franchise=obj).count()
        if count > 0:
            url = reverse('admin:setup_servicecenter_franchise_context', args=[obj.id])
            return format_html('<a href="{}">{} مركز</a>', url, count)
        return count
    service_center_count.short_description = _('Service Centers')


@admin.register(Company)
class CompanyAdmin(HierarchicalContextAdmin):
    list_display = ('name', 'code', 'franchise', 'city', 'phone', 'is_active', 'service_center_count')
    list_filter = ('is_active', 'franchise', 'city')
    search_fields = ('name', 'code', 'email', 'phone', 'franchise__name')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('franchise', 'name', 'code', 'logo', 'is_active')
        }),
        (_('Contact Information'), {
            'fields': ('address', 'city', 'state', 'country', 'postal_code', 'phone', 'email', 'website')
        }),
        (_('Business Information'), {
            'fields': ('tax_id', 'registration_number', 'founding_date', 'service_level')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes'),
            'classes': ('collapse',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def service_center_count(self, obj):
        count = obj.service_centers.count()
        if count > 0:
            url = reverse('admin:setup_servicecenter_company_context', args=[obj.id])
            return format_html('<a href="{}">{} مركز</a>', url, count)
        return count
    service_center_count.short_description = _('Service Centers')


@admin.register(ServiceCenter)
class ServiceCenterAdmin(HierarchicalContextAdmin):
    list_display = ('name', 'code', 'company', 'center_type', 'size', 'city', 'is_active', 'customer_count')
    list_filter = ('is_active', 'size', 'center_type', 'company__franchise', 'company')
    search_fields = ('name', 'code', 'company__name', 'city')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('company', 'name', 'code', 'center_type', 'size', 'is_active')
        }),
        (_('Service Capability'), {
            'fields': ('serves_all_vehicle_makes', 'capacity', 'service_level')
        }),
        (_('Contact Information'), {
            'fields': ('address', 'city', 'state', 'country', 'postal_code', 'latitude', 'longitude', 'phone', 'email')
        }),
        (_('Warehouse & Management'), {
            'fields': ('primary_warehouse', 'secondary_warehouses', 'manager')
        }),
        (_('Operational Information'), {
            'fields': ('opening_hours',)
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes'),
            'classes': ('collapse',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def customer_count(self, obj):
        count = obj.customers.count()
        if count > 0:
            url = reverse('admin:setup_customer_service_center_context', args=[obj.id])
            return format_html('<a href="{}">{} عميل</a>', url, count)
        return count
    customer_count.short_description = _('Customers')


@admin.register(Customer)
class CustomerAdmin(HierarchicalContextAdmin):
    list_display = ('full_name', 'email', 'phone', 'customer_type', 'service_center', 'is_active')
    list_filter = ('is_active', 'customer_type', 'gender', 'service_center__company__franchise', 'service_center')
    search_fields = ('first_name', 'last_name', 'email', 'phone', 'company_name')
    readonly_fields = ('created_at', 'updated_at', 'age')
    
    fieldsets = (
        (_('Personal Information'), {
            'fields': ('first_name', 'second_name', 'third_name', 'last_name', 'gender', 'date_of_birth', 'age')
        }),
        (_('Identification'), {
            'fields': ('id_type', 'id_number', 'nationality')
        }),
        (_('Contact Information'), {
            'fields': ('email', 'phone', 'alternative_phone', 'address', 'city', 'state', 'postal_code', 'country')
        }),
        (_('Business Information'), {
            'fields': ('customer_type', 'company_name', 'company_registration', 'commercial_registration'),
            'description': _('For corporate customers')
        }),
        (_('Service Association'), {
            'fields': ('service_center', 'classification')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes', 'is_active'),
            'classes': ('collapse',)
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(UserProfile)
class UserProfileAdmin(HierarchicalContextAdmin):
    list_display = ('user', 'role', 'franchise', 'company', 'service_center', 'department', 'is_active')
    list_filter = ('is_active', 'role', 'franchise', 'company', 'service_center', 'department')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'user__email', 'employee_id')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('User Information'), {
            'fields': ('user', 'employee_id', 'role')
        }),
        (_('Organization Association'), {
            'fields': ('franchise', 'company', 'service_center')
        }),
        (_('Contact Information'), {
            'fields': ('phone', 'mobile', 'address', 'city', 'emergency_contact', 'emergency_phone')
        }),
        (_('Employment Details'), {
            'fields': ('hire_date', 'department', 'position', 'salary')
        }),
        (_('Skills & Certifications'), {
            'fields': ('skills', 'certifications'),
            'classes': ('collapse',)
        }),
        (_('Status'), {
            'fields': ('is_active', 'notes')
        }),
        (_('System Information'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


# Register remaining models with standard admin
admin.site.register(ServiceCenterType)
admin.site.register(ServiceLevel)

@admin.register(VehicleMake)
class VehicleMakeAdmin(admin.ModelAdmin):
    list_display = ('name', 'country_of_origin', 'is_active')
    list_filter = ('is_active', 'country_of_origin')
    search_fields = ('name', 'description', 'country_of_origin')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(VehicleModel)  
class VehicleModelAdmin(admin.ModelAdmin):
    list_display = ('name', 'make', 'vehicle_class', 'year_introduced', 'year_discontinued', 'is_active')
    list_filter = ('make', 'vehicle_class', 'is_active')
    search_fields = ('name', 'make__name', 'description', 'vehicle_class')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('make',)

@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    list_display = ('make', 'model', 'year', 'license_plate', 'vin', 'owner', 'service_center')
    list_filter = ('make', 'service_center', 'standard_make')
    search_fields = ('make', 'model', 'license_plate', 'vin', 'owner__first_name', 'owner__last_name')
    autocomplete_fields = ('owner', 'service_center', 'standard_make', 'standard_model')
    filter_horizontal = ('drivers',)

admin.site.register(UserRole)
admin.site.register(TechnicianSpecialization)
admin.site.register(TechnicianProfile)
admin.site.register(UserWarehouseAssignment)
admin.site.register(ServiceHistory)
admin.site.register(VehicleOwnershipTransfer)
admin.site.register(ServiceCenterMakeModel)
