from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import VehicleOwnershipTransfer, Vehicle, Customer, ServiceCenter, Company, Franchise, VehicleMake, VehicleModel
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from .models import (
    UserProfile, UserRole, TechnicianProfile, TechnicianSpecialization, UserWarehouseAssignment
)
from .utils import (
    validate_arabic_name, 
    validate_egyptian_national_id, 
    validate_egyptian_phone,
    validate_commercial_registration,
    is_egyptian_context
)

class VehicleOwnershipTransferForm(forms.ModelForm):
    """Form for creating vehicle ownership transfers"""
    
    class Meta:
        model = VehicleOwnershipTransfer
        fields = ['vehicle', 'new_owner', 'transfer_date', 'sale_price', 
                 'odometer_reading', 'notes']
        widgets = {
            'transfer_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        # Allow restricting vehicle choices
        vehicle_choices = kwargs.pop('vehicle_choices', None)
        super().__init__(*args, **kwargs)
        
        if vehicle_choices:
            self.fields['vehicle'].queryset = vehicle_choices
            
        # Always limit new_owner choices to active customers
        self.fields['new_owner'].queryset = Customer.objects.filter(is_active=True)
        
        # Set the previous_owner field based on the vehicle selected
        self.fields['vehicle'].widget.attrs['onchange'] = 'updatePreviousOwner(this.value)'
    
    def clean(self):
        """Custom form validation"""
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        new_owner = cleaned_data.get('new_owner')
        
        if vehicle and new_owner:
            # Automatically set previous_owner based on vehicle's current owner
            cleaned_data['previous_owner'] = vehicle.owner
            
            # Validate that new_owner is different from current owner
            if vehicle.owner == new_owner:
                self.add_error('new_owner', _('New owner must be different from the current owner.'))
                
            # Check if there's already a pending transfer for this vehicle
            existing_transfers = VehicleOwnershipTransfer.objects.filter(
                vehicle=vehicle, status='pending'
            )
            if existing_transfers.exists():
                self.add_error('vehicle', _('There is already a pending transfer for this vehicle.'))
                
        return cleaned_data

class BulkVehicleOwnershipTransferForm(forms.Form):
    """Form for creating multiple vehicle ownership transfers at once"""
    new_owner = forms.ModelChoiceField(
        queryset=Customer.objects.filter(is_active=True),
        label=_('New Owner'),
        help_text=_('Select the new owner for all selected vehicles')
    )
    transfer_date = forms.DateField(
        label=_('Transfer Date'),
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    sale_price = forms.DecimalField(
        label=_('Sale Price'),
        max_digits=15,
        decimal_places=2,
        required=False
    )
    odometer_reading = forms.IntegerField(
        label=_('Odometer Reading'),
        required=False
    )
    notes = forms.CharField(
        label=_('Notes'),
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False
    )

class CustomerForm(forms.ModelForm):
    """Form for creating and updating customers"""
    
    class Meta:
        model = Customer
        fields = [
            'first_name', 'second_name', 'third_name', 'last_name', 'customer_type', 
            'email', 'phone', 'id_number', 'id_type', 'address', 'city', 'nationality', 'is_active', 
            'notes', 'commercial_registration', 'company_name'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الاسم الأول')
            }),
            'second_name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الاسم الثاني')
            }),
            'third_name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الاسم الثالث')
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('اسم العائلة')
            }),
            'customer_type': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('البريد الإلكتروني')
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم الهاتف'),
                'dir': 'ltr'
            }),
            'id_number': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم الهوية'),
                'dir': 'ltr'
            }),
            'id_type': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('العنوان')
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('المدينة')
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ملاحظات (اختياري)')
            }),
            'commercial_registration': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم السجل التجاري'),
                'dir': 'ltr'
            }),
            'company_name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('اسم الشركة')
            }),
            'nationality': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
        }
        labels = {
            'first_name': _('الاسم الأول'),
            'second_name': _('الاسم الثاني'),
            'third_name': _('الاسم الثالث'),
            'last_name': _('اسم العائلة'),
            'customer_type': _('نوع العميل'),
            'email': _('البريد الإلكتروني'),
            'phone': _('رقم الهاتف'),
            'id_number': _('رقم الهوية'),
            'id_type': _('نوع الهوية'),
            'address': _('العنوان'),
            'city': _('المدينة'),
            'is_active': _('نشط'),
            'notes': _('ملاحظات'),
            'commercial_registration': _('السجل التجاري'),
            'company_name': _('اسم الشركة'),
            'nationality': _('الجنسية'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Make some fields optional
        self.fields['second_name'].required = False
        self.fields['third_name'].required = False
        self.fields['email'].required = False
        self.fields['id_number'].required = False
        self.fields['id_type'].required = False
        self.fields['address'].required = False
        self.fields['city'].required = False
        self.fields['notes'].required = False
        self.fields['commercial_registration'].required = False
        self.fields['company_name'].required = False
    
    def clean_first_name(self):
        first_name = self.cleaned_data.get('first_name')
        if is_egyptian_context():
            validate_arabic_name(first_name)
        return first_name
    
    def clean_second_name(self):
        second_name = self.cleaned_data.get('second_name')
        if is_egyptian_context() and second_name:
            validate_arabic_name(second_name)
        return second_name
    
    def clean_third_name(self):
        third_name = self.cleaned_data.get('third_name')
        if is_egyptian_context() and third_name:
            validate_arabic_name(third_name)
        return third_name
    
    def clean_last_name(self):
        last_name = self.cleaned_data.get('last_name')
        if is_egyptian_context():
            validate_arabic_name(last_name)
        return last_name
    
    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if is_egyptian_context() and phone:
            validate_egyptian_phone(phone)
        return phone
    
    def clean_id_number(self):
        id_number = self.cleaned_data.get('id_number')
        id_type = self.cleaned_data.get('id_type')
        
        if id_number:
            # Check for duplicate ID number (excluding current instance)
            queryset = Customer.objects.filter(id_number=id_number)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise ValidationError(_('رقم الهوية مستخدم مسبقاً'))
            
            # Apply Egyptian validation for national ID
            if is_egyptian_context() and id_type == 'national_id':
                validate_egyptian_national_id(id_number)
        
        return id_number
    
    def clean_commercial_registration(self):
        commercial_registration = self.cleaned_data.get('commercial_registration')
        if is_egyptian_context() and commercial_registration:
            validate_commercial_registration(commercial_registration)
        return commercial_registration
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            # Check for duplicate email (excluding current instance)
            queryset = Customer.objects.filter(email=email)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise ValidationError(_('البريد الإلكتروني مستخدم مسبقاً'))
        return email
    
    def clean(self):
        cleaned_data = super().clean()
        customer_type = cleaned_data.get('customer_type')
        commercial_registration = cleaned_data.get('commercial_registration')
        company_name = cleaned_data.get('company_name')
        
        # For corporate customers in Egyptian context, commercial registration is required
        if (customer_type == 'corporate' and is_egyptian_context() and 
            not commercial_registration):
            raise ValidationError({
                'commercial_registration': _('السجل التجاري مطلوب للشركات في مصر')
            })
        
        # If commercial registration is provided, customer type should be corporate
        if commercial_registration and customer_type != 'corporate':
            raise ValidationError({
                'customer_type': _('نوع العميل يجب أن يكون شركة عند إدخال السجل التجاري')
            })
        
        return cleaned_data

class VehicleForm(forms.ModelForm):
    """Form for creating and updating vehicles"""
    
    class Meta:
        model = Vehicle
        fields = [
            'owner', 'make', 'model', 'year', 'license_plate', 'vin',
            'color', 'standard_make', 'standard_model', 'service_center', 'notes'
        ]
        widgets = {
            'owner': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'data-search': 'true'
            }),
            'make': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الماركة')
            }),
            'model': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الموديل')
            }),
            'year': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'min': '1900',
                'max': '2030'
            }),
            'license_plate': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم اللوحة'),
                'dir': 'ltr'
            }),
            'vin': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم الشاسيه'),
                'dir': 'ltr'
            }),
            'color': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('اللون')
            }),
            'standard_make': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'standard_model': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'service_center': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ملاحظات (اختياري)')
            }),
        }
        labels = {
            'owner': _('المالك'),
            'make': _('الماركة'),
            'model': _('الموديل'),
            'year': _('سنة الصنع'),
            'license_plate': _('رقم اللوحة'),
            'vin': _('رقم الشاسيه'),
            'color': _('اللون'),
            'standard_make': _('الماركة القياسية'),
            'standard_model': _('الموديل القياسي'),
            'service_center': _('مركز الخدمة'),
            'notes': _('ملاحظات'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            # Filter customers, vehicle makes, and service centers based on current tenant
            self.fields['owner'].queryset = Customer.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('first_name', 'last_name')
            
            self.fields['standard_make'].queryset = VehicleMake.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')
            
            self.fields['service_center'].queryset = ServiceCenter.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('name')
            
            # Initially show no models until make is selected
            self.fields['standard_model'].queryset = VehicleModel.objects.none()
            
            # If editing existing vehicle, load the appropriate models
            if self.instance.pk and self.instance.standard_make:
                self.fields['standard_model'].queryset = VehicleModel.objects.filter(
                    make=self.instance.standard_make,
                    tenant_id=tenant_id
                ).order_by('name')
        
        # Make some fields optional
        self.fields['vin'].required = False
        self.fields['color'].required = False
        self.fields['year'].required = False
        self.fields['standard_make'].required = False
        self.fields['standard_model'].required = False
        self.fields['service_center'].required = False
        self.fields['notes'].required = False
    
    def clean_license_plate(self):
        license_plate = self.cleaned_data.get('license_plate')
        if license_plate:
            # Check for duplicate license plate (excluding current instance)
            queryset = Vehicle.objects.filter(license_plate=license_plate)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise ValidationError(_('رقم اللوحة مستخدم مسبقاً'))
        return license_plate
    
    def clean_vin(self):
        vin = self.cleaned_data.get('vin')
        if vin:
            # Check for duplicate VIN (excluding current instance)
            queryset = Vehicle.objects.filter(vin=vin)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise ValidationError(_('رقم الشاسيه مستخدم مسبقاً'))
        return vin
    
    def clean_year(self):
        year = self.cleaned_data.get('year')
        if year:
            from datetime import date
            current_year = date.today().year
            if year < 1900 or year > current_year + 1:
                raise ValidationError(_('سنة الصنع غير صحيحة'))
        return year

class ServiceCenterForm(forms.ModelForm):
    """Form for creating and updating service centers"""
    
    class Meta:
        model = ServiceCenter
        fields = [
            'name', 'company', 'code', 'center_type', 'size', 'address', 'city', 
            'phone', 'email', 'manager', 'capacity', 'opening_hours', 'is_active', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('اسم مركز الخدمة')
            }),
            'company': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رمز مركز الخدمة')
            }),
            'center_type': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'size': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('عنوان مركز الخدمة')
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('المدينة')
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم الهاتف'),
                'dir': 'ltr'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('البريد الإلكتروني')
            }),
            'manager': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'capacity': forms.NumberInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'min': '1',
                'placeholder': _('السعة اليومية')
            }),
            'opening_hours': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ساعات العمل (JSON)')
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ملاحظات (اختياري)')
            }),
        }
        labels = {
            'name': _('اسم مركز الخدمة'),
            'company': _('الشركة'),
            'code': _('رمز مركز الخدمة'),
            'center_type': _('نوع المركز'),
            'size': _('حجم المركز'),
            'address': _('العنوان'),
            'city': _('المدينة'),
            'phone': _('رقم الهاتف'),
            'email': _('البريد الإلكتروني'),
            'manager': _('المدير'),
            'capacity': _('السعة اليومية'),
            'opening_hours': _('ساعات العمل'),
            'is_active': _('نشط'),
            'notes': _('ملاحظات'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            # Filter companies, center types, and managers based on current tenant
            self.fields['company'].queryset = Company.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')
            
            # Import required models
            from django.contrib.auth.models import User
            from setup.models import ServiceCenterType
            
            self.fields['center_type'].queryset = ServiceCenterType.objects.filter(
                is_active=True
            ).order_by('name')
            
            self.fields['manager'].queryset = User.objects.filter(
                is_active=True
            ).order_by('first_name', 'last_name')
        
        # Make some fields optional
        self.fields['email'].required = False
        self.fields['manager'].required = False
        self.fields['capacity'].required = False
        self.fields['opening_hours'].required = False
        self.fields['notes'].required = False

class CompanyForm(forms.ModelForm):
    """Form for creating and updating companies"""
    
    class Meta:
        model = Company
        fields = [
            'name', 'franchise', 'code', 'registration_number', 'tax_id',
            'address', 'city', 'phone', 'email', 'website',
            'founding_date', 'is_active', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('اسم الشركة')
            }),
            'franchise': forms.Select(attrs={
                'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
            }),
            'registration_number': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم السجل التجاري'),
                'dir': 'ltr'
            }),
            'tax_id': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الرقم الضريبي'),
                'dir': 'ltr'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('عنوان الشركة')
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('المدينة')
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم الهاتف'),
                'dir': 'ltr'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('البريد الإلكتروني')
            }),
            'website': forms.URLInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الموقع الإلكتروني'),
                'dir': 'ltr'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رمز الشركة')
            }),
            'founding_date': forms.DateInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'type': 'date'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ملاحظات (اختياري)')
            }),
        }
        labels = {
            'name': _('اسم الشركة'),
            'franchise': _('الامتياز'),
            'registration_number': _('رقم السجل التجاري'),
            'tax_id': _('الرقم الضريبي'),
            'address': _('العنوان'),
            'city': _('المدينة'),
            'phone': _('رقم الهاتف'),
            'email': _('البريد الإلكتروني'),
            'website': _('الموقع الإلكتروني'),
            'code': _('رمز الشركة'),
            'founding_date': _('تاريخ التأسيس'),
            'is_active': _('نشط'),
            'notes': _('ملاحظات'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            # Filter franchises based on current tenant
            self.fields['franchise'].queryset = Franchise.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')
        
        # Make some fields optional
        self.fields['code'].required = False
        self.fields['registration_number'].required = False
        self.fields['tax_id'].required = False
        self.fields['email'].required = False
        self.fields['website'].required = False
        self.fields['founding_date'].required = False
        self.fields['notes'].required = False

class FranchiseForm(forms.ModelForm):
    """Form for creating and updating franchises"""
    
    class Meta:
        model = Franchise
        fields = [
            'name', 'code', 'address', 'city', 'phone', 'email', 'website',
            'tax_id', 'registration_number', 'founding_date', 'is_active', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('اسم الامتياز')
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رمز الامتياز')
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('العنوان')
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('المدينة')
            }),
            'tax_id': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الرقم الضريبي'),
                'dir': 'ltr'
            }),
            'registration_number': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم السجل التجاري'),
                'dir': 'ltr'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('رقم الهاتف'),
                'dir': 'ltr'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('البريد الإلكتروني')
            }),
            'website': forms.URLInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'placeholder': _('الموقع الإلكتروني'),
                'dir': 'ltr'
            }),
            'founding_date': forms.DateInput(attrs={
                'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'type': 'date'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
                'rows': 3,
                'placeholder': _('ملاحظات (اختياري)')
            }),
        }
        labels = {
            'name': _('اسم الامتياز'),
            'code': _('رمز الامتياز'),
            'address': _('العنوان'),
            'city': _('المدينة'),
            'phone': _('رقم الهاتف'),
            'email': _('البريد الإلكتروني'),
            'website': _('الموقع الإلكتروني'),
            'tax_id': _('الرقم الضريبي'),
            'registration_number': _('رقم السجل التجاري'),
            'founding_date': _('تاريخ التأسيس'),
            'is_active': _('نشط'),
            'notes': _('ملاحظات'),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Make some fields optional
        self.fields['code'].required = False
        self.fields['address'].required = False
        self.fields['city'].required = False
        self.fields['tax_id'].required = False
        self.fields['registration_number'].required = False
        self.fields['email'].required = False
        self.fields['website'].required = False
        self.fields['founding_date'].required = False
        self.fields['notes'].required = False

class VehicleTransferForm(forms.Form):
    """Form for transferring vehicle ownership"""
    
    vehicle = forms.ModelChoiceField(
        queryset=Vehicle.objects.none(),
        label=_('المركبة'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'data-search': 'true'
        })
    )
    
    new_customer = forms.ModelChoiceField(
        queryset=Customer.objects.none(),
        label=_('العميل الجديد'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'data-search': 'true'
        })
    )
    
    transfer_reason = forms.CharField(
        label=_('سبب النقل'),
        widget=forms.Textarea(attrs={
            'class': 'form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'rows': 3,
            'placeholder': _('سبب نقل المركبة')
        })
    )
    
    transfer_date = forms.DateField(
        label=_('تاريخ النقل'),
        widget=forms.DateInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'type': 'date'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            # Filter vehicles and customers based on current tenant
            self.fields['vehicle'].queryset = Vehicle.objects.filter(
                tenant_id=tenant_id
            ).select_related('customer', 'make', 'model').order_by('customer__first_name', 'customer__last_name', 'license_plate')
            
            self.fields['new_customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('first_name', 'last_name')
    
    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        new_customer = cleaned_data.get('new_customer')
        
        if vehicle and new_customer:
            # Check if the new customer is the same as current owner
            if vehicle.customer == new_customer:
                raise ValidationError(_('لا يمكن نقل المركبة لنفس العميل الحالي'))
        
        return cleaned_data

class VehicleSearchForm(forms.Form):
    """Form for searching vehicles with advanced filters"""
    
    search = forms.CharField(
        required=False,
        label=_('البحث'),
        widget=forms.TextInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'placeholder': _('ابحث برقم اللوحة أو الشاسيه أو العميل')
        })
    )
    
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.none(),
        required=False,
        empty_label=_('-- جميع العملاء --'),
        label=_('العميل'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
        })
    )
    
    make = forms.ModelChoiceField(
        queryset=VehicleMake.objects.none(),
        required=False,
        empty_label=_('-- جميع الماركات --'),
        label=_('الماركة'),
        widget=forms.Select(attrs={
            'class': 'form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50'
        })
    )
    
    year_from = forms.IntegerField(
        required=False,
        label=_('من سنة'),
        widget=forms.NumberInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'min': '1900',
            'max': '2030',
            'placeholder': _('من')
        })
    )
    
    year_to = forms.IntegerField(
        required=False,
        label=_('إلى سنة'),
        widget=forms.NumberInput(attrs={
            'class': 'form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50',
            'min': '1900',
            'max': '2030',
            'placeholder': _('إلى')
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            self.fields['customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id
            ).order_by('first_name', 'last_name')
            
            self.fields['make'].queryset = VehicleMake.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')

class UserRoleForm(forms.ModelForm):
    """Form for creating and editing user roles"""
    
    class Meta:
        model = UserRole
        fields = [
            'name', 'description', 'level', 'priority',
            'can_create_users', 'can_manage_inventory', 'can_manage_work_orders',
            'can_manage_sales', 'can_manage_customers', 'can_view_reports',
            'can_manage_settings', 'can_manage_franchises', 'can_manage_companies',
            'can_manage_service_centers', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'اسم الدور'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'وصف الدور ومسؤولياته',
                'rows': 3
            }),
            'level': forms.Select(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'priority': forms.NumberInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '1',
                'max': '999'
            }),
        }


class CustomUserCreationForm(UserCreationForm):
    """Enhanced user creation form with additional fields"""
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'الاسم الأول'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'الاسم الأخير'
        })
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'البريد الإلكتروني'
        })
    )
    
    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'اسم المستخدم'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['password1'].widget.attrs.update({
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'كلمة المرور'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'تأكيد كلمة المرور'
        })


class UserProfileForm(forms.ModelForm):
    """Form for creating and editing user profiles"""
    
    class Meta:
        model = UserProfile
        fields = [
            'employee_id', 'phone', 'mobile', 'address', 'city',
            'emergency_contact', 'emergency_phone', 'franchise', 'company',
            'service_center', 'role', 'hire_date', 'department', 'position',
            'salary', 'skills', 'certifications', 'is_active', 'notes'
        ]
        widgets = {
            'employee_id': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'رقم الموظف'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'رقم الهاتف'
            }),
            'mobile': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'رقم الجوال'
            }),
            'address': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'العنوان',
                'rows': 3
            }),
            'city': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'المدينة'
            }),
            'emergency_contact': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'جهة اتصال الطوارئ'
            }),
            'emergency_phone': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'هاتف الطوارئ'
            }),
            'franchise': forms.Select(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'company': forms.Select(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'service_center': forms.Select(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'role': forms.Select(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'hire_date': forms.DateInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'type': 'date'
            }),
            'department': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'القسم'
            }),
            'position': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'المنصب'
            }),
            'salary': forms.NumberInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'الراتب',
                'step': '0.01'
            }),
            'skills': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'المهارات (مفصولة بفواصل)',
                'rows': 3
            }),
            'certifications': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'الشهادات (مفصولة بفواصل)',
                'rows': 3
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'ملاحظات',
                'rows': 3
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Filter choices based on tenant
        if self.tenant_id:
            self.fields['franchise'].queryset = Franchise.objects.filter(tenant_id=self.tenant_id)
            self.fields['company'].queryset = Company.objects.filter(tenant_id=self.tenant_id)
            self.fields['service_center'].queryset = ServiceCenter.objects.filter(tenant_id=self.tenant_id)
        
        # Set up cascading dropdowns
        self.fields['company'].queryset = Company.objects.none()
        self.fields['service_center'].queryset = ServiceCenter.objects.none()
        
        if 'franchise' in self.data:
            try:
                franchise_id = int(self.data.get('franchise'))
                self.fields['company'].queryset = Company.objects.filter(franchise_id=franchise_id)
            except (ValueError, TypeError):
                pass
        elif self.instance.pk and self.instance.franchise:
            self.fields['company'].queryset = self.instance.franchise.companies.all()
        
        if 'company' in self.data:
            try:
                company_id = int(self.data.get('company'))
                self.fields['service_center'].queryset = ServiceCenter.objects.filter(company_id=company_id)
            except (ValueError, TypeError):
                pass
        elif self.instance.pk and self.instance.company:
            self.fields['service_center'].queryset = self.instance.company.service_centers.all()
    
    def clean_skills(self):
        """Convert skills string to list"""
        skills = self.cleaned_data.get('skills', '')
        if isinstance(skills, str):
            return [skill.strip() for skill in skills.split(',') if skill.strip()]
        return skills
    
    def clean_certifications(self):
        """Convert certifications string to list"""
        certifications = self.cleaned_data.get('certifications', '')
        if isinstance(certifications, str):
            return [cert.strip() for cert in certifications.split(',') if cert.strip()]
        return certifications


class TechnicianSpecializationForm(forms.ModelForm):
    """Form for creating and editing technician specializations"""
    
    class Meta:
        model = TechnicianSpecialization
        fields = [
            'name', 'description', 'category', 'certification_required',
            'certification_body', 'compatible_service_center_types', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'اسم التخصص'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'وصف التخصص',
                'rows': 3
            }),
            'category': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'فئة التخصص'
            }),
            'certification_body': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'جهة الشهادة'
            }),
            'compatible_service_center_types': forms.CheckboxSelectMultiple(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
        }


class TechnicianProfileForm(forms.ModelForm):
    """Form for creating and editing technician profiles"""
    
    class Meta:
        model = TechnicianProfile
        fields = [
            'license_number', 'license_expiry', 'experience_years',
            'specializations', 'available_for_emergency', 'max_jobs_per_day',
            'tools_assigned', 'equipment_certified'
        ]
        widgets = {
            'license_number': forms.TextInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'رقم الرخصة'
            }),
            'license_expiry': forms.DateInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'type': 'date'
            }),
            'experience_years': forms.NumberInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'سنوات الخبرة',
                'min': '0'
            }),
            'specializations': forms.CheckboxSelectMultiple(attrs={
                'class': 'form-checkbox h-4 w-4 text-blue-600'
            }),
            'max_jobs_per_day': forms.NumberInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'أقصى عدد وظائف يومياً',
                'min': '1',
                'max': '20'
            }),
            'tools_assigned': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'الأدوات المخصصة (مفصولة بفواصل)',
                'rows': 3
            }),
            'equipment_certified': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'المعدات المعتمدة (مفصولة بفواصل)',
                'rows': 3
            }),
        }
    
    def clean_tools_assigned(self):
        """Convert tools string to list"""
        tools = self.cleaned_data.get('tools_assigned', '')
        if isinstance(tools, str):
            return [tool.strip() for tool in tools.split(',') if tool.strip()]
        return tools
    
    def clean_equipment_certified(self):
        """Convert equipment string to list"""
        equipment = self.cleaned_data.get('equipment_certified', '')
        if isinstance(equipment, str):
            return [equip.strip() for equip in equipment.split(',') if equip.strip()]
        return equipment


class UserWarehouseAssignmentForm(forms.ModelForm):
    """Form for assigning users to warehouses"""
    
    class Meta:
        model = UserWarehouseAssignment
        fields = [
            'user_profile', 'warehouse', 'assignment_type',
            'can_receive_inventory', 'can_issue_inventory', 'can_transfer_inventory',
            'can_adjust_inventory', 'can_view_reports', 'valid_from', 'valid_until',
            'is_active', 'notes'
        ]
        widgets = {
            'user_profile': forms.Select(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'warehouse': forms.Select(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'assignment_type': forms.Select(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'valid_from': forms.DateInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'type': 'date'
            }),
            'valid_until': forms.DateInput(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'type': 'date'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'ملاحظات',
                'rows': 3
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Filter choices based on tenant
        if self.tenant_id:
            self.fields['user_profile'].queryset = UserProfile.objects.filter(tenant_id=self.tenant_id)
            # Assuming warehouse Location model has tenant_id
            try:
                from warehouse.models import Location
                self.fields['warehouse'].queryset = Location.objects.filter(tenant_id=self.tenant_id)
            except:
                pass


class CombinedUserCreationForm(forms.Form):
    """Combined form for creating user and profile together"""
    
    # User fields
    username = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'اسم المستخدم'
        })
    )
    first_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'الاسم الأول'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'الاسم الأخير'
        })
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'البريد الإلكتروني'
        })
    )
    password1 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'كلمة المرور'
        })
    )
    password2 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'تأكيد كلمة المرور'
        })
    )
    
    # Profile fields
    employee_id = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'رقم الموظف'
        })
    )
    phone = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'رقم الهاتف'
        })
    )
    mobile = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'رقم الجوال'
        })
    )
    role = forms.ModelChoiceField(
        queryset=UserRole.objects.filter(is_active=True),
        widget=forms.Select(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    franchise = forms.ModelChoiceField(
        queryset=Franchise.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    company = forms.ModelChoiceField(
        queryset=Company.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    service_center = forms.ModelChoiceField(
        queryset=ServiceCenter.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    
    def __init__(self, *args, **kwargs):
        self.tenant_id = kwargs.pop('tenant_id', None)
        super().__init__(*args, **kwargs)
        
        # Filter choices based on tenant
        if self.tenant_id:
            self.fields['franchise'].queryset = Franchise.objects.filter(tenant_id=self.tenant_id)
            self.fields['company'].queryset = Company.objects.filter(tenant_id=self.tenant_id)
            self.fields['service_center'].queryset = ServiceCenter.objects.filter(tenant_id=self.tenant_id)
    
    def clean_password2(self):
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("كلمات المرور غير متطابقة")
        return password2
    
    def save(self, tenant_id=None):
        """Create both user and profile"""
        # Create user
        user = User.objects.create_user(
            username=self.cleaned_data['username'],
            email=self.cleaned_data['email'],
            password=self.cleaned_data['password1'],
            first_name=self.cleaned_data['first_name'],
            last_name=self.cleaned_data['last_name'],
        )
        
        # Create profile
        profile = UserProfile.objects.create(
            user=user,
            employee_id=self.cleaned_data.get('employee_id', ''),
            phone=self.cleaned_data.get('phone', ''),
            mobile=self.cleaned_data.get('mobile', ''),
            role=self.cleaned_data['role'],
            franchise=self.cleaned_data.get('franchise'),
            company=self.cleaned_data.get('company'),
            service_center=self.cleaned_data.get('service_center'),
            tenant_id=tenant_id,
        )
        
        return user, profile