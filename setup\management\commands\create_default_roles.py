from django.core.management.base import BaseCommand
from django.utils.translation import gettext as _
from setup.models import UserRole


class Command(BaseCommand):
    help = 'Create default user roles for the franchise system'

    def handle(self, *args, **options):
        """Create default user roles"""
        
        # Define default roles
        default_roles = [
            # Global Level Roles
            {
                'name': _('مدير النظام'),
                'description': _('مدير النظام الكامل مع صلاحيات شاملة'),
                'level': 'global',
                'priority': 1,
                'permissions': {
                    'can_create_users': True,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': True,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': True,
                    'can_manage_settings': True,
                    'can_manage_franchises': True,
                    'can_manage_companies': True,
                    'can_manage_service_centers': True,
                }
            },
            
            # Franchise Level Roles
            {
                'name': _('مدير الامتياز'),
                'description': _('مدير مسؤول عن امتياز واحد وجميع شركاته ومراكز الخدمة'),
                'level': 'franchise',
                'priority': 10,
                'permissions': {
                    'can_create_users': True,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': True,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': True,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': True,
                    'can_manage_service_centers': True,
                }
            },
            {
                'name': _('موظف الامتياز'),
                'description': _('موظف في مستوى الامتياز مع صلاحيات محدودة'),
                'level': 'franchise',
                'priority': 20,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': True,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': True,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            
            # Company Level Roles
            {
                'name': _('مدير الشركة'),
                'description': _('مدير مسؤول عن شركة واحدة ومراكز الخدمة التابعة لها'),
                'level': 'company',
                'priority': 30,
                'permissions': {
                    'can_create_users': True,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': True,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': True,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': True,
                }
            },
            {
                'name': _('مشرف الشركة'),
                'description': _('مشرف على عمليات الشركة'),
                'level': 'company',
                'priority': 40,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': True,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': True,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            {
                'name': _('موظف الشركة'),
                'description': _('موظف عام في الشركة'),
                'level': 'company',
                'priority': 50,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': False,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': False,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            
            # Service Center Level Roles
            {
                'name': _('مدير مركز الخدمة'),
                'description': _('مدير مسؤول عن مركز خدمة واحد'),
                'level': 'service_center',
                'priority': 60,
                'permissions': {
                    'can_create_users': True,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': True,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': True,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            {
                'name': _('مشرف مركز الخدمة'),
                'description': _('مشرف على عمليات مركز الخدمة'),
                'level': 'service_center',
                'priority': 70,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': True,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': True,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            {
                'name': _('فني أول'),
                'description': _('فني متقدم مع خبرة واسعة'),
                'level': 'service_center',
                'priority': 80,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': True,
                    'can_manage_sales': False,
                    'can_manage_customers': True,
                    'can_view_reports': False,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            {
                'name': _('فني'),
                'description': _('فني مؤهل للعمل على المركبات'),
                'level': 'service_center',
                'priority': 90,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': False,
                    'can_manage_work_orders': True,
                    'can_manage_sales': False,
                    'can_manage_customers': True,
                    'can_view_reports': False,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            {
                'name': _('فني متدرب'),
                'description': _('فني متدرب تحت الإشراف'),
                'level': 'service_center',
                'priority': 100,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': False,
                    'can_manage_work_orders': False,
                    'can_manage_sales': False,
                    'can_manage_customers': True,
                    'can_view_reports': False,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            {
                'name': _('موظف استقبال'),
                'description': _('موظف مسؤول عن استقبال العملاء'),
                'level': 'service_center',
                'priority': 110,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': False,
                    'can_manage_work_orders': True,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': False,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            {
                'name': _('موظف مبيعات'),
                'description': _('موظف مسؤول عن المبيعات وقطع الغيار'),
                'level': 'service_center',
                'priority': 120,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': False,
                    'can_manage_sales': True,
                    'can_manage_customers': True,
                    'can_view_reports': False,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
            {
                'name': _('موظف مخزن'),
                'description': _('موظف مسؤول عن إدارة المخزن'),
                'level': 'service_center',
                'priority': 130,
                'permissions': {
                    'can_create_users': False,
                    'can_manage_inventory': True,
                    'can_manage_work_orders': False,
                    'can_manage_sales': False,
                    'can_manage_customers': False,
                    'can_view_reports': False,
                    'can_manage_settings': False,
                    'can_manage_franchises': False,
                    'can_manage_companies': False,
                    'can_manage_service_centers': False,
                }
            },
        ]
        
        created_count = 0
        updated_count = 0
        
        for role_data in default_roles:
            # Extract permissions
            permissions = role_data.pop('permissions')
            
            # Create or update role
            role, created = UserRole.objects.get_or_create(
                name=role_data['name'],
                level=role_data['level'],
                defaults=role_data
            )
            
            # Update permissions
            for perm_name, perm_value in permissions.items():
                setattr(role, perm_name, perm_value)
            
            # Update other fields if not created
            if not created:
                for field, value in role_data.items():
                    if field != 'name' and field != 'level':  # Skip unique fields
                        setattr(role, field, value)
                updated_count += 1
            else:
                created_count += 1
            
            role.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"{'Created' if created else 'Updated'} role: {role.name}"
                )
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"\n✅ Successfully processed {len(default_roles)} roles:"
            )
        )
        self.stdout.write(
            self.style.SUCCESS(f"   - Created: {created_count}")
        )
        self.stdout.write(
            self.style.SUCCESS(f"   - Updated: {updated_count}")
        )
        
        # Display summary by level
        self.stdout.write(self.style.SUCCESS("\n📊 Summary by level:"))
        for level_code, level_name in UserRole.ROLE_LEVELS:
            count = UserRole.objects.filter(level=level_code, is_active=True).count()
            self.stdout.write(
                self.style.SUCCESS(f"   - {level_name}: {count} roles")
            ) 