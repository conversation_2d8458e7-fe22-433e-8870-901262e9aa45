from django.core.management.base import BaseCommand
from django.utils.translation import gettext as _
from setup.models import TechnicianSpecialization, ServiceCenterType


class Command(BaseCommand):
    help = 'Create default technician specializations'

    def handle(self, *args, **options):
        """Create default technician specializations"""
        
        # Define default specializations
        default_specializations = [
            # Engine Specializations
            {
                'name': _('محركات البنزين'),
                'description': _('تخصص في إصلاح وصيانة محركات البنزين'),
                'category': _('المحركات'),
                'certification_required': True,
                'certification_body': _('الهيئة السعودية للمواصفات والمقاييس والجودة'),
            },
            {
                'name': _('محركات الديزل'),
                'description': _('تخصص في إصلاح وصيانة محركات الديزل'),
                'category': _('المحركات'),
                'certification_required': True,
                'certification_body': _('الهيئة السعودية للمواصفات والمقاييس والجودة'),
            },
            {
                'name': _('المحركات الهجينة'),
                'description': _('تخصص في المحركات الهجينة والكهربائية'),
                'category': _('المحركات'),
                'certification_required': True,
                'certification_body': _('شهادة معتمدة في السيارات الكهربائية'),
            },
            
            # Transmission Specializations
            {
                'name': _('ناقل الحركة اليدوي'),
                'description': _('تخصص في إصلاح ناقل الحركة اليدوي'),
                'category': _('نقل الحركة'),
                'certification_required': False,
                'certification_body': '',
            },
            {
                'name': _('ناقل الحركة الأوتوماتيكي'),
                'description': _('تخصص في إصلاح ناقل الحركة الأوتوماتيكي'),
                'category': _('نقل الحركة'),
                'certification_required': True,
                'certification_body': _('شهادة معتمدة في ناقل الحركة الأوتوماتيكي'),
            },
            {
                'name': _('ناقل الحركة CVT'),
                'description': _('تخصص في ناقل الحركة المتغير باستمرار'),
                'category': _('نقل الحركة'),
                'certification_required': True,
                'certification_body': _('شهادة متخصصة CVT'),
            },
            
            # Electrical Specializations
            {
                'name': _('الأنظمة الكهربائية'),
                'description': _('تخصص في الأنظمة الكهربائية للمركبات'),
                'category': _('الكهرباء'),
                'certification_required': True,
                'certification_body': _('شهادة كهربائي مركبات'),
            },
            {
                'name': _('أنظمة الإضاءة'),
                'description': _('تخصص في أنظمة الإضاءة والمصابيح'),
                'category': _('الكهرباء'),
                'certification_required': False,
                'certification_body': '',
            },
            {
                'name': _('أنظمة الشحن'),
                'description': _('تخصص في المولدات وأنظمة الشحن'),
                'category': _('الكهرباء'),
                'certification_required': True,
                'certification_body': _('شهادة أنظمة الشحن'),
            },
            
            # Electronics Specializations
            {
                'name': _('أنظمة التشخيص الإلكتروني'),
                'description': _('تخصص في استخدام أجهزة التشخيص الإلكتروني'),
                'category': _('الإلكترونيات'),
                'certification_required': True,
                'certification_body': _('شهادة أجهزة التشخيص'),
            },
            {
                'name': _('وحدات التحكم الإلكتروني (ECU)'),
                'description': _('تخصص في برمجة وإصلاح وحدات التحكم'),
                'category': _('الإلكترونيات'),
                'certification_required': True,
                'certification_body': _('شهادة وحدات التحكم الإلكتروني'),
            },
            {
                'name': _('أنظمة الترفيه والملاحة'),
                'description': _('تخصص في أنظمة الصوت والملاحة'),
                'category': _('الإلكترونيات'),
                'certification_required': False,
                'certification_body': '',
            },
            
            # Brake Specializations
            {
                'name': _('أنظمة الفرامل التقليدية'),
                'description': _('تخصص في فرامل الأقراص والطبول'),
                'category': _('الفرامل'),
                'certification_required': False,
                'certification_body': '',
            },
            {
                'name': _('نظام ABS'),
                'description': _('تخصص في نظام منع انغلاق الفرامل'),
                'category': _('الفرامل'),
                'certification_required': True,
                'certification_body': _('شهادة أنظمة ABS'),
            },
            {
                'name': _('أنظمة الفرامل الإلكترونية'),
                'description': _('تخصص في الفرامل الإلكترونية المتقدمة'),
                'category': _('الفرامل'),
                'certification_required': True,
                'certification_body': _('شهادة الفرامل الإلكترونية'),
            },
            
            # Suspension Specializations
            {
                'name': _('نظام التعليق التقليدي'),
                'description': _('تخصص في نظام التعليق التقليدي'),
                'category': _('التعليق'),
                'certification_required': False,
                'certification_body': '',
            },
            {
                'name': _('نظام التعليق الهوائي'),
                'description': _('تخصص في أنظمة التعليق الهوائي'),
                'category': _('التعليق'),
                'certification_required': True,
                'certification_body': _('شهادة التعليق الهوائي'),
            },
            {
                'name': _('نظام التعليق الإلكتروني'),
                'description': _('تخصص في أنظمة التعليق الإلكتروني المتكيف'),
                'category': _('التعليق'),
                'certification_required': True,
                'certification_body': _('شهادة التعليق الإلكتروني'),
            },
            
            # Air Conditioning Specializations
            {
                'name': _('أنظمة التكييف'),
                'description': _('تخصص في إصلاح وصيانة أنظمة التكييف'),
                'category': _('التكييف'),
                'certification_required': True,
                'certification_body': _('شهادة تبريد وتكييف المركبات'),
            },
            {
                'name': _('أنظمة التدفئة'),
                'description': _('تخصص في أنظمة التدفئة والتهوية'),
                'category': _('التكييف'),
                'certification_required': False,
                'certification_body': '',
            },
            
            # Body Work Specializations
            {
                'name': _('إصلاح الهيكل'),
                'description': _('تخصص في إصلاح هياكل المركبات'),
                'category': _('الهيكل'),
                'certification_required': True,
                'certification_body': _('شهادة إصلاح الهياكل'),
            },
            {
                'name': _('الدهان والطلاء'),
                'description': _('تخصص في دهان وطلاء المركبات'),
                'category': _('الهيكل'),
                'certification_required': True,
                'certification_body': _('شهادة الدهان والطلاء'),
            },
            {
                'name': _('اللحام'),
                'description': _('تخصص في لحام المركبات'),
                'category': _('الهيكل'),
                'certification_required': True,
                'certification_body': _('شهادة اللحام'),
            },
            
            # Advanced Systems
            {
                'name': _('أنظمة مساعدة السائق (ADAS)'),
                'description': _('تخصص في أنظمة مساعدة السائق المتقدمة'),
                'category': _('الأنظمة المتقدمة'),
                'certification_required': True,
                'certification_body': _('شهادة أنظمة ADAS'),
            },
            {
                'name': _('أنظمة الأمان'),
                'description': _('تخصص في أنظمة الأمان والوسائد الهوائية'),
                'category': _('الأنظمة المتقدمة'),
                'certification_required': True,
                'certification_body': _('شهادة أنظمة الأمان'),
            },
            {
                'name': _('أنظمة المعلومات والترفيه'),
                'description': _('تخصص في الأنظمة المعلوماتية المتقدمة'),
                'category': _('الأنظمة المتقدمة'),
                'certification_required': False,
                'certification_body': '',
            },
        ]
        
        created_count = 0
        updated_count = 0
        
        for spec_data in default_specializations:
            # Create or update specialization
            specialization, created = TechnicianSpecialization.objects.get_or_create(
                name=spec_data['name'],
                category=spec_data['category'],
                defaults=spec_data
            )
            
            # Update fields if not created
            if not created:
                for field, value in spec_data.items():
                    if field not in ['name', 'category']:  # Skip unique fields
                        setattr(specialization, field, value)
                specialization.save()
                updated_count += 1
            else:
                created_count += 1
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"{'Created' if created else 'Updated'} specialization: {specialization.name}"
                )
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"\n✅ Successfully processed {len(default_specializations)} specializations:"
            )
        )
        self.stdout.write(
            self.style.SUCCESS(f"   - Created: {created_count}")
        )
        self.stdout.write(
            self.style.SUCCESS(f"   - Updated: {updated_count}")
        )
        
        # Display summary by category
        self.stdout.write(self.style.SUCCESS("\n📊 Summary by category:"))
        categories = TechnicianSpecialization.objects.values_list('category', flat=True).distinct()
        for category in categories:
            count = TechnicianSpecialization.objects.filter(
                category=category, 
                is_active=True
            ).count()
            self.stdout.write(
                self.style.SUCCESS(f"   - {category}: {count} specializations")
            )
        
        # Show certification requirements
        cert_required = TechnicianSpecialization.objects.filter(
            certification_required=True, 
            is_active=True
        ).count()
        total_specs = TechnicianSpecialization.objects.filter(is_active=True).count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f"\n🎓 Certification requirements: {cert_required}/{total_specs} "
                f"({cert_required/total_specs*100:.1f}%) require certification"
            )
        ) 