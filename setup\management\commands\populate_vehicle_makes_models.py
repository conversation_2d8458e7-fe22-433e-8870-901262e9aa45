from django.core.management.base import BaseCommand
from django.db import transaction
from setup.models import ServiceCenter, ServiceCenterMakeModel
import csv
import os

class Command(BaseCommand):
    help = 'Populates vehicle makes and models for service centers'

    def add_arguments(self, parser):
        parser.add_argument(
            '--center-code',
            dest='center_code',
            help='Specific service center code to populate',
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            dest='clear',
            help='Clear existing makes/models before populating',
        )
        parser.add_argument(
            '--csv',
            dest='csv_file',
            help='CSV file path containing makes and models to import',
        )
        parser.add_argument(
            '--region',
            dest='region',
            choices=['global', 'us', 'eu', 'asia', 'middle_east'],
            default='global',
            help='Region-specific vehicle makes to include',
        )

    def handle(self, *args, **options):
        with transaction.atomic():
            # Common vehicle makes and models by region
            common_makes_models = self._get_makes_models_by_region(options['region'])
            
            # If CSV file provided, load makes/models from it
            if options.get('csv_file'):
                if os.path.exists(options['csv_file']):
                    common_makes_models = self._load_from_csv(options['csv_file'])
                    self.stdout.write(f"Loaded vehicle makes/models from {options['csv_file']}")
                else:
                    self.stdout.write(self.style.ERROR(f"CSV file not found: {options['csv_file']}"))
                    return
            
            # Get service centers to populate
            if options.get('center_code'):
                service_centers = ServiceCenter.objects.filter(code=options['center_code'])
                if not service_centers:
                    self.stdout.write(self.style.ERROR(f"No service center found with code: {options['center_code']}"))
                    return
            else:
                service_centers = ServiceCenter.objects.filter(serves_all_vehicle_makes=False)
            
            if not service_centers:
                self.stdout.write(self.style.WARNING("No service centers found that need vehicle makes configuration"))
                return
                
            self.stdout.write(f"Found {service_centers.count()} service centers to update")
            
            for service_center in service_centers:
                self.stdout.write(f"Populating makes/models for {service_center.name}")
                
                # Clear existing makes/models if requested
                if options.get('clear'):
                    count = ServiceCenterMakeModel.objects.filter(service_center=service_center).delete()[0]
                    self.stdout.write(f"Cleared {count} existing makes/models")
                
                # Populate with makes/models
                for make, models in common_makes_models.items():
                    # Add entry for the make with all its models (blank model name)
                    ServiceCenterMakeModel.objects.update_or_create(
                        tenant_id=service_center.tenant_id,
                        service_center=service_center,
                        make=make,
                        model='',  # Empty model means all models of this make
                        defaults={'is_active': True}
                    )
                    
                    # Add specific models if detailed configuration desired
                    if options.get('detailed', False):
                        for model in models:
                            ServiceCenterMakeModel.objects.update_or_create(
                                tenant_id=service_center.tenant_id,
                                service_center=service_center,
                                make=make,
                                model=model,
                                defaults={'is_active': True}
                            )
                
                self.stdout.write(self.style.SUCCESS(f"Successfully populated makes/models for {service_center.name}"))
            
            self.stdout.write(self.style.SUCCESS('Successfully populated vehicle makes and models'))
    
    def _load_from_csv(self, csv_file_path):
        """Load vehicle makes and models from CSV file"""
        makes_models = {}
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.reader(file)
                for row in reader:
                    if len(row) >= 2:
                        make = row[0].strip()
                        model = row[1].strip() if len(row) > 1 else ""
                        
                        if make not in makes_models:
                            makes_models[make] = []
                            
                        if model and model not in makes_models[make]:
                            makes_models[make].append(model)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error reading CSV file: {str(e)}"))
            
        return makes_models
    
    def _get_makes_models_by_region(self, region):
        """Get region-specific vehicle makes and models"""
        # Global makes present in most regions
        global_makes = {
            'Toyota': ['Corolla', 'Camry', 'RAV4', 'Land Cruiser', 'Hilux'],
            'Honda': ['Civic', 'Accord', 'CR-V', 'Pilot'],
            'Ford': ['F-150', 'Escape', 'Explorer', 'Focus', 'Mustang'],
            'Nissan': ['Altima', 'Sentra', 'Rogue', 'Pathfinder', 'Patrol'],
            'Hyundai': ['Elantra', 'Sonata', 'Tucson', 'Santa Fe'],
            'Kia': ['Optima', 'Sportage', 'Sorento', 'Forte'],
        }
        
        # Middle East specific makes/models
        if region == 'middle_east':
            return {
                **global_makes,
                'Toyota': ['Land Cruiser', 'Hilux', 'Fortuner', 'Camry', 'Corolla', 'Prado'],
                'Nissan': ['Patrol', 'Pathfinder', 'Altima', 'Sunny', 'X-Trail'],
                'Lexus': ['LX', 'GX', 'RX', 'ES', 'IS'],
                'Infiniti': ['QX80', 'QX60', 'Q50', 'QX50'],
                'Mercedes-Benz': ['G-Class', 'S-Class', 'E-Class', 'GLS', 'GLE'],
                'BMW': ['X5', 'X7', '7 Series', '5 Series', 'X6'],
                'GMC': ['Yukon', 'Sierra', 'Terrain', 'Acadia'],
                'Chevrolet': ['Tahoe', 'Suburban', 'Silverado', 'Camaro', 'Impala'],
                'Mitsubishi': ['Pajero', 'Outlander', 'Lancer', 'ASX'],
                'Haval': ['H6', 'H9', 'Jolion'],
                'MG': ['ZS', 'HS', 'RX8', 'GT'],
            }
        
        # US specific makes/models
        elif region == 'us':
            return {
                **global_makes,
                'Chevrolet': ['Silverado', 'Malibu', 'Equinox', 'Suburban', 'Tahoe', 'Corvette'],
                'Jeep': ['Wrangler', 'Grand Cherokee', 'Cherokee', 'Compass'],
                'Dodge': ['Ram', 'Charger', 'Challenger', 'Durango'],
                'Cadillac': ['Escalade', 'XT5', 'CT5', 'XT4'],
                'Buick': ['Enclave', 'Encore', 'Envision'],
                'Lincoln': ['Navigator', 'Aviator', 'Corsair'],
                'Tesla': ['Model 3', 'Model Y', 'Model S', 'Model X'],
                'Chrysler': ['300', 'Pacifica'],
            }
        
        # EU specific makes/models
        elif region == 'eu':
            return {
                **global_makes,
                'Volkswagen': ['Golf', 'Polo', 'Tiguan', 'Passat', 'T-Roc'],
                'Audi': ['A3', 'A4', 'Q3', 'Q5', 'A6'],
                'BMW': ['3 Series', '5 Series', 'X3', 'X5', '1 Series'],
                'Mercedes-Benz': ['A-Class', 'C-Class', 'E-Class', 'GLA', 'GLC'],
                'Skoda': ['Octavia', 'Fabia', 'Kodiaq', 'Superb'],
                'Renault': ['Clio', 'Captur', 'Megane', 'Kadjar'],
                'Peugeot': ['208', '3008', '2008', '508'],
                'Citroen': ['C3', 'C4', 'C5 Aircross'],
                'Fiat': ['500', 'Panda', 'Tipo'],
                'Opel': ['Corsa', 'Astra', 'Crossland', 'Insignia'],
                'Volvo': ['XC60', 'XC40', 'V60', 'S90'],
            }
            
        # Asia specific makes/models
        elif region == 'asia':
            return {
                **global_makes,
                'Toyota': ['Corolla', 'Camry', 'Vios', 'Fortuner', 'Innova'],
                'Honda': ['City', 'Civic', 'Accord', 'CR-V', 'HR-V'],
                'Suzuki': ['Swift', 'Dzire', 'Ertiga', 'Baleno', 'Vitara'],
                'Proton': ['Saga', 'X70', 'X50', 'Persona'],
                'Perodua': ['Myvi', 'Axia', 'Bezza', 'Aruz'],
                'Nissan': ['Almera', 'X-Trail', 'Navara', 'Teana'],
                'Mitsubishi': ['Xpander', 'Pajero', 'Triton', 'ASX'],
                'Hyundai': ['Accent', 'Elantra', 'Tucson', 'Santa Fe', 'Creta'],
                'Kia': ['Seltos', 'Sportage', 'Cerato', 'Rio'],
                'Mazda': ['CX-5', 'CX-3', 'Mazda3', 'Mazda2'],
                'Isuzu': ['D-Max', 'MU-X'],
            }
        
        # Default global makes
        return {
            'Toyota': ['Corolla', 'Camry', 'RAV4', 'Land Cruiser', 'Hilux'],
            'Honda': ['Civic', 'Accord', 'CR-V', 'Pilot'],
            'Ford': ['F-150', 'Escape', 'Explorer', 'Focus', 'Mustang'],
            'Chevrolet': ['Silverado', 'Malibu', 'Equinox', 'Suburban'],
            'Nissan': ['Altima', 'Sentra', 'Rogue', 'Pathfinder', 'Patrol'],
            'Hyundai': ['Elantra', 'Sonata', 'Tucson', 'Santa Fe'],
            'Kia': ['Optima', 'Sportage', 'Sorento', 'Forte'],
            'BMW': ['3 Series', '5 Series', 'X3', 'X5'],
            'Mercedes-Benz': ['C-Class', 'E-Class', 'GLE', 'S-Class'],
            'Audi': ['A4', 'A6', 'Q5', 'Q7'],
            'Volkswagen': ['Golf', 'Passat', 'Tiguan', 'Jetta'],
            'Mazda': ['Mazda3', 'Mazda6', 'CX-5', 'CX-9'],
            'Subaru': ['Outback', 'Forester', 'Impreza', 'Crosstrek'],
            'Lexus': ['RX', 'ES', 'NX', 'IS'],
            'Volvo': ['XC90', 'XC60', 'S60', 'V60'],
        } 