# Generated by Django 4.2.20 on 2025-05-08 09:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0002_transferorder_items_count_transfer'),
        ('setup', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='servicecenter',
            name='primary_warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_for_service_centers', to='warehouse.location', verbose_name='Primary Warehouse'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='secondary_warehouses',
            field=models.ManyToManyField(blank=True, related_name='secondary_for_service_centers', to='warehouse.location', verbose_name='Secondary Warehouses'),
        ),
    ]
