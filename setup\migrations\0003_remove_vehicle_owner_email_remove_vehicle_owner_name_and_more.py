# Generated by Django 4.2.20 on 2025-05-08 12:49

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('setup', '0002_servicecenter_primary_warehouse_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='vehicle',
            name='owner_email',
        ),
        migrations.RemoveField(
            model_name='vehicle',
            name='owner_name',
        ),
        migrations.RemoveField(
            model_name='vehicle',
            name='owner_phone',
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('first_name', models.CharField(max_length=100, verbose_name='First Name')),
                ('second_name', models.CharField(blank=True, max_length=100, verbose_name='Second Name')),
                ('third_name', models.CharField(blank=True, max_length=100, verbose_name='Third Name')),
                ('last_name', models.CharField(max_length=100, verbose_name='Last Name')),
                ('customer_type', models.CharField(choices=[('individual', 'Individual'), ('corporate', 'Corporate')], default='individual', max_length=20, verbose_name='Customer Type')),
                ('national_id', models.CharField(blank=True, max_length=50, verbose_name='National ID')),
                ('passport_number', models.CharField(blank=True, max_length=50, verbose_name='Passport Number')),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=10, verbose_name='Gender')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='Date of Birth')),
                ('company_name', models.CharField(blank=True, max_length=255, verbose_name='Company Name')),
                ('company_registration', models.CharField(blank=True, max_length=100, verbose_name='Company Registration')),
                ('phone', models.CharField(max_length=50, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='City')),
                ('state', models.CharField(blank=True, max_length=100, verbose_name='State/Province')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='Postal Code')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='Country')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('service_center', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customers', to='setup.servicecenter', verbose_name='Service Center')),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
                'ordering': ['first_name', 'last_name'],
            },
        ),
        migrations.AddField(
            model_name='vehicle',
            name='drivers',
            field=models.ManyToManyField(blank=True, related_name='driven_vehicles', to='setup.customer', verbose_name='Drivers'),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='owned_vehicles', to='setup.customer', verbose_name='Owner'),
        ),
    ]
