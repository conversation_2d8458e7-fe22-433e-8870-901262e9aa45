# Generated by Django 4.2.20 on 2025-05-08 14:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('billing', '0002_customerclassification_customerclassificationhistory_and_more'),
        ('setup', '0003_remove_vehicle_owner_email_remove_vehicle_owner_name_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='customer',
            name='second_name',
        ),
        migrations.RemoveField(
            model_name='customer',
            name='third_name',
        ),
        migrations.AddField(
            model_name='customer',
            name='alternative_phone',
            field=models.CharField(blank=True, max_length=20, verbose_name='Alternative Phone'),
        ),
        migrations.AddField(
            model_name='customer',
            name='classification',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customers', to='billing.customerclassification', verbose_name='Classification'),
        ),
        migrations.AddField(
            model_name='customer',
            name='id_number',
            field=models.CharField(blank=True, max_length=100, verbose_name='ID Number'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='first_name',
            field=models.CharField(max_length=255, verbose_name='First Name'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='last_name',
            field=models.CharField(max_length=255, verbose_name='Last Name'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='phone',
            field=models.CharField(max_length=20, verbose_name='Phone'),
        ),
    ]
