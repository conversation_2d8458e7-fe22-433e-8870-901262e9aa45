# Generated by Django 4.2.20 on 2025-05-08 19:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('setup', '0007_vehicleownershiptransfer'),
    ]

    operations = [
        migrations.AddField(
            model_name='servicelevel',
            name='availability_target_percent',
            field=models.DecimalField(decimal_places=2, default=99.5, help_text='Uptime/availability target in percent', max_digits=5, verbose_name='Availability Target (%)'),
        ),
        migrations.AddField(
            model_name='servicelevel',
            name='emergency_response_time_hours',
            field=models.PositiveIntegerField(default=4, help_text='Response time for emergency/critical issues', verbose_name='Emergency Response Time (Hours)'),
        ),
        migrations.AddField(
            model_name='servicelevel',
            name='maintenance_window_hours',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum duration for scheduled maintenance', null=True, verbose_name='Maintenance Window (Hours)'),
        ),
        migrations.AddField(
            model_name='servicelevel',
            name='max_incidents_per_month',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum number of support incidents included in the SLA', null=True, verbose_name='Maximum Incidents per Month'),
        ),
        migrations.AddField(
            model_name='servicelevel',
            name='onsite_response_time_hours',
            field=models.PositiveIntegerField(default=48, help_text='Time to arrive onsite if required', verbose_name='Onsite Response Time (Hours)'),
        ),
        migrations.AddField(
            model_name='servicelevel',
            name='parts_delivery_time_hours',
            field=models.PositiveIntegerField(default=72, help_text='Maximum time for parts delivery', verbose_name='Parts Delivery Time (Hours)'),
        ),
        migrations.AddField(
            model_name='servicelevel',
            name='penalty_per_hour_downtime',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Financial penalty per hour of downtime exceeding SLA', max_digits=10, null=True, verbose_name='Penalty per Hour of Downtime'),
        ),
        migrations.AddField(
            model_name='servicelevel',
            name='scheduled_maintenance_frequency',
            field=models.CharField(blank=True, help_text="Frequency of scheduled maintenance (e.g., 'Monthly', 'Quarterly')", max_length=100, verbose_name='Maintenance Frequency'),
        ),
        migrations.AddField(
            model_name='servicelevel',
            name='service_desk_hours',
            field=models.CharField(default='9AM-5PM, Mon-Fri', help_text='Hours when service desk is available', max_length=100, verbose_name='Service Desk Hours'),
        ),
    ]
