# Generated by Django 4.2.20 on 2025-05-14 12:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0008_enhance_service_level"),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name="customer",
            name="national_id",
        ),
        migrations.RemoveField(
            model_name="customer",
            name="passport_number",
        ),
        migrations.AddField(
            model_name="customer",
            name="id_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("national_id", "National ID"),
                    ("passport", "Passport"),
                    ("residence_permit", "Residence Permit"),
                    ("driving_license", "Driving License"),
                    ("other", "Other"),
                ],
                max_length=20,
                verbose_name="Identification Type",
            ),
        ),
        migrations.AddField(
            model_name="customer",
            name="second_name",
            field=models.Char<PERSON><PERSON>(
                blank=True, max_length=255, verbose_name="Second Name"
            ),
        ),
        migrations.AddField(
            model_name="customer",
            name="third_name",
            field=models.Char<PERSON><PERSON>(
                blank=True, max_length=255, verbose_name="Third Name"
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="customer",
            name="gender",
            field=models.<PERSON>r<PERSON><PERSON>(
                blank=True,
                choices=[("male", "<PERSON>"), ("female", "Female"), ("other", "Other")],
                max_length=10,
                verbose_name="Gender",
            ),
        ),
    ]
