# Generated by Django 4.2.20 on 2025-05-15 10:09

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0009_remove_customer_national_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="VehicleMake",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(db_index=True, verbose_name="Tenant ID"),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="Make Name"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="vehicle_makes",
                        verbose_name="Logo",
                    ),
                ),
                (
                    "country_of_origin",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Country of Origin"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "attributes",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Custom Attributes"
                    ),
                ),
            ],
            options={
                "verbose_name": "Vehicle Make",
                "verbose_name_plural": "Vehicle Makes",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="VehicleModel",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(db_index=True, verbose_name="Tenant ID"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Model Name")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "year_introduced",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Year Introduced"
                    ),
                ),
                (
                    "year_discontinued",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Year Discontinued"
                    ),
                ),
                (
                    "vehicle_class",
                    models.CharField(
                        blank=True,
                        help_text="E.g., Sedan, SUV, Truck, etc.",
                        max_length=50,
                        verbose_name="Vehicle Class",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "attributes",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Custom Attributes"
                    ),
                ),
                (
                    "make",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="models",
                        to="setup.vehiclemake",
                        verbose_name="Make",
                    ),
                ),
            ],
            options={
                "verbose_name": "Vehicle Model",
                "verbose_name_plural": "Vehicle Models",
                "ordering": ["make__name", "name"],
                "unique_together": {("tenant_id", "make", "name")},
            },
        ),
        migrations.AddField(
            model_name="vehicle",
            name="standard_make",
            field=models.ForeignKey(
                blank=True,
                help_text="Reference to standardized make/brand",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="vehicles",
                to="setup.vehiclemake",
                verbose_name="Standard Make",
            ),
        ),
        migrations.AddField(
            model_name="vehicle",
            name="standard_model",
            field=models.ForeignKey(
                blank=True,
                help_text="Reference to standardized model",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="vehicles",
                to="setup.vehiclemodel",
                verbose_name="Standard Model",
            ),
        ),
    ]
