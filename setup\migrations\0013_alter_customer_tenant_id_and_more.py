# Generated by Django 4.2.20 on 2025-06-06 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0012_populate_egyptian_vehicle_data"),
    ]

    operations = [
        migrations.AlterField(
            model_name="customer",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="servicecenter",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="servicecentermakemodel",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="servicehistory",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="vehicle",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="vehiclemake",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="vehiclemodel",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="vehicleownershiptransfer",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
    ]
