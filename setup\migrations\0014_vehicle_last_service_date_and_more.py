# Generated by Django 4.2.20 on 2025-06-06 11:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0013_alter_customer_tenant_id_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="vehicle",
            name="last_service_date",
            field=models.DateField(
                blank=True, null=True, verbose_name="Last Service Date"
            ),
        ),
        migrations.AddField(
            model_name="vehicle",
            name="last_service_odometer",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Odometer reading at last service",
                null=True,
                verbose_name="Last Service Odometer",
            ),
        ),
    ]
