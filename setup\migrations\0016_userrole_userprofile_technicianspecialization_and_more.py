# Generated by Django 4.2.20 on 2025-06-15 15:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("warehouse", "0008_alter_binlocation_tenant_id_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("setup", "0015_company_tenant_id_franchise_tenant_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserRole",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="Role Name")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("franchise", "Franchise Level"),
                            ("company", "Company Level"),
                            ("service_center", "Service Center Level"),
                            ("global", "Global Level"),
                        ],
                        max_length=20,
                        verbose_name="Role Level",
                    ),
                ),
                (
                    "can_create_users",
                    models.BooleanField(default=False, verbose_name="Can Create Users"),
                ),
                (
                    "can_manage_inventory",
                    models.BooleanField(
                        default=False, verbose_name="Can Manage Inventory"
                    ),
                ),
                (
                    "can_manage_work_orders",
                    models.BooleanField(
                        default=False, verbose_name="Can Manage Work Orders"
                    ),
                ),
                (
                    "can_manage_sales",
                    models.BooleanField(default=False, verbose_name="Can Manage Sales"),
                ),
                (
                    "can_manage_customers",
                    models.BooleanField(
                        default=False, verbose_name="Can Manage Customers"
                    ),
                ),
                (
                    "can_view_reports",
                    models.BooleanField(default=False, verbose_name="Can View Reports"),
                ),
                (
                    "can_manage_settings",
                    models.BooleanField(
                        default=False, verbose_name="Can Manage Settings"
                    ),
                ),
                (
                    "can_manage_franchises",
                    models.BooleanField(
                        default=False, verbose_name="Can Manage Franchises"
                    ),
                ),
                (
                    "can_manage_companies",
                    models.BooleanField(
                        default=False, verbose_name="Can Manage Companies"
                    ),
                ),
                (
                    "can_manage_service_centers",
                    models.BooleanField(
                        default=False, verbose_name="Can Manage Service Centers"
                    ),
                ),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=100,
                        help_text="Lower number = higher priority",
                        verbose_name="Priority",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
            ],
            options={
                "verbose_name": "User Role",
                "verbose_name_plural": "User Roles",
                "ordering": ["priority", "name"],
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "employee_id",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Employee ID"
                    ),
                ),
                (
                    "phone",
                    models.CharField(blank=True, max_length=20, verbose_name="Phone"),
                ),
                (
                    "mobile",
                    models.CharField(blank=True, max_length=20, verbose_name="Mobile"),
                ),
                ("address", models.TextField(blank=True, verbose_name="Address")),
                (
                    "city",
                    models.CharField(blank=True, max_length=100, verbose_name="City"),
                ),
                (
                    "emergency_contact",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Emergency Contact"
                    ),
                ),
                (
                    "emergency_phone",
                    models.CharField(
                        blank=True, max_length=20, verbose_name="Emergency Phone"
                    ),
                ),
                (
                    "hire_date",
                    models.DateField(blank=True, null=True, verbose_name="Hire Date"),
                ),
                (
                    "department",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Department"
                    ),
                ),
                (
                    "position",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Position"
                    ),
                ),
                (
                    "salary",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="Salary",
                    ),
                ),
                (
                    "skills",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of technical skills",
                        verbose_name="Skills",
                    ),
                ),
                (
                    "certifications",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of certifications",
                        verbose_name="Certifications",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="users",
                        to="setup.company",
                        verbose_name="Company",
                    ),
                ),
                (
                    "franchise",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="users",
                        to="setup.franchise",
                        verbose_name="Franchise",
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="users",
                        to="setup.userrole",
                        verbose_name="Role",
                    ),
                ),
                (
                    "service_center",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="users",
                        to="setup.servicecenter",
                        verbose_name="Service Center",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "ordering": ["user__first_name", "user__last_name"],
            },
        ),
        migrations.CreateModel(
            name="TechnicianSpecialization",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, verbose_name="Specialization Name"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Category"
                    ),
                ),
                (
                    "certification_required",
                    models.BooleanField(
                        default=False, verbose_name="Certification Required"
                    ),
                ),
                (
                    "certification_body",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Certification Body"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "compatible_service_center_types",
                    models.ManyToManyField(
                        blank=True,
                        related_name="compatible_specializations",
                        to="setup.servicecentertype",
                        verbose_name="Compatible Service Center Types",
                    ),
                ),
            ],
            options={
                "verbose_name": "Technician Specialization",
                "verbose_name_plural": "Technician Specializations",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="TechnicianProfile",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "license_number",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="License Number"
                    ),
                ),
                (
                    "license_expiry",
                    models.DateField(
                        blank=True, null=True, verbose_name="License Expiry"
                    ),
                ),
                (
                    "experience_years",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Years of Experience"
                    ),
                ),
                (
                    "average_job_time",
                    models.DurationField(
                        blank=True, null=True, verbose_name="Average Job Time"
                    ),
                ),
                (
                    "customer_rating",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=3,
                        null=True,
                        verbose_name="Customer Rating",
                    ),
                ),
                (
                    "jobs_completed",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Jobs Completed"
                    ),
                ),
                (
                    "available_for_emergency",
                    models.BooleanField(
                        default=True, verbose_name="Available for Emergency"
                    ),
                ),
                (
                    "max_jobs_per_day",
                    models.PositiveIntegerField(
                        default=5, verbose_name="Max Jobs Per Day"
                    ),
                ),
                (
                    "tools_assigned",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Tools Assigned"
                    ),
                ),
                (
                    "equipment_certified",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Equipment Certified"
                    ),
                ),
                (
                    "specializations",
                    models.ManyToManyField(
                        related_name="technicians",
                        to="setup.technicianspecialization",
                        verbose_name="Specializations",
                    ),
                ),
                (
                    "user_profile",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="technician_profile",
                        to="setup.userprofile",
                        verbose_name="User Profile",
                    ),
                ),
            ],
            options={
                "verbose_name": "Technician Profile",
                "verbose_name_plural": "Technician Profiles",
                "ordering": ["user_profile__user__first_name"],
            },
        ),
        migrations.CreateModel(
            name="UserWarehouseAssignment",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "assignment_type",
                    models.CharField(
                        choices=[
                            ("primary", "Primary Warehouse"),
                            ("secondary", "Secondary Access"),
                            ("temporary", "Temporary Access"),
                        ],
                        default="primary",
                        max_length=20,
                        verbose_name="Assignment Type",
                    ),
                ),
                (
                    "can_receive_inventory",
                    models.BooleanField(
                        default=False, verbose_name="Can Receive Inventory"
                    ),
                ),
                (
                    "can_issue_inventory",
                    models.BooleanField(
                        default=False, verbose_name="Can Issue Inventory"
                    ),
                ),
                (
                    "can_transfer_inventory",
                    models.BooleanField(
                        default=False, verbose_name="Can Transfer Inventory"
                    ),
                ),
                (
                    "can_adjust_inventory",
                    models.BooleanField(
                        default=False, verbose_name="Can Adjust Inventory"
                    ),
                ),
                (
                    "can_view_reports",
                    models.BooleanField(default=True, verbose_name="Can View Reports"),
                ),
                (
                    "valid_from",
                    models.DateField(
                        default=django.utils.timezone.now, verbose_name="Valid From"
                    ),
                ),
                (
                    "valid_until",
                    models.DateField(blank=True, null=True, verbose_name="Valid Until"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "user_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warehouse_assignments",
                        to="setup.userprofile",
                        verbose_name="User Profile",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_assignments",
                        to="warehouse.location",
                        verbose_name="Warehouse",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Warehouse Assignment",
                "verbose_name_plural": "User Warehouse Assignments",
                "ordering": ["assignment_type", "warehouse__name"],
                "unique_together": {("tenant_id", "user_profile", "warehouse")},
            },
        ),
    ]
