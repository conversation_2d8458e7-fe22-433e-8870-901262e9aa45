{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ company.name }} - {% trans "تفاصيل الشركة" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex items-center justify-between">
            <div class="text-white">
                <h1 class="text-3xl font-bold mb-2">{{ company.name }}</h1>
                <p class="text-blue-100">{% trans "تفاصيل الشركة" %}</p>
                <div class="flex items-center mt-2">
                    <span class="px-3 py-1 rounded-full text-sm font-medium {% if company.is_active %}bg-green-500 text-white{% else %}bg-red-500 text-white{% endif %}">
                        {% if company.is_active %}{% trans "نشط" %}{% else %}{% trans "غير نشط" %}{% endif %}
                    </span>
                    <span class="mr-3 text-blue-200">{% trans "كود الشركة" %}: {{ company.code }}</span>
                    {% if company.franchise %}
                    <span class="mr-3 text-blue-200">{% trans "الامتياز" %}: {{ company.franchise.name }}</span>
                    {% endif %}
                </div>
            </div>
            <div class="text-white text-left">
                {% if company.logo %}
                    <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="w-20 h-20 rounded-lg object-cover">
                {% else %}
                    <i class="fas fa-building text-6xl opacity-20"></i>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-3 mb-6">
        <a href="{% url 'setup:company_edit' company.pk %}" 
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-edit ml-2"></i>
            {% trans "تعديل الشركة" %}
        </a>
        <a href="{% url 'setup:company_list' %}" 
           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-arrow-right ml-2"></i>
            {% trans "العودة للقائمة" %}
        </a>
        <a href="{% url 'setup:service_center_create' %}?company={{ company.pk }}" 
           class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-plus ml-2"></i>
            {% trans "إضافة مركز خدمة" %}
        </a>
        {% if company.franchise %}
        <a href="{% url 'setup:franchise_detail' company.franchise.pk %}" 
           class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-crown ml-2"></i>
            {% trans "عرض الامتياز" %}
        </a>
        {% endif %}
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-info-circle ml-3"></i>
                        {% trans "المعلومات الأساسية" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "اسم الشركة" %}</label>
                            <p class="text-gray-900 font-semibold">{{ company.name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "كود الشركة" %}</label>
                            <p class="text-gray-900 font-mono">{{ company.code }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الامتياز التابع له" %}</label>
                            {% if company.franchise %}
                                <a href="{% url 'setup:franchise_detail' company.franchise.pk %}" class="text-blue-600 hover:text-blue-800 font-semibold">{{ company.franchise.name }}</a>
                            {% else %}
                                <p class="text-gray-900">{% trans "غير محدد" %}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "تاريخ التأسيس" %}</label>
                            <p class="text-gray-900">{{ company.founding_date|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الحالة" %}</label>
                            <span class="px-2 py-1 rounded-full text-xs font-medium {% if company.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if company.is_active %}{% trans "نشط" %}{% else %}{% trans "غير نشط" %}{% endif %}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "مستوى الخدمة" %}</label>
                            {% if company.service_level %}
                                <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{{ company.service_level.name }}</span>
                            {% else %}
                                <p class="text-gray-900">{% trans "غير محدد" %}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-green-500 to-green-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-address-book ml-3"></i>
                        {% trans "معلومات الاتصال" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الهاتف" %}</label>
                            <p class="text-gray-900">{{ company.phone|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "البريد الإلكتروني" %}</label>
                            <p class="text-gray-900">{{ company.email|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الموقع الإلكتروني" %}</label>
                            {% if company.website %}
                                <a href="{{ company.website }}" target="_blank" class="text-blue-600 hover:text-blue-800">{{ company.website }}</a>
                            {% else %}
                                <p class="text-gray-900">{% trans "غير محدد" %}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الرمز البريدي" %}</label>
                            <p class="text-gray-900">{{ company.postal_code|default:"غير محدد" }}</p>
                        </div>
                    </div>
                    
                    {% if company.address %}
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "العنوان" %}</label>
                        <p class="text-gray-900">{{ company.address }}</p>
                        {% if company.city or company.state or company.country %}
                        <p class="text-gray-600 text-sm mt-1">
                            {{ company.city }}{% if company.city and company.state %}, {% endif %}{{ company.state }}{% if company.state and company.country %}, {% endif %}{{ company.country }}
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Business Information -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-orange-500 to-orange-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-briefcase ml-3"></i>
                        {% trans "المعلومات التجارية" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الرقم الضريبي" %}</label>
                            <p class="text-gray-900 font-mono">{{ company.tax_id|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "رقم التسجيل" %}</label>
                            <p class="text-gray-900 font-mono">{{ company.registration_number|default:"غير محدد" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            {% if company.notes %}
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-gray-500 to-gray-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-sticky-note ml-3"></i>
                        {% trans "ملاحظات" %}
                    </h3>
                </div>
                <div class="p-6">
                    <p class="text-gray-900 whitespace-pre-wrap">{{ company.notes }}</p>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            
            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-chart-bar ml-3"></i>
                        {% trans "الإحصائيات" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">{% trans "مراكز الخدمة" %}</span>
                            <span class="text-2xl font-bold text-blue-600">{{ service_centers_count|default:"0" }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">{% trans "المستخدمين" %}</span>
                            <span class="text-2xl font-bold text-green-600">{{ users_count|default:"0" }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">{% trans "العملاء" %}</span>
                            <span class="text-2xl font-bold text-orange-600">{{ customers_count|default:"0" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Level Details -->
            {% if company.service_level %}
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-medal ml-3"></i>
                        {% trans "مستوى الخدمة" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm text-gray-600">{% trans "المستوى" %}</span>
                            <p class="font-semibold text-gray-900">{{ company.service_level.name }}</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">{% trans "وقت الاستجابة" %}</span>
                            <p class="text-gray-900">{{ company.service_level.response_time_hours }} {% trans "ساعة" %}</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-600">{% trans "وقت الحل" %}</span>
                            <p class="text-gray-900">{{ company.service_level.resolution_time_hours }} {% trans "ساعة" %}</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-teal-500 to-teal-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-clock ml-3"></i>
                        {% trans "النشاط الأخير" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <div class="text-sm text-gray-600">
                            <i class="fas fa-calendar-plus text-green-500 ml-2"></i>
                            {% trans "تم الإنشاء" %}: {{ company.created_at|date:"d/m/Y H:i" }}
                        </div>
                        <div class="text-sm text-gray-600">
                            <i class="fas fa-calendar-edit text-blue-500 ml-2"></i>
                            {% trans "آخر تحديث" %}: {{ company.updated_at|date:"d/m/Y H:i" }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-rose-500 to-rose-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-bolt ml-3"></i>
                        {% trans "إجراءات سريعة" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <a href="{% url 'setup:service_center_create' %}?company={{ company.pk }}" 
                           class="block w-full bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-lg transition-colors duration-200 text-center">
                            <i class="fas fa-store ml-2"></i>
                            {% trans "إضافة مركز خدمة" %}
                        </a>
                        <a href="{% url 'setup:user_profile_create' %}?company={{ company.pk }}" 
                           class="block w-full bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-lg transition-colors duration-200 text-center">
                            <i class="fas fa-user-plus ml-2"></i>
                            {% trans "إضافة مستخدم" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Centers Section -->
    {% if service_centers %}
    <div class="mt-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-green-500 to-green-600 px-6 py-4">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-store ml-3"></i>
                    {% trans "مراكز الخدمة التابعة" %} ({{ service_centers.count }})
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for service_center in service_centers %}
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-900">{{ service_center.name }}</h4>
                            <span class="px-2 py-1 rounded-full text-xs font-medium {% if service_center.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if service_center.is_active %}{% trans "نشط" %}{% else %}{% trans "غير نشط" %}{% endif %}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">{{ service_center.code }}</p>
                        <p class="text-xs text-gray-500 mb-3">{{ service_center.center_type.name }}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">{% trans "السعة" %}: {{ service_center.capacity }}</span>
                            <a href="{% url 'setup:service_center_detail' service_center.pk %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                {% trans "عرض التفاصيل" %}
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %} 