{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "قائمة الشركات" %}{% endblock %}

{% block extra_css %}
<style>
    .rtl-container {
        direction: rtl;
        text-align: right;
        font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
    }
    
    .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .card-modern {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
    }
    
    .card-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    
    .btn-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .btn-gradient:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-outline {
        border: 2px solid #667eea;
        color: #667eea;
        background: transparent;
        padding: 6px 12px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .btn-outline:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-active {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }
    
    .status-inactive {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }
    
    .search-box {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 10px;
        padding: 12px 16px;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .search-box:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .table-modern {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .table-modern th {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        color: #374151;
        font-weight: 600;
        padding: 16px;
        border: none;
        text-align: right;
    }
    
    .table-modern td {
        padding: 16px;
        border-top: 1px solid #f1f5f9;
        vertical-align: middle;
    }
    
    .table-modern tbody tr:hover {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #64748b;
    }
    
    .empty-state svg {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .pagination-modern .page-link {
        border: none;
        color: #667eea;
        padding: 8px 12px;
        margin: 0 2px;
        border-radius: 6px;
        transition: all 0.3s ease;
    }
    
    .pagination-modern .page-link:hover {
        background: #667eea;
        color: white;
    }
    
    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="rtl-container">
    <!-- Header Section -->
    <div class="gradient-bg text-white p-6 mb-6 rounded-lg">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold mb-2">{% trans "قائمة الشركات" %}</h1>
                <p class="text-blue-100">{% trans "إدارة شركات الامتياز" %}</p>
            </div>
            <div class="flex gap-3">
                <a href="{% url 'setup:company_create' %}" class="btn-gradient">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "إضافة شركة جديدة" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card-modern p-6 mb-6">
        <form method="get" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "البحث" %}</label>
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="{% trans 'البحث في الاسم، الكود، البريد الإلكتروني...' %}"
                           class="search-box">
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "الحالة" %}</label>
                    <select name="status" class="search-box">
                        <option value="">{% trans "جميع الحالات" %}</option>
                        <option value="active" {% if current_status == 'active' %}selected{% endif %}>{% trans "نشط" %}</option>
                        <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>{% trans "غير نشط" %}</option>
                    </select>
                </div>
                
                <!-- Franchise Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "الامتياز" %}</label>
                    <select name="franchise" class="search-box">
                        <option value="">{% trans "جميع الامتيازات" %}</option>
                        {% for franchise in franchises %}
                            <option value="{{ franchise.id }}" {% if current_franchise == franchise.id|stringformat:"s" %}selected{% endif %}>
                                {{ franchise.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Actions -->
                <div class="flex items-end gap-2">
                    <button type="submit" class="btn-gradient">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        {% trans "بحث" %}
                    </button>
                    <a href="{% url 'setup:company_list' %}" class="btn-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        {% trans "إعادة تعيين" %}
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Companies List -->
    {% if companies %}
        <div class="card-modern">
            <div class="table-modern">
                <table class="w-full">
                    <thead>
                        <tr>
                            <th>{% trans "اسم الشركة" %}</th>
                            <th>{% trans "الكود" %}</th>
                            <th>{% trans "الامتياز" %}</th>
                            <th>{% trans "معلومات الاتصال" %}</th>
                            <th>{% trans "الموقع" %}</th>
                            <th>{% trans "مستوى الخدمة" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for company in companies %}
                        <tr>
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-green-600 flex items-center justify-center text-white font-bold">
                                        {{ company.name|first }}
                                    </div>
                                    <div>
                                        <div class="font-semibold text-gray-900">{{ company.name }}</div>
                                        {% if company.logo %}
                                            <div class="text-xs text-gray-500">{% trans "يحتوي على شعار" %}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                    {{ company.code }}
                                </span>
                            </td>
                            <td>
                                <div class="font-medium text-gray-900">{{ company.franchise.name }}</div>
                                <div class="text-xs text-gray-500">{{ company.franchise.code }}</div>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">{{ company.phone }}</div>
                                <div class="text-xs text-gray-500">{{ company.email }}</div>
                                {% if company.website %}
                                    <div class="text-xs text-blue-500">{{ company.website|truncatechars:30 }}</div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">{{ company.city }}</div>
                                <div class="text-xs text-gray-500">{{ company.state }}, {{ company.country }}</div>
                            </td>
                            <td>
                                {% if company.service_level %}
                                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                        {{ company.service_level.name }}
                                    </span>
                                {% else %}
                                    <span class="text-gray-400">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if company.is_active %}
                                    <span class="status-badge status-active">{% trans "نشط" %}</span>
                                {% else %}
                                    <span class="status-badge status-inactive">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="flex gap-2">
                                    <a href="{% url 'setup:company_detail' company.pk %}" 
                                       class="text-blue-600 hover:text-blue-800 p-1 rounded" title="{% trans 'عرض التفاصيل' %}">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                        </svg>
                                    </a>
                                    <a href="{% url 'setup:company_edit' company.pk %}" 
                                       class="text-green-600 hover:text-green-800 p-1 rounded" title="{% trans 'تعديل' %}">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="flex justify-center mt-6">
            <nav class="pagination-modern">
                <ul class="flex items-center gap-1">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_franchise %}&franchise={{ current_franchise }}{% endif %}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"/>
                                </svg>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_franchise %}&franchise={{ current_franchise }}{% endif %}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                </svg>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_franchise %}&franchise={{ current_franchise }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_franchise %}&franchise={{ current_franchise }}{% endif %}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_franchise %}&franchise={{ current_franchise }}{% endif %}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"/>
                                </svg>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="card-modern">
            <div class="empty-state">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="mx-auto">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد شركات" %}</h3>
                <p class="text-gray-500 mb-4">{% trans "لم يتم العثور على أي شركات مطابقة لمعايير البحث" %}</p>
                <a href="{% url 'setup:company_create' %}" class="btn-gradient">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "إضافة شركة جديدة" %}
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}