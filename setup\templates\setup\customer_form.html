{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "إضافة عميل" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">
            {% if form.instance.pk %}
                {% trans "تحديث بيانات العميل" %}
            {% else %}
                {% trans "إضافة عميل جديد" %}
            {% endif %}
        </h1>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "اسم العميل" %} <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="id_name" required
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="{% trans 'أدخل اسم العميل' %}"
                           value="{{ form.name.value|default:'' }}">
                    {% if form.name.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.name.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="id_customer_type" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "نوع العميل" %}
                    </label>
                    <select name="customer_type" id="id_customer_type" 
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{% trans "اختر نوع العميل" %}</option>
                        <option value="individual" {% if form.customer_type.value == 'individual' %}selected{% endif %}>
                            {% trans "فرد" %}
                        </option>
                        <option value="company" {% if form.customer_type.value == 'company' %}selected{% endif %}>
                            {% trans "شركة" %}
                        </option>
                    </select>
                </div>
                
                <div>
                    <label for="id_phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "رقم الهاتف" %}
                    </label>
                    <input type="tel" name="phone_number" id="id_phone_number"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="{% trans 'رقم الهاتف' %}"
                           value="{{ form.phone_number.value|default:'' }}">
                </div>
                
                <div>
                    <label for="id_email" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "البريد الإلكتروني" %}
                    </label>
                    <input type="email" name="email" id="id_email"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="{% trans 'البريد الإلكتروني' %}"
                           value="{{ form.email.value|default:'' }}">
                </div>
                
                <div>
                    <label for="id_national_id" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الرقم القومي" %}
                    </label>
                    <input type="text" name="national_id" id="id_national_id"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="{% trans 'الرقم القومي أو رقم الهوية' %}"
                           value="{{ form.national_id.value|default:'' }}">
                </div>
                
                <div>
                    <label for="id_tax_number" class="block text-sm font-medium text-gray-700 mb-2">
                        {% trans "الرقم الضريبي" %}
                    </label>
                    <input type="text" name="tax_number" id="id_tax_number"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="{% trans 'الرقم الضريبي (للشركات)' %}"
                           value="{{ form.tax_number.value|default:'' }}">
                </div>
            </div>
            
            <div>
                <label for="id_address" class="block text-sm font-medium text-gray-700 mb-2">
                    {% trans "العنوان" %}
                </label>
                <textarea name="address" id="id_address" rows="3" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="{% trans 'العنوان الكامل' %}">{{ form.address.value|default:'' }}</textarea>
            </div>
            
            <div>
                <label for="id_notes" class="block text-sm font-medium text-gray-700 mb-2">
                    {% trans "ملاحظات" %}
                </label>
                <textarea name="notes" id="id_notes" rows="3" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="{% trans 'ملاحظات إضافية (اختيارية)' %}">{{ form.notes.value|default:'' }}</textarea>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" name="is_active" id="id_is_active" 
                       {% if form.is_active.value != False %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="id_is_active" class="mr-2 block text-sm text-gray-900">
                    {% trans "عميل نشط" %}
                </label>
            </div>
            
            <div class="flex justify-end space-x-4">
                <a href="{% url 'setup:dashboard' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors">
                    {% trans "إلغاء" %}
                </a>
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors">
                    {% if form.instance.pk %}
                        {% trans "تحديث" %}
                    {% else %}
                        {% trans "إضافة" %}
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 