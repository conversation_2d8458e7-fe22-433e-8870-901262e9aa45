{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
.search-form {
    background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-4" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-gray-800">{{ page_title }}</h1>
            <p class="text-muted">{{ page_subtitle }}</p>
        </div>
        <a href="{% url 'setup:customer_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans "إضافة عميل جديد" %}
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card search-form text-white mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">{% trans "البحث" %}</label>
                    <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                           placeholder="{% trans 'البحث في العملاء...' %}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "الحالة" %}</label>
                    <select class="form-control" name="status">
                        <option value="">{% trans "جميع الحالات" %}</option>
                        <option value="active" {% if current_status == 'active' %}selected{% endif %}>{% trans "نشط" %}</option>
                        <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>{% trans "غير نشط" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "نوع العميل" %}</label>
                    <select class="form-control" name="customer_type">
                        <option value="">{% trans "جميع الأنواع" %}</option>
                        {% for type_key, type_name in customer_types %}
                            <option value="{{ type_key }}" {% if current_customer_type == type_key %}selected{% endif %}>
                                {{ type_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-light me-2">
                        <i class="fas fa-search"></i> {% trans "بحث" %}
                    </button>
                    <a href="{% url 'setup:customer_list' %}" class="btn btn-outline-light">
                        <i class="fas fa-times"></i> {% trans "مسح" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                {% trans "قائمة العملاء" %} ({{ customers|length }} {% trans "عميل" %})
            </h6>
        </div>
        <div class="card-body">
            {% if customers %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>{% trans "الاسم" %}</th>
                                <th>{% trans "الهاتف" %}</th>
                                <th>{% trans "البريد الإلكتروني" %}</th>
                                <th>{% trans "نوع العميل" %}</th>
                                <th>{% trans "المدينة" %}</th>
                                <th>{% trans "الحالة" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                            <tr>
                                <td>
                                    <strong>{{ customer.full_name }}</strong>
                                    {% if customer.company_name %}
                                        <br><small class="text-muted">{{ customer.company_name }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ customer.phone }}
                                    {% if customer.alternative_phone %}
                                        <br><small class="text-muted">{{ customer.alternative_phone }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ customer.email|default:"-" }}</td>
                                <td>
                                    <span class="badge bg-info">{{ customer.get_customer_type_display }}</span>
                                </td>
                                <td>{{ customer.city|default:"-" }}</td>
                                <td>
                                    {% if customer.is_active %}
                                        <span class="badge bg-success">{% trans "نشط" %}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'setup:customer_detail' customer.pk %}" 
                                           class="btn btn-sm btn-outline-info" title="{% trans 'عرض التفاصيل' %}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'setup:customer_edit' customer.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="{% trans 'تعديل' %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="{% trans 'Page navigation' %}">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_customer_type %}&customer_type={{ current_customer_type }}{% endif %}">{% trans "الأولى" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_customer_type %}&customer_type={{ current_customer_type }}{% endif %}">{% trans "السابقة" %}</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_customer_type %}&customer_type={{ current_customer_type }}{% endif %}">{% trans "التالية" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_customer_type %}&customer_type={{ current_customer_type }}{% endif %}">{% trans "الأخيرة" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "لا يوجد عملاء" %}</h5>
                    <p class="text-muted">{% trans "ابدأ بإضافة عميل جديد" %}</p>
                    <a href="{% url 'setup:customer_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "إضافة عميل جديد" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 