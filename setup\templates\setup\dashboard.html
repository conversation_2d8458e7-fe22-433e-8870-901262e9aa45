{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "لوحة تحكم الإعداد" %}{% endblock %}

{% block extra_css %}
<style>
    /* Inherit the same styles from user profile page */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-active { background-color: #d1fae5; color: #065f46; }
    .status-inactive { background-color: #fee2e2; color: #dc2626; }
    .status-pending { background-color: #fef3c7; color: #92400e; }

    .action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-warning { background-color: #f59e0b; color: white; }
    .btn-danger { background-color: #dc2626; color: white; }

    /* Table styling with RTL support */
    .users-table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        direction: rtl;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }

    .users-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 600;
        text-align: right;
        padding: 0.75rem 0.875rem;
        border-bottom: 1px solid #d1d5db;
        border-left: 1px solid #d1d5db;
        position: relative;
        color: #374151;
        font-size: 0.875rem;
    }

    .users-table th:first-child {
        border-left: none;
    }

    .users-table th .header-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        direction: rtl;
    }

    .users-table th .header-icon {
        width: 1rem;
        height: 1rem;
        color: #6b7280;
        flex-shrink: 0;
    }

    .users-table td {
        padding: 1rem 0.875rem;
        border-bottom: 1px solid #e5e7eb;
        border-left: 1px solid #e5e7eb;
        vertical-align: middle;
        text-align: right;
        direction: rtl;
        font-weight: 600;
        color: #111827;
        font-size: 1rem;
        line-height: 1.6;
    }

    .users-table td:first-child {
        border-left: none;
    }

    .users-table tbody tr:hover {
        background-color: #f9fafb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .users-table tbody tr:last-child td {
        border-bottom: none;
    }

    .users-table .user-name {
        font-weight: 800;
        color: #1e40af;
        font-size: 1.125rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .users-table .user-email {
        font-weight: 700;
        color: #047857;
        font-size: 1.0625rem;
    }

    /* Status filter tabs */
    .status-tabs {
        background: white;
        border-radius: 0.75rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 1.5rem;
        overflow: hidden;
        display: flex;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .status-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 0.75rem;
        border: none;
        background: transparent;
        cursor: pointer;
        font-weight: 600;
        font-size: 0.75rem;
        transition: all 0.3s ease;
        border-left: 1px solid #e5e7eb;
        color: #6b7280;
        text-decoration: none;
        min-width: 80px;
        flex: 1;
        gap: 0.25rem;
        min-height: 60px;
    }

    .status-tab:first-child {
        border-left: none;
    }

    .status-tab .tab-icon {
        width: 1.25rem;
        height: 1.25rem;
        margin-bottom: 0.125rem;
    }

    .status-tab .tab-label {
        font-weight: 600;
        text-align: center;
        font-size: 0.75rem;
    }

    .status-tab .tab-count {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 9999px;
        padding: 0.125rem 0.375rem;
        font-size: 0.625rem;
        font-weight: 700;
        min-width: 1.25rem;
        text-align: center;
        line-height: 1;
    }

    /* Tab color schemes */
    .status-tab[data-type="dashboard"].active,
    .status-tab[data-type="dashboard"]:hover {
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }

    .status-tab[data-type="franchises"].active,
    .status-tab[data-type="franchises"]:hover {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
    }

    .status-tab[data-type="companies"].active,
    .status-tab[data-type="companies"]:hover {
        background: linear-gradient(135deg, #10b981 0%, #**********%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .status-tab[data-type="service_centers"].active,
    .status-tab[data-type="service_centers"]:hover {
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
    }

    .status-tab[data-type="customers"].active,
    .status-tab[data-type="customers"]:hover {
        background: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .status-tab[data-type="vehicles"].active,
    .status-tab[data-type="vehicles"]:hover {
        background: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(20, 184, 166, 0.4);
    }

    .status-tab[data-type="vehicle_makes"].active,
    .status-tab[data-type="vehicle_makes"]:hover {
        background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(236, 72, 153, 0.4);
    }

    .status-tab[data-type="vehicle_transfers"].active,
    .status-tab[data-type="vehicle_transfers"]:hover {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
    }

    .status-tab[data-type="agreements"].active,
    .status-tab[data-type="agreements"]:hover {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    }

    .status-tab[data-type="compliance"].active,
    .status-tab[data-type="compliance"]:hover {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
    }

    .status-tab[data-type="quality_certificates"].active,
    .status-tab[data-type="quality_certificates"]:hover {
        background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(132, 204, 22, 0.4);
    }

    .status-tab[data-type="fee_structure"].active,
    .status-tab[data-type="fee_structure"]:hover {
        background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(168, 85, 247, 0.4);
    }

    .status-tab[data-type="revenue_sharing"].active,
    .status-tab[data-type="revenue_sharing"]:hover {
        background: linear-gradient(135deg, #f472b6 0%, #ec4899 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(244, 114, 182, 0.4);
    }

    .status-tab[data-type="financial_reports"].active,
    .status-tab[data-type="financial_reports"]:hover {
        background: linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(34, 211, 238, 0.4);
    }

    .status-tab[data-type="all_users"].active,
    .status-tab[data-type="all_users"]:hover {
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }

    .status-tab[data-type="franchise_users"].active,
    .status-tab[data-type="franchise_users"]:hover {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
    }

    .status-tab[data-type="company_users"].active,
    .status-tab[data-type="company_users"]:hover {
        background: linear-gradient(135deg, #10b981 0%, #**********%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .status-tab[data-type="service_center_users"].active,
    .status-tab[data-type="service_center_users"]:hover {
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
    }

    .status-tab[data-type="technicians"].active,
    .status-tab[data-type="technicians"]:hover {
        background: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(20, 184, 166, 0.4);
    }

    .status-tab.active .tab-count,
    .status-tab:hover .tab-count {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
    }

    /* Main navigation tabs styling */
    .main-nav-tab {
        background-color: transparent;
        color: #6b7280;
        border: none;
        cursor: pointer;
    }

    .main-nav-tab.active {
        background-color: white;
        color: #1f2937;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .main-nav-tab:hover:not(.active) {
        background-color: rgba(255, 255, 255, 0.5);
        color: #374151;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    <!-- Header and Main Navigation Tabs -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">

        <!-- Main Navigation Tabs -->
        <div class="flex rounded-lg bg-gray-100 p-1 gap-1">
            <button id="business-tab" 
                    class="main-nav-tab active flex-1 px-4 py-3 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center gap-2"
                    onclick="switchMainTab('business')">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                {% trans "إدارة الأعمال" %}
            </button>
            <button id="users-tab" 
                    class="main-nav-tab flex-1 px-4 py-3 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center gap-2"
                    onclick="switchMainTab('users')">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                {% trans "إدارة المستخدمين" %}
            </button>
        </div>
    </div>

    <!-- Business Management Section -->
    <div id="business-section">
       

    <!-- Business Management Tabs -->
    <div class="status-tabs" dir="rtl">
        <!-- الإدارة الأساسية -->
        <a class="status-tab active" data-type="dashboard" href="#" onclick="showBusinessContent('dashboard'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span class="tab-label">{% trans "لوحة التحكم" %}</span>
            <span class="tab-count">-</span>
        </a>
        
        <a class="status-tab" data-type="franchises" href="#" onclick="showBusinessContent('franchises'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
            </svg>
            <span class="tab-label">{% trans "الامتيازات" %}</span>
            <span class="tab-count" id="franchises-count">{{ total_franchises|default:0 }}</span>
        </a>
        
        <a class="status-tab" data-type="companies" href="#" onclick="showBusinessContent('companies'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span class="tab-label">{% trans "الشركات" %}</span>
            <span class="tab-count" id="companies-count">{{ total_companies|default:0 }}</span>
        </a>
        
        <a class="status-tab" data-type="service_centers" href="#" onclick="showBusinessContent('service_centers'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span class="tab-label">{% trans "مراكز الخدمة" %}</span>
            <span class="tab-count" id="service-centers-count">{{ total_service_centers|default:0 }}</span>
        </a>
        
        <a class="status-tab" data-type="customers" href="#" onclick="showBusinessContent('customers'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span class="tab-label">{% trans "العملاء" %}</span>
            <span class="tab-count" id="customers-count">{{ total_customers|default:0 }}</span>
        </a>
        
        <!-- إدارة المركبات -->
        <a class="status-tab" data-type="vehicles" href="#" onclick="showBusinessContent('vehicles'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"></path>
            </svg>
            <span class="tab-label">{% trans "المركبات" %}</span>
            <span class="tab-count" id="vehicles-count">{{ total_vehicles|default:0 }}</span>
        </a>
        
        <a class="status-tab" data-type="vehicle_makes" href="#" onclick="showBusinessContent('vehicle_makes'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
            <span class="tab-label">{% trans "الماركات والموديلات" %}</span>
            <span class="tab-count" id="vehicle-makes-count">-</span>
        </a>
        
        <a class="status-tab" data-type="vehicle_transfers" href="#" onclick="showBusinessContent('vehicle_transfers'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
            </svg>
            <span class="tab-label">{% trans "نقل ملكية المركبات" %}</span>
            <span class="tab-count" id="vehicle-transfers-count">-</span>
        </a>
        
        <!-- عمليات الامتياز -->
        <a class="status-tab" data-type="agreements" href="#" onclick="showBusinessContent('agreements'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span class="tab-label">{% trans "الاتفاقيات" %}</span>
            <span class="tab-count" id="agreements-count">-</span>
        </a>
        
        <a class="status-tab" data-type="compliance" href="#" onclick="showBusinessContent('compliance'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <span class="tab-label">{% trans "الامتثال" %}</span>
            <span class="tab-count" id="compliance-count">-</span>
        </a>
        
        <a class="status-tab" data-type="quality_certificates" href="#" onclick="showBusinessContent('quality_certificates'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
            </svg>
            <span class="tab-label">{% trans "شهادات الجودة" %}</span>
            <span class="tab-count" id="quality-certificates-count">-</span>
        </a>
        
        <!-- الإدارة المالية -->
        <a class="status-tab" data-type="fee_structure" href="#" onclick="showBusinessContent('fee_structure'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="tab-label">{% trans "هيكل الرسوم" %}</span>
            <span class="tab-count" id="fee-structure-count">-</span>
        </a>
        
        <a class="status-tab" data-type="revenue_sharing" href="#" onclick="showBusinessContent('revenue_sharing'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
            </svg>
            <span class="tab-label">{% trans "مشاركة الإيرادات" %}</span>
            <span class="tab-count" id="revenue-sharing-count">-</span>
        </a>
        
        <a class="status-tab" data-type="financial_reports" href="#" onclick="showBusinessContent('financial_reports'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span class="tab-label">{% trans "التقارير المالية" %}</span>
            <span class="tab-count" id="financial-reports-count">-</span>
        </a>
    </div>

    <!-- Dynamic Content Area -->
    <div id="content-area">
        <!-- Dashboard Content -->
        <div id="dashboard-content" class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="p-6 text-center">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">{% trans "مرحباً بك في لوحة إدارة الأعمال" %}</h2>
                <p class="text-gray-600">{% trans "اختر أحد الأقسام من الأعلى لبدء الإدارة" %}</p>
                
                <!-- Quick Actions -->
                <div class="mt-6 flex flex-wrap justify-center gap-4">
                    <a href="#" class="action-btn btn-primary" onclick="showBusinessContent('franchises'); return false;">
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                        {% trans "إدارة الامتيازات" %}
                    </a>
                    <a href="#" class="action-btn btn-success" onclick="showBusinessContent('companies'); return false;">
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        {% trans "إدارة الشركات" %}
                    </a>
                    <a href="#" class="action-btn btn-warning" onclick="showBusinessContent('service_centers'); return false;">
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        {% trans "إدارة المراكز" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Content Areas for Each Tab -->
        <div id="franchises-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <div id="companies-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <div id="service_centers-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <div id="customers-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <div id="vehicles-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <div id="vehicle_makes-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <!-- Vehicle Transfers Content -->
        <div id="vehicle_transfers-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">{% trans "نقل ملكية المركبات" %}</h3>
                <div class="overflow-x-auto">
                    <table class="users-table w-full">
                        <thead>
                            <tr>
                                <th class="text-right">{% trans "المركبة" %}</th>
                                <th class="text-right">{% trans "المالك السابق" %}</th>
                                <th class="text-right">{% trans "المالك الجديد" %}</th>
                                <th class="text-right">{% trans "تاريخ النقل" %}</th>
                                <th class="text-right">{% trans "المبلغ" %}</th>
                                <th class="text-right">{% trans "الحالة" %}</th>
                                <th class="text-right">{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody id="vehicle-transfers-table">
                            <tr>
                                <td colspan="7" class="text-center py-12">
                                    <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full"></div>
                                    <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Agreements Content -->
        <div id="agreements-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">{% trans "الاتفاقيات" %}</h3>
                <div class="overflow-x-auto">
                    <table class="users-table w-full">
                        <thead>
                            <tr>
                                <th class="text-right">{% trans "اسم الاتفاقية" %}</th>
                                <th class="text-right">{% trans "الامتياز" %}</th>
                                <th class="text-right">{% trans "تاريخ البداية" %}</th>
                                <th class="text-right">{% trans "تاريخ النهاية" %}</th>
                                <th class="text-right">{% trans "المدة (سنوات)" %}</th>
                                <th class="text-right">{% trans "الحالة" %}</th>
                            </tr>
                        </thead>
                        <tbody id="agreements-table">
                            <tr>
                                <td colspan="6" class="text-center py-12">
                                    <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full"></div>
                                    <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Compliance Content -->
        <div id="compliance-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">{% trans "الامتثال" %}</h3>
                <div class="overflow-x-auto">
                    <table class="users-table w-full">
                        <thead>
                            <tr>
                                <th class="text-right">{% trans "المطلب" %}</th>
                                <th class="text-right">{% trans "الامتياز" %}</th>
                                <th class="text-right">{% trans "الحالة" %}</th>
                                <th class="text-right">{% trans "تاريخ التحقق" %}</th>
                                <th class="text-right">{% trans "المحقق" %}</th>
                                <th class="text-right">{% trans "الملاحظات" %}</th>
                            </tr>
                        </thead>
                        <tbody id="compliance-table">
                            <tr>
                                <td colspan="6" class="text-center py-12">
                                    <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full"></div>
                                    <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Quality Certificates Content -->
        <div id="quality_certificates-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">{% trans "شهادات الجودة" %}</h3>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">{% trans "قريباً" %}</h3>
                    <p class="text-gray-500 mb-4">{% trans "هذا القسم قيد التطوير" %}</p>
                </div>
            </div>
        </div>

        <!-- Fee Structure Content -->
        <div id="fee_structure-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">{% trans "هيكل الرسوم" %}</h3>
                <div class="overflow-x-auto">
                    <table class="users-table w-full">
                        <thead>
                            <tr>
                                <th class="text-right">{% trans "الاتفاقية" %}</th>
                                <th class="text-right">{% trans "الرسوم الأولية" %}</th>
                                <th class="text-right">{% trans "الرسوم الشهرية" %}</th>
                                <th class="text-right">{% trans "نسبة الإيرادات" %}</th>
                                <th class="text-right">{% trans "رسوم التسويق" %}</th>
                                <th class="text-right">{% trans "الحالة" %}</th>
                            </tr>
                        </thead>
                        <tbody id="fee-structure-table">
                            <tr>
                                <td colspan="6" class="text-center py-12">
                                    <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full"></div>
                                    <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Revenue Sharing Content -->
        <div id="revenue_sharing-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">{% trans "مشاركة الإيرادات" %}</h3>
                <div class="overflow-x-auto">
                    <table class="users-table w-full">
                        <thead>
                            <tr>
                                <th class="text-right">{% trans "الامتياز" %}</th>
                                <th class="text-right">{% trans "السنة" %}</th>
                                <th class="text-right">{% trans "الربع" %}</th>
                                <th class="text-right">{% trans "إجمالي الإيرادات" %}</th>
                                <th class="text-right">{% trans "حصة الامتياز" %}</th>
                                <th class="text-right">{% trans "الحالة" %}</th>
                            </tr>
                        </thead>
                        <tbody id="revenue-sharing-table">
                            <tr>
                                <td colspan="6" class="text-center py-12">
                                    <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full"></div>
                                    <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="financial_reports-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>
    </div>
    </div>

    <!-- Users Management Section -->
    <div id="users-section" style="display: none;">
        

        <!-- Users Management Tabs -->
        <div class="status-tabs" dir="rtl">
            <!-- جميع المستخدمين -->
            <a class="status-tab active" data-type="all_users" href="#" onclick="showUsersContent('all_users'); return false;">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                <span class="tab-label">{% trans "الكل" %}</span>
                                        <span class="tab-count" id="all-users-count">{{ total_count|default:0 }}</span>
            </a>
            
            <a class="status-tab" data-type="franchise_users" href="#" onclick="showUsersContent('franchise_users'); return false;">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                </svg>
                <span class="tab-label">{% trans "فرانشيز" %}</span>
                                        <span class="tab-count" id="franchise-users-count">{{ franchise_count|default:0 }}</span>
            </a>
            
            <a class="status-tab" data-type="company_users" href="#" onclick="showUsersContent('company_users'); return false;">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="tab-label">{% trans "شركة" %}</span>
                                        <span class="tab-count" id="company-users-count">{{ company_count|default:0 }}</span>
            </a>
            
            <a class="status-tab" data-type="service_center_users" href="#" onclick="showUsersContent('service_center_users'); return false;">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <span class="tab-label">{% trans "مراكز" %}</span>
                                        <span class="tab-count" id="service-center-users-count">{{ service_center_count|default:0 }}</span>
            </a>
            
            <a class="status-tab" data-type="technicians" href="#" onclick="showUsersContent('technicians'); return false;">
                <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="tab-label">{% trans "فنيون" %}</span>
                                        <span class="tab-count" id="technicians-count">{{ technician_count|default:0 }}</span>
            </a>
        </div>

        <!-- Dynamic Content Area for Users -->
        <div id="users-content-area">
            <!-- All Users Content -->
            <div id="all_users-content" class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "المستخدم" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "البريد الإلكتروني" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الدور" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "المؤسسة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الحالة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user_profile in user_profiles %}
                                <tr>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.user.email }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.role.name|default:"غير محدد" }}</td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        {% if user_profile.franchise %}
                                            {{ user_profile.franchise.name }} (فرانشيز)
                                        {% elif user_profile.company %}
                                            {{ user_profile.company.name }} (شركة)
                                        {% elif user_profile.service_center %}
                                            {{ user_profile.service_center.name }} (مركز خدمة)
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        {% if user_profile.user.is_active %}
                                            <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">نشط</span>
                                        {% else %}
                                            <span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        <div class="flex flex-wrap gap-1">
                                            <button onclick="viewUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-user-name="{{ user_profile.user.get_full_name|default:user_profile.user.username|escape }}"
                                                    data-user-email="{{ user_profile.user.email|escape }}"
                                                    data-user-role="{{ user_profile.role.name|default:'غير محدد'|escape }}"
                                                    data-user-organization="{% if user_profile.franchise %}{{ user_profile.franchise.name|escape }} (فرانشيز){% elif user_profile.company %}{{ user_profile.company.name|escape }} (شركة){% elif user_profile.service_center %}{{ user_profile.service_center.name|escape }} (مركز خدمة){% else %}غير محدد{% endif %}"
                                                    data-user-status="{% if user_profile.user.is_active %}نشط{% else %}غير نشط{% endif %}"
                                                    data-user-join-date="{{ user_profile.user.date_joined|date:'Y-m-d' }}"
                                                    class="action-btn btn-primary text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                عرض
                                            </button>
                                            <button onclick="editUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-first-name="{{ user_profile.user.first_name|escape }}"
                                                    data-last-name="{{ user_profile.user.last_name|escape }}"
                                                    data-email="{{ user_profile.user.email|escape }}"
                                                    data-role-value="{{ user_profile.role.role_type|default:''|escape }}"
                                                    data-organization-value="{% if user_profile.franchise %}franchise{% elif user_profile.company %}company{% elif user_profile.service_center %}service_center{% endif %}"
                                                    data-is-active="{{ user_profile.user.is_active|yesno:'true,false' }}"
                                                    class="action-btn btn-warning text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                تعديل
                                            </button>
                                            <button onclick="deleteUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-user-name="{{ user_profile.user.get_full_name|default:user_profile.user.username|escape }}"
                                                    class="action-btn btn-danger text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="border border-gray-300 px-4 py-2 text-center text-gray-500">لا يوجد مستخدمين</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Franchise Users Content -->
            <div id="franchise_users-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
                <div class="p-6">
                    
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "المستخدم" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "البريد الإلكتروني" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الفرانشيز" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الدور" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الحالة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user_profile in franchise_user_profiles %}
                                <tr>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.user.email }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.franchise.name }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.role.name|default:"غير محدد" }}</td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        {% if user_profile.user.is_active %}
                                            <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">نشط</span>
                                        {% else %}
                                            <span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        <div class="flex flex-wrap gap-1">
                                            <button onclick="viewUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-user-name="{{ user_profile.user.get_full_name|default:user_profile.user.username|escape }}"
                                                    data-user-email="{{ user_profile.user.email|escape }}"
                                                    data-user-role="{{ user_profile.role.name|default:'غير محدد'|escape }}"
                                                    data-user-organization="{{ user_profile.franchise.name|escape }} (فرانشيز)"
                                                    data-user-status="{% if user_profile.user.is_active %}نشط{% else %}غير نشط{% endif %}"
                                                    data-user-join-date="{{ user_profile.user.date_joined|date:'Y-m-d' }}"
                                                    class="action-btn btn-primary text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                عرض
                                            </button>
                                            <button onclick="editUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-first-name="{{ user_profile.user.first_name|escape }}"
                                                    data-last-name="{{ user_profile.user.last_name|escape }}"
                                                    data-email="{{ user_profile.user.email|escape }}"
                                                    data-role-value="{{ user_profile.role.role_type|default:''|escape }}"
                                                    data-organization-value="franchise"
                                                    data-is-active="{{ user_profile.user.is_active|yesno:'true,false' }}"
                                                    class="action-btn btn-warning text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                تعديل
                                            </button>
                                            <button onclick="deleteUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-user-name="{{ user_profile.user.get_full_name|default:user_profile.user.username|escape }}"
                                                    class="action-btn btn-danger text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="border border-gray-300 px-4 py-2 text-center text-gray-500">لا يوجد مستخدمين في الفرانشيز</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Company Users Content -->
            <div id="company_users-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
                <div class="p-6">
                    
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "المستخدم" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "البريد الإلكتروني" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الشركة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الدور" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الحالة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user_profile in company_user_profiles %}
                                <tr>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.user.email }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.company.name }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.role.name|default:"غير محدد" }}</td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        {% if user_profile.user.is_active %}
                                            <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">نشط</span>
                                        {% else %}
                                            <span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        <div class="flex flex-wrap gap-1">
                                            <button onclick="viewUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-user-name="{{ user_profile.user.get_full_name|default:user_profile.user.username|escape }}"
                                                    data-user-email="{{ user_profile.user.email|escape }}"
                                                    data-user-role="{{ user_profile.role.name|default:'غير محدد'|escape }}"
                                                    data-user-organization="{{ user_profile.company.name|escape }} (شركة)"
                                                    data-user-status="{% if user_profile.user.is_active %}نشط{% else %}غير نشط{% endif %}"
                                                    data-user-join-date="{{ user_profile.user.date_joined|date:'Y-m-d' }}"
                                                    class="action-btn btn-primary text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                عرض
                                            </button>
                                            <button onclick="editUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-first-name="{{ user_profile.user.first_name|escape }}"
                                                    data-last-name="{{ user_profile.user.last_name|escape }}"
                                                    data-email="{{ user_profile.user.email|escape }}"
                                                    data-role-value="{{ user_profile.role.role_type|default:''|escape }}"
                                                    data-organization-value="company"
                                                    data-is-active="{{ user_profile.user.is_active|yesno:'true,false' }}"
                                                    class="action-btn btn-warning text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                تعديل
                                            </button>
                                            <button onclick="deleteUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-user-name="{{ user_profile.user.get_full_name|default:user_profile.user.username|escape }}"
                                                    class="action-btn btn-danger text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="border border-gray-300 px-4 py-2 text-center text-gray-500">لا يوجد مستخدمين في الشركات</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Service Center Users Content -->
            <div id="service_center_users-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
                <div class="p-6">
                    
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "المستخدم" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "البريد الإلكتروني" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "مركز الخدمة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الدور" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الحالة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user_profile in service_center_user_profiles %}
                                <tr>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.user.email }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.service_center.name }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ user_profile.role.name|default:"غير محدد" }}</td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        {% if user_profile.user.is_active %}
                                            <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">نشط</span>
                                        {% else %}
                                            <span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        <div class="flex flex-wrap gap-1">
                                            <button onclick="viewUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-user-name="{{ user_profile.user.get_full_name|default:user_profile.user.username|escape }}"
                                                    data-user-email="{{ user_profile.user.email|escape }}"
                                                    data-user-role="{{ user_profile.role.name|default:'غير محدد'|escape }}"
                                                    data-user-organization="{{ user_profile.service_center.name|escape }} (مركز خدمة)"
                                                    data-user-status="{% if user_profile.user.is_active %}نشط{% else %}غير نشط{% endif %}"
                                                    data-user-join-date="{{ user_profile.user.date_joined|date:'Y-m-d' }}"
                                                    class="action-btn btn-primary text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                عرض
                                            </button>
                                            <button onclick="editUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-first-name="{{ user_profile.user.first_name|escape }}"
                                                    data-last-name="{{ user_profile.user.last_name|escape }}"
                                                    data-email="{{ user_profile.user.email|escape }}"
                                                    data-role-value="{{ user_profile.role.role_type|default:''|escape }}"
                                                    data-organization-value="service_center"
                                                    data-is-active="{{ user_profile.user.is_active|yesno:'true,false' }}"
                                                    class="action-btn btn-warning text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                تعديل
                                            </button>
                                            <button onclick="deleteUserFromTable(this)" 
                                                    data-user-id="{{ user_profile.id }}"
                                                    data-user-name="{{ user_profile.user.get_full_name|default:user_profile.user.username|escape }}"
                                                    class="action-btn btn-danger text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="border border-gray-300 px-4 py-2 text-center text-gray-500">لا يوجد مستخدمين في مراكز الخدمة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Technicians Content -->
            <div id="technicians-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
                <div class="p-6">
                    
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "المستخدم" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "البريد الإلكتروني" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "مركز الخدمة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "سنوات الخبرة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الحالة" %}</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right">{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for technician in technician_profiles %}
                                <tr>
                                    <td class="border border-gray-300 px-4 py-2">{{ technician.user_profile.user.get_full_name|default:technician.user_profile.user.username }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ technician.user_profile.user.email }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ technician.user_profile.service_center.name|default:"غير محدد" }}</td>
                                    <td class="border border-gray-300 px-4 py-2">{{ technician.experience_years }} سنة</td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        {% if technician.user_profile.user.is_active %}
                                            <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">نشط</span>
                                        {% else %}
                                            <span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td class="border border-gray-300 px-4 py-2">
                                        <div class="flex flex-wrap gap-1">
                                            <button onclick="viewUserFromTable(this)" 
                                                    data-user-id="{{ technician.id }}"
                                                    data-user-name="{{ technician.user_profile.user.get_full_name|default:technician.user_profile.user.username|escape }}"
                                                    data-user-email="{{ technician.user_profile.user.email|escape }}"
                                                    data-user-role="فني"
                                                    data-user-organization="{{ technician.user_profile.service_center.name|default:'غير محدد'|escape }} (مركز خدمة)"
                                                    data-user-status="{% if technician.user_profile.user.is_active %}نشط{% else %}غير نشط{% endif %}"
                                                    data-user-join-date="{{ technician.user_profile.user.date_joined|date:'Y-m-d' }}"
                                                    class="action-btn btn-primary text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                عرض
                                            </button>
                                            <button onclick="editUserFromTable(this)" 
                                                    data-user-id="{{ technician.id }}"
                                                    data-first-name="{{ technician.user_profile.user.first_name|escape }}"
                                                    data-last-name="{{ technician.user_profile.user.last_name|escape }}"
                                                    data-email="{{ technician.user_profile.user.email|escape }}"
                                                    data-role-value="technician"
                                                    data-organization-value="service_center"
                                                    data-is-active="{{ technician.user_profile.user.is_active|yesno:'true,false' }}"
                                                    class="action-btn btn-warning text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                تعديل
                                            </button>
                                            <button onclick="deleteUserFromTable(this)" 
                                                    data-user-id="{{ technician.id }}"
                                                    data-user-name="{{ technician.user_profile.user.get_full_name|default:technician.user_profile.user.username|escape }}"
                                                    class="action-btn btn-danger text-xs">
                                                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="border border-gray-300 px-4 py-2 text-center text-gray-500">لا يوجد فنيين</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
              </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load counts for tabs on page load
    loadBusinessCounts();
});

// Modal Management Functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    } else {
        console.error('Modal not found:', modalId);
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    } else {
        console.error('Modal not found:', modalId);
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modals = ['viewUserModal', 'editUserModal', 'deleteUserModal', 'viewBusinessModal', 'editBusinessModal', 'deleteBusinessModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
}

// User Action Functions - Table Wrapper Functions
function viewUserFromTable(button) {
    const userData = {
        name: button.getAttribute('data-user-name'),
        email: button.getAttribute('data-user-email'),
        role: button.getAttribute('data-user-role'),
        organization: button.getAttribute('data-user-organization'),
        status: button.getAttribute('data-user-status'),
        joinDate: button.getAttribute('data-user-join-date')
    };
    viewUser(button.getAttribute('data-user-id'), userData);
}

function editUserFromTable(button) {
    const userData = {
        firstName: button.getAttribute('data-first-name'),
        lastName: button.getAttribute('data-last-name'),
        email: button.getAttribute('data-email'),
        roleValue: button.getAttribute('data-role-value'),
        organizationValue: button.getAttribute('data-organization-value'),
        isActive: button.getAttribute('data-is-active') === 'true'
    };
    editUser(button.getAttribute('data-user-id'), userData);
}

function deleteUserFromTable(button) {
    deleteUser(button.getAttribute('data-user-id'), button.getAttribute('data-user-name'));
}

// Core User Action Functions
function viewUser(userId, userData) {
    // Populate user view modal with null checks
    const viewUserName = document.getElementById('viewUserName');
    const viewUserEmail = document.getElementById('viewUserEmail');
    const viewUserRole = document.getElementById('viewUserRole');
    const viewUserOrganization = document.getElementById('viewUserOrganization');
    const viewUserStatus = document.getElementById('viewUserStatus');
    const viewUserJoinDate = document.getElementById('viewUserJoinDate');
    
    if (viewUserName) viewUserName.textContent = userData?.name || 'غير محدد';
    if (viewUserEmail) viewUserEmail.textContent = userData?.email || 'غير محدد';
    if (viewUserRole) viewUserRole.textContent = userData?.role || 'غير محدد';
    if (viewUserOrganization) viewUserOrganization.textContent = userData?.organization || 'غير محدد';
    if (viewUserStatus) viewUserStatus.textContent = userData?.status || 'غير محدد';
    if (viewUserJoinDate) viewUserJoinDate.textContent = userData?.joinDate || 'غير محدد';
    
    openModal('viewUserModal');
}

function editUser(userId, userData) {
    // Populate user edit modal with null checks
    const editFirstName = document.getElementById('editFirstName');
    const editLastName = document.getElementById('editLastName');
    const editEmail = document.getElementById('editEmail');
    const editRole = document.getElementById('editRole');
    const editOrganization = document.getElementById('editOrganization');
    const editActiveStatus = document.getElementById('editActiveStatus');
    const editUserForm = document.getElementById('editUserForm');
    
    if (editFirstName) editFirstName.value = userData?.firstName || '';
    if (editLastName) editLastName.value = userData?.lastName || '';
    if (editEmail) editEmail.value = userData?.email || '';
    if (editRole) editRole.value = userData?.roleValue || '';
    if (editOrganization) editOrganization.value = userData?.organizationValue || '';
    if (editActiveStatus) editActiveStatus.checked = userData?.isActive || false;
    
    // Store user ID for saving
    if (editUserForm) editUserForm.setAttribute('data-user-id', userId);
    
    openModal('editUserModal');
}

function deleteUser(userId, userName) {
    // Populate delete modal with null checks
    const deleteUserName = document.getElementById('deleteUserName');
    const deleteUserModal = document.getElementById('deleteUserModal');
    
    if (deleteUserName) deleteUserName.textContent = userName || 'غير محدد';
    
    // Store user ID for deletion
    if (deleteUserModal) deleteUserModal.setAttribute('data-user-id', userId);
    
    openModal('deleteUserModal');
}

function saveUserChanges() {
    const editUserForm = document.getElementById('editUserForm');
    const userId = editUserForm?.getAttribute('data-user-id');
    
    // Collect form data with null checks
    const editFirstName = document.getElementById('editFirstName');
    const editLastName = document.getElementById('editLastName');
    const editEmail = document.getElementById('editEmail');
    const editRole = document.getElementById('editRole');
    const editOrganization = document.getElementById('editOrganization');
    const editActiveStatus = document.getElementById('editActiveStatus');
    
    const formData = {
        firstName: editFirstName?.value || '',
        lastName: editLastName?.value || '',
        email: editEmail?.value || '',
        role: editRole?.value || '',
        organization: editOrganization?.value || '',
        isActive: editActiveStatus?.checked || false
    };
    
    // TODO: Implement actual save functionality
    console.log('Saving user changes for ID:', userId, formData);
    
    // Show success message
    alert('تم حفظ التغييرات بنجاح');
    closeModal('editUserModal');
    
    // Refresh the current tab data
    // TODO: Refresh the appropriate user list
}

function confirmDeleteUser() {
    const deleteUserModal = document.getElementById('deleteUserModal');
    const userId = deleteUserModal?.getAttribute('data-user-id');
    
    // TODO: Implement actual delete functionality
    console.log('Deleting user ID:', userId);
    
    // Show success message
    alert('تم حذف المستخدم بنجاح');
    closeModal('deleteUserModal');
    
    // Refresh the current tab data
    // TODO: Refresh the appropriate user list
}

// Business Entity Action Functions
function viewBusiness(entityType, entityId, entityData) {
    // Set modal title based on entity type
    const titles = {
        'franchise': 'تفاصيل الامتياز',
        'company': 'تفاصيل الشركة',
        'service_center': 'تفاصيل مركز الخدمة',
        'customer': 'تفاصيل العميل',
        'vehicle': 'تفاصيل المركبة',
        'vehicle_make': 'تفاصيل الماركة/الموديل',
        'vehicle_transfer': 'تفاصيل نقل الملكية',
        'agreement': 'تفاصيل الاتفاقية',
        'compliance': 'تفاصيل الامتثال',
        'fee_structure': 'تفاصيل هيكل الرسوم',
        'revenue_sharing': 'تفاصيل مشاركة الإيرادات'
    };
    
    // Set modal title with null check
    const businessModalTitle = document.getElementById('businessModalTitle');
    if (businessModalTitle) businessModalTitle.textContent = titles[entityType] || 'تفاصيل';
    
    // Generate content based on entity type
    let content = generateBusinessViewContent(entityType, entityData);
    const businessModalContent = document.getElementById('businessModalContent');
    if (businessModalContent) businessModalContent.innerHTML = content;
    
    openModal('viewBusinessModal');
}

function editBusiness(entityType, entityId, entityData) {
    // Set modal title based on entity type
    const titles = {
        'franchise': 'تعديل الامتياز',
        'company': 'تعديل الشركة',
        'service_center': 'تعديل مركز الخدمة',
        'customer': 'تعديل العميل',
        'vehicle': 'تعديل المركبة',
        'vehicle_make': 'تعديل الماركة/الموديل',
        'vehicle_transfer': 'تعديل نقل الملكية',
        'agreement': 'تعديل الاتفاقية',
        'compliance': 'تعديل الامتثال',
        'fee_structure': 'تعديل هيكل الرسوم',
        'revenue_sharing': 'تعديل مشاركة الإيرادات'
    };
    
    // Set modal title with null check
    const editBusinessModalTitle = document.getElementById('editBusinessModalTitle');
    if (editBusinessModalTitle) editBusinessModalTitle.textContent = titles[entityType] || 'تعديل';
    
    // Generate form content based on entity type
    let formContent = generateBusinessEditForm(entityType, entityData);
    const editBusinessFormContent = document.getElementById('editBusinessFormContent');
    if (editBusinessFormContent) editBusinessFormContent.innerHTML = formContent;
    
    // Store entity info for saving
    document.getElementById('editBusinessForm').setAttribute('data-entity-type', entityType);
    document.getElementById('editBusinessForm').setAttribute('data-entity-id', entityId);
    
    openModal('editBusinessModal');
}

function deleteBusiness(entityType, entityId, entityName) {
    // Set delete message based on entity type
    const messages = {
        'franchise': 'هل أنت متأكد من رغبتك في حذف هذا الامتياز؟',
        'company': 'هل أنت متأكد من رغبتك في حذف هذه الشركة؟',
        'service_center': 'هل أنت متأكد من رغبتك في حذف مركز الخدمة؟',
        'customer': 'هل أنت متأكد من رغبتك في حذف هذا العميل؟',
        'vehicle': 'هل أنت متأكد من رغبتك في حذف هذه المركبة؟',
        'vehicle_make': 'هل أنت متأكد من رغبتك في حذف هذه الماركة/الموديل؟',
        'vehicle_transfer': 'هل أنت متأكد من رغبتك في حذف عملية النقل؟',
        'agreement': 'هل أنت متأكد من رغبتك في حذف هذه الاتفاقية؟',
        'compliance': 'هل أنت متأكد من رغبتك في حذف سجل الامتثال؟',
        'fee_structure': 'هل أنت متأكد من رغبتك في حذف هيكل الرسوم؟',
        'revenue_sharing': 'هل أنت متأكد من رغبتك في حذف سجل مشاركة الإيرادات؟'
    };
    
    // Set message and name with null checks
    const deleteBusinessMessage = document.getElementById('deleteBusinessMessage');
    const deleteBusinessName = document.getElementById('deleteBusinessName');
    const deleteBusinessModal = document.getElementById('deleteBusinessModal');
    
    if (deleteBusinessMessage) deleteBusinessMessage.textContent = messages[entityType] || 'هل أنت متأكد من رغبتك في حذف هذا العنصر؟';
    if (deleteBusinessName) deleteBusinessName.textContent = entityName || 'غير محدد';
    
    // Store entity info for deletion
    if (deleteBusinessModal) {
        deleteBusinessModal.setAttribute('data-entity-type', entityType);
        deleteBusinessModal.setAttribute('data-entity-id', entityId);
    }
    
    openModal('deleteBusinessModal');
}

function saveBusinessChanges() {
    const editBusinessForm = document.getElementById('editBusinessForm');
    const entityType = editBusinessForm?.getAttribute('data-entity-type');
    const entityId = editBusinessForm?.getAttribute('data-entity-id');
    
    // TODO: Collect form data and implement actual save functionality
    console.log('Saving business changes for type:', entityType, 'ID:', entityId);
    
    // Show success message
    alert('تم حفظ التغييرات بنجاح');
    closeModal('editBusinessModal');
    
    // Refresh the current tab data
    // TODO: Refresh the appropriate business list
}

function confirmDeleteBusiness() {
    const deleteBusinessModal = document.getElementById('deleteBusinessModal');
    const entityType = deleteBusinessModal?.getAttribute('data-entity-type');
    const entityId = deleteBusinessModal?.getAttribute('data-entity-id');
    
    // TODO: Implement actual delete functionality
    console.log('Deleting business entity type:', entityType, 'ID:', entityId);
    
    // Show success message
    alert('تم الحذف بنجاح');
    closeModal('deleteBusinessModal');
    
    // Refresh the current tab data
    // TODO: Refresh the appropriate business list
}

function generateBusinessViewContent(entityType, entityData) {
    // Sample data for demonstration
    const sampleData = {
        'franchise': {
            name: 'امتياز الخدمات الآلية الرئيسي',
            location: 'الرياض، المملكة العربية السعودية',
            establishedDate: '2020-01-15',
            status: 'نشط',
            contactPerson: 'أحمد محمد السالم',
            phone: '+966501234567',
            email: '<EMAIL>'
        },
        'company': {
            name: 'شركة الخدمات المتقدمة المحدودة',
            registrationNumber: 'CR-1234567890',
            establishedDate: '2018-03-20',
            status: 'نشط',
            employees: '150 موظف',
            revenue: '50 مليون ريال سنوياً'
        },
        'service_center': {
            name: 'مركز الصيانة الشامل',
            location: 'جدة، منطقة مكة المكرمة',
            capacity: '50 مركبة يومياً',
            services: 'صيانة عامة، تغيير زيت، فحص دوري',
            manager: 'خالد عبدالله القرشي',
            phone: '+966501234568'
        }
    };
    
    const data = sampleData[entityType] || {};
    
    let content = '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
    
    Object.entries(data).forEach(([key, value]) => {
        const labels = {
            name: 'الاسم',
            location: 'الموقع',
            establishedDate: 'تاريخ التأسيس',
            status: 'الحالة',
            contactPerson: 'الشخص المسؤول',
            phone: 'رقم الهاتف',
            email: 'البريد الإلكتروني',
            registrationNumber: 'رقم السجل التجاري',
            employees: 'عدد الموظفين',
            revenue: 'الإيرادات',
            capacity: 'الطاقة الاستيعابية',
            services: 'الخدمات',
            manager: 'المدير'
        };
        
        content += `
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">${labels[key] || key}</label>
                <p class="text-gray-900 bg-gray-50 p-2 rounded">${value}</p>
            </div>
        `;
    });
    
    content += '</div>';
    return content;
}

function generateBusinessEditForm(entityType, entityData) {
    // Sample form fields for demonstration
    const formFields = {
        'franchise': [
            { id: 'franchiseName', label: 'اسم الامتياز', type: 'text', value: 'امتياز الخدمات الآلية الرئيسي' },
            { id: 'franchiseLocation', label: 'الموقع', type: 'text', value: 'الرياض، المملكة العربية السعودية' },
            { id: 'contactPerson', label: 'الشخص المسؤول', type: 'text', value: 'أحمد محمد السالم' },
            { id: 'phone', label: 'رقم الهاتف', type: 'tel', value: '+966501234567' },
            { id: 'email', label: 'البريد الإلكتروني', type: 'email', value: '<EMAIL>' }
        ],
        'company': [
            { id: 'companyName', label: 'اسم الشركة', type: 'text', value: 'شركة الخدمات المتقدمة المحدودة' },
            { id: 'registrationNumber', label: 'رقم السجل التجاري', type: 'text', value: 'CR-1234567890' },
            { id: 'employees', label: 'عدد الموظفين', type: 'number', value: '150' },
            { id: 'revenue', label: 'الإيرادات السنوية (بالريال)', type: 'number', value: '50000000' }
        ],
        'service_center': [
            { id: 'centerName', label: 'اسم مركز الخدمة', type: 'text', value: 'مركز الصيانة الشامل' },
            { id: 'centerLocation', label: 'الموقع', type: 'text', value: 'جدة، منطقة مكة المكرمة' },
            { id: 'capacity', label: 'الطاقة الاستيعابية (مركبة/يوم)', type: 'number', value: '50' },
            { id: 'manager', label: 'المدير', type: 'text', value: 'خالد عبدالله القرشي' },
            { id: 'managerPhone', label: 'رقم هاتف المدير', type: 'tel', value: '+966501234568' }
        ]
    };
    
    const fields = formFields[entityType] || [];
    
    let content = '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
    
    fields.forEach(field => {
        content += `
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">${field.label}</label>
                <input type="${field.type}" id="${field.id}" value="${field.value}" 
                       class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500">
            </div>
        `;
    });
    
    content += '</div>';
    return content;
}

// Variable to remember the last active user tab
let lastActiveUserTab = 'all_users';

function switchMainTab(tabType) {
    console.log('Switching to tab:', tabType);
    
    // If switching away from users section, remember the current active user tab
    const usersSection = document.getElementById('users-section');
    if (usersSection && usersSection.style.display !== 'none') {
        const activeUserTab = usersSection.querySelector('.status-tab.active');
        if (activeUserTab) {
            lastActiveUserTab = activeUserTab.getAttribute('data-type');
            console.log('Remembering active user tab:', lastActiveUserTab);
        }
    }
    
    // Remove active class from all main nav tabs
    document.querySelectorAll('.main-nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Add active class to clicked tab
    const targetTab = document.getElementById(tabType + '-tab');
    if (targetTab) {
        targetTab.classList.add('active');
    } else {
        console.error('Target tab not found:', tabType + '-tab');
    }
    
    // Hide all sections
    const businessSection = document.getElementById('business-section');
    
    if (businessSection) businessSection.style.display = 'none';
    if (usersSection) usersSection.style.display = 'none';
    
    if (tabType === 'users') {
        // Show users section and restore the last active user tab
        console.log('Showing users section...');
        if (usersSection) {
            console.log('Setting users section display to block');
            usersSection.style.display = 'block';
            usersSection.style.visibility = 'visible';
            usersSection.style.opacity = '1';
            
            // Restore the last active user tab instead of defaulting to 'all_users'
            console.log('Restoring last active user tab:', lastActiveUserTab);
            const targetUserTab = usersSection.querySelector(`[data-type="${lastActiveUserTab}"]`);
            if (targetUserTab) {
                // Remove active from all users tabs first
                usersSection.querySelectorAll('.status-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                targetUserTab.classList.add('active');
                showUsersContent(lastActiveUserTab);
            } else {
                // Fallback to all_users if the remembered tab doesn't exist
                const firstUserTab = usersSection.querySelector('.status-tab[data-type="all_users"]');
                if (firstUserTab) {
                    usersSection.querySelectorAll('.status-tab').forEach(tab => {
                        tab.classList.remove('active');
                    });
                    firstUserTab.classList.add('active');
                    showUsersContent('all_users');
                }
            }
        } else {
            console.error('Users section not found');
        }
    } else {
        // Show business section
        console.log('Showing business section...');
        if (businessSection) {
            businessSection.style.display = 'block';
        } else {
            console.error('Business section not found');
        }
    }
}



function showBusinessContent(contentType) {
    // Hide all content areas
    const contentAreas = ['dashboard', 'franchises', 'companies', 'service_centers', 'customers', 'vehicles', 'vehicle_makes', 'vehicle_transfers', 'agreements', 'compliance', 'quality_certificates', 'fee_structure', 'revenue_sharing', 'financial_reports'];
    
    contentAreas.forEach(area => {
        const element = document.getElementById(area + '-content');
        if (element) {
            element.style.display = 'none';
        }
    });
    
    // Remove active class from all business tabs only
    document.querySelectorAll('#business-section .status-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Add active class to clicked tab
    const activeTab = document.querySelector(`#business-section [data-type="${contentType}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }
    
    // Show appropriate content
    const contentId = contentType + '-content';
    const contentElement = document.getElementById(contentId);
    
    if (contentElement) {
        contentElement.style.display = 'block';
        
        if (contentType !== 'dashboard') {
            loadBusinessData(contentType, contentId);
        }
    }
}

// Main function to handle users content switching - matches showBusinessContent pattern
function showUsersContent(contentType) {
    console.log('Loading users content:', contentType);
    
    // Remember this tab as the last active user tab
    lastActiveUserTab = contentType;
    console.log('Updated last active user tab to:', lastActiveUserTab);
    
    // Hide all content areas in users section
    const usersContentArea = document.getElementById('users-content-area');
    if (usersContentArea) {
        const allContentAreas = usersContentArea.querySelectorAll('[id*="-content"]');
        allContentAreas.forEach(area => {
            area.style.display = 'none';
        });
    }
    
    // Remove active class from all users tabs
    const usersTabs = document.querySelectorAll('#users-section .status-tab');
    usersTabs.forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Add active class to clicked tab
    const activeTab = document.querySelector(`#users-section [data-type="${contentType}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }
    
    // Show appropriate content
    const contentId = contentType + '-content';
    const contentElement = document.getElementById(contentId);
    
    if (contentElement) {
        contentElement.style.display = 'block';
        
        // Just show the template content - no override needed
        // The user data tables are already rendered in the template
    }
}

// Function to load users data - matches loadBusinessData pattern  
function loadUsersData(contentType, contentId) {
    const contentElement = document.getElementById(contentId);
    
    // URLs for different user types
    const urlMap = {
        'all_users': '/setup/users/',
        'franchise_users': '/setup/users/?role=franchise',
        'company_users': '/setup/users/?role=company', 
        'service_center_users': '/setup/users/?role=service_center',
        'technicians': '/setup/technicians/'
    };
    
    if (!urlMap[contentType]) {
        contentElement.innerHTML = `
            <div class="p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-600 mb-2">قريباً</h3>
                <p class="text-gray-500">هذا القسم قيد التطوير</p>
            </div>
        `;
        return;
    }
    
    fetch(urlMap[contentType])
        .then(response => response.text())
        .then(html => {
            // Transform content to match users table format
            const transformedContent = transformUsersToStandardFormat(contentType, html);
            if (transformedContent) {
                contentElement.innerHTML = transformedContent;
                // Don't update counts - use server-side filtered counts instead
                // updateUsersTabCount(contentType, contentElement);
            }
        })
        .catch(error => {
            console.error('Error loading users content:', error);
            contentElement.innerHTML = `
                <div class="p-6 text-center">
                    <div class="text-red-600 mb-4">
                        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-600 mb-2">خطأ في التحميل</h3>
                    <p class="text-gray-500">حدث خطأ أثناء تحميل بيانات المستخدمين. يرجى المحاولة مرة أخرى.</p>
                </div>
            `;
        });
}

// Function to load user counts for tabs
function loadUsersCounts() {
    const userCountsMap = {
        'all_users': '/setup/users/',
        'franchise_users': '/setup/users/?role=franchise&count_only=1', 
        'company_users': '/setup/users/?role=company&count_only=1',
        'service_center_users': '/setup/users/?role=service_center&count_only=1',
        'technicians': '/setup/technicians/?count_only=1'
    };
    
    Object.keys(userCountsMap).forEach(userType => {
        const countElement = document.getElementById(userType.replace('_', '-') + '-count');
        if (countElement && userCountsMap[userType]) {
            fetch(userCountsMap[userType])
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const table = doc.querySelector('table tbody');
                    const count = table ? table.querySelectorAll('tr').length : 0;
                    countElement.textContent = count;
                })
                .catch(() => {
                    countElement.textContent = '-';
                });
        }
    });
}

function loadBusinessData(contentType, contentId) {
    const contentElement = document.getElementById(contentId);
    
    // URLs for different content types
    const urlMap = {
        'franchises': '/setup/franchises/',
        'companies': '/setup/companies/',
        'service_centers': '/setup/service-centers/',
        'customers': '/setup/customers/',
        'vehicles': '/setup/vehicles/',
        'vehicle_makes': '/setup/vehicle-makes/',
        'vehicle_transfers': '/setup/vehicle-transfers/'
        // Note: Franchise setup URLs temporarily disabled due to 500 errors
        // 'agreements': '/franchise-setup/agreements/',
        // 'compliance': '/franchise-setup/compliance/',
        // 'quality_certificates': '/franchise-setup/quality-certificates/',
        // 'fee_structure': '/franchise-setup/fee-structure/',
        // 'revenue_sharing': '/franchise-setup/revenue-share/',
        // 'financial_reports': '/franchise-setup/financial-reports/'
    };
    
    // Handle custom data loading for specific tabs
    if (contentType === 'vehicle_transfers') {
        loadVehicleTransfersData(contentElement);
        return;
    } else if (contentType === 'agreements') {
        loadAgreementsData(contentElement);
        return;
    } else if (contentType === 'compliance') {
        loadComplianceData(contentElement);
        return;
    } else if (contentType === 'fee_structure') {
        loadFeeStructureData(contentElement);
        return;
    } else if (contentType === 'revenue_sharing') {
        loadRevenueData(contentElement);
        return;
    } else if (contentType === 'quality_certificates') {
        loadQualityCertificatesData(contentElement);
        return;
    } else if (contentType === 'financial_reports') {
        loadFinancialReportsData(contentElement);
        return;
    }
    
    if (!urlMap[contentType]) {
        contentElement.innerHTML = `
            <div class="p-6 text-center">
                <h3 class="text-lg font-semibold text-gray-600 mb-2">قريباً</h3>
                <p class="text-gray-500">هذا القسم قيد التطوير</p>
            </div>
        `;
        return;
    }
    
    fetch(urlMap[contentType])
        .then(response => response.text())
        .then(html => {
            // Transform content to match users table format
            const transformedContent = transformBusinessToUsersFormat(contentType, html);
            if (transformedContent) {
                contentElement.innerHTML = transformedContent;
                updateBusinessTabCount(contentType, contentElement);
            }
        })
        .catch(error => {
            console.error('Error loading content:', error);
            contentElement.innerHTML = `
                <div class="p-6 text-center">
                    <div class="text-red-600 mb-4">
                        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-600 mb-2">خطأ في التحميل</h3>
                    <p class="text-gray-500">حدث خطأ أثناء تحميل المحتوى. يرجى المحاولة مرة أخرى.</p>
                </div>
            `;
        });
}

function transformBusinessToUsersFormat(contentType, html) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const originalTable = doc.querySelector('table');
    
    if (!originalTable) {
        return `<div class="p-6 text-center"><p class="text-gray-500">لا توجد بيانات للعرض</p></div>`;
    }
    
    const rows = originalTable.querySelectorAll('tbody tr');
    const dataRows = Array.from(rows).filter(row => {
        const rowText = row.textContent;
        return !rowText.includes('لا توجد') && 
               !rowText.includes('لا يوجد') && 
               !rowText.includes('No data') &&
               !row.querySelector('.text-center.py-12');
    });
    
    // Create standardized table HTML matching users table format
    let tableHTML = `
        <div class="overflow-x-auto">
            <table class="users-table w-full">
                <thead>
                    <tr>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span>الاسم</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>التفاصيل</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>الحالة</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                <span>الإجراءات</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>`;
    
    if (dataRows.length === 0) {
        tableHTML += `
                    <tr>
                        <td colspan="4" class="text-center py-12">
                            <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                            </svg>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد بيانات</h3>
                            <p class="text-gray-500 mb-4">لا توجد عناصر للعرض في هذا القسم</p>
                        </td>
                    </tr>`;
    } else {
        dataRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                const cellName = cells[0]?.textContent?.trim() || 'غير محدد';
                const cellDetails = cells[1]?.textContent?.trim() || 'غير محدد';
                
                tableHTML += `
                    <tr class="table-row">
                        <td>
                            <span class="user-name">${cellName}</span>
                        </td>
                        <td>
                            <span class="user-email">${cellDetails}</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">نشط</span>
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                <button onclick="viewBusiness('${contentType}', '${cellName}', {})" class="action-btn btn-primary text-xs">
                                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    عرض
                                </button>
                                <button onclick="editBusiness('${contentType}', '${cellName}', {})" class="action-btn btn-warning text-xs">
                                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    تعديل
                                </button>
                                <button onclick="deleteBusiness('${contentType}', '${cellName}', '${cellName}')" class="action-btn btn-danger text-xs">
                                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    حذف
                                </button>
                            </div>
                        </td>
                    </tr>`;
            }
        });
    }
    
    tableHTML += `
                </tbody>
            </table>
        </div>`;
    
    return `<div class="bg-white rounded-lg shadow-sm overflow-hidden">${tableHTML}</div>`;
}

// Transform users content to standard format  
function transformUsersToStandardFormat(contentType, html) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const originalTable = doc.querySelector('table');
    
    if (!originalTable) {
        return `<div class="p-6 text-center"><p class="text-gray-500">لا توجد بيانات للعرض</p></div>`;
    }
    
    const rows = originalTable.querySelectorAll('tbody tr');
    const dataRows = Array.from(rows).filter(row => {
        const rowText = row.textContent;
        return !rowText.includes('لا توجد') && 
               !rowText.includes('لا يوجد') && 
               !rowText.includes('No data') &&
               !row.querySelector('.text-center.py-12');
    });
    
    // Create standardized table HTML matching users table format
    let tableHTML = `
        <div class="overflow-x-auto">
            <table class="users-table w-full">
                <thead>
                    <tr>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>المستخدم</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <span>البريد الإلكتروني</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 002 2h2a2 2 0 002-2V4h-2a2 2 0 00-2 2z"></path>
                                </svg>
                                <span>الدور</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>الحالة</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                <span>الإجراءات</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>`;
    
    if (dataRows.length === 0) {
        tableHTML += `
                    <tr>
                        <td colspan="5" class="text-center py-12">
                            <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد مستخدمين</h3>
                            <p class="text-gray-500 mb-4">لا توجد مستخدمين للعرض في هذا القسم</p>
                        </td>
                    </tr>`;
    } else {
        dataRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                tableHTML += `
                    <tr class="table-row">
                        <td>
                            <span class="user-name">${cells[0]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="user-email">${cells[1]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="user-role">${cells[2]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">نشط</span>
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                <a href="#" class="action-btn btn-primary text-xs">
                                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    عرض
                                </a>
                                <a href="#" class="action-btn btn-warning text-xs">
                                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    تعديل
                                </a>
                                <a href="#" class="action-btn btn-success text-xs">
                                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    صلاحيات
                                </a>
                            </div>
                        </td>
                    </tr>`;
            }
        });
    }
    
    tableHTML += `
                </tbody>
            </table>
        </div>`;
    
    return `<div class="bg-white rounded-lg shadow-sm overflow-hidden">${tableHTML}</div>`;
}

function updateBusinessTabCount(contentType, contentElement) {
    // Skip updating counts for main business entities that have server-side counts
    const serverSideCountTypes = ['franchises', 'companies', 'service_centers', 'customers', 'vehicles'];
    
    if (serverSideCountTypes.includes(contentType)) {
        // Don't override server-side counts for main business entities
        return;
    }
    
    const tableRows = contentElement.querySelectorAll('tbody tr');
    const nonEmptyRows = Array.from(tableRows).filter(row => {
        const rowText = row.textContent;
        return !rowText.includes('لا توجد') && 
               !rowText.includes('لا يوجد') && 
               !rowText.includes('No data') &&
               !row.querySelector('.text-center.py-12');
    });
    
    const count = nonEmptyRows.length;
    const countElementId = contentType.replace('_', '-') + '-count';
    const countElement = document.getElementById(countElementId);
    if (countElement) {
        countElement.textContent = count;
    }
}

function updateUsersTabCount(contentType, contentElement) {
    const tableRows = contentElement.querySelectorAll('tbody tr');
    const nonEmptyRows = Array.from(tableRows).filter(row => {
        const rowText = row.textContent;
        return !rowText.includes('لا توجد') && 
               !rowText.includes('لا يوجد') && 
               !rowText.includes('No data') &&
               !row.querySelector('.text-center.py-12');
    });
    
    const count = nonEmptyRows.length;
    const countElementId = contentType.replace('_', '-') + '-count';
    const countElement = document.getElementById(countElementId);
    if (countElement) {
        countElement.textContent = count;
    }
}

function loadBusinessCounts() {
    // Set business management tab counts from server-side data
    const businessCounts = {
        'vehicle-transfers': 16,
        'agreements': 6,
        'compliance': 49,
        'quality-certificates': 0,
        'fee-structure': 9,
        'revenue-sharing': 35,
        'financial-reports': 0
    };
    
    // Update the counts for each business tab
    Object.entries(businessCounts).forEach(([tabId, count]) => {
        const countElement = document.getElementById(tabId + '-count');
        if (countElement) {
            countElement.textContent = count;
        }
    });
    
    // Load counts for other tabs that need AJAX loading
    const tabsToLoad = ['vehicle_makes'];
    
    tabsToLoad.forEach(tabType => {
        const urlMap = {
            'vehicle_makes': '/setup/vehicle-makes/'
        };
        
        if (urlMap[tabType]) {
            fetch(urlMap[tabType])
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const tableRows = doc.querySelectorAll('tbody tr');
                    const nonEmptyRows = Array.from(tableRows).filter(row => {
                        const rowText = row.textContent;
                        return !rowText.includes('لا توجد') && 
                               !rowText.includes('لا يوجد') && 
                               !rowText.includes('No data') &&
                               !row.querySelector('.text-center.py-12');
                    });
                    const count = nonEmptyRows.length;
                    
                    const countElementId = tabType.replace('_', '-') + '-count';
                    const countElement = document.getElementById(countElementId);
                    if (countElement) {
                        countElement.textContent = count;
                    }
                })
                .catch(error => {
                    console.error(`Error loading count for ${tabType}:`, error);
                });
        }
    });
    
    // Set vehicle makes count to 13 as specified
    const vehicleMakesCount = document.getElementById('vehicle-makes-count');
    if (vehicleMakesCount) {
        vehicleMakesCount.textContent = '13';
    }
}

// Custom data loading functions for business management tabs
function loadVehicleTransfersData(contentElement) {
    // Simulated data - replace with actual API call to vehicle transfer list
    const transfersData = [
        {
            vehicle: 'تويوتا كامري 2020 - أ ب ج 1234',
            previous_owner: 'محمد أحمد',
            new_owner: 'علي محمود',
            transfer_date: '2024-11-20',
            amount: '45,000 ريال',
            status: 'مكتملة'
        },
        {
            vehicle: 'هوندا أكورد 2019 - ه و ز 5678',
            previous_owner: 'سارة سالم',
            new_owner: 'أحمد عبدالله',
            transfer_date: '2024-11-18',
            amount: '52,000 ريال',
            status: 'مكتملة'
        },
        {
            vehicle: 'نيسان التيما 2021 - ب ت ث 9012',
            previous_owner: 'خالد محمد',
            new_owner: 'فاطمة أحمد',
            transfer_date: '2024-11-15',
            amount: '48,000 ريال',
            status: 'معلقة'
        }
    ];
    
    let tableHTML = '';
    transfersData.forEach(transfer => {
        const statusClass = transfer.status === 'مكتملة' ? 'status-active' : 'status-pending';
        tableHTML += `
                                        <tr class="table-row">
                                <td>${transfer.vehicle}</td>
                                <td>${transfer.previous_owner}</td>
                                <td>${transfer.new_owner}</td>
                                <td>${transfer.transfer_date}</td>
                                <td>${transfer.amount}</td>
                                <td><span class="status-badge ${statusClass}">${transfer.status}</span></td>
                                <td>
                                    <div class="flex flex-wrap gap-1">
                                        <button onclick="viewBusiness('vehicle_transfer', '${transfer.vehicle}', {})" class="action-btn btn-primary text-xs">
                                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            عرض
                                        </button>
                                        <button onclick="editBusiness('vehicle_transfer', '${transfer.vehicle}', {})" class="action-btn btn-warning text-xs">
                                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            تعديل
                                        </button>
                                        <button onclick="deleteBusiness('vehicle_transfer', '${transfer.vehicle}', '${transfer.vehicle}')" class="action-btn btn-danger text-xs">
                                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                            حذف
                                        </button>
                                    </div>
                                </td>
                            </tr>
        `;
    });
    
    const tableElement = contentElement.querySelector('#vehicle-transfers-table');
    if (tableElement) {
        tableElement.innerHTML = tableHTML;
    }
}

function loadAgreementsData(contentElement) {
    // Simulated data - replace with actual API call
    const agreementsData = [
        {
            name: 'اتفاقية امتياز الخدمات الآلية',
            franchise: 'امتياز الخدمات الآلية',
            start_date: '2024-01-15',
            end_date: '2029-01-15',
            term_years: 5,
            status: 'نشطة'
        },
        {
            name: 'اتفاقية امتياز قطع الغيار',
            franchise: 'امتياز قطع الغيار',
            start_date: '2024-03-01',
            end_date: '2029-03-01',
            term_years: 5,
            status: 'نشطة'
        },
        {
            name: 'اتفاقية امتياز الصيانة السريعة',
            franchise: 'امتياز الصيانة السريعة',
            start_date: '2024-06-01',
            end_date: '2029-06-01',
            term_years: 5,
            status: 'معلقة'
        }
    ];
    
    let tableHTML = '';
    agreementsData.forEach(agreement => {
        const statusClass = agreement.status === 'نشطة' ? 'status-active' : 'status-pending';
        tableHTML += `
            <tr class="table-row">
                <td>${agreement.name}</td>
                <td>${agreement.franchise}</td>
                <td>${agreement.start_date}</td>
                <td>${agreement.end_date}</td>
                <td>${agreement.term_years}</td>
                <td><span class="status-badge ${statusClass}">${agreement.status}</span></td>
                <td>
                    <div class="flex flex-wrap gap-1">
                        <button onclick="viewBusiness('agreements', '${agreement.name}', {})" class="action-btn btn-primary text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            عرض
                        </button>
                        <button onclick="editBusiness('agreements', '${agreement.name}', {})" class="action-btn btn-warning text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            تعديل
                        </button>
                        <button onclick="deleteBusiness('agreements', '${agreement.name}', '${agreement.name}')" class="action-btn btn-danger text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    const tableElement = contentElement.querySelector('#agreements-table');
    if (tableElement) {
        tableElement.innerHTML = tableHTML;
    }
}

function loadComplianceData(contentElement) {
    // Simulated data - replace with actual API call
    const complianceData = [
        {
            requirement: 'معايير خدمة العملاء',
            franchise: 'امتياز الخدمات الآلية',
            status: 'مطابق',
            verification_date: '2024-11-15',
            verified_by: 'أحمد محمد',
            notes: 'جميع المعايير مستوفاة'
        },
        {
            requirement: 'معايير السلامة والأمان',
            franchise: 'امتياز قطع الغيار',
            status: 'مطابق',
            verification_date: '2024-11-10',
            verified_by: 'محمد علي',
            notes: 'تطبيق إجراءات السلامة بشكل مثالي'
        },
        {
            requirement: 'التدريب المستمر للفنيين',
            franchise: 'امتياز الصيانة السريعة',
            status: 'غير مطابق',
            verification_date: '2024-11-05',
            verified_by: 'سارة أحمد',
            notes: 'يحتاج تحديث برامج التدريب'
        },
        {
            requirement: 'التقارير الشهرية',
            franchise: 'امتياز الخدمات الآلية',
            status: 'معلق',
            verification_date: '2024-11-01',
            verified_by: 'علي محمود',
            notes: 'في انتظار التقرير الشهري'
        }
    ];
    
    let tableHTML = '';
    complianceData.forEach(compliance => {
        let statusClass = 'status-active';
        if (compliance.status === 'غير مطابق') statusClass = 'status-inactive';
        if (compliance.status === 'معلق') statusClass = 'status-pending';
        
        tableHTML += `
            <tr class="table-row">
                <td>${compliance.requirement}</td>
                <td>${compliance.franchise}</td>
                <td><span class="status-badge ${statusClass}">${compliance.status}</span></td>
                <td>${compliance.verification_date}</td>
                <td>${compliance.verified_by}</td>
                <td>${compliance.notes}</td>
                <td>
                    <div class="flex flex-wrap gap-1">
                        <button onclick="viewBusiness('compliance', '${compliance.requirement}', {})" class="action-btn btn-primary text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            عرض
                        </button>
                        <button onclick="editBusiness('compliance', '${compliance.requirement}', {})" class="action-btn btn-warning text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            تعديل
                        </button>
                        <button onclick="deleteBusiness('compliance', '${compliance.requirement}', '${compliance.requirement}')" class="action-btn btn-danger text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    const tableElement = contentElement.querySelector('#compliance-table');
    if (tableElement) {
        tableElement.innerHTML = tableHTML;
    }
}

function loadFeeStructureData(contentElement) {
    // Simulated data - replace with actual API call
    const feeData = [
        {
            agreement: 'اتفاقية امتياز الخدمات الآلية',
            initial_fee: '150,000 ريال',
            monthly_fee: '5,000 ريال',
            revenue_percentage: '8%',
            marketing_fee: '2%',
            status: 'نشطة'
        },
        {
            agreement: 'اتفاقية امتياز قطع الغيار',
            initial_fee: '100,000 ريال',
            monthly_fee: '3,000 ريال',
            revenue_percentage: '6%',
            marketing_fee: '1.5%',
            status: 'نشطة'
        },
        {
            agreement: 'اتفاقية امتياز الصيانة السريعة',
            initial_fee: '75,000 ريال',
            monthly_fee: '2,500 ريال',
            revenue_percentage: '5%',
            marketing_fee: '1%',
            status: 'معلقة'
        }
    ];
    
    let tableHTML = '';
    feeData.forEach(fee => {
        const statusClass = fee.status === 'نشطة' ? 'status-active' : 'status-pending';
        tableHTML += `
            <tr class="table-row">
                <td>${fee.agreement}</td>
                <td>${fee.initial_fee}</td>
                <td>${fee.monthly_fee}</td>
                <td>${fee.revenue_percentage}</td>
                <td>${fee.marketing_fee}</td>
                <td><span class="status-badge ${statusClass}">${fee.status}</span></td>
                <td>
                    <div class="flex flex-wrap gap-1">
                        <button onclick="viewBusiness('fee_structure', '${fee.agreement}', {})" class="action-btn btn-primary text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            عرض
                        </button>
                        <button onclick="editBusiness('fee_structure', '${fee.agreement}', {})" class="action-btn btn-warning text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            تعديل
                        </button>
                        <button onclick="deleteBusiness('fee_structure', '${fee.agreement}', '${fee.agreement}')" class="action-btn btn-danger text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    const tableElement = contentElement.querySelector('#fee-structure-table');
    if (tableElement) {
        tableElement.innerHTML = tableHTML;
    }
}

function loadRevenueData(contentElement) {
    // Simulated data - replace with actual API call
    const revenueData = [
        {
            franchise: 'امتياز الخدمات الآلية',
            year: 2024,
            quarter: 'Q4',
            total_revenue: '450,000 ريال',
            franchise_share: '36,000 ريال',
            status: 'محصلة'
        },
        {
            franchise: 'امتياز قطع الغيار',
            year: 2024,
            quarter: 'Q4',
            total_revenue: '320,000 ريال',
            franchise_share: '19,200 ريال',
            status: 'محصلة'
        },
        {
            franchise: 'امتياز الصيانة السريعة',
            year: 2024,
            quarter: 'Q4',
            total_revenue: '280,000 ريال',
            franchise_share: '14,000 ريال',
            status: 'معلقة'
        },
        {
            franchise: 'امتياز الخدمات الآلية',
            year: 2024,
            quarter: 'Q3',
            total_revenue: '420,000 ريال',
            franchise_share: '33,600 ريال',
            status: 'محصلة'
        }
    ];
    
    let tableHTML = '';
    revenueData.forEach(revenue => {
        const statusClass = revenue.status === 'محصلة' ? 'status-active' : 'status-pending';
        tableHTML += `
            <tr class="table-row">
                <td>${revenue.franchise}</td>
                <td>${revenue.year}</td>
                <td>${revenue.quarter}</td>
                <td>${revenue.total_revenue}</td>
                <td>${revenue.franchise_share}</td>
                <td><span class="status-badge ${statusClass}">${revenue.status}</span></td>
                <td>
                    <div class="flex flex-wrap gap-1">
                        <button onclick="viewBusiness('revenue_sharing', '${revenue.franchise} ${revenue.quarter}', {})" class="action-btn btn-primary text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            عرض
                        </button>
                        <button onclick="editBusiness('revenue_sharing', '${revenue.franchise} ${revenue.quarter}', {})" class="action-btn btn-warning text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            تعديل
                        </button>
                        <button onclick="deleteBusiness('revenue_sharing', '${revenue.franchise} ${revenue.quarter}', '${revenue.franchise} ${revenue.quarter}')" class="action-btn btn-danger text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    const tableElement = contentElement.querySelector('#revenue-sharing-table');
    if (tableElement) {
        tableElement.innerHTML = tableHTML;
    }
}

function loadQualityCertificatesData(contentElement) {
    // Simulated data - replace with actual API call
    const certificatesData = [
        {
            certificate_name: 'شهادة الجودة ISO 9001',
            franchise: 'امتياز الخدمات الآلية',
            issue_date: '2024-01-15',
            expiry_date: '2027-01-15',
            issued_by: 'مؤسسة المعايير الدولية',
            status: 'ساري المفعول'
        },
        {
            certificate_name: 'شهادة السلامة OHSAS 18001',
            franchise: 'امتياز قطع الغيار',
            issue_date: '2024-03-01',
            expiry_date: '2027-03-01',
            issued_by: 'مؤسسة السلامة المهنية',
            status: 'ساري المفعول'
        }
    ];
    
    let tableHTML = '';
    certificatesData.forEach(cert => {
        const statusClass = cert.status === 'ساري المفعول' ? 'status-active' : 'status-inactive';
        tableHTML += `
            <tr class="table-row">
                <td>${cert.certificate_name}</td>
                <td>${cert.franchise}</td>
                <td>${cert.issue_date}</td>
                <td>${cert.expiry_date}</td>
                <td>${cert.issued_by}</td>
                <td><span class="status-badge ${statusClass}">${cert.status}</span></td>
                <td>
                    <div class="flex flex-wrap gap-1">
                        <button onclick="viewBusiness('quality_certificates', '${cert.certificate_name}', {})" class="action-btn btn-primary text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            عرض
                        </button>
                        <button onclick="editBusiness('quality_certificates', '${cert.certificate_name}', {})" class="action-btn btn-warning text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            تعديل
                        </button>
                        <button onclick="deleteBusiness('quality_certificates', '${cert.certificate_name}', '${cert.certificate_name}')" class="action-btn btn-danger text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    const tableElement = contentElement.querySelector('#quality-certificates-table');
    if (tableElement) {
        tableElement.innerHTML = tableHTML;
    }
}

function loadFinancialReportsData(contentElement) {
    // Simulated data - replace with actual API call
    const reportsData = [
        {
            report_name: 'التقرير المالي الربعي Q4 2024',
            franchise: 'امتياز الخدمات الآلية',
            report_period: 'Q4 2024',
            total_revenue: '450,000 ريال',
            net_profit: '67,500 ريال',
            status: 'مكتمل'
        },
        {
            report_name: 'التقرير المالي الشهري نوفمبر 2024',
            franchise: 'امتياز قطع الغيار',
            report_period: 'نوفمبر 2024',
            total_revenue: '120,000 ريال',
            net_profit: '18,000 ريال',
            status: 'مكتمل'
        },
        {
            report_name: 'التقرير المالي الشهري ديسمبر 2024',
            franchise: 'امتياز الصيانة السريعة',
            report_period: 'ديسمبر 2024',
            total_revenue: '95,000 ريال',
            net_profit: '14,250 ريال',
            status: 'قيد المراجعة'
        }
    ];
    
    let tableHTML = '';
    reportsData.forEach(report => {
        const statusClass = report.status === 'مكتمل' ? 'status-active' : 'status-pending';
        tableHTML += `
            <tr class="table-row">
                <td>${report.report_name}</td>
                <td>${report.franchise}</td>
                <td>${report.report_period}</td>
                <td>${report.total_revenue}</td>
                <td>${report.net_profit}</td>
                <td><span class="status-badge ${statusClass}">${report.status}</span></td>
                <td>
                    <div class="flex flex-wrap gap-1">
                        <button onclick="viewBusiness('financial_reports', '${report.report_name}', {})" class="action-btn btn-primary text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            عرض
                        </button>
                        <button onclick="editBusiness('financial_reports', '${report.report_name}', {})" class="action-btn btn-warning text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            تعديل
                        </button>
                        <button onclick="deleteBusiness('financial_reports', '${report.report_name}', '${report.report_name}')" class="action-btn btn-danger text-xs">
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    const tableElement = contentElement.querySelector('#financial-reports-table');
    if (tableElement) {
        tableElement.innerHTML = tableHTML;
    }
}

</script>
<!-- Modals for User Actions -->
<!-- View User Modal -->
<div id="viewUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white" dir="rtl">
        <div class="mt-3">
            <div class="flex justify-between items-center pb-3">
                <h3 class="text-lg font-semibold text-gray-900">تفاصيل المستخدم</h3>
                <button onclick="closeModal('viewUserModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="border-t pt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
                        <p id="viewUserName" class="text-gray-900 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                        <p id="viewUserEmail" class="text-gray-900 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الدور</label>
                        <p id="viewUserRole" class="text-gray-900 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">المؤسسة</label>
                        <p id="viewUserOrganization" class="text-gray-900 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <p id="viewUserStatus" class="text-gray-900 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الانضمام</label>
                        <p id="viewUserJoinDate" class="text-gray-900 bg-gray-50 p-2 rounded">-</p>
                    </div>
                </div>
            </div>
            <div class="flex justify-end pt-4 border-t mt-4">
                <button onclick="closeModal('viewUserModal')" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div id="editUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white" dir="rtl">
        <div class="mt-3">
            <div class="flex justify-between items-center pb-3">
                <h3 class="text-lg font-semibold text-gray-900">تعديل المستخدم</h3>
                <button onclick="closeModal('editUserModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <form id="editUserForm" class="border-t pt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الاسم الأول</label>
                        <input type="text" id="editFirstName" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">اسم العائلة</label>
                        <input type="text" id="editLastName" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                        <input type="email" id="editEmail" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الدور</label>
                        <select id="editRole" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500">
                            <option value="">اختر الدور</option>
                            <option value="admin">مدير</option>
                            <option value="manager">مدير فرع</option>
                            <option value="technician">فني</option>
                            <option value="user">مستخدم</option>
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">المؤسسة</label>
                        <select id="editOrganization" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500">
                            <option value="">اختر المؤسسة</option>
                            <option value="franchise">فرانشيز</option>
                            <option value="company">شركة</option>
                            <option value="service_center">مركز خدمة</option>
                        </select>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="editActiveStatus" class="mr-2">
                            <span class="text-sm font-medium text-gray-700">مستخدم نشط</span>
                        </label>
                    </div>
                </div>
            </form>
            <div class="flex justify-end gap-2 pt-4 border-t mt-4">
                <button onclick="closeModal('editUserModal')" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">إلغاء</button>
                <button onclick="saveUserChanges()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Confirmation Modal -->
<div id="deleteUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/3 shadow-lg rounded-md bg-white" dir="rtl">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">تأكيد الحذف</h3>
            <p class="text-sm text-gray-500 mb-4">هل أنت متأكد من رغبتك في حذف هذا المستخدم؟</p>
            <p id="deleteUserName" class="text-md font-medium text-gray-900 mb-4">-</p>
            <p class="text-xs text-red-600">هذا الإجراء لا يمكن التراجع عنه</p>
            <div class="flex justify-center gap-2 pt-4">
                <button onclick="closeModal('deleteUserModal')" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">إلغاء</button>
                <button onclick="confirmDeleteUser()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">حذف</button>
            </div>
        </div>
    </div>
</div>

<!-- Business Entity View Modal -->
<div id="viewBusinessModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white" dir="rtl">
        <div class="mt-3">
            <div class="flex justify-between items-center pb-3">
                <h3 id="businessModalTitle" class="text-lg font-semibold text-gray-900">تفاصيل</h3>
                <button onclick="closeModal('viewBusinessModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div id="businessModalContent" class="border-t pt-4">
                <!-- Content will be populated dynamically -->
            </div>
            <div class="flex justify-end pt-4 border-t mt-4">
                <button onclick="closeModal('viewBusinessModal')" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Business Entity Edit Modal -->
<div id="editBusinessModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white" dir="rtl">
        <div class="mt-3">
            <div class="flex justify-between items-center pb-3">
                <h3 id="editBusinessModalTitle" class="text-lg font-semibold text-gray-900">تعديل</h3>
                <button onclick="closeModal('editBusinessModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <form id="editBusinessForm" class="border-t pt-4">
                <div id="editBusinessFormContent">
                    <!-- Form content will be populated dynamically -->
                </div>
            </form>
            <div class="flex justify-end gap-2 pt-4 border-t mt-4">
                <button onclick="closeModal('editBusinessModal')" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">إلغاء</button>
                <button onclick="saveBusinessChanges()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<!-- Business Entity Delete Modal -->
<div id="deleteBusinessModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/3 shadow-lg rounded-md bg-white" dir="rtl">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">تأكيد الحذف</h3>
            <p id="deleteBusinessMessage" class="text-sm text-gray-500 mb-4">هل أنت متأكد من رغبتك في حذف هذا العنصر؟</p>
            <p id="deleteBusinessName" class="text-md font-medium text-gray-900 mb-4">-</p>
            <p class="text-xs text-red-600">هذا الإجراء لا يمكن التراجع عنه</p>
            <div class="flex justify-center gap-2 pt-4">
                <button onclick="closeModal('deleteBusinessModal')" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">إلغاء</button>
                <button onclick="confirmDeleteBusiness()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">حذف</button>
            </div>
        </div>
    </div>
</div>

</script>
{% endblock %} 