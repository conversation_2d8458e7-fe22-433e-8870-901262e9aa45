{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ franchise.name }} - {% trans "تفاصيل الامتياز" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex items-center justify-between">
            <div class="text-white">
                <h1 class="text-3xl font-bold mb-2">{{ franchise.name }}</h1>
                <p class="text-purple-100">{% trans "تفاصيل الامتياز" %}</p>
                <div class="flex items-center mt-2">
                    <span class="px-3 py-1 rounded-full text-sm font-medium {% if franchise.is_active %}bg-green-500 text-white{% else %}bg-red-500 text-white{% endif %}">
                        {% if franchise.is_active %}{% trans "نشط" %}{% else %}{% trans "غير نشط" %}{% endif %}
                    </span>
                    <span class="mr-3 text-purple-200">{% trans "كود الامتياز" %}: {{ franchise.code }}</span>
                </div>
            </div>
            <div class="text-white text-left">
                {% if franchise.logo %}
                    <img src="{{ franchise.logo.url }}" alt="{{ franchise.name }}" class="w-20 h-20 rounded-lg object-cover">
                {% else %}
                    <i class="fas fa-crown text-6xl opacity-20"></i>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-3 mb-6">
        <a href="{% url 'setup:franchise_edit' franchise.pk %}" 
           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-edit ml-2"></i>
            {% trans "تعديل الامتياز" %}
        </a>
        <a href="{% url 'setup:franchise_list' %}" 
           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-arrow-right ml-2"></i>
            {% trans "العودة للقائمة" %}
        </a>
        <a href="{% url 'setup:company_create' %}?franchise={{ franchise.pk }}" 
           class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-plus ml-2"></i>
            {% trans "إضافة شركة جديدة" %}
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-info-circle ml-3"></i>
                        {% trans "المعلومات الأساسية" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "اسم الامتياز" %}</label>
                            <p class="text-gray-900 font-semibold">{{ franchise.name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "كود الامتياز" %}</label>
                            <p class="text-gray-900 font-mono">{{ franchise.code }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "تاريخ التأسيس" %}</label>
                            <p class="text-gray-900">{{ franchise.founding_date|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الحالة" %}</label>
                            <span class="px-2 py-1 rounded-full text-xs font-medium {% if franchise.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if franchise.is_active %}{% trans "نشط" %}{% else %}{% trans "غير نشط" %}{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-green-500 to-green-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-address-book ml-3"></i>
                        {% trans "معلومات الاتصال" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الهاتف" %}</label>
                            <p class="text-gray-900">{{ franchise.phone|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "البريد الإلكتروني" %}</label>
                            <p class="text-gray-900">{{ franchise.email|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الموقع الإلكتروني" %}</label>
                            {% if franchise.website %}
                                <a href="{{ franchise.website }}" target="_blank" class="text-blue-600 hover:text-blue-800">{{ franchise.website }}</a>
                            {% else %}
                                <p class="text-gray-900">{% trans "غير محدد" %}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الرمز البريدي" %}</label>
                            <p class="text-gray-900">{{ franchise.postal_code|default:"غير محدد" }}</p>
                        </div>
                    </div>
                    
                    {% if franchise.address %}
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "العنوان" %}</label>
                        <p class="text-gray-900">{{ franchise.address }}</p>
                        {% if franchise.city or franchise.state or franchise.country %}
                        <p class="text-gray-600 text-sm mt-1">
                            {{ franchise.city }}{% if franchise.city and franchise.state %}, {% endif %}{{ franchise.state }}{% if franchise.state and franchise.country %}, {% endif %}{{ franchise.country }}
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Business Information -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-orange-500 to-orange-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-briefcase ml-3"></i>
                        {% trans "المعلومات التجارية" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الرقم الضريبي" %}</label>
                            <p class="text-gray-900 font-mono">{{ franchise.tax_id|default:"غير محدد" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "رقم التسجيل" %}</label>
                            <p class="text-gray-900 font-mono">{{ franchise.registration_number|default:"غير محدد" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            {% if franchise.notes %}
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-gray-500 to-gray-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-sticky-note ml-3"></i>
                        {% trans "ملاحظات" %}
                    </h3>
                </div>
                <div class="p-6">
                    <p class="text-gray-900 whitespace-pre-wrap">{{ franchise.notes }}</p>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            
            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-chart-bar ml-3"></i>
                        {% trans "الإحصائيات" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">{% trans "عدد الشركات" %}</span>
                            <span class="text-2xl font-bold text-blue-600">{{ companies_count|default:"0" }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">{% trans "مراكز الخدمة" %}</span>
                            <span class="text-2xl font-bold text-green-600">{{ service_centers_count|default:"0" }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">{% trans "المستخدمين" %}</span>
                            <span class="text-2xl font-bold text-orange-600">{{ users_count|default:"0" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-clock ml-3"></i>
                        {% trans "النشاط الأخير" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <div class="text-sm text-gray-600">
                            <i class="fas fa-calendar-plus text-green-500 ml-2"></i>
                            {% trans "تم الإنشاء" %}: {{ franchise.created_at|date:"d/m/Y H:i" }}
                        </div>
                        <div class="text-sm text-gray-600">
                            <i class="fas fa-calendar-edit text-blue-500 ml-2"></i>
                            {% trans "آخر تحديث" %}: {{ franchise.updated_at|date:"d/m/Y H:i" }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-teal-500 to-teal-600 px-6 py-4">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-bolt ml-3"></i>
                        {% trans "إجراءات سريعة" %}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        <a href="{% url 'setup:company_create' %}?franchise={{ franchise.pk }}" 
                           class="block w-full bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-lg transition-colors duration-200 text-center">
                            <i class="fas fa-building ml-2"></i>
                            {% trans "إضافة شركة" %}
                        </a>
                        <a href="{% url 'setup:user_profile_create' %}?franchise={{ franchise.pk }}" 
                           class="block w-full bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-lg transition-colors duration-200 text-center">
                            <i class="fas fa-user-plus ml-2"></i>
                            {% trans "إضافة مستخدم" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Companies Section -->
    {% if companies %}
    <div class="mt-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-building ml-3"></i>
                    {% trans "الشركات التابعة" %} ({{ companies.count }})
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for company in companies %}
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-900">{{ company.name }}</h4>
                            <span class="px-2 py-1 rounded-full text-xs font-medium {% if company.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if company.is_active %}{% trans "نشط" %}{% else %}{% trans "غير نشط" %}{% endif %}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">{{ company.code }}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">{{ company.service_centers.count }} {% trans "مراكز خدمة" %}</span>
                            <a href="{% url 'setup:company_detail' company.pk %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                {% trans "عرض التفاصيل" %}
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %} 