{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}
    {% if form.instance.pk %}
        {% trans "تعديل الامتياز" %}
    {% else %}
        {% trans "إضافة امتياز جديد" %}
    {% endif %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .rtl-container {
        direction: rtl;
        text-align: right;
        font-family: 'Tahoma', 'Arial', sans-serif;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .back-button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .back-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .form-container {
        background: white;
        border-radius: 16px;
        box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        border: 1px solid #f3f4f6;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .form-body {
        padding: 2rem;
    }
    
    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8fafc;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    @media (min-width: 768px) {
        .form-row.two-cols {
            grid-template-columns: 1fr 1fr;
        }
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }
    
    .required-field::after {
        content: ' *';
        color: #ef4444;
    }
    
    /* Force form field visibility */
    input, textarea, select {
        display: block !important;
        width: 100% !important;
        padding: 0.75rem !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 8px !important;
        font-size: 0.875rem !important;
        line-height: 1.25rem !important;
        background-color: white !important;
        transition: all 0.3s ease !important;
        min-height: 44px !important;
        box-sizing: border-box !important;
    }
    
    input:focus, textarea:focus, select:focus {
        outline: none !important;
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }
    
    input[type="checkbox"] {
        width: 1rem !important;
        height: 1rem !important;
        margin-left: 0.5rem !important;
    }
    
    .checkbox-wrapper {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background: white;
        border-radius: 12px;
        border: 2px solid #e5e7eb;
    }
    
    .error-list ul {
        list-style: none;
        padding: 0;
        margin: 0.5rem 0 0 0;
    }
    
    .error-list li {
        background: #fef2f2;
        color: #dc2626;
        padding: 0.5rem;
        border-radius: 6px;
        font-size: 0.875rem;
        border: 1px solid #fecaca;
        margin-bottom: 0.25rem;
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding: 1.5rem;
        background: #f8fafc;
        border-radius: 12px;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        min-height: 44px;
        font-size: 0.875rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
    }
    
    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary {
        background: #6b7280;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="rtl-container">
    <!-- Back Button -->
    <a href="{% url 'setup:dashboard' %}" class="back-button">
        <i class="fas fa-arrow-right"></i>
        {% trans "العودة للإعدادات" %}
    </a>

    <div class="form-container">
        <div class="form-header">
            <h1 class="text-3xl font-bold mb-2">
                {% if form.instance.pk %}
                    <i class="fas fa-edit ml-2"></i>
                    {% trans "تعديل الامتياز" %}
                {% else %}
                    <i class="fas fa-plus ml-2"></i>
                    {% trans "إضافة امتياز جديد" %}
                {% endif %}
            </h1>
            <p class="text-blue-100">{% trans "أدخل بيانات الامتياز" %}</p>
        </div>
        
        <div class="form-body">
            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle text-blue-600"></i>
                        {% trans "المعلومات الأساسية" %}
                    </h3>
                    
                    <div class="form-row two-cols">
                        <div class="form-group">
                            <label class="form-label required-field" for="{{ form.name.id_for_label }}">
                                {% trans "اسم الامتياز" %}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="error-list">
                                    <ul>
                                        {% for error in form.name.errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="{{ form.code.id_for_label }}">
                                {% trans "كود الامتياز" %}
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="error-list">
                                    <ul>
                                        {% for error in form.code.errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-wrapper">
                            {{ form.is_active }}
                            <label for="{{ form.is_active.id_for_label }}" class="form-label" style="margin-bottom: 0;">
                                {% trans "الامتياز نشط" %}
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-address-card text-green-600"></i>
                        {% trans "معلومات الاتصال" %}
                    </h3>
                    
                    <div class="form-row two-cols">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.phone.id_for_label }}">
                                {% trans "رقم الهاتف" %}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="error-list">
                                    <ul>
                                        {% for error in form.phone.errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="{{ form.email.id_for_label }}">
                                {% trans "البريد الإلكتروني" %}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="error-list">
                                    <ul>
                                        {% for error in form.email.errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="{{ form.website.id_for_label }}">
                            {% trans "الموقع الإلكتروني" %}
                        </label>
                        {{ form.website }}
                        {% if form.website.errors %}
                            <div class="error-list">
                                <ul>
                                    {% for error in form.website.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-row two-cols">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.city.id_for_label }}">
                                {% trans "المدينة" %}
                            </label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                <div class="error-list">
                                    <ul>
                                        {% for error in form.city.errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="{{ form.address.id_for_label }}">
                            {% trans "العنوان التفصيلي" %}
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="error-list">
                                <ul>
                                    {% for error in form.address.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Business Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-building text-purple-600"></i>
                        {% trans "المعلومات التجارية" %}
                    </h3>
                    
                    <div class="form-row two-cols">
                        <div class="form-group">
                            <label class="form-label" for="{{ form.tax_id.id_for_label }}">
                                {% trans "الرقم الضريبي" %}
                            </label>
                            {{ form.tax_id }}
                            {% if form.tax_id.errors %}
                                <div class="error-list">
                                    <ul>
                                        {% for error in form.tax_id.errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="{{ form.registration_number.id_for_label }}">
                                {% trans "رقم السجل التجاري" %}
                            </label>
                            {{ form.registration_number }}
                            {% if form.registration_number.errors %}
                                <div class="error-list">
                                    <ul>
                                        {% for error in form.registration_number.errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="{{ form.founding_date.id_for_label }}">
                            {% trans "تاريخ التأسيس" %}
                        </label>
                        {{ form.founding_date }}
                        {% if form.founding_date.errors %}
                            <div class="error-list">
                                <ul>
                                    {% for error in form.founding_date.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="{{ form.notes.id_for_label }}">
                            {% trans "ملاحظات" %}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="error-list">
                                <ul>
                                    {% for error in form.notes.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <a href="{% url 'setup:franchise_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        {% trans "إلغاء" %}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        {% if form.instance.pk %}
                            {% trans "حفظ التعديلات" %}
                        {% else %}
                            {% trans "إضافة الامتياز" %}
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 