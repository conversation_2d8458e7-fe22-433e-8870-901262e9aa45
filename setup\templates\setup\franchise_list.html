{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "قائمة الامتيازات" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{% trans "قائمة الامتيازات" %}</h1>
            <p class="mt-1 text-sm text-gray-500">{% trans "إدارة امتيازات الشركة" %}</p>
        </div>
        <a href="{% url 'setup:franchise_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center space-x-2 transition duration-300">
            <i class="fas fa-plus"></i>
            <span>{% trans "إضافة امتياز جديد" %}</span>
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                    <i class="fas fa-crown text-xl"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-sm text-gray-500">{% trans "إجمالي الامتيازات" %}</p>
                    <p class="text-lg font-semibold">{{ franchises.count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-sm text-gray-500">{% trans "نشط" %}</p>
                    <p class="text-lg font-semibold">{{ franchises|length }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-500">
                    <i class="fas fa-building text-xl"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-sm text-gray-500">{% trans "الشركات التابعة" %}</p>
                    <p class="text-lg font-semibold">{{ total_companies|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-amber-100 text-amber-500">
                    <i class="fas fa-store text-xl"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-sm text-gray-500">{% trans "مراكز الخدمة" %}</p>
                    <p class="text-lg font-semibold">{{ total_service_centers|default:"0" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">{% trans "البحث والتصفية" %}</h3>
        </div>
        <div class="p-4">
        <form method="get" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "البحث" %}</label>
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="{% trans 'البحث في الاسم، الكود، البريد الإلكتروني...' %}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "الحالة" %}</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">{% trans "جميع الحالات" %}</option>
                        <option value="active" {% if current_status == 'active' %}selected{% endif %}>{% trans "نشط" %}</option>
                        <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>{% trans "غير نشط" %}</option>
                    </select>
                </div>
                
                    <div class="flex items-end">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition duration-300 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}">
                            <i class="fas fa-search"></i> {% trans "بحث" %}
                    </button>
                        <a href="{% url 'setup:franchise_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md transition duration-300">
                            <i class="fas fa-undo"></i> {% trans "إعادة تعيين" %}
                    </a>
                </div>
            </div>
        </form>
    </div>
    </div>

    <!-- Franchises Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">{% trans "الامتيازات" %}
                {% if franchises %}
                <span class="text-sm text-gray-500">({{ page_obj.paginator.count }} {% trans "امتياز" %})</span>
                {% endif %}
            </h3>
        </div>
        
    {% if franchises %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                        <tr>
                        <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الاسم" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الكود" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "البريد الإلكتروني" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الشركات" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "تاريخ الإنشاء" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الإجراءات" %}
                        </th>
                        </tr>
                    </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                        {% for franchise in franchises %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-crown text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                                    <div class="text-sm font-medium text-gray-900">{{ franchise.name }}</div>
                                    {% if franchise.description %}
                                    <div class="text-sm text-gray-500">{{ franchise.description|truncatechars:50 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ franchise.code|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ franchise.contact_email|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ franchise.companies.count }} {% trans "شركة" %}
                                </span>
                            </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ franchise.created_at|date:"d/m/Y" }}
                            </td>
                        <td class="px-6 py-4 whitespace-nowrap text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="{% url 'setup:franchise_detail' franchise.pk %}" class="text-blue-600 hover:text-blue-900" title="{% trans 'عرض التفاصيل' %}">
                                    <i class="fas fa-eye"></i>
                                    </a>
                                <a href="{% url 'setup:franchise_edit' franchise.pk %}" class="text-green-600 hover:text-green-900" title="{% trans 'تعديل' %}">
                                    <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "السابق" %}
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        {% trans "التالي" %}
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        {% trans "عرض" %}
                        <span class="font-medium">{{ page_obj.start_index }}</span>
                        {% trans "إلى" %}
                        <span class="font-medium">{{ page_obj.end_index }}</span>
                        {% trans "من" %}
                        <span class="font-medium">{{ page_obj.paginator.count }}</span>
                        {% trans "نتيجة" %}
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-{% if LANGUAGE_CODE == 'ar' %}r{% else %}l{% endif %}-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %}"></i>
                            </a>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-{% if LANGUAGE_CODE == 'ar' %}l{% else %}r{% endif %}-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}"></i>
                            </a>
                    {% endif %}
            </nav>
                </div>
            </div>
        </div>
        {% endif %}

    {% else %}
        <div class="text-center py-8">
            <i class="fas fa-crown text-gray-300 text-5xl mb-4"></i>
            <p class="text-gray-500 mb-4">{% trans "لا توجد امتيازات" %}</p>
            <a href="{% url 'setup:franchise_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center space-x-2 transition duration-300">
                <i class="fas fa-plus"></i>
                <span>{% trans "إضافة امتياز جديد" %}</span>
            </a>
        </div>
    {% endif %}
    </div>
</div>
{% endblock %}