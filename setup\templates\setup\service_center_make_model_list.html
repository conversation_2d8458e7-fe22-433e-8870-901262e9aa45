{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid p-4" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-gray-800">{{ page_title }}</h1>
            <p class="text-muted">{{ page_subtitle }}</p>
        </div>
        <a href="{% url 'setup:service_center_make_model_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans "إضافة تخصص جديد" %}
        </a>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                {% trans "ماركات وموديلات مراكز الخدمة" %} ({{ service_center_make_models|length }} {% trans "تخصص" %})
            </h6>
        </div>
        <div class="card-body">
            {% if service_center_make_models %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>{% trans "مركز الخدمة" %}</th>
                                <th>{% trans "الماركة" %}</th>
                                <th>{% trans "الموديل" %}</th>
                                <th>{% trans "الحالة" %}</th>
                                <th>{% trans "ملاحظات" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in service_center_make_models %}
                            <tr>
                                <td>
                                    <strong>{{ item.service_center.name }}</strong>
                                    <br><small class="text-muted">{{ item.service_center.address|truncatechars:30 }}</small>
                                </td>
                                <td>{{ item.make }}</td>
                                <td>{{ item.model|default:"-" }}</td>
                                <td>
                                    {% if item.is_active %}
                                        <span class="badge bg-success">{% trans "نشط" %}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.notes %}
                                        {{ item.notes|truncatechars:30 }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'setup:service_center_make_model_edit' item.pk %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "لا توجد تخصصات مراكز خدمة" %}</h5>
                    <p class="text-muted">{% trans "ابدأ بإضافة تخصص مركز خدمة جديد" %}</p>
                    <a href="{% url 'setup:service_center_make_model_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "إضافة تخصص جديد" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 