{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "أنواع مراكز الخدمة" %} | {% trans "نظام Aftersails" %}{% endblock %}

{% block extra_css %}
<style>
    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-active { background-color: #d1fae5; color: #065f46; }
    .status-inactive { background-color: #fee2e2; color: #dc2626; }

    .action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-warning { background-color: #f59e0b; color: white; }

    /* Table styling with RTL support */
    .service-types-table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        direction: rtl;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }

    .service-types-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 600;
        text-align: right;
        padding: 0.75rem 0.875rem;
        border-bottom: 1px solid #d1d5db;
        border-left: 1px solid #d1d5db;
        position: relative;
        color: #374151;
        font-size: 0.875rem;
    }

    .service-types-table th:first-child {
        border-left: none;
    }

    .service-types-table th .header-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        direction: rtl;
    }

    .service-types-table th .header-icon {
        width: 1rem;
        height: 1rem;
        color: #6b7280;
        flex-shrink: 0;
    }

    .service-types-table td {
        padding: 1rem 0.875rem;
        border-bottom: 1px solid #e5e7eb;
        border-left: 1px solid #e5e7eb;
        vertical-align: middle;
        text-align: right;
        direction: rtl;
        font-weight: 600;
        color: #111827;
        font-size: 1rem;
        line-height: 1.6;
    }

    .service-types-table td:first-child {
        border-left: none;
    }

    .service-types-table tbody tr:hover {
        background-color: #f9fafb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .service-types-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Enhanced text styling */
    .service-types-table .type-name {
        font-weight: 800;
        color: #1e40af;
        font-size: 1.125rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .service-types-table .capacity-info {
        font-weight: 700;
        color: #047857;
        font-size: 1.0625rem;
    }

    .service-types-table .color-badge {
        font-weight: 700;
        color: #374151;
        font-size: 1rem;
    }

    .service-types-table .description {
        font-weight: 600;
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* RTL adjustments */
    html[dir="rtl"] .action-btn i {
        margin-left: 0.25rem;
        margin-right: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    <!-- Header and Actions -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
        <div class="flex flex-wrap items-center justify-between gap-2">
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-1">{% trans "أنواع مراكز الخدمة" %}</h1>
                <p class="text-gray-600">{% trans "إدارة أنواع وتصنيفات مراكز الخدمة" %}</p>
            </div>
            <div class="flex gap-2">
                <a href="{% url 'setup:service_center_type_create' %}" 
                   class="inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm transition-colors duration-200">
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {% trans "إضافة نوع جديد" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Service Types Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">
                {% trans "قائمة أنواع مراكز الخدمة" %} ({{ service_center_types|length }} {% trans "نوع" %})
            </h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="service-types-table w-full">
                <thead>
                    <tr>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span>{% trans "النوع" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>{% trans "السعة القصوى" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M15 5l2 2"></path>
                                </svg>
                                <span>{% trans "رمز اللون" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "الحالة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                <span>{% trans "الإجراءات" %}</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for type in service_center_types %}
                    <tr>
                        <td>
                            <span class="type-name">{{ type.name }}</span>
                            {% if type.description %}
                                <span class="description">{{ type.description|truncatechars:50 }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="capacity-info">{{ type.max_capacity }} {% trans "مركبة" %}</span>
                        </td>
                        <td>
                            {% if type.color_code %}
                                <div class="flex items-center gap-2">
                                    <div class="w-6 h-6 rounded-full border-2 border-gray-300" style="background-color: {{ type.color_code }};"></div>
                                    <span class="color-badge">{{ type.color_code }}</span>
                                </div>
                            {% else %}
                                <span class="text-gray-400">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if type.is_active %}
                                <span class="status-badge status-active">نشط</span>
                            {% else %}
                                <span class="status-badge status-inactive">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="flex gap-1">
                                <a href="{% url 'setup:service_center_type_edit' type.pk %}" class="action-btn btn-warning text-xs">
                                    <i class="fas fa-edit ml-1"></i>
                                    {% trans "تعديل" %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center py-12">
                            <i class="fas fa-building text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">{% trans "لا توجد أنواع مراكز خدمة" %}</h3>
                            <p class="text-gray-500 mb-4">{% trans "ابدأ بإضافة نوع مركز خدمة جديد" %}</p>
                            <a href="{% url 'setup:service_center_type_create' %}" class="action-btn btn-primary">
                                <i class="fas fa-plus ml-2"></i>
                                {% trans "إضافة نوع جديد" %}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 