{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "سجل الخدمات" %} | {% trans "نظام Aftersails" %}{% endblock %}

{% block extra_css %}
<style>
    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-completed { background-color: #d1fae5; color: #065f46; }
    .status-pending { background-color: #fef3c7; color: #d97706; }
    .status-cancelled { background-color: #fee2e2; color: #dc2626; }

    .action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-info { background-color: #06b6d4; color: white; }

    /* Table styling with RTL support */
    .service-history-table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        direction: rtl;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }

    .service-history-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 600;
        text-align: right;
        padding: 0.75rem 0.875rem;
        border-bottom: 1px solid #d1d5db;
        border-left: 1px solid #d1d5db;
        position: relative;
        color: #374151;
        font-size: 0.875rem;
    }

    .service-history-table th:first-child {
        border-left: none;
    }

    .service-history-table th .header-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        direction: rtl;
    }

    .service-history-table th .header-icon {
        width: 1rem;
        height: 1rem;
        color: #6b7280;
        flex-shrink: 0;
    }

    .service-history-table td {
        padding: 1rem 0.875rem;
        border-bottom: 1px solid #e5e7eb;
        border-left: 1px solid #e5e7eb;
        vertical-align: middle;
        text-align: right;
        direction: rtl;
        font-weight: 600;
        color: #111827;
        font-size: 1rem;
        line-height: 1.6;
    }

    .service-history-table td:first-child {
        border-left: none;
    }

    .service-history-table tbody tr:hover {
        background-color: #f9fafb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .service-history-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Enhanced text styling */
    .service-history-table .vehicle-info {
        font-weight: 800;
        color: #1e40af;
        font-size: 1.125rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .service-history-table .service-center-name {
        font-weight: 700;
        color: #059669;
        font-size: 1.0625rem;
    }

    .service-history-table .service-date {
        font-weight: 700;
        color: #7c2d12;
        font-size: 1rem;
    }

    .service-history-table .service-type {
        font-weight: 700;
        color: #7c3aed;
        font-size: 1rem;
    }

    .service-history-table .cost-info {
        font-weight: 700;
        color: #dc2626;
        font-size: 1.0625rem;
    }

    .service-history-table .vehicle-details {
        font-weight: 600;
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* RTL adjustments */
    html[dir="rtl"] .action-btn i {
        margin-left: 0.25rem;
        margin-right: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    <!-- Header and Actions -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
        <div class="flex flex-wrap items-center justify-between gap-2">
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-1">{% trans "سجل الخدمات" %}</h1>
                <p class="text-gray-600">{% trans "متابعة وإدارة سجل الخدمات المقدمة للمركبات" %}</p>
            </div>
            <div class="flex gap-2">
                <!-- Filter and Export buttons could go here -->
                <button class="inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg shadow-sm transition-colors duration-200">
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    {% trans "تصدير" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Service History Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">
                {% trans "قائمة سجل الخدمات" %} ({{ service_records|length }} {% trans "سجل" %})
            </h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="service-history-table w-full">
                <thead>
                    <tr>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0M15 17a2 2 0 104 0"></path>
                                </svg>
                                <span>{% trans "المركبة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span>{% trans "مركز الخدمة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-4 8v6m-6-2l6-6 6 6"></path>
                                </svg>
                                <span>{% trans "تاريخ الخدمة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span>{% trans "نوع الخدمة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <span>{% trans "التكلفة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "الحالة" %}</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in service_records %}
                    <tr>
                        <td>
                            <span class="vehicle-info">{{ record.vehicle.license_plate }}</span>
                            <span class="vehicle-details">{{ record.vehicle.make }} {{ record.vehicle.model }}</span>
                        </td>
                        <td>
                            <span class="service-center-name">{{ record.service_center.name }}</span>
                        </td>
                        <td>
                            <span class="service-date">{{ record.service_date|date:"Y-m-d" }}</span>
                        </td>
                        <td>
                            <span class="service-type">{{ record.service_type }}</span>
                        </td>
                        <td>
                            {% if record.cost %}
                                <span class="cost-info">{{ record.cost }} {% trans "ريال" %}</span>
                            {% else %}
                                <span class="text-gray-400">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge status-completed">مكتمل</span>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-12">
                            <i class="fas fa-clipboard-list text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">{% trans "لا يوجد سجل خدمات" %}</h3>
                            <p class="text-gray-500 mb-4">{% trans "سيظهر سجل الخدمات هنا عند بدء الخدمات" %}</p>
                            <button class="action-btn btn-primary">
                                <i class="fas fa-plus ml-2"></i>
                                {% trans "إنشاء أول خدمة" %}
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 