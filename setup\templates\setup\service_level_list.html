{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "مستويات الخدمة" %} | {% trans "نظام Aftersails" %}{% endblock %}

{% block extra_css %}
<style>
    /* Priority badges */
    .priority-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .priority-1 { background-color: #fee2e2; color: #dc2626; }
    .priority-2 { background-color: #fef3c7; color: #d97706; }
    .priority-3 { background-color: #dbeafe; color: #2563eb; }
    .priority-default { background-color: #f3f4f6; color: #6b7280; }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-active { background-color: #d1fae5; color: #065f46; }
    .status-inactive { background-color: #fee2e2; color: #dc2626; }

    .action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-warning { background-color: #f59e0b; color: white; }

    /* Table styling with RTL support */
    .service-levels-table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        direction: rtl;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }

    .service-levels-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 600;
        text-align: right;
        padding: 0.75rem 0.875rem;
        border-bottom: 1px solid #d1d5db;
        border-left: 1px solid #d1d5db;
        position: relative;
        color: #374151;
        font-size: 0.875rem;
    }

    .service-levels-table th:first-child {
        border-left: none;
    }

    .service-levels-table th .header-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        direction: rtl;
    }

    .service-levels-table th .header-icon {
        width: 1rem;
        height: 1rem;
        color: #6b7280;
        flex-shrink: 0;
    }

    .service-levels-table td {
        padding: 1rem 0.875rem;
        border-bottom: 1px solid #e5e7eb;
        border-left: 1px solid #e5e7eb;
        vertical-align: middle;
        text-align: right;
        direction: rtl;
        font-weight: 600;
        color: #111827;
        font-size: 1rem;
        line-height: 1.6;
    }

    .service-levels-table td:first-child {
        border-left: none;
    }

    .service-levels-table tbody tr:hover {
        background-color: #f9fafb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .service-levels-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Enhanced text styling */
    .service-levels-table .level-name {
        font-weight: 800;
        color: #1e40af;
        font-size: 1.125rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .service-levels-table .time-info {
        font-weight: 700;
        color: #047857;
        font-size: 1.0625rem;
    }

    .service-levels-table .support-hours {
        font-weight: 700;
        color: #7c3aed;
        font-size: 1rem;
    }

    .service-levels-table .description {
        font-weight: 600;
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* RTL adjustments */
    html[dir="rtl"] .action-btn i {
        margin-left: 0.25rem;
        margin-right: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    <!-- Header and Actions -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
        <div class="flex flex-wrap items-center justify-between gap-2">
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-1">{% trans "مستويات الخدمة" %}</h1>
                <p class="text-gray-600">{% trans "إدارة مستويات جودة وأولوية الخدمة" %}</p>
            </div>
            <div class="flex gap-2">
                <a href="{% url 'setup:service_level_create' %}" 
                   class="inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm transition-colors duration-200">
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {% trans "إضافة مستوى جديد" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Service Levels Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">
                {% trans "قائمة مستويات الخدمة" %} ({{ service_levels|length }} {% trans "مستوى" %})
            </h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="service-levels-table w-full">
                <thead>
                    <tr>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "المستوى" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>{% trans "الأولوية" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "وقت الاستجابة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "وقت الحل" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                </svg>
                                <span>{% trans "ساعات الدعم" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "الحالة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                <span>{% trans "الإجراءات" %}</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for level in service_levels %}
                    <tr>
                        <td>
                            <span class="level-name">{{ level.name }}</span>
                            {% if level.description %}
                                <span class="description">{{ level.description|truncatechars:50 }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="priority-badge {% if level.priority == 1 %}priority-1{% elif level.priority == 2 %}priority-2{% elif level.priority == 3 %}priority-3{% else %}priority-default{% endif %}">
                                {% trans "أولوية" %} {{ level.priority }}
                            </span>
                        </td>
                        <td>
                            <span class="time-info">{{ level.response_time_hours }} {% trans "ساعة" %}</span>
                        </td>
                        <td>
                            <span class="time-info">{{ level.resolution_time_hours }} {% trans "ساعة" %}</span>
                        </td>
                        <td>
                            <span class="support-hours">{{ level.support_hours }}</span>
                        </td>
                        <td>
                            {% if level.is_active %}
                                <span class="status-badge status-active">نشط</span>
                            {% else %}
                                <span class="status-badge status-inactive">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="flex gap-1">
                                <a href="{% url 'setup:service_level_edit' level.pk %}" class="action-btn btn-warning text-xs">
                                    <i class="fas fa-edit ml-1"></i>
                                    {% trans "تعديل" %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-12">
                            <i class="fas fa-layer-group text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">{% trans "لا توجد مستويات خدمة" %}</h3>
                            <p class="text-gray-500 mb-4">{% trans "ابدأ بإضافة مستوى خدمة جديد" %}</p>
                            <a href="{% url 'setup:service_level_create' %}" class="action-btn btn-primary">
                                <i class="fas fa-plus ml-2"></i>
                                {% trans "إضافة مستوى جديد" %}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 