{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
.search-form {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}
.user-nav-tabs {
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 1.5rem;
}
.user-nav-tabs .nav-link {
    color: #6b7280;
    font-weight: 500;
    border: none;
    border-bottom: 3px solid transparent;
    padding: 0.75rem 1.25rem;
    transition: all 0.3s ease;
    text-decoration: none;
}
.user-nav-tabs .nav-link:hover {
    color: #374151;
    border-bottom-color: #3b82f6;
}
.user-nav-tabs .nav-link.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background-color: transparent;
}
.user-nav-tabs .nav-link i {
    margin-right: 0.5rem;
}
.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- User Management Navigation -->
    <div class="mb-6">
        <div class="w-full">
            <ul class="flex justify-center user-nav-tabs">
                <li class="flex-none">
                    <a class="nav-link active flex items-center" href="{% url 'setup:technician_list' %}">
                        <i class="fas fa-wrench"></i>
                        {% trans "الفنيون" %}
                    </a>
                </li>
                <li class="flex-none">
                    <a class="nav-link flex items-center" href="{% url 'setup:user_profile_list' %}?role_level=service_center">
                        <i class="fas fa-cog"></i>
                        {% trans "مستخدمو مراكز الخدمة" %}
                    </a>
                </li>
                <li class="flex-none">
                    <a class="nav-link flex items-center" href="{% url 'setup:user_profile_list' %}?role_level=franchise">
                        <i class="fas fa-store"></i>
                        {% trans "مستخدمو الامتياز" %}
                    </a>
                </li>
                <li class="flex-none">
                    <a class="nav-link flex items-center" href="{% url 'setup:user_profile_list' %}?role_level=company">
                        <i class="fas fa-building"></i>
                        {% trans "مستخدمو الشركة" %}
                    </a>
                </li>
                <li class="flex-none">
                    <a class="nav-link flex items-center" href="{% url 'setup:user_profile_list' %}">
                        <i class="fas fa-users"></i>
                        {% trans "جميع المستخدمين" %}
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-800">{{ page_title }}</h1>
            <p class="text-gray-600 mt-1">{% trans "إدارة ملفات الفنيين وتخصصاتهم" %}</p>
        </div>
        <div class="flex space-x-3 rtl:space-x-reverse">
            <a href="{% url 'setup:technician_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                <i class="fas fa-plus mr-2"></i> {% trans "إضافة فني جديد" %}
            </a>
            <a href="{% url 'setup:user_profile_create' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                <i class="fas fa-user-plus mr-2"></i> {% trans "إضافة مستخدم جديد" %}
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-form text-white mb-6 rounded-lg shadow-sm">
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium mb-2">{% trans "البحث" %}</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 focus:ring-blue-500 focus:border-blue-500" 
                           name="search" value="{{ search_query }}" 
                           placeholder="{% trans 'البحث في الفنيين...' %}">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">{% trans "سنوات الخبرة" %}</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 focus:ring-blue-500 focus:border-blue-500" name="experience_years">
                        <option value="">{% trans "جميع المستويات" %}</option>
                        {% for years in experience_ranges %}
                            <option value="{{ years }}" {% if current_experience_years == years|stringformat:"s" %}selected{% endif %}>
                                {{ years }} {% trans "سنة" %}{% if years > 1 %}{% trans "ات" %}{% endif %}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex items-end space-x-2 rtl:space-x-reverse">
                    <button type="submit" class="bg-white hover:bg-gray-100 text-gray-800 px-4 py-2 rounded-md flex items-center transition-colors">
                        <i class="fas fa-search mr-2"></i> {% trans "بحث" %}
                    </button>
                    <a href="{% url 'setup:technician_list' %}" class="border border-white hover:bg-white hover:text-gray-800 text-white px-4 py-2 rounded-md flex items-center transition-colors">
                        <i class="fas fa-times mr-2"></i> {% trans "مسح" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h6 class="text-lg font-semibold text-blue-600">
                {% trans "قائمة الفنيين" %} ({{ technician_profiles|length }} {% trans "فني" %})
            </h6>
        </div>
        <div class="p-6">
            {% if technician_profiles %}
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-800 text-white">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-right">{% trans "الاسم" %}</th>
                                <th class="border border-gray-300 px-4 py-3 text-right">{% trans "البريد الإلكتروني" %}</th>
                                <th class="border border-gray-300 px-4 py-3 text-right">{% trans "سنوات الخبرة" %}</th>
                                <th class="border border-gray-300 px-4 py-3 text-right">{% trans "الهاتف" %}</th>
                                <th class="border border-gray-300 px-4 py-3 text-right">{% trans "رقم الهوية" %}</th>
                                <th class="border border-gray-300 px-4 py-3 text-right">{% trans "الحالة" %}</th>
                                <th class="border border-gray-300 px-4 py-3 text-right">{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white">
                            {% for profile in technician_profiles %}
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="border border-gray-300 px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="avatar-sm bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-wrench"></i>
                                        </div>
                                        <div>
                                            <strong class="text-gray-900">{{ profile.user_profile.user.get_full_name|default:profile.user_profile.user.username }}</strong>
                                            {% if profile.license_number %}
                                                <br><small class="text-gray-500">License: {{ profile.license_number }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="border border-gray-300 px-4 py-3 text-gray-900">{{ profile.user_profile.user.email|default:"-" }}</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        {{ profile.experience_years }} {% trans "سنة" %}
                                    </span>
                                </td>
                                <td class="border border-gray-300 px-4 py-3 text-gray-900">
                                    {{ profile.user_profile.phone|default:"-" }}
                                    {% if profile.user_profile.mobile %}
                                        <br><small class="text-gray-500">{{ profile.user_profile.mobile }}</small>
                                    {% endif %}
                                </td>
                                <td class="border border-gray-300 px-4 py-3 text-gray-900">{{ profile.user_profile.national_id|default:"-" }}</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    {% if profile.user_profile.user.is_active %}
                                        <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">{% trans "نشط" %}</span>
                                    {% else %}
                                        <span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">{% trans "غير نشط" %}</span>
                                    {% endif %}
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <div class="flex space-x-2 rtl:space-x-reverse">
                                        <a href="{% url 'setup:technician_detail' profile.pk %}" 
                                           class="bg-blue-100 hover:bg-blue-200 text-blue-600 px-2 py-1 rounded text-sm transition-colors" 
                                           title="{% trans 'عرض التفاصيل' %}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'setup:technician_edit' profile.pk %}" 
                                           class="bg-gray-100 hover:bg-gray-200 text-gray-600 px-2 py-1 rounded text-sm transition-colors" 
                                           title="{% trans 'تعديل' %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav class="flex justify-center mt-6" aria-label="{% trans 'Page navigation' %}">
                    <ul class="flex space-x-1 rtl:space-x-reverse">
                        {% if page_obj.has_previous %}
                            <li>
                                <a class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded" 
                                   href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_experience_years %}&experience_years={{ current_experience_years }}{% endif %}">{% trans "الأولى" %}</a>
                            </li>
                            <li>
                                <a class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded" 
                                   href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_experience_years %}&experience_years={{ current_experience_years }}{% endif %}">{% trans "السابقة" %}</a>
                            </li>
                        {% endif %}

                        <li>
                            <span class="px-3 py-2 text-sm bg-blue-600 text-white rounded">{{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li>
                                <a class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded" 
                                   href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_experience_years %}&experience_years={{ current_experience_years }}{% endif %}">{% trans "التالية" %}</a>
                            </li>
                            <li>
                                <a class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded" 
                                   href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_experience_years %}&experience_years={{ current_experience_years }}{% endif %}">{% trans "الأخيرة" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-12">
                    <div class="mb-4">
                        <i class="fas fa-wrench text-6xl text-gray-300"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-600 mb-3">{% trans "لا يوجد فنيون" %}</h4>
                    <p class="text-gray-500 mb-6">{% trans "ابدأ بإضافة فني جديد للنظام" %}</p>
                    <div class="flex justify-center space-x-3 rtl:space-x-reverse">
                        <a href="{% url 'setup:technician_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                            <i class="fas fa-plus mr-2"></i> {% trans "إضافة فني جديد" %}
                        </a>
                        <a href="{% url 'setup:user_profile_create' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                            <i class="fas fa-user-plus mr-2"></i> {% trans "إضافة مستخدم جديد" %}
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Add active class to current tab
document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('.user-nav-tabs .nav-link');
    const currentPath = window.location.pathname;
    
    navLinks.forEach(link => {
        if (link.href.includes('technician_list') && currentPath.includes('technician')) {
            link.classList.add('active');
        } else if (link.href.includes('technician_list')) {
            // Remove active class from other technician links
            return;
        }
    });
});
</script>
{% endblock %} 