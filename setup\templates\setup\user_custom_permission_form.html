{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
.form-section {
    background: #f8fafc;
    border-left: 4px solid #3b82f6;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 0.5rem 0.5rem 0;
}
.permission-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}
.checkbox-group {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}
.checkbox-group:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}
.checkbox-group input[type="checkbox"] {
    transform: scale(1.2);
    margin-right: 0.5rem;
}
.multi-select-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <nav class="flex mb-3" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="{% url 'setup:user_profile_list' %}" class="text-gray-700 hover:text-blue-600">
                            <i class="fas fa-users mr-2"></i>
                            {% trans "المستخدمين" %}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <a href="{% url 'setup:user_custom_permissions' user_profile.pk %}" class="text-gray-700 hover:text-blue-600">
                                {{ user_profile.user.get_full_name|default:user_profile.user.username }}
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <span class="text-gray-500">
                                {% if form_action == 'create' %}
                                    {% trans "إضافة صلاحية مخصصة" %}
                                {% else %}
                                    {% trans "تعديل صلاحية مخصصة" %}
                                {% endif %}
                            </span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-3xl font-bold text-gray-800">{{ page_title }}</h1>
            <p class="text-gray-600 mt-1">{{ page_subtitle }}</p>
        </div>
        <div class="flex space-x-3 rtl:space-x-reverse">
            <a href="{% url 'setup:user_custom_permissions' user_profile.pk %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                <i class="fas fa-arrow-left mr-2"></i> {% trans "إلغاء" %}
            </a>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mr-4">
                <i class="fas fa-user"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-800">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</h3>
                <p class="text-gray-600">{{ user_profile.user.email }}</p>
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">{{ user_profile.role.name }}</span>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h6 class="text-lg font-semibold text-blue-600">
                {% if form_action == 'create' %}
                    <i class="fas fa-plus mr-2"></i>{% trans "إضافة صلاحية مخصصة جديدة" %}
                {% else %}
                    <i class="fas fa-edit mr-2"></i>{% trans "تعديل الصلاحية المخصصة" %}
                {% endif %}
            </h6>
        </div>
        
        <form method="post" class="p-6">
            {% csrf_token %}
            
            <!-- Module Selection -->
            <div class="form-section">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">
                    <i class="fas fa-cog mr-2"></i>{% trans "اختيار الوحدة" %}
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.module_tab.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.module_tab.label }}
                        </label>
                        {{ form.module_tab }}
                        {% if form.module_tab.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.module_tab.errors }}</div>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.is_active.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.is_active.label }}
                        </label>
                        <div class="flex items-center">
                            {{ form.is_active }}
                            <span class="mr-2 text-sm text-gray-600">{% trans "تفعيل هذه الصلاحية" %}</span>
                        </div>
                        {% if form.is_active.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.is_active.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Permissions -->
            <div class="form-section">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">
                    <i class="fas fa-shield-alt mr-2"></i>{% trans "الصلاحيات" %}
                </h4>
                <div class="permission-checkboxes">
                    <div class="checkbox-group">
                        <label class="flex items-center cursor-pointer">
                            {{ form.can_view }}
                            <div>
                                <div class="font-medium text-gray-700">{% trans "عرض" %}</div>
                                <div class="text-sm text-gray-500">{% trans "إمكانية عرض البيانات" %}</div>
                            </div>
                        </label>
                        {% if form.can_view.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.can_view.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="checkbox-group">
                        <label class="flex items-center cursor-pointer">
                            {{ form.can_add }}
                            <div>
                                <div class="font-medium text-gray-700">{% trans "إضافة" %}</div>
                                <div class="text-sm text-gray-500">{% trans "إمكانية إضافة بيانات جديدة" %}</div>
                            </div>
                        </label>
                        {% if form.can_add.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.can_add.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="checkbox-group">
                        <label class="flex items-center cursor-pointer">
                            {{ form.can_edit }}
                            <div>
                                <div class="font-medium text-gray-700">{% trans "تعديل" %}</div>
                                <div class="text-sm text-gray-500">{% trans "إمكانية تعديل البيانات الموجودة" %}</div>
                            </div>
                        </label>
                        {% if form.can_edit.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.can_edit.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="checkbox-group">
                        <label class="flex items-center cursor-pointer">
                            {{ form.can_delete }}
                            <div>
                                <div class="font-medium text-gray-700">{% trans "حذف" %}</div>
                                <div class="text-sm text-gray-500">{% trans "إمكانية حذف البيانات" %}</div>
                            </div>
                        </label>
                        {% if form.can_delete.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.can_delete.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="checkbox-group">
                        <label class="flex items-center cursor-pointer">
                            {{ form.can_approve }}
                            <div>
                                <div class="font-medium text-gray-700">{% trans "موافقة" %}</div>
                                <div class="text-sm text-gray-500">{% trans "إمكانية الموافقة على العمليات" %}</div>
                            </div>
                        </label>
                        {% if form.can_approve.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.can_approve.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="checkbox-group">
                        <label class="flex items-center cursor-pointer">
                            {{ form.can_report }}
                            <div>
                                <div class="font-medium text-gray-700">{% trans "تقارير" %}</div>
                                <div class="text-sm text-gray-500">{% trans "إمكانية عرض وإنشاء التقارير" %}</div>
                            </div>
                        </label>
                        {% if form.can_report.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.can_report.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Scope Settings -->
            <div class="form-section">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">
                    <i class="fas fa-map-marker-alt mr-2"></i>{% trans "نطاق الصلاحية" %}
                </h4>
                <p class="text-sm text-gray-600 mb-4">{% trans "حدد النطاق المخصوص لهذه الصلاحية. يمكن ترك جميع الحقول فارغة للحصول على صلاحية عامة." %}</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Franchises -->
                    <div>
                        <label for="{{ form.franchises.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-store mr-1"></i>{{ form.franchises.label }}
                        </label>
                        <div class="multi-select-container">
                            {{ form.franchises }}
                        </div>
                        {% if form.franchises.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.franchises.errors }}</div>
                        {% endif %}
                        <p class="text-xs text-gray-500 mt-1">{% trans "اختر الفرانشيز المسموح الوصول إليها" %}</p>
                    </div>
                    
                    <!-- Companies -->
                    <div>
                        <label for="{{ form.companies.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-building mr-1"></i>{{ form.companies.label }}
                        </label>
                        <div class="multi-select-container">
                            {{ form.companies }}
                        </div>
                        {% if form.companies.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.companies.errors }}</div>
                        {% endif %}
                        <p class="text-xs text-gray-500 mt-1">{% trans "اختر الشركات المسموح الوصول إليها" %}</p>
                    </div>
                    
                    <!-- Service Centers -->
                    <div>
                        <label for="{{ form.service_centers.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-cog mr-1"></i>{{ form.service_centers.label }}
                        </label>
                        <div class="multi-select-container">
                            {{ form.service_centers }}
                        </div>
                        {% if form.service_centers.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.service_centers.errors }}</div>
                        {% endif %}
                        <p class="text-xs text-gray-500 mt-1">{% trans "اختر مراكز الخدمة المسموح الوصول إليها" %}</p>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    <i class="fas fa-info-circle mr-1"></i>
                    {% trans "تأكد من تحديد الصلاحيات والنطاق المناسب قبل الحفظ" %}
                </div>
                <div class="flex space-x-3 rtl:space-x-reverse">
                    <a href="{% url 'setup:user_custom_permissions' user_profile.pk %}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg transition-colors">
                        {% trans "إلغاء" %}
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        {% if form_action == 'create' %}
                            <i class="fas fa-plus mr-2"></i>{% trans "إضافة الصلاحية" %}
                        {% else %}
                            <i class="fas fa-save mr-2"></i>{% trans "حفظ التغييرات" %}
                        {% endif %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add styling to form fields
    const selectFields = document.querySelectorAll('select');
    selectFields.forEach(field => {
        field.classList.add('w-full', 'px-3', 'py-2', 'border', 'border-gray-300', 'rounded-md', 
                           'focus:ring-blue-500', 'focus:border-blue-500');
    });
    
    const checkboxFields = document.querySelectorAll('input[type="checkbox"]');
    checkboxFields.forEach(field => {
        field.classList.add('rounded', 'text-blue-600', 'focus:ring-blue-500');
    });

    // Handle multi-select styling
    const multiSelectFields = document.querySelectorAll('select[multiple]');
    multiSelectFields.forEach(field => {
        field.classList.add('h-full');
    });
});
</script>
{% endblock %} 