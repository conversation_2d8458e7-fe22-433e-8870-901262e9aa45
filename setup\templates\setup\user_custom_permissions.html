{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
.permission-card {
    transition: all 0.3s ease;
    border-left: 4px solid #3b82f6;
}
.permission-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}
.permission-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}
.scope-info {
    background: #f8fafc;
    border-left: 3px solid #e2e8f0;
    padding: 0.5rem;
    margin: 0.5rem 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <nav class="flex mb-3" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="{% url 'setup:user_profile_list' %}" class="text-gray-700 hover:text-blue-600">
                            <i class="fas fa-users mr-2"></i>
                            {% trans "المستخدمين" %}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <span class="text-gray-500">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <span class="text-gray-500">{% trans "الصلاحيات المخصصة" %}</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-3xl font-bold text-gray-800">{{ page_title }}</h1>
            <p class="text-gray-600 mt-1">{{ page_subtitle }}</p>
        </div>
        <div class="flex space-x-3 rtl:space-x-reverse">
            <a href="{% url 'setup:user_custom_permission_create' user_profile.pk %}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                <i class="fas fa-plus mr-2"></i> {% trans "إضافة صلاحية مخصصة" %}
            </a>
            <a href="{% url 'setup:user_profile_list' %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                <i class="fas fa-arrow-left mr-2"></i> {% trans "العودة إلى المستخدمين" %}
            </a>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mr-4">
                <i class="fas fa-user text-2xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-gray-800">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</h3>
                <p class="text-gray-600">{{ user_profile.user.email }}</p>
                <div class="flex items-center mt-2">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-2">{{ user_profile.role.name }}</span>
                    {% if user_profile.franchise %}
                        <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full mr-2">
                            <i class="fas fa-store mr-1"></i>{{ user_profile.franchise.name }}
                        </span>
                    {% endif %}
                    {% if user_profile.company %}
                        <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full mr-2">
                            <i class="fas fa-building mr-1"></i>{{ user_profile.company.name }}
                        </span>
                    {% endif %}
                    {% if user_profile.service_center %}
                        <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                            <i class="fas fa-cog mr-1"></i>{{ user_profile.service_center.name }}
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Permissions List -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h6 class="text-lg font-semibold text-blue-600">
                {% trans "الصلاحيات المخصصة" %} ({{ permissions|length }} {% trans "صلاحية" %})
            </h6>
        </div>
        <div class="p-6">
            {% if permissions %}
                <div class="grid gap-4">
                    {% for permission in permissions %}
                    <div class="permission-card bg-white border border-gray-200 rounded-lg p-4">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <h4 class="text-lg font-medium text-gray-800 mr-3">{{ permission.module_tab.name }}</h4>
                                    {% if permission.is_active %}
                                        <span class="permission-badge bg-green-100 text-green-800">{% trans "نشط" %}</span>
                                    {% else %}
                                        <span class="permission-badge bg-red-100 text-red-800">{% trans "غير نشط" %}</span>
                                    {% endif %}
                                </div>
                                
                                <!-- Permission Details -->
                                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-3">
                                    {% if permission.can_view %}
                                        <span class="permission-badge bg-blue-100 text-blue-800">
                                            <i class="fas fa-eye mr-1"></i>{% trans "عرض" %}
                                        </span>
                                    {% endif %}
                                    {% if permission.can_add %}
                                        <span class="permission-badge bg-green-100 text-green-800">
                                            <i class="fas fa-plus mr-1"></i>{% trans "إضافة" %}
                                        </span>
                                    {% endif %}
                                    {% if permission.can_edit %}
                                        <span class="permission-badge bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-edit mr-1"></i>{% trans "تعديل" %}
                                        </span>
                                    {% endif %}
                                    {% if permission.can_delete %}
                                        <span class="permission-badge bg-red-100 text-red-800">
                                            <i class="fas fa-trash mr-1"></i>{% trans "حذف" %}
                                        </span>
                                    {% endif %}
                                    {% if permission.can_approve %}
                                        <span class="permission-badge bg-purple-100 text-purple-800">
                                            <i class="fas fa-check mr-1"></i>{% trans "موافقة" %}
                                        </span>
                                    {% endif %}
                                    {% if permission.can_report %}
                                        <span class="permission-badge bg-indigo-100 text-indigo-800">
                                            <i class="fas fa-chart-bar mr-1"></i>{% trans "تقارير" %}
                                        </span>
                                    {% endif %}
                                </div>

                                <!-- Scope Information -->
                                <div class="scope-info rounded">
                                    <h5 class="font-medium text-gray-700 mb-2">{% trans "نطاق الصلاحية:" %}</h5>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                                        {% if permission.franchises.exists %}
                                            <div>
                                                <strong>{% trans "الفرانشيز:" %}</strong>
                                                {% for franchise in permission.franchises.all %}
                                                    <span class="inline-block bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs mr-1 mb-1">{{ franchise.name }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if permission.companies.exists %}
                                            <div>
                                                <strong>{% trans "الشركات:" %}</strong>
                                                {% for company in permission.companies.all %}
                                                    <span class="inline-block bg-gray-50 text-gray-700 px-2 py-1 rounded text-xs mr-1 mb-1">{{ company.name }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if permission.service_centers.exists %}
                                            <div>
                                                <strong>{% trans "مراكز الخدمة:" %}</strong>
                                                {% for center in permission.service_centers.all %}
                                                    <span class="inline-block bg-green-50 text-green-700 px-2 py-1 rounded text-xs mr-1 mb-1">{{ center.name }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    {% if not permission.franchises.exists and not permission.companies.exists and not permission.service_centers.exists %}
                                        <p class="text-gray-500 text-sm">{% trans "لا يوجد نطاق محدد - صلاحية عامة" %}</p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex space-x-2 rtl:space-x-reverse ml-4">
                                <a href="{% url 'setup:user_custom_permission_edit' permission.pk %}" 
                                   class="bg-gray-100 hover:bg-gray-200 text-gray-600 px-3 py-2 rounded text-sm transition-colors" 
                                   title="{% trans 'تعديل الصلاحية' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'setup:user_custom_permission_toggle' permission.pk %}" 
                                   class="{% if permission.is_active %}bg-red-100 hover:bg-red-200 text-red-600{% else %}bg-green-100 hover:bg-green-200 text-green-600{% endif %} px-3 py-2 rounded text-sm transition-colors" 
                                   title="{% if permission.is_active %}{% trans 'إلغاء تفعيل' %}{% else %}{% trans 'تفعيل' %}{% endif %}">
                                    <i class="fas {% if permission.is_active %}fa-toggle-on{% else %}fa-toggle-off{% endif %}"></i>
                                </a>
                                <a href="{% url 'setup:user_custom_permission_delete' permission.pk %}" 
                                   class="bg-red-100 hover:bg-red-200 text-red-600 px-3 py-2 rounded text-sm transition-colors"
                                   title="{% trans 'حذف الصلاحية' %}"
                                   onclick="return confirm('هل أنت متأكد من حذف هذه الصلاحية؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="mb-4">
                        <i class="fas fa-key text-6xl text-gray-300"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-600 mb-3">{% trans "لا توجد صلاحيات مخصصة" %}</h4>
                    <p class="text-gray-500 mb-6">{% trans "لم يتم تعيين أي صلاحيات مخصصة لهذا المستخدم بعد" %}</p>
                    <div class="flex justify-center space-x-3 rtl:space-x-reverse">
                        <a href="{% url 'setup:user_custom_permission_create' user_profile.pk %}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                            <i class="fas fa-plus mr-2"></i> {% trans "إضافة صلاحية مخصصة" %}
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Available Modules Info -->
    {% if available_modules %}
    <div class="bg-blue-50 rounded-lg p-6 mt-6">
        <h4 class="text-lg font-semibold text-blue-800 mb-3">
            <i class="fas fa-info-circle mr-2"></i>{% trans "الوحدات المتاحة للصلاحيات المخصصة" %}
        </h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            {% for module in available_modules %}
                <div class="bg-white rounded-lg p-3 text-center">
                    <i class="fas fa-cog text-blue-600 text-xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-700">{{ module.name }}</p>
                </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %} 