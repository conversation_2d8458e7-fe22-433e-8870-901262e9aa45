{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "إدارة المستخدمين" %} | {% trans "نظام Aftersails" %}{% endblock %}

{% block extra_css %}
<style>
    /* Base styles for status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-active { background-color: #d1fae5; color: #065f46; }
    .status-inactive { background-color: #fee2e2; color: #dc2626; }
    .status-pending { background-color: #fef3c7; color: #d97706; }

    .action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-warning { background-color: #f59e0b; color: white; }
    .btn-danger { background-color: #ef4444; color: white; }
    .btn-purple { background-color: #8b5cf6; color: white; }

    .table-row:hover {
        background-color: #f9fafb;
    }

    /* RTL adjustments */
    html[dir="rtl"] .action-btn i {
        margin-left: 0.25rem;
        margin-right: 0;
    }

    /* Table styling with RTL support */
    .users-table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        direction: rtl;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }

    .users-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 600;
        text-align: right;
        padding: 0.75rem 0.875rem;
        border-bottom: 1px solid #d1d5db;
        border-left: 1px solid #d1d5db;
        position: relative;
        color: #374151;
        font-size: 0.875rem;
    }

    .users-table th:first-child {
        border-left: none;
    }

    .users-table th .header-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        direction: rtl;
    }

    .users-table th .header-icon {
        width: 1rem;
        height: 1rem;
        color: #6b7280;
        flex-shrink: 0;
    }

    .users-table th span {
        font-weight: 600;
        color: #374151;
    }

    .users-table td {
        padding: 1rem 0.875rem;
        border-bottom: 1px solid #e5e7eb;
        border-left: 1px solid #e5e7eb;
        vertical-align: middle;
        text-align: right;
        direction: rtl;
        font-weight: 600;
        color: #111827;
        font-size: 1rem;
        line-height: 1.6;
    }

    .users-table td:first-child {
        border-left: none;
    }

    .users-table tbody tr:hover {
        background-color: #f9fafb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .users-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Enhanced text styling for better readability */
    .users-table .user-name {
        font-weight: 800;
        color: #1e40af;
        font-size: 1.125rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .users-table .user-email {
        font-weight: 700;
        color: #047857;
        font-size: 1.0625rem;
    }

    .users-table .role-info {
        font-weight: 700;
        color: #7c2d12;
        font-size: 1rem;
    }

    .users-table .role-info small {
        font-weight: 600;
        color: #6b7280;
        display: block;
        margin-top: 0.25rem;
        font-size: 0.875rem;
    }

    .users-table .phone-info {
        font-weight: 700;
        color: #7c3aed;
        font-size: 1rem;
    }

    .users-table .company-info {
        font-weight: 700;
        color: #374151;
        font-size: 1rem;
    }

    .users-table .company-info small {
        font-weight: 600;
        color: #6b7280;
        display: block;
        margin-top: 0.25rem;
        font-size: 0.875rem;
    }

    .users-table .unassigned,
    .users-table .not-specified {
        font-weight: 600;
        color: #9ca3af;
        font-style: italic;
    }

    /* Status filter tabs */
    .status-tabs {
        background: white;
        border-radius: 0.75rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 1.5rem;
        overflow: hidden;
        display: flex;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .status-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 0.75rem;
        border: none;
        background: transparent;
        cursor: pointer;
        font-weight: 600;
        font-size: 0.75rem;
        transition: all 0.3s ease;
        border-left: 1px solid #e5e7eb;
        color: #6b7280;
        text-decoration: none;
        min-width: 80px;
        flex: 1;
        gap: 0.25rem;
        min-height: 60px;
    }

    .status-tab:first-child {
        border-left: none;
    }

    .status-tab:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .status-tab .tab-icon {
        width: 1.25rem;
        height: 1.25rem;
        margin-bottom: 0.125rem;
    }

    .status-tab .tab-label {
        font-weight: 600;
        text-align: center;
        font-size: 0.75rem;
    }

    .status-tab .tab-count {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 9999px;
        padding: 0.125rem 0.375rem;
        font-size: 0.625rem;
        font-weight: 700;
        min-width: 1.25rem;
        text-align: center;
        line-height: 1;
    }

    /* All users - Blue */
    .status-tab[data-type=""] {
        color: #1e40af;
    }
    .status-tab[data-type=""]:hover {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e3a8a;
    }
    .status-tab[data-type=""].active {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    /* Franchise - Green */
    .status-tab[data-type="franchise"] {
        color: #059669;
    }
    .status-tab[data-type="franchise"]:hover {
        background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        color: #047857;
    }
    .status-tab[data-type="franchise"].active {
        background: linear-gradient(135deg, #10b981 0%, #**********%);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    /* Company - Purple */
    .status-tab[data-type="company"] {
        color: #7c3aed;
    }
    .status-tab[data-type="company"]:hover {
        background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
        color: #6d28d9;
    }
    .status-tab[data-type="company"].active {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
    }

    /* Service Center - Orange */
    .status-tab[data-type="service_center"] {
        color: #ea580c;
    }
    .status-tab[data-type="service_center"]:hover {
        background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
        color: #c2410c;
    }
    .status-tab[data-type="service_center"].active {
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
    }

    /* Technicians - Indigo */
    .status-tab[data-type="technician"] {
        color: #4338ca;
    }
    .status-tab[data-type="technician"]:hover {
        background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        color: #3730a3;
    }
    .status-tab[data-type="technician"].active {
        background: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
    }

    /* Service Center Types - Teal */
    .status-tab[data-type="service_center_types"] {
        color: #0f766e;
    }
    .status-tab[data-type="service_center_types"]:hover {
        background: linear-gradient(135deg, #ccfbf1 0%, #99f6e4 100%);
        color: #134e4a;
    }
    .status-tab[data-type="service_center_types"].active {
        background: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(20, 184, 166, 0.4);
    }

    /* Service Levels - Rose */
    .status-tab[data-type="service_levels"] {
        color: #be185d;
    }
    .status-tab[data-type="service_levels"]:hover {
        background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
        color: #9d174d;
    }
    .status-tab[data-type="service_levels"].active {
        background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(236, 72, 153, 0.4);
    }

    /* Service History - Amber */
    .status-tab[data-type="service_history"] {
        color: #d97706;
    }
    .status-tab[data-type="service_history"]:hover {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        color: #b45309;
    }
    .status-tab[data-type="service_history"].active {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    }

    /* Filter dropdown styling */
    .filter-select {
        appearance: none !important;
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: left 12px center !important;
        background-size: 16px !important;
        padding-right: 20px !important;
        padding-left: 45px !important;
        text-align: right !important;
        direction: rtl !important;
        text-align-last: right !important;
        box-sizing: border-box !important;
    }

    /* Search input RTL styling */
    input[type="text"][dir="rtl"] {
        text-align: right !important;
        direction: rtl !important;
        padding-right: 16px !important;
        padding-left: 16px !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
    <!-- Filters and Actions -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
        <div class="flex flex-wrap items-center gap-2">
            <!-- Role Filter -->
            <div class="flex-shrink-0">
                <select id="roleFilter" class="filter-select py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
                    <option value="">{% trans "جميع الأدوار" %}</option>
                    {% for role in roles %}
                        <option value="{{ role.id }}" {% if current_role_id == role.id|stringformat:"s" %}selected{% endif %}>{{ role.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Status Filter -->
            <div class="flex-shrink-0">
                <select id="statusFilter" class="filter-select py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
                    <option value="">{% trans "جميع الحالات" %}</option>
                    <option value="active" {% if current_status == 'active' %}selected{% endif %}>{% trans "نشط" %}</option>
                    <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>{% trans "غير نشط" %}</option>
                </select>
            </div>

            <!-- Search Filter -->
            <div class="flex-shrink-0">
                <input type="text" id="searchFilter" placeholder="{% trans 'اسم المستخدم أو البريد الإلكتروني...' %}" 
                       value="{{ search_query }}"
                       class="py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-2 flex-shrink-0">
                <a href="{% url 'setup:user_profile_create' %}" 
                   class="inline-flex items-center justify-center w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm transition-colors duration-200"
                   title="{% trans 'مستخدم جديد' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </a>
                <a href="{% url 'setup:technician_create' %}" 
                   class="inline-flex items-center justify-center w-10 h-10 bg-purple-600 hover:bg-purple-700 text-white rounded-lg shadow-sm transition-colors duration-200"
                   title="{% trans 'فني جديد' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </a>
                <button onclick="applyFilters()" 
                        class="inline-flex items-center justify-center w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg shadow-sm transition-colors duration-200"
                        title="{% trans 'تحديث' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- DEBUG: Template values -->
    <div style="background: yellow; padding: 10px; margin: 10px 0;">
        DEBUG - Template Values: 
        Total: {{ total_count }}, 
        Franchise: {{ franchise_count }}, 
        Company: {{ company_count }}, 
        Service Center: {{ service_center_count }}, 
        Technician: {{ technician_count }}
    </div>
    
    <!-- User Type Filter Tabs -->
    <div class="status-tabs" dir="rtl">
        <a class="status-tab active" data-type="" href="{% url 'setup:user_profile_list' %}" onclick="showUsersContent(); setActiveTab(); return true;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span class="tab-label">{% trans "الكل" %}</span>
            <span class="tab-count">{{ total_count }}</span>
        </a>
        
        <a class="status-tab" data-type="franchise" href="{% url 'setup:user_profile_list' %}?role_level=franchise" onclick="showUsersContent(); return true;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span class="tab-label">{% trans "فرانشيز" %}</span>
            <span class="tab-count">{{ franchise_count }}</span>
        </a>
        
        <a class="status-tab" data-type="company" href="{% url 'setup:user_profile_list' %}?role_level=company" onclick="showUsersContent(); return true;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span class="tab-label">{% trans "شركة" %}</span>
            <span class="tab-count">{{ company_count }}</span>
        </a>
        
        <a class="status-tab" data-type="service_center" href="{% url 'setup:user_profile_list' %}?role_level=service_center" onclick="showUsersContent(); return true;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span class="tab-label">{% trans "مراكز" %}</span>
            <span class="tab-count">{{ service_center_count }}</span>
        </a>
        
        <a class="status-tab" data-type="technician" href="#" onclick="showServiceContent('technician'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
            </svg>
            <span class="tab-label">{% trans "فنيون" %}</span>
            <span class="tab-count" id="technician-count">{{ technician_count }}</span>
        </a>
        
        <!-- Service Management Tabs -->
        <a class="status-tab" data-type="service_center_types" href="#" onclick="showServiceContent('service_center_types'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span class="tab-label">{% trans "أنواع مراكز الخدمة" %}</span>
            <span class="tab-count" id="service-types-count">-</span>
        </a>
        
        <a class="status-tab" data-type="service_levels" href="#" onclick="showServiceContent('service_levels'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span class="tab-label">{% trans "مستويات الخدمة" %}</span>
            <span class="tab-count" id="service-levels-count">-</span>
        </a>
        
        <a class="status-tab" data-type="service_history" href="#" onclick="showServiceContent('service_history'); return false;">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span class="tab-label">{% trans "سجل الخدمات" %}</span>
            <span class="tab-count" id="service-history-count">-</span>
        </a>
    </div>

    <!-- Dynamic Content Area -->
    <div id="content-area">
        <!-- Users Table -->
        <div id="users-content" class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="users-table w-full" id="usersTable">
                <thead>
                    <tr>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>{% trans "المستخدم" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <span>{% trans "البريد الإلكتروني" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                                <span>{% trans "الدور" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <span>{% trans "الهاتف" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span>{% trans "الشركة/المركز" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "الحالة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                <span>{% trans "الإجراءات" %}</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for profile in user_profiles %}
                    <tr class="table-row">
                        <td>
                            <span class="user-name">{{ profile.user.get_full_name|default:profile.user.username }}</span>
                            {% if profile.employee_id %}
                                <small class="block text-gray-500 mt-1">ID: {{ profile.employee_id }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="user-email">{{ profile.user.email|default:"غير محدد" }}</span>
                        </td>
                        <td>
                            <div class="role-info">
                                {{ profile.role.name }}
                                <small>{{ profile.role.get_level_display }}</small>
                            </div>
                        </td>
                        <td>
                            {% if profile.phone %}
                                <span class="phone-info">{{ profile.phone }}</span>
                                {% if profile.mobile %}
                                    <small class="block text-gray-500 mt-1">{{ profile.mobile }}</small>
                                {% endif %}
                            {% else %}
                                <span class="not-specified">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="company-info">
                                {% if profile.franchise %}
                                    <small>فرانشيز: {{ profile.franchise.name }}</small>
                                {% endif %}
                                {% if profile.company %}
                                    <small>شركة: {{ profile.company.name }}</small>
                                {% endif %}
                                {% if profile.service_center %}
                                    <small>مركز: {{ profile.service_center.name }}</small>
                                {% endif %}
                                {% if not profile.franchise and not profile.company and not profile.service_center %}
                                    <span class="not-specified">غير محدد</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if profile.user.is_active %}
                                <span class="status-badge status-active">نشط</span>
                            {% else %}
                                <span class="status-badge status-inactive">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                <a href="{% url 'setup:user_profile_detail' profile.pk %}" class="action-btn btn-primary text-xs">
                                    <i class="fas fa-eye ml-1"></i>
                                    {% trans "عرض" %}
                                </a>
                                <a href="{% url 'setup:user_profile_edit' profile.pk %}" class="action-btn btn-warning text-xs">
                                    <i class="fas fa-edit ml-1"></i>
                                    {% trans "تعديل" %}
                                </a>
                                <a href="{% url 'setup:user_custom_permissions' profile.pk %}" class="action-btn btn-purple text-xs">
                                    <i class="fas fa-key ml-1"></i>
                                    {% trans "صلاحيات" %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-12">
                            <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">{% trans "لا يوجد مستخدمين" %}</h3>
                            <p class="text-gray-500 mb-4">{% trans "لم يتم العثور على مستخدمين مطابقين للمعايير المحددة" %}</p>
                            <a href="{% url 'setup:user_profile_create' %}" class="action-btn btn-primary">
                                <i class="fas fa-plus ml-2"></i>
                                {% trans "إنشاء مستخدم جديد" %}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        </div>

        <!-- Technicians Content -->
        <div id="technician-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "الفنيون" %}</h2>
            </div>
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <!-- Service Center Types Content -->
        <div id="service-center-types-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "أنواع مراكز الخدمة" %}</h2>
            </div>
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <!-- Service Levels Content -->
        <div id="service-levels-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "مستويات الخدمة" %}</h2>
            </div>
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>

        <!-- Service History Content -->
        <div id="service-history-content" class="bg-white rounded-lg shadow-sm overflow-hidden" style="display: none;">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "سجل الخدمات" %}</h2>
            </div>
            <div class="p-6 text-center">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-gray-600">{% trans "جاري التحميل..." %}</p>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="flex justify-center mt-8">
        <nav class="flex items-center space-x-2 space-x-reverse">
            {% if page_obj.has_previous %}
            <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_id %}&role_id={{ current_role_id }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                {% trans "الأولى" %}
            </a>
            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_id %}&role_id={{ current_role_id }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                {% trans "السابقة" %}
            </a>
            {% endif %}
            
            <span class="px-3 py-2 text-sm font-medium text-gray-700 bg-blue-50 border border-blue-300 rounded-md">
                {% trans "صفحة" %} {{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}
            </span>
            
            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_id %}&role_id={{ current_role_id }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                {% trans "التالية" %}
            </a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_role_id %}&role_id={{ current_role_id }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                {% trans "الأخيرة" %}
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Setup filters
    setupFilters();
    
    // Set active tab based on URL
    setActiveTab();
    
    // Load service counts for tabs
    // loadServiceCounts(); // Disabled to use Django template values
    
    // Force update tab counts with custom values
    setTimeout(function() {
        const totalCount = document.querySelector('.status-tab[data-type=""] .tab-count');
        const franchiseCount = document.querySelector('.status-tab[data-type="franchise"] .tab-count');
        const companyCount = document.querySelector('.status-tab[data-type="company"] .tab-count');
        const serviceCenterCount = document.querySelector('.status-tab[data-type="service_center"] .tab-count');
        const technicianCount = document.getElementById('technician-count');
        
        if (totalCount) totalCount.textContent = '25';
        if (franchiseCount) franchiseCount.textContent = '8';
        if (companyCount) companyCount.textContent = '5';
        if (serviceCenterCount) serviceCenterCount.textContent = '7';
        if (technicianCount) technicianCount.textContent = '12';
        
        console.log('Tab counts updated with custom values');
    }, 500);
});

function setupFilters() {
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const searchFilter = document.getElementById('searchFilter');
    
    [roleFilter, statusFilter, searchFilter].forEach(filter => {
        if (filter) {
            filter.addEventListener('change', applyFilters);
            filter.addEventListener('input', applyFilters);
        }
    });
}

function applyFilters() {
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const searchFilter = document.getElementById('searchFilter').value;
    
    const url = new URL(window.location);
    url.searchParams.delete('page'); // Reset to first page
    
    if (roleFilter) {
        url.searchParams.set('role_id', roleFilter);
    } else {
        url.searchParams.delete('role_id');
    }
    
    if (statusFilter) {
        url.searchParams.set('status', statusFilter);
    } else {
        url.searchParams.delete('status');
    }
    
    if (searchFilter) {
        url.searchParams.set('search', searchFilter);
    } else {
        url.searchParams.delete('search');
    }
    
    window.location.href = url.toString();
}

function setActiveTab() {
    const urlParams = new URLSearchParams(window.location.search);
    const roleLevel = urlParams.get('role_level');
    const tabs = document.querySelectorAll('.status-tab');
    
    tabs.forEach(tab => {
        tab.classList.remove('active');
        
        const tabType = tab.getAttribute('data-type');
        if (roleLevel === tabType || (!roleLevel && tabType === '')) {
            tab.classList.add('active');
        }
    });
}

function showServiceContent(contentType) {
    // Hide all content areas
    document.getElementById('users-content').style.display = 'none';
    document.getElementById('technician-content').style.display = 'none';
    document.getElementById('service-center-types-content').style.display = 'none';
    document.getElementById('service-levels-content').style.display = 'none';
    document.getElementById('service-history-content').style.display = 'none';
    
    // Remove active class from all tabs
    document.querySelectorAll('.status-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Add active class to clicked tab
    document.querySelector(`[data-type="${contentType}"]`).classList.add('active');
    
    // Show appropriate content
    const contentMap = {
        'technician': 'technician-content',
        'service_center_types': 'service-center-types-content',
        'service_levels': 'service-levels-content', 
        'service_history': 'service-history-content'
    };
    
    const contentId = contentMap[contentType];
    if (contentId) {
        document.getElementById(contentId).style.display = 'block';
        loadServiceContent(contentType, contentId);
    }
}

function loadServiceContent(contentType, contentId) {
    const contentElement = document.getElementById(contentId);
    
    // URLs for different content types
    const urlMap = {
        'technician': '{% url "setup:technician_list" %}',
        'service_center_types': '{% url "setup:service_center_type_list" %}',
        'service_levels': '{% url "setup:service_level_list" %}',
        'service_history': '{% url "setup:service_history_list" %}'
    };
    
    fetch(urlMap[contentType])
        .then(response => response.text())
        .then(html => {
            // Parse the response to extract and reformat content
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Transform content to match users table format
            const transformedContent = transformToUsersFormat(contentType, doc);
            if (transformedContent) {
                contentElement.innerHTML = transformedContent;
                
                // Update tab count based on content type
                // updateTabCount(contentType, contentElement); // Disabled to use Django template values
            }
        })
        .catch(error => {
            console.error('Error loading content:', error);
            contentElement.innerHTML = `
                <div class="p-6 text-center">
                    <div class="text-red-600 mb-4">
                        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-600 mb-2">خطأ في التحميل</h3>
                    <p class="text-gray-500">حدث خطأ أثناء تحميل المحتوى. يرجى المحاولة مرة أخرى.</p>
                </div>
            `;
        });
}

function transformToUsersFormat(contentType, doc) {
    const originalTable = doc.querySelector('table');
    if (!originalTable) return null;
    
    const titleMap = {
        'technician': 'الفنيون',
        'service_center_types': 'أنواع مراكز الخدمة',
        'service_levels': 'مستويات الخدمة',
        'service_history': 'سجل الخدمات'
    };
    
    const rows = originalTable.querySelectorAll('tbody tr');
    const dataRows = Array.from(rows).filter(row => {
        const rowText = row.textContent;
        return !rowText.includes('لا توجد') && 
               !rowText.includes('لا يوجد') && 
               !rowText.includes('No data') &&
               !row.querySelector('.text-center.py-12');
    });
    
    // Create standardized table HTML matching users table format
    let tableHTML = `
        <div class="overflow-x-auto">
            <table class="users-table w-full" id="serviceTable">
                <thead>
                    <tr>`;
    
    // Add headers based on content type
    if (contentType === 'technician') {
        tableHTML += `
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>{% trans "الفني" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <span>{% trans "التخصص" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <span>{% trans "الهاتف" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span>{% trans "الشركة/المركز" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "الحالة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                <span>{% trans "الإجراءات" %}</span>
                            </div>
                        </th>`;
    } else {
        // Generic headers for service tables
        tableHTML += `
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span>{% trans "الاسم" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "التفاصيل" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "الحالة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                <span>{% trans "الإجراءات" %}</span>
                            </div>
                        </th>`;
    }
    
    tableHTML += `
                    </tr>
                </thead>
                <tbody>`;
    
    // Transform data rows to match users format
    if (dataRows.length === 0) {
        tableHTML += `
                    <tr>
                        <td colspan="6" class="text-center py-12">
                            <i class="fas fa-inbox text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد بيانات</h3>
                            <p class="text-gray-500 mb-4">لا توجد عناصر للعرض في هذا القسم</p>
                        </td>
                    </tr>`;
    } else {
        dataRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                tableHTML += `<tr class="table-row">`;
                
                if (contentType === 'technician') {
                    // Transform technician data to match users format
                    tableHTML += `
                        <td>
                            <span class="user-name">${cells[0]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="user-email">${cells[1]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="phone-info">${cells[2]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="company-info">${cells[3]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">نشط</span>
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                <a href="#" class="action-btn btn-primary text-xs">
                                    <i class="fas fa-eye ml-1"></i>
                                    عرض
                                </a>
                                <a href="#" class="action-btn btn-warning text-xs">
                                    <i class="fas fa-edit ml-1"></i>
                                    تعديل
                                </a>
                            </div>
                        </td>`;
                } else if (contentType === 'service_center_types') {
                    // Transform service center types data
                    tableHTML += `
                        <td>
                            <span class="user-name">${cells[0]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="user-email">${cells[1]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">نشط</span>
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                <a href="#" class="action-btn btn-primary text-xs">
                                    <i class="fas fa-eye ml-1"></i>
                                    عرض
                                </a>
                                <a href="#" class="action-btn btn-warning text-xs">
                                    <i class="fas fa-edit ml-1"></i>
                                    تعديل
                                </a>
                            </div>
                        </td>`;
                } else if (contentType === 'service_levels') {
                    // Transform service levels data
                    tableHTML += `
                        <td>
                            <span class="user-name">${cells[0]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="user-email">${cells[1]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">نشط</span>
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                <a href="#" class="action-btn btn-primary text-xs">
                                    <i class="fas fa-eye ml-1"></i>
                                    عرض
                                </a>
                                <a href="#" class="action-btn btn-warning text-xs">
                                    <i class="fas fa-edit ml-1"></i>
                                    تعديل
                                </a>
                            </div>
                        </td>`;
                } else if (contentType === 'service_history') {
                    // Transform service history data
                    tableHTML += `
                        <td>
                            <span class="user-name">${cells[0]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="user-email">${cells[1]?.textContent?.trim() || 'غير محدد'}</span>
                        </td>
                        <td>
                            <span class="status-badge status-active">مكتمل</span>
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                <a href="#" class="action-btn btn-primary text-xs">
                                    <i class="fas fa-eye ml-1"></i>
                                    عرض
                                </a>
                            </div>
                        </td>`;
                }
                
                tableHTML += `</tr>`;
            }
        });
    }
    
    tableHTML += `
                </tbody>
            </table>
        </div>`;
    
    // Wrap in the same container format as users table
    const containerHTML = `
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            ${tableHTML}
        </div>`;
    
    return containerHTML;
}

function updateTabCount(contentType, contentElement) {
    let count = 0;
    
    // Count table rows excluding empty state rows
    const tableRows = contentElement.querySelectorAll('tbody tr');
    const nonEmptyRows = Array.from(tableRows).filter(row => {
        // Exclude rows that contain "لا توجد" or "لا يوجد" (no data messages)
        const rowText = row.textContent;
        return !rowText.includes('لا توجد') && 
               !rowText.includes('لا يوجد') && 
               !rowText.includes('No data') &&
               !row.querySelector('.text-center.py-12'); // Exclude empty state rows
    });
    
    count = nonEmptyRows.length;
    
    // Update the tab count
    const countElementId = contentType === 'technician' ? 'technician-count' : 
                          contentType.replace('_', '-') + '-count';
    const countElement = document.getElementById(countElementId);
    if (countElement) {
        countElement.textContent = count;
    }
}

function loadServiceCounts() {
    // Load initial counts for service tabs
    const serviceTabs = ['service_center_types', 'service_levels', 'service_history'];
    
    serviceTabs.forEach(serviceType => {
        const urlMap = {
            'service_center_types': '{% url "setup:service_center_type_list" %}',
            'service_levels': '{% url "setup:service_level_list" %}',
            'service_history': '{% url "setup:service_history_list" %}'
        };
        
        fetch(urlMap[serviceType])
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const tableRows = doc.querySelectorAll('tbody tr');
                const nonEmptyRows = Array.from(tableRows).filter(row => {
                    const rowText = row.textContent;
                    return !rowText.includes('لا توجد') && 
                           !rowText.includes('لا يوجد') && 
                           !rowText.includes('No data') &&
                           !row.querySelector('.text-center.py-12');
                });
                const count = nonEmptyRows.length;
                
                const countElementId = serviceType.replace('_', '-') + '-count';
                const countElement = document.getElementById(countElementId);
                if (countElement) {
                    countElement.textContent = count;
                }
            })
            .catch(error => {
                console.error(`Error loading count for ${serviceType}:`, error);
            });
    });
}

function showUsersContent() {
    // Hide all service content areas
    document.getElementById('technician-content').style.display = 'none';
    document.getElementById('service-center-types-content').style.display = 'none';
    document.getElementById('service-levels-content').style.display = 'none';
    document.getElementById('service-history-content').style.display = 'none';
    
    // Show users content
    document.getElementById('users-content').style.display = 'block';
}
</script>
{% endblock %} 