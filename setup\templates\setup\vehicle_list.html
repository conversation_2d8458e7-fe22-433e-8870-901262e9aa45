{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
.search-form {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-4" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-gray-800">{{ page_title }}</h1>
            <p class="text-muted">{{ page_subtitle }}</p>
        </div>
        <a href="{% url 'setup:vehicle_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans "إضافة مركبة جديدة" %}
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card search-form text-white mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">{% trans "البحث" %}</label>
                    <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                           placeholder="{% trans 'البحث في المركبات...' %}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "المالك" %}</label>
                    <select class="form-control" name="owner">
                        <option value="">{% trans "جميع الملاك" %}</option>
                        {% for customer in customers %}
                            <option value="{{ customer.id }}" {% if current_filters.owner == customer.id|stringformat:"s" %}selected{% endif %}>
                                {{ customer.full_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{% trans "الماركة" %}</label>
                    <input type="text" class="form-control" name="make" value="{{ current_filters.make }}" 
                           placeholder="{% trans 'الماركة' %}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">{% trans "من سنة" %}</label>
                    <input type="number" class="form-control" name="year_from" value="{{ current_filters.year_from }}" 
                           placeholder="{% trans 'من سنة' %}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-light me-2">
                        <i class="fas fa-search"></i> {% trans "بحث" %}
                    </button>
                    <a href="{% url 'setup:vehicle_list' %}" class="btn btn-outline-light">
                        <i class="fas fa-times"></i> {% trans "مسح" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                {% trans "قائمة المركبات" %} ({{ vehicles|length }} {% trans "مركبة" %})
            </h6>
        </div>
        <div class="card-body">
            {% if vehicles %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>{% trans "رقم اللوحة" %}</th>
                                <th>{% trans "المالك" %}</th>
                                <th>{% trans "الماركة والموديل" %}</th>
                                <th>{% trans "السنة" %}</th>
                                <th>{% trans "VIN" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for vehicle in vehicles %}
                            <tr>
                                <td>
                                    <strong>{{ vehicle.license_plate }}</strong>
                                </td>
                                <td>
                                    {% if vehicle.owner %}
                                        {{ vehicle.owner.full_name }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ vehicle.make }} {{ vehicle.model }}</strong>
                                    {% if vehicle.engine_type %}
                                        <br><small class="text-muted">{{ vehicle.engine_type }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ vehicle.year|default:"-" }}</td>
                                <td>
                                    {% if vehicle.vin %}
                                        <small>{{ vehicle.vin|truncatechars:12 }}</small>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'setup:vehicle_detail' vehicle.pk %}" 
                                           class="btn btn-sm btn-outline-info" title="{% trans 'عرض التفاصيل' %}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'setup:vehicle_edit' vehicle.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="{% trans 'تعديل' %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="{% trans 'Page navigation' %}">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filters.owner %}&owner={{ current_filters.owner }}{% endif %}{% if current_filters.make %}&make={{ current_filters.make }}{% endif %}">{% trans "الأولى" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filters.owner %}&owner={{ current_filters.owner }}{% endif %}{% if current_filters.make %}&make={{ current_filters.make }}{% endif %}">{% trans "السابقة" %}</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filters.owner %}&owner={{ current_filters.owner }}{% endif %}{% if current_filters.make %}&make={{ current_filters.make }}{% endif %}">{% trans "التالية" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filters.owner %}&owner={{ current_filters.owner }}{% endif %}{% if current_filters.make %}&make={{ current_filters.make }}{% endif %}">{% trans "الأخيرة" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-car fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "لا توجد مركبات" %}</h5>
                    <p class="text-muted">{% trans "ابدأ بإضافة مركبة جديدة" %}</p>
                    <a href="{% url 'setup:vehicle_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "إضافة مركبة جديدة" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 