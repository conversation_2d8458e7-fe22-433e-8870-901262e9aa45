{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
.search-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-4" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-gray-800">{{ page_title }}</h1>
            <p class="text-muted">{{ page_subtitle }}</p>
        </div>
        <a href="{% url 'setup:vehicle_make_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans "إضافة ماركة جديدة" %}
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card search-form text-white mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">{% trans "البحث" %}</label>
                    <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                           placeholder="{% trans 'البحث في الماركات...' %}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans "الحالة" %}</label>
                    <select class="form-control" name="status">
                        <option value="">{% trans "جميع الحالات" %}</option>
                        <option value="active" {% if current_status == 'active' %}selected{% endif %}>{% trans "نشط" %}</option>
                        <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>{% trans "غير نشط" %}</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-light me-2">
                        <i class="fas fa-search"></i> {% trans "بحث" %}
                    </button>
                    <a href="{% url 'setup:vehicle_make_list' %}" class="btn btn-outline-light">
                        <i class="fas fa-times"></i> {% trans "مسح" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                {% trans "قائمة ماركات المركبات" %} ({{ vehicle_makes|length }} {% trans "ماركة" %})
            </h6>
        </div>
        <div class="card-body">
            {% if vehicle_makes %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>{% trans "الماركة" %}</th>
                                <th>{% trans "بلد المنشأ" %}</th>
                                <th>{% trans "الوصف" %}</th>
                                <th>{% trans "الحالة" %}</th>
                                <th>{% trans "تاريخ الإنشاء" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for make in vehicle_makes %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if make.logo %}
                                            <img src="{{ make.logo.url }}" alt="{{ make.name }}" 
                                                 class="rounded me-2" style="width: 32px; height: 32px;">
                                        {% else %}
                                            <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-2" 
                                                 style="width: 32px; height: 32px; font-size: 14px;">
                                                {{ make.name|first }}
                                            </div>
                                        {% endif %}
                                        <strong>{{ make.name }}</strong>
                                    </div>
                                </td>
                                <td>{{ make.country_of_origin|default:"-" }}</td>
                                <td>{{ make.description|truncatechars:50|default:"-" }}</td>
                                <td>
                                    {% if make.is_active %}
                                        <span class="badge bg-success">{% trans "نشط" %}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ make.created_at|date:"Y-m-d" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'setup:vehicle_make_edit' make.pk %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'setup:vehicle_model_list' %}?make_id={{ make.pk }}" 
                                           class="btn btn-sm btn-outline-info" title="{% trans 'عرض الموديلات' %}">
                                            <i class="fas fa-list"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="{% trans 'Page navigation' %}">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">{% trans "الأولى" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">{% trans "السابقة" %}</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">{% trans "التالية" %}</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">{% trans "الأخيرة" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-car fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "لا توجد ماركات مركبات" %}</h5>
                    <p class="text-muted">{% trans "ابدأ بإضافة ماركة مركبة جديدة" %}</p>
                    <a href="{% url 'setup:vehicle_make_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "إضافة ماركة جديدة" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 