{% extends "base.html" %}
{% load i18n static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
<style>
    .transfer-details {
        margin-bottom: 20px;
    }
    .transfer-actions {
        margin-top: 30px;
        margin-bottom: 20px;
    }
    .status-badge {
        font-size: 1rem;
        padding: 5px 10px;
        border-radius: 5px;
    }
    .status-pending {
        background-color: #ffc107;
        color: #212529;
    }
    .status-approved {
        background-color: #17a2b8;
        color: white;
    }
    .status-completed {
        background-color: #28a745;
        color: white;
    }
    .status-rejected {
        background-color: #dc3545;
        color: white;
    }
    .status-cancelled {
        background-color: #6c757d;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1>{{ title }}</h1>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "Transfer Information" %}</h5>
            <span class="status-badge status-{{ transfer.status }}">
                {{ transfer.get_status_display }}
            </span>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>{% trans "Transfer Date:" %}</strong> {{ transfer.transfer_date }}</p>
                    <p><strong>{% trans "Vehicle:" %}</strong> {{ transfer.vehicle }}</p>
                    <p><strong>{% trans "Previous Owner:" %}</strong> {{ transfer.previous_owner }}</p>
                    <p><strong>{% trans "New Owner:" %}</strong> {{ transfer.new_owner }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>{% trans "Sale Price:" %}</strong> {{ transfer.sale_price|default:"-" }}</p>
                    <p><strong>{% trans "Odometer Reading:" %}</strong> {{ transfer.odometer_reading|default:"-" }}</p>
                    {% if transfer.approved_by %}
                    <p><strong>{% trans "Approved By:" %}</strong> {{ transfer.approved_by }}</p>
                    <p><strong>{% trans "Approval Date:" %}</strong> {{ transfer.approved_date }}</p>
                    {% endif %}
                </div>
            </div>
            
            {% if transfer.notes %}
            <div class="mb-3">
                <h6>{% trans "Notes:" %}</h6>
                <p>{{ transfer.notes|linebreaks }}</p>
            </div>
            {% endif %}
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">{% trans "Vehicle Details" %}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>{% trans "Make:" %}</strong> {{ transfer.vehicle.make }}</p>
                    <p><strong>{% trans "Model:" %}</strong> {{ transfer.vehicle.model }}</p>
                    <p><strong>{% trans "Year:" %}</strong> {{ transfer.vehicle.year }}</p>
                    <p><strong>{% trans "License Plate:" %}</strong> {{ transfer.vehicle.license_plate }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>{% trans "VIN:" %}</strong> {{ transfer.vehicle.vin|default:"-" }}</p>
                    <p><strong>{% trans "Service Center:" %}</strong> {{ transfer.vehicle.service_center|default:"-" }}</p>
                    <p><strong>{% trans "Current Owner:" %}</strong> {{ transfer.vehicle.owner|default:"-" }}</p>
                </div>
            </div>
        </div>
    </div>
    
    {% if transfer.status == 'pending' or transfer.status == 'approved' %}
    <div class="transfer-actions">
        <h5>{% trans "Actions" %}</h5>
        <div class="action-buttons d-flex flex-wrap">
            {% if transfer.status == 'pending' %}
            <form method="post" class="me-2 mb-2">
                {% csrf_token %}
                <input type="hidden" name="action" value="approve">
                <button type="submit" class="btn btn-primary">
                    {% trans "Approve Transfer" %}
                </button>
            </form>
            {% endif %}
            
            {% if transfer.status == 'approved' %}
            <form method="post" class="me-2 mb-2">
                {% csrf_token %}
                <input type="hidden" name="action" value="complete">
                <button type="submit" class="btn btn-success">
                    {% trans "Complete Transfer" %}
                </button>
            </form>
            {% endif %}
            
            <form method="post" class="me-2 mb-2">
                {% csrf_token %}
                <input type="hidden" name="action" value="reject">
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                    {% trans "Reject Transfer" %}
                </button>
            </form>
            
            <form method="post" class="me-2 mb-2">
                {% csrf_token %}
                <input type="hidden" name="action" value="cancel">
                <button type="submit" class="btn btn-secondary">
                    {% trans "Cancel Transfer" %}
                </button>
            </form>
        </div>
    </div>
    
    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="post">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="reject">
                    
                    <div class="modal-header">
                        <h5 class="modal-title" id="rejectModalLabel">{% trans "Reject Transfer" %}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="reason">{% trans "Reason for Rejection:" %}</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                        <button type="submit" class="btn btn-danger">{% trans "Reject" %}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endif %}
    
    <div class="mt-3">
        <a href="{% url 'admin:setup_vehicleownershiptransfer_changelist' %}" class="btn btn-secondary">
            {% trans "Back to Transfers" %}
        </a>
        <a href="{% url 'setup:vehicle_detail' pk=transfer.vehicle.pk %}" class="btn btn-info">
            {% trans "View Vehicle" %}
        </a>
    </div>
</div>
{% endblock %} 