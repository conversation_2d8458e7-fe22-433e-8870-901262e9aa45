{% extends "base.html" %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid p-4" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1 text-gray-800">{{ page_title }}</h1>
            <p class="text-muted">{{ page_subtitle }}</p>
        </div>
        <a href="{% url 'setup:vehicle_transfer_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans "إضافة نقل ملكية" %}
        </a>
    </div>

    <!-- Results -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                {% trans "نقل ملكية المركبات" %} ({{ transfers|length }} {% trans "عملية" %})
            </h6>
        </div>
        <div class="card-body">
            {% if transfers %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>{% trans "المركبة" %}</th>
                                <th>{% trans "المالك السابق" %}</th>
                                <th>{% trans "المالك الجديد" %}</th>
                                <th>{% trans "تاريخ النقل" %}</th>
                                <th>{% trans "سعر البيع" %}</th>
                                <th>{% trans "قراءة العداد" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transfer in transfers %}
                            <tr>
                                <td>
                                    <strong>{{ transfer.vehicle.license_plate }}</strong>
                                    <br><small class="text-muted">{{ transfer.vehicle.make }} {{ transfer.vehicle.model }}</small>
                                </td>
                                <td>{{ transfer.previous_owner.full_name }}</td>
                                <td>{{ transfer.new_owner.full_name }}</td>
                                <td>{{ transfer.transfer_date|date:"Y-m-d" }}</td>
                                <td>
                                    {% if transfer.sale_price %}
                                        {{ transfer.sale_price|floatformat:2 }} {% trans "ريال" %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if transfer.odometer_reading %}
                                        {{ transfer.odometer_reading|floatformat:0 }} {% trans "كم" %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "لا توجد عمليات نقل ملكية" %}</h5>
                    <p class="text-muted">{% trans "ابدأ بإضافة عملية نقل ملكية جديدة" %}</p>
                    <a href="{% url 'setup:vehicle_transfer_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "إضافة نقل ملكية" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 