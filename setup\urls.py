from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from . import views

# Create a router for API viewsets
router = DefaultRouter()
# router.register(r'api/service-center-makes-models', views.ServiceCenterMakeModelViewSet)
# router.register(r'api/vehicle-makes', views.VehicleMakeViewSet)
# router.register(r'api/vehicle-models', views.VehicleModelViewSet)

app_name = 'setup'

urlpatterns = [
    # Dashboard
    path('', views.setup_dashboard, name='dashboard'),
    
    # Franchise Management
    path('franchises/', views.FranchiseListView.as_view(), name='franchise_list'),
    path('franchises/<uuid:pk>/', views.FranchiseDetailView.as_view(), name='franchise_detail'),
    path('franchises/create/', views.FranchiseCreateView.as_view(), name='franchise_create'),
    path('franchises/<uuid:pk>/edit/', views.FranchiseUpdateView.as_view(), name='franchise_edit'),
    
    # Company Management
    path('companies/', views.CompanyListView.as_view(), name='company_list'),
    path('companies/<uuid:pk>/', views.CompanyDetailView.as_view(), name='company_detail'),
    path('companies/create/', views.CompanyCreateView.as_view(), name='company_create'),
    path('companies/<uuid:pk>/edit/', views.CompanyUpdateView.as_view(), name='company_edit'),
    
    # Customer Management
    path('customers/', views.CustomerListView.as_view(), name='customer_list'),
    path('customers/<uuid:pk>/', views.CustomerDetailView.as_view(), name='customer_detail'),
    path('customers/create/', views.CustomerCreateView.as_view(), name='customer_create'),
    path('customers/<uuid:pk>/edit/', views.CustomerUpdateView.as_view(), name='customer_edit'),
    
    # Vehicle Management
    path('vehicles/', views.VehicleListView.as_view(), name='vehicle_list'),
    path('vehicles/<uuid:pk>/', views.VehicleDetailView.as_view(), name='vehicle_detail'),
    path('vehicles/create/', views.VehicleCreateView.as_view(), name='vehicle_create'),
    path('vehicles/<uuid:pk>/edit/', views.VehicleUpdateView.as_view(), name='vehicle_edit'),
    
    # Service Center Management
    path('service-centers/', views.ServiceCenterListView.as_view(), name='service_center_list'),
    path('service-centers/<uuid:pk>/', views.ServiceCenterDetailView.as_view(), name='service_center_detail'),
    path('service-centers/create/', views.ServiceCenterCreateView.as_view(), name='service_center_create'),
    path('service-centers/<uuid:pk>/edit/', views.ServiceCenterUpdateView.as_view(), name='service_center_edit'),
    
    # User Role Management
    path('user-roles/', views.UserRoleListView.as_view(), name='user_role_list'),
    path('user-roles/<uuid:pk>/', views.UserRoleDetailView.as_view(), name='user_role_detail'),
    path('user-roles/create/', views.UserRoleCreateView.as_view(), name='user_role_create'),
    path('user-roles/<uuid:pk>/edit/', views.UserRoleUpdateView.as_view(), name='user_role_edit'),
    
    # User Profile Management
    path('users/', views.UserProfileListView.as_view(), name='user_profile_list'),
    path('users/<uuid:pk>/', views.UserProfileDetailView.as_view(), name='user_profile_detail'),
    path('users/create/', views.UserProfileCreateView.as_view(), name='user_profile_create'),
    path('users/<uuid:pk>/edit/', views.UserProfileUpdateView.as_view(), name='user_profile_edit'),
    
    # User Custom Permissions Management
    path('users/<uuid:user_id>/permissions/', views.UserCustomPermissionListView.as_view(), name='user_custom_permissions'),
    path('users/<uuid:user_id>/permissions/create/', views.UserCustomPermissionCreateView.as_view(), name='user_custom_permission_create'),
    path('permissions/<uuid:pk>/edit/', views.UserCustomPermissionUpdateView.as_view(), name='user_custom_permission_edit'),
    path('permissions/<uuid:pk>/delete/', views.UserCustomPermissionDeleteView.as_view(), name='user_custom_permission_delete'),
    path('permissions/<uuid:permission_id>/toggle/', views.toggle_user_custom_permission, name='user_custom_permission_toggle'),
    
    # Technician Management
    path('technicians/', views.TechnicianProfileListView.as_view(), name='technician_list'),
    path('technicians/<uuid:pk>/', views.TechnicianProfileDetailView.as_view(), name='technician_detail'),
    path('technicians/create/', views.TechnicianProfileCreateView.as_view(), name='technician_create'),
    path('technicians/<uuid:pk>/edit/', views.TechnicianProfileUpdateView.as_view(), name='technician_edit'),
    
    # Technician Specializations
    path('specializations/', views.TechnicianSpecializationListView.as_view(), name='specialization_list'),
    path('specializations/create/', views.TechnicianSpecializationCreateView.as_view(), name='specialization_create'),
    path('specializations/<uuid:pk>/edit/', views.TechnicianSpecializationUpdateView.as_view(), name='specialization_edit'),
    
    # Warehouse Assignments
    path('warehouse-assignments/', views.UserWarehouseAssignmentListView.as_view(), name='warehouse_assignment_list'),
    path('warehouse-assignments/create/', views.UserWarehouseAssignmentCreateView.as_view(), name='warehouse_assignment_create'),
    path('warehouse-assignments/<uuid:pk>/edit/', views.UserWarehouseAssignmentUpdateView.as_view(), name='warehouse_assignment_edit'),
    
    # Vehicle Makes and Models
    path('vehicle-makes/', views.VehicleMakeListView.as_view(), name='vehicle_make_list'),
    path('vehicle-makes/create/', views.VehicleMakeCreateView.as_view(), name='vehicle_make_create'),
    path('vehicle-makes/<uuid:pk>/edit/', views.VehicleMakeUpdateView.as_view(), name='vehicle_make_edit'),
    
    path('vehicle-models/', views.VehicleModelListView.as_view(), name='vehicle_model_list'),
    path('vehicle-models/create/', views.VehicleModelCreateView.as_view(), name='vehicle_model_create'),
    path('vehicle-models/<uuid:pk>/edit/', views.VehicleModelUpdateView.as_view(), name='vehicle_model_edit'),
    
    # Service Center Types
    path('service-center-types/', views.ServiceCenterTypeListView.as_view(), name='service_center_type_list'),
    path('service-center-types/create/', views.ServiceCenterTypeCreateView.as_view(), name='service_center_type_create'),
    path('service-center-types/<uuid:pk>/edit/', views.ServiceCenterTypeUpdateView.as_view(), name='service_center_type_edit'),
    
    # Service Center Makes & Models
    path('service-center-makes-models/', views.ServiceCenterMakeModelListView.as_view(), name='service_center_make_model_list'),
    path('service-center-makes-models/create/', views.ServiceCenterMakeModelCreateView.as_view(), name='service_center_make_model_create'),
    path('service-center-makes-models/<uuid:pk>/edit/', views.ServiceCenterMakeModelUpdateView.as_view(), name='service_center_make_model_edit'),
    
    # Service Levels
    path('service-levels/', views.ServiceLevelListView.as_view(), name='service_level_list'),
    path('service-levels/create/', views.ServiceLevelCreateView.as_view(), name='service_level_create'),
    path('service-levels/<uuid:pk>/edit/', views.ServiceLevelUpdateView.as_view(), name='service_level_edit'),
    
    # Service History
    path('service-history/', views.ServiceHistoryListView.as_view(), name='service_history_list'),
    
    # Vehicle Ownership Transfers
    path('vehicle-transfers/', views.VehicleOwnershipTransferListView.as_view(), name='vehicle_transfer_list'),
    path('vehicle-transfers/create/', views.VehicleOwnershipTransferCreateView.as_view(), name='vehicle_transfer_create'),
    
    # API Endpoints
    path('api/search-customers/', views.api_search_customers, name='api_search_customers'),
    path('api/customer/<uuid:customer_id>/vehicles/', views.api_get_customer_vehicles, name='api_customer_vehicles'),
    path('api/vehicle-models/', views.api_get_vehicle_models, name='api_vehicle_models'),
    path('api/transfer-vehicle/', views.api_transfer_vehicle, name='api_transfer_vehicle'),
    
    # AJAX endpoints for cascading dropdowns
    path('api/franchises/', views.api_get_franchises, name='api_franchises'),
    path('api/companies-by-franchise/', views.api_get_companies_by_franchise, name='api_companies_by_franchise'),
    path('api/service-centers-by-company/', views.api_get_service_centers_by_company, name='api_service_centers_by_company'),
    path('api/user-profiles-by-role/', views.api_get_user_profiles_by_role, name='api_user_profiles_by_role'),
    
    # Include router URLs
    path('', include(router.urls)),
] 