import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.conf import settings

def is_arabic_text(text):
    """
    Check if text contains only Arabic characters and allowed symbols
    """
    if not text:
        return True
    
    # Arabic Unicode ranges and allowed characters
    arabic_pattern = r'^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\u0980-\u09FF\s\-\.]*$'
    return bool(re.match(arabic_pattern, text.strip()))

def validate_arabic_name(name):
    """
    Validate that a name contains only Arabic characters
    """
    if not name:
        return
    
    if not is_arabic_text(name):
        raise ValidationError(_('يجب أن يحتوي الاسم على حروف عربية فقط'))

def validate_egyptian_national_id(id_number):
    """
    Validate Egyptian National ID format
    Egyptian ID: 14 digits XYYMMDDLLSSS
    X: Birth century (2 for 1900-1999, 3 for 2000-2099)
    YYMMDD: Birth date
    LL: Governorate code
    SSS: Sequential number
    """
    if not id_number:
        return
    
    # Remove any spaces or dashes
    clean_id = re.sub(r'[\s\-]', '', str(id_number))
    
    # Check if it's exactly 14 digits
    if not re.match(r'^\d{14}$', clean_id):
        raise ValidationError(_('الرقم القومي المصري يجب أن يكون 14 رقماً'))
    
    # Extract components
    century = int(clean_id[0])
    year = int(clean_id[1:3])
    month = int(clean_id[3:5])
    day = int(clean_id[5:7])
    governorate = int(clean_id[7:9])
    
    # Validate century (2 for 1900s, 3 for 2000s)
    if century not in [2, 3]:
        raise ValidationError(_('الرقم القومي غير صحيح - رقم القرن يجب أن يكون 2 أو 3'))
    
    # Validate month
    if month < 1 or month > 12:
        raise ValidationError(_('الرقم القومي غير صحيح - الشهر غير صالح'))
    
    # Validate day
    if day < 1 or day > 31:
        raise ValidationError(_('الرقم القومي غير صحيح - اليوم غير صالح'))
    
    # Validate governorate code (01-35 for Egyptian governorates)
    if governorate < 1 or governorate > 35:
        raise ValidationError(_('الرقم القومي غير صحيح - كود المحافظة غير صالح'))

def validate_egyptian_phone(phone):
    """
    Validate Egyptian phone number format
    """
    if not phone:
        return
    
    # Remove spaces, hyphens, and parentheses
    clean_phone = re.sub(r'[\s\-\(\)]', '', str(phone))
    
    # Egyptian phone patterns:
    # Mobile: 01[0-9]{9} or +201[0-9]{9}
    # Landline: 0[2-9][0-9]{7,8} or +20[2-9][0-9]{7,8}
    
    patterns = [
        r'^01[0-9]{9}$',           # Mobile: 01xxxxxxxxx
        r'^\+2001[0-9]{9}$',       # Mobile with country code: +2001xxxxxxxxx
        r'^0[2-9][0-9]{7,8}$',     # Landline: 0xxxxxxxx or 0xxxxxxxxx
        r'^\+20[2-9][0-9]{7,8}$',  # Landline with country code: +20xxxxxxxx
    ]
    
    if not any(re.match(pattern, clean_phone) for pattern in patterns):
        raise ValidationError(_('رقم الهاتف المصري غير صحيح'))

def get_current_language():
    """
    Get current language code
    """
    return getattr(settings, 'LANGUAGE_CODE', 'ar')

def is_egyptian_context():
    """
    Check if we're in Egyptian context based on timezone and language
    """
    return (
        getattr(settings, 'TIME_ZONE', '') == 'Africa/Cairo' or
        get_current_language() == 'ar'
    )

def validate_commercial_registration(registration_number):
    """
    Validate Egyptian commercial registration number
    """
    if not registration_number:
        return
    
    # Remove spaces and dashes
    clean_reg = re.sub(r'[\s\-]', '', str(registration_number))
    
    # Egyptian commercial registration is typically 7-10 digits
    if not re.match(r'^\d{7,10}$', clean_reg):
        raise ValidationError(_('رقم السجل التجاري يجب أن يكون من 7 إلى 10 أرقام')) 