from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, F, Sum
from django.utils import timezone
from core.views import TenantCreateView, TenantListView, TenantDetailView, TenantUpdateView
import json

# Import models
from .models import (
    Customer, Vehicle, ServiceCenter, Company, Franchise, VehicleMake, VehicleModel,
    UserProfile, TechnicianSpecialization, TechnicianProfile, UserWarehouseAssignment,
    ServiceCenterType, ServiceCenterMakeModel, ServiceLevel, ServiceHistory, VehicleOwnershipTransfer
)
# Import UserRole from user_roles app (not setup app)
from user_roles.models import UserRole

# Import forms
from .forms import (
    CustomerForm, VehicleForm, ServiceCenterForm, CompanyForm, FranchiseForm,
    UserRoleForm, UserProfileForm, TechnicianSpecializationForm, TechnicianProfileForm,
    UserWarehouseAssignmentForm, CombinedUserCreationForm
)

@login_required
def setup_dashboard(request):
    """Dashboard for setup module with comprehensive statistics"""
    # Get tenant_id from request (set by middleware)
    tenant_id = getattr(request, 'tenant_id', None)
    
    # Apply role-based filtering for data access
    user = request.user
    is_superuser = user.is_superuser
    

    
    # Initialize filter collections for role-based access
    allowed_franchises = []
    allowed_companies = []
    allowed_service_centers = []
    
    # Check if user should be filtered by role (similar to main dashboard logic)
    if is_superuser:
        # Superuser gets all data (with tenant filtering)
        if tenant_id:
            franchise_queryset = Franchise.objects.filter(tenant_id=tenant_id)
            company_queryset = Company.objects.filter(tenant_id=tenant_id)
            service_center_queryset = ServiceCenter.objects.filter(tenant_id=tenant_id)
            customer_queryset = Customer.objects.filter(tenant_id=tenant_id)
            vehicle_queryset = Vehicle.objects.filter(tenant_id=tenant_id)
        else:
            franchise_queryset = Franchise.objects.all()
            company_queryset = Company.objects.all()
            service_center_queryset = ServiceCenter.objects.all()
            customer_queryset = Customer.objects.all()
            vehicle_queryset = Vehicle.objects.all()
    else:
        # Apply role-based filtering for non-superusers
        from user_roles.services import DataFilterService
        
        # Get user's data scope based on their roles
        user_data_scope = DataFilterService.get_user_data_scope(user)
        scope_level = user_data_scope.get('scope', 'none')
        scope_filters = user_data_scope.get('filters', {})
        
        # Base querysets with tenant filtering
        base_filters = {}
        if tenant_id:
            base_filters['tenant_id'] = tenant_id
            
        # Apply scope-based filtering
        if scope_level == 'system':
            # System level access - see everything
            franchise_queryset = Franchise.objects.filter(**base_filters)
            company_queryset = Company.objects.filter(**base_filters)
            service_center_queryset = ServiceCenter.objects.filter(**base_filters)
            customer_queryset = Customer.objects.filter(**base_filters)
            vehicle_queryset = Vehicle.objects.filter(**base_filters)
        elif scope_level == 'franchise':
            # Franchise level access
            franchise_id = scope_filters.get('franchise_id')
            if franchise_id:
                franchise_queryset = Franchise.objects.filter(id=franchise_id, **base_filters)
                company_queryset = Company.objects.filter(franchise_id=franchise_id, **base_filters)
                service_center_queryset = ServiceCenter.objects.filter(company__franchise_id=franchise_id, **base_filters)
                customer_queryset = Customer.objects.filter(service_center__company__franchise_id=franchise_id, **base_filters)
                vehicle_queryset = Vehicle.objects.filter(service_center__company__franchise_id=franchise_id, **base_filters)
            else:
                franchise_queryset = Franchise.objects.none()
                company_queryset = Company.objects.none()
                service_center_queryset = ServiceCenter.objects.none()
                customer_queryset = Customer.objects.none()
                vehicle_queryset = Vehicle.objects.none()
        elif scope_level == 'company':
            # Company level access
            company_id = scope_filters.get('company_id')
            if company_id:
                franchise_queryset = Franchise.objects.filter(companies__id=company_id, **base_filters).distinct()
                company_queryset = Company.objects.filter(id=company_id, **base_filters)
                service_center_queryset = ServiceCenter.objects.filter(company_id=company_id, **base_filters)
                customer_queryset = Customer.objects.filter(service_center__company_id=company_id, **base_filters)
                vehicle_queryset = Vehicle.objects.filter(service_center__company_id=company_id, **base_filters)
            else:
                franchise_queryset = Franchise.objects.none()
                company_queryset = Company.objects.none()
                service_center_queryset = ServiceCenter.objects.none()
                customer_queryset = Customer.objects.none()
                vehicle_queryset = Vehicle.objects.none()
        elif scope_level == 'service_center':
            # Service center level access
            service_center_id = scope_filters.get('service_center_id')
            if service_center_id:
                franchise_queryset = Franchise.objects.filter(companies__service_centers__id=service_center_id, **base_filters).distinct()
                company_queryset = Company.objects.filter(service_centers__id=service_center_id, **base_filters).distinct()
                service_center_queryset = ServiceCenter.objects.filter(id=service_center_id, **base_filters)
                customer_queryset = Customer.objects.filter(service_center_id=service_center_id, **base_filters)
                vehicle_queryset = Vehicle.objects.filter(service_center_id=service_center_id, **base_filters)
            else:
                franchise_queryset = Franchise.objects.none()
                company_queryset = Company.objects.none()
                service_center_queryset = ServiceCenter.objects.none()
                customer_queryset = Customer.objects.none()
                vehicle_queryset = Vehicle.objects.none()
        else:
            # No access or unknown scope
            franchise_queryset = Franchise.objects.none()
            company_queryset = Company.objects.none()
            service_center_queryset = ServiceCenter.objects.none()
            customer_queryset = Customer.objects.none()
            vehicle_queryset = Vehicle.objects.none()
    
    # Get inventory statistics
    try:
        from inventory.models import Item
        if tenant_id:
            inventory_queryset = Item.objects.filter(tenant_id=tenant_id)
        else:
            inventory_queryset = Item.objects.all()
        total_items = inventory_queryset.count()
        low_stock_items = inventory_queryset.filter(quantity__lte=F('min_stock_level')).count()
    except ImportError:
        total_items = 0
        low_stock_items = 0
    
    # Get work orders statistics
    try:
        from work_orders.models import WorkOrder
        if tenant_id:
            work_order_queryset = WorkOrder.objects.filter(tenant_id=tenant_id)
        else:
            work_order_queryset = WorkOrder.objects.all()
        total_work_orders = work_order_queryset.count()
        pending_work_orders = work_order_queryset.filter(status='pending').count()
    except ImportError:
        total_work_orders = 0
        pending_work_orders = 0
    
    # Get sales statistics
    try:
        from sales.models import SalesOrder
        if tenant_id:
            sales_queryset = SalesOrder.objects.filter(tenant_id=tenant_id)
        else:
            sales_queryset = SalesOrder.objects.all()
        total_sales = sales_queryset.count()
        this_month_sales = sales_queryset.filter(
            order_date__gte=timezone.now().replace(day=1)
        ).count()
    except ImportError:
        total_sales = 0
        this_month_sales = 0
    
    # Calculate user counts with role-based filtering
    from .models import UserProfile, TechnicianProfile
    
    # Get user profile counts based on role-based filtering
    if tenant_id:
        user_profile_base = UserProfile.objects.filter(tenant_id=tenant_id).select_related('user', 'role', 'franchise', 'company', 'service_center')
    else:
        user_profile_base = UserProfile.objects.all().select_related('user', 'role', 'franchise', 'company', 'service_center')
    
    # Apply role-based filtering for user counts
    if is_superuser:
        # Superuser sees all user profiles
        filtered_user_profiles = user_profile_base
    else:
        # Apply role-based filtering for non-superusers
        from user_roles.services import DataFilterService
        
        # Get user's data scope based on their roles
        user_data_scope = DataFilterService.get_user_data_scope(request.user)
        scope_level = user_data_scope.get('scope', 'none')
        scope_filters = user_data_scope.get('filters', {})
        
        # Apply scope-based filtering for user profiles
        if scope_level == 'franchise':
            franchise_id = scope_filters.get('franchise_id')
            if franchise_id:
                filtered_user_profiles = user_profile_base.filter(franchise_id=franchise_id)
            else:
                filtered_user_profiles = user_profile_base.none()
        elif scope_level == 'company':
            company_id = scope_filters.get('company_id')
            if company_id:
                filtered_user_profiles = user_profile_base.filter(company_id=company_id)
            else:
                filtered_user_profiles = user_profile_base.none()
        elif scope_level == 'service_center':
            service_center_id = scope_filters.get('service_center_id')
            if service_center_id:
                filtered_user_profiles = user_profile_base.filter(service_center_id=service_center_id)
            else:
                filtered_user_profiles = user_profile_base.none()
        else:
            # System level or superuser - see all
            filtered_user_profiles = user_profile_base
    
    # Calculate user counts based on filtered data
    total_count = filtered_user_profiles.count()
    franchise_count = filtered_user_profiles.filter(franchise__isnull=False).count()
    company_count = filtered_user_profiles.filter(company__isnull=False).count()
    service_center_count = filtered_user_profiles.filter(service_center__isnull=False).count()
    
    # Count technicians with same filtering logic
    if tenant_id:
        technician_base = TechnicianProfile.objects.filter(tenant_id=tenant_id)
    else:
        technician_base = TechnicianProfile.objects.all()
    
    if is_superuser:
        technician_count = technician_base.count()
    else:
        # Apply role-based filtering for technicians
        if scope_level == 'franchise':
            franchise_id = scope_filters.get('franchise_id')
            if franchise_id:
                technician_count = technician_base.filter(
                    user_profile__service_center__company__franchise_id=franchise_id
                ).count()
            else:
                technician_count = 0
        elif scope_level == 'company':
            company_id = scope_filters.get('company_id')
            if company_id:
                technician_count = technician_base.filter(
                    user_profile__service_center__company_id=company_id
                ).count()
            else:
                technician_count = 0
        elif scope_level == 'service_center':
            service_center_id = scope_filters.get('service_center_id')
            if service_center_id:
                technician_count = technician_base.filter(
                    user_profile__service_center_id=service_center_id
                ).count()
            else:
                technician_count = 0
        else:
            technician_count = technician_base.count()
    
    # Get business management tab counts
    business_management_counts = {}
    
    # Get vehicle ownership transfers count
    if tenant_id:
        vehicle_transfer_count = VehicleOwnershipTransfer.objects.filter(tenant_id=tenant_id).count()
    else:
        vehicle_transfer_count = VehicleOwnershipTransfer.objects.all().count()
    
    # Get franchise-related counts
    try:
        from franchise_setup.models import FranchiseAgreement, FranchiseCompliance, FranchiseFee, RevenueShare
        
        franchise_agreement_count = FranchiseAgreement.objects.count()
        franchise_compliance_count = FranchiseCompliance.objects.count()
        franchise_fee_count = FranchiseFee.objects.count()
        revenue_share_count = RevenueShare.objects.count()
    except ImportError:
        franchise_agreement_count = 0
        franchise_compliance_count = 0
        franchise_fee_count = 0
        revenue_share_count = 0
    
    # Get quality check counts
    try:
        from work_orders.models import WorkOrderQualityCheck
        quality_check_count = WorkOrderQualityCheck.objects.count()
    except ImportError:
        quality_check_count = 0
    
    business_management_counts = {
        'vehicle_transfers': vehicle_transfer_count,
        'franchise_agreements': franchise_agreement_count,
        'compliance_records': franchise_compliance_count,
        'quality_checks': quality_check_count,
        'fee_structures': franchise_fee_count,
        'revenue_sharing': revenue_share_count,
    }
    
    context = {
       
        # Core Setup Statistics
        'total_franchises': franchise_queryset.count(),
        'total_companies': company_queryset.count(),
        'total_service_centers': service_center_queryset.count(),
        'active_service_centers': service_center_queryset.filter(is_active=True).count(),
        
        # Customer & Vehicle Statistics
        'total_customers': customer_queryset.count(),
        'total_vehicles': vehicle_queryset.count(),
        'active_customers': customer_queryset.filter(is_active=True).count(),
        
        # User counts with role-based filtering
        'total_count': total_count,
        'franchise_count': franchise_count, 
        'company_count': company_count,
        'service_center_count': service_center_count,
        'technician_count': technician_count,
        
        # User profile data for display in tabs
        'user_profiles': filtered_user_profiles.select_related('user', 'role', 'franchise', 'company', 'service_center'),
        'franchise_user_profiles': filtered_user_profiles.filter(franchise__isnull=False).select_related('user', 'role', 'franchise'),
        'company_user_profiles': filtered_user_profiles.filter(company__isnull=False).select_related('user', 'role', 'company'),
        'service_center_user_profiles': filtered_user_profiles.filter(service_center__isnull=False).select_related('user', 'role', 'service_center'),
        'technician_profiles': technician_base.select_related('user_profile__user', 'user_profile__service_center'),
        
        # Business Statistics
        'total_items': total_items,
        'low_stock_items': low_stock_items,
        'total_work_orders': total_work_orders,
        'pending_work_orders': pending_work_orders,
        'total_sales': total_sales,
        'this_month_sales': this_month_sales,
        
        # Recent additions
        'recent_customers': customer_queryset.order_by('-created_at')[:5],
        'recent_vehicles': vehicle_queryset.select_related('customer', 'make', 'model').order_by('-created_at')[:5],
        'recent_franchises': franchise_queryset.order_by('-created_at')[:3],
        'recent_companies': company_queryset.order_by('-created_at')[:5],
        'recent_service_centers': service_center_queryset.select_related('company').order_by('-created_at')[:5],
        
        # Monthly statistics
        'customers_this_month': customer_queryset.filter(
            created_at__gte=timezone.now().replace(day=1)
        ).count(),
        'vehicles_this_month': vehicle_queryset.filter(
            created_at__gte=timezone.now().replace(day=1)
        ).count(),
        'companies_this_month': company_queryset.filter(
            created_at__gte=timezone.now().replace(day=1)
        ).count(),
        
        # Business management tab counts
        'business_management_counts': business_management_counts,
        
        # Data quality indicators
        'data_health': {
            'franchises_with_companies': franchise_queryset.annotate(
                company_count=Count('companies')
            ).filter(company_count__gt=0).count(),
            'companies_with_service_centers': company_queryset.annotate(
                service_center_count=Count('service_centers')
            ).filter(service_center_count__gt=0).count(),
            'customers_with_vehicles': customer_queryset.annotate(
                vehicle_count=Count('owned_vehicles')
            ).filter(vehicle_count__gt=0).count(),
        }
    }
    
    return render(request, 'setup/dashboard.html', context)


# ==================== CUSTOMER MANAGEMENT ====================

class CustomerListView(TenantListView):
    """List view for customers"""
    model = Customer
    template_name = 'setup/customer_list.html'
    context_object_name = 'customers'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone__icontains=search_query) |
                Q(national_id__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        # Customer type filter
        customer_type = self.request.GET.get('customer_type', '')
        if customer_type:
            queryset = queryset.filter(customer_type=customer_type)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('قائمة العملاء'),
            'page_subtitle': _('إدارة عملاء الشركة'),
            'search_query': self.request.GET.get('search', ''),
            'current_status': self.request.GET.get('status', ''),
            'current_customer_type': self.request.GET.get('customer_type', ''),
            'customer_types': Customer.CUSTOMER_TYPES,
        })
        return context


class CustomerDetailView(TenantDetailView):
    """Detail view for individual customer"""
    model = Customer
    template_name = 'setup/customer_detail.html'
    context_object_name = 'customer'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        customer = self.get_object()
        
        # Get customer vehicles
        tenant_id = getattr(self.request, 'tenant_id', None)
        vehicles = Vehicle.objects.filter(
            tenant_id=tenant_id,
            owner=customer
        ).select_related('owner').order_by('-created_at') if tenant_id else Vehicle.objects.filter(
            owner=customer
        ).select_related('owner').order_by('-created_at')
        
        # Get recent work orders (if available)
        recent_work_orders = []
        try:
            from work_orders.models import WorkOrder
            recent_work_orders = WorkOrder.objects.filter(
                tenant_id=tenant_id,
                customer=customer
            ).select_related('vehicle').order_by('-created_at')[:10] if tenant_id else WorkOrder.objects.filter(
                customer=customer
            ).select_related('vehicle').order_by('-created_at')[:10]
        except ImportError:
            pass
        
        context.update({
            'page_title': customer.full_name,
            'page_subtitle': _('تفاصيل العميل'),
            'vehicles': vehicles,
            'vehicles_count': vehicles.count(),
            'recent_work_orders': recent_work_orders,
            'work_orders_count': len(recent_work_orders),
        })
        return context


class CustomerCreateView(TenantCreateView):
    """Create view for new customers"""
    model = Customer
    form_class = CustomerForm
    template_name = 'setup/customer_form.html'
    success_url = reverse_lazy('setup:customer_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة عميل جديد'),
            'page_subtitle': _('إدخال بيانات عميل جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة العميل بنجاح'))
        return super().form_valid(form)


class CustomerUpdateView(TenantUpdateView):
    """Update view for customers"""
    model = Customer
    form_class = CustomerForm
    template_name = 'setup/customer_form.html'
    success_url = reverse_lazy('setup:customer_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل العميل'),
            'page_subtitle': f'{_("تحديث بيانات")} {self.get_object().full_name}',
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات العميل بنجاح'))
        return super().form_valid(form)


# ==================== VEHICLE MANAGEMENT ====================

class VehicleListView(TenantListView):
    """List view for vehicles"""
    model = Vehicle
    template_name = 'setup/vehicle_list.html'
    context_object_name = 'vehicles'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('owner')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(vin__icontains=search_query) |
                Q(license_plate__icontains=search_query) |
                Q(owner__first_name__icontains=search_query) |
                Q(owner__last_name__icontains=search_query) |
                Q(make__icontains=search_query) |
                Q(model__icontains=search_query)
            )
        
        # Owner filter
        owner_id = self.request.GET.get('owner', '')
        if owner_id:
            queryset = queryset.filter(owner_id=owner_id)
        
        # Make filter
        make_filter = self.request.GET.get('make', '')
        if make_filter:
            queryset = queryset.filter(make__icontains=make_filter)
        
        # Year range filter
        year_from = self.request.GET.get('year_from', '')
        year_to = self.request.GET.get('year_to', '')
        if year_from:
            queryset = queryset.filter(year__gte=year_from)
        if year_to:
            queryset = queryset.filter(year__lte=year_to)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('قائمة المركبات'),
            'page_subtitle': _('إدارة مركبات العملاء'),
            'search_query': self.request.GET.get('search', ''),
            'customers': Customer.objects.filter(
                tenant_id=getattr(self.request, 'tenant_id', None),
                is_active=True
            ).order_by('first_name', 'last_name'),
            'current_filters': {
                'owner': self.request.GET.get('owner', ''),
                'make': self.request.GET.get('make', ''),
                'year_from': self.request.GET.get('year_from', ''),
                'year_to': self.request.GET.get('year_to', ''),
            },
        })
        return context


class VehicleDetailView(TenantDetailView):
    """Detail view for individual vehicle"""
    model = Vehicle
    template_name = 'setup/vehicle_detail.html'
    context_object_name = 'vehicle'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        vehicle = self.get_object()
        
        # Get vehicle work orders (if available)
        recent_work_orders = []
        try:
            from work_orders.models import WorkOrder
            recent_work_orders = WorkOrder.objects.filter(
                tenant_id=getattr(self.request, 'tenant_id', None),
                vehicle=vehicle
            ).order_by('-created_at')[:10]
        except ImportError:
            pass
        
        # Get maintenance history
        maintenance_history = []
        try:
            from work_orders.models import MaintenanceRecord
            maintenance_history = MaintenanceRecord.objects.filter(
                tenant_id=getattr(self.request, 'tenant_id', None),
                vehicle=vehicle
            ).order_by('-service_date')[:10]
        except ImportError:
            pass
        
        context.update({
            'page_title': f'{vehicle.make} {vehicle.model} - {vehicle.license_plate}',
            'page_subtitle': _('تفاصيل المركبة'),
            'recent_work_orders': recent_work_orders,
            'maintenance_history': maintenance_history,
            'work_orders_count': len(recent_work_orders),
        })
        return context


class VehicleCreateView(TenantCreateView):
    """Create view for new vehicles"""
    model = Vehicle
    form_class = VehicleForm
    template_name = 'setup/vehicle_form.html'
    success_url = reverse_lazy('setup:vehicle_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة مركبة جديدة'),
            'page_subtitle': _('تسجيل مركبة جديدة'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة المركبة بنجاح'))
        return super().form_valid(form)


class VehicleUpdateView(TenantUpdateView):
    """Update view for vehicles"""
    model = Vehicle
    form_class = VehicleForm
    template_name = 'setup/vehicle_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_success_url(self):
        return reverse('setup:vehicle_detail', kwargs={'pk': self.object.pk})
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل المركبة'),
            'page_subtitle': f'{_("تحديث بيانات")} {self.get_object().license_plate}',
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات المركبة بنجاح'))
        return super().form_valid(form)


# ==================== SERVICE CENTER MANAGEMENT ====================

class ServiceCenterListView(TenantListView):
    """List view for service centers"""
    model = ServiceCenter
    template_name = 'setup/service_center_list.html'
    context_object_name = 'service_centers'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(address__icontains=search_query) |
                Q(phone__icontains=search_query) |
                Q(email__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get counts for statistics
        queryset = self.get_queryset()
        total_service_centers = queryset.count()
        active_service_centers = queryset.filter(is_active=True).count()
        
        # Get related counts
        if hasattr(self.request, 'tenant_id') and self.request.tenant_id:
            total_franchises = Franchise.objects.filter(tenant_id=self.request.tenant_id).count()
            total_companies = Company.objects.filter(tenant_id=self.request.tenant_id).count()
            total_customers = Customer.objects.filter(tenant_id=self.request.tenant_id).count()
        else:
            total_franchises = Franchise.objects.count()
            total_companies = Company.objects.count()
            total_customers = Customer.objects.count()
        
        # Calculate capacity statistics
        total_capacity = queryset.aggregate(total=Sum('capacity'))['total'] or 0
        
        context.update({
            'page_title': _('مراكز الخدمة'),
            'page_subtitle': _('إدارة مراكز الخدمة والصيانة'),
            'search_query': self.request.GET.get('search', ''),
            'current_status': self.request.GET.get('status', ''),
            
            # Statistics
            'total_service_centers': total_service_centers,
            'active_service_centers': active_service_centers,
            'inactive_service_centers': total_service_centers - active_service_centers,
            'total_franchises': total_franchises,
            'total_companies': total_companies,
            'total_customers': total_customers,
            'total_capacity': total_capacity,
        })
        return context


class ServiceCenterCreateView(TenantCreateView):
    """Create view for new service centers"""
    model = ServiceCenter
    form_class = ServiceCenterForm
    template_name = 'setup/service_center_form.html'
    success_url = reverse_lazy('setup:service_center_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة مركز خدمة جديد'),
            'page_subtitle': _('تسجيل مركز خدمة جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة مركز الخدمة بنجاح'))
        return super().form_valid(form)


class ServiceCenterDetailView(TenantDetailView):
    """Detail view for individual service center"""
    model = ServiceCenter
    template_name = 'setup/service_center_detail.html'
    context_object_name = 'service_center'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        service_center = self.get_object()
        
        # Get recent work orders (if available)
        recent_work_orders = []
        try:
            from work_orders.models import WorkOrder
            tenant_id = getattr(self.request, 'tenant_id', None)
            recent_work_orders = WorkOrder.objects.filter(
                tenant_id=tenant_id,
                service_center=service_center
            ).order_by('-created_at')[:10]
        except ImportError:
            pass
        
        context.update({
            'page_title': service_center.name,
            'page_subtitle': _('تفاصيل مركز الخدمة'),
            'recent_work_orders': recent_work_orders,
            'work_orders_count': len(recent_work_orders),
        })
        return context


class ServiceCenterUpdateView(TenantUpdateView):
    """Update view for service centers"""
    model = ServiceCenter
    form_class = ServiceCenterForm
    template_name = 'setup/service_center_form.html'
    success_url = reverse_lazy('setup:service_center_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل مركز الخدمة'),
            'page_subtitle': f'{_("تحديث بيانات")} {self.get_object().name}',
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات مركز الخدمة بنجاح'))
        return super().form_valid(form)


# ==================== API ENDPOINTS ====================

@login_required
def api_search_customers(request):
    """API endpoint for customer search"""
    query = request.GET.get('q', '')
    tenant_id = getattr(request, 'tenant_id', None)
    customers = Customer.objects.filter(
        tenant_id=tenant_id,
        name__icontains=query
    )[:10]
    
    results = []
    for customer in customers:
        results.append({
            'id': str(customer.id),
                                'text': customer.full_name,
            'email': customer.email,
            'phone': customer.phone,
        })
    
    return JsonResponse({'results': results})


@login_required
def api_get_customer_vehicles(request, customer_id):
    """API endpoint to get vehicles for a specific customer"""
    try:
        tenant_id = getattr(request, 'tenant_id', None)
        customer = Customer.objects.get(
            pk=customer_id,
            tenant_id=tenant_id
        )
        
        vehicles = Vehicle.objects.filter(
            owner=customer,
            tenant_id=tenant_id
        ).select_related('owner')
        
        vehicle_data = []
        for vehicle in vehicles:
            vehicle_data.append({
                'id': str(vehicle.id),
                'text': f'{vehicle.make} {vehicle.model} - {vehicle.license_plate}',
                'make': vehicle.make,
                'model': vehicle.model,
                'year': vehicle.year,
                'license_plate': vehicle.license_plate,
                'vin': vehicle.vin,
            })
        
        return JsonResponse({
            'success': True,
            'vehicles': vehicle_data,
            'customer': {
                'id': str(customer.id),
                'name': customer.full_name,
                'email': customer.email,
                'phone': customer.phone,
            }
        })
        
    except Customer.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': _('العميل غير موجود')
        }, status=404)


@login_required
def api_get_vehicle_models(request):
    """API endpoint to get vehicle models for a specific make"""
    make_id = request.GET.get('make_id')
    
    if not make_id:
        return JsonResponse({'error': _('معرف الماركة مطلوب')}, status=400)
    
    try:
        tenant_id = getattr(request, 'tenant_id', None)
        models = VehicleModel.objects.filter(
            make_id=make_id,
            tenant_id=tenant_id
        ).order_by('name')
        
        model_data = []
        for model in models:
            model_data.append({
                'id': str(model.id),
                'name': model.name,
                'text': model.name,
            })
        
        return JsonResponse({
            'success': True,
            'models': model_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required 
def api_transfer_vehicle(request):
    """API endpoint to transfer vehicle to another customer"""
    if request.method != 'POST':
        return JsonResponse({'error': _('طريقة الطلب غير صحيحة')}, status=405)
    
    try:
        data = json.loads(request.body)
        vehicle_id = data.get('vehicle_id')
        new_customer_id = data.get('new_customer_id')
        
        if not vehicle_id or not new_customer_id:
            return JsonResponse({
                'success': False,
                'error': _('معرف المركبة والعميل الجديد مطلوبان')
            }, status=400)
        
        # Get vehicle and verify ownership
        tenant_id = getattr(request, 'tenant_id', None)
        vehicle = Vehicle.objects.get(
            pk=vehicle_id,
            tenant_id=tenant_id
        )
        
        # Get new customer and verify
        new_customer = Customer.objects.get(
            pk=new_customer_id,
            tenant_id=tenant_id
        )
        
        # Check if vehicle has active work orders
        try:
            from work_orders.models import WorkOrder
            active_work_orders = WorkOrder.objects.filter(
                vehicle=vehicle,
                status__in=['pending', 'in_progress']
            ).exists()
            
            if active_work_orders:
                return JsonResponse({
                    'success': False,
                    'error': _('لا يمكن نقل المركبة حيث توجد أوامر عمل نشطة عليها')
                })
        except ImportError:
            pass
        
        # Perform transfer
        old_customer = vehicle.owner
        vehicle.owner = new_customer
        vehicle.save()
        
        # Log transfer (you can create a transfer history model if needed)
        
        return JsonResponse({
            'success': True,
            'message': _('تم نقل المركبة بنجاح'),
            'vehicle': {
                'id': str(vehicle.id),
                'license_plate': vehicle.license_plate,
                'old_customer': old_customer.full_name,
                'new_customer': new_customer.full_name,
            }
        })
        
    except Vehicle.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': _('المركبة غير موجودة')
        }, status=404)
    except Customer.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': _('العميل غير موجود')
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


# ==================== COMPANY MANAGEMENT ====================

class CompanyListView(TenantListView):
    """List view for companies"""
    model = Company
    template_name = 'setup/company_list.html'
    context_object_name = 'companies'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(code__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        # Franchise filter
        franchise_id = self.request.GET.get('franchise', '')
        if franchise_id:
            queryset = queryset.filter(franchise_id=franchise_id)
        
        return queryset.select_related('franchise').order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get counts for statistics
        queryset = self.get_queryset()
        total_companies = queryset.count()
        active_companies = queryset.filter(is_active=True).count()
        
        # Get related counts
        if hasattr(self.request, 'tenant_id') and self.request.tenant_id:
            total_franchises = Franchise.objects.filter(tenant_id=self.request.tenant_id).count()
            total_service_centers = ServiceCenter.objects.filter(tenant_id=self.request.tenant_id).count()
            franchises = Franchise.objects.filter(tenant_id=self.request.tenant_id, is_active=True).order_by('name')
        else:
            total_franchises = Franchise.objects.count()
            total_service_centers = ServiceCenter.objects.count()
            franchises = Franchise.objects.filter(is_active=True).order_by('name')
        
        context.update({
            'page_title': _('قائمة الشركات'),
            'page_subtitle': _('إدارة شركات الامتياز'),
            'search_query': self.request.GET.get('search', ''),
            'current_status': self.request.GET.get('status', ''),
            'current_franchise': self.request.GET.get('franchise', ''),
            'franchises': franchises,
            
            # Statistics
            'total_companies': total_companies,
            'active_companies': active_companies,
            'inactive_companies': total_companies - active_companies,
            'total_franchises': total_franchises,
            'total_service_centers': total_service_centers,
        })
        return context


class CompanyDetailView(TenantDetailView):
    """Detail view for individual company"""
    model = Company
    template_name = 'setup/company_detail.html'
    context_object_name = 'company'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company = self.get_object()
        
        # Get company service centers
        tenant_id = getattr(self.request, 'tenant_id', None)
        service_centers = ServiceCenter.objects.filter(
            tenant_id=tenant_id,
            company=company
        ).order_by('-created_at')
        
        # Get users count
        users_count = 0
        try:
            users_count = UserProfile.objects.filter(
                tenant_id=tenant_id,
                company=company
            ).count()
        except:
            pass
        
        # Get customers count
        customers_count = Customer.objects.filter(
            tenant_id=tenant_id
        ).count()
        
        # Get recent work orders (if available)
        recent_work_orders = []
        try:
            from work_orders.models import WorkOrder
            recent_work_orders = WorkOrder.objects.filter(
                tenant_id=tenant_id,
                service_center__company=company
            ).select_related('service_center').order_by('-created_at')[:10]
        except ImportError:
            pass
        
        context.update({
            'page_title': company.name,
            'page_subtitle': _('تفاصيل الشركة'),
            'service_centers': service_centers,
            'service_centers_count': service_centers.count(),
            'users_count': users_count,
            'customers_count': customers_count,
            'recent_work_orders': recent_work_orders,
            'work_orders_count': len(recent_work_orders),
        })
        return context


class CompanyCreateView(TenantCreateView):
    """Create view for new companies"""
    model = Company
    form_class = CompanyForm
    template_name = 'setup/company_form.html'
    success_url = reverse_lazy('setup:company_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة شركة جديدة'),
            'page_subtitle': _('إدخال بيانات شركة جديدة'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة الشركة بنجاح'))
        return super().form_valid(form)


class CompanyUpdateView(TenantUpdateView):
    """Update view for companies"""
    model = Company
    form_class = CompanyForm
    template_name = 'setup/company_form.html'
    success_url = reverse_lazy('setup:company_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل الشركة'),
            'page_subtitle': f'{_("تحديث بيانات")} {self.get_object().name}',
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات الشركة بنجاح'))
        return super().form_valid(form)


# ==================== FRANCHISE MANAGEMENT ====================

class FranchiseListView(TenantListView):
    """List view for franchises"""
    model = Franchise
    template_name = 'setup/franchise_list.html'
    context_object_name = 'franchises'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(code__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get tenant_id from request (set by middleware)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get additional statistics for the dashboard
        if tenant_id:
            total_companies = Company.objects.filter(tenant_id=tenant_id).count()
            total_service_centers = ServiceCenter.objects.filter(tenant_id=tenant_id).count()
        else:
            total_companies = Company.objects.count()
            total_service_centers = ServiceCenter.objects.count()
        
        context.update({
            'page_title': _('قائمة الامتيازات'),
            'page_subtitle': _('إدارة امتيازات الشركة'),
            'search_query': self.request.GET.get('search', ''),
            'current_status': self.request.GET.get('status', ''),
            'total_companies': total_companies,
            'total_service_centers': total_service_centers,
        })
        return context


class FranchiseDetailView(TenantDetailView):
    """Detail view for individual franchise"""
    model = Franchise
    template_name = 'setup/franchise_detail.html'
    context_object_name = 'franchise'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        franchise = self.get_object()
        
        # Get franchise companies
        tenant_id = getattr(self.request, 'tenant_id', None)
        companies = Company.objects.filter(
            tenant_id=tenant_id,
            franchise=franchise
        ).order_by('-created_at')
        
        # Get service centers count
        service_centers_count = ServiceCenter.objects.filter(
            tenant_id=tenant_id,
            company__franchise=franchise
        ).count()
        
        # Get users count (if UserProfile model exists)
        users_count = 0
        try:
            users_count = UserProfile.objects.filter(
                tenant_id=tenant_id,
                franchise=franchise
            ).count()
        except:
            pass
        
        context.update({
            'page_title': franchise.name,
            'page_subtitle': _('تفاصيل الامتياز'),
            'companies': companies,
            'companies_count': companies.count(),
            'service_centers_count': service_centers_count,
            'users_count': users_count,
        })
        return context


class FranchiseCreateView(TenantCreateView):
    """Create view for new franchises"""
    model = Franchise
    form_class = FranchiseForm
    template_name = 'setup/franchise_form.html'
    success_url = reverse_lazy('setup:franchise_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة امتياز جديد'),
            'page_subtitle': _('إدخال بيانات امتياز جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة الامتياز بنجاح'))
        return super().form_valid(form)


class FranchiseUpdateView(TenantUpdateView):
    """Update view for franchises"""
    model = Franchise
    form_class = FranchiseForm
    template_name = 'setup/franchise_form.html'
    success_url = reverse_lazy('setup:franchise_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل الامتياز'),
            'page_subtitle': f'{_("تحديث بيانات")} {self.get_object().name}',
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات الامتياز بنجاح'))
        return super().form_valid(form)


# ==================== USER ROLE MANAGEMENT ====================

class UserRoleListView(TenantListView):
    """List view for user roles"""
    model = UserRole
    template_name = 'setup/user_role_list.html'
    context_object_name = 'user_roles'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )
        
        # Level filter
        level = self.request.GET.get('level', '')
        if level:
            queryset = queryset.filter(level=level)
        
        return queryset.order_by('priority', 'name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('أدوار المستخدمين'),
            'page_subtitle': _('إدارة أدوار ووصلاحيات المستخدمين'),
            'search_query': self.request.GET.get('search', ''),
            'current_level': self.request.GET.get('level', ''),
            'role_levels': UserRole.ROLE_LEVELS,
        })
        return context


class UserRoleDetailView(TenantDetailView):
    """Detail view for individual user role"""
    model = UserRole
    template_name = 'setup/user_role_detail.html'
    context_object_name = 'user_role'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تفاصيل الدور'),
            'page_subtitle': self.get_object().name,
        })
        return context


class UserRoleCreateView(TenantCreateView):
    """Create view for new user roles"""
    model = UserRole
    form_class = UserRoleForm
    template_name = 'setup/user_role_form.html'
    success_url = reverse_lazy('setup:user_role_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة دور جديد'),
            'page_subtitle': _('إنشاء دور مستخدم جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة الدور بنجاح'))
        return super().form_valid(form)


class UserRoleUpdateView(TenantUpdateView):
    """Update view for user roles"""
    model = UserRole
    form_class = UserRoleForm
    template_name = 'setup/user_role_form.html'
    success_url = reverse_lazy('setup:user_role_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل الدور'),
            'page_subtitle': self.get_object().name,
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث الدور بنجاح'))
        return super().form_valid(form)


# ==================== USER PROFILE MANAGEMENT ====================

class UserProfileListView(TenantListView):
    """List view for user profiles"""
    model = UserProfile
    template_name = 'setup/user_profile_list.html'
    context_object_name = 'user_profiles'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('user', 'role', 'franchise', 'company', 'service_center')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(user__username__icontains=search_query) |
                Q(user__first_name__icontains=search_query) |
                Q(user__last_name__icontains=search_query) |
                Q(user__email__icontains=search_query) |
                Q(phone__icontains=search_query)
            )
        
        # Role filter
        role_id = self.request.GET.get('role_id', '')
        if role_id:
            queryset = queryset.filter(role_id=role_id)
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(user__is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(user__is_active=False)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get roles for filter (UserRole doesn't have tenant_id field)
        roles = UserRole.objects.all()
        
        # Calculate counts for each user type
        base_queryset = super().get_queryset().select_related('user', 'role', 'franchise', 'company', 'service_center')
        
        # Count users by their assignments/roles
        franchise_count = base_queryset.filter(franchise__isnull=False).count()
        company_count = base_queryset.filter(company__isnull=False).count()
        service_center_count = base_queryset.filter(service_center__isnull=False).count()
        
        # Count technicians from TechnicianProfile
        from .models import TechnicianProfile
        # Simple count for now - will be filtered by tenant in future enhancement
        technician_count = TechnicianProfile.objects.count()
        
        # Total count
        total_count = base_queryset.count()
        
        # Apply role-based filtering for actual counts
        from user_roles.services import DataFilterService
        user_data_scope = DataFilterService.get_user_data_scope(self.request.user)
        scope_level = user_data_scope.get('scope', 'none')
        scope_filters = user_data_scope.get('filters', {})
        
        # Get filtered user profiles based on role-based access
        if scope_level == 'franchise':
            franchise_id = scope_filters.get('franchise_id')
            if franchise_id:
                filtered_queryset = base_queryset.filter(franchise_id=franchise_id)
            else:
                filtered_queryset = base_queryset.none()
        elif scope_level == 'company':
            company_id = scope_filters.get('company_id')
            if company_id:
                filtered_queryset = base_queryset.filter(company_id=company_id)
            else:
                filtered_queryset = base_queryset.none()
        elif scope_level == 'service_center':
            service_center_id = scope_filters.get('service_center_id')
            if service_center_id:
                filtered_queryset = base_queryset.filter(service_center_id=service_center_id)
            else:
                filtered_queryset = base_queryset.none()
        else:
            # System level or superuser - see all
            filtered_queryset = base_queryset
        
        # Calculate real counts based on filtered data
        real_total_count = filtered_queryset.count()
        real_franchise_count = filtered_queryset.filter(franchise__isnull=False).count()
        real_company_count = filtered_queryset.filter(company__isnull=False).count()
        real_service_center_count = filtered_queryset.filter(service_center__isnull=False).count()
        
        # Count technicians with same filtering logic
        from .models import TechnicianProfile
        if scope_level == 'franchise':
            franchise_id = scope_filters.get('franchise_id')
            if franchise_id:
                real_technician_count = TechnicianProfile.objects.filter(
                    user_profile__service_center__company__franchise_id=franchise_id
                ).count()
            else:
                real_technician_count = 0
        elif scope_level == 'company':
            company_id = scope_filters.get('company_id')
            if company_id:
                real_technician_count = TechnicianProfile.objects.filter(
                    user_profile__service_center__company_id=company_id
                ).count()
            else:
                real_technician_count = 0
        elif scope_level == 'service_center':
            service_center_id = scope_filters.get('service_center_id')
            if service_center_id:
                real_technician_count = TechnicianProfile.objects.filter(
                    user_profile__service_center_id=service_center_id
                ).count()
            else:
                real_technician_count = 0
        else:
            real_technician_count = TechnicianProfile.objects.count()

        context.update({
            'page_title': _('ملفات المستخدمين'),
            'page_subtitle': _('إدارة ملفات المستخدمين الشخصية'),
            'search_query': self.request.GET.get('search', ''),
            'current_role_id': self.request.GET.get('role_id', ''),
            'current_status': self.request.GET.get('status', ''),
            'roles': roles,
            # Real counts based on role-based filtering
            'total_count': real_total_count,
            'franchise_count': real_franchise_count,
            'company_count': real_company_count,
            'service_center_count': real_service_center_count,
            'technician_count': real_technician_count,
        })
        return context


class UserProfileDetailView(TenantDetailView):
    """Detail view for individual user profile"""
    model = UserProfile
    template_name = 'setup/user_profile_detail.html'
    context_object_name = 'user_profile'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        profile = self.get_object()
        context.update({
            'page_title': _('ملف المستخدم'),
            'page_subtitle': f"{profile.user.get_full_name() or profile.user.username}",
        })
        return context


class UserProfileCreateView(TenantCreateView):
    """Create view for new user profiles"""
    model = UserProfile
    form_class = CombinedUserCreationForm
    template_name = 'setup/user_profile_form.html'
    success_url = reverse_lazy('setup:user_profile_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة مستخدم جديد'),
            'page_subtitle': _('إنشاء حساب مستخدم وملف شخصي'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة المستخدم بنجاح'))
        return super().form_valid(form)


class UserProfileUpdateView(TenantUpdateView):
    """Update view for user profiles"""
    model = UserProfile
    form_class = UserProfileForm
    template_name = 'setup/user_profile_form.html'
    success_url = reverse_lazy('setup:user_profile_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        profile = self.get_object()
        context.update({
            'page_title': _('تعديل ملف المستخدم'),
            'page_subtitle': f"{profile.user.get_full_name() or profile.user.username}",
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث ملف المستخدم بنجاح'))
        return super().form_valid(form)


# ==================== TECHNICIAN SPECIALIZATION MANAGEMENT ====================

class TechnicianSpecializationListView(TenantListView):
    """List view for technician specializations"""
    model = TechnicianSpecialization
    template_name = 'setup/technician_specialization_list.html'
    context_object_name = 'specializations'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(category__icontains=search_query)
            )
        
        # Category filter
        category = self.request.GET.get('category', '')
        if category:
            queryset = queryset.filter(category=category)
        
        return queryset.order_by('category', 'name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get unique categories from existing specializations
        categories = TechnicianSpecialization.objects.values_list('category', flat=True).distinct().order_by('category')
        categories = [(cat, cat) for cat in categories if cat]  # Convert to choices format
        
        context.update({
            'page_title': _('تخصصات الفنيين'),
            'page_subtitle': _('إدارة التخصصات التقنية للفنيين'),
            'search_query': self.request.GET.get('search', ''),
            'current_category': self.request.GET.get('category', ''),
            'categories': categories,
        })
        return context


class TechnicianSpecializationCreateView(TenantCreateView):
    """Create view for new technician specializations"""
    model = TechnicianSpecialization
    form_class = TechnicianSpecializationForm
    template_name = 'setup/technician_specialization_form.html'
    success_url = reverse_lazy('setup:technician_specialization_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة تخصص جديد'),
            'page_subtitle': _('إنشاء تخصص تقني جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة التخصص بنجاح'))
        return super().form_valid(form)


class TechnicianSpecializationUpdateView(TenantUpdateView):
    """Update view for technician specializations"""
    model = TechnicianSpecialization
    form_class = TechnicianSpecializationForm
    template_name = 'setup/technician_specialization_form.html'
    success_url = reverse_lazy('setup:technician_specialization_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل التخصص'),
            'page_subtitle': self.get_object().name,
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث التخصص بنجاح'))
        return super().form_valid(form)


# ==================== TECHNICIAN PROFILE MANAGEMENT ====================

class TechnicianProfileListView(TenantListView):
    """List view for technician profiles"""
    model = TechnicianProfile
    template_name = 'setup/technician_profile_list.html'
    context_object_name = 'technician_profiles'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('user_profile__user', 'user_profile__service_center')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(user_profile__user__username__icontains=search_query) |
                Q(user_profile__user__first_name__icontains=search_query) |
                Q(user_profile__user__last_name__icontains=search_query) |
                Q(license_number__icontains=search_query)
            )
        
        # Experience years filter
        experience_years = self.request.GET.get('experience_years', '')
        if experience_years:
            queryset = queryset.filter(experience_years__gte=experience_years)
        
        # Emergency availability filter
        emergency_available = self.request.GET.get('emergency_available', '')
        if emergency_available == 'true':
            queryset = queryset.filter(available_for_emergency=True)
        elif emergency_available == 'false':
            queryset = queryset.filter(available_for_emergency=False)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Define experience year ranges for filtering
        experience_year_ranges = [
            ('1', _('1+ Years')),
            ('3', _('3+ Years')),
            ('5', _('5+ Years')),
            ('10', _('10+ Years')),
        ]
        context.update({
            'page_title': _('ملفات الفنيين'),
            'page_subtitle': _('إدارة ملفات الفنيين التخصصية'),
            'search_query': self.request.GET.get('search', ''),
            'current_experience_years': self.request.GET.get('experience_years', ''),
            'current_emergency_available': self.request.GET.get('emergency_available', ''),
            'experience_year_ranges': experience_year_ranges,
        })
        return context


class TechnicianProfileDetailView(TenantDetailView):
    """Detail view for individual technician profile"""
    model = TechnicianProfile
    template_name = 'setup/technician_profile_detail.html'
    context_object_name = 'technician_profile'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        profile = self.get_object()
        context.update({
            'page_title': _('ملف الفني'),
            'page_subtitle': f"{profile.user_profile.user.get_full_name() or profile.user_profile.user.username}",
        })
        return context


class TechnicianProfileCreateView(TenantCreateView):
    """Create view for new technician profiles"""
    model = TechnicianProfile
    form_class = TechnicianProfileForm
    template_name = 'setup/technician_profile_form.html'
    success_url = reverse_lazy('setup:technician_profile_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة ملف فني'),
            'page_subtitle': _('إنشاء ملف فني تخصصي'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة ملف الفني بنجاح'))
        return super().form_valid(form)


class TechnicianProfileUpdateView(TenantUpdateView):
    """Update view for technician profiles"""
    model = TechnicianProfile
    form_class = TechnicianProfileForm
    template_name = 'setup/technician_profile_form.html'
    success_url = reverse_lazy('setup:technician_profile_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        profile = self.get_object()
        context.update({
            'page_title': _('تعديل ملف الفني'),
            'page_subtitle': f"{profile.user_profile.user.get_full_name() or profile.user_profile.user.username}",
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث ملف الفني بنجاح'))
        return super().form_valid(form)


# ==================== USER WAREHOUSE ASSIGNMENT MANAGEMENT ====================

class UserWarehouseAssignmentListView(TenantListView):
    """List view for user warehouse assignments"""
    model = UserWarehouseAssignment
    template_name = 'setup/user_warehouse_assignment_list.html'
    context_object_name = 'assignments'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('user_profile__user', 'warehouse')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(user_profile__user__username__icontains=search_query) |
                Q(user_profile__user__first_name__icontains=search_query) |
                Q(user_profile__user__last_name__icontains=search_query) |
                Q(warehouse__name__icontains=search_query)
            )
        
        # Assignment type filter
        assignment_type = self.request.GET.get('assignment_type', '')
        if assignment_type:
            queryset = queryset.filter(assignment_type=assignment_type)
        
        # Status filter  
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعيينات المستودعات'),
            'page_subtitle': _('إدارة تعيين المستخدمين للمستودعات'),
            'search_query': self.request.GET.get('search', ''),
            'current_assignment_type': self.request.GET.get('assignment_type', ''),
            'current_status': self.request.GET.get('status', ''),
            'assignment_types': UserWarehouseAssignment.ASSIGNMENT_TYPES,
        })
        return context


class UserWarehouseAssignmentCreateView(TenantCreateView):
    """Create view for new user warehouse assignments"""
    model = UserWarehouseAssignment
    form_class = UserWarehouseAssignmentForm
    template_name = 'setup/user_warehouse_assignment_form.html'
    success_url = reverse_lazy('setup:user_warehouse_assignment_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعيين مستودع جديد'),
            'page_subtitle': _('تعيين مستخدم لمستودع'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تعيين المستخدم للمستودع بنجاح'))
        return super().form_valid(form)


class UserWarehouseAssignmentUpdateView(TenantUpdateView):
    """Update view for user warehouse assignments"""
    model = UserWarehouseAssignment
    form_class = UserWarehouseAssignmentForm
    template_name = 'setup/user_warehouse_assignment_form.html'
    success_url = reverse_lazy('setup:user_warehouse_assignment_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        assignment = self.get_object()
        context.update({
            'page_title': _('تعديل تعيين المستودع'),
            'page_subtitle': f"{assignment.user_profile.user.get_full_name()} - {assignment.warehouse.name}",
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث تعيين المستودع بنجاح'))
        return super().form_valid(form)


# ==================== API VIEWS FOR CASCADING DROPDOWNS ====================

@login_required
def api_get_companies_by_franchise(request):
    """Get companies by franchise ID for cascading dropdowns"""
    franchise_id = request.GET.get('franchise_id')
    tenant_id = getattr(request, 'tenant_id', None)
    
    if not franchise_id:
        return JsonResponse({'companies': []})
    
    # Build queryset with tenant filtering
    if tenant_id:
        companies = Company.objects.filter(franchise_id=franchise_id, tenant_id=tenant_id)
    else:
        companies = Company.objects.filter(franchise_id=franchise_id)
    
    companies_data = [
        {'id': company.id, 'name': company.name}
        for company in companies.order_by('name')
    ]
    
    return JsonResponse({'companies': companies_data})


@login_required
def api_get_service_centers_by_company(request):
    """Get service centers by company ID for cascading dropdowns"""
    company_id = request.GET.get('company_id')
    tenant_id = getattr(request, 'tenant_id', None)
    
    if not company_id:
        return JsonResponse({'service_centers': []})
    
    # Build queryset with tenant filtering
    if tenant_id:
        service_centers = ServiceCenter.objects.filter(company_id=company_id, tenant_id=tenant_id)
    else:
        service_centers = ServiceCenter.objects.filter(company_id=company_id)
    
    service_centers_data = [
        {'id': sc.id, 'name': sc.name}
        for sc in service_centers.order_by('name')
    ]
    
    return JsonResponse({'service_centers': service_centers_data})


@login_required
def api_get_user_profiles_by_role(request):
    """Get user profiles by role for assignments"""
    role_id = request.GET.get('role_id')
    tenant_id = getattr(request, 'tenant_id', None)
    
    if not role_id:
        return JsonResponse({'user_profiles': []})
    
    # Build queryset with tenant filtering
    if tenant_id:
        user_profiles = UserProfile.objects.filter(role_id=role_id, tenant_id=tenant_id)
    else:
        user_profiles = UserProfile.objects.filter(role_id=role_id)
    
    user_profiles = user_profiles.select_related('user')
    
    profiles_data = [
        {
            'id': profile.id, 
            'name': f"{profile.user.get_full_name() or profile.user.username}",
            'username': profile.user.username
        }
        for profile in user_profiles.order_by('user__first_name', 'user__last_name', 'user__username')
    ]
    
    return JsonResponse({'user_profiles': profiles_data})


@login_required
def api_get_franchises(request):
    """Get all franchises for dropdown selection"""
    tenant_id = getattr(request, 'tenant_id', None)
    
    # Build queryset with tenant filtering
    if tenant_id:
        franchises = Franchise.objects.filter(tenant_id=tenant_id)
    else:
        franchises = Franchise.objects.all()
    
    franchises_data = [
        {'id': franchise.id, 'name': franchise.name}
        for franchise in franchises.filter(is_active=True).order_by('name')
    ]
    
    return JsonResponse({'franchises': franchises_data})


# ==================== VEHICLE MAKE MANAGEMENT ====================

class VehicleMakeListView(TenantListView):
    """List view for vehicle makes"""
    model = VehicleMake
    template_name = 'setup/vehicle_make_list.html'
    context_object_name = 'vehicle_makes'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(country_of_origin__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('ماركات المركبات'),
            'page_subtitle': _('إدارة ماركات وعلامات المركبات'),
            'search_query': self.request.GET.get('search', ''),
            'current_status': self.request.GET.get('status', ''),
        })
        return context


class VehicleMakeCreateView(TenantCreateView):
    """Create view for new vehicle makes"""
    model = VehicleMake
    template_name = 'setup/vehicle_make_form.html'
    fields = ['name', 'description', 'logo', 'country_of_origin', 'is_active', 'attributes']
    success_url = reverse_lazy('setup:vehicle_make_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة ماركة مركبة'),
            'page_subtitle': _('إنشاء ماركة مركبة جديدة'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة ماركة المركبة بنجاح'))
        return super().form_valid(form)


class VehicleMakeUpdateView(TenantUpdateView):
    """Update view for vehicle makes"""
    model = VehicleMake
    template_name = 'setup/vehicle_make_form.html'
    fields = ['name', 'description', 'logo', 'country_of_origin', 'is_active', 'attributes']
    success_url = reverse_lazy('setup:vehicle_make_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل ماركة المركبة'),
            'page_subtitle': self.get_object().name,
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث ماركة المركبة بنجاح'))
        return super().form_valid(form)


# ==================== VEHICLE MODEL MANAGEMENT ====================

class VehicleModelListView(TenantListView):
    """List view for vehicle models"""
    model = VehicleModel
    template_name = 'setup/vehicle_model_list.html'
    context_object_name = 'vehicle_models'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('make')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(make__name__icontains=search_query) |
                Q(vehicle_class__icontains=search_query)
            )
        
        # Make filter
        make_id = self.request.GET.get('make_id', '')
        if make_id:
            queryset = queryset.filter(make_id=make_id)
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('make__name', 'name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get makes for filter
        if tenant_id:
            makes = VehicleMake.objects.filter(tenant_id=tenant_id, is_active=True)
        else:
            makes = VehicleMake.objects.filter(is_active=True)
        
        context.update({
            'page_title': _('موديلات المركبات'),
            'page_subtitle': _('إدارة موديلات المركبات'),
            'search_query': self.request.GET.get('search', ''),
            'current_make_id': self.request.GET.get('make_id', ''),
            'current_status': self.request.GET.get('status', ''),
            'makes': makes,
        })
        return context


class VehicleModelCreateView(TenantCreateView):
    """Create view for new vehicle models"""
    model = VehicleModel
    template_name = 'setup/vehicle_model_form.html'
    fields = ['make', 'name', 'description', 'year_introduced', 'year_discontinued', 'vehicle_class', 'is_active', 'attributes']
    success_url = reverse_lazy('setup:vehicle_model_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة موديل مركبة'),
            'page_subtitle': _('إنشاء موديل مركبة جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة موديل المركبة بنجاح'))
        return super().form_valid(form)


class VehicleModelUpdateView(TenantUpdateView):
    """Update view for vehicle models"""
    model = VehicleModel
    template_name = 'setup/vehicle_model_form.html'
    fields = ['make', 'name', 'description', 'year_introduced', 'year_discontinued', 'vehicle_class', 'is_active', 'attributes']
    success_url = reverse_lazy('setup:vehicle_model_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل موديل المركبة'),
            'page_subtitle': f"{self.get_object().make.name} {self.get_object().name}",
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث موديل المركبة بنجاح'))
        return super().form_valid(form)


# ==================== SERVICE CENTER TYPE MANAGEMENT ====================

class ServiceCenterTypeListView(ListView):
    """List view for service center types"""
    model = ServiceCenterType
    template_name = 'setup/service_center_type_list.html'
    context_object_name = 'service_center_types'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = ServiceCenterType.objects.all()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('أنواع مراكز الخدمة'),
            'page_subtitle': _('إدارة أنواع وتصنيفات مراكز الخدمة'),
            'search_query': self.request.GET.get('search', ''),
            'current_status': self.request.GET.get('status', ''),
        })
        return context


class ServiceCenterTypeCreateView(CreateView):
    """Create view for new service center types"""
    model = ServiceCenterType
    template_name = 'setup/service_center_type_form.html'
    fields = ['name', 'description', 'max_capacity', 'color_code', 'is_active', 'attributes']
    success_url = reverse_lazy('setup:service_center_type_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة نوع مركز خدمة'),
            'page_subtitle': _('إنشاء نوع مركز خدمة جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة نوع مركز الخدمة بنجاح'))
        return super().form_valid(form)


class ServiceCenterTypeUpdateView(UpdateView):
    """Update view for service center types"""
    model = ServiceCenterType
    template_name = 'setup/service_center_type_form.html'
    fields = ['name', 'description', 'max_capacity', 'color_code', 'is_active', 'attributes']
    success_url = reverse_lazy('setup:service_center_type_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل نوع مركز الخدمة'),
            'page_subtitle': self.get_object().name,
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث نوع مركز الخدمة بنجاح'))
        return super().form_valid(form)


# ==================== SERVICE CENTER MAKE MODEL MANAGEMENT ====================

class ServiceCenterMakeModelListView(TenantListView):
    """List view for service center make models"""
    model = ServiceCenterMakeModel
    template_name = 'setup/service_center_make_model_list.html'
    context_object_name = 'service_center_make_models'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('service_center')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(make__icontains=search_query) |
                Q(model__icontains=search_query) |
                Q(service_center__name__icontains=search_query)
            )
        
        # Service center filter
        service_center_id = self.request.GET.get('service_center_id', '')
        if service_center_id:
            queryset = queryset.filter(service_center_id=service_center_id)
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('service_center__name', 'make', 'model')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get service centers for filter
        if tenant_id:
            service_centers = ServiceCenter.objects.filter(tenant_id=tenant_id, is_active=True)
        else:
            service_centers = ServiceCenter.objects.filter(is_active=True)
        
        context.update({
            'page_title': _('ماركات وموديلات مراكز الخدمة'),
            'page_subtitle': _('إدارة الماركات والموديلات المدعومة في مراكز الخدمة'),
            'search_query': self.request.GET.get('search', ''),
            'current_service_center_id': self.request.GET.get('service_center_id', ''),
            'current_status': self.request.GET.get('status', ''),
            'service_centers': service_centers,
        })
        return context


class ServiceCenterMakeModelCreateView(TenantCreateView):
    """Create view for new service center make models"""
    model = ServiceCenterMakeModel
    template_name = 'setup/service_center_make_model_form.html'
    fields = ['service_center', 'make', 'model', 'is_active', 'notes']
    success_url = reverse_lazy('setup:service_center_make_model_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة دعم ماركة وموديل'),
            'page_subtitle': _('إضافة دعم ماركة وموديل جديد لمركز خدمة'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة دعم الماركة والموديل بنجاح'))
        return super().form_valid(form)


class ServiceCenterMakeModelUpdateView(TenantUpdateView):
    """Update view for service center make models"""
    model = ServiceCenterMakeModel
    template_name = 'setup/service_center_make_model_form.html'
    fields = ['service_center', 'make', 'model', 'is_active', 'notes']
    success_url = reverse_lazy('setup:service_center_make_model_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        obj = self.get_object()
        context.update({
            'page_title': _('تعديل دعم الماركة والموديل'),
            'page_subtitle': f"{obj.service_center.name} - {obj.make} {obj.model}",
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث دعم الماركة والموديل بنجاح'))
        return super().form_valid(form)


# ==================== SERVICE LEVEL MANAGEMENT ====================

class ServiceLevelListView(ListView):
    """List view for service levels"""
    model = ServiceLevel
    template_name = 'setup/service_level_list.html'
    context_object_name = 'service_levels'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = ServiceLevel.objects.all()
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('priority', 'name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('مستويات الخدمة'),
            'page_subtitle': _('إدارة مستويات واتفاقيات الخدمة'),
            'search_query': self.request.GET.get('search', ''),
            'current_status': self.request.GET.get('status', ''),
        })
        return context


class ServiceLevelCreateView(CreateView):
    """Create view for new service levels"""
    model = ServiceLevel
    template_name = 'setup/service_level_form.html'
    fields = ['name', 'description', 'priority', 'response_time_hours', 'resolution_time_hours', 
              'support_hours', 'is_active']
    success_url = reverse_lazy('setup:service_level_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('إضافة مستوى خدمة'),
            'page_subtitle': _('إنشاء مستوى خدمة جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم إضافة مستوى الخدمة بنجاح'))
        return super().form_valid(form)


class ServiceLevelUpdateView(UpdateView):
    """Update view for service levels"""
    model = ServiceLevel
    template_name = 'setup/service_level_form.html'
    fields = ['name', 'description', 'priority', 'response_time_hours', 'resolution_time_hours', 
              'support_hours', 'is_active']
    success_url = reverse_lazy('setup:service_level_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('تعديل مستوى الخدمة'),
            'page_subtitle': self.get_object().name,
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث مستوى الخدمة بنجاح'))
        return super().form_valid(form)


# ==================== SERVICE HISTORY MANAGEMENT ====================

class ServiceHistoryListView(TenantListView):
    """List view for service history records"""
    model = ServiceHistory
    template_name = 'setup/service_history_list.html'
    context_object_name = 'service_records'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('vehicle', 'service_center')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(vehicle__license_plate__icontains=search_query) |
                Q(vehicle__make__icontains=search_query) |
                Q(vehicle__model__icontains=search_query) |
                Q(work_order_number__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(technician__icontains=search_query)
            )
        
        # Service center filter
        service_center_id = self.request.GET.get('service_center_id', '')
        if service_center_id:
            queryset = queryset.filter(service_center_id=service_center_id)
        
        # Date range filter
        date_from = self.request.GET.get('date_from', '')
        date_to = self.request.GET.get('date_to', '')
        if date_from:
            queryset = queryset.filter(service_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(service_date__lte=date_to)
        
        return queryset.order_by('-service_date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get service centers for filter
        if tenant_id:
            service_centers = ServiceCenter.objects.filter(tenant_id=tenant_id, is_active=True)
        else:
            service_centers = ServiceCenter.objects.filter(is_active=True)
        
        context.update({
            'page_title': _('سجل الخدمات'),
            'page_subtitle': _('سجل خدمات المركبات التاريخي'),
            'search_query': self.request.GET.get('search', ''),
            'current_service_center_id': self.request.GET.get('service_center_id', ''),
            'date_from': self.request.GET.get('date_from', ''),
            'date_to': self.request.GET.get('date_to', ''),
            'service_centers': service_centers,
        })
        return context


# ==================== VEHICLE OWNERSHIP TRANSFER MANAGEMENT ====================

class VehicleOwnershipTransferListView(TenantListView):
    """List view for vehicle ownership transfers"""
    model = VehicleOwnershipTransfer
    template_name = 'setup/vehicle_transfer_list.html'
    context_object_name = 'transfers'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('vehicle', 'previous_owner', 'new_owner')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(vehicle__license_plate__icontains=search_query) |
                Q(vehicle__make__icontains=search_query) |
                Q(vehicle__model__icontains=search_query) |
                Q(previous_owner__first_name__icontains=search_query) |
                Q(previous_owner__last_name__icontains=search_query) |
                Q(new_owner__first_name__icontains=search_query) |
                Q(new_owner__last_name__icontains=search_query)
            )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status:
            queryset = queryset.filter(status=status)
        
        return queryset.order_by('-transfer_date', '-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('نقل ملكية المركبات'),
            'page_subtitle': _('إدارة عمليات نقل ملكية المركبات'),
            'search_query': self.request.GET.get('search', ''),
            'current_status': self.request.GET.get('status', ''),
            'status_choices': VehicleOwnershipTransfer.TRANSFER_STATUS_CHOICES,
        })
        return context


class VehicleOwnershipTransferCreateView(TenantCreateView):
    """Create view for new vehicle ownership transfers"""
    model = VehicleOwnershipTransfer
    template_name = 'setup/vehicle_transfer_form.html'
    fields = ['vehicle', 'previous_owner', 'new_owner', 'transfer_date', 'sale_price', 
              'odometer_reading', 'documents', 'notes']
    success_url = reverse_lazy('setup:vehicle_transfer_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('نقل ملكية مركبة'),
            'page_subtitle': _('تسجيل نقل ملكية مركبة جديد'),
            'form_action': 'create',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تسجيل نقل الملكية بنجاح'))
        return super().form_valid(form)


# ==================== USER CUSTOM PERMISSIONS MANAGEMENT ====================

from user_roles.models import UserCustomPermission, ModuleTab

class UserCustomPermissionListView(TenantListView):
    """List view for user custom permissions for a specific user"""
    model = UserCustomPermission
    template_name = 'setup/user_custom_permissions.html'
    context_object_name = 'permissions'
    paginate_by = 20
    
    def get_queryset(self):
        self.user_profile = get_object_or_404(UserProfile, pk=self.kwargs['user_id'])
        return UserCustomPermission.objects.filter(user=self.user_profile.user).select_related(
            'module_tab'
        ).prefetch_related('franchises', 'companies', 'service_centers')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'user_profile': self.user_profile,
            'page_title': _('صلاحيات المستخدم المخصصة'),
            'page_subtitle': f"{self.user_profile.user.get_full_name() or self.user_profile.user.username}",
            'available_modules': ModuleTab.objects.filter(is_active=True).order_by('order'),
        })
        return context


class UserCustomPermissionCreateView(TenantCreateView):
    """Create view for new user custom permissions"""
    model = UserCustomPermission
    template_name = 'setup/user_custom_permission_form.html'
    fields = ['module_tab', 'can_view', 'can_add', 'can_edit', 'can_delete', 'can_approve', 'can_report', 
              'franchises', 'companies', 'service_centers', 'is_active']
    
    def get_success_url(self):
        return reverse_lazy('setup:user_custom_permissions', kwargs={'user_id': self.kwargs['user_id']})
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # Add horizontal filter widget for better UX
        form.fields['franchises'].widget.attrs.update({'class': 'form-control'})
        form.fields['companies'].widget.attrs.update({'class': 'form-control'})
        form.fields['service_centers'].widget.attrs.update({'class': 'form-control'})
        return form
    
    def form_valid(self, form):
        self.user_profile = get_object_or_404(UserProfile, pk=self.kwargs['user_id'])
        form.instance.user = self.user_profile.user
        messages.success(self.request, _('تم إضافة الصلاحية المخصصة بنجاح'))
        return super().form_valid(form)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        self.user_profile = get_object_or_404(UserProfile, pk=self.kwargs['user_id'])
        context.update({
            'user_profile': self.user_profile,
            'page_title': _('إضافة صلاحية مخصصة'),
            'page_subtitle': f"{self.user_profile.user.get_full_name() or self.user_profile.user.username}",
            'form_action': 'create',
        })
        return context


class UserCustomPermissionUpdateView(TenantUpdateView):
    """Update view for user custom permissions"""
    model = UserCustomPermission
    template_name = 'setup/user_custom_permission_form.html'
    fields = ['module_tab', 'can_view', 'can_add', 'can_edit', 'can_delete', 'can_approve', 'can_report',
              'franchises', 'companies', 'service_centers', 'is_active']
    
    def get_success_url(self):
        return reverse_lazy('setup:user_custom_permissions', kwargs={'user_id': self.object.user.userprofile.pk})
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # Add horizontal filter widget for better UX
        form.fields['franchises'].widget.attrs.update({'class': 'form-control'})
        form.fields['companies'].widget.attrs.update({'class': 'form-control'})
        form.fields['service_centers'].widget.attrs.update({'class': 'form-control'})
        return form
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'user_profile': self.object.user.userprofile,
            'page_title': _('تعديل الصلاحية المخصصة'),
            'page_subtitle': f"{self.object.user.get_full_name() or self.object.user.username}",
            'form_action': 'update',
        })
        return context
    
    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث الصلاحية المخصصة بنجاح'))
        return super().form_valid(form)


class UserCustomPermissionDeleteView(DeleteView):
    """Delete view for user custom permissions"""
    model = UserCustomPermission
    
    def get_success_url(self):
        return reverse_lazy('setup:user_custom_permissions', kwargs={'user_id': self.object.user.userprofile.pk})
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('تم حذف الصلاحية المخصصة بنجاح'))
        return super().delete(request, *args, **kwargs)


@login_required
def toggle_user_custom_permission(request, permission_id):
    """Toggle active status of a user custom permission"""
    permission = get_object_or_404(UserCustomPermission, pk=permission_id)
    permission.is_active = not permission.is_active
    permission.save()
    
    status = _('تم تفعيل') if permission.is_active else _('تم إلغاء تفعيل')
    messages.success(request, f'{status} الصلاحية المخصصة')
    
    return redirect('setup:user_custom_permissions', user_id=permission.user.userprofile.pk)



