/* Supply Chain Dashboard Enhanced Styles */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --dark-color: #1f2937;
    --light-bg: #f8fafc;
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Enhancements */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Hero Section Enhanced */
.hero-section {
    background: var(--primary-gradient);
    border-radius: 24px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='10' cy='10' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Dashboard Cards Enhanced */
.dashboard-card {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.dashboard-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.dashboard-card:hover::before {
    left: 100%;
}

/* Feature Icons Enhanced */
.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: var(--transition);
    transform: translate(-50%, -50%);
}

.dashboard-card:hover .feature-icon::after {
    width: 120%;
    height: 120%;
}

.feature-icon.bg-primary { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
.feature-icon.bg-success { background: linear-gradient(135deg, #10b981, #047857); }
.feature-icon.bg-warning { background: linear-gradient(135deg, #f59e0b, #d97706); }

/* Animated Counters */
.stat-counter {
    font-size: 1.5rem;
    font-weight: 700;
    color: inherit;
    display: inline-block;
    transition: var(--transition);
}

.dashboard-card:hover .stat-counter {
    transform: scale(1.1);
}

/* Quick Access Buttons Enhanced */
.quick-access-btn {
    transition: var(--transition);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(145deg, transparent, rgba(0,0,0,0.1)) border-box;
}

.quick-access-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    transition: left 0.3s;
}

.quick-access-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-medium);
}

.quick-access-btn:hover::before {
    left: 100%;
}

/* Workflow Steps Enhanced */
.workflow-step {
    position: relative;
    text-align: center;
    padding: 1.5rem;
    transition: var(--transition);
}

.workflow-step::after {
    content: '→';
    position: absolute;
    right: -1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: #94a3b8;
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.workflow-step:last-child::after {
    display: none;
}

.workflow-step:hover {
    transform: scale(1.05);
}

.workflow-step:hover::after {
    color: var(--primary-color);
    transform: translateY(-50%) scale(1.2);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Progress Bars for Status */
.progress-bar {
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 2s ease-in-out;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-fill.bg-success { background: linear-gradient(90deg, #10b981, #059669); }
.progress-fill.bg-info { background: linear-gradient(90deg, #06b6d4, #0891b2); }
.progress-fill.bg-warning { background: linear-gradient(90deg, #f59e0b, #d97706); }

/* Activity Feed Enhanced */
.activity-item {
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    transition: var(--transition);
    position: relative;
    padding-left: 2rem;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    transform: translateY(-50%);
    animation: pulse-dot 2s infinite;
}

.activity-item:hover {
    background: #f8fafc;
    padding-left: 2.5rem;
}

@keyframes pulse-dot {
    0%, 100% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.2); }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Tooltips Enhanced */
.tooltip-enhanced {
    position: relative;
    cursor: help;
}

.tooltip-enhanced::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip-enhanced::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #1f2937;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.tooltip-enhanced:hover::before,
.tooltip-enhanced:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Card Headers Enhanced */
.card-header-enhanced {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    position: relative;
    overflow: hidden;
}

.card-header-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E");
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .dashboard-card {
        margin-bottom: 1rem;
    }
    
    .workflow-step::after {
        display: none;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .hero-section {
        border-radius: 16px;
        padding: 2rem 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-bg: #1f2937;
    }
    
    .dashboard-card {
        background: linear-gradient(145deg, #374151, #1f2937);
        color: #f9fafb;
    }
    
    body {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }
}

/* Print Styles */
@media print {
    .dashboard-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
    
    .hero-section {
        background: #f3f4f6 !important;
        color: #1f2937 !important;
    }
} 