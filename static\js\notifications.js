/**
 * Enhanced Global Notification System with Dropdown and Actions
 */

class GlobalNotificationSystem {
    constructor() {
        this.zIndex = 99999; // Highest z-index for notifications
        this.modalZIndex = 9998; // Z-index for modals (below notifications)
        this.overlayZIndex = 9997; // Z-index for overlays (below modals)
        this.notifications = [];
        this.actionItems = [];
        this.pollInterval = null;
        this.lastUpdateTime = null;
        
        this.init();
    }

    init() {
        try {
            // Check if notification elements exist on this page
            const notificationButton = document.getElementById('notifications-dropdown-button');
            const notificationDropdown = document.getElementById('notifications-dropdown');
            
            // If core notification elements don't exist, don't initialize the system
            if (!notificationButton && !notificationDropdown) {
                console.log('Notification elements not found on this page, skipping initialization');
                return;
            }
            
            // Create notification container if it doesn't exist
            this.createNotificationContainer();
            
            // Override any existing showNotification functions
            window.showNotification = this.showNotification.bind(this);
            window.notify = this.showNotification.bind(this);
            
            // Add global CSS for notifications
            this.addGlobalStyles();
            
            // Initialize dropdown functionality if elements exist
            this.initializeDropdown();
            
            // Start polling for new notifications (only if we can load them)
            this.startPolling();
            
            // Load initial notifications
            this.loadNotifications();
        } catch (error) {
            console.warn('Notification system initialization failed:', error);
        }
    }

    createNotificationContainer() {
        let container = document.getElementById('global-notifications-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'global-notifications-container';
            container.className = 'fixed top-4 right-4 space-y-2';
            container.style.zIndex = this.zIndex;
            container.style.pointerEvents = 'none'; // Allow clicks through container
            document.body.appendChild(container);
        }
        return container;
    }

    addGlobalStyles() {
        const styleId = 'global-notification-styles';
        if (document.getElementById(styleId)) return;

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .global-notification {
                z-index: ${this.zIndex} !important;
                pointer-events: auto;
                max-width: 400px;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                backdrop-filter: blur(8px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .global-notification.animate-in {
                animation: slideInRight 0.3s ease-out;
            }

            .global-notification.animate-out {
                animation: slideOutRight 0.3s ease-in;
            }

            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            @keyframes progressBar {
                from { width: 100%; }
                to { width: 0%; }
            }

            /* Notification dropdown specific styles */
            .notification-item {
                transition: all 0.2s ease;
            }
            
            .notification-item:hover {
                background-color: #f8fafc;
            }
            
            .notification-item.unread {
                background-color: #eff6ff;
                border-left: 4px solid #3b82f6;
            }
            
            .notification-actions {
                display: flex;
                gap: 0.5rem;
                margin-top: 0.5rem;
            }
            
            .notification-action-btn {
                padding: 0.25rem 0.75rem;
                border-radius: 0.375rem;
                font-size: 0.75rem;
                font-weight: 500;
                transition: all 0.2s ease;
                border: none;
                cursor: pointer;
            }
            
            .notification-action-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .priority-urgent { border-left-color: #dc2626 !important; }
            .priority-high { border-left-color: #ea580c !important; }
            .priority-medium { border-left-color: #ca8a04 !important; }
            .priority-low { border-left-color: #16a34a !important; }
            
            .loading-spinner {
                border: 2px solid #f3f4f6;
                border-top: 2px solid #3b82f6;
                border-radius: 50%;
                width: 1rem;
                height: 1rem;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    initializeDropdown() {
        try {
            // Initialize dropdown toggle functionality
            const dropdownButton = document.getElementById('notifications-dropdown-button');
            const dropdown = document.getElementById('notifications-dropdown');
            
            if (!dropdownButton || !dropdown) {
                console.log('Notification dropdown elements not found, skipping initialization');
                return;
            }
            
            dropdownButton.addEventListener('click', (e) => {
                e.stopPropagation();
                const isHidden = dropdown.classList.contains('hidden');
                
                if (isHidden) {
                    dropdown.classList.remove('hidden');
                    dropdownButton.setAttribute('aria-expanded', 'true');
                    this.loadNotifications();
                } else {
                    dropdown.classList.add('hidden');
                    dropdownButton.setAttribute('aria-expanded', 'false');
                }
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!dropdown.contains(e.target) && !dropdownButton.contains(e.target)) {
                    dropdown.classList.add('hidden');
                    dropdownButton.setAttribute('aria-expanded', 'false');
                }
            });
        } catch (error) {
            console.warn('Failed to initialize dropdown:', error);
        }
    }

    getLanguagePrefix() {
        // Get language prefix from current URL or fallback to 'ar'
        const path = window.location.pathname;
        const langMatch = path.match(/^\/([a-z]{2})\//);
        return langMatch ? `/${langMatch[1]}` : '/ar';
    }

    async loadNotifications() {
        try {
            const langPrefix = this.getLanguagePrefix();
            
            // Check if notifications endpoints exist before trying to load
            const notificationsResponse = await fetch(`${langPrefix}/notifications/api/notifications/?limit=10`);
            let notifications = [];
            
            // Check for redirects (likely to login page)
            if (notificationsResponse.redirected || notificationsResponse.url.includes('login')) {
                console.warn('Notifications API redirected to login - authentication required');
            } else if (notificationsResponse.ok && notificationsResponse.status < 300) {
                const contentType = notificationsResponse.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    try {
                        const responseText = await notificationsResponse.text();
                        // Check if response starts with HTML
                        if (responseText.trim().startsWith('<!DOCTYPE') || responseText.trim().startsWith('<html')) {
                            console.warn('Notifications API returned HTML instead of JSON - likely authentication issue');
                        } else {
                            const data = JSON.parse(responseText);
                            notifications = data.notifications || [];
                        }
                    } catch (jsonError) {
                        console.warn('Failed to parse notifications JSON:', jsonError.message);
                    }
                } else {
                    console.warn('Notifications API returned non-JSON response, content-type:', contentType);
                }
            } else {
                console.warn('Notifications API not available, status:', notificationsResponse.status);
            }
            
            this.notifications = notifications;

            // Load action items with similar error handling
            const actionResponse = await fetch(`${langPrefix}/notifications/api/actions/?limit=10`);
            let actionItems = [];
            
            // Check for redirects (likely to login page)
            if (actionResponse.redirected || actionResponse.url.includes('login')) {
                console.warn('Actions API redirected to login - authentication required');
            } else if (actionResponse.ok && actionResponse.status < 300) {
                const contentType = actionResponse.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    try {
                        const responseText = await actionResponse.text();
                        // Check if response starts with HTML
                        if (responseText.trim().startsWith('<!DOCTYPE') || responseText.trim().startsWith('<html')) {
                            console.warn('Actions API returned HTML instead of JSON - likely authentication issue');
                        } else {
                            const actionData = JSON.parse(responseText);
                            actionItems = actionData.action_items || [];
                        }
                    } catch (jsonError) {
                        console.warn('Failed to parse actions JSON:', jsonError.message);
                    }
                } else {
                    console.warn('Actions API returned non-JSON response, content-type:', contentType);
                }
            } else {
                console.warn('Actions API not available, status:', actionResponse.status);
            }
            
            this.actionItems = actionItems;
            
            this.renderNotifications();
            this.updateBadge();
            
        } catch (error) {
            console.warn('Notifications system not available:', error);
            // Don't show error state, just silently handle it
            this.notifications = [];
            this.actionItems = [];
            this.renderNotifications();
            this.updateBadge();
        }
    }

    renderNotifications() {
        const notificationsList = document.getElementById('notifications-list');
        const loadingElement = document.getElementById('notifications-loading');
        const emptyElement = document.getElementById('notifications-empty');
        const countElement = document.getElementById('notifications-count');
        
        if (!notificationsList) return;
        
        // Hide loading state
        if (loadingElement) loadingElement.classList.add('hidden');
        
        // Combine notifications and action items
        const allItems = [
            ...this.notifications.map(n => ({...n, type: 'notification'})),
            ...this.actionItems.map(a => ({...a, type: 'action_item'}))
        ].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        
        if (allItems.length === 0) {
            emptyElement?.classList.remove('hidden');
            notificationsList.innerHTML = '';
            if (countElement) countElement.textContent = '0 إشعار';
            return;
        }
        
        emptyElement?.classList.add('hidden');
        
        // Render notifications
        notificationsList.innerHTML = allItems.slice(0, 10).map(item => 
            this.renderNotificationItem(item)
        ).join('');
        
        // Update count
        if (countElement) {
            const unreadCount = allItems.filter(item => 
                item.type === 'notification' ? !item.is_read : item.status === 'pending'
            ).length;
            countElement.textContent = `${allItems.length} إشعار (${unreadCount} غير مقروء)`;
        }
    }

    renderNotificationItem(item) {
        const isUnread = item.type === 'notification' ? !item.is_read : item.status === 'pending';
        const priority = item.priority || 'medium';
        const timeAgo = this.getTimeAgo(item.created_at);
        
        let actionButtons = '';
        
        if (item.type === 'action_item' && item.metadata?.actions) {
            actionButtons = `
                <div class="notification-actions">
                    ${item.metadata.actions.map(action => `
                        <button class="notification-action-btn ${action.class}"
                                onclick="globalNotificationSystem.handleActionClick('${item.id}', '${action.type}', '${item.action_type}')">
                            ${action.label}
                        </button>
                    `).join('')}
                </div>
            `;
        } else if (item.type === 'notification' && item.action_required && item.action_url) {
            actionButtons = `
                <div class="notification-actions">
                    <a href="${item.action_url}" class="notification-action-btn bg-blue-600 text-white">
                        ${item.action_text || 'عرض'}
                    </a>
                    <button class="notification-action-btn bg-gray-200 text-gray-700"
                            onclick="globalNotificationSystem.markAsRead('${item.id}')">
                        تم
                    </button>
                </div>
            `;
        }
        
        const icon = this.getNotificationIcon(item);
        
        return `
            <div class="notification-item p-3 ${isUnread ? 'unread priority-' + priority : ''}" 
                 data-notification-id="${item.id}">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <i class="${icon} text-lg"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex justify-between items-start">
                            <h4 class="text-sm font-medium text-gray-900 line-clamp-2">
                                ${item.title}
                            </h4>
                            <span class="text-xs text-gray-500 whitespace-nowrap ml-2">
                                ${timeAgo}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                            ${item.message || item.description}
                        </p>
                        ${actionButtons}
                    </div>
                </div>
            </div>
        `;
    }

    getNotificationIcon(item) {
        const iconMap = {
            'work_order_created': 'fas fa-wrench text-blue-500',
            'work_order_assigned': 'fas fa-user-check text-green-500',
            'work_order_completed': 'fas fa-check-circle text-green-500',
            'inventory_low_stock': 'fas fa-exclamation-triangle text-orange-500',
            'purchase_order_created': 'fas fa-shopping-cart text-purple-500',
            'transfer_request_created': 'fas fa-exchange-alt text-blue-500',
            'transfer_completed': 'fas fa-check text-green-500',
            'material_request': 'fas fa-box text-yellow-500',
            'quality_inspection': 'fas fa-search text-blue-500',
            'generate_invoice': 'fas fa-file-invoice text-green-500',
        };
        
        const typeCode = item.notification_type?.code || item.action_type || 'default';
        return iconMap[typeCode] || 'fas fa-bell text-gray-500';
    }

    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `${diffInMinutes} د`;
        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} س`;
        return `${Math.floor(diffInMinutes / 1440)} ي`;
    }

    updateBadge() {
        const badge = document.getElementById('notification-badge');
        if (!badge) return;
        
        const unreadCount = this.notifications.filter(n => !n.is_read).length + 
                           this.actionItems.filter(a => a.status === 'pending').length;
        
        if (unreadCount > 0) {
            badge.textContent = unreadCount > 99 ? '99+' : unreadCount.toString();
            badge.classList.remove('hidden');
        } else {
            badge.classList.add('hidden');
        }
    }

    async handleActionClick(itemId, actionType, itemActionType) {
        try {
            // Show loading state
            this.showNotification('جاري المعالجة...', 'info', 2000);
            
            const langPrefix = this.getLanguagePrefix();
            const response = await fetch(`${langPrefix}/notifications/api/handle-action/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    item_id: itemId,
                    action_type: actionType,
                    item_action_type: itemActionType
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message || 'تم بنجاح', 'success');
                
                // Reload notifications to reflect changes
                this.loadNotifications();
                
                // If action requires redirect, handle it
                if (result.redirect_url) {
                    setTimeout(() => {
                        window.location.href = result.redirect_url;
                    }, 1000);
                }
            } else {
                this.showNotification(result.message || 'حدث خطأ', 'error');
            }
            
        } catch (error) {
            console.error('Error handling action:', error);
            this.showNotification('حدث خطأ في المعالجة', 'error');
        }
    }

    async markAsRead(notificationId) {
        try {
            const langPrefix = this.getLanguagePrefix();
            const response = await fetch(`${langPrefix}/notifications/mark-read/${notificationId}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                // Update local state
                const notification = this.notifications.find(n => n.id === notificationId);
                if (notification) {
                    notification.is_read = true;
                }
                
                this.renderNotifications();
                this.updateBadge();
            }
            
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    async markAllNotificationsRead() {
        try {
            const langPrefix = this.getLanguagePrefix();
            const response = await fetch(`${langPrefix}/notifications/mark-all-read/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                // Update local state
                this.notifications.forEach(n => n.is_read = true);
                
                this.renderNotifications();
                this.updateBadge();
                this.showNotification('تم تحديد جميع الإشعارات كمقروءة', 'success');
            }
            
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    }

    startPolling() {
        // Only start polling if we can successfully load notifications initially
        try {
            // Poll for new notifications every 30 seconds
            this.pollInterval = setInterval(() => {
                try {
                    this.loadNotifications();
                } catch (error) {
                    console.warn('Error during notification polling:', error);
                }
            }, 30000);
        } catch (error) {
            console.warn('Failed to start notification polling:', error);
        }
    }

    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    }

    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    showErrorState() {
        const notificationsList = document.getElementById('notifications-list');
        const loadingElement = document.getElementById('notifications-loading');
        
        if (loadingElement) loadingElement.classList.add('hidden');
        
        if (notificationsList) {
            notificationsList.innerHTML = `
                <div class="text-center py-8 text-red-500">
                    <i class="fas fa-exclamation-triangle text-3xl mb-2"></i>
                    <p class="text-sm">حدث خطأ في تحميل الإشعارات</p>
                    <button onclick="globalNotificationSystem.loadNotifications()" 
                            class="mt-2 text-xs text-blue-600 hover:text-blue-800">
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }
    }

    // Original notification methods remain the same
    showNotification(message, type = 'info', duration = 5000, options = {}) {
        const container = this.createNotificationContainer();
        
        const typeConfig = {
            'success': {
                bg: 'bg-green-50',
                border: 'border-green-200',
                text: 'text-green-800',
                icon: 'fas fa-check-circle text-green-500',
                title: options.title || 'نجح',
                titleEn: 'Success'
            },
            'error': {
                bg: 'bg-red-50',
                border: 'border-red-200', 
                text: 'text-red-800',
                icon: 'fas fa-exclamation-circle text-red-500',
                title: options.title || 'خطأ',
                titleEn: 'Error'
            },
            'warning': {
                bg: 'bg-yellow-50',
                border: 'border-yellow-200',
                text: 'text-yellow-800', 
                icon: 'fas fa-exclamation-triangle text-yellow-500',
                title: options.title || 'تحذير',
                titleEn: 'Warning'
            },
            'info': {
                bg: 'bg-blue-50',
                border: 'border-blue-200',
                text: 'text-blue-800',
                icon: 'fas fa-info-circle text-blue-500',
                title: options.title || 'معلومات',
                titleEn: 'Info'
            }
        };

        const config = typeConfig[type] || typeConfig['info'];
        const notificationId = 'notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        const notification = document.createElement('div');
        notification.id = notificationId;
        notification.className = `global-notification animate-in p-4 rounded-lg shadow-lg transition-all duration-300 ${config.bg} ${config.border} ${config.text}`;
        
        // Add RTL support
        const isRTL = document.documentElement.dir === 'rtl' || document.documentElement.lang === 'ar';
        
        notification.innerHTML = `
            <div class="flex items-start gap-3 ${isRTL ? 'flex-row-reverse' : ''}">
                <div class="flex-shrink-0">
                    <i class="${config.icon} text-lg"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="text-sm font-semibold mb-1">${config.title}</div>
                    <div class="text-sm leading-5" style="white-space: pre-line;">${message}</div>
                    ${options.actions ? `
                        <div class="mt-2 flex gap-2">
                            ${options.actions.map(action => `
                                <button onclick="${action.onClick}" class="text-xs px-2 py-1 rounded ${action.class || 'bg-gray-200 hover:bg-gray-300'}">
                                    ${action.text}
                                </button>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
                <button onclick="globalNotificationSystem.hideNotification('${notificationId}')" class="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors duration-200 ${isRTL ? 'ml-2' : 'mr-2'}">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
            ${duration > 0 ? `<div class="absolute bottom-0 left-0 h-1 bg-current opacity-20 transition-all duration-${duration}" style="width: 100%; animation: progressBar ${duration}ms linear;"></div>` : ''}
        `;

        container.appendChild(notification);

        // Auto-remove after specified duration
        if (duration > 0) {
            setTimeout(() => {
                this.hideNotification(notificationId);
            }, duration);
        }

        // Return notification element for manual control
        return notification;
    }

    hideNotification(notificationId) {
        const notification = document.getElementById(notificationId);
        if (notification) {
            notification.classList.remove('animate-in');
            notification.classList.add('animate-out');
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }

    // Helper methods
    showSuccess(message, duration = 5000, options = {}) {
        return this.showNotification(message, 'success', duration, options);
    }

    showError(message, duration = 7000, options = {}) {
        return this.showNotification(message, 'error', duration, options);
    }

    showWarning(message, duration = 6000, options = {}) {
        return this.showNotification(message, 'warning', duration, options);
    }

    showInfo(message, duration = 5000, options = {}) {
        return this.showNotification(message, 'info', duration, options);
    }
}

// Initialize the global notification system
let globalNotificationSystem;

document.addEventListener('DOMContentLoaded', function() {
    try {
        globalNotificationSystem = new GlobalNotificationSystem();
        
        // Make functions globally available only if initialization succeeded
        window.markAllNotificationsRead = function() {
            if (globalNotificationSystem) {
                globalNotificationSystem.markAllNotificationsRead();
            }
        };
        
        window.globalNotificationSystem = globalNotificationSystem;
    } catch (error) {
        console.warn('Failed to initialize global notification system:', error);
        
        // Provide fallback functions to prevent errors
        window.markAllNotificationsRead = function() {
            console.warn('Notification system not available');
        };
        
        window.globalNotificationSystem = null;
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    try {
        if (globalNotificationSystem) {
            globalNotificationSystem.stopPolling();
        }
    } catch (error) {
        console.warn('Error during notification system cleanup:', error);
    }
}); 