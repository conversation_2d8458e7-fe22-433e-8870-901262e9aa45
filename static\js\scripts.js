/**
 * Aftersails Frontend Scripts
 * 
 * This file contains shared JavaScript functionality used across the application.
 */

// Initialize RTL support based on document language
document.addEventListener('DOMContentLoaded', function() {
  // Set up RTL detection
  const htmlElement = document.documentElement;
  const language = htmlElement.getAttribute('lang') || 'ar';
  const direction = language === 'ar' ? 'rtl' : 'ltr';
  
  htmlElement.setAttribute('dir', direction);
  htmlElement.classList.add(direction);
  
  // Initialize tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-tooltip-target]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new Flowbite.Tooltip(tooltipTriggerEl);
  });
  
  // Initialize Flowbite dropdowns (ensure Flowbite is loaded)
  if (window.Flowbite && window.Flowbite.Dropdown) {
    const dropdownTriggerList = Array.from(document.querySelectorAll('[data-dropdown-toggle]'));
    dropdownTriggerList.map(function (dropdownTriggerEl) {
      return new Flowbite.Dropdown(dropdownTriggerEl);
    });
  } else {
    console.warn("Flowbite or Flowbite.Dropdown not found. Dropdowns may not be initialized.");
  }

  // Handle form validation
  setupFormValidation();
  
  // Setup notifications
  setupNotifications();
  
  // HTMX events for loading indicators
  document.addEventListener('htmx:beforeRequest', function(event) {
    const target = event.detail.elt;
    showLoadingIndicator(target);
  });
  
  document.addEventListener('htmx:afterRequest', function(event) {
    const target = event.detail.elt;
    hideLoadingIndicator(target);
  });
  
  // Dark mode toggle if we support it
  const darkModeToggle = document.getElementById('dark-mode-toggle');
  if (darkModeToggle) {
    darkModeToggle.addEventListener('click', toggleDarkMode);
  }
});

/**
 * Setup form validation with Arabic error messages
 */
function setupFormValidation() {
  const forms = document.querySelectorAll('form[data-validate="true"]');
  
  forms.forEach(form => {
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    inputs.forEach(input => {
      // Add blur event to validate when user leaves the field
      input.addEventListener('blur', function() {
        validateField(input);
      });
      
      // Add input event to clear error when user types
      input.addEventListener('input', function() {
        const errorElement = input.parentNode.querySelector('.error-message');
        if (errorElement) {
          errorElement.textContent = '';
          input.classList.remove('border-red-500');
        }
      });
    });
    
    form.addEventListener('submit', function(event) {
      let isValid = true;
      
      inputs.forEach(input => {
        if (!validateField(input)) {
          isValid = false;
        }
      });
      
      if (!isValid) {
        event.preventDefault();
      }
    });
  });
}

/**
 * Validate a single form field
 * @param {HTMLElement} field - The input field to validate
 * @returns {boolean} - Whether the field is valid
 */
function validateField(field) {
  const isValid = field.checkValidity();
  const errorElement = field.parentNode.querySelector('.error-message') || 
                      createErrorElement(field);
  
  if (!isValid) {
    let message = '';
    
    if (field.validity.valueMissing) {
      message = document.documentElement.getAttribute('lang') === 'ar' ? 
        'هذا الحقل مطلوب' : 'This field is required';
    } else if (field.validity.typeMismatch) {
      message = document.documentElement.getAttribute('lang') === 'ar' ? 
        'يرجى إدخال قيمة بالتنسيق الصحيح' : 'Please enter a value in the correct format';
    } else if (field.validity.patternMismatch) {
      message = document.documentElement.getAttribute('lang') === 'ar' ? 
        'يرجى مطابقة التنسيق المطلوب' : 'Please match the requested format';
    } else if (field.validity.tooShort) {
      message = document.documentElement.getAttribute('lang') === 'ar' ? 
        `يجب أن يكون طول هذا الحقل ${field.minLength} حرفًا على الأقل` : 
        `This field must be at least ${field.minLength} characters long`;
    } else if (field.validity.tooLong) {
      message = document.documentElement.getAttribute('lang') === 'ar' ? 
        `يجب أن يكون طول هذا الحقل ${field.maxLength} حرفًا كحد أقصى` : 
        `This field must be at most ${field.maxLength} characters long`;
    }
    
    errorElement.textContent = message;
    field.classList.add('border-red-500');
    return false;
  } else {
    errorElement.textContent = '';
    field.classList.remove('border-red-500');
    return true;
  }
}

/**
 * Create an error message element for a form field
 * @param {HTMLElement} field - The input field
 * @returns {HTMLElement} - The created error element
 */
function createErrorElement(field) {
  const errorElement = document.createElement('p');
  errorElement.className = 'error-message text-sm text-red-500 mt-1 rtl:text-right';
  field.parentNode.appendChild(errorElement);
  return errorElement;
}

/**
 * Show loading indicator on an element
 * @param {HTMLElement} element - The target element
 */
function showLoadingIndicator(element) {
  // Only add loading if the element doesn't have one already
  if (!element.querySelector('.loading-indicator')) {
    const loadingIndicator = document.createElement('span');
    loadingIndicator.className = 'loading-indicator inline-block animate-spin ml-2 h-4 w-4 border-2 border-t-transparent border-primary-500 rounded-full';
    
    // For button elements, append inside
    if (element.tagName === 'BUTTON') {
      element.appendChild(loadingIndicator);
    } else {
      // For other elements, insert after
      element.parentNode.insertBefore(loadingIndicator, element.nextSibling);
    }
    
    // Disable the element if it's a form control
    if (['BUTTON', 'INPUT', 'SELECT', 'TEXTAREA'].includes(element.tagName)) {
      element.setAttribute('disabled', 'disabled');
    }
  }
}

/**
 * Hide loading indicator on an element
 * @param {HTMLElement} element - The target element
 */
function hideLoadingIndicator(element) {
  const loadingIndicator = element.querySelector('.loading-indicator');
  if (loadingIndicator) {
    loadingIndicator.remove();
  }
  
  // Re-enable the element
  if (['BUTTON', 'INPUT', 'SELECT', 'TEXTAREA'].includes(element.tagName)) {
    element.removeAttribute('disabled');
  }
}

/**
 * Setup notifications system
 */
function setupNotifications() {
  // Get notification elements
  const notifications = document.querySelectorAll('.notification');
  
  // Add auto-dismiss after 5 seconds
  notifications.forEach(notification => {
    setTimeout(() => {
      dismissNotification(notification);
    }, 5000);
    
    // Add click handler for dismiss button
    const dismissButton = notification.querySelector('.notification-dismiss');
    if (dismissButton) {
      dismissButton.addEventListener('click', () => {
        dismissNotification(notification);
      });
    }
  });
}

/**
 * Dismiss a notification with animation
 * @param {HTMLElement} notification - The notification element
 */
function dismissNotification(notification) {
  notification.classList.add('opacity-0');
  setTimeout(() => {
    notification.remove();
  }, 300);
}

/**
 * Toggle dark mode
 */
function toggleDarkMode() {
  document.documentElement.classList.toggle('dark');
  
  // Save preference to localStorage
  const isDarkMode = document.documentElement.classList.contains('dark');
  localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');
  
  // Update any dark mode toggles
  const darkModeToggles = document.querySelectorAll('.dark-mode-toggle');
  darkModeToggles.forEach(toggle => {
    const toggleIcon = toggle.querySelector('i');
    if (toggleIcon) {
      toggleIcon.className = isDarkMode ? 
        'fas fa-sun' : 'fas fa-moon';
    }
  });
}
