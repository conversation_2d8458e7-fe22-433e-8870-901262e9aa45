/**
 * Supply Chain Dashboard Enhanced JavaScript
 * Provides interactive functionality, animations, and real-time updates
 */

class SupplyChainDashboard {
    constructor() {
        this.isLoaded = false;
        this.counters = new Map();
        this.notifications = [];
        this.refreshInterval = null;
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
            this.initializeAnimations();
            this.startCounterAnimations();
            this.initializeTooltips();
            this.setupRealTimeUpdates();
            this.initializeProgressBars();
            this.setupKeyboardShortcuts();
            this.initializeNotifications();
            this.isLoaded = true;
            console.log('Supply Chain Dashboard initialized successfully');
        });
    }

    setupEventListeners() {
        // Card hover effects
        document.querySelectorAll('.dashboard-card').forEach(card => {
            card.addEventListener('mouseenter', this.handleCardHover.bind(this));
            card.addEventListener('mouseleave', this.handleCardLeave.bind(this));
        });

        // Quick access button clicks
        document.querySelectorAll('.quick-access-btn').forEach(btn => {
            btn.addEventListener('click', this.handleQuickActionClick.bind(this));
        });

        // Workflow step interactions
        document.querySelectorAll('.workflow-step').forEach(step => {
            step.addEventListener('click', this.handleWorkflowStepClick.bind(this));
        });

        // Search functionality
        const searchInput = document.getElementById('dashboard-search');
        if (searchInput) {
            searchInput.addEventListener('input', this.handleSearch.bind(this));
        }

        // Window resize handler
        window.addEventListener('resize', this.handleResize.bind(this));

        // Visibility change for pausing animations when tab is not visible
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }

    initializeAnimations() {
        // Stagger animation for cards
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 150);
        });

        // Hero section animation
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            heroSection.style.opacity = '0';
            heroSection.style.transform = 'scale(0.95)';
            
            setTimeout(() => {
                heroSection.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
                heroSection.style.opacity = '1';
                heroSection.style.transform = 'scale(1)';
            }, 200);
        }
    }

    startCounterAnimations() {
        const counters = document.querySelectorAll('.stat-counter');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => observer.observe(counter));
    }

    animateCounter(element) {
        const target = parseInt(element.textContent) || 0;
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;

        const updateCounter = () => {
            current += increment;
            if (current < target) {
                element.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        };

        updateCounter();
    }

    initializeTooltips() {
        // Enhanced tooltips for better UX
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.classList.add('tooltip-enhanced');
        });

        // Add contextual tooltips to key elements
        this.addContextualTooltips();
    }

    addContextualTooltips() {
        const tooltipData = {
            '.feature-icon': 'Click to access this management area',
            '.quick-access-btn': 'Quick action - keyboard shortcuts available',
            '.workflow-step': 'Click to learn more about this step',
            '.stat-counter': 'Real-time data - updates every 5 minutes'
        };

        Object.entries(tooltipData).forEach(([selector, tooltip]) => {
            document.querySelectorAll(selector).forEach(element => {
                if (!element.getAttribute('data-tooltip')) {
                    element.setAttribute('data-tooltip', tooltip);
                    element.classList.add('tooltip-enhanced');
                }
            });
        });
    }

    setupRealTimeUpdates() {
        // Start real-time data refresh every 5 minutes
        this.refreshInterval = setInterval(() => {
            this.refreshDashboardData();
        }, 300000); // 5 minutes

        // Immediate refresh on page load
        setTimeout(() => {
            this.refreshDashboardData();
        }, 2000);
    }

    async refreshDashboardData() {
        try {
            // Show loading indicators
            this.showLoadingIndicators();

            // Simulate API call (replace with actual endpoint)
            const response = await fetch('/api/supply-chain/dashboard-data/', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.updateDashboardData(data);
                this.showNotification('Dashboard data updated successfully', 'success');
            } else {
                throw new Error('Failed to fetch dashboard data');
            }
        } catch (error) {
            console.error('Error refreshing dashboard data:', error);
            this.showNotification('Failed to update dashboard data', 'error');
        } finally {
            this.hideLoadingIndicators();
        }
    }

    updateDashboardData(data) {
        // Update counters
        if (data.stats) {
            Object.entries(data.stats).forEach(([key, value]) => {
                const element = document.querySelector(`[data-stat="${key}"]`);
                if (element) {
                    this.animateCounterUpdate(element, value);
                }
            });
        }

        // Update activity feed
        if (data.activities) {
            this.updateActivityFeed(data.activities);
        }

        // Update alerts
        if (data.alerts) {
            this.updateAlerts(data.alerts);
        }
    }

    animateCounterUpdate(element, newValue) {
        const currentValue = parseInt(element.textContent) || 0;
        const difference = newValue - currentValue;
        
        if (difference !== 0) {
            element.style.transform = 'scale(1.1)';
            element.style.color = difference > 0 ? '#10b981' : '#ef4444';
            
            setTimeout(() => {
                this.animateCounter(element, newValue);
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.color = '';
                }, 500);
            }, 200);
        }
    }

    initializeProgressBars() {
        const progressBars = document.querySelectorAll('.progress-bar');
        
        progressBars.forEach(bar => {
            const fill = bar.querySelector('.progress-fill');
            const percentage = fill.getAttribute('data-percentage') || 0;
            
            // Animate progress bar on load
            setTimeout(() => {
                fill.style.width = percentage + '%';
            }, 1000);
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only activate shortcuts when not in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

            const shortcuts = {
                'KeyI': () => this.navigateTo('inventory'),
                'KeyW': () => this.navigateTo('warehouse'),
                'KeyP': () => this.navigateTo('purchasing'),
                'KeyD': () => this.navigateTo('unified-dashboard'),
                'KeyR': () => this.refreshDashboardData(),
                'KeyS': () => this.focusSearch(),
                'Escape': () => this.closeModals()
            };

            if (e.ctrlKey && shortcuts[e.code]) {
                e.preventDefault();
                shortcuts[e.code]();
            }
        });

        // Show keyboard shortcuts help
        this.createKeyboardShortcutsHelp();
    }

    createKeyboardShortcutsHelp() {
        const helpButton = document.createElement('button');
        helpButton.className = 'btn btn-outline-secondary position-fixed';
        helpButton.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;';
        helpButton.innerHTML = '<i class="fas fa-keyboard"></i>';
        helpButton.setAttribute('data-tooltip', 'Keyboard Shortcuts (?)');
        helpButton.classList.add('tooltip-enhanced');
        
        helpButton.addEventListener('click', this.showKeyboardShortcuts.bind(this));
        document.body.appendChild(helpButton);
    }

    showKeyboardShortcuts() {
        const modal = document.createElement('div');
        modal.className = 'modal fade show';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Keyboard Shortcuts</h5>
                        <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Navigation</h6>
                                <ul class="list-unstyled">
                                    <li><kbd>Ctrl + I</kbd> Inventory</li>
                                    <li><kbd>Ctrl + W</kbd> Warehouse</li>
                                    <li><kbd>Ctrl + P</kbd> Purchasing</li>
                                    <li><kbd>Ctrl + D</kbd> Dashboard</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Actions</h6>
                                <ul class="list-unstyled">
                                    <li><kbd>Ctrl + R</kbd> Refresh Data</li>
                                    <li><kbd>Ctrl + S</kbd> Search</li>
                                    <li><kbd>Esc</kbd> Close Modals</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Auto-close after 5 seconds
        setTimeout(() => {
            modal.remove();
        }, 5000);
    }

    initializeNotifications() {
        // Create notification container
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'position-fixed';
        container.style.cssText = 'top: 20px; right: 20px; z-index: 99999; max-width: 400px;';
        document.body.appendChild(container);
    }

    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show shadow-sm`;
        notification.style.cssText = 'margin-bottom: 10px; border-radius: 10px;';
        
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        
        notification.innerHTML = `
            <i class="fas fa-${iconMap[type]} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.getElementById('notification-container');
        container.appendChild(notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }

    handleCardHover(event) {
        const card = event.currentTarget;
        card.style.transform = 'translateY(-8px) scale(1.02)';
        
        // Add ripple effect
        this.createRippleEffect(card, event);
    }

    handleCardLeave(event) {
        const card = event.currentTarget;
        card.style.transform = '';
    }

    createRippleEffect(element, event) {
        const ripple = document.createElement('div');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        element.style.position = 'relative';
        element.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }

    handleQuickActionClick(event) {
        const button = event.currentTarget;
        
        // Add click animation
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
        
        // Show loading state for external links
        if (button.href && !button.href.includes('#')) {
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        }
    }

    handleWorkflowStepClick(event) {
        const step = event.currentTarget;
        const stepInfo = this.getWorkflowStepInfo(step);
        
        if (stepInfo) {
            this.showWorkflowModal(stepInfo);
        }
    }

    getWorkflowStepInfo(step) {
        const stepTexts = {
            'Create Order': {
                title: 'Create Purchase Order',
                description: 'Generate purchase orders based on stock levels and reorder points.',
                actions: ['Check low stock items', 'Select suppliers', 'Create PO', 'Send to approval']
            },
            'Receive Goods': {
                title: 'Receive Goods',
                description: 'Process incoming shipments and update inventory levels.',
                actions: ['Scan barcodes', 'Verify quantities', 'Update stock', 'Generate receipts']
            },
            'Store Items': {
                title: 'Store Items',
                description: 'Allocate items to appropriate warehouse locations.',
                actions: ['Choose locations', 'Update item positions', 'Print labels', 'Confirm storage']
            },
            'Track & Analyze': {
                title: 'Track & Analyze',
                description: 'Monitor performance and generate insights.',
                actions: ['View reports', 'Analyze trends', 'Optimize processes', 'Plan improvements']
            }
        };

        const stepTitle = step.querySelector('h6')?.textContent;
        return stepTexts[stepTitle] || null;
    }

    showWorkflowModal(stepInfo) {
        const modal = document.createElement('div');
        modal.className = 'modal fade show';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${stepInfo.title}</h5>
                        <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                    </div>
                    <div class="modal-body">
                        <p>${stepInfo.description}</p>
                        <h6>Key Actions:</h6>
                        <ul>
                            ${stepInfo.actions.map(action => `<li>${action}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary">Learn More</button>
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Close</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    handleSearch(event) {
        const query = event.target.value.toLowerCase();
        const searchableElements = document.querySelectorAll('[data-searchable]');
        
        searchableElements.forEach(element => {
            const text = element.textContent.toLowerCase();
            const isVisible = text.includes(query);
            element.style.display = isVisible ? '' : 'none';
        });
    }

    handleResize() {
        // Recalculate layouts on resize
        this.recalculateLayouts();
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // Pause animations and updates when tab is not visible
            this.pauseAnimations();
        } else {
            // Resume when tab becomes visible
            this.resumeAnimations();
        }
    }

    pauseAnimations() {
        document.querySelectorAll('[style*="animation"]').forEach(el => {
            el.style.animationPlayState = 'paused';
        });
    }

    resumeAnimations() {
        document.querySelectorAll('[style*="animation"]').forEach(el => {
            el.style.animationPlayState = 'running';
        });
    }

    navigateTo(section) {
        const urls = {
            'inventory': '/ar/core/supply-chain/items/',
            'warehouse': '/ar/core/supply-chain/locations/',
            'purchasing': '/ar/core/supply-chain/purchase-orders/',
            'unified-dashboard': '/ar/core/supply-chain/unified/'
        };
        
        if (urls[section]) {
            window.location.href = urls[section];
        }
    }

    focusSearch() {
        const searchInput = document.getElementById('dashboard-search');
        if (searchInput) {
            searchInput.focus();
        }
    }

    closeModals() {
        document.querySelectorAll('.modal.show').forEach(modal => {
            modal.remove();
        });
    }

    showLoadingIndicators() {
        document.querySelectorAll('.stat-counter').forEach(counter => {
            counter.innerHTML = '<div class="loading-skeleton" style="width: 40px; height: 20px;"></div>';
        });
    }

    hideLoadingIndicators() {
        // Will be handled by updateDashboardData
    }

    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }

    recalculateLayouts() {
        // Force repaint for better responsive behavior
        document.querySelectorAll('.dashboard-card').forEach(card => {
            card.style.transform = 'translateZ(0)';
        });
    }

    // Cleanup on page unload
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        // Remove event listeners
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        window.removeEventListener('resize', this.handleResize);
    }
}

// CSS animations for ripple effect
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize dashboard when DOM is ready
const dashboard = new SupplyChainDashboard();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    dashboard.destroy();
});

// Export for global access
window.SupplyChainDashboard = dashboard;