/**
 * Supply Chain Management JavaScript
 * Handles all modal interactions and API calls for the supply chain system
 */

class SupplyChainManager {
    constructor() {
        this.baseApiUrl = '/api/supply-chain/';
        this.initializeEventListeners();
        this.loadInitialData();
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Item Management Events
        document.addEventListener('click', this.handleItemEvents.bind(this));
        
        // Movement Events
        document.addEventListener('click', this.handleMovementEvents.bind(this));
        
        // Location Events
        document.addEventListener('click', this.handleLocationEvents.bind(this));
        
        // Transfer Events
        document.addEventListener('click', this.handleTransferEvents.bind(this));
        
        // Supplier Events
        document.addEventListener('click', this.handleSupplierEvents.bind(this));
        
        // Purchase Order Events
        document.addEventListener('click', this.handlePurchaseOrderEvents.bind(this));
        
        // Form submissions
        document.addEventListener('submit', this.handleFormSubmissions.bind(this));
    }

    // Load initial dashboard data
    async loadInitialData() {
        try {
            await this.loadSupplyChainStats();
            await this.loadLowStockItems();
            await this.loadPendingTransfers();
            await this.loadPendingOrders();
        } catch (error) {
            console.error('Error loading initial data:', error);
        }
    }

    // ========== ITEM MANAGEMENT ==========
    
    handleItemEvents(event) {
        if (event.target.matches('[data-action="show-items"]')) {
            event.preventDefault();
            this.openItemsModal();
        }
        
        if (event.target.matches('[data-action="add-item"]')) {
            event.preventDefault();
            this.openAddItemModal();
        }
        
        if (event.target.matches('[data-action="edit-item"]')) {
            event.preventDefault();
            const itemId = event.target.dataset.itemId;
            this.openEditItemModal(itemId);
        }
        
        if (event.target.matches('[data-action="delete-item"]')) {
            event.preventDefault();
            const itemId = event.target.dataset.itemId;
            this.deleteItem(itemId);
        }
    }

    async openItemsModal() {
        try {
            const response = await fetch(`${this.baseApiUrl}items/`);
            const data = await response.json();
            
            const modalHtml = this.generateItemsModalHtml(data.results);
            this.showModal('عرض جميع الأصناف', modalHtml, 'modal-lg');
        } catch (error) {
            this.showError('خطأ في تحميل الأصناف');
        }
    }

    async openAddItemModal() {
        const modalHtml = `
            <form id="add-item-form" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="item-name" class="form-label">اسم الصنف *</label>
                            <input type="text" class="form-control" id="item-name" name="name" required>
                            <div class="invalid-feedback">يرجى إدخال اسم الصنف</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="item-sku" class="form-label">رمز الصنف</label>
                            <input type="text" class="form-control" id="item-sku" name="sku">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="item-category" class="form-label">الفئة</label>
                            <select class="form-select" id="item-category" name="category">
                                <option value="">اختر الفئة</option>
                                <!-- Categories will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="item-unit" class="form-label">وحدة القياس</label>
                            <input type="text" class="form-control" id="item-unit" name="unit" placeholder="قطعة، كيلو، لتر">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="item-cost-price" class="form-label">سعر التكلفة</label>
                            <input type="number" step="0.01" class="form-control" id="item-cost-price" name="cost_price">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="item-selling-price" class="form-label">سعر البيع</label>
                            <input type="number" step="0.01" class="form-control" id="item-selling-price" name="selling_price">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="item-min-stock" class="form-label">الحد الأدنى للمخزون</label>
                            <input type="number" class="form-control" id="item-min-stock" name="minimum_stock_level">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="item-description" class="form-label">وصف الصنف</label>
                    <textarea class="form-control" id="item-description" name="description" rows="3"></textarea>
                </div>
                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الصنف</button>
                </div>
            </form>
        `;
        
        this.showModal('إضافة صنف جديد', modalHtml);
    }

    async saveItem(formData) {
        try {
            const response = await fetch(`${this.baseApiUrl}items/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(formData)
            });
            
            if (response.ok) {
                this.hideModal();
                this.showSuccess('تم إضافة الصنف بنجاح');
                this.refreshPage();
            } else {
                const errorData = await response.json();
                this.showError('خطأ في إضافة الصنف: ' + JSON.stringify(errorData));
            }
        } catch (error) {
            this.showError('خطأ في إضافة الصنف');
        }
    }

    // ========== MOVEMENT MANAGEMENT ==========
    
    handleMovementEvents(event) {
        if (event.target.matches('[data-action="show-movements"]')) {
            event.preventDefault();
            this.openMovementsModal();
        }
        
        if (event.target.matches('[data-action="add-movement"]')) {
            event.preventDefault();
            this.openAddMovementModal();
        }
        
        if (event.target.matches('[data-action="stock-adjustment"]')) {
            event.preventDefault();
            this.openStockAdjustmentModal();
        }
    }

    async openMovementsModal() {
        try {
            const response = await fetch(`${this.baseApiUrl}movements/`);
            const data = await response.json();
            
            const modalHtml = this.generateMovementsModalHtml(data.results);
            this.showModal('سجل حركات المخزون', modalHtml, 'modal-lg');
        } catch (error) {
            this.showError('خطأ في تحميل حركات المخزون');
        }
    }

    async openAddMovementModal() {
        const modalHtml = `
            <form id="add-movement-form" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="movement-item" class="form-label">الصنف *</label>
                            <select class="form-select" id="movement-item" name="item" required>
                                <option value="">اختر الصنف</option>
                                <!-- Items will be loaded dynamically -->
                            </select>
                            <div class="invalid-feedback">يرجى اختيار الصنف</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="movement-type" class="form-label">نوع الحركة *</label>
                            <select class="form-select" id="movement-type" name="movement_type" required>
                                <option value="">اختر نوع الحركة</option>
                                <option value="IN">دخول</option>
                                <option value="OUT">خروج</option>
                                <option value="ADJUSTMENT">تعديل</option>
                                <option value="TRANSFER">تحويل</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار نوع الحركة</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="movement-quantity" class="form-label">الكمية *</label>
                            <input type="number" class="form-control" id="movement-quantity" name="quantity" required>
                            <div class="invalid-feedback">يرجى إدخال الكمية</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="movement-location" class="form-label">الموقع</label>
                            <select class="form-select" id="movement-location" name="location">
                                <option value="">اختر الموقع</option>
                                <!-- Locations will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="movement-notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="movement-notes" name="notes" rows="3"></textarea>
                </div>
                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تسجيل الحركة</button>
                </div>
            </form>
        `;
        
        this.showModal('تسجيل حركة جديدة', modalHtml);
        await this.loadItemsForSelect('movement-item');
        await this.loadLocationsForSelect('movement-location');
    }

    // ========== LOCATION MANAGEMENT ==========
    
    handleLocationEvents(event) {
        if (event.target.matches('[data-action="show-locations"]')) {
            event.preventDefault();
            this.openLocationsModal();
        }
        
        if (event.target.matches('[data-action="add-location"]')) {
            event.preventDefault();
            this.openAddLocationModal();
        }
        
        if (event.target.matches('[data-action="item-locations"]')) {
            event.preventDefault();
            this.openItemLocationsModal();
        }
    }

    async openLocationsModal() {
        try {
            const response = await fetch(`${this.baseApiUrl}locations/`);
            const data = await response.json();
            
            const modalHtml = this.generateLocationsModalHtml(data.results);
            this.showModal('إدارة المواقع', modalHtml, 'modal-lg');
        } catch (error) {
            this.showError('خطأ في تحميل المواقع');
        }
    }

    async openAddLocationModal() {
        const modalHtml = `
            <form id="add-location-form" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="location-name" class="form-label">اسم الموقع *</label>
                            <input type="text" class="form-control" id="location-name" name="name" required>
                            <div class="invalid-feedback">يرجى إدخال اسم الموقع</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="location-code" class="form-label">رمز الموقع</label>
                            <input type="text" class="form-control" id="location-code" name="code">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="location-type" class="form-label">نوع الموقع</label>
                            <select class="form-select" id="location-type" name="location_type">
                                <option value="MAIN_WAREHOUSE">مستودع رئيسي</option>
                                <option value="WAREHOUSE">مستودع فرعي</option>
                                <option value="SHOWROOM">صالة عرض</option>
                                <option value="WORKSHOP">ورشة</option>
                                <option value="OFFICE">مكتب</option>
                                <option value="STORAGE_ROOM">غرفة تخزين</option>
                                <option value="SECTION">قسم</option>
                                <option value="SHELF">رف</option>
                                <option value="BIN">صندوق</option>
                                <option value="RACK">رف معدني</option>
                                <option value="ZONE">منطقة</option>
                                <option value="BAY">خليج</option>
                                <option value="LEVEL">مستوى</option>
                                <option value="COLD_STORAGE">تخزين بارد</option>
                                <option value="HAZMAT_STORAGE">تخزين مواد خطرة</option>
                                <option value="RECEIVING_DOCK">رصيف الاستلام</option>
                                <option value="SHIPPING_DOCK">رصيف الشحن</option>
                                <option value="PICKING_AREA">منطقة التجميع</option>
                                <option value="PACKING_AREA">منطقة التعبئة</option>
                                <option value="QUALITY_CONTROL">مراقبة الجودة</option>
                                <option value="RETURNS_AREA">منطقة المرتجعات</option>
                                <option value="QUARANTINE">الحجر الصحي</option>
                                <option value="TRANSIT">نقل مؤقت</option>
                                <option value="VIRTUAL">موقع افتراضي</option>
                                <option value="EXTERNAL">موقع خارجي</option>
                                <option value="CUSTOMER_SITE">موقع العميل</option>
                                <option value="SUPPLIER_SITE">موقع المورد</option>
                                <option value="OTHER">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="location-capacity" class="form-label">السعة القصوى</label>
                            <input type="number" class="form-control" id="location-capacity" name="capacity">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="location-description" class="form-label">وصف الموقع</label>
                    <textarea class="form-control" id="location-description" name="description" rows="3"></textarea>
                </div>
                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الموقع</button>
                </div>
            </form>
        `;
        
        this.showModal('إضافة موقع جديد', modalHtml);
    }

    // ========== TRANSFER MANAGEMENT ==========
    
    handleTransferEvents(event) {
        if (event.target.matches('[data-action="show-transfers"]')) {
            event.preventDefault();
            this.openTransfersModal();
        }
        
        if (event.target.matches('[data-action="add-transfer"]')) {
            event.preventDefault();
            this.openAddTransferModal();
        }
        
        if (event.target.matches('[data-action="pending-transfers"]')) {
            event.preventDefault();
            this.openPendingTransfersModal();
        }
    }

    async openTransfersModal() {
        try {
            const response = await fetch(`${this.baseApiUrl}transfers/`);
            const data = await response.json();
            
            const modalHtml = this.generateTransfersModalHtml(data.results);
            this.showModal('سجل التحويلات', modalHtml, 'modal-lg');
        } catch (error) {
            this.showError('خطأ في تحميل التحويلات');
        }
    }

    // ========== SUPPLIER MANAGEMENT ==========
    
    handleSupplierEvents(event) {
        if (event.target.matches('[data-action="show-suppliers"]')) {
            event.preventDefault();
            this.openSuppliersModal();
        }
        
        if (event.target.matches('[data-action="add-supplier"]')) {
            event.preventDefault();
            this.openAddSupplierModal();
        }
    }

    async openSuppliersModal() {
        try {
            const response = await fetch(`${this.baseApiUrl}suppliers/`);
            const data = await response.json();
            
            const modalHtml = this.generateSuppliersModalHtml(data.results);
            this.showModal('عرض جميع الموردين', modalHtml, 'modal-lg');
        } catch (error) {
            this.showError('خطأ في تحميل الموردين');
        }
    }

    // ========== PURCHASE ORDER MANAGEMENT ==========
    
    handlePurchaseOrderEvents(event) {
        if (event.target.matches('[data-action="show-purchase-orders"]')) {
            event.preventDefault();
            this.openPurchaseOrdersModal();
        }
        
        if (event.target.matches('[data-action="add-purchase-order"]')) {
            event.preventDefault();
            this.openAddPurchaseOrderModal();
        }
        
        if (event.target.matches('[data-action="pending-orders"]')) {
            event.preventDefault();
            this.openPendingOrdersModal();
        }
    }

    async openPurchaseOrdersModal() {
        try {
            const response = await fetch(`${this.baseApiUrl}purchase-orders/`);
            const data = await response.json();
            
            const modalHtml = this.generatePurchaseOrdersModalHtml(data.results);
            this.showModal('عرض جميع الأوامر', modalHtml, 'modal-lg');
        } catch (error) {
            this.showError('خطأ في تحميل أوامر الشراء');
        }
    }

    // ========== FORM HANDLING ==========
    
    handleFormSubmissions(event) {
        if (event.target.matches('#add-item-form')) {
            event.preventDefault();
            this.handleItemFormSubmission(event.target);
        }
        
        if (event.target.matches('#add-movement-form')) {
            event.preventDefault();
            this.handleMovementFormSubmission(event.target);
        }
        
        if (event.target.matches('#add-location-form')) {
            event.preventDefault();
            this.handleLocationFormSubmission(event.target);
        }
        
        if (event.target.matches('#stock-adjustment-form')) {
            event.preventDefault();
            this.handleStockAdjustmentFormSubmission(event.target);
        }
    }

    async handleItemFormSubmission(form) {
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        await this.saveItem(data);
    }

    // ========== DATA LOADING HELPERS ==========
    
    async loadSupplyChainStats() {
        try {
            const response = await fetch(`${this.baseApiUrl}stats/`);
            const data = await response.json();
            this.updateStatsDisplay(data);
        } catch (error) {
            console.error('Error loading supply chain stats:', error);
        }
    }

    async loadLowStockItems() {
        try {
            const response = await fetch(`${this.baseApiUrl}low-stock/`);
            const data = await response.json();
            this.updateLowStockDisplay(data);
        } catch (error) {
            console.error('Error loading low stock items:', error);
        }
    }

    async loadItemsForSelect(selectId) {
        try {
            const response = await fetch(`${this.baseApiUrl}items/`);
            const data = await response.json();
            const select = document.getElementById(selectId);
            
            if (select) {
                data.results.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = item.name;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading items for select:', error);
        }
    }

    async loadLocationsForSelect(selectId) {
        try {
            const response = await fetch(`${this.baseApiUrl}locations/`);
            const data = await response.json();
            const select = document.getElementById(selectId);
            
            if (select) {
                data.results.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading locations for select:', error);
        }
    }

    // ========== HTML GENERATORS ==========
    
    generateItemsModalHtml(items) {
        if (!items || items.length === 0) {
            return '<p class="text-center text-muted">لا توجد أصناف</p>';
        }
        
        const itemsHtml = items.map(item => `
            <tr>
                <td>
                    <strong>${item.name}</strong><br>
                    <small class="text-muted">${item.sku || 'بدون رمز'}</small>
                </td>
                <td>${item.category || '-'}</td>
                <td>${item.current_stock || 0}</td>
                <td>${item.unit || '-'}</td>
                <td>
                    <span class="badge ${item.current_stock <= item.minimum_stock_level ? 'bg-danger' : 'bg-success'}">
                        ${item.current_stock <= item.minimum_stock_level ? 'منخفض' : 'عادي'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-sm" data-action="edit-item" data-item-id="${item.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" data-action="delete-item" data-item-id="${item.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        
        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الصنف</th>
                            <th>الفئة</th>
                            <th>المخزون</th>
                            <th>الوحدة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHtml}
                    </tbody>
                </table>
            </div>
        `;
    }

    generateMovementsModalHtml(movements) {
        if (!movements || movements.length === 0) {
            return '<p class="text-center text-muted">لا توجد حركات</p>';
        }
        
        const movementsHtml = movements.map(movement => `
            <tr>
                <td>${movement.item_name}</td>
                <td>
                    <span class="badge ${this.getMovementTypeBadgeClass(movement.movement_type)}">
                        ${this.getMovementTypeText(movement.movement_type)}
                    </span>
                </td>
                <td>${movement.quantity}</td>
                <td>${movement.location_name || '-'}</td>
                <td>${new Date(movement.created_at).toLocaleDateString('ar')}</td>
                <td>${movement.created_by_name || '-'}</td>
            </tr>
        `).join('');
        
        return `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الصنف</th>
                            <th>النوع</th>
                            <th>الكمية</th>
                            <th>الموقع</th>
                            <th>التاريخ</th>
                            <th>المستخدم</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${movementsHtml}
                    </tbody>
                </table>
            </div>
        `;
    }

    // ========== UTILITY METHODS ==========
    
    showModal(title, content, size = '') {
        const modalHtml = `
            <div class="modal fade" id="supplyChainModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog ${size}">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing modal
        const existingModal = document.getElementById('supplyChainModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // Add new modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('supplyChainModal'));
        modal.show();
    }

    hideModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('supplyChainModal'));
        if (modal) {
            modal.hide();
        }
    }

    showSuccess(message) {
        // Create a toast notification for success
        this.showToast(message, 'success');
    }

    showError(message) {
        // Create a toast notification for error
        this.showToast(message, 'danger');
    }

    showToast(message, type) {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }

    getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }

    refreshPage() {
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    getMovementTypeBadgeClass(type) {
        const classes = {
            'IN': 'bg-success',
            'OUT': 'bg-danger',
            'ADJUSTMENT': 'bg-warning',
            'TRANSFER': 'bg-info'
        };
        return classes[type] || 'bg-secondary';
    }

    getMovementTypeText(type) {
        const texts = {
            'IN': 'دخول',
            'OUT': 'خروج',
            'ADJUSTMENT': 'تعديل',
            'TRANSFER': 'تحويل'
        };
        return texts[type] || type;
    }

    updateStatsDisplay(data) {
        // Update dashboard statistics
        const statsElements = document.querySelectorAll('[data-stat]');
        statsElements.forEach(element => {
            const statType = element.dataset.stat;
            if (data[statType] !== undefined) {
                element.textContent = data[statType];
            }
        });
    }

    updateLowStockDisplay(data) {
        // Update low stock notifications
        const lowStockElement = document.querySelector('[data-low-stock-count]');
        if (lowStockElement && data.length !== undefined) {
            lowStockElement.textContent = data.length;
        }
    }

    // Generate locations modal HTML
    generateLocationsModalHtml(locations) {
        if (!locations || locations.length === 0) {
            return `
                <div class="text-center py-8">
                    <div class="mb-4">
                        <i class="fas fa-map-marker-alt text-gray-400 text-5xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مواقع مسجلة</h3>
                    <p class="text-gray-500 mb-4">ابدأ بإضافة موقع جديد لإدارة المخزون</p>
                    <button onclick="supplyChainManager.openAddLocationModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة موقع جديد
                    </button>
                </div>
            `;
        }

        let locationsRows = locations.map(location => {
            const statusClass = location.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
            const statusIcon = location.is_active ? 'fas fa-check-circle' : 'fas fa-times-circle';
            
            return `
                <tr class="hover:bg-gray-50">
                    <td class="border border-gray-300 px-4 py-2 font-mono text-sm">${location.code || '-'}</td>
                    <td class="border border-gray-300 px-4 py-2">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-warehouse text-gray-500"></i>
                            <span class="font-medium">${location.name}</span>
                        </div>
                        ${location.location_type ? `<span class="text-xs text-gray-500 mt-1 block">${this.getLocationTypeText(location.location_type)}</span>` : ''}
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-sm">
                        <div class="max-w-xs truncate" title="${location.formatted_address || location.address || '-'}">
                            ${location.formatted_address || location.address || '-'}
                        </div>
                        ${location.phone ? `<div class="text-xs text-gray-500 mt-1"><i class="fas fa-phone"></i> ${location.phone}</div>` : ''}
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        <span class="font-medium">${location.area_display || '-'}</span>
                        ${location.capacity ? `<div class="text-xs text-gray-500">سعة: ${location.capacity}</div>` : ''}
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        <span class="font-medium text-blue-600">${location.items_count || 0}</span>
                        ${location.total_quantity ? `<div class="text-xs text-gray-500">إجمالي: ${location.total_quantity} وحدة</div>` : ''}
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded text-sm ${statusClass}">
                            <i class="${statusIcon}"></i>
                            ${location.is_active ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-center">
                        <div class="flex justify-center gap-2">
                            <button onclick="viewLocation('${location.id}')" class="text-blue-600 hover:text-blue-800 p-1" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editLocation('${location.id}')" class="text-green-600 hover:text-green-800 p-1" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="manageLocationItems('${location.id}')" class="text-purple-600 hover:text-purple-800 p-1" title="إدارة الأصناف">
                                <i class="fas fa-boxes"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        return `
            <div class="mb-4 flex justify-between items-center">
                <div class="flex gap-2">
                    <button onclick="supplyChainManager.openAddLocationModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        إضافة موقع جديد
                    </button>
                    <button onclick="location.reload()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                        <i class="fas fa-sync"></i>
                        تحديث
                    </button>
                </div>
                <div class="text-sm text-gray-600">
                    إجمالي المواقع: <span class="font-medium">${locations.length}</span>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-barcode text-gray-600"></i> الرمز
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-warehouse text-gray-600"></i> الاسم والنوع
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-right">
                                <i class="fas fa-map-marker-alt text-gray-600"></i> العنوان
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-center">
                                <i class="fas fa-expand text-gray-600"></i> المساحة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-center">
                                <i class="fas fa-boxes text-gray-600"></i> الأصناف
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-center">
                                <i class="fas fa-toggle-on text-gray-600"></i> الحالة
                            </th>
                            <th class="border border-gray-300 px-4 py-2 text-center">
                                <i class="fas fa-cogs text-gray-600"></i> الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        ${locationsRows}
                    </tbody>
                </table>
            </div>
        `;
    }

    // Get location type display text
    getLocationTypeText(type) {
        const typeTexts = {
            'MAIN_WAREHOUSE': 'مستودع رئيسي',
            'WAREHOUSE': 'مستودع فرعي', 
            'SHOWROOM': 'صالة عرض',
            'WORKSHOP': 'ورشة',
            'OFFICE': 'مكتب',
            'STORAGE_ROOM': 'غرفة تخزين',
            'SECTION': 'قسم',
            'SHELF': 'رف',
            'BIN': 'صندوق',
            'RACK': 'رف معدني',
            'ZONE': 'منطقة',
            'BAY': 'خليج',
            'LEVEL': 'مستوى',
            'COLD_STORAGE': 'تخزين بارد',
            'HAZMAT_STORAGE': 'تخزين مواد خطرة',
            'RECEIVING_DOCK': 'رصيف الاستلام',
            'SHIPPING_DOCK': 'رصيف الشحن',
            'PICKING_AREA': 'منطقة التجميع',
            'PACKING_AREA': 'منطقة التعبئة',
            'QUALITY_CONTROL': 'مراقبة الجودة',
            'RETURNS_AREA': 'منطقة المرتجعات',
            'QUARANTINE': 'الحجر الصحي',
            'TRANSIT': 'نقل مؤقت',
            'VIRTUAL': 'موقع افتراضي',
            'EXTERNAL': 'موقع خارجي',
            'CUSTOMER_SITE': 'موقع العميل',
            'SUPPLIER_SITE': 'موقع المورد',
            'OTHER': 'أخرى'
        };
        return typeTexts[type] || type;
    }
}

// Initialize the Supply Chain Manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.supplyChainManager = new SupplyChainManager();
}); 