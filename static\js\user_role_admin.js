// Function to handle the tenant_id field based on service center or company selection
(function($) {
    $(document).ready(function() {
        const serviceCenter = $('#id_service_center');
        const company = $('#id_company');
        const tenantField = $('#id_tenant_id');
        
        // Store the endpoint for fetching tenant data
        const apiEndpoint = '/user-roles/api/tenant-info/';
        
        // Function to update tenant_id when service center changes
        serviceCenter.on('change', function() {
            const selectedId = $(this).val();
            if (selectedId) {
                // Clear company field
                company.val('');
                
                // Fetch tenant_id for the selected service center
                fetchTenantId('service_center', selectedId);
            }
        });
        
        // Function to update tenant_id when company changes
        company.on('change', function() {
            const selectedId = $(this).val();
            if (selectedId) {
                // Clear service center field
                serviceCenter.val('');
                
                // Fetch tenant_id for the selected company
                fetchTenantId('company', selectedId);
            }
        });
        
        // Function to fetch tenant_id from the server
        function fetchTenantId(entityType, entityId) {
            $.ajax({
                url: apiEndpoint,
                data: {
                    entity_type: entityType,
                    entity_id: entityId
                },
                dataType: 'json',
                success: function(data) {
                    if (data.tenant_id) {
                        tenantField.val(data.tenant_id);
                        // Update help text
                        const helpText = tenantField.siblings('.help');
                        if (helpText.length) {
                            helpText.text('Using tenant ID from selected ' + 
                                (entityType === 'service_center' ? 'service center' : 'company'));
                        }
                    }
                },
                error: function() {
                    // Display error or fallback
                    console.error('Failed to fetch tenant ID');
                    // You may want to set a default tenant ID or clear the field
                }
            });
        }
        
        // If service_center or company is already selected on page load, fetch tenant_id
        if (serviceCenter.val()) {
            fetchTenantId('service_center', serviceCenter.val());
        } else if (company.val()) {
            fetchTenantId('company', company.val());
        }
    });
})(django.jQuery); 