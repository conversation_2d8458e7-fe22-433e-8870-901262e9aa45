class WorkOrderAutoCompletion {
    constructor() {
        this.init();
    }

    init() {
        // Add auto-completion buttons to work order pages
        this.addAutoCompletionButtons();
        
        // Add event listeners
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('auto-complete-btn')) {
                e.preventDefault();
                const workOrderId = e.target.dataset.workOrderId;
                this.autoCompleteWorkOrder(workOrderId);
            }
            
            if (e.target.classList.contains('check-completion-status-btn')) {
                e.preventDefault();
                const workOrderId = e.target.dataset.workOrderId;
                this.checkCompletionStatus(workOrderId);
            }
            
            if (e.target.classList.contains('auto-complete-all-btn')) {
                e.preventDefault();
                this.autoCompleteAllReadyWorkOrders();
            }
        });
    }

    addAutoCompletionButtons() {
        // Add buttons to work order detail pages
        const workOrderDetails = document.querySelectorAll('.work-order-detail, .work-order-card');
        
        workOrderDetails.forEach(detail => {
            const workOrderId = detail.dataset.workOrderId;
            const status = detail.dataset.status;
            
            if (status === 'in_progress' && workOrderId) {
                this.addCompletionButton(detail, workOrderId);
            }
        });
        
        // Add bulk completion button to lists
        const workOrderLists = document.querySelectorAll('.work-order-list, .sales-dashboard');
        workOrderLists.forEach(list => {
            this.addBulkCompletionButton(list);
        });
    }

    addCompletionButton(container, workOrderId) {
        const buttonHtml = `
            <button type="button" 
                    class="auto-complete-btn bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm ml-2" 
                    data-work-order-id="${workOrderId}"
                    title="Auto-complete if ready">
                <i class="fas fa-magic mr-1"></i>
                Auto Complete
            </button>
            <button type="button" 
                    class="check-completion-status-btn bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm ml-1" 
                    data-work-order-id="${workOrderId}"
                    title="Check completion status">
                <i class="fas fa-clipboard-check mr-1"></i>
                Check Status
            </button>
        `;
        
        // Find a good place to insert the buttons
        const actionContainer = container.querySelector('.work-order-actions, .action-buttons, .card-actions');
        if (actionContainer) {
            actionContainer.insertAdjacentHTML('beforeend', buttonHtml);
        } else {
            // Create action container if not exists
            const newActionContainer = document.createElement('div');
            newActionContainer.className = 'work-order-actions mt-2';
            newActionContainer.innerHTML = buttonHtml;
            container.appendChild(newActionContainer);
        }
    }

    addBulkCompletionButton(container) {
        const buttonHtml = `
            <button type="button" 
                    class="auto-complete-all-btn bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded" 
                    title="Auto-complete all ready work orders">
                <i class="fas fa-magic mr-1"></i>
                Auto Complete All Ready
            </button>
        `;
        
        // Find dashboard actions or similar container
        const dashboardActions = container.querySelector('.dashboard-actions, .bulk-actions, .page-actions');
        if (dashboardActions) {
            dashboardActions.insertAdjacentHTML('beforeend', buttonHtml);
        }
    }

    async autoCompleteWorkOrder(workOrderId) {
        try {
            this.showLoading(`Auto-completing work order...`);
            
            const formData = new FormData();
            formData.append('work_order_id', workOrderId);
            formData.append('csrfmiddlewaretoken', this.getCSRFToken());
            
            const response = await fetch('/work_orders/api/auto-complete-work-orders/', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                if (data.completed) {
                    this.showSuccess(
                        `Work order ${data.work_order_number} completed successfully!` +
                        (data.invoice_created ? ` Invoice ${data.invoice_number} created.` : '') +
                        (data.sales_order_created ? ` Sales order ${data.sales_order_number} created.` : '')
                    );
                    
                    // Refresh the page to show updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    this.showWarning(`Work order ${data.work_order_number} is not ready for completion: ${data.message}`);
                }
            } else {
                this.showError(`Error: ${data.message || data.error}`);
            }
            
        } catch (error) {
            console.error('Auto-completion error:', error);
            this.showError('Network error during auto-completion');
        } finally {
            this.hideLoading();
        }
    }

    async checkCompletionStatus(workOrderId) {
        try {
            const response = await fetch(`/work_orders/api/check-completion-status/${workOrderId}/`);
            const data = await response.json();
            
            if (data.success) {
                const statusModal = this.createStatusModal(data);
                document.body.appendChild(statusModal);
            } else {
                this.showError(`Error checking status: ${data.error}`);
            }
            
        } catch (error) {
            console.error('Status check error:', error);
            this.showError('Network error during status check');
        }
    }

    async autoCompleteAllReadyWorkOrders() {
        try {
            this.showLoading('Checking and auto-completing all ready work orders...');
            
            const formData = new FormData();
            formData.append('csrfmiddlewaretoken', this.getCSRFToken());
            
            const response = await fetch('/work_orders/api/auto-complete-work-orders/', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                const summary = data.summary;
                this.showSuccess(
                    `Bulk completion finished:\n` +
                    `• Checked: ${summary.checked} work orders\n` +
                    `• Completed: ${summary.completed} work orders\n` +
                    `• Invoices created: ${summary.invoices_created}\n` +
                    `• Sales orders created: ${summary.sales_orders_created}\n` +
                    `• Errors: ${summary.errors_count}`
                );
                
                // Show details if available
                if (data.details && data.details.length > 0) {
                    this.showCompletionDetails(data.details);
                }
                
                // Refresh page after showing results
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } else {
                this.showError(`Bulk completion error: ${data.message || data.error}`);
            }
            
        } catch (error) {
            console.error('Bulk completion error:', error);
            this.showError('Network error during bulk completion');
        } finally {
            this.hideLoading();
        }
    }

    createStatusModal(data) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[9998]';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Completion Status</h3>
                        <button type="button" class="close-modal text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">Work Order:</span>
                            <span>${data.work_order_number}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="font-medium">Status:</span>
                            <span class="px-2 py-1 rounded text-sm ${data.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">${data.status}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="font-medium">Ready for Completion:</span>
                            <span class="${data.can_complete ? 'text-green-600' : 'text-red-600'}">${data.can_complete ? 'Yes' : 'No'}</span>
                        </div>
                        
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="font-medium">Operations:</span>
                                <span class="${data.operations_completed ? 'text-green-600' : 'text-yellow-600'}">${data.progress.operations}</span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="font-medium">Materials:</span>
                                <span class="${data.materials_consumed ? 'text-green-600' : 'text-yellow-600'}">${data.progress.materials}</span>
                            </div>
                        </div>
                        
                        ${data.missing_operations.length > 0 ? `
                            <div class="mt-3">
                                <h4 class="font-medium text-red-600">Missing Operations:</h4>
                                <ul class="list-disc list-inside text-sm text-gray-600 mt-1">
                                    ${data.missing_operations.map(op => `<li>${op}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                        
                        ${data.missing_materials.length > 0 ? `
                            <div class="mt-3">
                                <h4 class="font-medium text-red-600">Missing Materials:</h4>
                                <ul class="list-disc list-inside text-sm text-gray-600 mt-1">
                                    ${data.missing_materials.map(mat => `<li>${mat}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="flex justify-end mt-4">
                        <button type="button" class="close-modal px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Add close event listeners
        modal.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.remove();
            });
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        return modal;
    }

    showCompletionDetails(details) {
        // Create a details modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[9998]';
        modal.innerHTML = `
            <div class="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Completion Details</h3>
                        <button type="button" class="close-details text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="max-h-96 overflow-y-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Work Order</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Completed</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Invoice Created</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Message</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                ${details.map(detail => `
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${detail.work_order_number}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 py-1 rounded ${detail.completed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                                ${detail.completed ? 'Yes' : 'No'}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 py-1 rounded ${detail.invoice_created ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                                                ${detail.invoice_created ? 'Yes' : 'No'}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">${detail.message}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="flex justify-end mt-4">
                        <button type="button" class="close-details px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Add close event listeners
        modal.querySelectorAll('.close-details').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.remove();
            });
        });
        
        document.body.appendChild(modal);
    }

    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
               document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showLoading(message) {
        this.hideNotifications();
        const notification = document.createElement('div');
        notification.id = 'loading-notification';
        notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded shadow-lg z-[99999]';
        notification.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${message}`;
        document.body.appendChild(notification);
    }

    hideLoading() {
        const loading = document.getElementById('loading-notification');
        if (loading) loading.remove();
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showWarning(message) {
        this.showNotification(message, 'warning');
    }

    showNotification(message, type = 'info') {
        this.hideNotifications();
        
        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 ${colors[type]} text-white px-4 py-2 rounded shadow-lg z-[99999] max-w-md`;
        notification.innerHTML = `
            <i class="${icons[type]} mr-2"></i>
            <span style="white-space: pre-line;">${message}</span>
            <button type="button" class="ml-2 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    hideNotifications() {
        document.querySelectorAll('#loading-notification, .fixed.top-4.right-4').forEach(el => {
            if (el.classList.contains('bg-blue-500') || 
                el.classList.contains('bg-green-500') || 
                el.classList.contains('bg-red-500') || 
                el.classList.contains('bg-yellow-500')) {
                el.remove();
            }
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.workOrderAutoCompletion = new WorkOrderAutoCompletion();
}); 