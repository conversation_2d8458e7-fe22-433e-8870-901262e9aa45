// Comprehensive Deduplication Script for Work Order Form
// Prevents duplicate operations and spare parts from being added

console.log('Loading work order deduplication script...');

// Global tracking to prevent duplicate auto-selections
window.autoSelectionState = {
    operationsAdded: new Set(),
    partsAdded: new Set(),
    isPeriodicMaintenanceProcessed: false,
    isAutoSelectionInProgress: false
};

// Function to clear auto-selection state when needed
function clearAutoSelectionState() {
    console.log('Clearing auto-selection state');
    window.autoSelectionState = {
        operationsAdded: new Set(),
        partsAdded: new Set(),
        isPeriodicMaintenanceProcessed: false,
        isAutoSelectionInProgress: false
    };
}

// Enhanced addMaintenanceOperation with deduplication
document.addEventListener('DOMContentLoaded', function() {
    // Wait for the original function to be defined
    setTimeout(function() {
        if (typeof window.addMaintenanceOperation === 'function') {
            const originalAddMaintenanceOperation = window.addMaintenanceOperation;
            
            window.addMaintenanceOperation = function(operation) {
                const operationsListEl = document.getElementById('operations-list');
                
                if (!operationsListEl) {
                    console.error('Operations list element not found');
                    return;
                }
                
                // Check for duplicates - prevent adding the same operation twice
                const existingOperations = operationsListEl.querySelectorAll('.operation-item');
                for (let existingOp of existingOperations) {
                    const existingSelect = existingOp.querySelector('[id^="operation_description_"]');
                    if (existingSelect && existingSelect.value === operation.name) {
                        console.log(`Operation '${operation.name}' already exists, skipping duplicate`);
                        return; // Skip adding duplicate
                    }
                }
                
                // Track this operation as added
                window.autoSelectionState.operationsAdded.add(operation.name);
                
                // Call original function
                return originalAddMaintenanceOperation(operation);
            };
            
            console.log('Enhanced addMaintenanceOperation with deduplication');
        }
    }, 1000);
});

// Enhanced addPartToList with better deduplication
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        if (typeof window.addPartToList === 'function') {
            const originalAddPartToList = window.addPartToList;
            
            window.addPartToList = function(id, name, quantity, unit, price) {
                // Check if this exact part (by name and price) was already auto-added
                const partKey = `${name}_${price}`;
                if (window.autoSelectionState.partsAdded.has(partKey)) {
                    console.log(`Part '${name}' already auto-selected, skipping duplicate`);
                    return;
                }
                
                // Also check if part already exists in the DOM
                const sparePartsList = document.getElementById('spare-parts-list');
                if (sparePartsList) {
                    const existingParts = sparePartsList.querySelectorAll('.part-name');
                    for (let existingPart of existingParts) {
                        if (existingPart.textContent.trim() === name.trim()) {
                            console.log(`Part '${name}' already exists in DOM, skipping duplicate`);
                            return;
                        }
                    }
                }
                
                // Track this part as added
                window.autoSelectionState.partsAdded.add(partKey);
                
                // Call original function
                return originalAddPartToList(id, name, quantity, unit, price);
            };
            
            console.log('Enhanced addPartToList with deduplication');
        }
    }, 1000);
});

// Enhanced autoSelectPeriodicMaintenanceParts with duplicate prevention
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        if (typeof window.autoSelectPeriodicMaintenanceParts === 'function') {
            const originalAutoSelectPeriodicMaintenanceParts = window.autoSelectPeriodicMaintenanceParts;
            
            window.autoSelectPeriodicMaintenanceParts = function() {
                // Prevent multiple calls
                if (window.autoSelectionState.isPeriodicMaintenanceProcessed) {
                    console.log('Periodic maintenance parts already processed, skipping');
                    return;
                }
                
                // Mark as processed
                window.autoSelectionState.isPeriodicMaintenanceProcessed = true;
                
                // Call original function
                return originalAutoSelectPeriodicMaintenanceParts();
            };
            
            console.log('Enhanced autoSelectPeriodicMaintenanceParts with deduplication');
        }
    }, 1000);
});

// Enhanced autoSelectPartsByOperationName with duplicate prevention
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        if (typeof window.autoSelectPartsByOperationName === 'function') {
            const originalAutoSelectPartsByOperationName = window.autoSelectPartsByOperationName;
            
            window.autoSelectPartsByOperationName = function(operationName) {
                // Check if already in progress
                if (window.autoSelectionState.isAutoSelectionInProgress) {
                    console.log('Auto-selection already in progress, skipping');
                    return;
                }
                
                // Check if we already processed this operation
                const operationKey = `operation_${operationName}`;
                if (window.autoSelectionState.partsAdded.has(operationKey)) {
                    console.log(`Parts for operation '${operationName}' already selected, skipping`);
                    return;
                }
                
                // Mark operation as processed
                window.autoSelectionState.partsAdded.add(operationKey);
                
                // Set flag to prevent concurrent calls
                window.autoSelectionState.isAutoSelectionInProgress = true;
                
                // Call original function
                const result = originalAutoSelectPartsByOperationName(operationName);
                
                // Reset flag after a delay
                setTimeout(() => {
                    window.autoSelectionState.isAutoSelectionInProgress = false;
                }, 1000);
                
                return result;
            };
            
            console.log('Enhanced autoSelectPartsByOperationName with deduplication');
        }
    }, 1000);
});

// Clear state when moving between major steps
document.addEventListener('DOMContentLoaded', function() {
    // Monitor step changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                const target = mutation.target;
                if (target.classList.contains('form-step') && target.style.display !== 'none') {
                    // A step became visible
                    if (target.id === 'step-operations') {
                        // Clear state when operations step is shown
                        clearAutoSelectionState();
                        console.log('Cleared auto-selection state for operations step');
                    }
                }
            }
        });
    });
    
    // Observe all form steps
    const formSteps = document.querySelectorAll('.form-step');
    formSteps.forEach(step => {
        observer.observe(step, { attributes: true, attributeFilter: ['style'] });
    });
});

// Prevent duplicate default operation additions
document.addEventListener('DOMContentLoaded', function() {
    let periodicMaintenanceProcessed = false;
    
    // Override the specific function that adds default operations
    setTimeout(function() {
        // Look for the function that handles default periodic operations
        const originalFunctions = [];
        
        // Monitor for when default operations are about to be added
        if (window.fetchScheduledOperations) {
            const originalFetchScheduledOperations = window.fetchScheduledOperations;
            window.fetchScheduledOperations = function(...args) {
                if (periodicMaintenanceProcessed) {
                    console.log('Default periodic operations already processed, skipping');
                    return;
                }
                periodicMaintenanceProcessed = true;
                return originalFetchScheduledOperations.apply(this, args);
            };
        }
        
    }, 2000);
});

// Add button click protection to prevent multiple rapid clicks
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        // Protect next step button
        const nextButton = document.getElementById('next-step-btn');
        if (nextButton) {
            let isProcessing = false;
            
            nextButton.addEventListener('click', function(e) {
                if (isProcessing) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Button click prevented - already processing');
                    return false;
                }
                
                isProcessing = true;
                setTimeout(() => {
                    isProcessing = false;
                }, 2000);
            }, true); // Use capture phase
        }
        
        // Protect operation category dropdown
        const operationCategory = document.getElementById('operation_category');
        if (operationCategory) {
            let lastChangeTime = 0;
            
            operationCategory.addEventListener('change', function(e) {
                const now = Date.now();
                if (now - lastChangeTime < 1000) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Category change prevented - too rapid');
                    return false;
                }
                lastChangeTime = now;
            }, true);
        }
        
        console.log('Button protection applied');
    }, 1000);
});

console.log('Work order deduplication script loaded successfully'); 