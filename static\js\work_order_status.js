// Work Order Status Management
class WorkOrderStatusManager {
    constructor() {
        this.modal = null;
        this.currentWorkOrderId = null;
        this.init();
    }

    init() {
        // Initialize status change buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('status-change-btn')) {
                e.preventDefault();
                const workOrderId = e.target.dataset.workOrderId;
                const currentStatus = e.target.dataset.currentStatus;
                this.showStatusChangeModal(workOrderId, currentStatus);
            }
        });

        // Initialize technician assignment buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('assign-technician-btn')) {
                e.preventDefault();
                const workOrderId = e.target.dataset.workOrderId;
                this.showTechnicianAssignmentModal(workOrderId);
            }
        });
    }

    showStatusChangeModal(workOrderId, currentStatus) {
        this.currentWorkOrderId = workOrderId;
        
        // Create modal HTML
        const modalHtml = `
            <div id="statusChangeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[9998]">
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">تغيير حالة أمر العمل</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="workOrderStatusManager.closeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        
                        <form id="statusChangeForm" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الحالة الحالية</label>
                                <div class="p-2 bg-gray-100 rounded text-sm">${this.getStatusLabel(currentStatus)}</div>
                            </div>
                            
                            <div>
                                <label for="newStatus" class="block text-sm font-medium text-gray-700 mb-2">الحالة الجديدة</label>
                                <select id="newStatus" name="new_status" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                                    <option value="">اختر الحالة الجديدة</option>
                                    ${this.getStatusOptions(currentStatus)}
                                </select>
                            </div>
                            
                            <div id="technicianSelection" class="hidden">
                                <label for="assignedTechnician" class="block text-sm font-medium text-gray-700 mb-2">تعيين فني</label>
                                <select id="assignedTechnician" name="assigned_technician" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">اختر فني</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="statusNotes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                                <textarea id="statusNotes" name="notes" rows="3" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="أضف ملاحظات حول تغيير الحالة..."></textarea>
                            </div>
                            
                            <div class="flex justify-end space-x-2 space-x-reverse">
                                <button type="button" onclick="workOrderStatusManager.closeModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                    إلغاء
                                </button>
                                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    تحديث الحالة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        // Add modal to DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById('statusChangeModal');

        // Setup form submission
        document.getElementById('statusChangeForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitStatusChange();
        });

        // Setup status change handler
        document.getElementById('newStatus').addEventListener('change', (e) => {
            this.handleStatusChange(e.target.value);
        });

        // Load technicians
        this.loadTechnicians();
    }

    showTechnicianAssignmentModal(workOrderId) {
        this.currentWorkOrderId = workOrderId;
        
        const modalHtml = `
            <div id="technicianAssignModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[9998]">
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">تعيين فني</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="workOrderStatusManager.closeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        
                        <form id="technicianAssignForm" class="space-y-4">
                            <div>
                                <label for="technicianSelect" class="block text-sm font-medium text-gray-700 mb-2">اختر فني</label>
                                <select id="technicianSelect" name="assigned_technician" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                                    <option value="">اختر فني</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="assignmentNotes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات التعيين</label>
                                <textarea id="assignmentNotes" name="notes" rows="3" class="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="أضف ملاحظات حول تعيين الفني..."></textarea>
                            </div>
                            
                            <div class="flex justify-end space-x-2 space-x-reverse">
                                <button type="button" onclick="workOrderStatusManager.closeModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                    إلغاء
                                </button>
                                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    تعيين الفني
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById('technicianAssignModal');

        document.getElementById('technicianAssignForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitTechnicianAssignment();
        });

        this.loadTechnicians('technicianSelect');
    }

    getStatusOptions(currentStatus) {
        const statusTransitions = {
            'planned': ['in_progress', 'on_hold', 'cancelled'],
            'in_progress': ['completed', 'on_hold', 'cancelled'],
            'on_hold': ['in_progress', 'cancelled'],
            'completed': [],
            'cancelled': []
        };

        const statusLabels = {
            'planned': 'استقبال',
            'in_progress': 'قيد التنفيذ',
            'on_hold': 'معلق',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        };

        const allowedTransitions = statusTransitions[currentStatus] || [];
        return allowedTransitions.map(status => 
            `<option value="${status}">${statusLabels[status]}</option>`
        ).join('');
    }

    getStatusLabel(status) {
        const statusLabels = {
            'planned': 'استقبال',
            'in_progress': 'قيد التنفيذ',
            'on_hold': 'معلق',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        };
        return statusLabels[status] || status;
    }

    handleStatusChange(newStatus) {
        const technicianSelection = document.getElementById('technicianSelection');
        
        // Show technician selection for in_progress status
        if (newStatus === 'in_progress') {
            technicianSelection.classList.remove('hidden');
            document.getElementById('assignedTechnician').required = true;
        } else {
            technicianSelection.classList.add('hidden');
            document.getElementById('assignedTechnician').required = false;
        }
    }

    async loadTechnicians(selectId = 'assignedTechnician') {
        try {
            const response = await fetch('/work_orders/api/technicians/');
            const data = await response.json();
            
            const select = document.getElementById(selectId);
            if (select && data.success) {
                // Clear existing options except the first one
                select.innerHTML = '<option value="">اختر فني</option>';
                
                data.technicians.forEach(tech => {
                    const option = document.createElement('option');
                    option.value = tech.id;
                    option.textContent = tech.name;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading technicians:', error);
        }
    }

    async submitStatusChange() {
        const form = document.getElementById('statusChangeForm');
        const formData = new FormData(form);
        
        try {
            const response = await fetch(`/work_orders/${this.currentWorkOrderId}/change-status/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('تم تحديث حالة أمر العمل بنجاح', 'success');
                this.closeModal();
                // Reload the page or update the UI
                window.location.reload();
            } else {
                this.showNotification(data.message || 'حدث خطأ أثناء تحديث الحالة', 'error');
            }
        } catch (error) {
            console.error('Error updating status:', error);
            this.showNotification('حدث خطأ أثناء تحديث الحالة', 'error');
        }
    }

    async submitTechnicianAssignment() {
        const form = document.getElementById('technicianAssignForm');
        const formData = new FormData(form);
        
        try {
            const response = await fetch(`/work_orders/${this.currentWorkOrderId}/assign-technician/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('تم تعيين الفني بنجاح', 'success');
                this.closeModal();
                window.location.reload();
            } else {
                this.showNotification(data.message || 'حدث خطأ أثناء تعيين الفني', 'error');
            }
        } catch (error) {
            console.error('Error assigning technician:', error);
            this.showNotification('حدث خطأ أثناء تعيين الفني', 'error');
        }
    }

    closeModal() {
        if (this.modal) {
            this.modal.remove();
            this.modal = null;
        }
        
        // Also remove any other modals
        const modals = document.querySelectorAll('#statusChangeModal, #technicianAssignModal');
        modals.forEach(modal => modal.remove());
    }

    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
               document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-[99999] p-4 rounded-md shadow-lg ${
            type === 'success' ? 'bg-green-500 text-white' : 
            type === 'error' ? 'bg-red-500 text-white' : 
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.workOrderStatusManager = new WorkOrderStatusManager();
}); 