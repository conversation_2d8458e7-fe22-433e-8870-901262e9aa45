{% load i18n %}

<!-- Example of role-based menu visibility -->
<nav class="bg-white shadow-lg">
    <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between h-16">
            <div class="flex">
                <!-- Main navigation -->
                <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                    
                    <!-- Always visible for authenticated users -->
                    <a href="{% url 'core:dashboard' %}" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        {% trans "Dashboard" %}
                    </a>
                    
                    <!-- Inventory module - only show if user has access -->
                    {% if user_permissions.can_access_inventory %}
                    <a href="{% url 'inventory:item_list' %}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-boxes mr-2"></i>
                        {% trans "Inventory" %}
                    </a>
                    {% endif %}
                    
                    <!-- Warehouse module - only show if user has access -->
                    {% if user_permissions.can_access_warehouse %}
                    <a href="{% url 'warehouse:location_list' %}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-warehouse mr-2"></i>
                        {% trans "Warehouse" %}
                    </a>
                    {% endif %}
                    
                    <!-- Sales module - only show if user has access -->
                    {% if user_permissions.can_access_sales %}
                    <a href="{% url 'sales:sale_list' %}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        {% trans "Sales" %}
                    </a>
                    {% endif %}
                    
                    <!-- Purchases module - only show if user has access -->
                    {% if user_permissions.can_access_purchases %}
                    <a href="{% url 'purchases:purchase_order_list' %}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-shopping-bag mr-2"></i>
                        {% trans "Purchases" %}
                    </a>
                    {% endif %}
                    
                    <!-- Work Orders module - only show if user has access -->
                    {% if user_permissions.can_access_work_orders %}
                    <a href="{% url 'work_orders:work_order_list' %}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-wrench mr-2"></i>
                        {% trans "Work Orders" %}
                    </a>
                    {% endif %}
                    
                    <!-- Reports module - only show if user has access -->
                    {% if user_permissions.can_access_reports %}
                    <a href="{% url 'reports:dashboard' %}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-chart-bar mr-2"></i>
                        {% trans "Reports" %}
                    </a>
                    {% endif %}
                    
                    <!-- Setup module - only show if user has access -->
                    {% if user_permissions.can_access_setup %}
                    <a href="{% url 'setup:customer_list' %}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-cog mr-2"></i>
                        {% trans "Setup" %}
                    </a>
                    {% endif %}
                    
                    <!-- Settings module - only show if user has access (usually admin only) -->
                    {% if user_permissions.can_access_settings %}
                    <a href="{% url 'settings:general' %}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                        <i class="fas fa-tools mr-2"></i>
                        {% trans "Settings" %}
                    </a>
                    {% endif %}
                    
                </div>
            </div>
            
            <!-- User menu -->
            <div class="hidden sm:ml-6 sm:flex sm:items-center">
                <!-- User role indicator -->
                {% if user_primary_role %}
                <span class="text-xs text-gray-500 mr-4">
                    {% trans "Role" %}: {{ user_primary_role.name }}
                </span>
                {% endif %}
                
                <!-- User profile dropdown -->
                <div class="ml-3 relative">
                    <div>
                        <button type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                            <span class="sr-only">{% trans "Open user menu" %}</span>
                            <div class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
                                <span class="text-white text-sm font-medium">{{ user.first_name.0|default:user.username.0|upper }}</span>
                            </div>
                        </button>
                    </div>
                    
                    <!-- Dropdown menu -->
                    <div class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
                        <div class="py-1" role="none">
                            <a href="{% url 'user_profile' %}" class="text-gray-700 block px-4 py-2 text-sm" role="menuitem" tabindex="-1">
                                {% trans "Your Profile" %}
                            </a>
                            <a href="{% url 'logout' %}" class="text-gray-700 block px-4 py-2 text-sm" role="menuitem" tabindex="-1">
                                {% trans "Sign out" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Example of role-based content sections -->
<div class="container mx-auto mt-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        <!-- Inventory Quick Stats -->
        {% if user_permissions.can_access_inventory %}
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-boxes text-gray-400 text-xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                {% trans "Total Items" %}
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                <!-- This would be populated with actual data -->
                                1,234
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'inventory:item_list' %}" class="font-medium text-indigo-600 hover:text-indigo-500">
                        {% trans "View all items" %}
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Sales Quick Stats -->
        {% if user_permissions.can_access_sales %}
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-shopping-cart text-gray-400 text-xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                {% trans "Monthly Sales" %}
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                <!-- This would be populated with actual data -->
                                $12,345
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'sales:sale_list' %}" class="font-medium text-indigo-600 hover:text-indigo-500">
                        {% trans "View all sales" %}
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Work Orders Quick Stats -->
        {% if user_permissions.can_access_work_orders %}
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-wrench text-gray-400 text-xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                {% trans "Active Work Orders" %}
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                <!-- This would be populated with actual data -->
                                23
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="{% url 'work_orders:work_order_list' %}" class="font-medium text-indigo-600 hover:text-indigo-500">
                        {% trans "View all work orders" %}
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div> 