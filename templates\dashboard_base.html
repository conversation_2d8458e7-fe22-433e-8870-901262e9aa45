{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load waffle_tags %}

{% block title %}{% trans "لوحة التحكم" %} - {{ block.super }}{% endblock %}

{% block body_class %}dashboard-layout{% endblock %}

{% block extra_css %}
<!-- Dashboard specific styles -->
<style>
    /* Dashboard layout improvements */
    .dashboard-sidebar {
        transition: transform 0.3s ease-in-out;
        box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    }
    
    .dashboard-main {
        min-height: calc(100vh - 8rem);
    }
    
    /* Notification styles */
    .notification {
        animation: slideInDown 0.3s ease-out;
    }
    
    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    /* Card hover effects */
    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    /* Sidebar menu animations */
    .sidebar-menu li a {
        transition: all 0.2s ease;
    }
    
    .sidebar-menu li a:hover {
        background-color: #f3f4f6;
        padding-right: 1.5rem;
    }
    
    /* Active menu item */
    .sidebar-menu li a.active {
        background-color: #3b82f6;
        color: white;
        border-radius: 0.5rem;
    }
    
    /* Mobile sidebar overlay */
    .sidebar-overlay {
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s ease;
    }
    
    /* Loading states */
    .loading-spinner {
        border: 2px solid #f3f4f6;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        width: 1rem;
        height: 1rem;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Form enhancements */
    .form-floating {
        position: relative;
    }
    
    .form-floating input:focus + label,
    .form-floating input:not(:placeholder-shown) + label {
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #3b82f6;
    }
    
    /* Dashboard statistics cards */
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(30px, -30px);
    }
    
    /* Quick action buttons */
    .quick-action {
        transition: all 0.2s ease;
    }
    
    .quick-action:hover {
        transform: scale(1.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="flex h-screen bg-gray-50">
    <!-- Sidebar -->
    <div id="sidebar" class="dashboard-sidebar fixed inset-y-0 right-0 z-50 w-64 bg-white border-l border-gray-200 transform lg:translate-x-0 lg:static lg:inset-0">
        <!-- Sidebar header -->
        <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <div class="flex items-center">
                <i class="fas fa-tachometer-alt text-blue-600 text-xl mr-2"></i>
                <h2 class="text-lg font-semibold text-gray-800">{% trans "لوحة التحكم" %}</h2>
            </div>
            <button id="sidebar-close" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <!-- User info -->
        {% if user.is_authenticated %}
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    {{ user.first_name|first|default:user.username|first|upper }}
                </div>
                <div class="mr-3">
                    <p class="text-sm font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</p>
                    <p class="text-xs text-gray-500">{{ user.email|default:"" }}</p>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Navigation menu -->
        <nav class="mt-4 px-4">
            <ul class="sidebar-menu space-y-2">
                <!-- Dashboard -->
                <li>
                    <a href="{% url 'core:main_dashboard' %}" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
                        <i class="fas fa-home ml-3 text-gray-400"></i>
                        {% trans "الرئيسية" %}
                    </a>
                </li>
                
                <!-- Work Orders -->
                {% flag "work_orders_module" %}
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="work-orders-menu">
                        <div class="flex items-center">
                            <i class="fas fa-wrench ml-3 text-gray-400"></i>
                            {% trans "أوامر العمل" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="work-orders-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="{% url 'work_orders:work_order_list' %}" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "قائمة أوامر العمل" %}</a></li>
                        <li><a href="{% url 'work_orders:work_order_create' %}" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "إنشاء أمر عمل جديد" %}</a></li>
                    </ul>
                </li>
                {% endflag %}
                
                <!-- Inventory -->
                {% flag "inventory_module" %}
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="inventory-menu">
                        <div class="flex items-center">
                            <i class="fas fa-boxes ml-3 text-gray-400"></i>
                            {% trans "المخزون" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="inventory-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "قائمة الأصناف" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "حركة المخزون" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "التوافق مع المركبات" %}</a></li>
                    </ul>
                </li>
                {% endflag %}
                
                <!-- Warehouse -->
                {% flag "warehouse_module" %}
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="warehouse-menu">
                        <div class="flex items-center">
                            <i class="fas fa-warehouse ml-3 text-gray-400"></i>
                            {% trans "المستودعات" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="warehouse-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "لوحة المستودعات" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "أوامر النقل" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "الاستلام والشحن" %}</a></li>
                    </ul>
                </li>
                {% endflag %}
                
                <!-- Sales -->
                {% flag "sales_module" %}
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="sales-menu">
                        <div class="flex items-center">
                            <i class="fas fa-shopping-cart ml-3 text-gray-400"></i>
                            {% trans "المبيعات" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="sales-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "نقطة البيع" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "أوامر البيع" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "المرتجعات" %}</a></li>
                    </ul>
                </li>
                {% endflag %}
                
                <!-- Purchases -->
                {% flag "purchases_module" %}
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="purchases-menu">
                        <div class="flex items-center">
                            <i class="fas fa-shopping-bag ml-3 text-gray-400"></i>
                            {% trans "المشتريات" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="purchases-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "الموردين" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "أوامر الشراء" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "الاستلام" %}</a></li>
                    </ul>
                </li>
                {% endflag %}
                
                <!-- Billing -->
                {% flag "billing_module" %}
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="billing-menu">
                        <div class="flex items-center">
                            <i class="fas fa-file-invoice-dollar ml-3 text-gray-400"></i>
                            {% trans "الفواتير والدفع" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="billing-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "الفواتير" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "المدفوعات" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "تصنيف العملاء" %}</a></li>
                    </ul>
                </li>
                {% endflag %}
                
                <!-- Reports -->
                {% flag "reports_module" %}
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="reports-menu">
                        <div class="flex items-center">
                            <i class="fas fa-chart-bar ml-3 text-gray-400"></i>
                            {% trans "التقارير" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="reports-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "لوحة التحليلات" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "تقارير المبيعات" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "تقارير المخزون" %}</a></li>
                    </ul>
                </li>
                {% endflag %}
                
                <!-- Setup -->
                {% flag "setup_module" %}
                <li>
                    <button class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100" data-toggle="submenu" data-target="setup-menu">
                        <div class="flex items-center">
                            <i class="fas fa-cogs ml-3 text-gray-400"></i>
                            {% trans "الإعدادات" %}
                        </div>
                        <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    </button>
                    <ul id="setup-menu" class="submenu hidden mt-2 mr-8 space-y-1">
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "العملاء" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "المركبات" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "مراكز الخدمة" %}</a></li>
                        <li><a href="#" class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100">{% trans "الامتيازات" %}</a></li>
                    </ul>
                </li>
                {% endflag %}
                
                <!-- User Roles -->
                {% flag "user_roles_module" %}
                <li>
                    <a href="#" class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100">
                        <i class="fas fa-users-cog ml-3 text-gray-400"></i>
                        {% trans "إدارة المستخدمين" %}
                    </a>
                </li>
                {% endflag %}
            </ul>
        </nav>
        
        <!-- Quick actions -->
        <div class="absolute bottom-4 left-4 right-4">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 class="text-sm font-medium text-blue-800 mb-2">{% trans "الإجراءات السريعة" %}</h4>
                <div class="grid grid-cols-2 gap-2">
                    <button class="quick-action bg-blue-600 text-white text-xs px-3 py-2 rounded-md hover:bg-blue-700 flex items-center justify-center">
                        <i class="fas fa-plus text-xs ml-1"></i>
                        {% trans "أمر عمل" %}
                    </button>
                    <button class="quick-action bg-green-600 text-white text-xs px-3 py-2 rounded-md hover:bg-green-700 flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-xs ml-1"></i>
                        {% trans "بيع" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sidebar overlay for mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay fixed inset-0 z-40 lg:hidden hidden" onclick="toggleSidebar()"></div>
    
    <!-- Main content -->
    <div class="dashboard-main flex-1 flex flex-col overflow-hidden lg:mr-64">
        <!-- Top bar -->
        <header class="bg-white border-b border-gray-200 px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <button id="sidebar-toggle" class="lg:hidden mr-4 p-2 rounded-md text-gray-400 hover:text-gray-600">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-semibold text-gray-900">{% block page_title %}{% trans "لوحة التحكم" %}{% endblock %}</h1>
                        <p class="text-sm text-gray-500">{% block page_subtitle %}{% trans "مرحباً بك في نظام إدارة الامتياز" %}{% endblock %}</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="p-2 text-gray-400 hover:text-gray-600 relative">
                            <i class="fas fa-bell text-lg"></i>
                            <span id="notification-badge" class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center hidden">3</span>
                        </button>
                    </div>
                    
                    <!-- Search -->
                    <div class="hidden md:block">
                        <div class="relative">
                            <input type="text" placeholder="{% trans 'بحث سريع...' %}" class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page content -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
            <!-- Notifications container -->
            <div id="notifications-container" class="fixed top-20 left-4 right-4 z-[9999] space-y-2"></div>
            
            {% block dashboard_content %}
            <!-- Default dashboard content -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- Statistics cards will be populated by JavaScript -->
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Recent activities and other widgets -->
            </div>
            {% endblock %}
        </main>
    </div>
</div>

<!-- Global notification system -->
<script>
window.notificationSystem = {
    show: function(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications-container');
        const id = 'notification-' + Date.now();
        
        const typeClasses = {
            success: 'bg-green-500 border-green-600',
            error: 'bg-red-500 border-red-600',
            warning: 'bg-yellow-500 border-yellow-600',
            info: 'bg-blue-500 border-blue-600'
        };
        
        const typeIcons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        const notification = document.createElement('div');
        notification.id = id;
        notification.className = `notification flex items-center p-4 rounded-lg shadow-lg text-white border-r-4 ${typeClasses[type]}`;
        notification.innerHTML = `
            <i class="${typeIcons[type]} ml-3"></i>
            <span class="flex-1">${message}</span>
            <button onclick="window.notificationSystem.hide('${id}')" class="mr-2 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(notification);
        
        if (duration > 0) {
            setTimeout(() => {
                window.notificationSystem.hide(id);
            }, duration);
        }
    },
    
    hide: function(id) {
        const element = document.getElementById(id);
        if (element) {
            element.style.animation = 'slideOutUp 0.3s ease-out';
            setTimeout(() => {
                element.remove();
            }, 300);
        }
    }
};

// Global notification function for backwards compatibility
window.notify = window.notificationSystem.show;

// Sidebar functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');
    
    sidebar.classList.toggle('-translate-x-full');
    overlay.classList.toggle('hidden');
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarClose = document.getElementById('sidebar-close');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }
    
    if (sidebarClose) {
        sidebarClose.addEventListener('click', toggleSidebar);
    }
    
    // Submenu toggles
    const submenuToggles = document.querySelectorAll('[data-toggle="submenu"]');
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const target = document.getElementById(targetId);
            const chevron = this.querySelector('.fa-chevron-down');
            
            if (target) {
                target.classList.toggle('hidden');
                chevron.classList.toggle('rotate-180');
            }
        });
    });
    
    // Set active menu item based on current URL
    const currentPath = window.location.pathname;
    const menuLinks = document.querySelectorAll('.sidebar-menu a');
    
    menuLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
            
            // Open parent submenu if exists
            const submenu = link.closest('.submenu');
            if (submenu) {
                submenu.classList.remove('hidden');
                const toggle = document.querySelector(`[data-target="${submenu.id}"]`);
                if (toggle) {
                    toggle.querySelector('.fa-chevron-down').classList.add('rotate-180');
                }
            }
        }
    });
    
    // Listen for notification events
    window.addEventListener('showNotification', function(event) {
        const { message, type, duration } = event.detail;
        window.notificationSystem.show(message, type, duration);
    });
});
</script>

<!-- Additional dashboard scripts -->
{% block dashboard_scripts %}{% endblock %}
{% endblock %} 