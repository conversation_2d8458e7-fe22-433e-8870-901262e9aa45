{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "المشتريات" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Purchase Orders -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-amber-100 text-amber-500">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "إجمالي طلبات الشراء" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ total_orders|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Pending Orders -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "طلبات قيد الانتظار" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ pending_orders|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Received Orders -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "طلبات مستلمة" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ received_orders|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Suppliers Count -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "عدد الموردين" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ suppliers_count|default:"0" }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "إجراءات سريعة" %}</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{% url 'purchases:purchase_order_create' %}" class="bg-amber-100 hover:bg-amber-200 text-amber-700 py-3 px-4 rounded-lg text-center transition duration-300">
                    <i class="fas fa-plus-circle mb-2 text-2xl"></i>
                    <p>{% trans "طلب شراء جديد" %}</p>
                </a>
                <a href="{% url 'purchases:supplier_create' %}" class="bg-blue-100 hover:bg-blue-200 text-blue-700 py-3 px-4 rounded-lg text-center transition duration-300">
                    <i class="fas fa-user-plus mb-2 text-2xl"></i>
                    <p>{% trans "مورد جديد" %}</p>
                </a>
                <a href="{% url 'purchases:purchase_order_list' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg text-center transition duration-300">
                    <i class="fas fa-list mb-2 text-2xl"></i>
                    <p>{% trans "جميع طلبات الشراء" %}</p>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Recent Purchase Orders -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "أحدث طلبات الشراء" %}</h2>
            <a href="{% url 'purchases:purchase_order_list' %}" class="text-amber-600 hover:text-amber-800 text-sm">
                {% trans "عرض الكل" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
            </a>
        </div>
        {% if recent_orders %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم الطلب" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المورد" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "التاريخ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الحالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in recent_orders %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.po_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.supplier.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ order.created_at|date:"d/m/Y" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if order.status == 'pending' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {% trans "قيد الانتظار" %}
                                        </span>
                                    {% elif order.status == 'received' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            {% trans "مستلم" %}
                                        </span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            {% trans "ملغي" %}
                                        </span>
                                    {% else %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {{ order.get_status_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'purchases:purchase_order_detail' order.id %}" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'purchases:purchase_order_update' order.id %}" class="text-amber-600 hover:text-amber-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا توجد طلبات شراء حديثة" %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 