{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تفاصيل طلب الشراء" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">{% trans "تفاصيل طلب الشراء" %}</h1>
            <p class="text-gray-600">{{ purchase_order.po_number }}</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
            <a href="{% url 'purchases:purchase_order_update' purchase_order.id %}" class="bg-amber-500 hover:bg-amber-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "تعديل" %}
            </a>
            <a href="{% url 'purchases:purchase_order_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "العودة" %}
            </a>
        </div>
    </div>
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    <!-- Purchase Order Info -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Order Details -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "تفاصيل الطلب" %}</h2>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 gap-x-4 gap-y-5">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "رقم الطلب" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ purchase_order.po_number }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "تاريخ الإنشاء" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ purchase_order.created_at|date:"d/m/Y H:i" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "تاريخ التسليم المتوقع" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if purchase_order.expected_delivery_date %}
                                {{ purchase_order.expected_delivery_date|date:"d/m/Y" }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "شروط الدفع" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if purchase_order.payment_terms %}
                                {{ purchase_order.payment_terms }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "الحالة" %}</dt>
                        <dd class="mt-1 text-sm">
                            {% if purchase_order.status == 'pending' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    {% trans "قيد الانتظار" %}
                                </span>
                            {% elif purchase_order.status == 'ordered' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {% trans "تم الطلب" %}
                                </span>
                            {% elif purchase_order.status == 'received' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {% trans "مستلم" %}
                                </span>
                            {% elif purchase_order.status == 'cancelled' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    {% trans "ملغي" %}
                                </span>
                            {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ purchase_order.get_status_display }}
                                </span>
                            {% endif %}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        
        <!-- Supplier Info -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "معلومات المورد" %}</h2>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 gap-x-4 gap-y-5">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "اسم المورد" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ purchase_order.supplier.name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "شخص الاتصال" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if purchase_order.supplier.contact_person %}
                                {{ purchase_order.supplier.contact_person }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "رقم الهاتف" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if purchase_order.supplier.phone %}
                                {{ purchase_order.supplier.phone }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "البريد الإلكتروني" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if purchase_order.supplier.email %}
                                <a href="mailto:{{ purchase_order.supplier.email }}" class="text-blue-600 hover:text-blue-800">
                                    {{ purchase_order.supplier.email }}
                                </a>
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "العنوان" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if purchase_order.supplier.address %}
                                {{ purchase_order.supplier.address }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        
        <!-- Notes -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "ملاحظات" %}</h2>
            </div>
            <div class="p-6">
                {% if purchase_order.notes %}
                    <p class="text-sm text-gray-900">{{ purchase_order.notes|linebreaks }}</p>
                {% else %}
                    <p class="text-sm text-gray-500">{% trans "لا توجد ملاحظات" %}</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Items Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "البنود" %}</h2>
            <!-- Add an "add item" button here if you want -->
        </div>
        
        {% if items %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الصنف" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الكمية" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "السعر" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجمالي" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "ملاحظات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in items %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ item.item.name }}</div>
                                    <div class="text-xs text-gray-500">{{ item.item.sku }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ item.quantity }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ item.unit_price }} {% trans "ج.م" %}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ item.total_price }} {% trans "ج.م" %}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">{{ item.notes|default:"-" }}</div>
                                </td>
                            </tr>
                        {% endfor %}
                        <!-- Summary row -->
                        <tr class="bg-gray-50">
                            <td colspan="3" class="px-6 py-4 text-right whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{% trans "الإجمالي" %}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-gray-900">
                                    {% if purchase_order.total_amount %}
                                        {{ purchase_order.total_amount }} {% trans "ج.م" %}
                                    {% else %}
                                        {% with total=0 %}
                                            {% for item in items %}
                                                {% with item_total=item.quantity|floatformat:"0"|floatformat:0|add:"0" %}
                                                    {% with total=total|add:item_total %}
                                                    {% endwith %}
                                                {% endwith %}
                                            {% endfor %}
                                            {{ total }} {% trans "ج.م" %}
                                        {% endwith %}
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا توجد بنود في هذا الطلب" %}
            </div>
        {% endif %}
    </div>
    
    <!-- Status History (if available) -->
    {% if purchase_order.status_history %}
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "سجل الحالة" %}</h2>
            </div>
            <div class="p-6">
                <ul class="border-l border-gray-200 {% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    {% for history in purchase_order.status_history %}
                        <li class="relative pb-8">
                            <div class="absolute {% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} -ml-px w-3 h-3 bg-gray-200 rounded-full -translate-x-1/2 mt-1.5 border border-white"></div>
                            <div class="{% if LANGUAGE_CODE == 'ar' %}mr-6{% else %}ml-6{% endif %}">
                                <p class="text-sm text-gray-500">{{ history.timestamp|date:"d/m/Y H:i" }}</p>
                                <p class="mt-1 text-sm text-gray-900">{{ history.status_display }}</p>
                                {% if history.notes %}
                                    <p class="mt-1 text-sm text-gray-500">{{ history.notes }}</p>
                                {% endif %}
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %} 