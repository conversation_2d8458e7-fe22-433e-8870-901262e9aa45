{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}
    {% if object %}
        {% trans "تعديل مورد" %}
    {% else %}
        {% trans "مورد جديد" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">
                {% if object %}
                    {% trans "تعديل مورد" %}
                {% else %}
                    {% trans "مورد جديد" %}
                {% endif %}
            </h1>
            <p class="text-gray-600">
                {% if object %}
                    {{ object.name }}
                {% else %}
                    {% trans "إضافة مورد جديد" %}
                {% endif %}
            </p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{% url 'purchases:supplier_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "العودة" %}
            </a>
        </div>
    </div>
    
    <!-- Form Section -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "معلومات المورد" %}</h2>
        </div>
        <div class="p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Display form errors if any -->
                {% if form.errors %}
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                        <p class="font-bold">{% trans "يرجى تصحيح الأخطاء التالية:" %}</p>
                        <ul class="list-disc {% if LANGUAGE_CODE == 'ar' %}mr-5{% else %}ml-5{% endif %} mt-2">
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <li>{{ field.label }}: {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                            {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Name -->
                    <div>
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "اسم المورد" %} <span class="text-red-500">*</span>
                        </label>
                        {{ form.name }}
                        {% if form.name.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.name.help_text }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Contact Person -->
                    <div>
                        <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "شخص الاتصال" %}
                        </label>
                        {{ form.contact_person }}
                        {% if form.contact_person.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.contact_person.help_text }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Phone -->
                    <div>
                        <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "رقم الهاتف" %}
                        </label>
                        {{ form.phone }}
                        {% if form.phone.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.phone.help_text }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Email -->
                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "البريد الإلكتروني" %}
                        </label>
                        {{ form.email }}
                        {% if form.email.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.email.help_text }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Address -->
                <div>
                    <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {% trans "العنوان" %}
                    </label>
                    {{ form.address }}
                    {% if form.address.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.address.help_text }}</p>
                    {% endif %}
                </div>
                
                <!-- Notes -->
                <div>
                    <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {% trans "ملاحظات" %}
                    </label>
                    {{ form.notes }}
                    {% if form.notes.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.notes.help_text }}</p>
                    {% endif %}
                </div>
                
                <div class="pt-5 border-t border-gray-200 flex justify-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 rounded-lg">
                        {% if object %}
                            {% trans "حفظ التغييرات" %}
                        {% else %}
                            {% trans "إضافة المورد" %}
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    {% if object %}
    <!-- Purchase Orders Section -->
    <div class="mt-8 bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "طلبات الشراء" %}</h2>
        </div>
        
        {% if purchase_orders %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم الطلب" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "التاريخ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الحالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in purchase_orders %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.po_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ order.created_at|date:"d/m/Y" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if order.status == 'pending' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {% trans "قيد الانتظار" %}
                                        </span>
                                    {% elif order.status == 'ordered' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {% trans "تم الطلب" %}
                                        </span>
                                    {% elif order.status == 'received' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            {% trans "مستلم" %}
                                        </span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            {% trans "ملغي" %}
                                        </span>
                                    {% else %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {{ order.get_status_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'purchases:purchase_order_detail' order.id %}" class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا توجد طلبات شراء لهذا المورد" %}
            </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %} 