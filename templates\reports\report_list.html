{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "التقارير" %} | {% trans "نظام Aftersails" %}{% endblock %}

{% block extra_css %}
<style>
    /* Modern styling inspired by work order list */
    .reports-container {
        direction: rtl;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }

    /* Enhanced cards */
    .reports-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        border: 1px solid rgba(229, 231, 235, 0.8);
    }

    .reports-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* Report type cards */
    .report-type-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .report-type-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    .report-type-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
    }

    /* Inventory report variant */
    .report-type-card.inventory::before {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    /* Sales report variant */
    .report-type-card.sales::before {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    /* Icon styling */
    .report-icon {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        margin-bottom: 1rem;
    }

    .inventory-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    .sales-icon {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    }

    .dashboard-icon {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
    }

    /* Action buttons */
    .action-btn {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        gap: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
    }

    .btn-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    .btn-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
    }

    .btn-purple {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        color: white;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-available {
        background-color: #d1fae5;
        color: #065f46;
    }

    .status-missing {
        background-color: #fee2e2;
        color: #dc2626;
    }

    /* Warning banner */
    .warning-banner {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border-right: 4px solid #f59e0b;
        border-radius: 0.75rem;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(245, 158, 11, 0.1);
    }

    /* Empty state */
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6b7280;
    }

    .empty-state-icon {
        width: 4rem;
        height: 4rem;
        margin: 0 auto 1rem;
        color: #d1d5db;
    }

    /* List items */
    .report-item {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .report-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        border-color: #3b82f6;
    }

    .report-item:last-child {
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6 reports-container" dir="rtl">
    
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">{% trans "التقارير" %}</h1>
        <p class="text-gray-600 text-lg">{% trans "عرض وإدارة التقارير والبيانات التحليلية" %}</p>
    </div>
    
    {% if missing_tenant %}
        <div class="warning-banner mb-6">
            <div class="flex items-center gap-3">
                <svg class="w-6 h-6 text-yellow-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <div class="flex-1">
                    <p class="font-semibold text-yellow-800 mb-1">{% trans "معرف المستأجر مفقود" %}</p>
                    <p class="text-sm text-yellow-700">
                        {% trans "لم يتم العثور على معرف المستأجر. يرجى التأكد من تعيين رأس X-Tenant-ID بشكل صحيح." %}
                    </p>
                </div>
            </div>
        </div>
    {% endif %}
    
    <!-- Advanced Reporting Section -->
    {% if has_advanced_reporting %}
    <div class="reports-card p-6 mb-8">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800 flex items-center gap-3">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span>{% trans "التقارير المتقدمة" %}</span>
            </h2>
            
            <a href="{% url 'reports:dashboard_list' %}" class="action-btn btn-purple">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span>{% trans "لوحات المعلومات" %}</span>
            </a>
        </div>
        
        {% if reports %}
            <div class="space-y-4">
                {% for report in reports %}
                <div class="report-item">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-2">
                                <h3 class="text-lg font-bold text-gray-900">{{ report.name }}</h3>
                                <span class="status-badge status-available">
                                    {{ report.get_report_type_display }}
                                </span>
                            </div>
                            <p class="text-gray-600">{{ report.description }}</p>
                        </div>
                        <div class="flex gap-2">
                            <a href="{% url 'reports:report_detail' report.id %}" class="action-btn btn-primary text-xs">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <span>{% trans "عرض" %}</span>
                            </a>
                            <a href="{% url 'reports:execute_report' report.id %}" class="action-btn btn-success text-xs">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "تشغيل" %}</span>
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                {% if missing_tenant %}
                    <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <p class="text-gray-500 mb-2">{% trans "لا توجد تقارير متاحة بسبب فقدان معرف المستأجر." %}</p>
                    <p class="text-gray-500">{% trans "يرجى تعيين رأس X-Tenant-ID للوصول إلى التقارير." %}</p>
                {% else %}
                    <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-gray-500 mb-2">{% trans "لا توجد تقارير متاحة." %}</p>
                    <p class="text-gray-500">{% trans "ستظهر التقارير هنا بمجرد إنشائها." %}</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- Basic Reports -->
    <div class="reports-card p-6">
        <h2 class="mb-6 text-2xl font-bold text-gray-800 flex items-center gap-3">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span>{% trans "التقارير الأساسية" %}</span>
        </h2>
        <p class="text-gray-600 mb-6">{% trans "وصول سريع للتقارير الشائعة" %}</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Inventory Report -->
            <div class="report-type-card inventory">
                <div class="report-icon inventory-icon">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">{% trans "تقرير المخزون" %}</h3>
                    <p class="text-gray-600 mb-4">{% trans "حالة المخزون الأساسية والمقاييس" %}</p>
                    <a href="{% url 'reports:basic_inventory' %}" class="action-btn btn-success w-full justify-center">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>{% trans "عرض التقرير" %}</span>
                    </a>
                </div>
            </div>
            
            <!-- Sales Report -->
            <div class="report-type-card sales">
                <div class="report-icon sales-icon">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">{% trans "تقرير المبيعات" %}</h3>
                    <p class="text-gray-600 mb-4">{% trans "مقاييس المبيعات الأساسية والاتجاهات" %}</p>
                    <a href="{% url 'reports:basic_sales' %}" class="action-btn btn-warning w-full justify-center">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>{% trans "عرض التقرير" %}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 