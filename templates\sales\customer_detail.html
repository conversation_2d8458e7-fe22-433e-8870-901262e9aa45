{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ customer.name }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">{{ customer.name }}</h1>
            <p class="text-gray-600">{% trans "معلومات العميل" %}</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
            <a href="{% url 'sales:customer_update' customer.id %}" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "تعديل" %}
            </a>
            <a href="{% url 'sales:customer_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "العودة" %}
            </a>
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Customer Info -->
        <div class="bg-white shadow rounded-lg overflow-hidden md:col-span-2">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "معلومات أساسية" %}</h2>
            </div>
            <div class="p-6">
                <div class="flex items-center mb-6">
                    <div class="flex-shrink-0 h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                        <span class="text-blue-800 text-2xl font-bold">{{ customer.name|slice:":1" }}</span>
                    </div>
                    <div class="{% if LANGUAGE_CODE == 'ar' %}mr-6{% else %}ml-6{% endif %}">
                        <h3 class="text-xl font-bold text-gray-900">{{ customer.name }}</h3>
                        {% if customer.email %}
                            <p class="text-gray-600">{{ customer.email }}</p>
                        {% endif %}
                        {% if customer.phone %}
                            <p class="text-gray-600">{{ customer.phone }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-5">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "البريد الإلكتروني" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if customer.email %}
                                <a href="mailto:{{ customer.email }}" class="text-blue-600 hover:text-blue-800">
                                    {{ customer.email }}
                                </a>
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "رقم الهاتف" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if customer.phone %}
                                {{ customer.phone }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "العنوان" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if customer.address %}
                                {{ customer.address }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "تاريخ التسجيل" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ customer.created_at|date:"d/m/Y" }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "آخر تحديث" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ customer.updated_at|date:"d/m/Y H:i" }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "عدد الطلبات" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ customer.orders.count }}</dd>
                    </div>
                </dl>
                
                {% if customer.notes %}
                    <div class="mt-6 border-t border-gray-200 pt-6">
                        <h3 class="text-sm font-medium text-gray-500 mb-2">{% trans "ملاحظات" %}</h3>
                        <p class="text-sm text-gray-900">{{ customer.notes|linebreaks }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Customer Stats -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "إحصائيات" %}</h2>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <!-- Total Purchases -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-blue-700 mb-1">{% trans "إجمالي المشتريات" %}</div>
                        <div class="text-2xl font-bold text-blue-800">{{ total_purchases|default:"0" }} {% trans "ج.م" %}</div>
                    </div>
                    
                    <!-- Recent Purchase -->
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-green-700 mb-1">{% trans "آخر طلب" %}</div>
                        {% if last_order %}
                            <div class="text-lg font-bold text-green-800">{{ last_order.order_date|date:"d/m/Y" }}</div>
                            <div class="text-sm text-green-700">{{ last_order.order_number }}</div>
                        {% else %}
                            <div class="text-sm text-green-700">{% trans "لا يوجد" %}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Orders Status -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-gray-700 mb-3">{% trans "حالة الطلبات" %}</div>
                        {% if orders_stats %}
                            <div class="space-y-2">
                                {% if orders_stats.draft %}
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">{% trans "مسودة" %}</span>
                                        <span class="text-sm font-medium">{{ orders_stats.draft }}</span>
                                    </div>
                                {% endif %}
                                {% if orders_stats.confirmed %}
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">{% trans "مؤكد" %}</span>
                                        <span class="text-sm font-medium">{{ orders_stats.confirmed }}</span>
                                    </div>
                                {% endif %}
                                {% if orders_stats.shipped %}
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">{% trans "تم الشحن" %}</span>
                                        <span class="text-sm font-medium">{{ orders_stats.shipped }}</span>
                                    </div>
                                {% endif %}
                                {% if orders_stats.delivered %}
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">{% trans "تم التسليم" %}</span>
                                        <span class="text-sm font-medium">{{ orders_stats.delivered }}</span>
                                    </div>
                                {% endif %}
                                {% if orders_stats.cancelled %}
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">{% trans "ملغي" %}</span>
                                        <span class="text-sm font-medium">{{ orders_stats.cancelled }}</span>
                                    </div>
                                {% endif %}
                                {% if orders_stats.returned %}
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">{% trans "مرتجع" %}</span>
                                        <span class="text-sm font-medium">{{ orders_stats.returned }}</span>
                                    </div>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="text-sm text-gray-500">{% trans "لا توجد طلبات" %}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Orders -->
    <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "الطلبات الأخيرة" %}</h2>
            <a href="{% url 'sales:sales_order_create' %}?customer={{ customer.id }}" class="text-sm text-blue-600 hover:text-blue-800">
                {% trans "إنشاء طلب جديد" %} <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
            </a>
        </div>
        
        {% if recent_orders %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم الطلب" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "التاريخ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الحالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المبلغ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in recent_orders %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.order_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ order.order_date|date:"d/m/Y" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if order.status == 'draft' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {% trans "مسودة" %}
                                        </span>
                                    {% elif order.status == 'confirmed' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {% trans "مؤكد" %}
                                        </span>
                                    {% elif order.status == 'shipped' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {% trans "تم الشحن" %}
                                        </span>
                                    {% elif order.status == 'delivered' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            {% trans "تم التسليم" %}
                                        </span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            {% trans "ملغي" %}
                                        </span>
                                    {% elif order.status == 'returned' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">
                                            {% trans "مرتجع" %}
                                        </span>
                                    {% else %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {{ order.get_status_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.total_amount }} {% trans "ج.م" %}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'sales:sales_order_detail' order.id %}" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:sales_order_update' order.id %}" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% if more_orders %}
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 text-center">
                    <a href="{% url 'sales:sales_order_list' %}?customer={{ customer.id }}" class="text-blue-600 hover:text-blue-800 font-medium">
                        {% trans "عرض جميع الطلبات" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
                    </a>
                </div>
            {% endif %}
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا توجد طلبات لهذا العميل" %}
                <div class="mt-4">
                    <a href="{% url 'sales:sales_order_create' %}?customer={{ customer.id }}" class="text-blue-600 hover:text-blue-800 font-medium">
                        {% trans "إنشاء طلب جديد" %} <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 