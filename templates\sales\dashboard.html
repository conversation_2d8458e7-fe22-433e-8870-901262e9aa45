{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "المبيعات" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Total Orders -->
        <div onclick="showTotalOrdersDetails()" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "إجمالي الطلبات" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ total_orders|default:"2" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Pending Orders -->
        <div onclick="showPendingOrdersDetails()" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "طلبات قيد التنفيذ" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ pending_orders|default:"2" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Completed Orders -->
        <div onclick="showCompletedOrdersDetails()" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "طلبات مكتملة" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ completed_orders|default:"8" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Customers Count -->
        <div onclick="showCustomersDetails()" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                    <i class="fas fa-users"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "عدد العملاء" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ customers_count|default:"69" }}</p>
                </div>
            </div>
        </div>

        <!-- Sales Performance Card -->
        <div onclick="showSalesPerformanceDetails()" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-500">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "أداء المبيعات" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ total_sales_amount|default:"0" }} {% trans "ج.م" %}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Work Order Integration Section -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-tools text-blue-500 mr-2"></i>
                {% trans "مبيعات أوامر العمل" %}
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div onclick="showWorkOrderSalesDetails()" class="text-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors cursor-pointer">
                    <i class="fas fa-shopping-cart text-blue-600 text-2xl mb-2"></i>
                    <div class="text-2xl font-bold text-blue-600">{{ work_order_sales|default:"0" }}</div>
                    <div class="text-sm text-gray-600">{% trans "مبيعات من أوامر العمل" %}</div>
                </div>
                <div onclick="showWorkOrderSalesDetails()" class="text-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors cursor-pointer">
                    <i class="fas fa-dollar-sign text-green-600 text-2xl mb-2"></i>
                    <div class="text-2xl font-bold text-green-600">{% if work_order_sales_revenue > 0 %}{{ work_order_sales_revenue|floatformat:2 }}{% else %}0{% endif %}</div>
                    <div class="text-sm text-gray-600">{% trans "إيرادات أوامر العمل" %}</div>
                </div>
                <div onclick="showWorkOrderSalesDetails()" class="text-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors cursor-pointer">
                    <i class="fas fa-user-cog text-purple-600 text-2xl mb-2"></i>
                    <div class="text-2xl font-bold text-purple-600">{% if work_order_labor_revenue > 0 %}{{ work_order_labor_revenue|floatformat:2 }}{% else %}0{% endif %}</div>
                    <div class="text-sm text-gray-600">{% trans "إيرادات العمالة" %}</div>
                </div>
                <div onclick="showWorkOrderSalesDetails()" class="text-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors cursor-pointer">
                    <i class="fas fa-cogs text-orange-600 text-2xl mb-2"></i>
                    <div class="text-2xl font-bold text-orange-600">{% if work_order_parts_revenue > 0 %}{{ work_order_parts_revenue|floatformat:2 }}{% else %}0{% endif %}</div>
                    <div class="text-sm text-gray-600">{% trans "إيرادات قطع الغيار" %}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Work Order Sales -->
    {% if recent_work_order_sales %}
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-list text-indigo-500 mr-2"></i>
                {% trans "مبيعات أوامر العمل الحديثة" %}
            </h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "رقم المبيعة" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "رقم أمر العمل" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "العميل" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "المركبة" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "نوع الخدمة" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "المبلغ الإجمالي" %}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for sale in recent_work_order_sales %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ sale.order_number }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if sale.work_order %}
                                    <button onclick="showWorkOrderDetails('{{ sale.work_order.id }}')" 
                                            class="text-indigo-600 hover:text-indigo-800 font-medium cursor-pointer hover:underline">
                                        {{ sale.work_order_number|default:sale.work_order.work_order_number }}
                                    </button>
                                {% else %}
                                    <span class="text-gray-400">لا يوجد</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ sale.customer }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if sale.vehicle %}{{ sale.vehicle.license_plate }}{% else %}N/A{% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ sale.get_service_type_display|default:sale.service_type }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ sale.total_amount }} {% trans "ج.م" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Comprehensive Data Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
        
        <!-- Insurance & Warranty Section -->
        <div class="bg-white shadow rounded-lg hover:shadow-lg transition-shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-shield-alt text-blue-500 mr-2"></i>
                        {% trans "التأمين والضمان" %}
                    </h3>
                    <div class="flex items-center space-x-2">
                        <button onclick="openInsurancePage()" class="text-blue-600 hover:text-blue-800 text-sm">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                        <div class="relative">
                            <button onclick="toggleInsuranceActions()" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div id="insuranceActions" class="hidden absolute left-0 mt-2 w-40 bg-white rounded-md shadow-lg z-10">
                                <div class="py-1">
                                    <a href="/ar/billing/insurance-companies/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-plus mr-2"></i>إضافة شركة تأمين
                                    </a>
                                    <a href="/ar/billing/insurance-policies/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-file-plus mr-2"></i>إضافة بوليصة
                                    </a>
                                    <a href="/ar/billing/vehicle-warranties/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-shield-alt mr-2"></i>إضافة ضمان
                                    </a>
                                    <a href="/ar/billing/warranty-types/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-plus-circle mr-2"></i>إضافة نوع ضمان
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/insurance-companies/'">
                        <div class="flex items-center">
                            <i class="fas fa-building text-blue-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">شركات التأمين</div>
                                <div class="text-xs text-gray-500">{{ insurance_companies }} شركات نشطة</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-blue-600">{{ insurance_companies }}</span>
                            <i class="fas fa-chevron-left text-blue-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/insurance-policies/'">
                        <div class="flex items-center">
                            <i class="fas fa-file-contract text-green-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">بوالص التأمين</div>
                                <div class="text-xs text-gray-500">{{ insurance_policies }} بوليصة نشطة</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-green-600">{{ insurance_policies }}</span>
                            <i class="fas fa-chevron-left text-green-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/vehicle-warranties/'">
                        <div class="flex items-center">
                            <i class="fas fa-car text-purple-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">ضمانات المركبات</div>
                                <div class="text-xs text-gray-500">{{ vehicle_warranties }} ضمان ساري</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-purple-600">{{ vehicle_warranties }}</span>
                            <i class="fas fa-chevron-left text-purple-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/warranty-types/'">
                        <div class="flex items-center">
                            <i class="fas fa-tools text-orange-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">أنواع الضمان</div>
                                <div class="text-xs text-gray-500">{{ warranty_types }} أنواع متاحة</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-orange-600">{{ warranty_types }}</span>
                            <i class="fas fa-chevron-left text-orange-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment & Financial Section -->
        <div class="bg-white shadow rounded-lg hover:shadow-lg transition-shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-credit-card text-green-500 mr-2"></i>
                        {% trans "المدفوعات والمالية" %}
                    </h3>
                    <div class="flex items-center space-x-2">
                        <button onclick="openPaymentPage()" class="text-green-600 hover:text-green-800 text-sm">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                        <div class="relative">
                            <button onclick="togglePaymentActions()" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div id="paymentActions" class="hidden absolute left-0 mt-2 w-40 bg-white rounded-md shadow-lg z-10">
                                <div class="py-1">
                                    <a href="/ar/billing/payment-methods/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-plus mr-2"></i>إضافة طريقة دفع
                                    </a>
                                    <a href="/ar/billing/payments/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-money-bill mr-2"></i>إضافة دفعة
                                    </a>
                                    <a href="/ar/billing/invoices/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-file-invoice mr-2"></i>إنشاء فاتورة
                                    </a>
                                    <a href="/ar/billing/discount-types/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-percentage mr-2"></i>إضافة نوع خصم
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-emerald-50 rounded-lg hover:bg-emerald-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/payment-methods/'">
                        <div class="flex items-center">
                            <i class="fas fa-money-check-alt text-emerald-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">طرق الدفع</div>
                                <div class="text-xs text-gray-500">{{ payment_methods }} طرق متاحة</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-emerald-600">{{ payment_methods }}</span>
                            <i class="fas fa-chevron-left text-emerald-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/payments/'">
                        <div class="flex items-center">
                            <i class="fas fa-receipt text-blue-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">المدفوعات</div>
                                <div class="text-xs text-gray-500">{{ total_payments }} دفعة هذا الشهر</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-blue-600">{{ total_payments }}</span>
                            <i class="fas fa-chevron-left text-blue-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/invoices/'">
                        <div class="flex items-center">
                            <i class="fas fa-file-invoice-dollar text-yellow-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">الفواتير</div>
                                <div class="text-xs text-gray-500">{{ pending_invoices }} فاتورة معلقة</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-yellow-600">{{ pending_invoices }}</span>
                            <i class="fas fa-chevron-left text-yellow-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg hover:bg-red-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/discount-types/'">
                        <div class="flex items-center">
                            <i class="fas fa-percentage text-red-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">أنواع الخصم</div>
                                <div class="text-xs text-gray-500">{{ discount_types }} نوع خصم</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-red-600">{{ discount_types }}</span>
                            <i class="fas fa-chevron-left text-red-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Management Section -->
        <div class="bg-white shadow rounded-lg hover:shadow-lg transition-shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-users text-indigo-500 mr-2"></i>
                        {% trans "إدارة العملاء" %}
                    </h3>
                    <div class="flex items-center space-x-2">
                        <button onclick="openCustomerManagementPage()" class="text-indigo-600 hover:text-indigo-800 text-sm">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                        <div class="relative">
                            <button onclick="toggleCustomerActions()" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div id="customerActions" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                                <div class="py-1">
                                    <a href="/ar/setup/customers/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user-plus mr-2"></i>إضافة عميل جديد
                                    </a>
                                    <a href="/ar/billing/preferences/create/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-heart mr-2"></i>إضافة تفضيل عميل
                                    </a>
                                    <a href="/ar/sales/customer-management/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-cog mr-2"></i>إعدادات العملاء
                                    </a>
                                    <a href="/ar/setup/customers/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-list mr-2"></i>قائمة العملاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/setup/customers/'">
                        <div class="flex items-center">
                            <i class="fas fa-user-tag text-indigo-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">قائمة العملاء</div>
                                <div class="text-xs text-gray-500">{{ customer_classifications }} عميل مسجل</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-indigo-600">{{ customer_classifications }}</span>
                            <i class="fas fa-chevron-left text-indigo-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-pink-50 rounded-lg hover:bg-pink-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/sales/customer-management/'">
                        <div class="flex items-center">
                            <i class="fas fa-history text-pink-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">إدارة العملاء</div>
                                <div class="text-xs text-gray-500">{{ customer_history_records }} سجل</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-pink-600">{{ customer_history_records }}</span>
                            <i class="fas fa-chevron-left text-pink-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-teal-50 rounded-lg hover:bg-teal-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/billing/preferences/'">
                        <div class="flex items-center">
                            <i class="fas fa-heart text-teal-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">تفضيلات العملاء</div>
                                <div class="text-xs text-gray-500">{{ customer_preferences }} تفضيل</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-teal-600">{{ customer_preferences }}</span>
                            <i class="fas fa-chevron-left text-teal-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-cyan-50 rounded-lg hover:bg-cyan-100 transition-colors cursor-pointer" onclick="window.location.href='/ar/setup/service-centers/'">
                        <div class="flex items-center">
                            <i class="fas fa-filter text-cyan-600 mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">مراكز الخدمة</div>
                                <div class="text-xs text-gray-500">{{ classification_criteria }} مركز</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-cyan-600">{{ classification_criteria }}</span>
                            <i class="fas fa-chevron-left text-cyan-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotions & Rules Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="bg-white shadow rounded-lg cursor-pointer hover:shadow-lg transition-shadow" onclick="openOffersPage()">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-tags text-red-500 mr-2"></i>
                    {% trans "العروض والقواعد" %}
                    <i class="fas fa-external-link-alt text-gray-400 text-sm mr-2"></i>
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-4 bg-red-50 rounded-lg">
                        <i class="fas fa-bullhorn text-red-600 text-2xl mb-2"></i>
                        <div class="text-lg font-bold text-red-600">{{ promotion_rules }}</div>
                        <div class="text-sm text-gray-600">قواعد العروض</div>
                    </div>
                    <div class="text-center p-4 bg-orange-50 rounded-lg">
                        <i class="fas fa-clipboard-list text-orange-600 text-2xl mb-2"></i>
                        <div class="text-lg font-bold text-orange-600">{{ promotion_rule_logs }}</div>
                        <div class="text-sm text-gray-600">سجلات القواعد</div>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-gradient-to-r from-red-50 to-orange-50 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-gray-900">العروض النشطة</div>
                            <div class="text-xs text-gray-500">{{ active_promotions }} عروض تنتهي قريباً</div>
                        </div>
                        <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">نشط</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-chart-line text-purple-500 mr-2"></i>
                    {% trans "إحصائيات سريعة" %}
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-4 bg-purple-50 rounded-lg">
                        <i class="fas fa-users text-purple-600 text-2xl mb-2"></i>
                        <div class="text-lg font-bold text-purple-600">{{ customers_count }}</div>
                        <div class="text-sm text-gray-600">إجمالي العملاء</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <i class="fas fa-shopping-cart text-green-600 text-2xl mb-2"></i>
                        <div class="text-lg font-bold text-green-600">{{ orders_today }}</div>
                        <div class="text-sm text-gray-600">طلبات اليوم</div>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-gradient-to-r from-purple-50 to-green-50 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm font-medium text-gray-900">معدل النمو</div>
                            <div class="text-xs text-gray-500">{{ sales_growth }} هذا الشهر</div>
                        </div>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            <i class="fas fa-arrow-up mr-1"></i>
                            ممتاز
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Price Lists Section -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-dollar-sign text-amber-500 mr-2"></i>
                    {% trans "قوائم الأسعار" %}
                </h3>
                <!-- Action Dropdown -->
                <div class="relative">
                    <button onclick="togglePriceListActions()" class="text-gray-400 hover:text-gray-600" id="priceListActionsButton">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div id="priceListActionsMenu" class="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50 hidden">
                        <div class="py-1">
                            <a href="{% url 'sales:price_configuration' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-cog mr-2"></i>إدارة قوائم الأسعار
                            </a>
                            <a href="{% url 'sales:price_configuration' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-wrench mr-2"></i>أسعار العمليات
                            </a>
                            <a href="{% url 'sales:price_configuration' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-cogs mr-2"></i>أسعار قطع الغيار
                            </a>
                            <div class="border-t border-gray-100"></div>
                            <a href="{% url 'sales:api_price_list_export' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-file-export mr-2"></i>تصدير قائمة الأسعار
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Operations Pricing -->
                <div class="border border-amber-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" data-url="{% url 'sales:price_configuration' %}">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <i class="fas fa-wrench text-amber-500 text-2xl mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">أسعار العمليات</div>
                                <div class="text-xs text-gray-500">تسعير العمليات والخدمات</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-left text-amber-400"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">عمليات متاحة:</span>
                            <span class="font-medium">{{ operations_count|default:"0" }}</span>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">مستويات تسعير:</span>
                            <span class="font-medium">{{ operation_pricing_levels|default:"3" }}</span>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">متوسط السعر:</span>
                            <span class="font-medium">{{ avg_operation_price|default:"150" }} ج.م</span>
                        </div>
                    </div>
                </div>

                <!-- Parts Pricing -->
                <div class="border border-blue-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" data-url="{% url 'sales:price_configuration' %}">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <i class="fas fa-cogs text-blue-500 text-2xl mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">أسعار قطع الغيار</div>
                                <div class="text-xs text-gray-500">تسعير قطع الغيار والمواد</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-left text-blue-400"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">قطع متاحة:</span>
                            <span class="font-medium">{{ parts_count|default:"0" }}</span>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">مستويات تسعير:</span>
                            <span class="font-medium">{{ part_pricing_levels|default:"5" }}</span>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">متوسط السعر:</span>
                            <span class="font-medium">{{ avg_part_price|default:"75" }} ج.م</span>
                        </div>
                    </div>
                </div>

                <!-- Special Pricing -->
                <div class="border border-green-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" data-url="{% url 'sales:price_configuration' %}">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <i class="fas fa-star text-green-500 text-2xl mr-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900">تسعير خاص</div>
                                <div class="text-xs text-gray-500">أسعار حسب العميل والمركز</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-left text-green-400"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">عملاء بتسعير خاص:</span>
                            <span class="font-medium">{{ special_pricing_customers|default:"0" }}</span>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">خصومات نشطة:</span>
                            <span class="font-medium">{{ active_discounts|default:"0" }}%</span>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span class="text-gray-600">أسعار ديناميكية:</span>
                            <span class="font-medium text-green-600">متاح</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Price Overview -->
            <div class="mt-6 bg-gradient-to-r from-amber-50 to-green-50 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-gray-900">ملخص الأسعار</div>
                        <div class="text-xs text-gray-500">آخر تحديث للأسعار منذ {{ price_last_update|default:"يوم واحد" }}</div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="text-center">
                            <div class="text-xs text-gray-600">عمليات</div>
                            <div class="text-sm font-bold text-amber-600">{{ total_operations_pricing|default:"0" }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">قطع غيار</div>
                            <div class="text-sm font-bold text-blue-600">{{ total_parts_pricing|default:"0" }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">خاص</div>
                            <div class="text-sm font-bold text-green-600">{{ total_special_pricing|default:"0" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Work Order Details Modal -->
<div id="workOrderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center border-b pb-3 mb-4">
                <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">{% trans "تفاصيل أمر العمل" %}</h3>
                <button onclick="closeWorkOrderModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Loading State -->
            <div id="modalLoading" class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-3xl text-blue-500 mb-4"></i>
                <p class="text-gray-600">{% trans "جاري تحميل تفاصيل أمر العمل..." %}</p>
            </div>
            
            <!-- Error State -->
            <div id="modalError" class="hidden text-center py-8">
                <i class="fas fa-exclamation-triangle text-3xl text-red-500 mb-4"></i>
                <p class="text-red-600">{% trans "حدث خطأ في تحميل البيانات" %}</p>
            </div>
            
            <!-- Modal Content -->
            <div id="modalContent" class="hidden">
                <!-- Work Order Basic Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-2">{% trans "معلومات أساسية" %}</h4>
                        <div class="space-y-2 text-sm">
                            <div><span class="font-medium">{% trans "رقم أمر العمل" %}:</span> <span id="woNumber"></span></div>
                            <div><span class="font-medium">{% trans "الحالة" %}:</span> <span id="woStatus"></span></div>
                            <div><span class="font-medium">{% trans "تاريخ الإنشاء" %}:</span> <span id="woCreatedAt"></span></div>
                            <div><span class="font-medium">{% trans "التاريخ المطلوب" %}:</span> <span id="woRequiredDate"></span></div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-2">{% trans "معلومات العميل والمركبة" %}</h4>
                        <div class="space-y-2 text-sm">
                            <div><span class="font-medium">{% trans "العميل" %}:</span> <span id="woCustomer"></span></div>
                            <div><span class="font-medium">{% trans "المركبة" %}:</span> <span id="woVehicle"></span></div>
                            <div><span class="font-medium">{% trans "رقم اللوحة" %}:</span> <span id="woVehiclePlate"></span></div>
                            <div><span class="font-medium">{% trans "مركز الخدمة" %}:</span> <span id="woServiceCenter"></span></div>
                        </div>
                    </div>
                </div>
                
                <!-- Work Order Operations -->
                <div class="mb-6">
                    <h4 class="font-semibold text-gray-800 mb-3">{% trans "العمليات" %}</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "العملية" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الوصف" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "التقني" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الحالة" %}</th>
                                </tr>
                            </thead>
                            <tbody id="woOperations" class="divide-y divide-gray-200">
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Work Order Materials -->
                <div class="mb-6">
                    <h4 class="font-semibold text-gray-800 mb-3">{% trans "المواد والقطع" %}</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الصنف" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الكمية المطلوبة" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الكمية المستخدمة" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "السعر" %}</th>
                                </tr>
                            </thead>
                            <tbody id="woMaterials" class="divide-y divide-gray-200">
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Work Order Notes -->
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 mb-2">{% trans "ملاحظات" %}</h4>
                    <p id="woNotes" class="text-sm text-gray-600"></p>
                </div>
            </div>
            
            <!-- Modal Footer -->
            <div class="flex justify-end pt-4 border-t mt-6">
                <button onclick="closeWorkOrderModal()" 
                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg mr-2">
                    {% trans "إغلاق" %}
                </button>
                <a id="viewWorkOrderLink" href="#" target="_blank" 
                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    {% trans "عرض التفاصيل الكاملة" %}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Card Details Modals -->

<!-- Total Orders Details Modal -->
<div id="totalOrdersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-shopping-cart text-green-500 mr-2"></i>
                تفاصيل إجمالي الطلبات
            </h3>
            <button onclick="closeTotalOrdersModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <!-- Loading State -->
        <div id="totalOrdersLoading" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-3xl text-gray-400"></i>
            <p class="mt-2 text-gray-500">جاري تحميل البيانات...</p>
        </div>
        
        <!-- Content -->
        <div id="totalOrdersContent" class="hidden">
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي الطلبات</div>
                    <div id="totalOrdersCount" class="text-2xl font-bold text-blue-600">0</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي القيمة</div>
                    <div id="totalOrdersValue" class="text-2xl font-bold text-green-600">0 ج.م</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">متوسط قيمة الطلب</div>
                    <div id="avgOrderValue" class="text-2xl font-bold text-purple-600">0 ج.م</div>
                </div>
            </div>
            
            <!-- Orders Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">رقم الطلب</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">العميل</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المركز</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                        </tr>
                    </thead>
                    <tbody id="totalOrdersTable" class="divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Pending Orders Details Modal -->
<div id="pendingOrdersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-clock text-yellow-500 mr-2"></i>
                تفاصيل الطلبات قيد التنفيذ
            </h3>
            <button onclick="closePendingOrdersModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <!-- Loading State -->
        <div id="pendingOrdersLoading" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-3xl text-gray-400"></i>
            <p class="mt-2 text-gray-500">جاري تحميل البيانات...</p>
        </div>
        
        <!-- Content -->
        <div id="pendingOrdersContent" class="hidden">
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي الطلبات المعلقة</div>
                    <div id="pendingOrdersCount" class="text-2xl font-bold text-yellow-600">0</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">طلبات عاجلة (أكثر من 7 أيام)</div>
                    <div id="urgentOrdersCount" class="text-2xl font-bold text-red-600">0</div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">طلبات حديثة (أقل من 7 أيام)</div>
                    <div id="recentOrdersCount" class="text-2xl font-bold text-blue-600">0</div>
                </div>
            </div>
            
            <!-- Orders Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">رقم الطلب</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">العميل</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المركز</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">عدد الأيام</th>
                        </tr>
                    </thead>
                    <tbody id="pendingOrdersTable" class="divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Completed Orders Details Modal -->
<div id="completedOrdersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                تفاصيل الطلبات المكتملة
            </h3>
            <button onclick="closeCompletedOrdersModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <!-- Loading State -->
        <div id="completedOrdersLoading" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-3xl text-gray-400"></i>
            <p class="mt-2 text-gray-500">جاري تحميل البيانات...</p>
        </div>
        
        <!-- Content -->
        <div id="completedOrdersContent" class="hidden">
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي الطلبات المكتملة</div>
                    <div id="completedOrdersCount" class="text-2xl font-bold text-green-600">0</div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي الإيرادات</div>
                    <div id="completedOrdersRevenue" class="text-2xl font-bold text-blue-600">0 ج.م</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">هذا الشهر</div>
                    <div id="completedOrdersThisMonth" class="text-2xl font-bold text-purple-600">0</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">الشهر الماضي</div>
                    <div id="completedOrdersLastMonth" class="text-2xl font-bold text-orange-600">0</div>
                </div>
            </div>
            
            <!-- Orders Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">رقم الطلب</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">العميل</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المركز</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الإكمال</th>
                        </tr>
                    </thead>
                    <tbody id="completedOrdersTable" class="divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Customers Details Modal -->
<div id="customersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-users text-blue-500 mr-2"></i>
                تفاصيل العملاء
            </h3>
            <button onclick="closeCustomersModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <!-- Loading State -->
        <div id="customersLoading" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-3xl text-gray-400"></i>
            <p class="mt-2 text-gray-500">جاري تحميل البيانات...</p>
        </div>
        
        <!-- Content -->
        <div id="customersContent" class="hidden">
            <!-- Search and Filter Controls -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">البحث</label>
                        <div class="relative">
                            <input 
                                type="text" 
                                id="customerSearch" 
                                placeholder="ابحث بالاسم، البريد، الهاتف، أو الشركة..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            >
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- VIP Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نوع العميل</label>
                        <select id="vipFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">جميع العملاء</option>
                            <option value="vip">عملاء VIP فقط</option>
                            <option value="regular">عملاء عاديون</option>
                        </select>
                    </div>
                    
                    <!-- Date Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التسجيل</label>
                        <select id="dateFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">جميع الفترات</option>
                            <option value="this_month">هذا الشهر</option>
                            <option value="last_month">الشهر الماضي</option>
                            <option value="last_3_months">آخر 3 أشهر</option>
                        </select>
                    </div>
                </div>
                
                <!-- Results Info and Per Page -->
                <div class="flex justify-between items-center mt-4">
                    <div id="resultsInfo" class="text-sm text-gray-600">
                        عرض 0 - 0 من 0 عميل
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm text-gray-700">عرض:</label>
                        <select id="perPageSelect" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span class="text-sm text-gray-700">عميل</span>
                    </div>
                </div>
            </div>
            
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي العملاء</div>
                    <div id="totalCustomersCount" class="text-2xl font-bold text-blue-600">0</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">عملاء VIP</div>
                    <div id="vipCustomersCount" class="text-2xl font-bold text-purple-600">0</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">عملاء جدد هذا الشهر</div>
                    <div id="newCustomersThisMonth" class="text-2xl font-bold text-green-600">0</div>
                </div>
            </div>
            
            <!-- Customers Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase border-l border-gray-300 cursor-pointer hover:bg-gray-100" onclick="sortCustomers('name')">
                                <div class="flex items-center justify-start">
                                    الاسم
                                    <i id="sort-name" class="fas fa-sort ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase border-l border-gray-300 cursor-pointer hover:bg-gray-100" onclick="sortCustomers('email')">
                                <div class="flex items-center justify-start">
                                    البريد الإلكتروني
                                    <i id="sort-email" class="fas fa-sort ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase border-l border-gray-300 cursor-pointer hover:bg-gray-100" onclick="sortCustomers('phone')">
                                <div class="flex items-center justify-start">
                                    الهاتف
                                    <i id="sort-phone" class="fas fa-sort ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase border-l border-gray-300 cursor-pointer hover:bg-gray-100" onclick="sortCustomers('company')">
                                <div class="flex items-center justify-start">
                                    الشركة
                                    <i id="sort-company" class="fas fa-sort ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase border-l border-gray-300 cursor-pointer hover:bg-gray-100" onclick="sortCustomers('total_orders')">
                                <div class="flex items-center justify-start">
                                    عدد الطلبات
                                    <i id="sort-total_orders" class="fas fa-sort ml-1 text-gray-400"></i>
                                </div>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase border-l border-gray-300 cursor-pointer hover:bg-gray-100" onclick="sortCustomers('total_spent')">
                                <div class="flex items-center justify-start">
                                    إجمالي الإنفاق
                                    <i id="sort-total_spent" class="fas fa-sort-down ml-1 text-blue-500"></i>
                                </div>
                            </th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100" onclick="sortCustomers('last_order_date')">
                                <div class="flex items-center justify-start">
                                    آخر طلب
                                    <i id="sort-last_order_date" class="fas fa-sort ml-1 text-gray-400"></i>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="customersTable" class="divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination Controls -->
            <div class="flex items-center justify-between bg-white px-4 py-3 sm:px-6 border-t border-gray-200">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button id="prevPageMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        السابق
                    </button>
                    <button id="nextPageMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                        التالي
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700" id="paginationInfo">
                            عرض <span class="font-medium">1</span> إلى <span class="font-medium">10</span> من <span class="font-medium">0</span> نتيجة
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="paginationControls">
                            <!-- Pagination buttons will be dynamically generated -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Performance Details Modal -->
<div id="salesPerformanceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-chart-line text-purple-500 mr-2"></i>
                تفاصيل أداء المبيعات
            </h3>
            <button onclick="closeSalesPerformanceModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <!-- Loading State -->
        <div id="salesPerformanceLoading" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-3xl text-gray-400"></i>
            <p class="mt-2 text-gray-500">جاري تحميل البيانات...</p>
        </div>
        
        <!-- Content -->
        <div id="salesPerformanceContent" class="hidden">
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي المبيعات</div>
                    <div id="totalSalesAmount" class="text-2xl font-bold text-blue-600">0 ج.م</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">عدد الطلبات</div>
                    <div id="totalSalesOrders" class="text-2xl font-bold text-green-600">0</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">مبيعات هذا الشهر</div>
                    <div id="thisMonthSales" class="text-2xl font-bold text-purple-600">0 ج.م</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">مبيعات الشهر الماضي</div>
                    <div id="lastMonthSales" class="text-2xl font-bold text-orange-600">0 ج.م</div>
                </div>
            </div>
            
            <!-- Top Centers Table -->
            <div class="mb-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">أفضل المراكز أداءً</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المركز</th>
                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">إجمالي المبيعات</th>
                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">عدد الطلبات</th>
                            </tr>
                        </thead>
                        <tbody id="topCentersTable" class="divide-y divide-gray-200">
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Monthly Performance Chart -->
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">الأداء الشهري</h4>
                <div id="monthlyChart" class="bg-gray-50 p-4 rounded-lg">
                    <canvas id="monthlyPerformanceChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Work Order Sales Details Modal -->
<div id="workOrderSalesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-tools text-blue-500 mr-2"></i>
                تفاصيل مبيعات أوامر العمل
            </h3>
            <button onclick="closeWorkOrderSalesModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <!-- Loading State -->
        <div id="workOrderSalesLoading" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-3xl text-gray-400"></i>
            <p class="mt-2 text-gray-500">جاري تحميل البيانات...</p>
        </div>
        
        <!-- Content -->
        <div id="workOrderSalesContent" class="hidden">
            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي المبيعات</div>
                    <div id="workOrderTotalSales" class="text-2xl font-bold text-blue-600">0</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إجمالي الإيرادات</div>
                    <div id="workOrderTotalRevenue" class="text-2xl font-bold text-green-600">0 ج.م</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إيرادات العمالة</div>
                    <div id="workOrderLaborRevenue" class="text-2xl font-bold text-purple-600">0 ج.م</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">إيرادات قطع الغيار</div>
                    <div id="workOrderPartsRevenue" class="text-2xl font-bold text-orange-600">0 ج.م</div>
                </div>
            </div>
            
            <!-- Revenue Breakdown -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">نسبة إيرادات العمالة</div>
                    <div id="laborPercentage" class="text-3xl font-bold text-purple-600">0%</div>
                </div>
                <div class="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg">
                    <div class="text-sm text-gray-600">نسبة إيرادات قطع الغيار</div>
                    <div id="partsPercentage" class="text-3xl font-bold text-orange-600">0%</div>
                </div>
            </div>
            
            <!-- Recent Sales Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">رقم الطلب</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">رقم أمر العمل</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">العميل</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">نوع الخدمة</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">المركز</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                        </tr>
                    </thead>
                    <tbody id="workOrderSalesTable" class="divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
// Dashboard Card Detail Functions

// Total Orders Details
function showTotalOrdersDetails() {
    const modal = document.getElementById('totalOrdersModal');
    modal.classList.remove('hidden');
    
    // Show loading state
    document.getElementById('totalOrdersLoading').classList.remove('hidden');
    document.getElementById('totalOrdersContent').classList.add('hidden');
    
    // First test the API connectivity
    console.log('Testing API connectivity...');
    
    // Fetch data
    fetch('/ar/sales/api/total-orders-details/')
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('API response data:', data);
            if (data.success) {
                populateTotalOrdersModal(data.data);
            } else {
                console.error('API Error:', data.error);
                showErrorInModal('totalOrdersModal', 'خطأ في جلب البيانات: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Fetch Error:', error);
            showErrorInModal('totalOrdersModal', 'خطأ في الاتصال بالخادم: ' + error.message);
        });
}

function showErrorInModal(modalId, message) {
    // Hide loading
    document.getElementById(modalId.replace('Modal', 'Loading')).classList.add('hidden');
    
    // Show error message
    const loadingDiv = document.getElementById(modalId.replace('Modal', 'Loading'));
    loadingDiv.innerHTML = `
        <div class="text-center py-8">
            <i class="fas fa-exclamation-triangle text-3xl text-red-400"></i>
            <p class="mt-2 text-red-600">${message}</p>
            <button onclick="window.location.reload()" class="mt-2 bg-blue-500 text-white px-4 py-2 rounded">إعادة تحميل</button>
        </div>
    `;
    loadingDiv.classList.remove('hidden');
}

function populateTotalOrdersModal(data) {
    // Hide loading, show content
    document.getElementById('totalOrdersLoading').classList.add('hidden');
    document.getElementById('totalOrdersContent').classList.remove('hidden');
    
    // Populate summary cards
    document.getElementById('totalOrdersCount').textContent = data.total_count;
    document.getElementById('totalOrdersValue').textContent = data.total_value.toFixed(2) + ' ج.م';
    document.getElementById('avgOrderValue').textContent = (data.total_value / data.total_count).toFixed(2) + ' ج.م';
    
    // Populate orders table
    const tableBody = document.getElementById('totalOrdersTable');
    tableBody.innerHTML = '';
    
    data.orders.forEach(order => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-4 py-2 text-sm font-medium text-gray-900">${order.order_number}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.customer_name}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.service_center}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.total_amount.toFixed(2)} ج.م</td>
            <td class="px-4 py-2">${getStatusBadge(order.status)}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${formatDateTime(order.created_at)}</td>
        `;
        tableBody.appendChild(row);
    });
}

function closeTotalOrdersModal() {
    document.getElementById('totalOrdersModal').classList.add('hidden');
}

// Pending Orders Details
function showPendingOrdersDetails() {
    const modal = document.getElementById('pendingOrdersModal');
    modal.classList.remove('hidden');
    
    document.getElementById('pendingOrdersLoading').classList.remove('hidden');
    document.getElementById('pendingOrdersContent').classList.add('hidden');
    
    fetch('/ar/sales/api/pending-orders-details/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populatePendingOrdersModal(data.data);
            }
        })
        .catch(error => console.error('Error:', error));
}

function populatePendingOrdersModal(data) {
    document.getElementById('pendingOrdersLoading').classList.add('hidden');
    document.getElementById('pendingOrdersContent').classList.remove('hidden');
    
    document.getElementById('pendingOrdersCount').textContent = data.total_count;
    document.getElementById('urgentOrdersCount').textContent = data.urgent_count;
    document.getElementById('recentOrdersCount').textContent = data.recent_count;
    
    const tableBody = document.getElementById('pendingOrdersTable');
    tableBody.innerHTML = '';
    
    data.orders.forEach(order => {
        const row = document.createElement('tr');
        const urgentClass = order.days_pending > 7 ? 'bg-red-50' : '';
        row.className = urgentClass;
        row.innerHTML = `
            <td class="px-4 py-2 text-sm font-medium text-gray-900">${order.order_number}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.customer_name}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.service_center}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.total_amount.toFixed(2)} ج.م</td>
            <td class="px-4 py-2">${getStatusBadge(order.status)}</td>
            <td class="px-4 py-2 text-sm text-gray-500">
                <span class="${order.days_pending > 7 ? 'text-red-600 font-bold' : 'text-gray-500'}">
                    ${order.days_pending} يوم
                </span>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function closePendingOrdersModal() {
    document.getElementById('pendingOrdersModal').classList.add('hidden');
}

// Completed Orders Details
function showCompletedOrdersDetails() {
    const modal = document.getElementById('completedOrdersModal');
    modal.classList.remove('hidden');
    
    document.getElementById('completedOrdersLoading').classList.remove('hidden');
    document.getElementById('completedOrdersContent').classList.add('hidden');
    
    fetch('/ar/sales/api/completed-orders-details/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateCompletedOrdersModal(data.data);
            }
        })
        .catch(error => console.error('Error:', error));
}

function populateCompletedOrdersModal(data) {
    document.getElementById('completedOrdersLoading').classList.add('hidden');
    document.getElementById('completedOrdersContent').classList.remove('hidden');
    
    document.getElementById('completedOrdersCount').textContent = data.total_count;
    document.getElementById('completedOrdersRevenue').textContent = data.total_revenue.toFixed(2) + ' ج.م';
    document.getElementById('completedOrdersThisMonth').textContent = data.this_month;
    document.getElementById('completedOrdersLastMonth').textContent = data.last_month;
    
    const tableBody = document.getElementById('completedOrdersTable');
    tableBody.innerHTML = '';
    
    data.orders.forEach(order => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-4 py-2 text-sm font-medium text-gray-900">${order.order_number}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.customer_name}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.service_center}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${order.total_amount.toFixed(2)} ج.م</td>
            <td class="px-4 py-2 text-sm text-gray-500">${formatDateTime(order.completed_at)}</td>
        `;
        tableBody.appendChild(row);
    });
}

function closeCompletedOrdersModal() {
    document.getElementById('completedOrdersModal').classList.add('hidden');
}

// Customers Details
let currentCustomersPage = 1;
let currentSort = '-total_spent';
let searchTimeout;

function showCustomersDetails() {
    const modal = document.getElementById('customersModal');
    modal.classList.remove('hidden');
    
    document.getElementById('customersLoading').classList.remove('hidden');
    document.getElementById('customersContent').classList.add('hidden');
    
    // Reset filters and pagination
    currentCustomersPage = 1;
    currentSort = '-total_spent';
    
    // Setup event listeners
    setupCustomersEventListeners();
    
    // Load customers data
    loadCustomersData();
}

function setupCustomersEventListeners() {
    // Search input with debouncing
    const searchInput = document.getElementById('customerSearch');
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentCustomersPage = 1;
            loadCustomersData();
        }, 500);
    });
    
    // Filter dropdowns
    document.getElementById('vipFilter').addEventListener('change', function() {
        currentCustomersPage = 1;
        loadCustomersData();
    });
    
    document.getElementById('dateFilter').addEventListener('change', function() {
        currentCustomersPage = 1;
        loadCustomersData();
    });
    
    // Per page selector
    document.getElementById('perPageSelect').addEventListener('change', function() {
        currentCustomersPage = 1;
        loadCustomersData();
    });
    
    // Mobile pagination buttons
    document.getElementById('prevPageMobile').addEventListener('click', function() {
        if (currentCustomersPage > 1) {
            currentCustomersPage--;
            loadCustomersData();
        }
    });
    
    document.getElementById('nextPageMobile').addEventListener('click', function() {
        currentCustomersPage++;
        loadCustomersData();
    });
}

function loadCustomersData() {
    const search = document.getElementById('customerSearch').value;
    const vipFilter = document.getElementById('vipFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    const perPage = document.getElementById('perPageSelect').value;
    
    const params = new URLSearchParams({
        page: currentCustomersPage,
        per_page: perPage,
        sort_by: currentSort
    });
    
    if (search) params.append('search', search);
    if (vipFilter) params.append('vip_filter', vipFilter);
    if (dateFilter) params.append('date_filter', dateFilter);
    
    fetch(`/ar/sales/api/customers-details/?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateCustomersModal(data.data);
            }
        })
        .catch(error => console.error('Error:', error));
}

function sortCustomers(field) {
    // Toggle sort direction
    if (currentSort === field) {
        currentSort = '-' + field;
    } else if (currentSort === '-' + field) {
        currentSort = field;
    } else {
        currentSort = '-' + field; // Default to descending
    }
    
    // Update sort icons
    updateSortIcons(field);
    
    // Reset to first page and reload data
    currentCustomersPage = 1;
    loadCustomersData();
}

function updateSortIcons(activeField) {
    // Reset all sort icons
    const sortIcons = document.querySelectorAll('[id^="sort-"]');
    sortIcons.forEach(icon => {
        icon.className = 'fas fa-sort ml-1 text-gray-400';
    });
    
    // Update active sort icon
    const activeIcon = document.getElementById(`sort-${activeField}`);
    if (activeIcon) {
        if (currentSort.startsWith('-')) {
            activeIcon.className = 'fas fa-sort-down ml-1 text-blue-500';
        } else {
            activeIcon.className = 'fas fa-sort-up ml-1 text-blue-500';
        }
    }
}

function populateCustomersModal(data) {
    document.getElementById('customersLoading').classList.add('hidden');
    document.getElementById('customersContent').classList.remove('hidden');
    
    // Update summary cards
    document.getElementById('totalCustomersCount').textContent = data.total_customers;
    document.getElementById('vipCustomersCount').textContent = data.vip_customers;
    document.getElementById('newCustomersThisMonth').textContent = data.new_this_month;
    
    // Update results info
    const pagination = data.pagination;
    document.getElementById('resultsInfo').textContent = 
        `عرض ${pagination.start_index} - ${pagination.end_index} من ${pagination.total_count} عميل`;
    
    // Update pagination info
    document.getElementById('paginationInfo').innerHTML = 
        `عرض <span class="font-medium">${pagination.start_index}</span> إلى <span class="font-medium">${pagination.end_index}</span> من <span class="font-medium">${pagination.total_count}</span> نتيجة`;
    
    // Update mobile pagination buttons
    const prevMobile = document.getElementById('prevPageMobile');
    const nextMobile = document.getElementById('nextPageMobile');
    prevMobile.disabled = !pagination.has_previous;
    nextMobile.disabled = !pagination.has_next;
    
    // Generate pagination controls
    generatePaginationControls(pagination);
    
    // Function to format phone number
    function formatPhoneNumber(phone) {
        if (!phone || phone === 'غير محدد') return 'غير محدد';
        
        // Convert Arabic numerals to English if present
        const arabicToEnglish = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        };
        
        let cleanPhone = phone;
        // Convert Arabic digits to English
        for (let arabic in arabicToEnglish) {
            cleanPhone = cleanPhone.replace(new RegExp(arabic, 'g'), arabicToEnglish[arabic]);
        }
        
        // Remove any non-digit characters
        cleanPhone = cleanPhone.replace(/\D/g, '');
        
        // Format Egyptian phone numbers in English
        if (cleanPhone.length === 11 && cleanPhone.startsWith('01')) {
            // Format: 01X XXXX XXXX
            return '+20 ' + cleanPhone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
        } else if (cleanPhone.length === 10 && cleanPhone.startsWith('1')) {
            // Format: 1X XXXX XXXX (if missing leading 0)
            return '+20 0' + cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '$1 $2 $3');
        } else if (cleanPhone.length === 13 && cleanPhone.startsWith('201')) {
            // International format starting with 201
            return '+' + cleanPhone.replace(/(\d{2})(\d{3})(\d{4})(\d{4})/, '$1 $2 $3 $4');
        } else if (cleanPhone.length >= 7) {
            // Generic formatting for other numbers
            if (cleanPhone.length === 7) {
                return cleanPhone.replace(/(\d{3})(\d{4})/, '$1-$2');
            } else if (cleanPhone.length === 8) {
                return cleanPhone.replace(/(\d{4})(\d{4})/, '$1-$2');
            } else if (cleanPhone.length >= 10) {
                return cleanPhone.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
            }
        }
        
        // Return original if no formatting pattern matches
        return cleanPhone || phone;
    }
    
    // Populate customers table
    const tableBody = document.getElementById('customersTable');
    tableBody.innerHTML = '';
    
    if (data.customers.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="7" class="px-4 py-8 text-center text-gray-500">
                <i class="fas fa-users text-4xl mb-2"></i>
                <div>لا توجد عملاء مطابقة للبحث</div>
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }
    
    data.customers.forEach(customer => {
        const row = document.createElement('tr');
        const vipClass = customer.is_vip ? 'bg-purple-50' : '';
        row.className = `hover:bg-gray-50 ${vipClass}`;
        row.innerHTML = `
            <td class="px-4 py-2 text-sm font-medium text-gray-900 border-l border-gray-200 text-left">
                ${customer.name || 'غير محدد'}
                ${customer.is_vip ? '<span class="ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">VIP</span>' : ''}
            </td>
            <td class="px-4 py-2 text-sm text-gray-500 border-l border-gray-200 text-left">${customer.email || 'غير محدد'}</td>
            <td class="px-4 py-2 text-sm text-gray-500 border-l border-gray-200 font-mono text-left" dir="ltr">${formatPhoneNumber(customer.phone)}</td>
            <td class="px-4 py-2 text-sm text-gray-500 border-l border-gray-200 text-left">${customer.company || 'غير محدد'}</td>
            <td class="px-4 py-2 text-sm text-gray-500 border-l border-gray-200 text-left font-semibold">${customer.total_orders || 0}</td>
            <td class="px-4 py-2 text-sm text-gray-500 border-l border-gray-200 text-left font-semibold">${(customer.total_spent || 0).toFixed(2)} ج.م</td>
            <td class="px-4 py-2 text-sm text-gray-500 text-left">${customer.last_order_date || 'لا يوجد'}</td>
        `;
        tableBody.appendChild(row);
    });
}

function generatePaginationControls(pagination) {
    const container = document.getElementById('paginationControls');
    container.innerHTML = '';
    
    const totalPages = pagination.total_pages;
    const currentPage = pagination.current_page;
    
    // Previous button
    const prevBtn = createPaginationButton('السابق', currentPage - 1, !pagination.has_previous);
    prevBtn.classList.add('rounded-l-md');
    container.appendChild(prevBtn);
    
    // Page numbers
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);
    
    // First page
    if (startPage > 1) {
        container.appendChild(createPaginationButton('1', 1, false));
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700';
            ellipsis.textContent = '...';
            container.appendChild(ellipsis);
        }
    }
    
    // Page range
    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === currentPage;
        const btn = createPaginationButton(i.toString(), i, false, isActive);
        container.appendChild(btn);
    }
    
    // Last page
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700';
            ellipsis.textContent = '...';
            container.appendChild(ellipsis);
        }
        container.appendChild(createPaginationButton(totalPages.toString(), totalPages, false));
    }
    
    // Next button
    const nextBtn = createPaginationButton('التالي', currentPage + 1, !pagination.has_next);
    nextBtn.classList.add('rounded-r-md');
    container.appendChild(nextBtn);
}

function createPaginationButton(text, page, disabled, active = false) {
    const button = document.createElement('button');
    button.textContent = text;
    button.className = `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
        active 
            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' 
            : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
    } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`;
    
    if (!disabled && !active) {
        button.addEventListener('click', () => {
            currentCustomersPage = page;
            loadCustomersData();
        });
    }
    
    button.disabled = disabled;
    return button;
}

function closeCustomersModal() {
    document.getElementById('customersModal').classList.add('hidden');
}

// Sales Performance Details
function showSalesPerformanceDetails() {
    const modal = document.getElementById('salesPerformanceModal');
    modal.classList.remove('hidden');
    
    document.getElementById('salesPerformanceLoading').classList.remove('hidden');
    document.getElementById('salesPerformanceContent').classList.add('hidden');
    
    fetch('/ar/sales/api/sales-performance-details/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateSalesPerformanceModal(data.data);
            }
        })
        .catch(error => console.error('Error:', error));
}

function populateSalesPerformanceModal(data) {
    document.getElementById('salesPerformanceLoading').classList.add('hidden');
    document.getElementById('salesPerformanceContent').classList.remove('hidden');
    
    document.getElementById('totalSalesAmount').textContent = data.total_sales.toFixed(2) + ' ج.م';
    document.getElementById('totalSalesOrders').textContent = data.total_orders;
    document.getElementById('thisMonthSales').textContent = data.this_month_sales.toFixed(2) + ' ج.م';
    document.getElementById('lastMonthSales').textContent = data.last_month_sales.toFixed(2) + ' ج.م';
    
    // Populate top centers table
    const centersTableBody = document.getElementById('topCentersTable');
    centersTableBody.innerHTML = '';
    
    data.top_centers.forEach(center => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-4 py-2 text-sm font-medium text-gray-900">${center.service_center__name || 'غير محدد'}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${center.total_sales ? center.total_sales.toFixed(2) : '0'} ج.م</td>
            <td class="px-4 py-2 text-sm text-gray-500">${center.order_count || 0}</td>
        `;
        centersTableBody.appendChild(row);
    });
    
    // Create simple monthly chart (would need Chart.js for full implementation)
    const chartContainer = document.getElementById('monthlyChart');
    chartContainer.innerHTML = '<p class="text-center text-gray-600">بيانات الأداء الشهري متاحة عبر واجهة برمجة التطبيقات</p>';
}

function closeSalesPerformanceModal() {
    document.getElementById('salesPerformanceModal').classList.add('hidden');
}

// Work Order Sales Details
function showWorkOrderSalesDetails() {
    const modal = document.getElementById('workOrderSalesModal');
    modal.classList.remove('hidden');
    
    document.getElementById('workOrderSalesLoading').classList.remove('hidden');
    document.getElementById('workOrderSalesContent').classList.add('hidden');
    
    fetch('/ar/sales/api/work-order-sales-details/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateWorkOrderSalesModal(data.data);
            }
        })
        .catch(error => console.error('Error:', error));
}

function populateWorkOrderSalesModal(data) {
    document.getElementById('workOrderSalesLoading').classList.add('hidden');
    document.getElementById('workOrderSalesContent').classList.remove('hidden');
    
    document.getElementById('workOrderTotalSales').textContent = data.total_sales;
    document.getElementById('workOrderTotalRevenue').textContent = data.total_revenue.toFixed(2) + ' ج.م';
    document.getElementById('workOrderLaborRevenue').textContent = data.labor_revenue.toFixed(2) + ' ج.م';
    document.getElementById('workOrderPartsRevenue').textContent = data.parts_revenue.toFixed(2) + ' ج.م';
    document.getElementById('laborPercentage').textContent = data.labor_percentage + '%';
    document.getElementById('partsPercentage').textContent = data.parts_percentage + '%';
    
    const tableBody = document.getElementById('workOrderSalesTable');
    tableBody.innerHTML = '';
    
    data.recent_sales.forEach(sale => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-4 py-2 text-sm font-medium text-gray-900">${sale.order_number}</td>
            <td class="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 cursor-pointer">${sale.work_order_number}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${sale.customer_name}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${sale.service_type}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${sale.total_amount.toFixed(2)} ج.م</td>
            <td class="px-4 py-2 text-sm text-gray-500">${sale.service_center}</td>
            <td class="px-4 py-2 text-sm text-gray-500">${formatDateTime(sale.created_at)}</td>
        `;
        tableBody.appendChild(row);
    });
}

function closeWorkOrderSalesModal() {
    document.getElementById('workOrderSalesModal').classList.add('hidden');
}

// Helper Functions
function formatDateTime(dateTimeString) {
    if (!dateTimeString) return 'غير محدد';
    const date = new Date(dateTimeString);
    return date.toLocaleDateString('ar-EG') + ' ' + date.toLocaleTimeString('ar-EG', {hour: '2-digit', minute:'2-digit'});
}

// Close modals when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const modals = [
        'totalOrdersModal', 'pendingOrdersModal', 'completedOrdersModal',
        'customersModal', 'salesPerformanceModal', 'workOrderSalesModal'
    ];
    
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.add('hidden');
                }
            });
        }
    });
    
    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            modals.forEach(modalId => {
                document.getElementById(modalId).classList.add('hidden');
            });
        }
    });
});

// Navigation Functions for Section Pages
function openInsurancePage() {
    window.location.href = '/sales/insurance/';
}

function openPaymentPage() {
    window.location.href = '/sales/payments/';
}

function openCustomerManagementPage() {
    window.location.href = '/sales/customer-management/';
}

function openOffersPage() {
    window.location.href = '/sales/offers/';
}

// Work Order Modal Functions
function showWorkOrderDetails(workOrderId) {
    // Show modal
    const modal = document.getElementById('workOrderModal');
    modal.classList.remove('hidden');
    
    // Show loading state
    document.getElementById('modalLoading').classList.remove('hidden');
    document.getElementById('modalError').classList.add('hidden');
    document.getElementById('modalContent').classList.add('hidden');
    
    // Fetch work order details
    fetch(`/ar/sales/api/work-order-details/${workOrderId}/`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to fetch work order details');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                populateWorkOrderModal(data.work_order);
            } else {
                throw new Error(data.error || 'Unknown error');
            }
        })
        .catch(error => {
            console.error('Error fetching work order details:', error);
            showModalError();
        });
}

function populateWorkOrderModal(workOrder) {
    // Hide loading, show content
    document.getElementById('modalLoading').classList.add('hidden');
    document.getElementById('modalError').classList.add('hidden');
    document.getElementById('modalContent').classList.remove('hidden');
    
    // Populate basic info
    document.getElementById('woNumber').textContent = workOrder.order_number || '';
    document.getElementById('woStatus').innerHTML = getStatusBadge(workOrder.status);
    document.getElementById('woCreatedAt').textContent = formatDate(workOrder.created_at);
    document.getElementById('woRequiredDate').textContent = formatDate(workOrder.required_date);
    
    // Populate customer and vehicle info
    document.getElementById('woCustomer').textContent = workOrder.customer_name || 'غير محدد';
    document.getElementById('woVehicle').textContent = workOrder.vehicle_info || 'غير محدد';
    document.getElementById('woVehiclePlate').textContent = workOrder.vehicle_plate || 'غير محدد';
    document.getElementById('woServiceCenter').textContent = workOrder.service_center || 'غير محدد';
    
    // Populate operations
    const operationsTableBody = document.getElementById('woOperations');
    operationsTableBody.innerHTML = '';
    if (workOrder.operations && workOrder.operations.length > 0) {
        workOrder.operations.forEach(operation => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 text-sm text-gray-900">${operation.name || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${operation.description || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${operation.technician || 'غير محدد'}</td>
                <td class="px-4 py-2">${getStatusBadge(operation.status)}</td>
            `;
            operationsTableBody.appendChild(row);
        });
    } else {
        operationsTableBody.innerHTML = '<tr><td colspan="4" class="px-4 py-2 text-center text-gray-500">لا توجد عمليات</td></tr>';
    }
    
    // Populate materials
    const materialsTableBody = document.getElementById('woMaterials');
    materialsTableBody.innerHTML = '';
    if (workOrder.materials && workOrder.materials.length > 0) {
        workOrder.materials.forEach(material => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 text-sm text-gray-900">${material.item_name || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${material.quantity_required || 0}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${material.quantity_used || 0}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${material.unit_price || 0} ج.م</td>
            `;
            materialsTableBody.appendChild(row);
        });
    } else {
        materialsTableBody.innerHTML = '<tr><td colspan="4" class="px-4 py-2 text-center text-gray-500">لا توجد مواد</td></tr>';
    }
    
    // Populate notes
    document.getElementById('woNotes').textContent = workOrder.notes || 'لا توجد ملاحظات';
    
    // Set view link
    document.getElementById('viewWorkOrderLink').href = `/ar/work-orders/${workOrder.id}/`;
}

function showModalError() {
    document.getElementById('modalLoading').classList.add('hidden');
    document.getElementById('modalContent').classList.add('hidden');
    document.getElementById('modalError').classList.remove('hidden');
}

function closeWorkOrderModal() {
    document.getElementById('workOrderModal').classList.add('hidden');
}

// Helper functions
function getStatusBadge(status) {
    const statusMap = {
        'pending': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">قيد الانتظار</span>',
        'in_progress': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">قيد التنفيذ</span>',
        'completed': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">مكتمل</span>',
        'on_hold': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">متوقف</span>',
        'cancelled': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">ملغي</span>',
        'draft': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">مسودة</span>'
    };
    return statusMap[status] || status;
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
}

// Close modal when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('workOrderModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeWorkOrderModal();
            }
        });
    }
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeWorkOrderModal();
        }
    });
    
    // Handle data-url click events for price list cards
    const clickableCards = document.querySelectorAll('[data-url]');
    clickableCards.forEach(card => {
        card.addEventListener('click', function() {
            const url = this.getAttribute('data-url');
            if (url) {
                window.location.href = url;
            }
        });
    });
});

// Action Dropdown Functions
function toggleInsuranceActions() {
    const dropdown = document.getElementById('insuranceActions');
    toggleDropdown(dropdown);
}

function togglePaymentActions() {
    const dropdown = document.getElementById('paymentActions');
    toggleDropdown(dropdown);
}

function toggleCustomerActions() {
    const dropdown = document.getElementById('customerActions');
    toggleDropdown(dropdown);
}

function togglePriceListActions() {
    const dropdown = document.getElementById('priceListActionsMenu');
    toggleDropdown(dropdown);
}

function toggleDropdown(dropdown) {
    // Close all other dropdowns first
    const allDropdowns = document.querySelectorAll('[id$="Actions"]');
    allDropdowns.forEach(dd => {
        if (dd !== dropdown) {
            dd.classList.add('hidden');
        }
    });
    
    // Toggle the requested dropdown
    dropdown.classList.toggle('hidden');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id$="Actions"]');
    const buttons = document.querySelectorAll('[onclick*="toggle"]');
    
    let clickedOnButton = false;
    buttons.forEach(button => {
        if (button.contains(event.target)) {
            clickedOnButton = true;
        }
    });
    
    if (!clickedOnButton) {
        dropdowns.forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    }
});

// API Test Functions (kept for debugging)
function testAPI() {
    const resultDiv = document.getElementById('apiTestResult');
    if (!resultDiv) return;
    
    resultDiv.innerHTML = '<span class="text-blue-600">جاري الاختبار...</span>';
    
    fetch('/ar/sales/api/test/')
        .then(response => {
            console.log('Test API Response:', response);
            return response.json();
        })
        .then(data => {
            console.log('Test API Data:', data);
            if (data.success) {
                resultDiv.innerHTML = `<span class="text-green-600">✓ API يعمل بنجاح! طلبات: ${data.data.orders_count}, عملاء: ${data.data.customers_count}</span>`;
            } else {
                resultDiv.innerHTML = `<span class="text-red-600">✗ خطأ: ${data.error}</span>`;
            }
        })
        .catch(error => {
            console.error('Test API Error:', error);
            resultDiv.innerHTML = `<span class="text-red-600">✗ خطأ في الاتصال: ${error.message}</span>`;
        });
}

function testTotalOrdersAPI() {
    const resultDiv = document.getElementById('apiTestResult');
    if (!resultDiv) return;
    
    resultDiv.innerHTML = '<span class="text-blue-600">جاري اختبار API الطلبات...</span>';
    
    fetch('/ar/sales/api/total-orders-details/')
        .then(response => {
            console.log('Total Orders API Response:', response);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Total Orders API Data:', data);
            if (data.success) {
                resultDiv.innerHTML = `<span class="text-green-600">✓ API الطلبات يعمل! العدد: ${data.data.total_count}, القيمة: ${data.data.total_value}</span>`;
            } else {
                resultDiv.innerHTML = `<span class="text-red-600">✗ خطأ في API الطلبات: ${data.error}</span>`;
            }
        })
        .catch(error => {
            console.error('Total Orders API Error:', error);
            resultDiv.innerHTML = `<span class="text-red-600">✗ خطأ في API الطلبات: ${error.message}</span>`;
        });
}
</script>
{% endblock %}
{% endblock %} 