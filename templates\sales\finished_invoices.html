{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .invoice-card {
        transition: all 0.3s ease;
    }
    .invoice-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .summary-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .filter-section {
        background: #f8fafc;
        border-radius: 12px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ page_title }}</h1>
                <p class="text-gray-600 mt-2">{{ page_subtitle }}</p>
            </div>
            <div class="flex space-x-3 space-x-reverse">
                <button onclick="exportInvoices()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    {% trans "تصدير" %}
                </button>
                <a href="{% url 'sales:dashboard' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة للوحة الرئيسية" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="summary-card rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{% trans "إجمالي الفواتير" %}</h3>
                    <p class="text-3xl font-bold">{{ totals.total_count }}</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-file-invoice"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-green-500">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{% trans "إجمالي الإيرادات" %}</h3>
                    <p class="text-3xl font-bold text-green-600">{{ totals.total_revenue|floatformat:2 }}</p>
                    <p class="text-sm text-gray-500">{% trans "ج.م" %}</p>
                </div>
                <div class="text-4xl text-green-500">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-blue-500">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{% trans "تكاليف العمالة" %}</h3>
                    <p class="text-3xl font-bold text-blue-600">{{ totals.total_labor_cost|floatformat:2 }}</p>
                    <p class="text-sm text-gray-500">{% trans "ج.م" %}</p>
                </div>
                <div class="text-4xl text-blue-500">
                    <i class="fas fa-user-cog"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-purple-500">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{% trans "تكاليف قطع الغيار" %}</h3>
                    <p class="text-3xl font-bold text-purple-600">{{ totals.total_parts_cost|floatformat:2 }}</p>
                    <p class="text-sm text-gray-500">{% trans "ج.م" %}</p>
                </div>
                <div class="text-4xl text-purple-500">
                    <i class="fas fa-cogs"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section p-6 mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "البحث والتصفية" %}</h2>
        </div>
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">{% trans "البحث" %}</label>
                <input type="text" name="search" id="search" value="{{ search_query }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" 
                       placeholder="{% trans 'رقم الفاتورة، العميل، أو رقم اللوحة' %}">
            </div>
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">{% trans "من تاريخ" %}</label>
                <input type="date" name="date_from" id="date_from" value="{{ current_filters.date_from }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">{% trans "إلى تاريخ" %}</label>
                <input type="date" name="date_to" id="date_to" value="{{ current_filters.date_to }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label for="service_center" class="block text-sm font-medium text-gray-700 mb-1">{% trans "مركز الخدمة" %}</label>
                <select name="service_center" id="service_center" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">{% trans "جميع المراكز" %}</option>
                    {% for center in service_centers %}
                        <option value="{{ center.id }}" {% if current_filters.service_center == center.id|stringformat:"s" %}selected{% endif %}>
                            {{ center.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="md:col-span-4 flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md flex items-center">
                    <i class="fas fa-search mr-2"></i>
                    {% trans "بحث" %}
                </button>
            </div>
        </form>
    </div>

    <!-- Invoices Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "الفواتير المكتملة" %}</h2>
        </div>
        
        {% if finished_invoices %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم الفاتورة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "العميل" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المركبة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "أمر العمل" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "مركز الخدمة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "التاريخ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المبلغ الإجمالي" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "عمالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "قطع غيار" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for invoice in finished_invoices %}
                        <tr class="invoice-card">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ invoice.order_number }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {% if invoice.customer.company_name %}
                                        {{ invoice.customer.company_name }}
                                    {% else %}
                                        {{ invoice.customer.first_name }} {{ invoice.customer.last_name }}
                                    {% endif %}
                                </div>
                                <div class="text-xs text-gray-500">{{ invoice.customer.phone }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if invoice.vehicle %}
                                    <div class="text-sm text-gray-900">{{ invoice.vehicle.make }} {{ invoice.vehicle.model }}</div>
                                    <div class="text-xs text-gray-500">{{ invoice.vehicle.license_plate }}</div>
                                {% else %}
                                    <span class="text-gray-400">-</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if invoice.work_order_number %}
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        {{ invoice.work_order_number }}
                                    </span>
                                {% else %}
                                    <span class="text-gray-400">-</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if invoice.service_center %}
                                    <div class="text-sm text-gray-900">{{ invoice.service_center.name }}</div>
                                {% else %}
                                    <span class="text-gray-400">-</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ invoice.order_date|date:"d/m/Y" }}</div>
                                {% if invoice.service_completion_date %}
                                    <div class="text-xs text-gray-400">{% trans "اكتمل:" %} {{ invoice.service_completion_date|date:"d/m/Y" }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-semibold text-gray-900">{{ invoice.total_amount|floatformat:2 }} {% trans "ج.م" %}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-blue-600">{{ invoice.labor_cost|floatformat:2 }} {% trans "ج.م" %}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-purple-600">{{ invoice.parts_cost|floatformat:2 }} {% trans "ج.م" %}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{% url 'sales:sales_order_detail' invoice.id %}" 
                                   class="text-indigo-600 hover:text-indigo-900 mr-3">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button onclick="viewInvoiceDetails('{{ invoice.id }}')" 
                                        class="text-green-600 hover:text-green-900 mr-3">
                                    <i class="fas fa-file-invoice"></i>
                                </button>
                                {% if invoice.work_order %}
                                    <button onclick="showWorkOrderDetails('{{ invoice.work_order.id }}')" 
                                            class="text-blue-600 hover:text-blue-900">
                                        <i class="fas fa-tools"></i>
                                    </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                {% trans "عرض" %}
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                {% trans "إلى" %}
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                {% trans "من" %}
                                <span class="font-medium">{{ page_obj.paginator.count }}</span>
                                {% trans "فاتورة" %}
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filters.date_from %}&date_from={{ current_filters.date_from }}{% endif %}{% if current_filters.date_to %}&date_to={{ current_filters.date_to }}{% endif %}{% if current_filters.service_center %}&service_center={{ current_filters.service_center }}{% endif %}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for i in page_obj.paginator.page_range %}
                                    {% if page_obj.number == i %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-700">
                                            {{ i }}
                                        </span>
                                    {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                                        <a href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filters.date_from %}&date_from={{ current_filters.date_from }}{% endif %}{% if current_filters.date_to %}&date_to={{ current_filters.date_to }}{% endif %}{% if current_filters.service_center %}&service_center={{ current_filters.service_center }}{% endif %}" 
                                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ i }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filters.date_from %}&date_from={{ current_filters.date_from }}{% endif %}{% if current_filters.date_to %}&date_to={{ current_filters.date_to }}{% endif %}{% if current_filters.service_center %}&service_center={{ current_filters.service_center }}{% endif %}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="p-8 text-center">
                <div class="text-gray-400 text-6xl mb-4">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد فواتير مكتملة" %}</h3>
                <p class="text-gray-500">{% trans "لم يتم العثور على فواتير مكتملة بالمعايير المحددة" %}</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
function exportInvoices() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'true');
    window.open('?' + params.toString(), '_blank');
}

function viewInvoiceDetails(invoiceId) {
    // Implement invoice details modal or redirect
    window.open(`/sales/orders/${invoiceId}/`, '_blank');
}

function showWorkOrderDetails(workOrderId) {
    // Implement work order details modal
    console.log('Show work order details for:', workOrderId);
}
</script>
{% endblock %} 