{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "طلبات المبيعات" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">{% trans "طلبات المبيعات" %}</h1>
            <p class="text-gray-600">{% trans "إدارة وعرض طلبات المبيعات" %}</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{% url 'sales:sales_order_create' %}" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "طلب مبيعات جديد" %}
            </a>
        </div>
    </div>
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    <!-- Search and Filter Form -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "بحث وتصفية" %}</h2>
        </div>
        <div class="p-6">
            <form method="get" class="flex flex-wrap gap-4">
                <div class="w-full md:w-1/4">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">{% trans "رقم الطلب" %}</label>
                    <input type="text" name="search" id="search" value="{{ current_search }}" class="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="{% trans 'بحث برقم الطلب' %}">
                </div>
                <div class="w-full md:w-1/4">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">{% trans "الحالة" %}</label>
                    <select name="status" id="status" class="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        <option value="">{% trans "جميع الحالات" %}</option>
                        <option value="draft" {% if current_status == 'draft' %}selected{% endif %}>{% trans "مسودة" %}</option>
                        <option value="confirmed" {% if current_status == 'confirmed' %}selected{% endif %}>{% trans "مؤكد" %}</option>
                        <option value="shipped" {% if current_status == 'shipped' %}selected{% endif %}>{% trans "تم الشحن" %}</option>
                        <option value="delivered" {% if current_status == 'delivered' %}selected{% endif %}>{% trans "تم التسليم" %}</option>
                        <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>{% trans "ملغي" %}</option>
                        <option value="returned" {% if current_status == 'returned' %}selected{% endif %}>{% trans "مرتجع" %}</option>
                    </select>
                </div>
                <div class="w-full md:w-1/4">
                    <label for="customer" class="block text-sm font-medium text-gray-700 mb-1">{% trans "العميل" %}</label>
                    <select name="customer" id="customer" class="shadow-sm focus:ring-green-500 focus:border-green-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        <option value="">{% trans "جميع العملاء" %}</option>
                        {% for customer in customers %}
                            <option value="{{ customer.id }}" {% if current_customer == customer.id|stringformat:"s" %}selected{% endif %}>{{ customer.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="w-full md:w-1/4 flex items-end">
                    <button type="submit" class="w-full bg-green-100 hover:bg-green-200 text-green-700 py-2 px-4 rounded-md flex justify-center items-center">
                        <i class="fas fa-filter {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "تصفية" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Sales Orders Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "قائمة طلبات المبيعات" %}</h2>
        </div>
        
        {% if sales_orders %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم الطلب" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "العميل" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "تاريخ الطلب" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الحالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المبلغ الإجمالي" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "إجمالي البنود" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in sales_orders %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.order_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.customer.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ order.order_date|date:"d/m/Y" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if order.status == 'draft' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {% trans "مسودة" %}
                                        </span>
                                    {% elif order.status == 'confirmed' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {% trans "مؤكد" %}
                                        </span>
                                    {% elif order.status == 'shipped' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {% trans "تم الشحن" %}
                                        </span>
                                    {% elif order.status == 'delivered' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            {% trans "تم التسليم" %}
                                        </span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            {% trans "ملغي" %}
                                        </span>
                                    {% elif order.status == 'returned' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">
                                            {% trans "مرتجع" %}
                                        </span>
                                    {% else %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {{ order.get_status_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.total_amount }} {% trans "ج.م" %}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.items.count }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'sales:sales_order_detail' order.id %}" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:sales_order_update' order.id %}" class="text-green-600 hover:text-green-900 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                {% trans "عرض" %}
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                {% trans "إلى" %}
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                {% trans "من" %}
                                <span class="font-medium">{{ paginator.count }}</span>
                                {% trans "نتيجة" %}
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_customer %}&customer={{ current_customer }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">{% trans "السابق" %}</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for i in paginator.page_range %}
                                    {% if page_obj.number == i %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-green-50 text-sm font-medium text-green-700">
                                            {{ i }}
                                        </span>
                                    {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                                        <a href="?page={{ i }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_customer %}&customer={{ current_customer }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ i }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_customer %}&customer={{ current_customer }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">{% trans "التالي" %}</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا توجد طلبات مبيعات" %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 