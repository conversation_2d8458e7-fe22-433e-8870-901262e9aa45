{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        transition: all 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .summary-gradient {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        color: white;
    }
    .labor-gradient {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white;
    }
    .parts-gradient {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        color: white;
    }
    .filter-section {
        background: #f8fafc;
        border-radius: 12px;
    }
    .item-row:hover {
        background-color: #f3f4f6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ page_title }}</h1>
                <p class="text-gray-600 mt-2">{{ page_subtitle }}</p>
            </div>
            <div class="flex space-x-3 space-x-reverse">
                <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    {% trans "تصدير التقرير" %}
                </button>
                <a href="{% url 'sales:dashboard' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة للوحة الرئيسية" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="summary-gradient rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{% trans "إجمالي العناصر" %}</h3>
                    <p class="text-3xl font-bold">{{ summary_stats.total_quantity|floatformat:0|default:0 }}</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-box-open"></i>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 border-r-4 border-blue-500">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{% trans "إجمالي الإيرادات" %}</h3>
                    <p class="text-3xl font-bold text-blue-600">{{ summary_stats.total_revenue|floatformat:2|default:0 }}</p>
                    <p class="text-sm text-gray-500">{% trans "ج.م" %}</p>
                </div>
                <div class="text-4xl text-blue-500">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>

        <div class="labor-gradient rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{% trans "إيرادات العمالة" %}</h3>
                    <p class="text-3xl font-bold">{{ summary_stats.labor_revenue|floatformat:2|default:0 }}</p>
                    <p class="text-sm opacity-80">{{ summary_stats.labor_count|default:0 }} {% trans "عنصر" %}</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-user-cog"></i>
                </div>
            </div>
        </div>

        <div class="parts-gradient rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">{% trans "إيرادات قطع الغيار" %}</h3>
                    <p class="text-3xl font-bold">{{ summary_stats.parts_revenue|floatformat:2|default:0 }}</p>
                    <p class="text-sm opacity-80">{{ summary_stats.parts_count|default:0 }} {% trans "عنصر" %}</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-cogs"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section p-6 mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "التصفية والبحث" %}</h2>
        </div>
        <form method="get" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">{% trans "من تاريخ" %}</label>
                <input type="date" name="date_from" id="date_from" value="{{ current_filters.date_from }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">{% trans "إلى تاريخ" %}</label>
                <input type="date" name="date_to" id="date_to" value="{{ current_filters.date_to }}" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label for="service_center" class="block text-sm font-medium text-gray-700 mb-1">{% trans "مركز الخدمة" %}</label>
                <select name="service_center" id="service_center" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">{% trans "جميع المراكز" %}</option>
                    {% for center in service_centers %}
                        <option value="{{ center.id }}" {% if current_filters.service_center == center.id|stringformat:"s" %}selected{% endif %}>
                            {{ center.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label for="item_type" class="block text-sm font-medium text-gray-700 mb-1">{% trans "نوع العنصر" %}</label>
                <select name="item_type" id="item_type" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    {% for value, label in item_type_choices %}
                        <option value="{{ value }}" {% if current_filters.item_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md flex items-center justify-center">
                    <i class="fas fa-search mr-2"></i>
                    {% trans "بحث" %}
                </button>
            </div>
        </form>
    </div>

    <!-- Top Items and Revenue by Center -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <!-- Top Selling Items -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">{% trans "أكثر العناصر مبيعاً" %}</h3>
            <div class="space-y-3">
                {% for item in top_items %}
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                        <p class="font-medium">{{ item.item__name }}</p>
                        <p class="text-sm text-gray-600">{{ item.item__sku }} - {{ item.get_item_type_display|default:item.item_type }}</p>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-600">{{ item.total_revenue|floatformat:2 }} {% trans "ج.م" %}</p>
                        <p class="text-sm text-gray-600">{{ item.total_sold|floatformat:0 }} {% trans "وحدة" %}</p>
                    </div>
                </div>
                {% empty %}
                <p class="text-gray-500 text-center py-4">{% trans "لا توجد بيانات متاحة" %}</p>
                {% endfor %}
            </div>
        </div>

        <!-- Revenue by Service Center -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">{% trans "الإيرادات حسب مركز الخدمة" %}</h3>
            <div class="space-y-3">
                {% for center in center_revenue %}
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                        <p class="font-medium">{{ center.sales_order__service_center__name|default:"غير محدد" }}</p>
                        <p class="text-sm text-gray-600">{{ center.item_count }} {% trans "عنصر" %}</p>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-blue-600">{{ center.total_revenue|floatformat:2 }} {% trans "ج.م" %}</p>
                    </div>
                </div>
                {% empty %}
                <p class="text-gray-500 text-center py-4">{% trans "لا توجد بيانات متاحة" %}</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Detailed Report Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "تقرير مفصل للعمليات وقطع الغيار" %}</h2>
        </div>
        
        {% if report_items %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "العنصر" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "النوع" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الكمية" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "سعر الوحدة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المجموع" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "العميل" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "مركز الخدمة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "التاريخ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم أمر العمل" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in report_items %}
                        <tr class="item-row">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ item.item.name }}</div>
                                <div class="text-sm text-gray-500">{{ item.item.sku }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if item.item_type == 'labor' %}bg-green-100 text-green-800
                                    {% elif item.item_type == 'parts' %}bg-red-100 text-red-800
                                    {% elif item.item_type == 'fee' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {% if item.item_type == 'labor' %}{% trans "عمالة" %}
                                    {% elif item.item_type == 'parts' %}{% trans "قطع غيار" %}
                                    {% elif item.item_type == 'fee' %}{% trans "رسوم" %}
                                    {% else %}{% trans "أخرى" %}{% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ item.quantity|floatformat:0 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ item.unit_price|floatformat:2 }} {% trans "ج.م" %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ item.total_price|floatformat:2 }} {% trans "ج.م" %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% if item.sales_order.customer.company_name %}
                                    {{ item.sales_order.customer.company_name }}
                                {% else %}
                                    {{ item.sales_order.customer.first_name }} {{ item.sales_order.customer.last_name }}
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.sales_order.service_center.name|default:"غير محدد" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ item.sales_order.order_date|date:"Y-m-d" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if item.sales_order.work_order_number %}
                                    <a href="{% url 'work_orders:work_order_detail' item.sales_order.work_order.id %}" class="text-blue-600 hover:text-blue-800">
                                        {{ item.sales_order.work_order_number }}
                                    </a>
                                {% else %}
                                    {% trans "غير محدد" %}
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            {% trans "السابق" %}
                        </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            {% trans "التالي" %}
                        </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            {% trans "عرض" %}
                            <span class="font-medium">{{ page_obj.start_index }}</span>
                            {% trans "إلى" %}
                            <span class="font-medium">{{ page_obj.end_index }}</span>
                            {% trans "من" %}
                            <span class="font-medium">{{ page_obj.paginator.count }}</span>
                            {% trans "نتيجة" %}
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">{% trans "السابق" %}</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span aria-current="page" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                        {{ num }}
                                    </span>
                                {% else %}
                                    <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        {{ num }}
                                    </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">{% trans "التالي" %}</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
        {% else %}
            <div class="px-6 py-12 text-center">
                <i class="fas fa-inbox text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد بيانات" %}</h3>
                <p class="text-gray-500">{% trans "لا توجد عمليات أو قطع غيار مباعة في الفترة المحددة" %}</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
function exportReport() {
    // Get current filters
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = "{% url 'sales:api_price_list_export' %}" + "?" + urlParams.toString();
    
    window.location.href = exportUrl;
}
</script>
{% endblock %} 