{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .edit-section {
        background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 100%);
        border-left: 4px solid #f97316;
    }
    .editable-field {
        transition: all 0.3s ease;
    }
    .editable-field:focus {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-edit ml-3 text-orange-500"></i>
                    {{ page_title }}
                </h1>
                <p class="text-gray-600 mt-2">{{ page_subtitle }}</p>
            </div>
            <div class="flex space-x-3 space-x-reverse">
                <button type="submit" form="editOrderForm" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-save mr-2"></i>
                    {% trans "حفظ التغييرات" %}
                </button>
                <a href="{% url 'sales:sales_order_preview' order.id %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-times mr-2"></i>
                    {% trans "إلغاء" %}
                </a>
            </div>
        </div>
    </div>

    <form id="editOrderForm" method="post">
        {% csrf_token %}
        
        <!-- Order Information -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Basic Info -->
            <div class="bg-white shadow rounded-lg p-6 edit-section">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-info-circle ml-2 text-orange-500"></i>
                    {% trans "معلومات الطلب" %}
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "رقم الطلب" %}</label>
                        <input type="text" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed" 
                               value="{{ order.order_number|default:order.id }}" 
                               readonly>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "تاريخ الطلب" %}</label>
                        <input type="date" 
                               name="order_date"
                               class="editable-field w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500" 
                               value="{{ order.order_date|date:'Y-m-d' }}">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "حالة الطلب" %}</label>
                        <select name="status" class="editable-field w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500">
                            <option value="draft" {% if order.status == 'draft' %}selected{% endif %}>{% trans "مسودة" %}</option>
                            <option value="confirmed" {% if order.status == 'confirmed' %}selected{% endif %}>{% trans "مؤكد" %}</option>
                            <option value="shipped" {% if order.status == 'shipped' %}selected{% endif %}>{% trans "مُرسل" %}</option>
                            <option value="delivered" {% if order.status == 'delivered' %}selected{% endif %}>{% trans "مُسلم" %}</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Customer Info (Read-only for reference) -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-user ml-2 text-green-500"></i>
                    {% trans "معلومات العميل" %}
                    <small class="mr-2 text-xs text-gray-500">({% trans "للمراجعة فقط" %})</small>
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">{% trans "اسم العميل" %}:</span>
                        <span class="font-semibold text-green-600">{{ order.customer.first_name }} {{ order.customer.last_name }}</span>
                    </div>
                    {% if order.customer.email %}
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">{% trans "البريد الإلكتروني" %}:</span>
                        <span class="font-medium">{{ order.customer.email }}</span>
                    </div>
                    {% endif %}
                    {% if order.customer.phone %}
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">{% trans "رقم الهاتف" %}:</span>
                        <span class="font-medium">{{ order.customer.phone }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Order Summary -->
            <div class="bg-white shadow rounded-lg p-6 edit-section">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-calculator ml-2 text-purple-500"></i>
                    {% trans "ملخص الطلب" %}
                </h3>
                <div class="space-y-4">
                    <div class="text-center">
                        <div class="text-sm text-gray-600 mb-1">{% trans "إجمالي العناصر" %}</div>
                        <div class="text-xl font-bold text-blue-600" id="totalItems">{{ order_items|length }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-sm text-gray-600 mb-1">{% trans "المبلغ الإجمالي" %}</div>
                        <div class="text-2xl font-bold text-emerald-600" id="totalAmount">
                            {{ order.total_amount|floatformat:2 }} {% trans "ج.م" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Items (Editable) -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-orange-50 to-orange-100">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-shopping-cart ml-2 text-orange-500"></i>
                        {% trans "عناصر الطلب" %}
                        <span class="mr-2 text-sm bg-orange-200 text-orange-800 px-2 py-1 rounded-full">{{ order_items|length }}</span>
                    </h3>
                    <button type="button" class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm flex items-center">
                        <i class="fas fa-plus mr-1"></i>
                        {% trans "إضافة صنف" %}
                    </button>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr class="divide-x divide-gray-200">
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الصنف" %}
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الكمية" %}
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "سعر الوحدة" %}
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الخصم" %}
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المجموع" %}
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "إجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="orderItems">
                        {% for item in order_items %}
                        <tr class="hover:bg-orange-50 item-row">
                            <td class="px-6 py-4 text-right border-r border-gray-200">
                                <div class="flex items-center justify-end">
                                    <i class="fas fa-cube ml-2 text-orange-400"></i>
                                    <span class="font-medium text-gray-900">{{ item.item.name }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-right border-r border-gray-200">
                                <input type="number" 
                                       name="quantity_{{ item.id }}"
                                       class="editable-field w-20 px-2 py-1 border border-gray-300 rounded text-center focus:ring-orange-500 focus:border-orange-500" 
                                       value="{{ item.quantity }}"
                                       min="1">
                            </td>
                            <td class="px-6 py-4 text-right border-r border-gray-200">
                                <input type="number" 
                                       name="unit_price_{{ item.id }}"
                                       class="editable-field w-24 px-2 py-1 border border-gray-300 rounded text-center focus:ring-orange-500 focus:border-orange-500" 
                                       value="{{ item.unit_price }}"
                                       step="0.01"
                                       min="0">
                            </td>
                            <td class="px-6 py-4 text-right border-r border-gray-200">
                                <input type="number" 
                                       name="discount_{{ item.id }}"
                                       class="editable-field w-24 px-2 py-1 border border-gray-300 rounded text-center focus:ring-orange-500 focus:border-orange-500" 
                                       value="{{ item.discount|default:0 }}"
                                       step="0.01"
                                       min="0">
                            </td>
                            <td class="px-6 py-4 text-right border-r border-gray-200">
                                <span class="font-bold text-emerald-600 item-total">{{ item.total_price|floatformat:2 }} {% trans "ج.م" %}</span>
                            </td>
                            <td class="px-6 py-4 text-right">
                                <button type="button" class="text-red-600 hover:text-red-900 remove-item" title="{% trans 'حذف' %}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                <i class="fas fa-box-open text-4xl mb-2"></i>
                                <p>{% trans "لا توجد عناصر في هذا الطلب" %}</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </form>

    <!-- Action Buttons (Footer) -->
    <div class="mt-8 flex justify-center space-x-4 space-x-reverse">
        <button type="submit" form="editOrderForm" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center text-lg">
            <i class="fas fa-save mr-2"></i>
            {% trans "حفظ جميع التغييرات" %}
        </button>
        <a href="{% url 'sales:sales_order_preview' order.id %}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg flex items-center text-lg">
            <i class="fas fa-eye mr-2"></i>
            {% trans "معاينة بدون حفظ" %}
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-calculate totals when quantities or prices change
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[type="number"]');
    
    inputs.forEach(input => {
        input.addEventListener('input', calculateRowTotal);
    });
    
    function calculateRowTotal(event) {
        const row = event.target.closest('.item-row');
        const quantity = parseFloat(row.querySelector('input[name^="quantity_"]').value) || 0;
        const unitPrice = parseFloat(row.querySelector('input[name^="unit_price_"]').value) || 0;
        const discount = parseFloat(row.querySelector('input[name^="discount_"]').value) || 0;
        
        const total = (quantity * unitPrice) - discount;
        row.querySelector('.item-total').textContent = total.toFixed(2) + ' ج.م';
        
        // Recalculate order total
        calculateOrderTotal();
    }
    
    function calculateOrderTotal() {
        const rows = document.querySelectorAll('.item-row');
        let totalItems = 0;
        let totalAmount = 0;
        
        rows.forEach(row => {
            const quantity = parseFloat(row.querySelector('input[name^="quantity_"]').value) || 0;
            const unitPrice = parseFloat(row.querySelector('input[name^="unit_price_"]').value) || 0;
            const discount = parseFloat(row.querySelector('input[name^="discount_"]').value) || 0;
            
            totalItems += quantity;
            totalAmount += (quantity * unitPrice) - discount;
        });
        
        document.getElementById('totalItems').textContent = totalItems;
        document.getElementById('totalAmount').textContent = totalAmount.toFixed(2) + ' ج.م';
    }
    
    // Remove item functionality
    document.querySelectorAll('.remove-item').forEach(button => {
        button.addEventListener('click', function() {
            if (confirm('{% trans "هل أنت متأكد من حذف هذا العنصر؟" %}')) {
                this.closest('.item-row').remove();
                calculateOrderTotal();
            }
        });
    });
});
</script>
{% endblock %} 