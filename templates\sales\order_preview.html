{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .info-section {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-left: 4px solid #3b82f6;
    }
    .status-badge {
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-file-alt ml-3 text-blue-500"></i>
                    {{ page_title }}
                </h1>
                <p class="text-gray-600 mt-2">{{ page_subtitle }}</p>
            </div>
            <div class="flex space-x-3 space-x-reverse">
                {% if order.status != 'delivered' and order.status != 'cancelled' %}
                <a href="{% url 'sales:sales_order_edit' order.id %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-edit mr-2"></i>
                    {% trans "تعديل الطلب" %}
                </a>
                {% endif %}
                <a href="{% url 'sales:recent_orders' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة للطلبات" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Order Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Basic Info -->
        <div class="bg-white shadow rounded-lg p-6 info-section">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-info-circle ml-2 text-blue-500"></i>
                {% trans "معلومات الطلب" %}
            </h3>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">{% trans "رقم الطلب" %}:</span>
                    <span class="font-semibold text-blue-600">{{ order.order_number|default:order.id }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">{% trans "تاريخ الإنشاء" %}:</span>
                    <span class="font-medium">{{ order.created_at|date:"Y/m/d H:i" }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">{% trans "تاريخ الطلب" %}:</span>
                    <span class="font-medium">{{ order.order_date|date:"Y/m/d" }}</span>
                </div>
            </div>
        </div>

        <!-- Customer Info -->
        <div class="bg-white shadow rounded-lg p-6 info-section">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-user ml-2 text-green-500"></i>
                {% trans "معلومات العميل" %}
            </h3>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">{% trans "اسم العميل" %}:</span>
                    <span class="font-semibold text-green-600">{{ order.customer.first_name }} {{ order.customer.last_name }}</span>
                </div>
                {% if order.customer.email %}
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">{% trans "البريد الإلكتروني" %}:</span>
                    <span class="font-medium">{{ order.customer.email }}</span>
                </div>
                {% endif %}
                {% if order.customer.phone %}
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">{% trans "رقم الهاتف" %}:</span>
                    <span class="font-medium">{{ order.customer.phone }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Status & Amount -->
        <div class="bg-white shadow rounded-lg p-6 info-section">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-chart-line ml-2 text-purple-500"></i>
                {% trans "حالة ومبلغ الطلب" %}
            </h3>
            <div class="space-y-4">
                <div class="text-center">
                    {% if order.status == 'draft' or order.status == 'confirmed' %}
                        <span class="status-badge inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <i class="fas fa-clock ml-1"></i>
                            {% trans "قيد التنفيذ" %}
                        </span>
                    {% elif order.status == 'delivered' %}
                        <span class="status-badge inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle ml-1"></i>
                            {% trans "مكتملة" %}
                        </span>
                    {% elif order.status == 'shipped' %}
                        <span class="status-badge inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                            <i class="fas fa-shipping-fast ml-1"></i>
                            {% trans "مُرسلة" %}
                        </span>
                    {% elif order.status == 'cancelled' %}
                        <span class="status-badge inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times-circle ml-1"></i>
                            {% trans "ملغية" %}
                        </span>
                    {% endif %}
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-emerald-600 flex items-center justify-center">
                        <i class="fas fa-coins ml-2"></i>
                        {{ order.total_amount|floatformat:2 }} {% trans "ج.م" %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-blue-100">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-shopping-cart ml-2 text-blue-500"></i>
                {% trans "عناصر الطلب" %}
                <span class="mr-2 text-sm bg-blue-200 text-blue-800 px-2 py-1 rounded-full">{{ order_items|length }}</span>
            </h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr class="divide-x divide-gray-200">
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الصنف" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الكمية" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "سعر الوحدة" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "الخصم" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "المجموع" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for item in order_items %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 text-right border-r border-gray-200">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-cube ml-2 text-blue-400"></i>
                                <span class="font-medium text-gray-900">{{ item.item.name }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-right border-r border-gray-200">
                            <span class="text-gray-900 font-medium">{{ item.quantity }}</span>
                        </td>
                        <td class="px-6 py-4 text-right border-r border-gray-200">
                            <span class="text-gray-900">{{ item.unit_price|floatformat:2 }} {% trans "ج.م" %}</span>
                        </td>
                        <td class="px-6 py-4 text-right border-r border-gray-200">
                            <span class="text-red-600">{{ item.discount|default:0|floatformat:2 }} {% trans "ج.م" %}</span>
                        </td>
                        <td class="px-6 py-4 text-right">
                            <span class="font-bold text-emerald-600">{{ item.total_price|floatformat:2 }} {% trans "ج.م" %}</span>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                            <i class="fas fa-box-open text-4xl mb-2"></i>
                            <p>{% trans "لا توجد عناصر في هذا الطلب" %}</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any interactive features here
</script>
{% endblock %} 