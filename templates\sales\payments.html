{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "المدفوعات والمالية" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-credit-card text-green-500 mr-3"></i>
                    {% trans "المدفوعات والمالية" %}
                </h1>
                <p class="text-gray-600 mt-2">{% trans "إدارة شاملة للمدفوعات والشؤون المالية" %}</p>
            </div>
            <a href="{% url 'sales:dashboard' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-arrow-right mr-2"></i>
                {% trans "العودة للوحة التحكم" %}
            </a>
        </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-emerald-50 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-emerald-100 text-emerald-600">
                    <i class="fas fa-money-check-alt text-2xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-emerald-600 text-sm font-medium">طرق الدفع</p>
                    <p class="text-3xl font-bold text-emerald-800">{{ payment_methods }}</p>
                    <p class="text-xs text-emerald-500">طرق متاحة</p>
                </div>
            </div>
        </div>

        <div class="bg-blue-50 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-receipt text-2xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-blue-600 text-sm font-medium">المدفوعات</p>
                    <p class="text-3xl font-bold text-blue-800">{{ total_payments }}</p>
                    <p class="text-xs text-blue-500">دفعة هذا الشهر</p>
                </div>
            </div>
        </div>

        <div class="bg-yellow-50 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-file-invoice-dollar text-2xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-yellow-600 text-sm font-medium">الفواتير</p>
                    <p class="text-3xl font-bold text-yellow-800">{{ pending_invoices }}</p>
                    <p class="text-xs text-yellow-500">فاتورة معلقة</p>
                </div>
            </div>
        </div>

        <div class="bg-red-50 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-percentage text-2xl"></i>
                </div>
                <div class="mr-4">
                    <p class="text-red-600 text-sm font-medium">أنواع الخصم</p>
                    <p class="text-3xl font-bold text-red-800">{{ discount_types }}</p>
                    <p class="text-xs text-red-500">نوع خصم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods Section -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-money-check-alt text-emerald-500 mr-2"></i>
                طرق الدفع المتاحة
            </h3>
        </div>
        <div class="p-6">
            {% if payment_methods_stats %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for method in payment_methods_stats %}
                <div class="border border-emerald-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            {% if 'نقد' in method.name or 'كاش' in method.name %}
                                <i class="fas fa-money-bill-wave text-emerald-600 text-2xl mr-3"></i>
                            {% elif 'بطاقة' in method.name or 'كارت' in method.name %}
                                <i class="fas fa-credit-card text-blue-600 text-2xl mr-3"></i>
                            {% elif 'تحويل' in method.name or 'بنك' in method.name %}
                                <i class="fas fa-university text-purple-600 text-2xl mr-3"></i>
                            {% else %}
                                <i class="fas fa-money-check-alt text-gray-600 text-2xl mr-3"></i>
                            {% endif %}
                            <div>
                                <div class="text-sm font-medium">{{ method.name }}</div>
                                <div class="text-xs text-gray-500">{{ method.description }}</div>
                            </div>
                        </div>
                        <span class="text-sm {% if method.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %} px-2 py-1 rounded-full">{{ method.status_display }}</span>
                    </div>
                    <div class="text-xs text-gray-600">{{ method.transactions_count }} معاملة هذا الشهر</div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-12 text-gray-500">
                <div class="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-money-check-alt text-2xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد طرق دفع محددة</h3>
                <p class="text-sm text-gray-500">قم بإضافة طرق دفع لعرضها هنا</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Recent Payments and Invoices -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Recent Payments -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-receipt text-blue-500 mr-2"></i>
                    أحدث المدفوعات
                </h3>
            </div>
            <div class="p-6">
                {% if recent_payments %}
                    <div class="space-y-4">
                        {% for payment in recent_payments %}
                        <div class="flex items-center justify-between p-4 border {% if payment.status == 'completed' %}border-blue-200{% elif payment.status == 'pending' %}border-yellow-200{% else %}border-gray-200{% endif %} rounded-lg">
                            <div>
                                <div class="text-sm font-medium">دفعة #{{ payment.payment_number }}</div>
                                <div class="text-xs text-gray-500">{{ payment.customer_name }} - {{ payment.payment_method }}</div>
                                <div class="text-xs {% if payment.status == 'completed' %}text-blue-600{% elif payment.status == 'pending' %}text-yellow-600{% else %}text-gray-600{% endif %}">{{ payment.status_description }}</div>
                            </div>
                            <div class="text-left">
                                <div class="text-sm font-bold text-green-600">{{ payment.amount }} ج.م</div>
                                <div class="text-xs text-gray-500">{{ payment.payment_date|date:"d/m/Y" }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12 text-gray-500">
                        <div class="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-credit-card text-2xl text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مدفوعات حديثة</h3>
                        <p class="text-sm text-gray-500">ستظهر المدفوعات الجديدة هنا عند إتمام الدفع</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Pending Invoices -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-file-invoice-dollar text-yellow-500 mr-2"></i>
                    الفواتير المعلقة
                </h3>
            </div>
            <div class="p-6">
                {% if pending_invoices_list %}
                    <div class="space-y-4">
                        {% for invoice in pending_invoices_list %}
                        <div class="flex items-center justify-between p-4 border {% if invoice.status == 'overdue' %}border-red-200{% elif invoice.status == 'due_today' %}border-yellow-200{% else %}border-blue-200{% endif %} rounded-lg">
                            <div>
                                <div class="text-sm font-medium">فاتورة #{{ invoice.invoice_number }}</div>
                                <div class="text-xs text-gray-500">{{ invoice.customer_name }} - {{ invoice.status_description }}</div>
                                <div class="text-xs {% if invoice.status == 'overdue' %}text-red-600{% elif invoice.status == 'due_today' %}text-yellow-600{% else %}text-blue-600{% endif %}">{{ invoice.due_date|date:"d/m/Y" }}</div>
                            </div>
                            <div class="text-left">
                                <div class="text-sm font-bold {% if invoice.status == 'overdue' %}text-red-600{% elif invoice.status == 'due_today' %}text-yellow-600{% else %}text-blue-600{% endif %}">{{ invoice.amount_due }} ج.م</div>
                                <span class="text-xs {% if invoice.status == 'overdue' %}bg-red-100 text-red-800{% elif invoice.status == 'due_today' %}bg-yellow-100 text-yellow-800{% else %}bg-blue-100 text-blue-800{% endif %} px-2 py-1 rounded-full">{{ invoice.status_display }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12 text-gray-500">
                        <div class="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-file-invoice-dollar text-2xl text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد فواتير معلقة</h3>
                        <p class="text-sm text-gray-500">جميع الفواتير مدفوعة أو لا توجد فواتير مستحقة حالياً</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Financial Statistics -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-chart-pie text-purple-500 mr-2"></i>
                الإحصائيات المالية
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600">{{ total_payments_this_month|floatformat:2 }}</div>
                    <div class="text-sm text-gray-600">إجمالي المدفوعات</div>
                    <div class="text-xs text-green-500">هذا الشهر</div>
                </div>
                
                <div class="text-center">
                    <div class="text-3xl font-bold text-yellow-600">{{ total_pending_invoices|floatformat:2 }}</div>
                    <div class="text-sm text-gray-600">الفواتير المعلقة</div>
                    <div class="text-xs text-yellow-500">تحتاج متابعة</div>
                </div>
                
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600">{{ payment_success_rate }}%</div>
                    <div class="text-sm text-gray-600">معدل نجاح الدفع</div>
                    <div class="text-xs text-blue-500">آخر 30 يوم</div>
                </div>
                
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600">{{ average_collection_days }}</div>
                    <div class="text-sm text-gray-600">متوسط أيام التحصيل</div>
                    <div class="text-xs text-purple-500">{% if average_collection_days <= 15 %}أداء ممتاز{% elif average_collection_days <= 30 %}أداء جيد{% else %}يحتاج تحسين{% endif %}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Discount Types -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-percentage text-red-500 mr-2"></i>
                أنواع الخصومات المتاحة
            </h3>
        </div>
        <div class="p-6">
            {% if discount_types_list %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {% for discount in discount_types_list %}
                <div class="border border-red-200 rounded-lg p-4 text-center">
                    {% if 'مميز' in discount.name or 'VIP' in discount.name %}
                        <i class="fas fa-star text-red-500 text-2xl mb-2"></i>
                    {% elif 'كمية' in discount.name or 'bulk' in discount.name %}
                        <i class="fas fa-shopping-cart text-blue-500 text-2xl mb-2"></i>
                    {% elif 'مقدم' in discount.name or 'advance' in discount.name %}
                        <i class="fas fa-calendar text-green-500 text-2xl mb-2"></i>
                    {% elif 'موسم' in discount.name or 'season' in discount.name %}
                        <i class="fas fa-gift text-purple-500 text-2xl mb-2"></i>
                    {% else %}
                        <i class="fas fa-percentage text-gray-500 text-2xl mb-2"></i>
                    {% endif %}
                    <div class="text-sm font-medium">{{ discount.name }}</div>
                    <div class="text-xs text-gray-500">{{ discount.percentage_range }}</div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-12 text-gray-500">
                <div class="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-percentage text-2xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أنواع خصومات محددة</h3>
                <p class="text-sm text-gray-500">قم بإضافة أنواع خصومات لعرضها هنا</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 