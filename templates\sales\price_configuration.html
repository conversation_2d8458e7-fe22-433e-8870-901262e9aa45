{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .pricing-card {
        transition: all 0.3s ease;
    }
    .pricing-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .filter-section {
        background: #f8fafc;
        border-radius: 12px;
    }
    .pricing-row:hover {
        background-color: #f3f4f6;
    }
    .operation-badge {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white;
    }
    .part-badge {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ page_title }}</h1>
                <p class="text-gray-600 mt-2">{{ page_subtitle }}</p>
            </div>
            <div class="flex space-x-3 space-x-reverse">
                <button onclick="exportPricing()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    {% trans "تصدير الأسعار" %}
                </button>
                <a href="{% url 'sales:dashboard' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة للوحة الرئيسية" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
            <a href="#operations" onclick="showTab('operations')" id="operations-tab" class="border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600" aria-current="page">
                {% trans "أسعار العمليات" %}
            </a>
            <a href="#parts" onclick="showTab('parts')" id="parts-tab" class="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                {% trans "أسعار قطع الغيار" %}
            </a>
        </nav>
    </div>

    <!-- Filter Section -->
    <div class="filter-section p-6 mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "التصفية والبحث" %}</h2>
        </div>
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="service_center" class="block text-sm font-medium text-gray-700 mb-1">{% trans "مركز الخدمة" %}</label>
                <select name="service_center" id="service_center" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">{% trans "جميع المراكز" %}</option>
                    {% for center in service_centers %}
                        <option value="{{ center.id }}">{{ center.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label for="company" class="block text-sm font-medium text-gray-700 mb-1">{% trans "الشركة" %}</label>
                <select name="company" id="company" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">{% trans "جميع الشركات" %}</option>
                    {% for company in companies %}
                        <option value="{{ company.id }}">{{ company.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label for="pricing_type" class="block text-sm font-medium text-gray-700 mb-1">{% trans "نوع التسعير" %}</label>
                <select name="pricing_type" id="pricing_type" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="operations">{% trans "العمليات" %}</option>
                    <option value="parts">{% trans "قطع الغيار" %}</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="button" onclick="filterPricing()" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md flex items-center justify-center">
                    <i class="fas fa-search mr-2"></i>
                    {% trans "بحث" %}
                </button>
            </div>
        </form>
    </div>

    <!-- Operations Pricing Tab -->
    <div id="operations-content" class="tab-content">
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "أسعار العمليات" %}</h2>
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                    <i class="fas fa-plus mr-1"></i>
                    {% trans "إضافة تسعير" %}
                </button>
            </div>
            
            {% if operation_pricing %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "نوع العملية" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "ماركة المركبة" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "موديل المركبة" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "السعر الأساسي" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "ساعات العمل" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "سعر الساعة" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "السعر الإجمالي" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الموقع" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الإجراءات" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for pricing in operation_pricing %}
                            <tr class="pricing-row">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="operation-badge px-2 py-1 text-xs font-semibold rounded-full">
                                            {{ pricing.operation_type.name }}
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ pricing.vehicle_make.name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ pricing.vehicle_model.name|default:"جميع الموديلات" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ pricing.base_price|floatformat:2 }} {% trans "ج.م" %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ pricing.labor_hours|floatformat:1 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ pricing.labor_rate|floatformat:2 }} {% trans "ج.م" %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ pricing.total_price|floatformat:2 }} {% trans "ج.م" %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if pricing.service_center %}
                                        {{ pricing.service_center.name }}
                                    {% elif pricing.company %}
                                        {{ pricing.company.name }}
                                    {% elif pricing.franchise %}
                                        {{ pricing.franchise.name }}
                                    {% else %}
                                        {% trans "عام" %}
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="px-6 py-12 text-center">
                    <i class="fas fa-cog text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد أسعار عمليات" %}</h3>
                    <p class="text-gray-500">{% trans "لم يتم تكوين أي أسعار للعمليات بعد" %}</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Parts Pricing Tab -->
    <div id="parts-content" class="tab-content hidden">
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "أسعار قطع الغيار" %}</h2>
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                    <i class="fas fa-plus mr-1"></i>
                    {% trans "إضافة تسعير" %}
                </button>
            </div>
            
            {% if part_pricing %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "قطعة الغيار" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "نوع العملية" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "ماركة المركبة" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "موديل المركبة" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "السعر" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "تسعير خاص" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الموقع" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {% trans "الإجراءات" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for pricing in part_pricing %}
                            <tr class="pricing-row">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ pricing.item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ pricing.item.sku }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ pricing.operation_type.name|default:"جميع العمليات" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ pricing.vehicle_make.name|default:"جميع الماركات" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ pricing.vehicle_model.name|default:"جميع الموديلات" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ pricing.price|floatformat:2 }} {% trans "ج.م" %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if pricing.is_special_pricing %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {% trans "خاص" %}
                                        </span>
                                    {% else %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {% trans "عادي" %}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if pricing.service_center %}
                                        {{ pricing.service_center.name }}
                                    {% elif pricing.company %}
                                        {{ pricing.company.name }}
                                    {% elif pricing.franchise %}
                                        {{ pricing.franchise.name }}
                                    {% else %}
                                        {% trans "عام" %}
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="px-6 py-12 text-center">
                    <i class="fas fa-wrench text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد أسعار قطع غيار" %}</h3>
                    <p class="text-gray-500">{% trans "لم يتم تكوين أي أسعار لقطع الغيار بعد" %}</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active styles from all tabs
    document.querySelectorAll('nav a').forEach(tab => {
        tab.classList.remove('border-blue-500', 'text-blue-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');
    
    // Add active styles to selected tab
    const activeTab = document.getElementById(tabName + '-tab');
    activeTab.classList.remove('border-transparent', 'text-gray-500');
    activeTab.classList.add('border-blue-500', 'text-blue-600');
}

function filterPricing() {
    const serviceCenter = document.getElementById('service_center').value;
    const company = document.getElementById('company').value;
    const pricingType = document.getElementById('pricing_type').value;
    
    // Build query parameters
    const params = new URLSearchParams();
    if (serviceCenter) params.append('service_center', serviceCenter);
    if (company) params.append('company', company);
    if (pricingType) params.append('type', pricingType);
    
    // Reload page with filters
    window.location.href = window.location.pathname + '?' + params.toString();
}

function exportPricing() {
    const pricingType = document.getElementById('pricing_type').value;
    const serviceCenter = document.getElementById('service_center').value;
    const company = document.getElementById('company').value;
    
    // Build export URL
    const params = new URLSearchParams();
    params.append('type', pricingType);
    if (serviceCenter) params.append('service_center', serviceCenter);
    if (company) params.append('company', company);
    
    const exportUrl = "{% url 'sales:api_price_list_export' %}" + "?" + params.toString();
    window.location.href = exportUrl;
}
</script>
{% endblock %} 