{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}
    {% if object %}
        {% trans "تعديل طلب المبيعات" %}
    {% else %}
        {% trans "طلب مبيعات جديد" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">
                {% if object %}
                    {% trans "تعديل طلب المبيعات" %}
                {% else %}
                    {% trans "طلب مبيعات جديد" %}
                {% endif %}
            </h1>
            <p class="text-gray-600">
                {% if object %}
                    {{ object.order_number }}
                {% else %}
                    {% trans "إنشاء طلب مبيعات جديد" %}
                {% endif %}
            </p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{% url 'sales:sales_order_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "العودة" %}
            </a>
        </div>
    </div>
    
    <!-- Form Section -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "معلومات طلب المبيعات" %}</h2>
        </div>
        <div class="p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Display form errors if any -->
                {% if form.errors %}
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                        <p class="font-bold">{% trans "يرجى تصحيح الأخطاء التالية:" %}</p>
                        <ul class="list-disc {% if LANGUAGE_CODE == 'ar' %}mr-5{% else %}ml-5{% endif %} mt-2">
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <li>{{ field.label }}: {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                            {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Customer -->
                    <div>
                        <label for="{{ form.customer.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "العميل" %} <span class="text-red-500">*</span>
                        </label>
                        {{ form.customer }}
                        {% if form.customer.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.customer.help_text }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Order Number -->
                    <div>
                        <label for="{{ form.order_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "رقم الطلب" %} <span class="text-red-500">*</span>
                        </label>
                        {{ form.order_number }}
                        {% if form.order_number.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.order_number.help_text }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Order Date -->
                    <div>
                        <label for="{{ form.order_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "تاريخ الطلب" %} <span class="text-red-500">*</span>
                        </label>
                        {{ form.order_date }}
                        {% if form.order_date.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.order_date.help_text }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Status (only for edit) -->
                    {% if object %}
                        <div>
                            <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                {% trans "الحالة" %}
                            </label>
                            {{ form.status }}
                            {% if form.status.help_text %}
                                <p class="mt-1 text-xs text-gray-500">{{ form.status.help_text }}</p>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
                
                <!-- Shipping Address -->
                <div>
                    <label for="{{ form.shipping_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {% trans "عنوان الشحن" %}
                    </label>
                    {{ form.shipping_address }}
                    {% if form.shipping_address.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.shipping_address.help_text }}</p>
                    {% endif %}
                </div>
                
                <!-- Notes -->
                <div>
                    <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {% trans "ملاحظات" %}
                    </label>
                    {{ form.notes }}
                    {% if form.notes.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.notes.help_text }}</p>
                    {% endif %}
                </div>
                
                <div class="pt-5 border-t border-gray-200 flex justify-end">
                    <button type="submit" class="bg-green-500 hover:bg-green-600 text-white py-2 px-6 rounded-lg">
                        {% if object %}
                            {% trans "حفظ التغييرات" %}
                        {% else %}
                            {% trans "إنشاء طلب المبيعات" %}
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    {% if object %}
        <!-- Items Section (only for edit) -->
        <div class="mt-8 bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "بنود الطلب" %}</h2>
                <button id="add-item-btn" class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded-lg text-sm">
                    <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                    {% trans "إضافة بند" %}
                </button>
            </div>
            
            <div id="items-container" class="p-6">
                {% if object.items.all %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {% trans "الصنف" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {% trans "الكمية" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {% trans "السعر" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {% trans "الخصم" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {% trans "الإجمالي" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {% trans "الإجراءات" %}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for item in object.items.all %}
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ item.item.name }}</div>
                                            <div class="text-xs text-gray-500">{{ item.item.sku }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ item.quantity }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ item.unit_price }} {% trans "ج.م" %}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ item.discount }} {% trans "ج.م" %}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ item.total_price }} {% trans "ج.م" %}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-blue-600 hover:text-blue-900 edit-item" data-id="{{ item.id }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-600 hover:text-red-900 delete-item {% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %}" data-id="{{ item.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                {% endfor %}
                                <!-- Summary row -->
                                <tr class="bg-gray-50">
                                    <td colspan="4" class="px-6 py-4 text-right whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{% trans "الإجمالي" %}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-bold text-gray-900">
                                            {{ object.total_amount }} {% trans "ج.م" %}
                                        </div>
                                    </td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-gray-500">
                        {% trans "لا توجد بنود في هذا الطلب حتى الآن" %}
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Item Modal (for adding/editing items) -->
        <div id="item-modal" class="fixed z-10 inset-0 overflow-y-auto hidden">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                    <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                <div class="inline-block align-bottom bg-white rounded-lg text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            {% trans "إضافة بند جديد" %}
                        </h3>
                        <div class="mt-4">
                            <form id="item-form" class="space-y-4">
                                <input type="hidden" id="item-id" name="item_id">
                                
                                <div>
                                    <label for="item" class="block text-sm font-medium text-gray-700">{% trans "الصنف" %}</label>
                                    <select id="item" name="item" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50" required>
                                        <option value="">{% trans "اختر الصنف" %}</option>
                                        <!-- Items will be loaded via JavaScript -->
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="quantity" class="block text-sm font-medium text-gray-700">{% trans "الكمية" %}</label>
                                    <input type="number" id="quantity" name="quantity" min="1" step="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50" required>
                                </div>
                                
                                <div>
                                    <label for="unit_price" class="block text-sm font-medium text-gray-700">{% trans "سعر الوحدة" %}</label>
                                    <input type="number" id="unit_price" name="unit_price" min="0" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50" required>
                                </div>
                                
                                <div>
                                    <label for="discount" class="block text-sm font-medium text-gray-700">{% trans "الخصم" %}</label>
                                    <input type="number" id="discount" name="discount" min="0" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50" value="0">
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="button" id="save-item-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm">
                            {% trans "حفظ" %}
                        </button>
                        <button type="button" id="close-modal-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            {% trans "إلغاء" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- JavaScript for item management -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Elements
                const addItemBtn = document.getElementById('add-item-btn');
                const itemModal = document.getElementById('item-modal');
                const closeModalBtn = document.getElementById('close-modal-btn');
                const saveItemBtn = document.getElementById('save-item-btn');
                const itemForm = document.getElementById('item-form');
                const modalTitle = document.getElementById('modal-title');
                
                // Open modal for adding a new item
                if (addItemBtn) {
                    addItemBtn.addEventListener('click', function() {
                        modalTitle.textContent = "{% trans "إضافة بند جديد" %}";
                        itemForm.reset();
                        document.getElementById('item-id').value = '';
                        itemModal.classList.remove('hidden');
                        
                        // Load items via AJAX if needed
                        // loadItems();
                    });
                }
                
                // Close modal
                if (closeModalBtn) {
                    closeModalBtn.addEventListener('click', function() {
                        itemModal.classList.add('hidden');
                    });
                }
                
                // Edit item
                document.querySelectorAll('.edit-item').forEach(button => {
                    button.addEventListener('click', function() {
                        const itemId = this.getAttribute('data-id');
                        modalTitle.textContent = "{% trans "تعديل البند" %}";
                        
                        // Load item data via AJAX
                        // loadItemData(itemId);
                        
                        itemModal.classList.remove('hidden');
                    });
                });
                
                // Delete item
                document.querySelectorAll('.delete-item').forEach(button => {
                    button.addEventListener('click', function() {
                        const itemId = this.getAttribute('data-id');
                        if (confirm("{% trans "هل أنت متأكد من حذف هذا البند؟" %}")) {
                            // Delete item via AJAX
                            // deleteItem(itemId);
                        }
                    });
                });
                
                // Save item
                if (saveItemBtn) {
                    saveItemBtn.addEventListener('click', function() {
                        // Validate form
                        if (itemForm.checkValidity()) {
                            // Save item via AJAX
                            // saveItem();
                            itemModal.classList.add('hidden');
                        } else {
                            itemForm.reportValidity();
                        }
                    });
                }
                
                // Function to load items
                function loadItems() {
                    // AJAX call to load items
                }
                
                // Function to load item data for editing
                function loadItemData(itemId) {
                    // AJAX call to load item data
                }
                
                // Function to save item
                function saveItem() {
                    // AJAX call to save item
                }
                
                // Function to delete item
                function deleteItem(itemId) {
                    // AJAX call to delete item
                }
            });
        </script>
    {% endif %}
</div>
{% endblock %} 