{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .filter-tab.active {
        background-color: #3B82F6 !important;
        color: white !important;
    }
    .order-row {
        transition: all 0.3s ease;
    }
    .order-row:hover {
        background-color: #F9FAFB;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ page_title }}</h1>
                <p class="text-gray-600 mt-2">{{ page_subtitle }}</p>
            </div>
            <div class="flex space-x-3 space-x-reverse">
                <a href="{% url 'sales:sales_order_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i>
                    {% trans "طلب جديد" %}
                </a>
                <a href="{% url 'sales:dashboard' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    {% trans "العودة للوحة الرئيسية" %}
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards - Clickable Filter Tabs -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "تصفية الطلبات" %}</h2>
        </div>
        <div class="grid grid-cols-5 divide-x divide-gray-200">
            <!-- إجمالي الطلبات -->
            <a href="{% url 'sales:recent_orders' %}" class="block p-4 hover:bg-gray-50 transition-colors {% if current_filter == 'all' %}bg-blue-50 border-b-2 border-blue-600{% endif %}">
                <div class="text-center">
                    <div class="flex justify-center mb-2">
                        <div class="p-2 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-list-alt text-lg"></i>
                        </div>
                    </div>
                    <p class="text-xs font-medium text-gray-600 mb-1">{% trans "إجمالي الطلبات" %}</p>
                    <p class="text-xl font-bold text-gray-900">{{ stats.total_orders }}</p>
                </div>
            </a>
            
            <!-- قيد التنفيذ -->
            <a href="{% url 'sales:recent_orders' %}?status=pending" class="block p-4 hover:bg-gray-50 transition-colors {% if current_filter == 'pending' %}bg-yellow-50 border-b-2 border-yellow-600{% endif %}">
                <div class="text-center">
                    <div class="flex justify-center mb-2">
                        <div class="p-2 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-clock text-lg"></i>
                        </div>
                    </div>
                    <p class="text-xs font-medium text-gray-600 mb-1">{% trans "قيد التنفيذ" %}</p>
                    <p class="text-xl font-bold text-gray-900">{{ stats.pending_orders }}</p>
                </div>
            </a>
            
            <!-- مكتملة -->
            <a href="{% url 'sales:recent_orders' %}?status=completed" class="block p-4 hover:bg-gray-50 transition-colors {% if current_filter == 'completed' %}bg-green-50 border-b-2 border-green-600{% endif %}">
                <div class="text-center">
                    <div class="flex justify-center mb-2">
                        <div class="p-2 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-check-circle text-lg"></i>
                        </div>
                    </div>
                    <p class="text-xs font-medium text-gray-600 mb-1">{% trans "مكتملة" %}</p>
                    <p class="text-xl font-bold text-gray-900">{{ stats.completed_orders }}</p>
                </div>
            </a>
            
            <!-- مُرسلة -->
            <a href="{% url 'sales:recent_orders' %}?status=shipped" class="block p-4 hover:bg-gray-50 transition-colors {% if current_filter == 'shipped' %}bg-purple-50 border-b-2 border-purple-600{% endif %}">
                <div class="text-center">
                    <div class="flex justify-center mb-2">
                        <div class="p-2 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-shipping-fast text-lg"></i>
                        </div>
                    </div>
                    <p class="text-xs font-medium text-gray-600 mb-1">{% trans "مُرسلة" %}</p>
                    <p class="text-xl font-bold text-gray-900">{{ stats.shipped_orders }}</p>
                </div>
            </a>
            
            <!-- ملغية -->
            <a href="{% url 'sales:recent_orders' %}?status=cancelled" class="block p-4 hover:bg-gray-50 transition-colors {% if current_filter == 'cancelled' %}bg-red-50 border-b-2 border-red-600{% endif %}">
                <div class="text-center">
                    <div class="flex justify-center mb-2">
                        <div class="p-2 rounded-full bg-red-100 text-red-600">
                            <i class="fas fa-times-circle text-lg"></i>
                        </div>
                    </div>
                    <p class="text-xs font-medium text-gray-600 mb-1">{% trans "ملغية" %}</p>
                    <p class="text-xl font-bold text-gray-900">{{ stats.cancelled_orders }}</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">{% trans "طلبات المبيعات" %}</h2>
                    <p class="text-sm text-gray-600 mt-1">
                        {% trans "عرض" %} {{ recent_orders|length }} {% trans "من أصل" %} {{ stats.total_orders }} {% trans "طلب" %}
                        {% if current_filter != 'all' %}
                            - {% trans "مفلتر حسب" %}: 
                            {% if current_filter == 'pending' %}{% trans "قيد التنفيذ" %}
                            {% elif current_filter == 'completed' %}{% trans "مكتملة" %}
                            {% elif current_filter == 'shipped' %}{% trans "مُرسلة" %}
                            {% elif current_filter == 'cancelled' %}{% trans "ملغية" %}
                            {% endif %}
                        {% endif %}
                    </p>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <button class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                        <i class="fas fa-download mr-1"></i>
                        {% trans "تصدير" %}
                    </button>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        <i class="fas fa-filter mr-1"></i>
                        {% trans "تصفية متقدمة" %}
                    </button>
                </div>
            </div>
        </div>
        

        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="ordersTable">
                <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                    <tr class="divide-x divide-gray-200">
                        <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider border-r border-gray-300">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-hashtag ml-2 text-blue-500"></i>
                                {% trans "رقم الطلب" %}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider border-r border-gray-300">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-user ml-2 text-green-500"></i>
                                {% trans "العميل" %}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider border-r border-gray-300">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-wrench ml-2 text-indigo-500"></i>
                                {% trans "أمر العمل" %}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider border-r border-gray-300">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-calendar-alt ml-2 text-purple-500"></i>
                                {% trans "التاريخ" %}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider border-r border-gray-300">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-info-circle ml-2 text-orange-500"></i>
                                {% trans "الحالة" %}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider border-r border-gray-300">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-dollar-sign ml-2 text-emerald-500"></i>
                                {% trans "المبلغ" %}
                            </div>
                        </th>
                        <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-cogs ml-2 text-gray-500"></i>
                                {% trans "الإجراءات" %}
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if recent_orders %}
                        {% for order in recent_orders %}
                        <tr class="order-row hover:bg-blue-50 transition-all duration-200" data-status="{{ order.status }}">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 text-right border-r border-gray-200">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-file-alt ml-2 text-blue-400"></i>
                                <a href="{% url 'sales:sales_order_preview' order.id %}" class="hover:text-blue-800 font-semibold">{{ order.order_number|default:order.id }}</a>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-200">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-user-circle ml-2 text-green-400"></i>
                                <span class="font-medium">{{ order.customer.first_name }} {{ order.customer.last_name }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-200">
                            <div class="flex items-center justify-end">
                                {% if order.work_order %}
                                    <i class="fas fa-wrench ml-2 text-indigo-400"></i>
                                    <button onclick="showWorkOrderDetails('{{ order.work_order.id }}')" 
                                            class="text-indigo-600 hover:text-indigo-800 font-medium cursor-pointer hover:underline">
                                        {{ order.work_order_number|default:order.work_order.work_order_number }}
                                    </button>
                                {% else %}
                                    <i class="fas fa-minus ml-2 text-gray-400"></i>
                                    <span class="text-gray-500">{% trans "لا يوجد" %}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right border-r border-gray-200">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-clock ml-2 text-purple-400"></i>
                                <span>{{ order.created_at|date:"Y/m/d" }}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right border-r border-gray-200">
                            <div class="flex justify-end">
                                {% if order.status == 'draft' or order.status == 'confirmed' %}
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock ml-1"></i>
                                        {% trans "قيد التنفيذ" %}
                                    </span>
                                {% elif order.status == 'delivered' %}
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        {% trans "مكتملة" %}
                                    </span>
                                {% elif order.status == 'shipped' %}
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        <i class="fas fa-shipping-fast ml-1"></i>
                                        {% trans "مُرسلة" %}
                                    </span>
                                {% elif order.status == 'cancelled' %}
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        {% trans "ملغية" %}
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-question-circle ml-1"></i>
                                        {{ order.get_status_display }}
                                    </span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-bold text-right border-r border-gray-200">
                            <div class="flex items-center justify-end">
                                <i class="fas fa-coins ml-2 text-emerald-400"></i>
                                <span class="text-emerald-600">{{ order.total_amount|floatformat:2 }} {% trans "ج.م" %}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-right">
                            <div class="flex justify-end space-x-1 space-x-reverse">
                                <!-- Preview Action -->
                                <a href="{% url 'sales:sales_order_preview' order.id %}" 
                                   class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                                   title="{% trans 'معاينة' %}">
                                    <i class="fas fa-eye text-sm"></i>
                                    <span class="mr-1 text-xs">{% trans "معاينة" %}</span>
                                </a>
                                
                                <!-- Edit Action (only if not completed) -->
                                {% if order.status != 'delivered' and order.status != 'cancelled' %}
                                <a href="{% url 'sales:sales_order_edit' order.id %}" 
                                   class="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
                                   title="{% trans 'تعديل' %}">
                                    <i class="fas fa-edit text-sm"></i>
                                    <span class="mr-1 text-xs">{% trans "تعديل" %}</span>
                                </a>
                                {% else %}
                                <span class="inline-flex items-center px-3 py-1.5 bg-gray-100 text-gray-400 rounded-md cursor-not-allowed"
                                      title="{% trans 'لا يمكن التعديل' %}">
                                    <i class="fas fa-lock text-sm"></i>
                                    <span class="mr-1 text-xs">{% trans "مقفل" %}</span>
                                </span>
                                {% endif %}
                            </div>
                        </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">{% trans "لا توجد طلبات مبيعات" %}</h3>
                                    <p class="text-gray-500">
                                        {% if current_filter != 'all' %}
                                            {% trans "لا توجد طلبات بالحالة المحددة" %}
                                        {% else %}
                                            {% trans "لم يتم إنشاء أي طلبات مبيعات بعد" %}
                                        {% endif %}
                                    </p>
                                    <a href="{% url 'sales:sales_order_create' %}" class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                                        {% trans "إنشاء طلب جديد" %}
                                    </a>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Work Order Details Modal -->
<div id="workOrderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center border-b pb-3 mb-4">
                <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">{% trans "تفاصيل أمر العمل" %}</h3>
                <button onclick="closeWorkOrderModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Loading State -->
            <div id="modalLoading" class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-3xl text-blue-500 mb-4"></i>
                <p class="text-gray-600">{% trans "جاري تحميل تفاصيل أمر العمل..." %}</p>
            </div>
            
            <!-- Error State -->
            <div id="modalError" class="hidden text-center py-8">
                <i class="fas fa-exclamation-triangle text-3xl text-red-500 mb-4"></i>
                <p class="text-red-600">{% trans "حدث خطأ في تحميل البيانات" %}</p>
            </div>
            
            <!-- Modal Content -->
            <div id="modalContent" class="hidden">
                <!-- Work Order Basic Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-2">{% trans "معلومات أساسية" %}</h4>
                        <div class="space-y-2 text-sm">
                            <div><span class="font-medium">{% trans "رقم أمر العمل" %}:</span> <span id="woNumber"></span></div>
                            <div><span class="font-medium">{% trans "الحالة" %}:</span> <span id="woStatus"></span></div>
                            <div><span class="font-medium">{% trans "تاريخ الإنشاء" %}:</span> <span id="woCreatedAt"></span></div>
                            <div><span class="font-medium">{% trans "التاريخ المطلوب" %}:</span> <span id="woRequiredDate"></span></div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-2">{% trans "معلومات العميل والمركبة" %}</h4>
                        <div class="space-y-2 text-sm">
                            <div><span class="font-medium">{% trans "العميل" %}:</span> <span id="woCustomer"></span></div>
                            <div><span class="font-medium">{% trans "المركبة" %}:</span> <span id="woVehicle"></span></div>
                            <div><span class="font-medium">{% trans "رقم اللوحة" %}:</span> <span id="woVehiclePlate"></span></div>
                            <div><span class="font-medium">{% trans "مركز الخدمة" %}:</span> <span id="woServiceCenter"></span></div>
                        </div>
                    </div>
                </div>
                
                <!-- Work Order Operations -->
                <div class="mb-6">
                    <h4 class="font-semibold text-gray-800 mb-3">{% trans "العمليات" %}</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "العملية" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الوصف" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "التقني" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الحالة" %}</th>
                                </tr>
                            </thead>
                            <tbody id="woOperations" class="divide-y divide-gray-200">
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Work Order Materials -->
                <div class="mb-6">
                    <h4 class="font-semibold text-gray-800 mb-3">{% trans "المواد والقطع" %}</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الصنف" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الكمية المطلوبة" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "الكمية المستخدمة" %}</th>
                                    <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">{% trans "السعر" %}</th>
                                </tr>
                            </thead>
                            <tbody id="woMaterials" class="divide-y divide-gray-200">
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Work Order Notes -->
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 mb-2">{% trans "ملاحظات" %}</h4>
                    <p id="woNotes" class="text-sm text-gray-600"></p>
                </div>
            </div>
            
            <!-- Modal Footer -->
            <div class="flex justify-end pt-4 border-t mt-6">
                <button onclick="closeWorkOrderModal()" 
                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg mr-2">
                    {% trans "إغلاق" %}
                </button>
                <a id="viewWorkOrderLink" href="#" target="_blank" 
                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    {% trans "عرض التفاصيل الكاملة" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Work Order Modal Functions
function showWorkOrderDetails(workOrderId) {
    // Show modal
    const modal = document.getElementById('workOrderModal');
    modal.classList.remove('hidden');
    
    // Show loading state
    document.getElementById('modalLoading').classList.remove('hidden');
    document.getElementById('modalError').classList.add('hidden');
    document.getElementById('modalContent').classList.add('hidden');
    
    // Fetch work order details
    fetch(`/ar/sales/api/work-order-details/${workOrderId}/`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to fetch work order details');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                populateWorkOrderModal(data.work_order);
            } else {
                throw new Error(data.error || 'Unknown error');
            }
        })
        .catch(error => {
            console.error('Error fetching work order details:', error);
            showModalError();
        });
}

function populateWorkOrderModal(workOrder) {
    // Hide loading, show content
    document.getElementById('modalLoading').classList.add('hidden');
    document.getElementById('modalError').classList.add('hidden');
    document.getElementById('modalContent').classList.remove('hidden');
    
    // Populate basic info
    document.getElementById('woNumber').textContent = workOrder.order_number || '';
    document.getElementById('woStatus').innerHTML = getStatusBadge(workOrder.status);
    document.getElementById('woCreatedAt').textContent = formatDate(workOrder.created_at);
    document.getElementById('woRequiredDate').textContent = formatDate(workOrder.required_date);
    
    // Populate customer and vehicle info
    document.getElementById('woCustomer').textContent = workOrder.customer_name || 'غير محدد';
    document.getElementById('woVehicle').textContent = workOrder.vehicle_info || 'غير محدد';
    document.getElementById('woVehiclePlate').textContent = workOrder.vehicle_plate || 'غير محدد';
    document.getElementById('woServiceCenter').textContent = workOrder.service_center || 'غير محدد';
    
    // Populate operations
    const operationsTableBody = document.getElementById('woOperations');
    operationsTableBody.innerHTML = '';
    if (workOrder.operations && workOrder.operations.length > 0) {
        workOrder.operations.forEach(operation => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 text-sm text-gray-900">${operation.name || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${operation.description || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${operation.technician || 'غير محدد'}</td>
                <td class="px-4 py-2">${getStatusBadge(operation.status)}</td>
            `;
            operationsTableBody.appendChild(row);
        });
    } else {
        operationsTableBody.innerHTML = '<tr><td colspan="4" class="px-4 py-2 text-center text-gray-500">لا توجد عمليات</td></tr>';
    }
    
    // Populate materials
    const materialsTableBody = document.getElementById('woMaterials');
    materialsTableBody.innerHTML = '';
    if (workOrder.materials && workOrder.materials.length > 0) {
        workOrder.materials.forEach(material => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 text-sm text-gray-900">${material.item_name || ''}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${material.quantity_required || 0}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${material.quantity_used || 0}</td>
                <td class="px-4 py-2 text-sm text-gray-600">${material.unit_price || 0} ج.م</td>
            `;
            materialsTableBody.appendChild(row);
        });
    } else {
        materialsTableBody.innerHTML = '<tr><td colspan="4" class="px-4 py-2 text-center text-gray-500">لا توجد مواد</td></tr>';
    }
    
    // Populate notes
    document.getElementById('woNotes').textContent = workOrder.notes || 'لا توجد ملاحظات';
    
    // Set view link
    document.getElementById('viewWorkOrderLink').href = `/ar/work-orders/${workOrder.id}/`;
}

function showModalError() {
    document.getElementById('modalLoading').classList.add('hidden');
    document.getElementById('modalContent').classList.add('hidden');
    document.getElementById('modalError').classList.remove('hidden');
}

function closeWorkOrderModal() {
    document.getElementById('workOrderModal').classList.add('hidden');
}

// Helper functions
function getStatusBadge(status) {
    const statusMap = {
        'pending': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">قيد الانتظار</span>',
        'in_progress': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">قيد التنفيذ</span>',
        'completed': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">مكتمل</span>',
        'on_hold': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">متوقف</span>',
        'cancelled': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">ملغي</span>',
        'draft': '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">مسودة</span>'
    };
    return statusMap[status] || status;
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
}

// Close modal when clicking outside
document.getElementById('workOrderModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeWorkOrderModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeWorkOrderModal();
    }
});
</script>
{% endblock %} 