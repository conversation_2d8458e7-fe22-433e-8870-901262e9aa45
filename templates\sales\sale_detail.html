{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تفاصيل طلب المبيعات" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">{% trans "تفاصيل طلب المبيعات" %}</h1>
            <p class="text-gray-600">{{ sales_order.order_number }}</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
            <a href="{% url 'sales:sales_order_update' sales_order.id %}" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "تعديل" %}
            </a>
            <a href="{% url 'sales:sales_order_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "العودة" %}
            </a>
        </div>
    </div>
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    <!-- Sales Order Info -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Order Details -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "تفاصيل الطلب" %}</h2>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 gap-x-4 gap-y-5">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "رقم الطلب" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ sales_order.order_number }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "تاريخ الإنشاء" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ sales_order.created_at|date:"d/m/Y H:i" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "تاريخ الطلب" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ sales_order.order_date|date:"d/m/Y" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "المبلغ الإجمالي" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ sales_order.total_amount }} {% trans "ج.م" %}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "الحالة" %}</dt>
                        <dd class="mt-1 text-sm">
                            {% if sales_order.status == 'draft' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {% trans "مسودة" %}
                                </span>
                            {% elif sales_order.status == 'confirmed' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {% trans "مؤكد" %}
                                </span>
                            {% elif sales_order.status == 'shipped' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    {% trans "تم الشحن" %}
                                </span>
                            {% elif sales_order.status == 'delivered' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {% trans "تم التسليم" %}
                                </span>
                            {% elif sales_order.status == 'cancelled' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    {% trans "ملغي" %}
                                </span>
                            {% elif sales_order.status == 'returned' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">
                                    {% trans "مرتجع" %}
                                </span>
                            {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ sales_order.get_status_display }}
                                </span>
                            {% endif %}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        
        <!-- Customer Info -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "معلومات العميل" %}</h2>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 gap-x-4 gap-y-5">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "اسم العميل" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ sales_order.customer.name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "البريد الإلكتروني" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if sales_order.customer.email %}
                                <a href="mailto:{{ sales_order.customer.email }}" class="text-blue-600 hover:text-blue-800">
                                    {{ sales_order.customer.email }}
                                </a>
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "رقم الهاتف" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if sales_order.customer.phone %}
                                {{ sales_order.customer.phone }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "العنوان" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if sales_order.customer.address %}
                                {{ sales_order.customer.address }}
                            {% else %}
                                {% trans "غير محدد" %}
                            {% endif %}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        
        <!-- Shipping & Notes -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "الشحن والملاحظات" %}</h2>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">{% trans "عنوان الشحن" %}</h3>
                    {% if sales_order.shipping_address %}
                        <p class="text-sm text-gray-900">{{ sales_order.shipping_address }}</p>
                    {% else %}
                        <p class="text-sm text-gray-500">{% trans "غير محدد" %}</p>
                    {% endif %}
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-2">{% trans "ملاحظات" %}</h3>
                    {% if sales_order.notes %}
                        <p class="text-sm text-gray-900">{{ sales_order.notes|linebreaks }}</p>
                    {% else %}
                        <p class="text-sm text-gray-500">{% trans "لا توجد ملاحظات" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Items Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "البنود" %}</h2>
            <!-- Add an "add item" button here if you want -->
        </div>
        
        {% if items %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الصنف" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الكمية" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "السعر" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الخصم" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجمالي" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in items %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ item.item.name }}</div>
                                    <div class="text-xs text-gray-500">{{ item.item.sku }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ item.quantity }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ item.unit_price }} {% trans "ج.م" %}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ item.discount }} {% trans "ج.م" %}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ item.total_price }} {% trans "ج.م" %}</div>
                                </td>
                            </tr>
                        {% endfor %}
                        <!-- Summary row -->
                        <tr class="bg-gray-50">
                            <td colspan="4" class="px-6 py-4 text-right whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{% trans "الإجمالي" %}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-gray-900">
                                    {{ sales_order.total_amount }} {% trans "ج.م" %}
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا توجد بنود في هذا الطلب" %}
            </div>
        {% endif %}
    </div>
    
    <!-- Returns (if any) -->
    {% if returns %}
        <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "المرتجعات" %}</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم المرتجع" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "التاريخ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "السبب" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for return in returns %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ return.return_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ return.return_date|date:"d/m/Y" }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">{{ return.reason|truncatechars:50 }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <!-- Add view details link when return detail page is implemented -->
                                    <a href="#" class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %} 