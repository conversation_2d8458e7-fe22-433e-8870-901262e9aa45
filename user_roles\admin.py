from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from django import forms
from .models import Role, UserRole, ModulePermission, ModuleTab, RoleModulePermission, UserCustomPermission
from setup.models import ServiceCenter, Company
from core.admin import TenantAdminMixin
from setup.admin import HierarchicalContextAdmin

class ModulePermissionInline(admin.TabularInline):
    model = ModulePermission
    extra = 1
    fields = ('module', 'action')

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'role_type', 'code', 'is_active', 'display_module_access')
    list_filter = ('role_type', 'is_active', 'can_access_setup', 'can_access_work_orders', 
                   'can_access_inventory', 'can_access_warehouse', 'can_access_sales',
                   'can_access_purchases', 'can_access_reports', 'can_access_settings')
    search_fields = ('name', 'code', 'description')
    inlines = [ModulePermissionInline]
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'role_type', 'description', 'is_active'),
        }),
        (_('Module Access'), {
            'fields': (
                'can_access_setup', 'can_access_work_orders', 'can_access_inventory',
                'can_access_warehouse', 'can_access_sales', 'can_access_purchases',
                'can_access_reports', 'can_access_settings'
            ),
        }),
        (_('Group Association'), {
            'fields': ('group',),
            'classes': ('collapse',),
        }),
    )
    readonly_fields = ('group',)
    
    def display_module_access(self, obj):
        """Show which modules this role can access"""
        modules = []
        if obj.can_access_setup:
            modules.append(str(_('Setup')))
        if obj.can_access_work_orders:
            modules.append(str(_('Work Orders')))
        if obj.can_access_inventory:
            modules.append(str(_('Inventory')))
        if obj.can_access_warehouse:
            modules.append(str(_('Warehouse')))
        if obj.can_access_sales:
            modules.append(str(_('Sales')))
        if obj.can_access_purchases:
            modules.append(str(_('Purchases')))
        if obj.can_access_reports:
            modules.append(str(_('Reports')))
        if obj.can_access_settings:
            modules.append(str(_('Settings')))
        
        if modules:
            return ', '.join(modules)
        return str(_('None'))
    
    display_module_access.short_description = _('Module Access')

# Custom ModelForm for UserRole
class UserRoleForm(forms.ModelForm):
    class Meta:
        model = UserRole
        fields = ('user', 'role', 'franchise', 'company', 'service_center', 'is_primary', 'is_active')
        
    def clean(self):
        """Validate and ensure tenant_id will be set correctly"""
        cleaned_data = super().clean()
        
        service_center = cleaned_data.get('service_center')
        company = cleaned_data.get('company')
        
        # We're not setting tenant_id here anymore as it will be handled by the save_model method
        # This validation just ensures we have a way to determine the tenant
        if not service_center and not company:
            # Neither service_center nor company is provided
            # We'll rely on the TenantAdminMixin.save_model to set tenant_id
            pass
        
        return cleaned_data

@admin.register(UserRole)
class UserRoleAdmin(TenantAdminMixin, admin.ModelAdmin):
    form = UserRoleForm
    list_display = ('user', 'role', 'display_scope', 'is_primary', 'is_active', 'tenant_id')
    list_filter = ('is_active', 'is_primary', 'role', 'role__role_type')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 
                     'role__name', 'franchise__name', 'company__name', 'service_center__name')
    fieldsets = (
        (None, {
            'fields': ('user', 'role', 'is_primary', 'is_active'),
        }),
        (_('Role Scope'), {
            'fields': ('franchise', 'company', 'service_center'),
            'description': _('Set the scope based on the role type. Only one should be set.'),
        }),
    )
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        
        # Add dynamic help text and validation
        if obj and obj.role:
            role_type = obj.role.role_type
            
            # Set help text based on role type
            if role_type == 'system_admin':
                form.base_fields['franchise'].help_text = _('System administrators do not need a scope')
                form.base_fields['company'].help_text = _('System administrators do not need a scope')
                form.base_fields['service_center'].help_text = _('System administrators do not need a scope')
            elif role_type == 'franchise_admin':
                form.base_fields['franchise'].help_text = _('Required for Franchise Administrators')
                form.base_fields['company'].help_text = _('Leave blank for Franchise Administrators')
                form.base_fields['service_center'].help_text = _('Leave blank for Franchise Administrators')
            elif role_type == 'company_admin':
                form.base_fields['franchise'].help_text = _('Leave blank for Company Administrators')
                form.base_fields['company'].help_text = _('Required for Company Administrators')
                form.base_fields['service_center'].help_text = _('Leave blank for Company Administrators')
            else:
                form.base_fields['franchise'].help_text = _('Leave blank for Service Center roles')
                form.base_fields['company'].help_text = _('Leave blank for Service Center roles')
                form.base_fields['service_center'].help_text = _('Required for Service Center roles')
        
        return form
    
    def save_model(self, request, obj, form, change):
        """
        Ensure tenant_id is properly set before saving the model
        """
        # Debug information
        print(f"Saving UserRole for user: {obj.user.username}, role: {obj.role.name}")
        print(f"Scope - Franchise: {obj.franchise}, Company: {obj.company}, Service Center: {obj.service_center}")
        
        # Set tenant_id from related objects
        if obj.service_center and hasattr(obj.service_center, 'tenant_id'):
            obj.tenant_id = obj.service_center.tenant_id
            print(f"Taking tenant_id from service center: {obj.tenant_id}")
        elif obj.company and hasattr(obj.company, 'tenant_id'):
            obj.tenant_id = obj.company.tenant_id 
            print(f"Taking tenant_id from company: {obj.tenant_id}")
        else:
            # Get from thread local via the TenantAdminMixin
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            if tenant_id:
                obj.tenant_id = tenant_id
                print(f"Taking tenant_id from current context: {obj.tenant_id}")
        
        print(f"Final tenant_id: {obj.tenant_id}")
        super().save_model(request, obj, form, change)
    
    def display_scope(self, obj):
        """Display the scope of this user role"""
        if obj.franchise:
            return f"{obj.franchise.name} (Franchise)"
        elif obj.company:
            return f"{obj.company.name} (Company)"
        elif obj.service_center:
            return f"{obj.service_center.name} (Service Center)"
        else:
            return _("System-wide")
    
    display_scope.short_description = _('Scope')

@admin.register(ModulePermission)
class ModulePermissionAdmin(admin.ModelAdmin):
    list_display = ('role', 'module', 'action')
    list_filter = ('module', 'action', 'role')
    search_fields = ('role__name',)

class RoleModulePermissionInline(admin.TabularInline):
    """Inline for managing role permissions on tabs"""
    model = RoleModulePermission
    extra = 0
    fields = ('module_tab', 'can_view', 'can_add', 'can_edit', 'can_delete', 'can_approve', 'can_report', 'scope_level', 'is_active')
    readonly_fields = ()
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('module_tab')

@admin.register(ModuleTab)
class ModuleTabAdmin(admin.ModelAdmin):
    list_display = ('name_en', 'name_ar', 'code', 'url_name', 'order', 'is_active', 'requires_authentication', 'is_system_admin_only')
    list_filter = ('is_active', 'requires_authentication', 'is_system_admin_only', 'parent_tab')
    search_fields = ('name_en', 'name_ar', 'code', 'url_name')
    ordering = ('order', 'name_en')
    
    fieldsets = (
        (None, {
            'fields': ('code', 'name_en', 'name_ar', 'url_name', 'icon_class', 'order', 'parent_tab')
        }),
        (_('Access Control'), {
            'fields': ('is_active', 'requires_authentication', 'is_system_admin_only'),
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Auto-generate Arabic name if not provided"""
        if not obj.name_ar:
            # Basic Arabic translations for common tabs
            translations = {
                'dashboard': 'الرئيسية',
                'supply_chain': 'سلسلة التوريد',
                'sales': 'المبيعات',
                'work_orders': 'أوامر العمل',
                'cashier': 'الكاشير',
                'reports': 'التقارير',
                'settings': 'الإعدادات',
                'inventory': 'المخزون',
                'warehouse': 'المستودعات',
                'purchases': 'المشتريات',
                'setup': 'الإعداد',
            }
            obj.name_ar = translations.get(obj.code.lower(), obj.name_en)
        
        super().save_model(request, obj, form, change)

@admin.register(RoleModulePermission)
class RoleModulePermissionAdmin(admin.ModelAdmin):
    list_display = ('role', 'module_tab', 'display_permissions', 'scope_level', 'is_active')
    list_filter = ('scope_level', 'is_active', 'can_view', 'can_add', 'can_edit', 'can_delete', 'can_approve', 'can_report')
    search_fields = ('role__name', 'module_tab__name_en', 'module_tab__name_ar', 'module_tab__code')
    ordering = ('role__name', 'module_tab__order')
    
    fieldsets = (
        (None, {
            'fields': ('role', 'module_tab', 'scope_level', 'is_active')
        }),
        (_('Permissions'), {
            'fields': ('can_view', 'can_add', 'can_edit', 'can_delete', 'can_approve', 'can_report'),
            'classes': ('wide',),
        }),
    )
    
    def display_permissions(self, obj):
        """Show enabled permissions"""
        permissions = []
        if obj.can_view:
            permissions.append(str(_('View')))
        if obj.can_add:
            permissions.append(str(_('Add')))
        if obj.can_edit:
            permissions.append(str(_('Edit')))
        if obj.can_delete:
            permissions.append(str(_('Delete')))
        if obj.can_approve:
            permissions.append(str(_('Approve')))
        if obj.can_report:
            permissions.append(str(_('Report')))
        
        return ', '.join(permissions) if permissions else str(_('None'))
    
    display_permissions.short_description = _('Permissions')
    
    actions = ['bulk_enable_view', 'bulk_enable_edit', 'bulk_disable_all']
    
    def bulk_enable_view(self, request, queryset):
        """Bulk action to enable view permission"""
        queryset.update(can_view=True)
        self.message_user(request, _('View permission enabled for selected items'))
    bulk_enable_view.short_description = _('Enable View permission')
    
    def bulk_enable_edit(self, request, queryset):
        """Bulk action to enable edit permissions"""
        queryset.update(can_view=True, can_edit=True)
        self.message_user(request, _('View and Edit permissions enabled for selected items'))
    bulk_enable_edit.short_description = _('Enable View & Edit permissions')
    
    def bulk_disable_all(self, request, queryset):
        """Bulk action to disable all permissions"""
        queryset.update(can_view=False, can_add=False, can_edit=False, can_delete=False, can_approve=False, can_report=False)
        self.message_user(request, _('All permissions disabled for selected items'))
    bulk_disable_all.short_description = _('Disable all permissions')

@admin.register(UserCustomPermission)
class UserCustomPermissionAdmin(HierarchicalContextAdmin):
    list_display = ('user', 'module_tab', 'display_scope', 'display_permissions', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 
                     'module_tab__name_en', 'module_tab__name_ar')
    ordering = ('user__username', 'module_tab__order')
    
    # Define which field relationships to use for hierarchical filtering
    hierarchy_field_mapping = {
        'franchise': 'franchises',
        'company': 'companies', 
        'service_center': 'service_centers'
    }
    
    # Use horizontal filter for better UX with multiple selections
    filter_horizontal = ('franchises', 'companies', 'service_centers')
    
    fieldsets = (
        (None, {
            'fields': ('user', 'module_tab', 'is_active')
        }),
        (_('Scope - Multiple Selections Allowed'), {
            'fields': ('franchises', 'companies', 'service_centers'),
            'description': _('Select multiple contexts where this override applies. Leave all blank for global override.'),
        }),
        (_('Permissions'), {
            'fields': ('can_view', 'can_add', 'can_edit', 'can_delete', 'can_approve', 'can_report'),
            'classes': ('wide',),
        }),
    )
    
    def display_scope(self, obj):
        """Display the scope of this permission override"""
        scope_parts = []
        
        # Check franchises
        if obj.franchises.exists():
            franchise_count = obj.franchises.count()
            if franchise_count == 1:
                scope_parts.append(f"{obj.franchises.first().name} (Franchise)")
            else:
                scope_parts.append(f"{franchise_count} Franchises")
        
        # Check companies
        if obj.companies.exists():
            company_count = obj.companies.count()
            if company_count == 1:
                scope_parts.append(f"{obj.companies.first().name} (Company)")
            else:
                scope_parts.append(f"{company_count} Companies")
        
        # Check service centers
        if obj.service_centers.exists():
            sc_count = obj.service_centers.count()
            if sc_count == 1:
                scope_parts.append(f"{obj.service_centers.first().name} (Service Center)")
            else:
                scope_parts.append(f"{sc_count} Service Centers")
        
        if scope_parts:
            return "; ".join(scope_parts)
        else:
            return _("Global")
    
    display_scope.short_description = _('Scope')
    
    def display_permissions(self, obj):
        """Show enabled permissions"""
        permissions = []
        if obj.can_view:
            permissions.append(str(_('View')))
        if obj.can_add:
            permissions.append(str(_('Add')))
        if obj.can_edit:
            permissions.append(str(_('Edit')))
        if obj.can_delete:
            permissions.append(str(_('Delete')))
        if obj.can_approve:
            permissions.append(str(_('Approve')))
        if obj.can_report:
            permissions.append(str(_('Report')))
        
        return ', '.join(permissions) if permissions else str(_('None'))
    
    display_permissions.short_description = _('Permissions')
    
    def save_model(self, request, obj, form, change):
        """Set tenant_id from related objects"""
        if obj.service_center and hasattr(obj.service_center, 'tenant_id'):
            obj.tenant_id = obj.service_center.tenant_id
        elif obj.company and hasattr(obj.company, 'tenant_id'):
            obj.tenant_id = obj.company.tenant_id
        elif obj.franchise and hasattr(obj.franchise, 'tenant_id'):
            obj.tenant_id = obj.franchise.tenant_id
        else:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            if tenant_id:
                obj.tenant_id = tenant_id
        
        super().save_model(request, obj, form, change)

# Add the inline to the existing RoleAdmin
class ExtendedRoleAdmin(RoleAdmin):
    """Extended RoleAdmin that includes dynamic permissions inline"""
    inlines = RoleAdmin.inlines + [RoleModulePermissionInline]

# Unregister the old RoleAdmin and register the new one
admin.site.unregister(Role)
admin.site.register(Role, ExtendedRoleAdmin)
