from functools import wraps
from django.shortcuts import redirect
from django.contrib import messages
from django.http import <PERSON>son<PERSON><PERSON>po<PERSON>, HttpResponseForbidden
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import PermissionDenied
from .services import PermissionService, DataFilterService


def _get_permission_context_from_request(request):
    """
    Helper function to extract permission context from request
    """
    from setup.models import Franchise, Company, ServiceCenter
    
    context = {}
    
    # Try to get context from URL parameters
    if hasattr(request, 'GET'):
        franchise_id = request.GET.get('franchise_id')
        company_id = request.GET.get('company_id')
        service_center_id = request.GET.get('service_center_id')
        
        try:
            if service_center_id:
                context['service_center'] = ServiceCenter.objects.get(id=service_center_id)
            if company_id:
                context['company'] = Company.objects.get(id=company_id)
            if franchise_id:
                context['franchise'] = Franchise.objects.get(id=franchise_id)
        except (ServiceCenter.DoesNotExist, Company.DoesNotExist, Franchise.DoesNotExist, ValueError):
            pass
    
    return context


def tab_permission_required(tab_code, action='view', raise_exception=False):
    """
    Decorator that checks if user has permission for a specific tab and action.
    
    Args:
        tab_code: The tab code to check (e.g., 'dashboard', 'sales')
        action: The action to check ('view', 'add', 'edit', 'delete', 'approve', 'report')
        raise_exception: If True, raise PermissionDenied instead of redirecting
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Superusers have all permissions
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            # Get permission context from request
            context = _get_permission_context_from_request(request)
            
            # Check if user has the required permission
            if PermissionService.can_access_tab(request.user, tab_code, action, context):
                return view_func(request, *args, **kwargs)
            else:
                if raise_exception:
                    raise PermissionDenied(_("You don't have permission to perform this action."))
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': str(_("You don't have permission to perform this action.")),
                        'permission_required': f"{tab_code}:{action}"
                    }, status=403)
                
                messages.error(request, _("You don't have permission to access this page."))
                return redirect(request.META.get('HTTP_REFERER', '/'))

        return _wrapped_view
    return decorator


def multiple_permissions_required(*permission_specs, require_all=True):
    """
    Decorator that checks multiple permissions at once.
    
    Args:
        *permission_specs: Tuples of (tab_code, action) to check
        require_all: If True, user must have all permissions. If False, user needs at least one.
    
    Example:
        @multiple_permissions_required(
            ('sales', 'view'),
            ('inventory', 'view'),
            require_all=True
        )
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            context = _get_permission_context_from_request(request)
            permissions_check = []
            
            for tab_code, action in permission_specs:
                has_permission = PermissionService.can_access_tab(request.user, tab_code, action, context)
                permissions_check.append(has_permission)
            
            # Check if requirements are met
            if require_all:
                has_access = all(permissions_check)
            else:
                has_access = any(permissions_check)
            
            if has_access:
                return view_func(request, *args, **kwargs)
            else:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': str(_("You don't have sufficient permissions to access this page.")),
                        'required_permissions': [f"{tab}:{action}" for tab, action in permission_specs]
                    }, status=403)
                
                messages.error(request, _("You don't have sufficient permissions to access this page."))
                return redirect('/')

        return _wrapped_view
    return decorator


def scope_required(minimum_scope='own_data'):
    """
    Decorator that checks if user has at least the minimum required data scope.
    
    Args:
        minimum_scope: Minimum scope level ('own_data', 'service_center', 'company', 'franchise', 'system')
    """
    scope_hierarchy = ['own_data', 'service_center', 'company', 'franchise', 'system']
    
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            context = _get_permission_context_from_request(request)
            user_scope = DataFilterService.get_user_data_scope(request.user, context)
            
            user_scope_level = user_scope.get('scope', 'own_data')
            
            try:
                user_scope_index = scope_hierarchy.index(user_scope_level)
                required_scope_index = scope_hierarchy.index(minimum_scope)
                
                if user_scope_index >= required_scope_index:
                    return view_func(request, *args, **kwargs)
            except ValueError:
                pass
            
            # Access denied
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': str(_("You don't have sufficient data access scope for this action.")),
                    'user_scope': user_scope_level,
                    'required_scope': minimum_scope
                }, status=403)
            
            messages.error(request, _("You don't have sufficient access level for this page."))
            return redirect('/')

        return _wrapped_view
    return decorator


def role_type_required(*allowed_role_types):
    """
    Decorator that checks if user has one of the specified role types.
    
    Args:
        *allowed_role_types: Role type codes that are allowed access
    
    Example:
        @role_type_required('system_admin', 'franchise_admin')
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            # Get user's primary role
            primary_role = None
            if hasattr(request.user, 'user_roles'):
                user_roles = request.user.user_roles.filter(is_active=True)
                for user_role in user_roles:
                    if user_role.is_primary:
                        primary_role = user_role.role
                        break
                if not primary_role and user_roles.exists():
                    primary_role = user_roles.first().role
            
            if primary_role and primary_role.role_type in allowed_role_types:
                return view_func(request, *args, **kwargs)
            else:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': str(_("You don't have the required role type to access this page.")),
                        'allowed_roles': list(allowed_role_types),
                        'user_role': primary_role.role_type if primary_role else None
                    }, status=403)
                
                messages.error(request, _("You don't have the required role to access this page."))
                return redirect('/')

        return _wrapped_view
    return decorator


def ajax_permission_required(tab_code, action='view'):
    """
    Lightweight decorator specifically for AJAX views that only returns JSON responses.
    
    Args:
        tab_code: The tab code to check
        action: The action to check
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            context = _get_permission_context_from_request(request)
            
            if PermissionService.can_access_tab(request.user, tab_code, action, context):
                return view_func(request, *args, **kwargs)
            else:
                return JsonResponse({
                    'success': False,
                    'error': str(_("You don't have permission to perform this action.")),
                    'permission_required': f"{tab_code}:{action}"
                }, status=403)

        return _wrapped_view
    return decorator


def dynamic_permission_check(request, tab_code, action='view', context=None):
    """
    Function-based permission check that can be used within views.
    
    Args:
        request: Django request object
        tab_code: The tab code to check
        action: The action to check
        context: Optional context override
    
    Returns:
        bool: True if user has permission, False otherwise
    """
    if request.user.is_superuser:
        return True
    
    if context is None:
        context = _get_permission_context_from_request(request)
    
    return PermissionService.can_access_tab(request.user, tab_code, action, context)


def get_user_accessible_tabs(request, context=None):
    """
    Function to get list of tabs accessible to the current user.
    
    Args:
        request: Django request object
        context: Optional context override
    
    Returns:
        list: List of accessible tab dictionaries
    """
    if context is None:
        context = _get_permission_context_from_request(request)
    
    return PermissionService.get_accessible_tabs(request.user, context)


class PermissionRequiredMixin:
    """
    Class-based view mixin for permission checking.
    """
    permission_tab_code = None
    permission_action = 'view'
    permission_raise_exception = False
    
    def dispatch(self, request, *args, **kwargs):
        if not self.has_permission(request):
            return self.handle_no_permission(request)
        return super().dispatch(request, *args, **kwargs)
    
    def has_permission(self, request):
        """
        Check if user has the required permission.
        """
        if request.user.is_superuser:
            return True
        
        if not self.permission_tab_code:
            return True  # No permission check if tab_code not specified
        
        context = _get_permission_context_from_request(request)
        return PermissionService.can_access_tab(
            request.user, 
            self.permission_tab_code, 
            self.permission_action, 
            context
        )
    
    def handle_no_permission(self, request):
        """
        Handle cases where user doesn't have permission.
        """
        if self.permission_raise_exception:
            raise PermissionDenied(_("You don't have permission to access this page."))
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'error': str(_("You don't have permission to access this page.")),
                'permission_required': f"{self.permission_tab_code}:{self.permission_action}"
            }, status=403)
        
        messages.error(request, _("You don't have permission to access this page."))
        return redirect(request.META.get('HTTP_REFERER', '/'))


class MultiplePermissionRequiredMixin:
    """
    Class-based view mixin for checking multiple permissions.
    """
    permission_required = []  # List of (tab_code, action) tuples
    permission_require_all = True
    
    def has_permission(self, request):
        """
        Check if user has the required permissions.
        """
        if request.user.is_superuser:
            return True
        
        if not self.permission_required:
            return True
        
        context = _get_permission_context_from_request(request)
        permissions_check = []
        
        for tab_code, action in self.permission_required:
            has_permission = PermissionService.can_access_tab(request.user, tab_code, action, context)
            permissions_check.append(has_permission)
        
        if self.permission_require_all:
            return all(permissions_check)
        else:
            return any(permissions_check)


class ScopeRequiredMixin:
    """
    Class-based view mixin for scope checking.
    """
    minimum_scope = 'own_data'
    
    def has_permission(self, request):
        """
        Check if user has sufficient scope.
        """
        if request.user.is_superuser:
            return True
        
        scope_hierarchy = ['own_data', 'service_center', 'company', 'franchise', 'system']
        context = _get_permission_context_from_request(request)
        user_scope = DataFilterService.get_user_data_scope(request.user, context)
        user_scope_level = user_scope.get('scope', 'own_data')
        
        try:
            user_scope_index = scope_hierarchy.index(user_scope_level)
            required_scope_index = scope_hierarchy.index(self.minimum_scope)
            return user_scope_index >= required_scope_index
        except ValueError:
            return False 