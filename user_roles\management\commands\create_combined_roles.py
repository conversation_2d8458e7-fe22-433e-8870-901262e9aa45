from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from django.db import transaction
from user_roles.models import Role, ModulePermission

class Command(BaseCommand):
    help = 'Creates combined roles for different service center sizes'

    def handle(self, *args, **options):
        with transaction.atomic():
            # Define the combined roles for different service center sizes
            combined_roles_data = [
                # SMALL SERVICE CENTER ROLES
                {
                    'name': 'Small Center Manager',
                    'code': 'small_center_manager',
                    'role_type': 'service_center_manager',
                    'description': 'Manager for small service centers with expanded responsibilities',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add', 'change', 'approve'],
                        'work_orders': ['view', 'add', 'change', 'approve'],
                        'inventory': ['view', 'add', 'change', 'approve'],
                        'warehouse': ['view', 'add', 'change', 'approve'],
                        'sales': ['view', 'add', 'change', 'approve'],
                        'purchases': ['view', 'add', 'change', 'approve'],
                        'reports': ['view', 'report'],
                        'settings': ['view', 'change'],
                    }
                },
                {
                    'name': 'Small Center Advisor',
                    'code': 'small_center_advisor',
                    'role_type': 'service_advisor',
                    'description': 'Service advisor for small centers who also handles reception, invoicing, and parts',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add'],
                        'work_orders': ['view', 'add', 'change'],
                        'inventory': ['view', 'add', 'change'],
                        'warehouse': ['view', 'add', 'change'],
                        'sales': ['view', 'add', 'change'],
                        'reports': ['view'],
                    }
                },
                {
                    'name': 'Small Center Technician',
                    'code': 'small_center_technician',
                    'role_type': 'technician',
                    'description': 'Technician for small centers with parts management responsibilities',
                    'is_active': True,
                    'modules': {
                        'work_orders': ['view', 'change'],
                        'inventory': ['view', 'add'],
                        'warehouse': ['view', 'add'],
                    }
                },
                
                # MEDIUM SERVICE CENTER ROLES
                {
                    'name': 'Medium Center Manager',
                    'code': 'medium_center_manager',
                    'role_type': 'service_center_manager',
                    'description': 'Manager for medium-sized service centers',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add', 'change', 'approve'],
                        'work_orders': ['view', 'add', 'change', 'approve'],
                        'inventory': ['view', 'approve'],
                        'warehouse': ['view', 'approve'],
                        'sales': ['view', 'approve'],
                        'purchases': ['view', 'approve'],
                        'reports': ['view', 'report'],
                        'settings': ['view'],
                    }
                },
                {
                    'name': 'Medium Center Advisor',
                    'code': 'medium_center_advisor',
                    'role_type': 'service_advisor',
                    'description': 'Service advisor for medium centers with reception duties',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view', 'add', 'change'],
                        'inventory': ['view'],
                        'sales': ['view', 'add', 'change'],
                    }
                },
                {
                    'name': 'Medium Center Parts Specialist',
                    'code': 'medium_center_parts',
                    'role_type': 'parts_clerk',
                    'description': 'Combined parts and warehouse role for medium centers',
                    'is_active': True,
                    'modules': {
                        'inventory': ['view', 'add', 'change'],
                        'warehouse': ['view', 'add', 'change'],
                        'purchases': ['view', 'add'],
                    }
                },
                {
                    'name': 'Medium Center Cashier',
                    'code': 'medium_center_cashier',
                    'role_type': 'cashier',
                    'description': 'Cashier/reception combined role for medium centers',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view', 'add'],
                        'sales': ['view', 'add', 'change'],
                    }
                },
                
                # LARGE SERVICE CENTER ROLES
                # These are more specialized, similar to the standard roles
                # but with clearer separation of duties
                {
                    'name': 'Large Center Service Manager',
                    'code': 'large_center_service_manager',
                    'role_type': 'service_center_manager',
                    'description': 'Service operations manager for large centers',
                    'is_active': True,
                    'modules': {
                        'work_orders': ['view', 'add', 'change', 'approve'],
                        'inventory': ['view'],
                        'reports': ['view', 'report'],
                        'setup': ['view', 'add', 'change'],
                    }
                },
                {
                    'name': 'Large Center Parts Manager',
                    'code': 'large_center_parts_manager',
                    'role_type': 'inventory_manager',
                    'description': 'Dedicated parts and inventory manager for large centers',
                    'is_active': True,
                    'modules': {
                        'inventory': ['view', 'add', 'change', 'approve'],
                        'warehouse': ['view', 'add', 'change', 'approve'],
                        'purchases': ['view', 'add', 'change'],
                        'reports': ['view', 'report'],
                    }
                },
                {
                    'name': 'Large Center Customer Service Manager',
                    'code': 'large_center_customer_manager',
                    'role_type': 'service_advisor',
                    'description': 'Customer service team manager for large centers',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add'],
                        'work_orders': ['view', 'add', 'change', 'approve'],
                        'sales': ['view', 'add', 'change'],
                        'reports': ['view'],
                    }
                },
            ]
            
            for role_data in combined_roles_data:
                modules = role_data.pop('modules')
                
                # Create or update role
                role, created = Role.objects.update_or_create(
                    code=role_data['code'],
                    defaults=role_data
                )
                
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created combined role: {role.name}'))
                else:
                    self.stdout.write(self.style.WARNING(f'Updated combined role: {role.name}'))
                
                # Add module permissions
                for module, actions in modules.items():
                    for action in actions:
                        ModulePermission.objects.get_or_create(
                            role=role,
                            module=module,
                            action=action
                        )
                
            self.stdout.write(self.style.SUCCESS('Successfully created combined roles for different service center sizes')) 