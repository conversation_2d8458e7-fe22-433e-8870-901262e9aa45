from django.core.management.base import BaseCommand
from django.db.utils import IntegrityError
from user_roles.models import Role
from django.utils.translation import gettext_lazy as _


class Command(BaseCommand):
    help = 'Creates default roles for the application'

    def handle(self, *args, **options):
        # Create default roles
        try:
            created_roles = Role.create_default_roles()
            
            if created_roles:
                self.stdout.write(self.style.SUCCESS(
                    _('Successfully created {} new roles:').format(len(created_roles))
                ))
                
                # List all created roles
                for role in created_roles:
                    self.stdout.write(self.style.SUCCESS(
                        f'  - {role.name} (code: {role.code})'
                    ))
            else:
                self.stdout.write(self.style.WARNING(
                    _('No new roles created - all default roles already exist.')
                ))
                
            # Display total number of roles in the system
            total_roles = Role.objects.count()
            self.stdout.write(self.style.SUCCESS(
                _('Total roles in system: {}').format(total_roles)
            ))
            
        except IntegrityError as e:
            self.stdout.write(self.style.ERROR(
                _('Error creating roles: {}').format(str(e))
            ))
        except Exception as e:
            self.stdout.write(self.style.ERROR(
                _('Unexpected error: {}').format(str(e))
            )) 