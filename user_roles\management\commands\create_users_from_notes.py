from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from user_roles.models import Role, UserRole
import os


class Command(BaseCommand):
    help = 'Create users from notes.md file data with appropriate roles'

    def add_arguments(self, parser):
        parser.add_argument(
            '--notes-file',
            type=str,
            default='notes.md',
            help='Path to the notes file (default: notes.md)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating users'
        )

    def handle(self, *args, **options):
        notes_file = options['notes_file']
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No users will be created'))
        
        # Read the notes file
        try:
            with open(notes_file, 'r', encoding='utf-8') as file:
                lines = file.readlines()
        except FileNotFoundError:
            raise CommandError(f'Notes file "{notes_file}" not found')
        
        # Parse user data
        user_data = []
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                # Parse format: username | password (description)
                if '|' not in line:
                    self.stdout.write(
                        self.style.WARNING(f'Skipping line {line_num}: Invalid format - {line}')
                    )
                    continue
                
                username_part, rest = line.split('|', 1)
                username = username_part.strip()
                
                if '(' not in rest or ')' not in rest:
                    self.stdout.write(
                        self.style.WARNING(f'Skipping line {line_num}: No description found - {line}')
                    )
                    continue
                
                password = rest[:rest.find('(')].strip()
                description = rest[rest.find('(')+1:rest.find(')')].strip()
                
                user_data.append({
                    'username': username,
                    'password': password,
                    'description': description
                })
                
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'Skipping line {line_num}: Error parsing - {e}')
                )
                continue
        
        if not user_data:
            raise CommandError('No valid user data found in notes file')
        
        self.stdout.write(f'Found {len(user_data)} users to create:')
        for data in user_data:
            self.stdout.write(f'  - {data["username"]} ({data["description"]})')
        
        if dry_run:
            return
        
        # Create users and assign roles
        created_count = 0
        updated_count = 0
        
        with transaction.atomic():
            for data in user_data:
                try:
                    # Create or get user
                    user, created = User.objects.get_or_create(
                        username=data['username'],
                        defaults={
                            'email': f'{data["username"]}@example.com',
                            'first_name': data['username'].replace('_', ' ').title(),
                            'is_staff': True,  # Allow admin access
                            'is_active': True,
                        }
                    )
                    
                    # Set password
                    user.set_password(data['password'])
                    user.save()
                    
                    if created:
                        created_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'✓ Created user: {data["username"]}')
                        )
                    else:
                        updated_count += 1
                        self.stdout.write(
                            self.style.WARNING(f'! Updated existing user: {data["username"]}')
                        )
                    
                    # Map username to role code
                    role_code = self.map_username_to_role(data['username'])
                    
                    if role_code:
                        try:
                            role = Role.objects.get(code=role_code)
                            
                            # Create or update user role assignment
                            user_role, role_created = UserRole.objects.get_or_create(
                                user=user,
                                role=role,
                                defaults={
                                    'is_primary': True,
                                    'is_active': True,
                                    'tenant_id': 1,  # Default tenant
                                }
                            )
                            
                            if role_created:
                                self.stdout.write(
                                    self.style.SUCCESS(f'  → Assigned role: {role.name}')
                                )
                            else:
                                self.stdout.write(
                                    self.style.WARNING(f'  → Role already assigned: {role.name}')
                                )
                                
                        except Role.DoesNotExist:
                            self.stdout.write(
                                self.style.ERROR(f'  × Role not found: {role_code}')
                            )
                    else:
                        self.stdout.write(
                            self.style.WARNING(f'  ? No role mapping found for: {data["username"]}')
                        )
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error creating user {data["username"]}: {e}')
                    )
        
        # Summary
        self.stdout.write(self.style.SUCCESS(f'\n✅ User creation completed:'))
        self.stdout.write(self.style.SUCCESS(f'   - Created: {created_count} users'))
        self.stdout.write(self.style.SUCCESS(f'   - Updated: {updated_count} users'))
        self.stdout.write(self.style.SUCCESS(f'   - Total processed: {len(user_data)} users'))
        
        # Suggest next steps
        self.stdout.write(self.style.SUCCESS(f'\n📝 Next steps:'))
        self.stdout.write(f'   1. Run: python manage.py create_default_roles (if not done already)')
        self.stdout.write(f'   2. Check user roles in admin panel')
        self.stdout.write(f'   3. Assign users to appropriate franchises/companies/service centers')

    def map_username_to_role(self, username):
        """
        Map usernames from notes.md to role codes in the system
        """
        role_mapping = {
            'system_admin': 'system_admin',
            'franchise_admin': 'franchise_admin', 
            'company_admin': 'company_admin',
            'center_manager': 'service_center_manager',
            'service_advisor': 'service_advisor',
            'technician': 'technician',
            'parts_clerk': 'parts_clerk',
            'cashier': 'cashier',
            'small_manager': 'small_center_manager',
            'small_advisor': 'small_center_advisor',
            'medium_manager': 'medium_center_manager',
            'medium_parts': 'medium_center_parts',
            'large_service_mgr': 'large_service_manager',
            'large_parts_mgr': 'large_parts_manager',
        }
        
        return role_mapping.get(username) 