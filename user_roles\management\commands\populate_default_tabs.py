from django.core.management.base import BaseCommand
from django.db import transaction
from user_roles.models import ModuleTab, Role, RoleModulePermission
from django.utils.translation import gettext_lazy as _


class Command(BaseCommand):
    help = 'Populate default ModuleTab entries and permissions for the existing navigation system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting to populate default module tabs and permissions...'))
        
        with transaction.atomic():
            # Create default module tabs
            self.create_default_tabs()
            
            # Create default permissions for existing roles
            self.create_default_permissions()
            
        self.stdout.write(self.style.SUCCESS('Successfully populated default module tabs and permissions!'))

    def create_default_tabs(self):
        """Create default ModuleTab entries for existing navigation"""
        
        # Define the default tabs based on existing Arabic navigation
        default_tabs = [
            {
                'code': 'dashboard',
                'name_ar': 'الرئيسية',
                'name_en': 'Dashboard',
                'url_name': 'core:main_dashboard',
                'icon_class': 'fas fa-tachometer-alt',
                'order': 1,
                'requires_authentication': True,
                'is_system_admin_only': False
            },
            {
                'code': 'supply_chain',
                'name_ar': 'سلسلة التوريد',
                'name_en': 'Supply Chain',
                'url_name': 'inventory:dashboard',
                'icon_class': 'fas fa-sitemap',
                'order': 2,
                'requires_authentication': True,
                'is_system_admin_only': False
            },
            {
                'code': 'inventory',
                'name_ar': 'المخزون',
                'name_en': 'Inventory',
                'url_name': 'inventory:item_list',
                'icon_class': 'fas fa-boxes',
                'order': 3,
                'requires_authentication': True,
                'is_system_admin_only': False,
                'parent_tab_code': 'supply_chain'
            },
            {
                'code': 'warehouse',
                'name_ar': 'المستودع',
                'name_en': 'Warehouse',
                'url_name': 'warehouse:dashboard',
                'icon_class': 'fas fa-warehouse',
                'order': 4,
                'requires_authentication': True,
                'is_system_admin_only': False,
                'parent_tab_code': 'supply_chain'
            },
            {
                'code': 'purchases',
                'name_ar': 'المشتريات',
                'name_en': 'Purchases',
                'url_name': 'purchases:dashboard',
                'icon_class': 'fas fa-shopping-cart',
                'order': 5,
                'requires_authentication': True,
                'is_system_admin_only': False,
                'parent_tab_code': 'supply_chain'
            },
            {
                'code': 'sales',
                'name_ar': 'المبيعات',
                'name_en': 'Sales',
                'url_name': 'sales:dashboard',
                'icon_class': 'fas fa-chart-line',
                'order': 6,
                'requires_authentication': True,
                'is_system_admin_only': False
            },
            {
                'code': 'work_orders',
                'name_ar': 'أوامر العمل',
                'name_en': 'Work Orders',
                'url_name': 'work_orders:dashboard',
                'icon_class': 'fas fa-tasks',
                'order': 7,
                'requires_authentication': True,
                'is_system_admin_only': False
            },
            {
                'code': 'cashier',
                'name_ar': 'الكاشير',
                'name_en': 'Cashier',
                'url_name': 'billing:cashier_dashboard',
                'icon_class': 'fas fa-cash-register',
                'order': 8,
                'requires_authentication': True,
                'is_system_admin_only': False
            },
            {
                'code': 'billing',
                'name_ar': 'الفواتير',
                'name_en': 'Billing',
                'url_name': 'billing:dashboard',
                'icon_class': 'fas fa-file-invoice-dollar',
                'order': 9,
                'requires_authentication': True,
                'is_system_admin_only': False
            },
            {
                'code': 'reports',
                'name_ar': 'التقارير',
                'name_en': 'Reports',
                'url_name': 'reports:report_list',
                'icon_class': 'fas fa-chart-bar',
                'order': 10,
                'requires_authentication': True,
                'is_system_admin_only': False
            },
            {
                'code': 'settings',
                'name_ar': 'الإعدادات',
                'name_en': 'Settings',
                'url_name': 'app_settings:settings',
                'icon_class': 'fas fa-cogs',
                'order': 11,
                'requires_authentication': True,
                'is_system_admin_only': False
            },
            {
                'code': 'setup',
                'name_ar': 'إعداد النظام',
                'name_en': 'System Setup',
                'url_name': 'setup:dashboard',
                'icon_class': 'fas fa-wrench',
                'order': 12,
                'requires_authentication': True,
                'is_system_admin_only': True
            }
        ]
        
        # Create tabs with parent-child relationships
        created_tabs = {}
        
        # First pass: Create all tabs without parent relationships
        for tab_data in default_tabs:
            tab_code = tab_data['code']
            
            # Check if tab already exists
            if ModuleTab.objects.filter(code=tab_code).exists():
                self.stdout.write(f'Tab {tab_code} already exists, skipping...')
                created_tabs[tab_code] = ModuleTab.objects.get(code=tab_code)
                continue
            
            # Create tab without parent first
            tab = ModuleTab.objects.create(
                code=tab_data['code'],
                name_ar=tab_data['name_ar'],
                name_en=tab_data['name_en'],
                url_name=tab_data['url_name'],
                icon_class=tab_data['icon_class'],
                order=tab_data['order'],
                requires_authentication=tab_data['requires_authentication'],
                is_system_admin_only=tab_data['is_system_admin_only']
            )
            
            created_tabs[tab_code] = tab
            self.stdout.write(f'Created tab: {tab_code} ({tab_data["name_ar"]})')
        
        # Second pass: Set parent relationships
        for tab_data in default_tabs:
            if 'parent_tab_code' in tab_data:
                child_tab = created_tabs[tab_data['code']]
                parent_tab = created_tabs[tab_data['parent_tab_code']]
                
                child_tab.parent_tab = parent_tab
                child_tab.save()
                
                self.stdout.write(f'Set parent relationship: {tab_data["code"]} -> {tab_data["parent_tab_code"]}')

    def create_default_permissions(self):
        """Create default RoleModulePermission entries for existing roles"""
        
        # Get all active roles and tabs
        roles = Role.objects.filter(is_active=True)
        tabs = ModuleTab.objects.filter(is_active=True)
        
        # Define role-based permission templates
        permission_templates = {
            'system_admin': {
                'all_tabs': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': True,
                    'can_approve': True,
                    'can_report': True,
                    'scope_level': 'system'
                }
            },
            'franchise_admin': {
                'all_tabs': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': True,
                    'can_approve': True,
                    'can_report': True,
                    'scope_level': 'franchise'
                },
                'excluded_tabs': ['setup']  # Franchise admin cannot access system setup
            },
            'company_admin': {
                'all_tabs': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': False,
                    'can_approve': True,
                    'can_report': True,
                    'scope_level': 'company'
                },
                'excluded_tabs': ['setup']
            },
            'service_center_manager': {
                'allowed_tabs': ['dashboard', 'inventory', 'warehouse', 'sales', 'work_orders', 'cashier', 'billing', 'reports'],
                'permissions': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': False,
                    'can_approve': True,
                    'can_report': True,
                    'scope_level': 'service_center'
                }
            },
            'service_advisor': {
                'allowed_tabs': ['dashboard', 'work_orders', 'sales', 'cashier', 'reports'],
                'permissions': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': False,
                    'can_approve': False,
                    'can_report': True,
                    'scope_level': 'service_center'
                }
            },
            'technician': {
                'allowed_tabs': ['dashboard', 'work_orders', 'inventory'],
                'permissions': {
                    'can_view': True,
                    'can_add': False,
                    'can_edit': True,
                    'can_delete': False,
                    'can_approve': False,
                    'can_report': False,
                    'scope_level': 'own_data'
                }
            },
            'inventory_manager': {
                'allowed_tabs': ['dashboard', 'inventory', 'warehouse', 'purchases', 'reports'],
                'permissions': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': False,
                    'can_approve': True,
                    'can_report': True,
                    'scope_level': 'service_center'
                }
            },
            'parts_clerk': {
                'allowed_tabs': ['dashboard', 'inventory', 'warehouse', 'purchases'],
                'permissions': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': False,
                    'can_approve': False,
                    'can_report': False,
                    'scope_level': 'service_center'
                }
            },
            'cashier': {
                'allowed_tabs': ['dashboard', 'sales', 'cashier', 'billing'],
                'permissions': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': False,
                    'can_approve': False,
                    'can_report': False,
                    'scope_level': 'service_center'
                }
            },
            'accountant': {
                'allowed_tabs': ['dashboard', 'billing', 'reports'],
                'permissions': {
                    'can_view': True,
                    'can_add': True,
                    'can_edit': True,
                    'can_delete': False,
                    'can_approve': True,
                    'can_report': True,
                    'scope_level': 'service_center'
                }
            },
            'readonly': {
                'allowed_tabs': ['dashboard', 'reports'],
                'permissions': {
                    'can_view': True,
                    'can_add': False,
                    'can_edit': False,
                    'can_delete': False,
                    'can_approve': False,
                    'can_report': True,
                    'scope_level': 'service_center'
                }
            }
        }
        
        # Create permissions for each role
        for role in roles:
            role_template = permission_templates.get(role.role_type)
            if not role_template:
                self.stdout.write(f'No permission template found for role type: {role.role_type}')
                continue
            
            for tab in tabs:
                # Check if permission already exists
                if RoleModulePermission.objects.filter(role=role, module_tab=tab).exists():
                    continue
                
                # Determine if this role should have access to this tab
                should_create_permission = False
                permission_data = {}
                
                if 'all_tabs' in role_template:
                    # Role has access to all tabs (admin roles)
                    excluded_tabs = role_template.get('excluded_tabs', [])
                    if tab.code not in excluded_tabs:
                        should_create_permission = True
                        permission_data = role_template['all_tabs']
                
                elif 'allowed_tabs' in role_template:
                    # Role has access to specific tabs
                    if tab.code in role_template['allowed_tabs']:
                        should_create_permission = True
                        permission_data = role_template['permissions']
                
                if should_create_permission:
                    RoleModulePermission.objects.create(
                        role=role,
                        module_tab=tab,
                        **permission_data
                    )
                    self.stdout.write(f'Created permission: {role.name} -> {tab.code}')
        
        self.stdout.write(self.style.SUCCESS('Default permissions created successfully!'))

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing tabs and permissions',
        ) 