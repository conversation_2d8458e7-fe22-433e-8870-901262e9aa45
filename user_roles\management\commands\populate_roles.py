from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from django.db import transaction
from user_roles.models import Role, ModulePermission

class Command(BaseCommand):
    help = 'Populates the Role model with predefined roles'

    def handle(self, *args, **options):
        with transaction.atomic():
            # Define the roles to create or update
            roles_data = [
                # Existing roles
                {
                    'name': 'System Administrator',
                    'code': 'system_admin',
                    'role_type': 'system_admin',
                    'description': 'Full access to all system features',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add', 'change', 'delete', 'approve'],
                        'work_orders': ['view', 'add', 'change', 'delete', 'approve'],
                        'inventory': ['view', 'add', 'change', 'delete', 'approve'],
                        'warehouse': ['view', 'add', 'change', 'delete', 'approve'],
                        'sales': ['view', 'add', 'change', 'delete', 'approve'],
                        'purchases': ['view', 'add', 'change', 'delete', 'approve'],
                        'reports': ['view', 'report'],
                        'settings': ['view', 'add', 'change', 'delete', 'approve'],
                    }
                },
                {
                    'name': 'Franchise Administrator',
                    'code': 'franchise_admin',
                    'role_type': 'franchise_admin',
                    'description': 'Administers a franchise and its companies',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add', 'change', 'approve'],
                        'work_orders': ['view', 'add', 'change', 'approve'],
                        'inventory': ['view', 'add', 'change', 'approve'],
                        'warehouse': ['view', 'add', 'change', 'approve'],
                        'sales': ['view', 'add', 'change', 'approve'],
                        'purchases': ['view', 'add', 'change', 'approve'],
                        'reports': ['view', 'report'],
                        'settings': ['view', 'change'],
                    }
                },
                {
                    'name': 'Company Administrator',
                    'code': 'company_admin',
                    'role_type': 'company_admin',
                    'description': 'Administers a company and its service centers',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add', 'change'],
                        'work_orders': ['view', 'add', 'change', 'approve'],
                        'inventory': ['view', 'add', 'change', 'approve'],
                        'warehouse': ['view', 'add', 'change', 'approve'],
                        'sales': ['view', 'add', 'change', 'approve'],
                        'purchases': ['view', 'add', 'change', 'approve'],
                        'reports': ['view', 'report'],
                        'settings': ['view', 'change'],
                    }
                },
                {
                    'name': 'Service Center Manager',
                    'code': 'service_center_manager',
                    'role_type': 'service_center_manager',
                    'description': 'Manages a service center',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add', 'change'],
                        'work_orders': ['view', 'add', 'change', 'approve'],
                        'inventory': ['view', 'add', 'change', 'approve'],
                        'warehouse': ['view', 'add', 'change', 'approve'],
                        'sales': ['view', 'add', 'change', 'approve'],
                        'purchases': ['view', 'add', 'change', 'approve'],
                        'reports': ['view', 'report'],
                        'settings': ['view'],
                    }
                },
                {
                    'name': 'Service Advisor',
                    'code': 'service_advisor',
                    'role_type': 'service_advisor',
                    'description': 'Advises customers and creates work orders',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view', 'add', 'change'],
                        'inventory': ['view'],
                        'warehouse': ['view'],
                        'sales': ['view', 'add', 'change'],
                        'reports': ['view'],
                    }
                },
                {
                    'name': 'Technician',
                    'code': 'technician',
                    'role_type': 'technician',
                    'description': 'Performs technical work on vehicles',
                    'is_active': True,
                    'modules': {
                        'work_orders': ['view', 'change'],
                        'inventory': ['view'],
                        'warehouse': ['view'],
                    }
                },
                {
                    'name': 'Inventory Manager',
                    'code': 'inventory_manager',
                    'role_type': 'inventory_manager',
                    'description': 'Manages inventory and warehouse',
                    'is_active': True,
                    'modules': {
                        'inventory': ['view', 'add', 'change', 'delete'],
                        'warehouse': ['view', 'add', 'change', 'delete'],
                        'purchases': ['view', 'add', 'change'],
                        'reports': ['view', 'report'],
                    }
                },
                {
                    'name': 'Parts Clerk',
                    'code': 'parts_clerk',
                    'role_type': 'parts_clerk',
                    'description': 'Handles parts and inventory',
                    'is_active': True,
                    'modules': {
                        'inventory': ['view', 'add', 'change'],
                        'warehouse': ['view', 'add', 'change'],
                        'purchases': ['view'],
                    }
                },
                {
                    'name': 'Accountant',
                    'code': 'accountant',
                    'role_type': 'accountant',
                    'description': 'Handles financial aspects',
                    'is_active': True,
                    'modules': {
                        'sales': ['view'],
                        'purchases': ['view'],
                        'reports': ['view', 'report'],
                    }
                },
                {
                    'name': 'Receptionist',
                    'code': 'receptionist',
                    'role_type': 'receptionist',
                    'description': 'Front desk operations',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view', 'add'],
                    }
                },
                {
                    'name': 'Customer Service',
                    'code': 'customer_service',
                    'role_type': 'customer_service',
                    'description': 'Handles customer inquiries and support',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view'],
                        'sales': ['view'],
                    }
                },
                {
                    'name': 'Read Only User',
                    'code': 'readonly',
                    'role_type': 'readonly',
                    'description': 'View-only access to all modules',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view'],
                        'inventory': ['view'],
                        'warehouse': ['view'],
                        'sales': ['view'],
                        'purchases': ['view'],
                        'reports': ['view'],
                        'settings': ['view'],
                    }
                },
                
                # New roles based on recommendations
                {
                    'name': 'Warranty Administrator',
                    'code': 'warranty_admin',
                    'role_type': 'warranty_admin',
                    'description': 'Processes warranty claims and insurance matters',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view', 'change'],
                        'sales': ['view', 'change'],
                        'reports': ['view', 'report'],
                    }
                },
                {
                    'name': 'Finance Manager',
                    'code': 'finance_manager',
                    'role_type': 'finance_manager',
                    'description': 'Oversees financial aspects, approves special pricing',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view', 'approve'],
                        'sales': ['view', 'change', 'approve'],
                        'purchases': ['view', 'change', 'approve'],
                        'reports': ['view', 'report'],
                    }
                },
                {
                    'name': 'Marketing Manager',
                    'code': 'marketing_manager',
                    'role_type': 'marketing_manager',
                    'description': 'Manages promotions, discounts, and customer classification',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'change'],
                        'sales': ['view', 'change'],
                        'reports': ['view', 'report'],
                    }
                },
                {
                    'name': 'Quality Control Inspector',
                    'code': 'quality_inspector',
                    'role_type': 'quality_inspector',
                    'description': 'Performs final checks on completed work',
                    'is_active': True,
                    'modules': {
                        'work_orders': ['view', 'change', 'approve'],
                        'inventory': ['view'],
                    }
                },
                {
                    'name': 'Fleet Account Manager',
                    'code': 'fleet_manager',
                    'role_type': 'fleet_manager',
                    'description': 'Handles corporate/fleet customer accounts',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'add', 'change'],
                        'work_orders': ['view', 'add', 'change'],
                        'sales': ['view', 'add', 'change'],
                        'reports': ['view', 'report'],
                    }
                },
                {
                    'name': 'Regional Manager',
                    'code': 'regional_manager',
                    'role_type': 'regional_manager',
                    'description': 'Oversees multiple service centers',
                    'is_active': True,
                    'modules': {
                        'setup': ['view', 'change', 'approve'],
                        'work_orders': ['view', 'approve'],
                        'inventory': ['view', 'approve'],
                        'warehouse': ['view', 'approve'],
                        'sales': ['view', 'approve'],
                        'purchases': ['view', 'approve'],
                        'reports': ['view', 'report'],
                    }
                },
                {
                    'name': 'Cashier/Billing Specialist',
                    'code': 'cashier',
                    'role_type': 'cashier',
                    'description': 'Handles payments, invoices, and billing',
                    'is_active': True,
                    'modules': {
                        'setup': ['view'],
                        'work_orders': ['view'],
                        'sales': ['view', 'add', 'change'],
                        'reports': ['view'],
                    }
                },
            ]
            
            for role_data in roles_data:
                modules = role_data.pop('modules')
                
                # Create or update role
                role, created = Role.objects.update_or_create(
                    code=role_data['code'],
                    defaults=role_data
                )
                
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created role: {role.name}'))
                else:
                    self.stdout.write(self.style.WARNING(f'Updated role: {role.name}'))
                
                # Add module permissions
                for module, actions in modules.items():
                    for action in actions:
                        ModulePermission.objects.get_or_create(
                            role=role,
                            module=module,
                            action=action
                        )
                
            self.stdout.write(self.style.SUCCESS('Successfully populated roles')) 