from django.core.management.base import BaseCommand, CommandError
from django.db.utils import IntegrityError
from user_roles.models import Role
from setup.models import ServiceCenter
from django.utils.translation import gettext_lazy as _


class Command(BaseCommand):
    help = 'Sets up recommended roles for a service center based on its size'

    def add_arguments(self, parser):
        parser.add_argument('service_center_code', type=str, help='Service center code')
        parser.add_argument('--create-roles', action='store_true', help='Create missing default roles if needed')

    def handle(self, *args, **options):
        service_center_code = options['service_center_code']
        create_roles = options['create_roles']
        
        try:
            # Find the service center
            try:
                service_center = ServiceCenter.objects.get(code=service_center_code)
            except ServiceCenter.DoesNotExist:
                raise CommandError(_('Service center with code {} does not exist').format(service_center_code))
            
            # Create default roles if requested
            if create_roles:
                Role.create_default_roles()
                self.stdout.write(self.style.SUCCESS(_('Default roles have been created.')))
            
            # Get recommended roles for this service center
            roles = Role.setup_service_center_roles(service_center)
            
            if not roles:
                self.stdout.write(self.style.WARNING(
                    _('No roles found for service center size: {}').format(service_center.size)
                ))
                return
            
            # Display recommended roles
            self.stdout.write(self.style.SUCCESS(
                _('Recommended roles for {} (size: {}):').format(service_center.name, service_center.size)
            ))
            
            for role in roles:
                self.stdout.write(self.style.SUCCESS(
                    f'  - {role.name} (code: {role.code})'
                ))
                
            self.stdout.write("")
            self.stdout.write(self.style.SUCCESS(
                _('To assign these roles to users, use the Django admin interface or API.')
            ))
            
        except IntegrityError as e:
            self.stdout.write(self.style.ERROR(
                _('Database error: {}').format(str(e))
            ))
        except Exception as e:
            self.stdout.write(self.style.ERROR(
                _('Unexpected error: {}').format(str(e))
            )) 