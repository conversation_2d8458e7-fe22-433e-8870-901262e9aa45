from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from user_roles.models import ModuleTab, Role, RoleModulePermission, UserRole
from user_roles.services import PermissionService, NavigationService, DataFilterService
from setup.models import ServiceCenter, Company, Franchise
from django.db import transaction


class Command(BaseCommand):
    help = 'Test the complete permission system integration'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting permission system integration tests...'))
        
        # Test 1: Check if ModuleTab entries were created
        self.test_module_tabs()
        
        # Test 2: Check if permissions were created
        self.test_permissions()
        
        # Test 3: Test service integration
        self.test_services()
        
        # Test 4: Test specific user scenarios
        self.test_user_scenarios()
        
        self.stdout.write(self.style.SUCCESS('Permission system integration tests completed!'))

    def test_module_tabs(self):
        """Test that ModuleTab entries were created correctly"""
        self.stdout.write('\n=== Test 1: ModuleTab Creation ===')
        
        tabs = ModuleTab.objects.all().order_by('order')
        self.stdout.write(f'Total tabs created: {tabs.count()}')
        
        expected_tabs = [
            'dashboard', 'supply_chain', 'inventory', 'warehouse', 'purchases',
            'sales', 'work_orders', 'cashier', 'billing', 'reports', 'settings', 'setup'
        ]
        
        existing_tabs = [tab.code for tab in tabs]
        missing_tabs = set(expected_tabs) - set(existing_tabs)
        
        if missing_tabs:
            self.stdout.write(self.style.WARNING(f'Missing tabs: {missing_tabs}'))
        else:
            self.stdout.write(self.style.SUCCESS('[OK] All expected tabs created'))
        
        # Check parent-child relationships
        supply_chain = ModuleTab.objects.filter(code='supply_chain').first()
        if supply_chain:
            sub_tabs = supply_chain.sub_tabs.all()
            self.stdout.write(f'Supply Chain sub-tabs: {[t.code for t in sub_tabs]}')
            
            expected_sub_tabs = ['inventory', 'warehouse', 'purchases']
            actual_sub_tabs = [t.code for t in sub_tabs]
            
            if set(expected_sub_tabs).issubset(set(actual_sub_tabs)):
                self.stdout.write(self.style.SUCCESS('[OK] Parent-child relationships correct'))
            else:
                self.stdout.write(self.style.ERROR('[ERROR] Parent-child relationships incorrect'))
        
        # Display all tabs
        for tab in tabs:
            parent_info = f" (parent: {tab.parent_tab.code})" if tab.parent_tab else ""
            self.stdout.write(f'- {tab.code}: {tab.name_ar} ({tab.name_en}){parent_info}')

    def test_permissions(self):
        """Test that permissions were created correctly"""
        self.stdout.write('\n=== Test 2: Permission Creation ===')
        
        permissions = RoleModulePermission.objects.all()
        self.stdout.write(f'Total permissions created: {permissions.count()}')
        
        # Check permissions by role type
        role_types = ['system_admin', 'franchise_admin', 'company_admin', 'service_center_manager', 'service_advisor']
        
        for role_type in role_types:
            roles = Role.objects.filter(role_type=role_type)
            if roles.exists():
                role = roles.first()
                role_permissions = RoleModulePermission.objects.filter(role=role)
                self.stdout.write(f'{role.name} ({role_type}): {role_permissions.count()} permissions')
                
                # Show sample permissions
                for perm in role_permissions[:3]:
                    permissions_str = []
                    if perm.can_view: permissions_str.append('view')
                    if perm.can_add: permissions_str.append('add')
                    if perm.can_edit: permissions_str.append('edit')
                    if perm.can_delete: permissions_str.append('delete')
                    if perm.can_approve: permissions_str.append('approve')
                    if perm.can_report: permissions_str.append('report')
                    
                    self.stdout.write(f'  - {perm.module_tab.code}: {", ".join(permissions_str)} (scope: {perm.scope_level})')

    def test_services(self):
        """Test that services work correctly"""
        self.stdout.write('\n=== Test 3: Service Integration ===')
        
        try:
            # Test with superuser if exists
            superuser = User.objects.filter(is_superuser=True).first()
            if superuser:
                self.stdout.write(f'Testing with superuser: {superuser.username}')
                
                # Test PermissionService
                permissions = PermissionService.get_user_permissions(superuser)
                self.stdout.write(f'[OK] PermissionService: {len(permissions)} tab permissions loaded')
                
                # Test specific permission check
                can_view_dashboard = PermissionService.can_access_tab(superuser, 'dashboard', 'view')
                self.stdout.write(f'[OK] Can access dashboard: {can_view_dashboard}')
                
                # Test NavigationService
                navigation = NavigationService.build_main_navigation(superuser)
                self.stdout.write(f'[OK] NavigationService: {len(navigation)} main navigation items')
                
                # Test DataFilterService
                scope = DataFilterService.get_user_data_scope(superuser)
                self.stdout.write(f'[OK] DataFilterService: User scope = {scope["scope"]}')
                
                # Test breadcrumb generation
                breadcrumbs = NavigationService.get_breadcrumb_path(superuser, 'inventory')
                self.stdout.write(f'[OK] Breadcrumbs for inventory: {[b["name_localized"] for b in breadcrumbs]}')
                
            else:
                self.stdout.write(self.style.WARNING('No superuser found - creating test user'))
                self.create_test_user()
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'[ERROR] Error testing services: {e}'))
            import traceback
            traceback.print_exc()

    def test_user_scenarios(self):
        """Test specific user scenarios"""
        self.stdout.write('\n=== Test 4: User Scenario Testing ===')
        
        # Test different role types
        test_roles = [
            ('system_admin', 'System Administrator'),
            ('service_advisor', 'Service Advisor'),
            ('technician', 'Technician'),
            ('cashier', 'Cashier')
        ]
        
        for role_type, role_name in test_roles:
            role = Role.objects.filter(role_type=role_type).first()
            if role:
                self.stdout.write(f'\nTesting {role_name}:')
                
                # Get role permissions
                role_permissions = RoleModulePermission.objects.filter(role=role)
                accessible_tabs = [perm.module_tab.code for perm in role_permissions if perm.can_view]
                
                self.stdout.write(f'  - Can access tabs: {accessible_tabs}')
                
                # Test specific tabs
                key_tabs = ['dashboard', 'sales', 'work_orders', 'inventory', 'setup']
                for tab_code in key_tabs:
                    has_permission = role_permissions.filter(module_tab__code=tab_code, can_view=True).exists()
                    status = '[OK]' if has_permission else '[NO]'
                    self.stdout.write(f'  - {tab_code}: {status}')

    def create_test_user(self):
        """Create a test user for testing"""
        self.stdout.write('Creating test superuser...')
        
        try:
            test_user = User.objects.create_superuser(
                username='test_admin',
                email='<EMAIL>',
                password='testpass123'
            )
            
            self.stdout.write(f'[OK] Created test superuser: {test_user.username}')
            
            # Test services with this user
            permissions = PermissionService.get_user_permissions(test_user)
            self.stdout.write(f'[OK] Test user permissions loaded: {len(permissions)} tabs')
            
            navigation = NavigationService.build_main_navigation(test_user)
            self.stdout.write(f'[OK] Test user navigation: {len(navigation)} main items')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'[ERROR] Error creating test user: {e}'))

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-user',
            action='store_true',
            help='Create a test superuser for testing',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        ) 