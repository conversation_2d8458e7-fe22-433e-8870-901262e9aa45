import logging
from django.conf import settings
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import resolve, reverse
from django.http import HttpResponseForbidden, JsonResponse
from functools import wraps
from .services import PermissionService, DataFilterService

logger = logging.getLogger(__name__)


def dynamic_permission_required(tab_code, action='view'):
    """
    Enhanced decorator for views that checks dynamic permissions.
    
    Args:
        tab_code: The tab code to check (e.g., 'dashboard', 'sales')
        action: The action to check ('view', 'add', 'edit', 'delete', 'approve', 'report')
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Superusers have all permissions
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            # Get permission context from request
            context = _get_permission_context_from_request(request)
            
            # Check if user has the required permission
            if PermissionService.can_access_tab(request.user, tab_code, action, context):
                return view_func(request, *args, **kwargs)
            else:
                logger.warning(
                    f"User {request.user.username} attempted to access {tab_code} "
                    f"with action '{action}' without permission."
                )
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': str(_("You don't have permission to perform this action.")),
                        'permission_required': f"{tab_code}:{action}"
                    }, status=403)
                
                messages.warning(request, _("You don't have permission to access this page."))
                return redirect(request.META.get('HTTP_REFERER', '/'))

        return _wrapped_view
    return decorator


def role_required(required_permission):
    """
    Legacy decorator for backward compatibility.
    Decorator for views that checks if a user has the required permission (module flag).
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Superusers have all permissions
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            # Get the user's primary role
            primary_role = None
            if hasattr(request.user, 'user_roles'):
                user_roles = request.user.user_roles.filter(is_active=True)
                for user_role_instance in user_roles:
                    if user_role_instance.is_primary:
                        primary_role = user_role_instance.role
                        break
                if not primary_role and user_roles.exists():
                    primary_role = user_roles.first().role
            
            if primary_role:
                if hasattr(primary_role, required_permission) and getattr(primary_role, required_permission):
                    return view_func(request, *args, **kwargs)
                else:
                    logger.warning(
                        f"User {request.user.username} with role {primary_role.name} "
                        f"attempted to access view requiring permission '{required_permission}' without it."
                    )
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return HttpResponseForbidden(_("You don't have permission to perform this action."))
                    messages.warning(request, _("You don't have permission to access this page."))
                    return redirect(request.META.get('HTTP_REFERER', '/'))
            else:
                logger.warning(
                    f"User {request.user.username} without a role attempted to access view "
                    f"requiring permission '{required_permission}'."
                )
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return HttpResponseForbidden(_("You need a role to access this page."))
                messages.warning(request, _("You need to be assigned a role to access this page."))
                return redirect('/')

        return _wrapped_view
    return decorator


def _get_permission_context_from_request(request):
    """
    Helper function to extract permission context from request
    """
    from setup.models import Franchise, Company, ServiceCenter
    
    context = {}
    
    # Try to get context from URL parameters
    if hasattr(request, 'GET'):
        franchise_id = request.GET.get('franchise_id')
        company_id = request.GET.get('company_id')
        service_center_id = request.GET.get('service_center_id')
        
        try:
            if service_center_id:
                context['service_center'] = ServiceCenter.objects.get(id=service_center_id)
            if company_id:
                context['company'] = Company.objects.get(id=company_id)
            if franchise_id:
                context['franchise'] = Franchise.objects.get(id=franchise_id)
        except (ServiceCenter.DoesNotExist, Company.DoesNotExist, Franchise.DoesNotExist, ValueError):
            pass
    
    return context


class DynamicPermissionMiddleware:
    """
    Enhanced middleware for dynamic permission checking
    """
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Map URL namespaces to tab codes
        self.namespace_to_tab = {
            'core': 'dashboard',
            'setup': 'setup',
            'work_orders': 'work_orders',
            'inventory': 'inventory',
            'warehouse': 'warehouse',
            'sales': 'sales',
            'purchases': 'purchases',
            'billing': 'billing',
            'reports': 'reports',
            'app_settings': 'settings',
            'franchise_setup': 'setup',
        }
        
        # Public URLs that don't require authentication
        self.public_urls = [
            reverse('login') if 'login' in [url.name for url in settings.ROOT_URLCONF if hasattr(url, 'name')] else '/login/',
            '/logout/',
            '/static/',
            '/media/',
            '/favicon.ico',
            '/i18n/setlang/',
        ]
        
        # Admin URLs that bypass this middleware
        self.admin_urls = [
            '/dzJAMvwB/',  # Admin URL
            '/admin/',
        ]

    def __call__(self, request):
        # Skip middleware check for public and admin URLs
        if any(request.path.startswith(url) for url in self.public_urls + self.admin_urls):
            return self.get_response(request)
        
        # Skip for unauthenticated users (let Django's auth middleware handle this)
        if not request.user.is_authenticated:
            return self.get_response(request)
        
        # Add permission context to request for use in views
        request.permission_context = _get_permission_context_from_request(request)
        
        # Get the URL namespace and determine the tab code
        try:
            resolved = resolve(request.path)
            namespace = resolved.namespace
            url_name = resolved.url_name
        except:
            namespace = None
            url_name = None
        
        # Determine which tab this URL belongs to
        tab_code = self._determine_tab_code(namespace, url_name, request.path)
        
        if tab_code:
            # Check if user can access this tab
            if not PermissionService.can_access_tab(request.user, tab_code, 'view', request.permission_context):
                logger.warning(
                    f"User {request.user.username} attempted to access {tab_code} without permission"
                )
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': str(_("You don't have permission to access this module")),
                        'tab_code': tab_code,
                        'required_permission': 'view'
                    }, status=403)
                
                messages.warning(
                    request, 
                    _("You don't have permission to access the {module} module").format(module=tab_code)
                )
                return redirect('/')
        
        response = self.get_response(request)
        return response
    
    def _determine_tab_code(self, namespace, url_name, path):
        """
        Determine the tab code from URL namespace, name, and path
        """
        # First try namespace mapping
        if namespace in self.namespace_to_tab:
            return self.namespace_to_tab[namespace]
        
        # Then try path-based detection
        path_segments = path.strip('/').split('/')
        
        # Common path patterns
        if 'setup' in path_segments:
            return 'setup'
        elif 'inventory' in path_segments:
            return 'inventory'
        elif 'warehouse' in path_segments:
            return 'warehouse'
        elif 'sales' in path_segments:
            return 'sales'
        elif 'purchases' in path_segments:
            return 'purchases'
        elif 'work_orders' in path_segments or 'work-orders' in path_segments:
            return 'work_orders'
        elif 'billing' in path_segments or 'cashier' in path_segments:
            return 'billing'
        elif 'reports' in path_segments:
            return 'reports'
        elif 'settings' in path_segments:
            return 'settings'
        
        # Default to dashboard for root and unknown paths
        return 'dashboard'


class DataFilterMiddleware:
    """
    Middleware to automatically apply data filtering based on user scope
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Add data scope information to request
        if hasattr(request, 'user') and request.user.is_authenticated:
            context = _get_permission_context_from_request(request)
            request.user_data_scope = DataFilterService.get_user_data_scope(request.user, context)
        else:
            request.user_data_scope = {'scope': 'none', 'filters': {}}

        response = self.get_response(request)
        return response


# Legacy middleware class for backward compatibility
class RoleBasedAccessMiddleware:
    """
    Legacy middleware to enforce role-based access control for URLs.
    This is maintained for backward compatibility.
    """
    def __init__(self, get_response):
        self.get_response = get_response
        # Map URL namespaces to required module access flags
        self.module_map = {
            'setup': 'can_access_setup',
            'work_orders': 'can_access_work_orders',
            'inventory': 'can_access_inventory',
            'warehouse': 'can_access_warehouse',
            'sales': 'can_access_sales',
            'purchases': 'can_access_purchases',
            'reports': 'can_access_reports',
            'app_settings': 'can_access_settings',
            'franchise_setup': 'can_access_setup',
        }
        
        # Public URLs that don't require authentication
        self.public_urls = [
            '/login/',
            '/logout/',
            '/static/',
            '/media/',
            '/favicon.ico',
            '/i18n/setlang/',
        ]
        
        # Admin URLs that bypass this middleware
        self.admin_urls = [
            '/dzJAMvwB/',  # Admin URL
        ]

    def __call__(self, request):
        # Skip middleware check for public and admin URLs
        if any(request.path.startswith(url) for url in self.public_urls + self.admin_urls):
            return self.get_response(request)
        
        # Skip for unauthenticated users (let Django's auth middleware handle this)
        if not request.user.is_authenticated:
            return self.get_response(request)
        
        # Get the URL namespace
        try:
            resolved = resolve(request.path)
            namespace = resolved.namespace
        except:
            namespace = None
        
        # If namespace is in our module map, check if the user has the required role
        if namespace in self.module_map:
            module_flag = self.module_map[namespace]
            
            # Get the user's primary role
            primary_role_obj = None
            if hasattr(request.user, 'user_roles'):
                user_roles = request.user.user_roles.filter(is_active=True)
                for user_role_instance in user_roles:
                    if user_role_instance.is_primary:
                        primary_role_obj = user_role_instance.role
                        break
                    
                if not primary_role_obj and user_roles.exists():
                    primary_role_obj = user_roles.first().role
            
            # If the user has a primary role, check if they can access this module
            if primary_role_obj:
                if not getattr(primary_role_obj, module_flag, False):
                    logger.warning(f"User {request.user.username} with role {primary_role_obj.name} attempted to access {namespace} without permission")
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return HttpResponseForbidden(_("You don't have permission to access this module"))
                    
                    messages.warning(request, _("You don't have permission to access the {module} module").format(module=namespace))
                    return redirect('/')
            
            # If the user doesn't have a primary role, check if they're a superuser
            elif not request.user.is_superuser:
                logger.warning(f"User {request.user.username} without a role attempted to access {namespace}")
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return HttpResponseForbidden(_("You don't have permission to access this module"))
                
                messages.warning(request, _("You need to be assigned a role to access the {module} module").format(module=namespace))
                return redirect('/')
        
        response = self.get_response(request)
        return response 