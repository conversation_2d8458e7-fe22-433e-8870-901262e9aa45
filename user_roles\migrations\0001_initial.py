# Generated by Django 4.2.20 on 2025-05-07 10:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('setup', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Role',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Role Name')),
                ('code', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50, unique=True, verbose_name='Role Code')),
                ('role_type', models.CharField(choices=[('system_admin', 'System Administrator'), ('franchise_admin', 'Franchise Administrator'), ('company_admin', 'Company Administrator'), ('service_center_manager', 'Service Center Manager'), ('service_advisor', 'Service Advisor'), ('technician', 'Technician'), ('inventory_manager', 'Inventory Manager'), ('parts_clerk', 'Parts Clerk'), ('accountant', 'Accountant'), ('receptionist', 'Receptionist'), ('customer_service', 'Customer Service'), ('readonly', 'Read Only User')], max_length=50, verbose_name='Role Type')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('can_access_setup', models.BooleanField(default=False, verbose_name='Can Access Setup')),
                ('can_access_work_orders', models.BooleanField(default=False, verbose_name='Can Access Work Orders')),
                ('can_access_inventory', models.BooleanField(default=False, verbose_name='Can Access Inventory')),
                ('can_access_warehouse', models.BooleanField(default=False, verbose_name='Can Access Warehouse')),
                ('can_access_sales', models.BooleanField(default=False, verbose_name='Can Access Sales')),
                ('can_access_purchases', models.BooleanField(default=False, verbose_name='Can Access Purchases')),
                ('can_access_reports', models.BooleanField(default=False, verbose_name='Can Access Reports')),
                ('can_access_settings', models.BooleanField(default=False, verbose_name='Can Access Settings')),
                ('group', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='role', to='auth.group', verbose_name='Permission Group')),
            ],
            options={
                'verbose_name': 'Role',
                'verbose_name_plural': 'Roles',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('is_primary', models.BooleanField(default=False, verbose_name='Is Primary Role')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to='setup.company', verbose_name='Company Scope')),
                ('franchise', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to='setup.franchise', verbose_name='Franchise Scope')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to='user_roles.role', verbose_name='Role')),
                ('service_center', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to='setup.servicecenter', verbose_name='Service Center Scope')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'User Role',
                'verbose_name_plural': 'User Roles',
                'ordering': ['-is_primary', 'role__name'],
            },
        ),
        migrations.CreateModel(
            name='ModulePermission',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('module', models.CharField(choices=[('setup', 'Setup'), ('work_orders', 'Work Orders'), ('inventory', 'Inventory'), ('warehouse', 'Warehouse'), ('sales', 'Sales'), ('purchases', 'Purchases'), ('reports', 'Reports'), ('settings', 'Settings')], max_length=50, verbose_name='Module')),
                ('action', models.CharField(choices=[('view', 'View'), ('add', 'Add'), ('change', 'Change'), ('delete', 'Delete'), ('approve', 'Approve'), ('report', 'Generate Reports')], max_length=50, verbose_name='Action')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='module_permissions', to='user_roles.role', verbose_name='Role')),
            ],
            options={
                'verbose_name': 'Module Permission',
                'verbose_name_plural': 'Module Permissions',
            },
        ),
        migrations.AlterUniqueTogether(
            name='modulepermission',
            unique_together={('role', 'module', 'action')},
        ),
    ]
