# Generated by Django 4.2.20 on 2025-07-03 17:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0018_add_nationality_field"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("user_roles", "0006_role_can_access_billing"),
    ]

    operations = [
        migrations.CreateModel(
            name="ModuleTab",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Tab Code"
                    ),
                ),
                (
                    "name_ar",
                    models.CharField(max_length=100, verbose_name="Arabic Name"),
                ),
                (
                    "name_en",
                    models.CharField(max_length=100, verbose_name="English Name"),
                ),
                ("url_name", models.CharField(max_length=100, verbose_name="URL Name")),
                (
                    "icon_class",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Icon CSS Class"
                    ),
                ),
                (
                    "order",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Display Order"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "requires_authentication",
                    models.BooleanField(
                        default=True, verbose_name="Requires Authentication"
                    ),
                ),
                (
                    "is_system_admin_only",
                    models.BooleanField(
                        default=False, verbose_name="System Admin Only"
                    ),
                ),
                (
                    "parent_tab",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_tabs",
                        to="user_roles.moduletab",
                        verbose_name="Parent Tab",
                    ),
                ),
            ],
            options={
                "verbose_name": "Module Tab",
                "verbose_name_plural": "Module Tabs",
                "ordering": ["order", "name_en"],
            },
        ),
        migrations.CreateModel(
            name="UserCustomPermission",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "permission_type",
                    models.CharField(
                        choices=[("grant", "Grant Access"), ("deny", "Deny Access")],
                        max_length=10,
                        verbose_name="Permission Type",
                    ),
                ),
                (
                    "can_view",
                    models.BooleanField(default=True, verbose_name="Can View"),
                ),
                ("can_add", models.BooleanField(default=False, verbose_name="Can Add")),
                (
                    "can_edit",
                    models.BooleanField(default=False, verbose_name="Can Edit"),
                ),
                (
                    "can_delete",
                    models.BooleanField(default=False, verbose_name="Can Delete"),
                ),
                (
                    "can_approve",
                    models.BooleanField(default=False, verbose_name="Can Approve"),
                ),
                (
                    "can_report",
                    models.BooleanField(
                        default=False, verbose_name="Can Generate Reports"
                    ),
                ),
                (
                    "reason",
                    models.TextField(blank=True, verbose_name="Reason for Override"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Expires At"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_permission_overrides",
                        to="setup.company",
                        verbose_name="Company Scope",
                    ),
                ),
                (
                    "franchise",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_permission_overrides",
                        to="setup.franchise",
                        verbose_name="Franchise Scope",
                    ),
                ),
                (
                    "module_tab",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_overrides",
                        to="user_roles.moduletab",
                        verbose_name="Module Tab",
                    ),
                ),
                (
                    "service_center",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_permission_overrides",
                        to="setup.servicecenter",
                        verbose_name="Service Center Scope",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Custom Permission",
                "verbose_name_plural": "User Custom Permissions",
                "unique_together": {
                    ("user", "module_tab", "franchise", "company", "service_center")
                },
            },
        ),
        migrations.CreateModel(
            name="RoleModulePermission",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "can_view",
                    models.BooleanField(default=True, verbose_name="Can View"),
                ),
                ("can_add", models.BooleanField(default=False, verbose_name="Can Add")),
                (
                    "can_edit",
                    models.BooleanField(default=False, verbose_name="Can Edit"),
                ),
                (
                    "can_delete",
                    models.BooleanField(default=False, verbose_name="Can Delete"),
                ),
                (
                    "can_approve",
                    models.BooleanField(default=False, verbose_name="Can Approve"),
                ),
                (
                    "can_report",
                    models.BooleanField(
                        default=False, verbose_name="Can Generate Reports"
                    ),
                ),
                (
                    "scope_level",
                    models.CharField(
                        choices=[
                            ("system", "System Wide"),
                            ("franchise", "Franchise Only"),
                            ("company", "Company Only"),
                            ("service_center", "Service Center Only"),
                            ("own_data", "Own Data Only"),
                        ],
                        default="own_data",
                        max_length=20,
                        verbose_name="Data Scope Level",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "module_tab",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="role_permissions",
                        to="user_roles.moduletab",
                        verbose_name="Module Tab",
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dynamic_permissions",
                        to="user_roles.role",
                        verbose_name="Role",
                    ),
                ),
            ],
            options={
                "verbose_name": "Role Module Permission",
                "verbose_name_plural": "Role Module Permissions",
                "unique_together": {("role", "module_tab")},
            },
        ),
    ]
