# Generated by Django 4.2.20 on 2025-07-03 18:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0018_add_nationality_field"),
        ("user_roles", "0007_dynamic_permissions"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="usercustompermission",
            name="expires_at",
        ),
        migrations.RemoveField(
            model_name="usercustompermission",
            name="permission_type",
        ),
        migrations.RemoveField(
            model_name="usercustompermission",
            name="reason",
        ),
        migrations.AlterField(
            model_name="moduletab",
            name="url_name",
            field=models.CharField(blank=True, max_length=100, verbose_name="URL Name"),
        ),
        migrations.AlterField(
            model_name="rolemodulepermission",
            name="can_view",
            field=models.BooleanField(default=False, verbose_name="Can View"),
        ),
        migrations.Alter<PERSON>ield(
            model_name="rolemodulepermission",
            name="role",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="role_module_permissions",
                to="user_roles.role",
                verbose_name="Role",
            ),
        ),
        migrations.AlterField(
            model_name="rolemodulepermission",
            name="scope_level",
            field=models.CharField(
                choices=[
                    ("system", "System Wide"),
                    ("franchise", "Franchise Level"),
                    ("company", "Company Level"),
                    ("service_center", "Service Center Level"),
                    ("own_data", "Own Data Only"),
                ],
                default="own_data",
                max_length=20,
                verbose_name="Scope Level",
            ),
        ),
        migrations.AlterField(
            model_name="usercustompermission",
            name="can_view",
            field=models.BooleanField(default=False, verbose_name="Can View"),
        ),
        migrations.AlterField(
            model_name="usercustompermission",
            name="company",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_custom_permissions",
                to="setup.company",
                verbose_name="Company Context",
            ),
        ),
        migrations.AlterField(
            model_name="usercustompermission",
            name="franchise",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_custom_permissions",
                to="setup.franchise",
                verbose_name="Franchise Context",
            ),
        ),
        migrations.AlterField(
            model_name="usercustompermission",
            name="module_tab",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_custom_permissions",
                to="user_roles.moduletab",
                verbose_name="Module Tab",
            ),
        ),
        migrations.AlterField(
            model_name="usercustompermission",
            name="service_center",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_custom_permissions",
                to="setup.servicecenter",
                verbose_name="Service Center Context",
            ),
        ),
    ]
