# Generated by Django 4.2.20 on 2025-07-04 08:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0018_add_nationality_field"),
        ("user_roles", "0008_remove_usercustompermission_expires_at_and_more"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="usercustompermission",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="usercustompermission",
            name="companies",
            field=models.ManyToManyField(
                blank=True,
                help_text="Select multiple companies where this permission applies",
                related_name="user_custom_permissions",
                to="setup.company",
                verbose_name="Company Context",
            ),
        ),
        migrations.AddField(
            model_name="usercustompermission",
            name="franchises",
            field=models.ManyToManyField(
                blank=True,
                help_text="Select multiple franchises where this permission applies",
                related_name="user_custom_permissions",
                to="setup.franchise",
                verbose_name="Franchise Context",
            ),
        ),
        migrations.AddField(
            model_name="usercustompermission",
            name="service_centers",
            field=models.ManyToManyField(
                blank=True,
                help_text="Select multiple service centers where this permission applies",
                related_name="user_custom_permissions",
                to="setup.servicecenter",
                verbose_name="Service Center Context",
            ),
        ),
        migrations.RemoveField(
            model_name="usercustompermission",
            name="company",
        ),
        migrations.RemoveField(
            model_name="usercustompermission",
            name="franchise",
        ),
        migrations.RemoveField(
            model_name="usercustompermission",
            name="service_center",
        ),
    ]
