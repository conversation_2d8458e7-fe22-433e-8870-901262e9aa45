from django.contrib.auth.models import User
from django.db.models import Q
from django.utils import timezone
from django.core.cache import cache
from typing import Dict, List, Optional, Union, Any
from .models import Role, UserRole, ModuleTab, RoleModulePermission, UserCustomPermission
from setup.models import Franchise, Company, ServiceCenter


class PermissionService:
    """
    Core service for checking dynamic permissions based on user roles and overrides
    """
    
    @staticmethod
    def get_user_permissions(user: User, context: Dict[str, Any] = None) -> Dict[str, Dict[str, bool]]:
        """
        Get all permissions for a user across all tabs
        
        Args:
            user: User instance
            context: Dictionary with 'franchise', 'company', 'service_center' for scope filtering
            
        Returns:
            Dictionary mapping tab codes to permission dictionaries
        """
        cache_key = f"user_permissions_{user.id}"
        if context:
            context_hash = hash(str(sorted(context.items())))
            cache_key += f"_{context_hash}"
        
        # Try cache first
        permissions = cache.get(cache_key)
        if permissions is not None:
            return permissions
        
        permissions = {}
        active_tabs = ModuleTab.objects.filter(is_active=True)
        
        for tab in active_tabs:
            permissions[tab.code] = PermissionService._get_tab_permissions(user, tab, context)
        
        # Cache for 5 minutes
        cache.set(cache_key, permissions, 300)
        return permissions
    
    @staticmethod
    def _get_tab_permissions(user: User, tab: ModuleTab, context: Dict[str, Any] = None) -> Dict[str, bool]:
        """
        Get permissions for a specific tab for a user
        """
        # Default permissions
        permissions = {
            'can_view': False,
            'can_add': False,
            'can_edit': False,
            'can_delete': False,
            'can_approve': False,
            'can_report': False,
            'scope_level': 'own_data'
        }
        
        # System admin has full access to everything
        if user.is_superuser:
            permissions.update({
                'can_view': True,
                'can_add': True,
                'can_edit': True,
                'can_delete': True,
                'can_approve': True,
                'can_report': True,
                'scope_level': 'system'
            })
            return permissions
        
        # Check if tab requires authentication
        if tab.requires_authentication and not user.is_authenticated:
            return permissions
        
        # Check if tab is system admin only
        if tab.is_system_admin_only and not user.is_superuser:
            return permissions
        
        # Get user roles
        user_roles = PermissionService._get_user_roles_in_context(user, context)
        
        # Aggregate permissions from all roles
        for user_role in user_roles:
            role_permissions = PermissionService._get_role_tab_permissions(user_role.role, tab)
            if role_permissions:
                # Take the highest permission level for each action
                for action in ['can_view', 'can_add', 'can_edit', 'can_delete', 'can_approve', 'can_report']:
                    permissions[action] = permissions[action] or role_permissions.get(action, False)
                
                # Take the broadest scope level
                role_scope = role_permissions.get('scope_level', 'own_data')
                if PermissionService._is_broader_scope(role_scope, permissions['scope_level']):
                    permissions['scope_level'] = role_scope
        
        # Check for user-specific overrides
        override_permissions = PermissionService._get_user_override_permissions(user, tab, context)
        if override_permissions:
            permissions.update(override_permissions)
        
        return permissions
    
    @staticmethod
    def _get_user_roles_in_context(user: User, context: Dict[str, Any] = None) -> List[UserRole]:
        """
        Get user roles that are relevant to the current context
        """
        queryset = UserRole.objects.filter(user=user, is_active=True).select_related('role')
        
        if not context:
            return list(queryset)
        
        # Filter roles based on context
        context_filter = Q()
        
        # Add context-specific filters
        if context.get('service_center'):
            context_filter |= Q(service_center=context['service_center'])
        if context.get('company'):
            context_filter |= Q(company=context['company']) | Q(service_center__company=context['company'])
        if context.get('franchise'):
            context_filter |= Q(franchise=context['franchise']) | Q(company__franchise=context['franchise']) | Q(service_center__company__franchise=context['franchise'])
        
        # Also include system-wide roles (no specific scope)
        context_filter |= Q(franchise__isnull=True, company__isnull=True, service_center__isnull=True)
        
        return list(queryset.filter(context_filter))
    
    @staticmethod
    def _get_role_tab_permissions(role: Role, tab: ModuleTab) -> Optional[Dict[str, Any]]:
        """
        Get permissions for a specific role and tab
        """
        try:
            role_permission = RoleModulePermission.objects.get(
                role=role, 
                module_tab=tab, 
                is_active=True
            )
            return {
                'can_view': role_permission.can_view,
                'can_add': role_permission.can_add,
                'can_edit': role_permission.can_edit,
                'can_delete': role_permission.can_delete,
                'can_approve': role_permission.can_approve,
                'can_report': role_permission.can_report,
                'scope_level': role_permission.scope_level
            }
        except RoleModulePermission.DoesNotExist:
            # Fall back to legacy module access flags
            return PermissionService._get_legacy_permissions(role, tab)
    
    @staticmethod
    def _get_legacy_permissions(role: Role, tab: ModuleTab) -> Optional[Dict[str, Any]]:
        """
        Get permissions from legacy role module access flags
        """
        # Map tab codes to legacy role fields
        legacy_mapping = {
            'dashboard': 'can_access_setup',  # Everyone can access dashboard
            'setup': 'can_access_setup',
            'work_orders': 'can_access_work_orders',
            'inventory': 'can_access_inventory',
            'warehouse': 'can_access_warehouse',
            'sales': 'can_access_sales',
            'purchases': 'can_access_purchases',
            'billing': 'can_access_billing',
            'cashier': 'can_access_billing',
            'reports': 'can_access_reports',
            'settings': 'can_access_settings',
        }
        
        legacy_field = legacy_mapping.get(tab.code)
        if legacy_field and hasattr(role, legacy_field) and getattr(role, legacy_field):
            # Determine scope based on role type
            scope_mapping = {
                'system_admin': 'system',
                'franchise_admin': 'franchise',
                'company_admin': 'company',
                'service_center_manager': 'service_center',
            }
            scope = scope_mapping.get(role.role_type, 'own_data')
            
            return {
                'can_view': True,
                'can_add': role.role_type in ['system_admin', 'franchise_admin', 'company_admin', 'service_center_manager'],
                'can_edit': role.role_type in ['system_admin', 'franchise_admin', 'company_admin', 'service_center_manager'],
                'can_delete': role.role_type in ['system_admin', 'franchise_admin'],
                'can_approve': role.role_type in ['system_admin', 'franchise_admin', 'company_admin'],
                'can_report': True,
                'scope_level': scope
            }
        
        return None
    
    @staticmethod
    def _get_user_override_permissions(user: User, tab: ModuleTab, context: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Get user-specific permission overrides
        """
        queryset = UserCustomPermission.objects.filter(
            user=user,
            module_tab=tab,
            is_active=True
        )
        
        # Filter by context if provided
        if context:
            context_filter = Q()
            if context.get('service_center'):
                context_filter |= Q(service_centers=context['service_center'])
            if context.get('company'):
                context_filter |= Q(companies=context['company'])
            if context.get('franchise'):
                context_filter |= Q(franchises=context['franchise'])
            
            # Also include global overrides (no specific scope)
            context_filter |= Q(franchises__isnull=True, companies__isnull=True, service_centers__isnull=True)
            
            queryset = queryset.filter(context_filter)
        
        # Get the most recent override
        override = queryset.order_by('-created_at').first()
        if override:
            return {
                'can_view': override.can_view,
                'can_add': override.can_add,
                'can_edit': override.can_edit,
                'can_delete': override.can_delete,
                'can_approve': override.can_approve,
                'can_report': override.can_report,
            }
        
        return None
    
    @staticmethod
    def _is_broader_scope(scope1: str, scope2: str) -> bool:
        """
        Check if scope1 is broader than scope2
        """
        scope_hierarchy = ['own_data', 'service_center', 'company', 'franchise', 'system']
        try:
            return scope_hierarchy.index(scope1) > scope_hierarchy.index(scope2)
        except ValueError:
            return False
    
    @staticmethod
    def can_access_tab(user: User, tab_code: str, action: str = 'view', context: Dict[str, Any] = None) -> bool:
        """
        Check if user can perform a specific action on a tab
        
        Args:
            user: User instance
            tab_code: Tab code (e.g., 'dashboard', 'sales')
            action: Action to check ('view', 'add', 'edit', 'delete', 'approve', 'report')
            context: Dictionary with 'franchise', 'company', 'service_center'
            
        Returns:
            Boolean indicating if user has permission
        """
        try:
            tab = ModuleTab.objects.get(code=tab_code, is_active=True)
        except ModuleTab.DoesNotExist:
            return False
        
        permissions = PermissionService._get_tab_permissions(user, tab, context)
        permission_key = f'can_{action}'
        
        return permissions.get(permission_key, False)
    
    @staticmethod
    def get_accessible_tabs(user: User, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Get list of tabs that user can access
        
        Returns:
            List of tab dictionaries with permission info
        """
        accessible_tabs = []
        user_permissions = PermissionService.get_user_permissions(user, context)
        
        for tab in ModuleTab.objects.filter(is_active=True).order_by('order'):
            tab_permissions = user_permissions.get(tab.code, {})
            
            if tab_permissions.get('can_view', False):
                accessible_tabs.append({
                    'code': tab.code,
                    'name_en': tab.name_en,
                    'name_ar': tab.name_ar,
                    'name_localized': tab.name_localized,
                    'url_name': tab.url_name,
                    'icon_class': tab.icon_class,
                    'order': tab.order,
                    'parent_tab': tab.parent_tab_id,
                    'permissions': tab_permissions
                })
        
        return accessible_tabs
    
    @staticmethod
    def clear_user_permission_cache(user: User):
        """
        Clear cached permissions for a user
        """
        pattern = f"user_permissions_{user.id}*"
        # Clear all cached keys for this user
        cache.delete_many([pattern])


class NavigationService:
    """
    Service for building dynamic navigation menus based on user permissions
    """
    
    @staticmethod
    def build_main_navigation(user: User, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Build the main navigation menu for a user
        """
        accessible_tabs = PermissionService.get_accessible_tabs(user, context)
        
        # Group tabs by parent
        main_tabs = []
        sub_tabs_map = {}
        
        for tab in accessible_tabs:
            if tab['parent_tab'] is None:
                main_tabs.append(tab)
            else:
                parent_id = tab['parent_tab']
                if parent_id not in sub_tabs_map:
                    sub_tabs_map[parent_id] = []
                sub_tabs_map[parent_id].append(tab)
        
        # Add sub-tabs to their parents
        for tab in main_tabs:
            tab['sub_tabs'] = sub_tabs_map.get(tab['code'], [])
        
        return main_tabs
    
    @staticmethod
    def get_breadcrumb_path(user: User, current_tab_code: str, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Get breadcrumb navigation path for current tab
        """
        try:
            current_tab = ModuleTab.objects.get(code=current_tab_code, is_active=True)
        except ModuleTab.DoesNotExist:
            return []
        
        # Check if user can access this tab
        if not PermissionService.can_access_tab(user, current_tab_code, 'view', context):
            return []
        
        breadcrumbs = []
        tab = current_tab
        
        # Build breadcrumb chain
        while tab:
            if PermissionService.can_access_tab(user, tab.code, 'view', context):
                breadcrumbs.insert(0, {
                    'code': tab.code,
                    'name_localized': tab.name_localized,
                    'url_name': tab.url_name,
                })
            tab = tab.parent_tab
        
        return breadcrumbs


class DataFilterService:
    """
    Service for filtering data based on user scope and permissions
    """
    
    @staticmethod
    def get_user_data_scope(user: User, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get the data scope for a user (what data they can see)
        """
        if user.is_superuser:
            return {'scope': 'system', 'filters': {}}
        
        user_roles = PermissionService._get_user_roles_in_context(user, context)
        
        # Determine the broadest scope from user's roles
        broadest_scope = 'own_data'
        scope_filters = {}
        
        for user_role in user_roles:
            role_scope = DataFilterService._get_role_scope(user_role)
            
            if PermissionService._is_broader_scope(role_scope['scope'], broadest_scope):
                broadest_scope = role_scope['scope']
                scope_filters = role_scope['filters']
        
        return {'scope': broadest_scope, 'filters': scope_filters}
    
    @staticmethod
    def _get_role_scope(user_role: UserRole) -> Dict[str, Any]:
        """
        Get the data scope for a specific user role
        """
        if user_role.role.role_type == 'system_admin':
            return {'scope': 'system', 'filters': {}}
        elif user_role.role.role_type == 'franchise_admin' and user_role.franchise:
            return {
                'scope': 'franchise',
                'filters': {
                    'franchise_id': user_role.franchise.id,
                    'company__franchise_id': user_role.franchise.id,
                    'service_center__company__franchise_id': user_role.franchise.id
                }
            }
        elif user_role.role.role_type == 'company_admin' and user_role.company:
            return {
                'scope': 'company',
                'filters': {
                    'company_id': user_role.company.id,
                    'service_center__company_id': user_role.company.id
                }
            }
        elif user_role.service_center:
            return {
                'scope': 'service_center',
                'filters': {
                    'service_center_id': user_role.service_center.id
                }
            }
        
        # Default to own data
        return {
            'scope': 'own_data',
            'filters': {
                'created_by': user_role.user.id,
                'user_id': user_role.user.id
            }
        }
    
    @staticmethod
    def apply_data_filters(queryset, user: User, context: Dict[str, Any] = None):
        """
        Apply data filtering to a queryset based on user permissions
        """
        scope_info = DataFilterService.get_user_data_scope(user, context)
        filters = scope_info['filters']
        
        if not filters:  # System-wide access
            return queryset
        
        # Get the model from the queryset
        model = queryset.model
        model_name = model.__name__
        
        # Apply OR filters for different scope levels based on model type
        from django.db.models import Q
        filter_q = Q()
        
        # Smart filtering based on model type and available filters
        for field, value in filters.items():
            # Map filters to appropriate fields for each model
            if model_name == 'Franchise' and field == 'franchise_id':
                # For Franchise model, use id field directly
                filter_q |= Q(id=value)
            elif model_name == 'Company' and field == 'company__franchise_id':
                # For Company model, use franchise_id field
                filter_q |= Q(franchise_id=value)
            elif model_name == 'ServiceCenter' and field == 'service_center__company__franchise_id':
                # For ServiceCenter model, use company__franchise_id field
                filter_q |= Q(company__franchise_id=value)
            elif model_name == 'Customer' and field == 'service_center__company__franchise_id':
                # For Customer model, use service_center__company__franchise_id field
                filter_q |= Q(service_center__company__franchise_id=value)
            elif model_name == 'Vehicle' and field == 'service_center__company__franchise_id':
                # For Vehicle model, use service_center__company__franchise_id field
                filter_q |= Q(service_center__company__franchise_id=value)
            else:
                # For other models or fields, try to apply the filter as-is
                try:
                    # Check if the field exists on the model
                    model._meta.get_field(field.split('__')[0])
                    filter_q |= Q(**{field: value})
                except:
                    # Skip invalid filters
                    continue
        
        return queryset.filter(filter_q) if filter_q else queryset 