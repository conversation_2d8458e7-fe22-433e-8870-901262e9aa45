from django.urls import path, include
from django.contrib import admin
from . import views
from .models import UserCustomPermission

app_name = 'user_roles'

urlpatterns = [
    path('api/tenant-info/', views.get_tenant_info, name='get_tenant_info'),
    
    # Hierarchical context URLs for User Custom Permissions in Django Admin
    path('admin/user_roles/usercustompermission/franchise/<uuid:franchise_id>/', 
         admin.site.admin_view(lambda request, franchise_id: 
            admin.site._registry[UserCustomPermission].changelist_view(
                request, extra_context={'context_level': 'franchise', 'context_id': franchise_id}
            )), 
         name='usercustompermission_franchise_changelist'),
    
    path('admin/user_roles/usercustompermission/company/<uuid:company_id>/', 
         admin.site.admin_view(lambda request, company_id: 
            admin.site._registry[UserCustomPermission].changelist_view(
                request, extra_context={'context_level': 'company', 'context_id': company_id}
            )), 
         name='usercustompermission_company_changelist'),
    
    path('admin/user_roles/usercustompermission/service_center/<uuid:service_center_id>/', 
         admin.site.admin_view(lambda request, service_center_id: 
            admin.site._registry[UserCustomPermission].changelist_view(
                request, extra_context={'context_level': 'service_center', 'context_id': service_center_id}
            )), 
         name='usercustompermission_servicecenter_changelist'),
] 