from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date

# Import models
from .models import Location, LocationType, BinLocation, ItemLocation, TransferOrder, TransferOrderItem, Transfer, WarehouseTimer
from inventory.models import Item
from setup.models import Customer, Franchise, Company, ServiceCenter





class LocationTypeForm(forms.ModelForm):
    """Form for creating and updating location types"""
    
    class Meta:
        model = LocationType
        fields = [
            'name', 'code', 'description', 'icon', 'color', 'is_active',
            'requires_bin_locations', 'is_storage', 'is_receiving', 
            'is_shipping', 'is_service'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter location type name...')
            }),
            'code': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter location type code...')
            }),
            'description': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter description...')
            }),
            'icon': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter icon name...')
            }),
            'color': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter color code...')
            }),
        }


class LocationForm(forms.ModelForm):
    """Form for creating and updating warehouse locations"""
    
    class Meta:
        model = Location
        fields = [
            'name', 'code', 'location_type', 'parent', 'address', 'city',
            'contact_name', 'phone', 'email', 'area_sqm', 'max_items',
            'is_active', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter location name...')
            }),
            'code': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter location code...')
            }),
            'location_type': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'parent': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'address': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter address...')
            }),
            'city': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter city...')
            }),
            'contact_name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter contact name...')
            }),
            'phone': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter phone number...')
            }),
            'email': forms.EmailInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter email address...')
            }),
            'area_sqm': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'placeholder': _('Enter area in square meters...')
            }),
            'max_items': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter maximum items...')
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter notes...')
            }),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            # Filter location types and parent locations based on user's tenant
            self.fields['location_type'].queryset = LocationType.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('name')
            
            self.fields['parent'].queryset = Location.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('name')
        
        # Make some fields optional
        self.fields['parent'].required = False
        self.fields['address'].required = False
        self.fields['city'].required = False
        self.fields['contact_name'].required = False
        self.fields['phone'].required = False
        self.fields['email'].required = False
        self.fields['area_sqm'].required = False
        self.fields['max_items'].required = False
        self.fields['notes'].required = False


class BinLocationForm(forms.ModelForm):
    """Form for creating and updating bin locations"""
    
    class Meta:
        model = BinLocation
        fields = [
            'location', 'name', 'code', 'description', 'aisle', 'rack',
            'shelf', 'position', 'barcode', 'is_active'
        ]
        widgets = {
            'location': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter bin name...')
            }),
            'code': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter bin code...')
            }),
            'description': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 2,
                'placeholder': _('Enter description...')
            }),
            'aisle': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter aisle...')
            }),
            'rack': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter rack...')
            }),
            'shelf': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter shelf...')
            }),
            'position': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter position...')
            }),
            'barcode': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter barcode...')
            }),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            self.fields['location'].queryset = Location.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('name')


class TransferOrderForm(forms.ModelForm):
    """Form for creating stock transfer orders"""
    
    class Meta:
        model = TransferOrder
        fields = [
            'reference', 'source_location', 'destination_location', 
            'status', 'notes'
        ]
        widgets = {
            'reference': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter reference number...')
            }),
            'source_location': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'destination_location': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'status': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter notes...')
            }),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            locations = Location.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('name')
            
            self.fields['source_location'].queryset = locations
            self.fields['destination_location'].queryset = locations
        
        # Make some fields optional
        self.fields['notes'].required = False
    
    def clean(self):
        cleaned_data = super().clean()
        source = cleaned_data.get('source_location')
        destination = cleaned_data.get('destination_location')
        
        if source and destination and source == destination:
            raise ValidationError(_('Source and destination locations must be different.'))
        
        return cleaned_data


class TransferOrderItemForm(forms.ModelForm):
    """Form for creating transfer order items"""
    
    class Meta:
        model = TransferOrderItem
        fields = ['transfer_order', 'item', 'quantity', 'notes']
        widgets = {
            'transfer_order': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'item': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'min': '0.01',
                'placeholder': _('Enter quantity...')
            }),
            'notes': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 2,
                'placeholder': _('Enter notes...')
            }),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            self.fields['transfer_order'].queryset = TransferOrder.objects.filter(
                tenant_id=tenant_id
            ).order_by('-created_at')
            
            self.fields['item'].queryset = Item.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')
        
        self.fields['notes'].required = False


class ItemLocationForm(forms.ModelForm):
    """Form for managing item locations"""
    
    class Meta:
        model = ItemLocation
        fields = [
            'item', 'location', 'quantity', 'bin_location',
            'reorder_point', 'max_stock', 'min_stock'
        ]
        widgets = {
            'item': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'location': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'placeholder': _('Enter quantity...')
            }),
            'bin_location': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'reorder_point': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'placeholder': _('Enter reorder point...')
            }),
            'max_stock': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'placeholder': _('Enter maximum stock...')
            }),
            'min_stock': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'step': '0.01',
                'placeholder': _('Enter minimum stock...')
            }),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            self.fields['item'].queryset = Item.objects.filter(
                tenant_id=tenant_id
            ).order_by('name')
            
            self.fields['location'].queryset = Location.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('name')
            
            self.fields['bin_location'].queryset = BinLocation.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('location__name', 'name')
        
        # Make some fields optional
        self.fields['bin_location'].required = False
        self.fields['reorder_point'].required = False
        self.fields['max_stock'].required = False
        self.fields['min_stock'].required = False


class WarehouseReportForm(forms.Form):
    """Form for generating warehouse reports"""
    
    REPORT_TYPE_CHOICES = [
        ('location_summary', _('Location Summary')),
        ('transfer_summary', _('Transfer Summary')),
        ('stock_levels', _('Stock Levels')),
        ('location_utilization', _('Location Utilization')),
    ]
    
    report_type = forms.ChoiceField(
        label=_("Report Type"),
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    location = forms.ModelChoiceField(
        label=_("Location"),
        queryset=Location.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    date_from = forms.DateField(
        label=_("Date From"),
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    date_to = forms.DateField(
        label=_("Date To"),
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                self.fields['location'].queryset = Location.objects.filter(
                    tenant_id=tenant_id,
                    is_active=True
                ).order_by('name')
    
    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError(_("Start date cannot be after end date."))
        
        return cleaned_data


class WarehouseTimerForm(forms.ModelForm):
    """Form for creating and updating warehouse timers"""
    
    class Meta:
        model = WarehouseTimer
        fields = [
            'name', 'description', 'timer_type', 'visibility_level',
            'start_date', 'end_date', 'start_time', 'end_time',
            'recurrence', 'recurrence_days', 'warehouses',
            'can_share_with_company_centers', 'blocks_operations',
            'requires_approval', 'notification_enabled', 'notification_minutes_before'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'placeholder': _('Enter timer name...')
            }),
            'description': forms.Textarea(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'rows': 3,
                'placeholder': _('Enter description...')
            }),
            'timer_type': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'visibility_level': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'onchange': 'updateOrganizationalFields(this.value)'
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'start_time': forms.TimeInput(attrs={
                'type': 'time',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'end_time': forms.TimeInput(attrs={
                'type': 'time',
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
            }),
            'recurrence': forms.Select(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'onchange': 'updateRecurrenceFields(this.value)'
            }),
            'warehouses': forms.SelectMultiple(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'size': '5'
            }),
            'notification_minutes_before': forms.NumberInput(attrs={
                'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
                'min': '0',
                'placeholder': _('Enter minutes...')
            }),
        }
    
    # Organizational fields (dynamically shown based on visibility level)
    franchise = forms.ModelChoiceField(
        label=_("Franchise"),
        queryset=Franchise.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'onchange': 'updateCompanyOptions(this.value)'
        })
    )
    
    company = forms.ModelChoiceField(
        label=_("Company"),
        queryset=Company.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'onchange': 'updateServiceCenterOptions(this.value)'
        })
    )
    
    service_center = forms.ModelChoiceField(
        label=_("Service Center"),
        queryset=ServiceCenter.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    # Recurrence days for weekly timers
    recurrence_days_field = forms.MultipleChoiceField(
        label=_("Days of Week"),
        choices=[
            (0, _('Monday')),
            (1, _('Tuesday')),
            (2, _('Wednesday')),
            (3, _('Thursday')),
            (4, _('Friday')),
            (5, _('Saturday')),
            (6, _('Sunday')),
        ],
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                # Set queryset for warehouses
                self.fields['warehouses'].queryset = Location.objects.filter(
                    tenant_id=tenant_id,
                    is_active=True
                ).order_by('name')
                
                # Set organizational querysets based on user's role
                try:
                    user_profile = user.profile
                    primary_role = None
                    
                    # Get user's primary role
                    if hasattr(user, 'user_roles'):
                        user_roles = user.user_roles.filter(is_active=True)
                        for user_role in user_roles:
                            if user_role.is_primary:
                                primary_role = user_role
                                break
                        if not primary_role and user_roles.exists():
                            primary_role = user_roles.first()
                    
                    if primary_role:
                        if primary_role.franchise:
                            # Franchise-level user
                            self.fields['franchise'].queryset = Franchise.objects.filter(
                                id=primary_role.franchise.id
                            )
                            self.fields['company'].queryset = Company.objects.filter(
                                franchise=primary_role.franchise
                            )
                            self.fields['service_center'].queryset = ServiceCenter.objects.filter(
                                company__franchise=primary_role.franchise
                            )
                        elif primary_role.company:
                            # Company-level user
                            self.fields['franchise'].queryset = Franchise.objects.filter(
                                id=primary_role.company.franchise.id
                            )
                            self.fields['company'].queryset = Company.objects.filter(
                                id=primary_role.company.id
                            )
                            self.fields['service_center'].queryset = ServiceCenter.objects.filter(
                                company=primary_role.company
                            )
                        elif primary_role.service_center:
                            # Service center-level user
                            self.fields['franchise'].queryset = Franchise.objects.filter(
                                id=primary_role.service_center.company.franchise.id
                            )
                            self.fields['company'].queryset = Company.objects.filter(
                                id=primary_role.service_center.company.id
                            )
                            self.fields['service_center'].queryset = ServiceCenter.objects.filter(
                                id=primary_role.service_center.id
                            )
                
                except AttributeError:
                    # Fallback if user profile doesn't exist
                    self.fields['franchise'].queryset = Franchise.objects.filter(tenant_id=tenant_id)
                    self.fields['company'].queryset = Company.objects.filter(tenant_id=tenant_id)
                    self.fields['service_center'].queryset = ServiceCenter.objects.filter(tenant_id=tenant_id)
        
        # Convert recurrence_days to list for display
        if self.instance.pk and self.instance.recurrence_days:
            self.fields['recurrence_days_field'].initial = self.instance.recurrence_days
        
        # Make some fields optional
        self.fields['end_date'].required = False
        self.fields['description'].required = False
        self.fields['recurrence_days_field'].required = False
    
    def clean(self):
        cleaned_data = super().clean()
        visibility_level = cleaned_data.get('visibility_level')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')
        recurrence = cleaned_data.get('recurrence')
        recurrence_days = cleaned_data.get('recurrence_days_field')
        
        # Validate organizational requirements
        if visibility_level == 'franchise':
            franchise = cleaned_data.get('franchise')
            if not franchise:
                self.add_error('franchise', _("Franchise is required for franchise-level timers"))
        elif visibility_level == 'company':
            company = cleaned_data.get('company')
            if not company:
                self.add_error('company', _("Company is required for company-level timers"))
        elif visibility_level == 'service_center':
            service_center = cleaned_data.get('service_center')
            if not service_center:
                self.add_error('service_center', _("Service Center is required for service center-level timers"))
        
        # Validate date range
        if end_date and start_date and start_date > end_date:
            self.add_error('end_date', _("End date cannot be before start date"))
        
        # Validate time range for same-day timers
        if not end_date or (start_date and end_date and start_date == end_date):
            if start_time and end_time and start_time >= end_time:
                self.add_error('end_time', _("End time must be after start time for same-day timers"))
        
        # Validate weekly recurrence
        if recurrence == 'weekly' and not recurrence_days:
            self.add_error('recurrence_days_field', _("Please select at least one day for weekly recurrence"))
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Set organizational fields based on visibility level
        visibility_level = self.cleaned_data.get('visibility_level')
        if visibility_level == 'franchise':
            instance.franchise = self.cleaned_data.get('franchise')
            instance.company = None
            instance.service_center = None
        elif visibility_level == 'company':
            company = self.cleaned_data.get('company')
            instance.company = company
            instance.franchise = company.franchise if company else None
            instance.service_center = None
        elif visibility_level == 'service_center':
            service_center = self.cleaned_data.get('service_center')
            instance.service_center = service_center
            if service_center:
                instance.company = service_center.company
                instance.franchise = service_center.company.franchise
        
        # Set recurrence days
        recurrence_days = self.cleaned_data.get('recurrence_days_field')
        if recurrence_days:
            instance.recurrence_days = [int(day) for day in recurrence_days]
        else:
            instance.recurrence_days = []
        
        if commit:
            instance.save()
            self.save_m2m()
        
        return instance


class WarehouseTimerFilterForm(forms.Form):
    """Form for filtering warehouse timers"""
    
    timer_type = forms.ChoiceField(
        label=_("Timer Type"),
        choices=[('', _('All Types'))] + WarehouseTimer.TIMER_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    visibility_level = forms.ChoiceField(
        label=_("Visibility Level"),
        choices=[('', _('All Levels'))] + WarehouseTimer.VISIBILITY_LEVEL_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    warehouse = forms.ModelChoiceField(
        label=_("Warehouse"),
        queryset=Location.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    is_active = forms.ChoiceField(
        label=_("Status"),
        choices=[
            ('', _('All')),
            ('true', _('Active')),
            ('false', _('Inactive')),
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            tenant_id = getattr(user, 'tenant_id', None)
            if tenant_id:
                self.fields['warehouse'].queryset = Location.objects.filter(
                    tenant_id=tenant_id,
                    is_active=True
                ).order_by('name') 