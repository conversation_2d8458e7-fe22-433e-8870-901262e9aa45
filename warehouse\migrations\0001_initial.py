# Generated by Django 4.2.20 on 2025-05-07 09:45

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Location',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('code', models.CharField(max_length=50, verbose_name='Code')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
            ],
            options={
                'verbose_name': 'Location',
                'verbose_name_plural': 'Locations',
                'unique_together': {('tenant_id', 'code')},
            },
        ),
        migrations.CreateModel(
            name='TransferOrder',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('reference', models.CharField(max_length=100, unique=True, verbose_name='Reference')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('in_transit', 'In Transit'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='Status')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('destination_location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='incoming_transfers', to='warehouse.location', verbose_name='Destination Location')),
                ('source_location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='outgoing_transfers', to='warehouse.location', verbose_name='Source Location')),
            ],
            options={
                'verbose_name': 'Transfer Order',
                'verbose_name_plural': 'Transfer Orders',
            },
        ),
        migrations.CreateModel(
            name='TransferOrderItem',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Quantity')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers', to='inventory.item', verbose_name='Item')),
                ('transfer_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='warehouse.transferorder', verbose_name='Transfer Order')),
            ],
            options={
                'verbose_name': 'Transfer Order Item',
                'verbose_name_plural': 'Transfer Order Items',
                'unique_together': {('transfer_order', 'item')},
            },
        ),
        migrations.CreateModel(
            name='ItemLocation',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Quantity')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='inventory.item')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='warehouse.location')),
            ],
            options={
                'verbose_name': 'Item Location',
                'verbose_name_plural': 'Item Locations',
                'unique_together': {('tenant_id', 'item', 'location')},
            },
        ),
    ]
