# Generated by Django 4.2.20 on 2025-05-07 11:44

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='transferorder',
            name='items_count',
            field=models.IntegerField(default=0, help_text='Total number of items in this transfer', verbose_name='Items Count'),
        ),
        migrations.CreateModel(
            name='Transfer',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('items_count', models.IntegerField(default=0, verbose_name='Items Count')),
                ('reference', models.CharField(blank=True, max_length=100, verbose_name='Reference')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('destination_location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='incoming_simple_transfers', to='warehouse.location', verbose_name='Destination Location')),
                ('source_location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='outgoing_simple_transfers', to='warehouse.location', verbose_name='Source Location')),
            ],
            options={
                'verbose_name': 'Transfer',
                'verbose_name_plural': 'Transfers',
            },
        ),
    ]
