# Generated by Django 4.2.20 on 2025-05-14 12:34

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("warehouse", "0005_add_location_type_and_bin_location"),
    ]

    operations = [
        migrations.AlterField(
            model_name="binlocation",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
        ),
        migrations.AlterField(
            model_name="binlocation",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4,
                editable=False,
                primary_key=True,
                serialize=False,
                verbose_name="ID",
            ),
        ),
        migrations.AlterField(
            model_name="binlocation",
            name="tenant_id",
            field=models.UUIDField(db_index=True, default=1, verbose_name="Tenant ID"),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="binlocation",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, verbose_name="Updated at"),
        ),
        migrations.AlterField(
            model_name="locationtype",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
        ),
        migrations.AlterField(
            model_name="locationtype",
            name="id",
            field=models.UUIDField(
                default=uuid.uuid4,
                editable=False,
                primary_key=True,
                serialize=False,
                verbose_name="ID",
            ),
        ),
        migrations.AlterField(
            model_name="locationtype",
            name="tenant_id",
            field=models.UUIDField(db_index=True, default=1, verbose_name="Tenant ID"),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="locationtype",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, verbose_name="Updated at"),
        ),
    ]
