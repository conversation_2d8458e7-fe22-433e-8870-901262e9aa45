# Generated by Django 4.2.20 on 2025-06-06 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "warehouse",
            "0006_alter_binlocation_created_at_alter_binlocation_id_and_more",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="binlocation",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="itemlocation",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="location",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="locationtype",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="transfer",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="transferorder",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="transferorderitem",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
    ]
