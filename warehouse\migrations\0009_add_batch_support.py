# Generated by Django 4.2.20 on 2025-06-24 12:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("inventory", "0027_add_batch_tracking"),
        ("warehouse", "0008_alter_binlocation_tenant_id_and_more"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="itemlocation",
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name="transferorderitem",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="itemlocation",
            name="batch",
            field=models.ForeignKey(
                blank=True,
                help_text="Specific batch stored at this location",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="locations",
                to="inventory.itembatch",
                verbose_name="Batch",
            ),
        ),
        migrations.AddField(
            model_name="transferorderitem",
            name="batch",
            field=models.ForeignKey(
                blank=True,
                help_text="Specific batch being transferred",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="transfers",
                to="inventory.itembatch",
                verbose_name="Batch",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="itemlocation",
            unique_together={
                ("tenant_id", "item", "location", "bin_location", "batch")
            },
        ),
        migrations.AlterUniqueTogether(
            name="transferorderitem",
            unique_together={("transfer_order", "item", "batch")},
        ),
    ]
