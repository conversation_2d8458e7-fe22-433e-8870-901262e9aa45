# Generated by Django 4.2.20 on 2025-06-28 09:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0016_userrole_userprofile_technicianspecialization_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("warehouse", "0009_add_batch_support"),
    ]

    operations = [
        migrations.CreateModel(
            name="WarehouseTimer",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="Timer Name")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "timer_type",
                    models.CharField(
                        choices=[
                            ("opening_hours", "Opening Hours"),
                            ("maintenance_window", "Maintenance Window"),
                            ("special_operations", "Special Operations"),
                            ("holiday_schedule", "Holiday Schedule"),
                            ("emergency_access", "Emergency Access"),
                            ("custom", "Custom Timer"),
                        ],
                        default="custom",
                        max_length=50,
                        verbose_name="Timer Type",
                    ),
                ),
                (
                    "visibility_level",
                    models.CharField(
                        choices=[
                            (
                                "franchise",
                                "Franchise Level - Visible to all companies and service centers",
                            ),
                            (
                                "company",
                                "Company Level - Visible to company and their service centers",
                            ),
                            (
                                "service_center",
                                "Service Center Level - Visible in service center only",
                            ),
                        ],
                        max_length=20,
                        verbose_name="Visibility Level",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="Start Date")),
                (
                    "end_date",
                    models.DateField(blank=True, null=True, verbose_name="End Date"),
                ),
                ("start_time", models.TimeField(verbose_name="Start Time")),
                ("end_time", models.TimeField(verbose_name="End Time")),
                (
                    "recurrence",
                    models.CharField(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("yearly", "Yearly"),
                            ("once", "One Time"),
                        ],
                        default="daily",
                        max_length=20,
                        verbose_name="Recurrence",
                    ),
                ),
                (
                    "recurrence_days",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Days of week for weekly recurrence (0=Monday, 6=Sunday)",
                        verbose_name="Recurrence Days",
                    ),
                ),
                (
                    "can_share_with_company_centers",
                    models.BooleanField(
                        default=False,
                        help_text="Allow other service centers in the same company to see this timer",
                        verbose_name="Share with Other Company Service Centers",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="Active")),
                (
                    "blocks_operations",
                    models.BooleanField(
                        default=False,
                        help_text="If true, prevents warehouse operations during timer period",
                        verbose_name="Blocks Operations",
                    ),
                ),
                (
                    "requires_approval",
                    models.BooleanField(
                        default=False,
                        help_text="If true, timer changes need approval",
                        verbose_name="Requires Approval",
                    ),
                ),
                (
                    "approved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Approved At"
                    ),
                ),
                (
                    "notification_enabled",
                    models.BooleanField(
                        default=True, verbose_name="Enable Notifications"
                    ),
                ),
                (
                    "notification_minutes_before",
                    models.PositiveIntegerField(
                        default=30, verbose_name="Notify Minutes Before"
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_warehouse_timers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approved By",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        help_text="Required for company-level timers",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warehouse_timers",
                        to="setup.company",
                        verbose_name="Company",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_warehouse_timers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "franchise",
                    models.ForeignKey(
                        blank=True,
                        help_text="Required for franchise-level timers",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warehouse_timers",
                        to="setup.franchise",
                        verbose_name="Franchise",
                    ),
                ),
                (
                    "service_center",
                    models.ForeignKey(
                        blank=True,
                        help_text="Required for service center-level timers",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="warehouse_timers",
                        to="setup.servicecenter",
                        verbose_name="Service Center",
                    ),
                ),
                (
                    "warehouses",
                    models.ManyToManyField(
                        help_text="Warehouses affected by this timer",
                        related_name="timers",
                        to="warehouse.location",
                        verbose_name="Affected Warehouses",
                    ),
                ),
            ],
            options={
                "verbose_name": "Warehouse Timer",
                "verbose_name_plural": "Warehouse Timers",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="WarehouseTimerLog",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("activated", "Timer Activated"),
                            ("deactivated", "Timer Deactivated"),
                            ("blocked_operation", "Blocked Operation"),
                            ("override_granted", "Override Granted"),
                            ("notification_sent", "Notification Sent"),
                        ],
                        max_length=50,
                        verbose_name="Action",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Additional Data"
                    ),
                ),
                (
                    "affected_warehouse",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="timer_logs",
                        to="warehouse.location",
                        verbose_name="Affected Warehouse",
                    ),
                ),
                (
                    "timer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="warehouse.warehousetimer",
                        verbose_name="Timer",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="warehouse_timer_logs",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Warehouse Timer Log",
                "verbose_name_plural": "Warehouse Timer Logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="warehousetimer",
            index=models.Index(
                fields=["visibility_level", "is_active"],
                name="warehouse_w_visibil_8d6092_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="warehousetimer",
            index=models.Index(
                fields=["start_date", "end_date"], name="warehouse_w_start_d_799254_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="warehousetimer",
            index=models.Index(
                fields=["franchise", "company", "service_center"],
                name="warehouse_w_franchi_e776bd_idx",
            ),
        ),
    ]
