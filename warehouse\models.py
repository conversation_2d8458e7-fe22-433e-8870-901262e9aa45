from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet
from django.utils import timezone
from datetime import datetime, time, timedelta


class LocationType(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Types of warehouse locations (e.g., Main Warehouse, Service Center Storage, Shelf, Bin, etc.)
    """
    name = models.CharField(_("Name"), max_length=100)
    code = models.CharField(_("Code"), max_length=50)
    description = models.TextField(_("Description"), blank=True)
    # UI display settings
    icon = models.CharField(_("Icon"), max_length=50, blank=True, help_text=_("Icon name for UI display"))
    color = models.CharField(_("Color"), max_length=20, blank=True, help_text=_("Color code for UI display"))
    # Configuration
    is_active = models.BooleanField(_("Active"), default=True)
    requires_bin_locations = models.BooleanField(_("Requires Bin Locations"), default=False, 
                                               help_text=_("If true, this location type must have bin locations defined"))
    is_storage = models.BooleanField(_("Is Storage Location"), default=True, 
                                   help_text=_("If true, this location can store inventory"))
    is_receiving = models.BooleanField(_("Is Receiving Location"), default=False,
                                     help_text=_("If true, this location can receive inventory from vendors"))
    is_shipping = models.BooleanField(_("Is Shipping Location"), default=False,
                                    help_text=_("If true, this location can ship inventory to customers"))
    is_service = models.BooleanField(_("Is Service Location"), default=False,
                                   help_text=_("If true, this location is associated with a service center"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Location Type")
        verbose_name_plural = _("Location Types")
        unique_together = [['tenant_id', 'code']]
        ordering = ['name']
        
    def __str__(self):
        return self.name


class Location(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Warehouse location model for inventory items
    """
    name = models.CharField(_("Name"), max_length=255)
    code = models.CharField(_("Code"), max_length=50)
    location_type = models.ForeignKey(
        LocationType,
        on_delete=models.PROTECT,
        related_name="locations",
        verbose_name=_("Location Type"),
        null=True, blank=True
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="children",
        verbose_name=_("Parent Location"),
        help_text=_("If this is a sub-location (e.g., a shelf within a warehouse)")
    )
    # Physical address
    address = models.TextField(_("Address"), blank=True)
    city = models.CharField(_("City"), max_length=100, blank=True)
    state = models.CharField(_("State/Province"), max_length=100, blank=True)
    country = models.CharField(_("Country"), max_length=100, blank=True)
    postal_code = models.CharField(_("Postal Code"), max_length=20, blank=True)
    latitude = models.DecimalField(_("Latitude"), max_digits=10, decimal_places=7, null=True, blank=True)
    longitude = models.DecimalField(_("Longitude"), max_digits=10, decimal_places=7, null=True, blank=True)
    # Contact information
    contact_name = models.CharField(_("Contact Name"), max_length=100, blank=True)
    phone = models.CharField(_("Phone"), max_length=50, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    # Storage capacity
    area_sqm = models.DecimalField(_("Area (sqm)"), max_digits=10, decimal_places=2, null=True, blank=True,
                                  help_text=_("Storage area in square meters"))
    max_items = models.PositiveIntegerField(_("Maximum Items"), null=True, blank=True,
                                          help_text=_("Maximum number of items this location can store"))
    max_volume = models.DecimalField(_("Maximum Volume"), max_digits=10, decimal_places=2, null=True, blank=True,
                                   help_text=_("Maximum volume in cubic meters"))
    max_weight = models.DecimalField(_("Maximum Weight"), max_digits=10, decimal_places=2, null=True, blank=True,
                                   help_text=_("Maximum weight in kilograms"))
    # Status
    is_active = models.BooleanField(_("Active"), default=True)
    # Additional details
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Location")
        verbose_name_plural = _("Locations")
        unique_together = [['tenant_id', 'code']]
        
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    def get_full_path(self):
        """Return full location path including parent locations"""
        path = []
        current = self
        while current:
            path.insert(0, current.name)
            current = current.parent
        return " > ".join(path)
    
    def get_storage_summary(self):
        """Get summary of items stored in this location"""
        total_items = self.items.count()
        unique_items = self.items.values('item').distinct().count()
        total_quantity = sum(item_loc.quantity for item_loc in self.items.all())
        
        return {
            "total_items": total_items,
            "unique_items": unique_items,
            "total_quantity": total_quantity
        }
    
    def clean(self):
        """Validate the location data"""
        from django.core.exceptions import ValidationError
        
        # Prevent circular references in the parent hierarchy
        if self.parent:
            current = self.parent
            while current:
                if current == self:
                    raise ValidationError(_("Circular reference detected in parent hierarchy"))
                current = current.parent
        
        # Ensure location type settings are consistent
        if self.location_type and self.location_type.requires_bin_locations and not self.children.exists():
            # Only warn, don't prevent saving
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'location_type': _("This location type requires bin locations, but none are defined.")},
                code='warning'  # Custom code to indicate this is a warning, not an error
            )


class BinLocation(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Specific storage bin within a location (e.g., shelves, racks, bins)
    """
    location = models.ForeignKey(
        Location,
        on_delete=models.CASCADE,
        related_name="bins",
        verbose_name=_("Location")
    )
    name = models.CharField(_("Name"), max_length=100)
    code = models.CharField(_("Code"), max_length=50)
    description = models.TextField(_("Description"), blank=True)
    is_active = models.BooleanField(_("Active"), default=True)
    # Bin position information
    aisle = models.CharField(_("Aisle"), max_length=20, blank=True, help_text=_("Aisle identifier"))
    rack = models.CharField(_("Rack"), max_length=20, blank=True, help_text=_("Rack identifier"))
    shelf = models.CharField(_("Shelf"), max_length=20, blank=True, help_text=_("Shelf identifier"))
    position = models.CharField(_("Position"), max_length=20, blank=True, help_text=_("Position identifier"))
    barcode = models.CharField(_("Barcode"), max_length=100, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Bin Location")
        verbose_name_plural = _("Bin Locations")
        unique_together = [['tenant_id', 'location', 'code']]
        ordering = ['location', 'aisle', 'rack', 'shelf', 'position']
    
    def __str__(self):
        pos_parts = []
        if self.aisle:
            pos_parts.append(f"A{self.aisle}")
        if self.rack:
            pos_parts.append(f"R{self.rack}")
        if self.shelf:
            pos_parts.append(f"S{self.shelf}")
        if self.position:
            pos_parts.append(f"P{self.position}")
            
        position_str = "-".join(pos_parts) if pos_parts else ""
        
        if position_str:
            return f"{self.name} ({position_str})"
        return self.name


class ItemLocation(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Many-to-many relationship between items and locations with quantity
    """
    item = models.ForeignKey('inventory.Item', related_name='locations', on_delete=models.CASCADE)
    location = models.ForeignKey(Location, related_name='items', on_delete=models.CASCADE)
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2, default=0)
    bin_location = models.ForeignKey(
        BinLocation, 
        related_name='items',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        verbose_name=_("Bin Location")
    )
    # Batch tracking support
    batch = models.ForeignKey(
        'inventory.ItemBatch',
        related_name='locations',
        on_delete=models.CASCADE,
        null=True, blank=True,
        verbose_name=_("Batch"),
        help_text=_("Specific batch stored at this location")
    )
    # Additional tracking information
    reorder_point = models.DecimalField(_("Reorder Point"), max_digits=10, decimal_places=2, null=True, blank=True,
                                      help_text=_("Minimum quantity before reordering for this specific location"))
    max_stock = models.DecimalField(_("Maximum Stock"), max_digits=10, decimal_places=2, null=True, blank=True,
                                  help_text=_("Maximum quantity to stock at this location"))
    min_stock = models.DecimalField(_("Minimum Stock"), max_digits=10, decimal_places=2, null=True, blank=True,
                                  help_text=_("Minimum quantity to maintain at this location"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item Location")
        verbose_name_plural = _("Item Locations")
        unique_together = [['tenant_id', 'item', 'location', 'bin_location', 'batch']]
        
    def __str__(self):
        bin_info = f" ({self.bin_location})" if self.bin_location else ""
        batch_info = f" [Batch: {self.batch.batch_number}]" if self.batch else ""
        return f"{self.item.name} at {self.location.name}{bin_info}{batch_info}: {self.quantity}"
    
    @property
    def is_low_stock(self):
        """Check if item is below minimum stock level at this location"""
        if self.min_stock is None:
            return False
        return self.quantity < self.min_stock
    
    def clean(self):
        """Validate ItemLocation data"""
        super().clean()
        
        # If batch is specified, ensure it belongs to the item
        if self.batch and self.batch.item != self.item:
            from django.core.exceptions import ValidationError
            raise ValidationError(_("Batch must belong to the same item"))
        
        # If item requires batch tracking, batch must be specified for positive quantities
        if (self.item and self.item.requires_batch_tracking() and 
            not self.batch and self.quantity > 0):
            from django.core.exceptions import ValidationError
            raise ValidationError(_("This item requires batch tracking for all stock locations"))
    
    def get_batch_info(self):
        """Get batch information for this location"""
        if not self.batch:
            return None
        
        return {
            'batch_number': self.batch.batch_number,
            'expiry_date': self.batch.expiry_date,
            'status': self.batch.status,
            'days_until_expiry': self.batch.days_until_expiry,
            'is_expired': self.batch.is_expired
        }


class TransferOrder(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Transfer order for moving items between locations
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('pending', _('Pending')),
        ('in_transit', _('In Transit')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    )
    
    reference = models.CharField(_("Reference"), max_length=100, unique=True)
    source_location = models.ForeignKey(
        Location, related_name='outgoing_transfers', 
        on_delete=models.PROTECT, verbose_name=_("Source Location")
    )
    destination_location = models.ForeignKey(
        Location, related_name='incoming_transfers', 
        on_delete=models.PROTECT, verbose_name=_("Destination Location")
    )
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='draft')
    notes = models.TextField(_("Notes"), blank=True)
    items_count = models.IntegerField(_("Items Count"), default=0, help_text=_("Total number of items in this transfer"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Transfer Order")
        verbose_name_plural = _("Transfer Orders")
        
    def __str__(self):
        return f"Transfer {self.reference}: {self.source_location.name} → {self.destination_location.name}"


class TransferOrderItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Items included in a transfer order
    """
    transfer_order = models.ForeignKey(
        TransferOrder, related_name='items', 
        on_delete=models.CASCADE, verbose_name=_("Transfer Order")
    )
    item = models.ForeignKey(
        'inventory.Item', related_name='transfers',
        on_delete=models.PROTECT, verbose_name=_("Item")
    )
    batch = models.ForeignKey(
        'inventory.ItemBatch',
        related_name='transfers',
        on_delete=models.PROTECT,
        null=True, blank=True,
        verbose_name=_("Batch"),
        help_text=_("Specific batch being transferred")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Transfer Order Item")
        verbose_name_plural = _("Transfer Order Items")
        unique_together = [['transfer_order', 'item', 'batch']]
        
    def __str__(self):
        batch_info = f" (Batch: {self.batch.batch_number})" if self.batch else ""
        return f"{self.quantity} x {self.item.name}{batch_info} in {self.transfer_order.reference}"
    
    def clean(self):
        """Validate TransferOrderItem data"""
        super().clean()
        
        # If batch is specified, ensure it belongs to the item
        if self.batch and self.batch.item != self.item:
            from django.core.exceptions import ValidationError
            raise ValidationError(_("Batch must belong to the same item"))
        
        # If item requires batch tracking, batch must be specified
        if self.item and self.item.requires_batch_tracking() and not self.batch:
            from django.core.exceptions import ValidationError
            raise ValidationError(_("This item requires batch tracking for transfers"))


class Transfer(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Transfer record for moved items between locations
    
    This is a simplified transfer record model for reporting purposes.
    """
    source_location = models.ForeignKey(
        Location, related_name='outgoing_simple_transfers', 
        on_delete=models.PROTECT, verbose_name=_("Source Location")
    )
    destination_location = models.ForeignKey(
        Location, related_name='incoming_simple_transfers', 
        on_delete=models.PROTECT, verbose_name=_("Destination Location")
    )
    items_count = models.IntegerField(_("Items Count"), default=0)
    reference = models.CharField(_("Reference"), max_length=100, blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Transfer")
        verbose_name_plural = _("Transfers")
        
    def __str__(self):
        return f"Transfer {self.id} from {self.source_location} to {self.destination_location}"


class WarehouseTimer(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Timer system for warehouses with different visibility levels
    """
    TIMER_TYPE_CHOICES = [
        ('opening_hours', _('Opening Hours')),
        ('maintenance_window', _('Maintenance Window')),
        ('special_operations', _('Special Operations')),
        ('holiday_schedule', _('Holiday Schedule')),
        ('emergency_access', _('Emergency Access')),
        ('custom', _('Custom Timer')),
    ]
    
    VISIBILITY_LEVEL_CHOICES = [
        ('franchise', _('Franchise Level - Visible to all companies and service centers')),
        ('company', _('Company Level - Visible to company and their service centers')),
        ('service_center', _('Service Center Level - Visible in service center only')),
    ]
    
    RECURRENCE_CHOICES = [
        ('daily', _('Daily')),
        ('weekly', _('Weekly')),
        ('monthly', _('Monthly')),
        ('yearly', _('Yearly')),
        ('once', _('One Time')),
    ]
    
    # Basic Information
    name = models.CharField(_("Timer Name"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    timer_type = models.CharField(_("Timer Type"), max_length=50, choices=TIMER_TYPE_CHOICES, default='custom')
    
    # Visibility and Scope
    visibility_level = models.CharField(_("Visibility Level"), max_length=20, choices=VISIBILITY_LEVEL_CHOICES)
    
    # Organizational Links (based on visibility level)
    franchise = models.ForeignKey(
        'setup.Franchise',
        on_delete=models.CASCADE,
        related_name='warehouse_timers',
        verbose_name=_("Franchise"),
        null=True, blank=True,
        help_text=_("Required for franchise-level timers")
    )
    company = models.ForeignKey(
        'setup.Company',
        on_delete=models.CASCADE,
        related_name='warehouse_timers',
        verbose_name=_("Company"),
        null=True, blank=True,
        help_text=_("Required for company-level timers")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.CASCADE,
        related_name='warehouse_timers',
        verbose_name=_("Service Center"),
        null=True, blank=True,
        help_text=_("Required for service center-level timers")
    )
    
    # Associated Warehouses
    warehouses = models.ManyToManyField(
        Location,
        related_name='timers',
        verbose_name=_("Affected Warehouses"),
        help_text=_("Warehouses affected by this timer")
    )
    
    # Time Settings
    start_date = models.DateField(_("Start Date"))
    end_date = models.DateField(_("End Date"), null=True, blank=True)
    start_time = models.TimeField(_("Start Time"))
    end_time = models.TimeField(_("End Time"))
    
    # Recurrence Settings
    recurrence = models.CharField(_("Recurrence"), max_length=20, choices=RECURRENCE_CHOICES, default='daily')
    recurrence_days = models.JSONField(_("Recurrence Days"), default=list, blank=True, 
                                     help_text=_("Days of week for weekly recurrence (0=Monday, 6=Sunday)"))
    
    # Sharing Settings (for service center level)
    can_share_with_company_centers = models.BooleanField(
        _("Share with Other Company Service Centers"),
        default=False,
        help_text=_("Allow other service centers in the same company to see this timer")
    )
    
    # Status and Permissions
    is_active = models.BooleanField(_("Active"), default=True)
    blocks_operations = models.BooleanField(_("Blocks Operations"), default=False,
                                          help_text=_("If true, prevents warehouse operations during timer period"))
    requires_approval = models.BooleanField(_("Requires Approval"), default=False,
                                          help_text=_("If true, timer changes need approval"))
    
    # Creator and Approval
    created_by = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='created_warehouse_timers',
        verbose_name=_("Created By")
    )
    approved_by = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='approved_warehouse_timers',
        verbose_name=_("Approved By")
    )
    approved_at = models.DateTimeField(_("Approved At"), null=True, blank=True)
    
    # Additional Settings
    notification_enabled = models.BooleanField(_("Enable Notifications"), default=True)
    notification_minutes_before = models.PositiveIntegerField(_("Notify Minutes Before"), default=30)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Warehouse Timer")
        verbose_name_plural = _("Warehouse Timers")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['visibility_level', 'is_active']),
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['franchise', 'company', 'service_center']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_visibility_level_display()})"
    
    def clean(self):
        """Validate timer configuration"""
        from django.core.exceptions import ValidationError
        
        # Validate organizational requirements
        if self.visibility_level == 'franchise' and not self.franchise:
            raise ValidationError({'franchise': _("Franchise is required for franchise-level timers")})
        elif self.visibility_level == 'company' and not self.company:
            raise ValidationError({'company': _("Company is required for company-level timers")})
        elif self.visibility_level == 'service_center' and not self.service_center:
            raise ValidationError({'service_center': _("Service Center is required for service center-level timers")})
        
        # Validate date range
        if self.end_date and self.start_date and self.start_date > self.end_date:
            raise ValidationError({'end_date': _("End date cannot be before start date")})
        
        # Validate time range for same-day timers
        if not self.end_date or (self.start_date and self.end_date and self.start_date == self.end_date):
            if self.start_time and self.end_time and self.start_time >= self.end_time:
                raise ValidationError({'end_time': _("End time must be after start time for same-day timers")})
        
        # Validate weekly recurrence
        if self.recurrence == 'weekly' and not self.recurrence_days:
            raise ValidationError({'recurrence_days': _("Please specify days for weekly recurrence")})
        
        # Set organizational hierarchy automatically
        if self.visibility_level == 'company' and self.company and not self.franchise:
            self.franchise = self.company.franchise
        elif self.visibility_level == 'service_center' and self.service_center:
            if not self.company:
                self.company = self.service_center.company
            if not self.franchise:
                self.franchise = self.service_center.company.franchise
    
    @classmethod
    def get_visible_timers_for_user(cls, user, warehouses=None):
        """
        Get timers visible to a user based on their role and organizational scope
        """
        from setup.models import UserProfile
        
        try:
            user_profile = user.profile
            primary_role = None
            
            # Get user's primary role
            if hasattr(user, 'user_roles'):
                user_roles = user.user_roles.filter(is_active=True)
                for user_role in user_roles:
                    if user_role.is_primary:
                        primary_role = user_role
                        break
                if not primary_role and user_roles.exists():
                    primary_role = user_roles.first()
        except (UserProfile.DoesNotExist, AttributeError):
            return cls.objects.none()
        
        if not primary_role:
            return cls.objects.none()
        
        # Build query based on user's organizational scope
        query = models.Q(is_active=True)
        
        if primary_role.franchise:
            # Franchise-level user can see:
            # 1. Franchise-level timers for their franchise
            # 2. Company-level timers for companies in their franchise
            # 3. Service center-level timers for service centers in their franchise (if shared)
            franchise_query = models.Q(
                visibility_level='franchise',
                franchise=primary_role.franchise
            )
            company_query = models.Q(
                visibility_level='company',
                company__franchise=primary_role.franchise
            )
            service_center_query = models.Q(
                visibility_level='service_center',
                service_center__company__franchise=primary_role.franchise,
                can_share_with_company_centers=True
            )
            query &= (franchise_query | company_query | service_center_query)
            
        elif primary_role.company:
            # Company-level user can see:
            # 1. Franchise-level timers for their franchise
            # 2. Company-level timers for their company
            # 3. Service center-level timers for service centers in their company (if shared)
            franchise_query = models.Q(
                visibility_level='franchise',
                franchise=primary_role.company.franchise
            )
            company_query = models.Q(
                visibility_level='company',
                company=primary_role.company
            )
            service_center_query = models.Q(
                visibility_level='service_center',
                service_center__company=primary_role.company,
                can_share_with_company_centers=True
            )
            query &= (franchise_query | company_query | service_center_query)
            
        elif primary_role.service_center:
            # Service center-level user can see:
            # 1. Franchise-level timers for their franchise
            # 2. Company-level timers for their company
            # 3. Service center-level timers for their service center
            # 4. Other service center timers in their company (if shared)
            franchise_query = models.Q(
                visibility_level='franchise',
                franchise=primary_role.service_center.company.franchise
            )
            company_query = models.Q(
                visibility_level='company',
                company=primary_role.service_center.company
            )
            own_service_center_query = models.Q(
                visibility_level='service_center',
                service_center=primary_role.service_center
            )
            shared_service_center_query = models.Q(
                visibility_level='service_center',
                service_center__company=primary_role.service_center.company,
                can_share_with_company_centers=True
            )
            query &= (franchise_query | company_query | own_service_center_query | shared_service_center_query)
        else:
            return cls.objects.none()
        
        # Filter by specific warehouses if provided
        if warehouses:
            warehouse_ids = [w.id if hasattr(w, 'id') else w for w in warehouses]
            query &= models.Q(warehouses__in=warehouse_ids)
        
        return cls.objects.filter(query).distinct()
    
    def is_active_now(self):
        """Check if timer is currently active"""
        if not self.is_active:
            return False
        
        now = timezone.now()
        current_date = now.date()
        current_time = now.time()
        current_weekday = now.weekday()  # 0=Monday, 6=Sunday
        
        # Check if current date is within timer date range
        if current_date < self.start_date:
            return False
        if self.end_date and current_date > self.end_date:
            return False
        
        # Check recurrence
        if self.recurrence == 'once':
            # One-time timer - only active on start date
            if current_date != self.start_date:
                return False
        elif self.recurrence == 'weekly':
            # Weekly timer - check if today is in the specified days
            if not self.recurrence_days or current_weekday not in self.recurrence_days:
                return False
        elif self.recurrence == 'monthly':
            # Monthly timer - active on the same day of month as start date
            if current_date.day != self.start_date.day:
                return False
        elif self.recurrence == 'yearly':
            # Yearly timer - active on the same day and month as start date
            if current_date.month != self.start_date.month or current_date.day != self.start_date.day:
                return False
        # Daily timer is always active within date range
        
        # Check if current time is within timer time range
        if self.end_date and current_date == self.end_date:
            # On the end date, check time range
            if current_time < self.start_time or current_time > self.end_time:
                return False
        elif current_date == self.start_date:
            # On the start date, check time range
            if current_time < self.start_time:
                return False
            if not self.end_date and current_time > self.end_time:
                return False
        else:
            # On other days, check if we're within the time range
            if current_time < self.start_time or current_time > self.end_time:
                return False
        
        return True
    
    def get_next_occurrence(self):
        """Get the next occurrence of this timer"""
        if not self.is_active:
            return None
        
        now = timezone.now()
        
        if self.recurrence == 'once':
            # One-time timer
            if now.date() <= self.start_date:
                return timezone.make_aware(datetime.combine(self.start_date, self.start_time))
            return None
        
        # Calculate next occurrence based on recurrence type
        if self.recurrence == 'daily':
            next_date = now.date()
            if now.time() > self.end_time:
                next_date += timedelta(days=1)
        elif self.recurrence == 'weekly':
            next_date = now.date()
            current_weekday = now.weekday()
            
            # Find next valid weekday
            days_ahead = None
            for day in sorted(self.recurrence_days):
                if day > current_weekday or (day == current_weekday and now.time() <= self.start_time):
                    days_ahead = day - current_weekday
                    break
            
            if days_ahead is None:
                # Next occurrence is next week
                days_ahead = (7 - current_weekday) + min(self.recurrence_days)
            
            next_date += timedelta(days=days_ahead)
        else:
            # Monthly/yearly - simplified calculation
            next_date = now.date() + timedelta(days=1)
        
        # Ensure next date is not beyond end date
        if self.end_date and next_date > self.end_date:
            return None
        
        return timezone.make_aware(datetime.combine(next_date, self.start_time))


class WarehouseTimerLog(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """
    Log of warehouse timer activations and operations
    """
    timer = models.ForeignKey(
        WarehouseTimer,
        on_delete=models.CASCADE,
        related_name='logs',
        verbose_name=_("Timer")
    )
    
    ACTION_CHOICES = [
        ('activated', _('Timer Activated')),
        ('deactivated', _('Timer Deactivated')),
        ('blocked_operation', _('Blocked Operation')),
        ('override_granted', _('Override Granted')),
        ('notification_sent', _('Notification Sent')),
    ]
    
    action = models.CharField(_("Action"), max_length=50, choices=ACTION_CHOICES)
    description = models.TextField(_("Description"), blank=True)
    
    # Context Information
    affected_warehouse = models.ForeignKey(
        Location,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='timer_logs',
        verbose_name=_("Affected Warehouse")
    )
    user = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='warehouse_timer_logs',
        verbose_name=_("User")
    )
    
    # Additional Data
    metadata = models.JSONField(_("Additional Data"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Warehouse Timer Log")
        verbose_name_plural = _("Warehouse Timer Logs")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.timer.name} - {self.get_action_display()} at {self.created_at}"

# Import signals to register them
try:
    from . import signals
except ImportError:
    pass
