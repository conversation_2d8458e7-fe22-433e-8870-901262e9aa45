from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from django.db.models import F
from .models import TransferOrder, TransferOrderItem, ItemLocation, Location, BinLocation
from inventory.models import Movement, MovementType
from notifications.services import NotificationService
from user_roles.models import UserRole
import logging

logger = logging.getLogger(__name__)


@receiver(pre_save, sender=TransferOrder)
def transfer_order_pre_save(sender, instance, **kwargs):
    """Track original status for transfer orders"""
    if instance.pk:
        try:
            original = TransferOrder.objects.get(pk=instance.pk)
            instance._original_status = original.status
        except TransferOrder.DoesNotExist:
            instance._original_status = None
    else:
        instance._original_status = None


@receiver(post_save, sender=TransferOrder)
def transfer_order_status_changed(sender, instance, created, **kwargs):
    """Handle transfer order status changes with notifications"""
    try:
        if created:
            _notify_transfer_order_created(instance)
        elif hasattr(instance, '_original_status') and instance._original_status != instance.status:
            _notify_transfer_order_status_change(instance, instance._original_status, instance.status)
            
    except Exception as e:
        logger.error(f"Error in transfer_order_status_changed signal: {e}")


@receiver(post_save, sender=TransferOrderItem)
def transfer_item_added(sender, instance, created, **kwargs):
    """Handle transfer order item additions"""
    if created:
        try:
            _notify_transfer_item_added(instance)
        except Exception as e:
            logger.error(f"Error in transfer_item_added signal: {e}")


@receiver(post_save, sender=ItemLocation)
def item_location_updated(sender, instance, created, **kwargs):
    """Handle item location updates and stock movements"""
    try:
        if created:
            _notify_new_item_location(instance)
        else:
            # Check for significant quantity changes
            if hasattr(instance, '_previous_quantity'):
                if abs(instance.quantity - instance._previous_quantity) >= 10:
                    _notify_location_quantity_change(instance, instance._previous_quantity)
                    
    except Exception as e:
        logger.error(f"Error in item_location_updated signal: {e}")


@receiver(pre_save, sender=ItemLocation)
def item_location_pre_save(sender, instance, **kwargs):
    """Track previous quantity before saving"""
    if instance.pk:
        try:
            previous = sender.objects.get(pk=instance.pk)
            instance._previous_quantity = previous.quantity
        except sender.DoesNotExist:
            instance._previous_quantity = 0


@receiver(post_save, sender=Location)
def location_created_or_updated(sender, instance, created, **kwargs):
    """Handle location creation and updates"""
    try:
        if created:
            _notify_new_location_created(instance)
        else:
            # Check if location status changed to inactive
            if hasattr(instance, '_previous_active') and instance._previous_active != instance.is_active:
                _notify_location_status_change(instance)
                
    except Exception as e:
        logger.error(f"Error in location_created_or_updated signal: {e}")


@receiver(pre_save, sender=Location)
def location_pre_save(sender, instance, **kwargs):
    """Track previous location status"""
    if instance.pk:
        try:
            previous = sender.objects.get(pk=instance.pk)
            instance._previous_active = previous.is_active
        except sender.DoesNotExist:
            instance._previous_active = True


# ==================== NOTIFICATION HELPER FUNCTIONS ====================

def _notify_transfer_order_created(transfer_order):
    """Notify about new transfer order creation with approval actions"""
    try:
        # Get warehouse managers and relevant staff
        managers = _get_users_by_roles([
            'warehouse_manager', 'inventory_manager', 'service_center_manager'
        ], transfer_order.tenant_id)
        
        for manager in managers:
            # Create notification
            NotificationService.create_notification(
                recipient=manager,
                notification_type_code='transfer_request_created',
                title=str(_("New Transfer Order - {order_number}")).format(
                    order_number=getattr(transfer_order, 'order_number', transfer_order.id)
                ),
                message=str(_("Transfer from {source} to {destination} requires approval")).format(
                    source=transfer_order.source_location.name,
                    destination=transfer_order.destination_location.name
                ),
                priority='medium',
                action_required=True,
                action_url=reverse('warehouse:transfer_order_detail', kwargs={'pk': transfer_order.pk}),
                action_text=str(_("Review Transfer")),
                related_object_type='transfer_order',
                related_object_id=str(transfer_order.id),
                tenant_id=transfer_order.tenant_id
            )
            
            # Create action item for approval
            NotificationService.create_action_item(
                assigned_to=manager,
                action_type='approve_transfer',
                title=str(_("Approve Transfer Order")).format(),
                description=str(_("Review and approve transfer from {source} to {destination}")).format(
                    source=transfer_order.source_location.name,
                    destination=transfer_order.destination_location.name
                ),
                priority='medium',
                action_url=reverse('warehouse:transfer_order_detail', kwargs={'pk': transfer_order.pk}),
                related_object_type='transfer_order',
                related_object_id=str(transfer_order.id),
                metadata={
                    'order_number': str(getattr(transfer_order, 'order_number', transfer_order.id)),
                    'source_location': transfer_order.source_location.name,
                    'destination_location': transfer_order.destination_location.name,
                    'actions': [
                        {'type': 'approve', 'label': str(_('Approve Transfer')), 'class': 'bg-green-600 text-white'},
                        {'type': 'deny', 'label': str(_('Reject Transfer')), 'class': 'bg-red-600 text-white'},
                        {'type': 'modify', 'label': str(_('Request Changes')), 'class': 'bg-blue-600 text-white'}
                    ]
                },
                tenant_id=transfer_order.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_transfer_order_created: {e}")


def _notify_transfer_order_status_change(transfer_order, old_status, new_status):
    """Notify about transfer order status changes"""
    try:
        # Get relevant users based on status change
        if new_status == 'approved':
            # Notify warehouse staff to execute transfer
            staff = _get_users_by_roles([
                'warehouse_staff', 'parts_clerk'
            ], transfer_order.tenant_id)
            
            for staff_member in staff:
                NotificationService.create_action_item(
                    assigned_to=staff_member,
                    action_type='execute_transfer',
                    title=str(_("Execute Approved Transfer")).format(),
                    description=str(_("Transfer order has been approved. Please execute the transfer")).format(),
                    priority='high',
                    action_url=reverse('warehouse:transfer_order_detail', kwargs={'pk': transfer_order.pk}),
                    related_object_type='transfer_order',
                    related_object_id=str(transfer_order.id),
                    metadata={
                        'source_location': transfer_order.source_location.name,
                        'destination_location': transfer_order.destination_location.name,
                        'actions': [
                            {'type': 'complete', 'label': str(_('Mark Complete')), 'class': 'bg-green-600 text-white'},
                            {'type': 'partial', 'label': str(_('Partial Transfer')), 'class': 'bg-yellow-600 text-white'},
                            {'type': 'issue', 'label': str(_('Report Issue')), 'class': 'bg-red-600 text-white'}
                        ]
                    },
                    tenant_id=transfer_order.tenant_id
                )
                
        elif new_status == 'completed':
            # Notify managers about completion
            managers = _get_users_by_roles([
                'warehouse_manager', 'inventory_manager'
            ], transfer_order.tenant_id)
            
            for manager in managers:
                NotificationService.create_notification(
                    recipient=manager,
                    notification_type_code='transfer_completed',
                    title=str(_("Transfer Order Completed")).format(),
                    message=str(_("Transfer from {source} to {destination} has been completed")).format(
                        source=transfer_order.source_location.name,
                        destination=transfer_order.destination_location.name
                    ),
                    priority='low',
                    action_required=False,
                    action_url=reverse('warehouse:transfer_order_detail', kwargs={'pk': transfer_order.pk}),
                    action_text=str(_("View Details")),
                    related_object_type='transfer_order',
                    related_object_id=str(transfer_order.id),
                    tenant_id=transfer_order.tenant_id
                )
                
        elif new_status == 'rejected':
            # Notify requester about rejection
            if hasattr(transfer_order, 'requested_by') and transfer_order.requested_by:
                NotificationService.create_notification(
                    recipient=transfer_order.requested_by,
                    notification_type_code='transfer_rejected',
                    title=str(_("Transfer Order Rejected")).format(),
                    message=str(_("Your transfer request has been rejected")).format(),
                    priority='medium',
                    action_required=False,
                    action_url=reverse('warehouse:transfer_order_detail', kwargs={'pk': transfer_order.pk}),
                    action_text=str(_("View Rejection Details")),
                    related_object_type='transfer_order',
                    related_object_id=str(transfer_order.id),
                    tenant_id=transfer_order.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_transfer_order_status_change: {e}")


def _notify_transfer_item_added(transfer_item):
    """Notify about items added to transfer orders"""
    try:
        # Check if this is a high-value or critical item
        item = transfer_item.item
        if hasattr(item, 'cost_price') and item.cost_price and (item.cost_price * transfer_item.requested_quantity) > 1000:
            
            managers = _get_users_by_roles([
                'warehouse_manager', 'inventory_manager'
            ], transfer_item.tenant_id)
            
            for manager in managers:
                NotificationService.create_action_item(
                    assigned_to=manager,
                    action_type='approve_high_value_transfer',
                    title=str(_("High-Value Item Transfer Approval")).format(),
                    description=str(_("High-value item {item_name} added to transfer. Value: {value}")).format(
                        item_name=item.name,
                        value=item.cost_price * transfer_item.requested_quantity
                    ),
                    priority='high',
                    action_url=reverse('warehouse:transfer_order_detail', kwargs={'pk': transfer_item.transfer_order.pk}),
                    related_object_type='transfer_order_item',
                    related_object_id=str(transfer_item.id),
                    metadata={
                        'item_name': item.name,
                        'quantity': float(transfer_item.requested_quantity),
                        'estimated_value': float(item.cost_price * transfer_item.requested_quantity) if item.cost_price else 0,
                        'actions': [
                            {'type': 'approve', 'label': str(_('Approve')), 'class': 'bg-green-600 text-white'},
                            {'type': 'deny', 'label': str(_('Reject')), 'class': 'bg-red-600 text-white'},
                            {'type': 'reduce_qty', 'label': str(_('Reduce Quantity')), 'class': 'bg-yellow-600 text-white'}
                        ]
                    },
                    tenant_id=transfer_item.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_transfer_item_added: {e}")


def _notify_new_item_location(item_location):
    """Notify about new item location assignments"""
    try:
        managers = _get_users_by_roles([
            'warehouse_manager', 'inventory_manager'
        ], item_location.tenant_id)
        
        for manager in managers:
            NotificationService.create_notification(
                recipient=manager,
                notification_type_code='item_location_created',
                title=str(_("New Item Location Assignment")).format(),
                message=str(_("Item {item_name} assigned to location {location_name}")).format(
                    item_name=item_location.item.name,
                    location_name=item_location.location.name
                ),
                priority='low',
                action_required=False,
                action_url=reverse('warehouse:item_location_detail', kwargs={'pk': item_location.pk}),
                action_text=str(_("View Location")),
                related_object_type='item_location',
                related_object_id=str(item_location.id),
                tenant_id=item_location.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_new_item_location: {e}")


def _notify_location_quantity_change(item_location, previous_quantity):
    """Notify about significant quantity changes in locations"""
    try:
        quantity_change = item_location.quantity - previous_quantity
        
        # Only notify for significant changes
        if abs(quantity_change) >= 10:
            managers = _get_users_by_roles([
                'warehouse_manager', 'inventory_manager'
            ], item_location.tenant_id)
            
            change_type = "increased" if quantity_change > 0 else "decreased"
            
            for manager in managers:
                NotificationService.create_notification(
                    recipient=manager,
                    notification_type_code='location_quantity_change',
                    title=str(_("Location Quantity Changed")).format(),
                    message=str(_("Location {location}: {item_name} {change_type} by {change} units")).format(
                        location=item_location.location.name,
                        item_name=item_location.item.name,
                        change_type=change_type,
                        change=abs(quantity_change)
                    ),
                    priority='medium',
                    action_required=abs(quantity_change) >= 50,  # Require action for large changes
                    action_url=reverse('warehouse:item_location_detail', kwargs={'pk': item_location.pk}),
                    action_text=str(_("Investigate Change")),
                    related_object_type='item_location',
                    related_object_id=str(item_location.id),
                    tenant_id=item_location.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_location_quantity_change: {e}")


def _notify_new_location_created(location):
    """Notify about new warehouse location creation"""
    try:
        managers = _get_users_by_roles([
            'warehouse_manager', 'service_center_manager'
        ], location.tenant_id)
        
        for manager in managers:
            NotificationService.create_action_item(
                assigned_to=manager,
                action_type='setup_new_location',
                title=str(_("New Location Setup Required")).format(),
                description=str(_("New location {location_name} created. Please complete setup")).format(
                    location_name=location.name
                ),
                priority='medium',
                action_url=reverse('warehouse:location_detail', kwargs={'pk': location.pk}),
                related_object_type='location',
                related_object_id=str(location.id),
                metadata={
                    'location_name': location.name,
                    'location_type': getattr(location, 'location_type', 'general'),
                    'actions': [
                        {'type': 'configure', 'label': str(_('Complete Setup')), 'class': 'bg-blue-600 text-white'},
                        {'type': 'assign_staff', 'label': str(_('Assign Staff')), 'class': 'bg-green-600 text-white'},
                        {'type': 'defer', 'label': str(_('Setup Later')), 'class': 'bg-gray-600 text-white'}
                    ]
                },
                tenant_id=location.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_new_location_created: {e}")


def _notify_location_status_change(location):
    """Notify about location status changes"""
    try:
        managers = _get_users_by_roles([
            'warehouse_manager', 'inventory_manager'
        ], location.tenant_id)
        
        status_text = "activated" if location.is_active else "deactivated"
        
        for manager in managers:
            NotificationService.create_notification(
                recipient=manager,
                notification_type_code='location_status_change',
                title=str(_("Location Status Changed")).format(),
                message=str(_("Location {location_name} has been {status}")).format(
                    location_name=location.name,
                    status=status_text
                ),
                priority='medium',
                action_required=not location.is_active,  # Require action when deactivated
                action_url=reverse('warehouse:location_detail', kwargs={'pk': location.pk}),
                action_text=str(_("Review Location")),
                related_object_type='location',
                related_object_id=str(location.id),
                tenant_id=location.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_location_status_change: {e}")


def _get_users_by_roles(role_codes, tenant_id):
    """Helper function to get users by role codes"""
    try:
        user_roles = UserRole.objects.filter(
            role__code__in=role_codes,
            tenant_id=tenant_id,
            is_active=True
        ).select_related('user', 'role')
        
        return [ur.user for ur in user_roles if ur.user.is_active]
    except Exception as e:
        logger.error(f"Error getting users by roles: {e}")
        return [] 