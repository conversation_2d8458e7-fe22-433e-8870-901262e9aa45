{% extends "core/dashboard_base.html" %}
{% load i18n %}
{% load core_tags %}

{% block title %}{% if object %}{% trans "Edit Timer" %}{% else %}{% trans "New Timer" %}{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-6">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">
                {% if object %}{% trans "Edit Warehouse Timer" %}{% else %}{% trans "Create Warehouse Timer" %}{% endif %}
            </h1>
            <p class="text-gray-600">{% trans "Configure timer settings and organizational visibility" %}</p>
        </div>
        <a href="{% url 'warehouse:timer_list' %}" 
           class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            {% trans "Back to Timers" %}
        </a>
    </div>

    <!-- Timer Form -->
    <div class="bg-white rounded-lg shadow-sm border">
        <form method="post" id="timer-form" class="p-6">
            {% csrf_token %}
            
            <!-- Basic Information Section -->
            <div class="border-b border-gray-200 pb-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Basic Information" %}</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Timer Name -->
                    <div class="md:col-span-2">
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.name.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Timer Type -->
                    <div>
                        <label for="{{ form.timer_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.timer_type.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.timer_type }}
                        {% if form.timer_type.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.timer_type.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Visibility Level -->
                    <div>
                        <label for="{{ form.visibility_level.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.visibility_level.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.visibility_level }}
                        {% if form.visibility_level.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.visibility_level.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.description.label }}
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Organizational Scope Section -->
            <div class="border-b border-gray-200 pb-6 mb-6" id="organizational-section">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Organizational Scope" %}</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Franchise -->
                    <div id="franchise-field" style="display: none;">
                        <label for="{{ form.franchise.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.franchise.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.franchise }}
                        {% if form.franchise.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.franchise.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Company -->
                    <div id="company-field" style="display: none;">
                        <label for="{{ form.company.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.company.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.company }}
                        {% if form.company.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.company.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Service Center -->
                    <div id="service-center-field" style="display: none;">
                        <label for="{{ form.service_center.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.service_center.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.service_center }}
                        {% if form.service_center.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.service_center.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Schedule Configuration Section -->
            <div class="border-b border-gray-200 pb-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Schedule Configuration" %}</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Date Range -->
                    <div>
                        <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.start_date.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.start_date }}
                        {% if form.start_date.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.start_date.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.end_date.label }}
                        </label>
                        {{ form.end_date }}
                        {% if form.end_date.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.end_date.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-xs text-gray-500">{% trans "Leave empty for ongoing timer" %}</p>
                    </div>
                    
                    <!-- Time Range -->
                    <div>
                        <label for="{{ form.start_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.start_time.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.start_time }}
                        {% if form.start_time.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.start_time.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.end_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.end_time.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.end_time }}
                        {% if form.end_time.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.end_time.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Recurrence -->
                    <div>
                        <label for="{{ form.recurrence.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.recurrence.label }} <span class="text-red-500">*</span>
                        </label>
                        {{ form.recurrence }}
                        {% if form.recurrence.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.recurrence.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Recurrence Days (for weekly) -->
                    <div id="recurrence-days-field" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.recurrence_days_field.label }} <span class="text-red-500">*</span>
                        </label>
                        <div class="grid grid-cols-2 gap-2">
                            {% for choice in form.recurrence_days_field %}
                                <div class="flex items-center">
                                    {{ choice.tag }}
                                    <label for="{{ choice.id_for_label }}" class="ml-2 text-sm text-gray-900">{{ choice.choice_label }}</label>
                                </div>
                            {% endfor %}
                        </div>
                        {% if form.recurrence_days_field.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.recurrence_days_field.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Warehouse Selection Section -->
            <div class="border-b border-gray-200 pb-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Affected Warehouses" %}</h3>
                <div>
                    <label for="{{ form.warehouses.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.warehouses.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.warehouses }}
                    {% if form.warehouses.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.warehouses.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500">{% trans "Hold Ctrl/Cmd to select multiple warehouses" %}</p>
                </div>
            </div>

            <!-- Options Section -->
            <div class="border-b border-gray-200 pb-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Timer Options" %}</h3>
                <div class="space-y-4">
                    <!-- Checkboxes -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center">
                            {{ form.blocks_operations }}
                            <label for="{{ form.blocks_operations.id_for_label }}" class="ml-2 text-sm text-gray-900">
                                {{ form.blocks_operations.label }}
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            {{ form.requires_approval }}
                            <label for="{{ form.requires_approval.id_for_label }}" class="ml-2 text-sm text-gray-900">
                                {{ form.requires_approval.label }}
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            {{ form.notification_enabled }}
                            <label for="{{ form.notification_enabled.id_for_label }}" class="ml-2 text-sm text-gray-900">
                                {{ form.notification_enabled.label }}
                            </label>
                        </div>
                        
                        <div class="flex items-center" id="service-center-sharing" style="display: none;">
                            {{ form.can_share_with_company_centers }}
                            <label for="{{ form.can_share_with_company_centers.id_for_label }}" class="ml-2 text-sm text-gray-900">
                                {{ form.can_share_with_company_centers.label }}
                            </label>
                        </div>
                    </div>
                    
                    <!-- Notification Minutes -->
                    <div id="notification-minutes-field" style="display: none;">
                        <label for="{{ form.notification_minutes_before.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.notification_minutes_before.label }}
                        </label>
                        {{ form.notification_minutes_before }}
                        {% if form.notification_minutes_before.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.notification_minutes_before.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-xs text-gray-500">{% trans "Minutes before timer starts to send notification" %}</p>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                        {% if object %}{% trans "Update Timer" %}{% else %}{% trans "Create Timer" %}{% endif %}
                    </button>
                    <a href="{% url 'warehouse:timer_list' %}" 
                       class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                        {% trans "Cancel" %}
                    </a>
                </div>
                
                {% if object %}
                <div class="flex items-center gap-4">
                    <a href="{% url 'warehouse:timer_detail' object.pk %}" 
                       class="text-blue-600 hover:text-blue-800 font-medium">
                        {% trans "View Timer" %}
                    </a>
                    <a href="{% url 'warehouse:timer_delete' object.pk %}" 
                       class="text-red-600 hover:text-red-800 font-medium">
                        {% trans "Delete Timer" %}
                    </a>
                </div>
                {% endif %}
            </div>
        </form>
    </div>
</div>

<!-- JavaScript for Dynamic Form Behavior -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const visibilityLevelField = document.getElementById('{{ form.visibility_level.id_for_label }}');
    const recurrenceField = document.getElementById('{{ form.recurrence.id_for_label }}');
    const notificationEnabledField = document.getElementById('{{ form.notification_enabled.id_for_label }}');
    
    // Update organizational fields based on visibility level
    function updateOrganizationalFields(visibilityLevel) {
        const franchiseField = document.getElementById('franchise-field');
        const companyField = document.getElementById('company-field');
        const serviceCenterField = document.getElementById('service-center-field');
        const serviceCenterSharing = document.getElementById('service-center-sharing');
        
        // Hide all fields first
        franchiseField.style.display = 'none';
        companyField.style.display = 'none';
        serviceCenterField.style.display = 'none';
        serviceCenterSharing.style.display = 'none';
        
        // Show relevant fields based on visibility level
        if (visibilityLevel === 'franchise') {
            franchiseField.style.display = 'block';
        } else if (visibilityLevel === 'company') {
            franchiseField.style.display = 'block';
            companyField.style.display = 'block';
        } else if (visibilityLevel === 'service_center') {
            franchiseField.style.display = 'block';
            companyField.style.display = 'block';
            serviceCenterField.style.display = 'block';
            serviceCenterSharing.style.display = 'flex';
        }
    }
    
    // Update recurrence fields based on recurrence type
    function updateRecurrenceFields(recurrenceType) {
        const recurrenceDaysField = document.getElementById('recurrence-days-field');
        
        if (recurrenceType === 'weekly') {
            recurrenceDaysField.style.display = 'block';
        } else {
            recurrenceDaysField.style.display = 'none';
        }
    }
    
    // Update notification fields
    function updateNotificationFields(notificationEnabled) {
        const notificationMinutesField = document.getElementById('notification-minutes-field');
        
        if (notificationEnabled) {
            notificationMinutesField.style.display = 'block';
        } else {
            notificationMinutesField.style.display = 'none';
        }
    }
    
    // Event listeners
    if (visibilityLevelField) {
        visibilityLevelField.addEventListener('change', function() {
            updateOrganizationalFields(this.value);
        });
        // Initialize on page load
        updateOrganizationalFields(visibilityLevelField.value);
    }
    
    if (recurrenceField) {
        recurrenceField.addEventListener('change', function() {
            updateRecurrenceFields(this.value);
        });
        // Initialize on page load
        updateRecurrenceFields(recurrenceField.value);
    }
    
    if (notificationEnabledField) {
        notificationEnabledField.addEventListener('change', function() {
            updateNotificationFields(this.checked);
        });
        // Initialize on page load
        updateNotificationFields(notificationEnabledField.checked);
    }
    
    // Company and Service Center dynamic updates
    const franchiseSelect = document.getElementById('{{ form.franchise.id_for_label }}');
    const companySelect = document.getElementById('{{ form.company.id_for_label }}');
    
    if (franchiseSelect) {
        franchiseSelect.addEventListener('change', function() {
            updateCompanyOptions(this.value);
        });
    }
    
    if (companySelect) {
        companySelect.addEventListener('change', function() {
            updateServiceCenterOptions(this.value);
        });
    }
    
    // AJAX functions for updating options (placeholder - would need backend support)
    function updateCompanyOptions(franchiseId) {
        // Implementation would depend on your AJAX endpoints
        console.log('Update company options for franchise:', franchiseId);
    }
    
    function updateServiceCenterOptions(companyId) {
        // Implementation would depend on your AJAX endpoints
        console.log('Update service center options for company:', companyId);
    }
});

// Global functions for form widget onchange attributes
function updateOrganizationalFields(visibilityLevel) {
    const event = new CustomEvent('change');
    const field = document.getElementById('{{ form.visibility_level.id_for_label }}');
    if (field) {
        field.value = visibilityLevel;
        field.dispatchEvent(event);
    }
}

function updateRecurrenceFields(recurrenceType) {
    const event = new CustomEvent('change');
    const field = document.getElementById('{{ form.recurrence.id_for_label }}');
    if (field) {
        field.value = recurrenceType;
        field.dispatchEvent(event);
    }
}
</script>
{% endblock %} 