{% extends "core/dashboard_base.html" %}
{% load i18n %}
{% load core_tags %}

{% block title %}{% trans "Warehouse Timers" %}{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-6">
    <!-- Header Section -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">{% trans "Warehouse Timers" %}</h1>
            <p class="text-gray-600">{% trans "Manage warehouse timers with different visibility levels" %}</p>
        </div>
        <div class="flex items-center gap-4 mt-4 lg:mt-0">
            <!-- Quick Stats -->
            <div class="flex items-center gap-4 bg-gray-50 rounded-lg px-4 py-2">
                <div class="text-center">
                    <div class="text-xl font-bold text-blue-600">{{ active_timers_count }}</div>
                    <div class="text-xs text-gray-500">{% trans "Active" %}</div>
                </div>
                <div class="text-center">
                    <div class="text-xl font-bold text-green-600">{{ running_timers|length }}</div>
                    <div class="text-xs text-gray-500">{% trans "Running Now" %}</div>
                </div>
            </div>
            
            <!-- Add Timer Button -->
            <a href="{% url 'warehouse:timer_create' %}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                {% trans "Add Timer" %}
            </a>
        </div>
    </div>

    <!-- Currently Running Timers Alert -->
    {% if running_timers %}
    <div class="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
            <svg class="w-5 h-5 text-amber-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            <div>
                <h3 class="text-amber-800 font-medium">{% trans "Active Timers" %}</h3>
                <p class="text-amber-700 text-sm mt-1">{% trans "The following timers are currently active:" %}</p>
                <ul class="mt-2 text-sm text-amber-700">
                    {% for timer in running_timers %}
                    <li class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-amber-500 rounded-full"></span>
                        <strong>{{ timer.name }}</strong> - 
                        {{ timer.get_timer_type_display }} 
                        ({{ timer.start_time|time:"H:i" }} - {{ timer.end_time|time:"H:i" }})
                        {% if timer.blocks_operations %}
                        <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">{% trans "Blocks Operations" %}</span>
                        {% endif %}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">{% trans "Filters" %}</h3>
        </div>
        <div class="p-4">
            <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Search" %}</label>
                    <input type="text" name="search" id="search" value="{{ search_query }}"
                           placeholder="{% trans 'Search timers...' %}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                
                <!-- Timer Type Filter -->
                <div>
                    {{ filter_form.timer_type.label_tag }}
                    {{ filter_form.timer_type }}
                </div>
                
                <!-- Visibility Level Filter -->
                <div>
                    {{ filter_form.visibility_level.label_tag }}
                    {{ filter_form.visibility_level }}
                </div>
                
                <!-- Status Filter -->
                <div>
                    {{ filter_form.is_active.label_tag }}
                    {{ filter_form.is_active }}
                </div>
                
                <!-- Submit Buttons -->
                <div class="flex items-end gap-2 md:col-span-2 lg:col-span-4">
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        {% trans "Apply Filters" %}
                    </button>
                    <a href="{% url 'warehouse:timer_list' %}" 
                       class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        {% trans "Clear" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Timers List -->
    <div class="bg-white rounded-lg shadow-sm border">
        {% if timers %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Timer Name" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Type" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Visibility Level" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Schedule" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Warehouses" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for timer in timers %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ timer.name }}</div>
                            {% if timer.description %}
                            <div class="text-sm text-gray-500">{{ timer.description|truncatechars:50 }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if timer.timer_type == 'opening_hours' %}bg-blue-100 text-blue-800
                                {% elif timer.timer_type == 'maintenance_window' %}bg-yellow-100 text-yellow-800
                                {% elif timer.timer_type == 'emergency_access' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ timer.get_timer_type_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if timer.visibility_level == 'franchise' %}
                                    <span class="text-purple-600 font-medium">{% trans "Franchise" %}</span>
                                    {% if timer.franchise %}<br><span class="text-xs text-gray-500">{{ timer.franchise.name }}</span>{% endif %}
                                {% elif timer.visibility_level == 'company' %}
                                    <span class="text-blue-600 font-medium">{% trans "Company" %}</span>
                                    {% if timer.company %}<br><span class="text-xs text-gray-500">{{ timer.company.name }}</span>{% endif %}
                                {% elif timer.visibility_level == 'service_center' %}
                                    <span class="text-green-600 font-medium">{% trans "Service Center" %}</span>
                                    {% if timer.service_center %}<br><span class="text-xs text-gray-500">{{ timer.service_center.name }}</span>{% endif %}
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div>{{ timer.start_time|time:"H:i" }} - {{ timer.end_time|time:"H:i" }}</div>
                            <div class="text-xs text-gray-500">{{ timer.get_recurrence_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if timer.warehouses.count > 0 %}
                                <div class="text-sm">{{ timer.warehouses.count }} {% trans "warehouse" %}{% if timer.warehouses.count > 1 %}s{% endif %}</div>
                                {% for warehouse in timer.warehouses.all|slice:":2" %}
                                    <div class="text-xs text-gray-500">{{ warehouse.name }}</div>
                                {% endfor %}
                                {% if timer.warehouses.count > 2 %}
                                    <div class="text-xs text-gray-400">{% trans "and" %} {{ timer.warehouses.count|add:"-2" }} {% trans "more" %}</div>
                                {% endif %}
                            {% else %}
                                <span class="text-gray-400">{% trans "No warehouses" %}</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex flex-col gap-1">
                                {% if timer.is_active %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {% trans "Active" %}
                                    </span>
                                    {% if timer.is_active_now %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                            {% trans "Running" %}
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {% trans "Inactive" %}
                                    </span>
                                {% endif %}
                                {% if timer.blocks_operations %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        {% trans "Blocks Ops" %}
                                    </span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center gap-2">
                                <!-- View -->
                                <a href="{% url 'warehouse:timer_detail' timer.pk %}" 
                                   class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                                   title="{% trans 'View Timer' %}">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </a>
                                
                                <!-- Edit -->
                                <a href="{% url 'warehouse:timer_update' timer.pk %}" 
                                   class="text-amber-600 hover:text-amber-900 transition-colors duration-200"
                                   title="{% trans 'Edit Timer' %}">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                </a>
                                
                                <!-- Toggle -->
                                <a href="{% url 'warehouse:timer_toggle' timer.pk %}" 
                                   class="{% if timer.is_active %}text-red-600 hover:text-red-900{% else %}text-green-600 hover:text-green-900{% endif %} transition-colors duration-200"
                                   title="{% if timer.is_active %}{% trans 'Deactivate Timer' %}{% else %}{% trans 'Activate Timer' %}{% endif %}">
                                    {% if timer.is_active %}
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    {% else %}
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    {% endif %}
                                </a>
                                
                                <!-- Delete -->
                                <a href="{% url 'warehouse:timer_delete' timer.pk %}" 
                                   class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                   title="{% trans 'Delete Timer' %}">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
            {% include 'components/pagination.html' with page_obj=page_obj %}
        </div>
        {% endif %}
        
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <h3 class="mt-4 text-lg font-medium text-gray-900">{% trans "No Timers Found" %}</h3>
            <p class="mt-2 text-gray-500">{% trans "No warehouse timers match your current filters." %}</p>
            <div class="mt-6">
                <a href="{% url 'warehouse:timer_create' %}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 inline-flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "Create First Timer" %}
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Auto-refresh script for real-time timer status -->
<script>
// Auto-refresh timer status every 30 seconds
setInterval(function() {
    // Check for running timers and update status indicators
    fetch('{% url "warehouse:api_timers_status" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update running timers count if needed
                const runningCount = data.timers.filter(timer => timer.is_currently_running).length;
                // You can update the UI here if needed
            }
        })
        .catch(error => console.log('Error refreshing timer status:', error));
}, 30000);
</script>
{% endblock %} 