from django.urls import path
from . import views

app_name = 'warehouse'
 
urlpatterns = [
    # Dashboard
    path('', views.warehouse_dashboard, name='dashboard'),
    path('dashboard/', views.warehouse_dashboard, name='dashboard'),
    
    # Location Types
    path('location-types/', views.LocationTypeListView.as_view(), name='location_type_list'),
    path('location-types/create/', views.LocationTypeCreateView.as_view(), name='location_type_create'),
    path('location-types/<uuid:pk>/', views.LocationTypeDetailView.as_view(), name='location_type_detail'),
    path('location-types/<uuid:pk>/edit/', views.LocationTypeUpdateView.as_view(), name='location_type_update'),
    
    # Locations
    path('locations/', views.LocationListView.as_view(), name='location_list'),
    path('locations/create/', views.LocationCreateView.as_view(), name='location_create'),
    path('locations/<uuid:pk>/', views.LocationDetailView.as_view(), name='location_detail'),
    path('locations/<uuid:pk>/edit/', views.LocationUpdateView.as_view(), name='location_update'),
    
    # Transfer Orders
    path('transfer-orders/', views.TransferOrderListView.as_view(), name='transfer_order_list'),
    path('transfer-orders/create/', views.TransferOrderCreateView.as_view(), name='transfer_order_create'),
    path('transfer-orders/<uuid:pk>/', views.TransferOrderDetailView.as_view(), name='transfer_order_detail'),
    
    # Item Locations
    path('item-locations/', views.ItemLocationListView.as_view(), name='item_location_list'),
    path('item-locations/create/', views.ItemLocationCreateView.as_view(), name='item_location_create'),
    
    # Warehouse Timers
    path('timers/', views.WarehouseTimerListView.as_view(), name='timer_list'),
    path('timers/create/', views.WarehouseTimerCreateView.as_view(), name='timer_create'),
    path('timers/<uuid:pk>/', views.WarehouseTimerDetailView.as_view(), name='timer_detail'),
    path('timers/<uuid:pk>/edit/', views.WarehouseTimerUpdateView.as_view(), name='timer_update'),
    path('timers/<uuid:pk>/delete/', views.WarehouseTimerDeleteView.as_view(), name='timer_delete'),
    path('timers/<uuid:pk>/toggle/', views.warehouse_timer_toggle, name='timer_toggle'),
    
    # Reports
    path('reports/', views.warehouse_reports, name='reports'),
    
    # API Endpoints
    path('api/location/<uuid:location_id>/items/', views.api_location_items, name='api_location_items'),
    path('api/transfer-order-stats/', views.api_transfer_order_stats, name='api_transfer_order_stats'),
    path('api/locations/', views.api_locations_list, name='api_locations_list'),
    path('api/timers/status/', views.api_warehouse_timers_status, name='api_timers_status'),
    path('api/check-operation-allowed/', views.api_check_warehouse_operation_allowed, name='api_check_operation_allowed'),
    
    # API Aliases for modal compatibility
    path('api/transfer/create/', views.TransferOrderCreateView.as_view(), name='transfer_create'),
    path('api/location/create/', views.LocationCreateView.as_view(), name='location_create'),
    path('api/timer/create/', views.WarehouseTimerCreateView.as_view(), name='timer_create'),
] 