from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, F, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from core.views import TenantCreateView, TenantListView, TenantDetailView, TenantUpdateView
import json
from decimal import Decimal

# Import models
from .models import Location, LocationType, BinLocation, ItemLocation, TransferOrder, TransferOrderItem, Transfer, WarehouseTimer, WarehouseTimerLog
from inventory.models import Item, Movement
from setup.models import Customer

# Import forms
from .forms import LocationForm, LocationTypeForm, TransferOrderForm, TransferOrderItemForm, ItemLocationForm, WarehouseReportForm, WarehouseTimerForm, WarehouseTimerFilterForm

# Import utilities
from core.middleware import get_current_tenant_id

@login_required
def warehouse_dashboard(request):
    """Dashboard for warehouse module"""
    
    # Get tenant_id properly
    tenant_id = get_current_tenant_id()
    
    # Get date ranges
    today = timezone.now().date()
    this_month_start = today.replace(day=1)
    
    # Basic statistics
    total_locations = Location.objects.filter(tenant_id=tenant_id).count()
    total_bin_locations = BinLocation.objects.filter(tenant_id=tenant_id).count()
    
    # Transfer statistics
    pending_transfers = TransferOrder.objects.filter(
        tenant_id=tenant_id,
        status='pending'
    ).count()
    
    total_transfers = TransferOrder.objects.filter(tenant_id=tenant_id).count()
    
    transfers_this_month = TransferOrder.objects.filter(
        tenant_id=tenant_id,
        created_at__gte=this_month_start
    ).count()
    
    # Item location statistics
    total_item_locations = ItemLocation.objects.filter(
        tenant_id=tenant_id
    ).count()
    
    # Low stock alerts - items with stock below minimum
    low_stock_items_query = ItemLocation.objects.filter(
        tenant_id=tenant_id,
        quantity__lte=F('min_stock'),
        min_stock__isnull=False
    ).select_related('item', 'location')
    
    low_stock_count = low_stock_items_query.count()
    
    # Recent activities
    recent_transfers = TransferOrder.objects.filter(
        tenant_id=tenant_id
    ).select_related('source_location', 'destination_location').order_by('-created_at')[:10]
    
    # Get locations for display
    locations = Location.objects.filter(
        tenant_id=tenant_id,
        is_active=True
    ).select_related('location_type').order_by('name')[:10]
    
    # Calculate storage utilization
    locations_with_items = ItemLocation.objects.filter(
        tenant_id=tenant_id
    ).values('location').distinct().count()
    used_locations = locations_with_items
    
    # Location utilization
    all_locations = Location.objects.filter(tenant_id=tenant_id)
    location_stats = []
    for location in all_locations:
        items_count = ItemLocation.objects.filter(
            tenant_id=tenant_id,
            location=location
        ).count()
        
        total_quantity = ItemLocation.objects.filter(
            tenant_id=tenant_id,
            location=location
        ).aggregate(total=Sum('quantity'))['total'] or 0
        
        # Calculate utilization based on max_items if available
        utilization = 0
        if location.max_items and location.max_items > 0:
            utilization = min((items_count / location.max_items) * 100, 100)
        
        location_stats.append({
            'location': location,
            'items_count': items_count,
            'total_quantity': total_quantity,
            'utilization': utilization,
        })
    
    context = {
        'page_title': _('Warehouse Dashboard'),
        'page_subtitle': _('Warehouse and Inventory Management'),
        
        # Basic stats - template compatibility
        'locations_count': total_locations,
        'bin_locations_count': total_bin_locations,
        'transfers_count': total_transfers,
        'storage_utilization': int((used_locations / max(total_locations, 1)) * 100),
        
        # Original stats
        'total_locations': total_locations,
        'total_bin_locations': total_bin_locations,
        'pending_transfers': pending_transfers,
        'low_stock_items_count': low_stock_count,
        'total_item_locations': total_item_locations,
        
        # Monthly stats
        'transfers_this_month': transfers_this_month,
        
        # Data for template display
        'locations': locations,
        'transfers': recent_transfers,
        'low_stock_items': low_stock_items_query[:10],
        
        # Recent activities
        'recent_transfers': recent_transfers,
        
        # Location statistics
        'location_stats': location_stats[:10],  # Limit to top 10
    }
    
    return render(request, 'warehouse/dashboard.html', context)

# ==================== LOCATION TYPE MANAGEMENT ====================

class LocationTypeListView(TenantListView):
    """List view for location types"""
    model = LocationType
    template_name = 'warehouse/location_type_list.html'
    context_object_name = 'location_types'
    paginate_by = 20

class LocationTypeCreateView(TenantCreateView):
    """Create view for new location types"""
    model = LocationType
    form_class = LocationTypeForm
    template_name = 'warehouse/location_type_form.html'
    success_url = reverse_lazy('warehouse:location_type_list')

class LocationTypeDetailView(TenantDetailView):
    """Detail view for individual location type"""
    model = LocationType
    template_name = 'warehouse/location_type_detail.html'
    context_object_name = 'location_type'

class LocationTypeUpdateView(TenantUpdateView):
    """Update view for location types"""
    model = LocationType
    form_class = LocationTypeForm
    template_name = 'warehouse/location_type_form.html'
    success_url = reverse_lazy('warehouse:location_type_list')

# ==================== LOCATION MANAGEMENT ====================

class LocationListView(TenantListView):
    """List view for locations"""
    model = Location
    template_name = 'warehouse/location_list.html'
    context_object_name = 'locations'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('location_type', 'parent')
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(code__icontains=search_query) |
                Q(city__icontains=search_query)
            )
        
        # Location type filter
        location_type_id = self.request.GET.get('location_type', '')
        if location_type_id:
            queryset = queryset.filter(location_type_id=location_type_id)
        
        return queryset.order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get tenant_id properly
        tenant_id = get_current_tenant_id()
        
        # Add item counts for each location
        locations = context['locations']
        for location in locations:
            location.items_count = ItemLocation.objects.filter(
                location=location,
                tenant_id=tenant_id
            ).count()
        
        context.update({
            'page_title': _('Locations'),
            'page_subtitle': _('Warehouse Location Management'),
            'search_query': self.request.GET.get('search', ''),
            'location_types': LocationType.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ).order_by('name'),
            'current_location_type': self.request.GET.get('location_type', ''),
        })
        return context

class LocationDetailView(TenantDetailView):
    """Detail view for individual location"""
    model = Location
    template_name = 'warehouse/location_detail.html'
    context_object_name = 'location'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        location = self.get_object()
        
        # Get tenant_id properly
        tenant_id = get_current_tenant_id()
        
        # Get items in this location
        item_locations = ItemLocation.objects.filter(
            location=location,
            tenant_id=tenant_id
        ).select_related('item', 'bin_location').order_by('item__name')
        
        # Get child locations
        child_locations = Location.objects.filter(
            parent=location,
            tenant_id=tenant_id
        ).order_by('name')
        
        # Get bin locations
        bin_locations = BinLocation.objects.filter(
            location=location,
            tenant_id=tenant_id
        ).order_by('name')
        
        # Get recent transfer orders involving this location
        recent_transfers_in = TransferOrder.objects.filter(
            destination_location=location,
            tenant_id=tenant_id
        ).order_by('-created_at')[:10]
        
        recent_transfers_out = TransferOrder.objects.filter(
            source_location=location,
            tenant_id=tenant_id
        ).order_by('-created_at')[:10]
        
        context.update({
            'page_title': location.name,
            'page_subtitle': _('Location Details'),
            'item_locations': item_locations,
            'child_locations': child_locations,
            'bin_locations': bin_locations,
            'recent_transfers_in': recent_transfers_in,
            'recent_transfers_out': recent_transfers_out,
        })
        return context

class LocationCreateView(TenantCreateView):
    """Create view for new locations"""
    model = Location
    form_class = LocationForm
    template_name = 'warehouse/location_form.html'
    success_url = reverse_lazy('warehouse:location_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

class LocationUpdateView(TenantUpdateView):
    """Update view for locations"""
    model = Location
    form_class = LocationForm
    template_name = 'warehouse/location_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_success_url(self):
        return reverse('warehouse:location_detail', kwargs={'pk': self.object.pk})

# ==================== TRANSFER ORDER MANAGEMENT ====================

class TransferOrderListView(TenantListView):
    """List view for transfer orders"""
    model = TransferOrder
    template_name = 'warehouse/transfer_order_list.html'
    context_object_name = 'transfer_orders'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related(
            'source_location', 'destination_location'
        )
        
        # Status filter
        status = self.request.GET.get('status', '')
        if status:
            queryset = queryset.filter(status=status)
        
        # Location filter
        location_id = self.request.GET.get('location', '')
        if location_id:
            queryset = queryset.filter(
                Q(source_location_id=location_id) |
                Q(destination_location_id=location_id)
            )
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('Transfer Orders'),
            'page_subtitle': _('Stock Transfer Management'),
            'locations': Location.objects.filter(
                tenant_id=get_current_tenant_id(),
                is_active=True
            ).order_by('name'),
            'current_filters': {
                'status': self.request.GET.get('status', ''),
                'location': self.request.GET.get('location', ''),
            },
            'status_choices': TransferOrder.STATUS_CHOICES,
        })
        return context

class TransferOrderCreateView(TenantCreateView):
    """Create view for new transfer orders"""
    model = TransferOrder
    form_class = TransferOrderForm
    template_name = 'warehouse/transfer_order_form.html'
    success_url = reverse_lazy('warehouse:transfer_order_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = get_current_tenant_id()
        return kwargs

class TransferOrderDetailView(TenantDetailView):
    """Detail view for transfer orders"""
    model = TransferOrder
    template_name = 'warehouse/transfer_order_detail.html'
    context_object_name = 'transfer_order'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        transfer_order = self.get_object()
        
        # Get tenant_id properly
        tenant_id = get_current_tenant_id()
        
        # Get transfer order items
        transfer_items = transfer_order.transfer_items.filter(
            tenant_id=tenant_id
        ).select_related('item')
        
        context.update({
            'transfer_items': transfer_items,
        })
        return context

# ==================== ITEM LOCATION MANAGEMENT ====================

class ItemLocationListView(TenantListView):
    """List view for item locations"""
    model = ItemLocation
    template_name = 'warehouse/item_location_list.html'
    context_object_name = 'item_locations'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('item', 'location', 'bin_location')
        
        # Location filter
        location_id = self.request.GET.get('location', '')
        if location_id:
            queryset = queryset.filter(location_id=location_id)
        
        # Item search
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(item__name__icontains=search_query)
        
        # Low stock filter
        low_stock = self.request.GET.get('low_stock', '')
        if low_stock == 'true':
            queryset = queryset.filter(
                quantity__lte=F('min_stock'),
                min_stock__isnull=False
            )
        
        return queryset.order_by('location__name', 'item__name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'page_title': _('Item Locations'),
            'page_subtitle': _('Item Stock by Location'),
            'locations': Location.objects.filter(
                tenant_id=get_current_tenant_id(),
                is_active=True
            ).order_by('name'),
            'current_filters': {
                'location': self.request.GET.get('location', ''),
                'search': self.request.GET.get('search', ''),
                'low_stock': self.request.GET.get('low_stock', ''),
            },
        })
        return context

class ItemLocationCreateView(TenantCreateView):
    """Create view for new item locations"""
    model = ItemLocation
    form_class = ItemLocationForm
    template_name = 'warehouse/item_location_form.html'
    success_url = reverse_lazy('warehouse:item_location_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['tenant_id'] = get_current_tenant_id()
        return kwargs

# ==================== REPORT VIEWS ====================

@login_required
def warehouse_reports(request):
    """Warehouse reports page"""
    form = WarehouseReportForm(user=request.user)
    
    if request.method == 'POST':
        form = WarehouseReportForm(request.POST, user=request.user)
        if form.is_valid():
            # Process report generation
            report_type = form.cleaned_data['report_type']
            location = form.cleaned_data.get('location')
            date_from = form.cleaned_data.get('date_from')
            date_to = form.cleaned_data.get('date_to')
            
            # Get tenant_id properly
            tenant_id = get_current_tenant_id()
            
            # Generate report data based on type
            context = generate_warehouse_report(
                tenant_id, report_type, 
                location, date_from, date_to
            )
            context['form'] = form
            context['report_generated'] = True
            
            return render(request, 'warehouse/reports.html', context)
    
    return render(request, 'warehouse/reports.html', {'form': form})

def generate_warehouse_report(tenant_id, report_type, location=None, date_from=None, date_to=None):
    """Generate warehouse report data"""
    
    context = {'report_type': report_type}
    
    if report_type == 'location_summary':
        locations = Location.objects.filter(tenant_id=tenant_id)
        if location:
            locations = locations.filter(id=location.id)
        
        location_data = []
        for loc in locations:
            item_count = ItemLocation.objects.filter(
                tenant_id=tenant_id, location=loc
            ).count()
            
            total_quantity = ItemLocation.objects.filter(
                tenant_id=tenant_id, location=loc
            ).aggregate(total=Sum('quantity'))['total'] or 0
            
            location_data.append({
                'location': loc,
                'item_count': item_count,
                'total_quantity': total_quantity,
            })
        
        context.update({
            'locations': location_data,
        })
    
    elif report_type == 'transfer_summary':
        transfers = TransferOrder.objects.filter(tenant_id=tenant_id)
        
        if location:
            transfers = transfers.filter(
                Q(source_location=location) | Q(destination_location=location)
            )
        
        if date_from:
            transfers = transfers.filter(created_at__gte=date_from)
        if date_to:
            transfers = transfers.filter(created_at__lte=date_to)
        
        context.update({
            'transfers': transfers.select_related('source_location', 'destination_location'),
            'total_transfers': transfers.count(),
        })
    
    elif report_type == 'stock_levels':
        item_locations = ItemLocation.objects.filter(tenant_id=tenant_id)
        if location:
            item_locations = item_locations.filter(location=location)
        
        context.update({
            'item_locations': item_locations.select_related('item', 'location'),
            'low_stock_items': item_locations.filter(
                quantity__lte=F('min_stock'),
                min_stock__isnull=False
            ).count(),
        })
    
    return context

# ==================== API VIEWS ====================

@login_required
def api_location_items(request, location_id):
    """API endpoint to get items in a specific location"""
    try:
        # Get tenant_id properly
        tenant_id = get_current_tenant_id()
        
        location = Location.objects.get(id=location_id, tenant_id=tenant_id)
        item_locations = ItemLocation.objects.filter(
            location=location,
            tenant_id=tenant_id
        ).select_related('item')
        
        items_data = []
        for item_loc in item_locations:
            items_data.append({
                'id': str(item_loc.item.id),
                'name': item_loc.item.name,
                'sku': item_loc.item.sku,
                'quantity': float(item_loc.quantity),
                'min_stock': float(item_loc.min_stock) if item_loc.min_stock else None,
                'max_stock': float(item_loc.max_stock) if item_loc.max_stock else None,
                'is_low_stock': item_loc.is_low_stock,
            })
        
        return JsonResponse({
            'success': True,
            'location': {
                'id': str(location.id),
                'name': location.name,
                'code': location.code,
            },
            'items': items_data
        })
        
    except Location.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Location not found'}, status=404)

@login_required
def api_transfer_order_stats(request):
    """API endpoint for transfer order statistics"""
    tenant_id = get_current_tenant_id()
    
    if not tenant_id:
        return JsonResponse({'error': 'No tenant access'}, status=403)
    
    # Get transfer order statistics
    total_transfers = TransferOrder.objects.filter(tenant_id=tenant_id).count()
    
    pending_transfers = TransferOrder.objects.filter(
        tenant_id=tenant_id,
        status='pending'
    ).count()
    
    completed_transfers = TransferOrder.objects.filter(
        tenant_id=tenant_id,
        status='completed'
    ).count()
    
    in_transit_transfers = TransferOrder.objects.filter(
        tenant_id=tenant_id,
        status='in_transit'
    ).count()
    
    # Get monthly transfers
    today = timezone.now().date()
    this_month_start = today.replace(day=1)
    
    transfers_this_month = TransferOrder.objects.filter(
        tenant_id=tenant_id,
        created_at__gte=this_month_start
    ).count()
    
    return JsonResponse({
        'total_transfers': total_transfers,
        'pending_transfers': pending_transfers,
        'completed_transfers': completed_transfers,
        'in_transit_transfers': in_transit_transfers,
        'transfers_this_month': transfers_this_month,
    })

@login_required
def api_locations_list(request):
    """API endpoint to get list of locations for AJAX calls"""
    tenant_id = get_current_tenant_id()
    
    locations = Location.objects.filter(
        tenant_id=tenant_id,
        is_active=True
    ).values('id', 'name', 'code')
    
    return JsonResponse({
        'locations': list(locations)
    })

# ==================== WAREHOUSE TIMER MANAGEMENT ====================

class WarehouseTimerListView(TenantListView):
    """List view for warehouse timers with role-based filtering"""
    model = WarehouseTimer
    template_name = 'warehouse/timer_list.html'
    context_object_name = 'timers'
    paginate_by = 20
    
    def get_queryset(self):
        """Get timers visible to current user based on their role"""
        queryset = WarehouseTimer.get_visible_timers_for_user(self.request.user)
        
        # Apply additional filters from form
        filter_form = WarehouseTimerFilterForm(self.request.GET, user=self.request.user)
        if filter_form.is_valid():
            timer_type = filter_form.cleaned_data.get('timer_type')
            visibility_level = filter_form.cleaned_data.get('visibility_level')
            warehouse = filter_form.cleaned_data.get('warehouse')
            is_active = filter_form.cleaned_data.get('is_active')
            
            if timer_type:
                queryset = queryset.filter(timer_type=timer_type)
            if visibility_level:
                queryset = queryset.filter(visibility_level=visibility_level)
            if warehouse:
                queryset = queryset.filter(warehouses=warehouse)
            if is_active:
                queryset = queryset.filter(is_active=(is_active == 'true'))
        
        # Search functionality
        search_query = self.request.GET.get('search', '')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )
        
        return queryset.select_related('franchise', 'company', 'service_center').prefetch_related('warehouses').order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_form'] = WarehouseTimerFilterForm(self.request.GET, user=self.request.user)
        context['search_query'] = self.request.GET.get('search', '')
        
        # Get current active timers count
        context['active_timers_count'] = self.get_queryset().filter(is_active=True).count()
        
        # Get currently running timers
        running_timers = []
        for timer in self.get_queryset().filter(is_active=True):
            if timer.is_active_now():
                running_timers.append(timer)
        context['running_timers'] = running_timers
        
        return context


class WarehouseTimerCreateView(TenantCreateView):
    """Create view for new warehouse timers"""
    model = WarehouseTimer
    form_class = WarehouseTimerForm
    template_name = 'warehouse/timer_form.html'
    success_url = reverse_lazy('warehouse:timer_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, _('Warehouse timer created successfully.'))
        return super().form_valid(form)


class WarehouseTimerDetailView(TenantDetailView):
    """Detail view for warehouse timers"""
    model = WarehouseTimer
    template_name = 'warehouse/timer_detail.html'
    context_object_name = 'timer'
    
    def get_queryset(self):
        """Ensure user can only see timers they have access to"""
        return WarehouseTimer.get_visible_timers_for_user(self.request.user)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get timer logs
        context['recent_logs'] = WarehouseTimerLog.objects.filter(
            timer=self.object
        ).select_related('affected_warehouse', 'user').order_by('-created_at')[:10]
        
        # Check if timer is currently active
        context['is_currently_active'] = self.object.is_active_now()
        
        # Get next occurrence
        context['next_occurrence'] = self.object.get_next_occurrence()
        
        # Get affected warehouses
        context['affected_warehouses'] = self.object.warehouses.all()
        
        return context


class WarehouseTimerUpdateView(TenantUpdateView):
    """Update view for warehouse timers"""
    model = WarehouseTimer
    form_class = WarehouseTimerForm
    template_name = 'warehouse/timer_form.html'
    
    def get_queryset(self):
        """Ensure user can only update timers they have access to"""
        return WarehouseTimer.get_visible_timers_for_user(self.request.user)
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_success_url(self):
        return reverse('warehouse:timer_detail', kwargs={'pk': self.object.pk})
    
    def form_valid(self, form):
        messages.success(self.request, _('Warehouse timer updated successfully.'))
        return super().form_valid(form)


class WarehouseTimerDeleteView(DeleteView):
    """Delete view for warehouse timers"""
    model = WarehouseTimer
    template_name = 'warehouse/timer_confirm_delete.html'
    success_url = reverse_lazy('warehouse:timer_list')
    
    def get_queryset(self):
        """Ensure user can only delete timers they have access to"""
        return WarehouseTimer.get_visible_timers_for_user(self.request.user)
    
    def delete(self, request, *args, **kwargs):
        messages.success(self.request, _('Warehouse timer deleted successfully.'))
        return super().delete(request, *args, **kwargs)


@login_required
def warehouse_timer_toggle(request, pk):
    """Toggle timer active status"""
    timer = get_object_or_404(WarehouseTimer, pk=pk)
    
    # Check if user has access to this timer
    accessible_timers = WarehouseTimer.get_visible_timers_for_user(request.user)
    if not accessible_timers.filter(pk=timer.pk).exists():
        messages.error(request, _('You do not have permission to modify this timer.'))
        return redirect('warehouse:timer_list')
    
    # Toggle status
    timer.is_active = not timer.is_active
    timer.save()
    
    # Log the action
    WarehouseTimerLog.objects.create(
        timer=timer,
        action='activated' if timer.is_active else 'deactivated',
        description=f'Timer {"activated" if timer.is_active else "deactivated"} by {request.user.username}',
        user=request.user,
        tenant_id=get_current_tenant_id()
    )
    
    status_text = _('activated') if timer.is_active else _('deactivated')
    messages.success(request, _('Timer {name} has been {status}.').format(
        name=timer.name, status=status_text
    ))
    
    return redirect('warehouse:timer_detail', pk=timer.pk)


@login_required
def api_warehouse_timers_status(request):
    """API endpoint to get current status of warehouse timers"""
    tenant_id = get_current_tenant_id()
    
    # Get timers visible to user
    timers = WarehouseTimer.get_visible_timers_for_user(request.user)
    
    # Get warehouse filter if provided
    warehouse_id = request.GET.get('warehouse_id')
    if warehouse_id:
        timers = timers.filter(warehouses__id=warehouse_id)
    
    timer_data = []
    for timer in timers.filter(is_active=True):
        is_currently_active = timer.is_active_now()
        next_occurrence = timer.get_next_occurrence()
        
        timer_data.append({
            'id': str(timer.id),
            'name': timer.name,
            'timer_type': timer.timer_type,
            'visibility_level': timer.visibility_level,
            'is_active': timer.is_active,
            'is_currently_running': is_currently_active,
            'blocks_operations': timer.blocks_operations,
            'start_time': timer.start_time.strftime('%H:%M'),
            'end_time': timer.end_time.strftime('%H:%M'),
            'next_occurrence': next_occurrence.isoformat() if next_occurrence else None,
            'warehouses': [{'id': str(w.id), 'name': w.name} for w in timer.warehouses.all()],
            'organization': {
                'franchise': timer.franchise.name if timer.franchise else None,
                'company': timer.company.name if timer.company else None,
                'service_center': timer.service_center.name if timer.service_center else None,
            }
        })
    
    return JsonResponse({
        'timers': timer_data,
        'success': True,
        'timestamp': timezone.now().isoformat()
    })


@login_required
def api_check_warehouse_operation_allowed(request):
    """API endpoint to check if warehouse operations are allowed"""
    tenant_id = get_current_tenant_id()
    warehouse_id = request.GET.get('warehouse_id')
    
    if not warehouse_id:
        return JsonResponse({'error': 'Warehouse ID required'}, status=400)
    
    try:
        warehouse = Location.objects.get(id=warehouse_id, tenant_id=tenant_id)
    except Location.DoesNotExist:
        return JsonResponse({'error': 'Warehouse not found'}, status=404)
    
    # Get timers affecting this warehouse
    blocking_timers = WarehouseTimer.get_visible_timers_for_user(request.user).filter(
        warehouses=warehouse,
        is_active=True,
        blocks_operations=True
    )
    
    # Check if any timer is currently blocking operations
    currently_blocked = False
    blocking_timer_info = None
    
    for timer in blocking_timers:
        if timer.is_active_now():
            currently_blocked = True
            blocking_timer_info = {
                'id': str(timer.id),
                'name': timer.name,
                'timer_type': timer.timer_type,
                'end_time': timer.end_time.strftime('%H:%M'),
                'description': timer.description
            }
            break
    
    return JsonResponse({
        'warehouse_id': warehouse_id,
        'warehouse_name': warehouse.name,
        'operations_allowed': not currently_blocked,
        'blocked_by_timer': blocking_timer_info,
        'success': True,
        'timestamp': timezone.now().isoformat()
    })
