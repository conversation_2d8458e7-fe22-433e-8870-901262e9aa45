from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    WorkOrder, WorkOrderType, WorkOrderOperation, WorkOrderMaterial,
    BillOfMaterials, BOMItem, MaintenanceSchedule, ScheduleOperation, OperationPart,
    WorkOrderHistory, WorkOrderTransferRequest, WorkOrderAllocation, 
    WorkOrderQualityCheck, WorkOrderActionLog, WorkOrderTemplate
    )
from core.admin import TenantAdminMixin

class WorkOrderHistoryInline(admin.TabularInline):
    model = WorkOrderHistory
    extra = 0
    readonly_fields = ('created_at', 'user', 'action_type', 'description', 'previous_value', 'new_value')
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False

class WorkOrderOperationInline(admin.TabularInline):
    model = WorkOrderOperation
    extra = 1

class WorkOrderMaterialInline(admin.TabularInline):
    model = WorkOrderMaterial
    extra = 1

@admin.register(WorkOrder)
class WorkOrderAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('work_order_number', 'customer_display', 'status', 'priority', 'planned_start_date')
    list_filter = ('status', 'priority', 'operation_category', 'service_center')
    search_fields = ('work_order_number', 'description', 'customer_name', 'customer__first_name', 'customer__last_name', 'vehicle__license_plate')
    readonly_fields = ('created_at', 'updated_at')
    exclude = ('tenant_id',)
    inlines = [WorkOrderOperationInline, WorkOrderMaterialInline, WorkOrderHistoryInline]
    fieldsets = (
        (None, {
            'fields': ('work_order_number', 'description', 'operation_category', 'maintenance_schedule')
        }),
        (_('Status and Priority'), {
            'fields': ('status', 'priority')
        }),
        (_('Customer Information'), {
            'fields': ('customer', ('customer_name', 'customer_phone'), 'customer_email', 'warranty_status')
        }),
        (_('Vehicle Information'), {
            'fields': ('service_center', 'vehicle', 'current_odometer', 'fuel_level', 'service_item_serial')
        }),
        (_('Scheduling'), {
            'fields': ('planned_start_date', 'planned_end_date', 'actual_start_date', 'actual_end_date')
        }),
        (_('Financial'), {
            'fields': ('estimated_cost', 'actual_cost')
        }),
        (_('Other'), {
            'fields': ('bill_of_materials', 'notes', 'attributes')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def customer_display(self, obj):
        """Display the customer name, prioritizing the Customer model if available"""
        if obj.customer:
            return obj.customer.full_name
        return obj.customer_name
    customer_display.short_description = _("Customer")
    
    def save_model(self, request, obj, form, change):
        """Auto-assign customer from vehicle if not set"""
        super().save_model(request, obj, form, change)
        if not change:  # Only for new objects
            obj.auto_assign_customer_from_vehicle()
            if obj.customer and not obj.customer_name:
                obj.customer_name = obj.customer.full_name
                obj.save()

@admin.register(WorkOrderType)
class WorkOrderTypeAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name', 'description')
    list_filter = ('is_active',)

class ScheduleOperationInline(admin.TabularInline):
    model = ScheduleOperation
    extra = 1

@admin.register(MaintenanceSchedule)
class MaintenanceScheduleAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'interval_type', 'mileage_interval', 'time_interval_months', 'vehicle_make', 'vehicle_model', 'is_active')
    list_filter = ('interval_type', 'is_active', 'vehicle_make')
    search_fields = ('name', 'description', 'vehicle_make', 'vehicle_model')
    inlines = [ScheduleOperationInline]
    exclude = ('tenant_id',)
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'is_active')
        }),
        (_('Interval Settings'), {
            'fields': ('interval_type', 'mileage_interval', 'time_interval_months')
        }),
        (_('Vehicle Compatibility'), {
            'fields': ('vehicle_make', 'vehicle_model', 'year_from', 'year_to')
        }),
    )

class OperationPartInline(admin.TabularInline):
    model = OperationPart
    extra = 1
    
@admin.register(ScheduleOperation)
class ScheduleOperationAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'maintenance_schedule', 'duration_minutes', 'sequence', 'is_required')
    list_filter = ('maintenance_schedule', 'is_required')
    search_fields = ('name', 'description', 'maintenance_schedule__name')
    inlines = [OperationPartInline]

class BOMItemInline(admin.TabularInline):
    model = BOMItem
    extra = 1

@admin.register(BillOfMaterials)
class BillOfMaterialsAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'version', 'finished_item', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description', 'finished_item__name')
    inlines = [BOMItemInline]

@admin.register(WorkOrderOperation)
class WorkOrderOperationAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'work_order', 'sequence', 'duration_minutes', 'is_completed')
    list_filter = ('is_completed',)
    search_fields = ('name', 'description', 'work_order__work_order_number')

@admin.register(WorkOrderMaterial)
class WorkOrderMaterialAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('item', 'work_order', 'quantity', 'unit_of_measure', 'is_consumed')
    list_filter = ('is_consumed',)
    search_fields = ('item__name', 'item__sku', 'work_order__work_order_number', 'notes')

class WorkOrderHistoryAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('work_order', 'action_type', 'description', 'created_at', 'user')
    list_filter = ('action_type', 'created_at')
    search_fields = ('work_order__work_order_number', 'description', 'user__username')
    readonly_fields = ('created_at', 'updated_at')
    exclude = ('tenant_id',)
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False

admin.site.register(WorkOrderHistory, WorkOrderHistoryAdmin)


@admin.register(WorkOrderTransferRequest)
class WorkOrderTransferRequestAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('transfer_number', 'work_order', 'item', 'source_warehouse', 'destination_warehouse', 'status', 'priority')
    list_filter = ('status', 'priority', 'source_warehouse', 'destination_warehouse')
    search_fields = ('transfer_number', 'work_order__work_order_number', 'item__name', 'item__sku')
    readonly_fields = ('created_at', 'updated_at', 'approved_at', 'completed_at')
    exclude = ('tenant_id',)
    fieldsets = (
        (None, {
            'fields': ('transfer_number', 'work_order', 'item', 'status', 'priority')
        }),
        (_('Warehouse Information'), {
            'fields': ('source_warehouse', 'destination_warehouse')
        }),
        (_('Quantity Information'), {
            'fields': ('requested_quantity', 'transferred_quantity')
        }),
        (_('Approval Information'), {
            'fields': ('requested_by', 'approved_by', 'approved_at')
        }),
        (_('Delivery Information'), {
            'fields': ('estimated_delivery_date', 'actual_delivery_date', 'completed_at')
        }),
        (_('Notes'), {
            'fields': ('notes', 'rejection_reason')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(WorkOrderAllocation)
class WorkOrderAllocationAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('work_order', 'item', 'warehouse', 'allocated_quantity', 'consumed_quantity', 'status')
    list_filter = ('status', 'warehouse', 'allocated_at')
    search_fields = ('work_order__work_order_number', 'item__name', 'item__sku')
    readonly_fields = ('created_at', 'updated_at', 'allocated_at', 'consumed_at')
    exclude = ('tenant_id',)
    fieldsets = (
        (None, {
            'fields': ('work_order', 'work_order_material', 'item', 'warehouse', 'status')
        }),
        (_('Batch Information'), {
            'fields': ('item_batch',)
        }),
        (_('Quantity Information'), {
            'fields': ('allocated_quantity', 'consumed_quantity')
        }),
        (_('Tracking Information'), {
            'fields': ('allocated_by', 'allocated_at', 'consumed_at', 'expires_at')
        }),
        (_('Notes'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(WorkOrderQualityCheck)
class WorkOrderQualityCheckAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('check_name', 'work_order', 'check_type', 'result', 'score', 'inspector', 'checked_at')
    list_filter = ('check_type', 'result', 'is_required', 'is_blocking')
    search_fields = ('check_name', 'work_order__work_order_number', 'description')
    readonly_fields = ('created_at', 'updated_at')
    exclude = ('tenant_id',)
    fieldsets = (
        (None, {
            'fields': ('work_order', 'check_type', 'check_name', 'description')
        }),
        (_('Inspector Information'), {
            'fields': ('inspector', 'checked_at', 'due_date')
        }),
        (_('Results'), {
            'fields': ('result', 'score', 'findings', 'recommendations', 'corrective_actions')
        }),
        (_('Settings'), {
            'fields': ('is_required', 'is_blocking')
        }),
        (_('Attachments'), {
            'fields': ('images', 'attachments')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(WorkOrderActionLog)
class WorkOrderActionLogAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('work_order', 'category', 'action', 'user', 'created_at')
    list_filter = ('category', 'created_at', 'user')
    search_fields = ('work_order__work_order_number', 'action', 'description')
    readonly_fields = ('created_at', 'updated_at')
    exclude = ('tenant_id',)
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(WorkOrderTemplate)
class WorkOrderTemplateAdmin(TenantAdminMixin, admin.ModelAdmin):
    list_display = ('name', 'template_type', 'is_active', 'is_public', 'usage_count', 'created_by')
    list_filter = ('template_type', 'is_active', 'is_public', 'created_by')
    search_fields = ('name', 'description', 'category')
    readonly_fields = ('created_at', 'updated_at', 'usage_count', 'last_used_at')
    exclude = ('tenant_id',)
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'template_type', 'category')
        }),
        (_('Vehicle Compatibility'), {
            'fields': ('compatible_makes', 'compatible_models', 'year_from', 'year_to')
        }),
        (_('Configuration'), {
            'fields': ('default_priority', 'estimated_duration_hours', 'estimated_cost')
        }),
        (_('Template Content'), {
            'fields': ('operations_template', 'materials_template', 'quality_checks_template')
        }),
        (_('Settings'), {
            'fields': ('is_active', 'is_public', 'tags')
        }),
        (_('Usage Tracking'), {
            'fields': ('usage_count', 'last_used_at', 'created_by')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
