from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
import json

from inventory.services import SmartSuggestionService, InventoryAllocationService
from inventory.models import Item, ItemBatch
from work_orders.models import WorkOrder, WorkOrderMaterial
from setup.models import Vehicle, Customer


@login_required
@require_http_methods(["GET"])
def api_get_smart_part_suggestions(request):
    """Get smart part suggestions based on operation type and vehicle"""
    try:
        operation_type = request.GET.get('operation_type', '')
        vehicle_id = request.GET.get('vehicle_id', '')
        
        # Get vehicle details if provided
        vehicle_make = None
        vehicle_model = None
        if vehicle_id:
            try:
                vehicle = Vehicle.objects.get(id=vehicle_id)
                vehicle_make = vehicle.make.name if vehicle.make else None
                vehicle_model = vehicle.model.name if vehicle.model else None
            except Vehicle.DoesNotExist:
                pass
        
        # Get suggestions with user context
        service_center = None
        if vehicle:
            service_center = getattr(vehicle, 'service_center', None)
        
        suggestions = SmartSuggestionService.suggest_parts_for_operation(
            operation_type=operation_type,
            vehicle_make=vehicle_make,
            vehicle_model=vehicle_model,
            user=request.user,
            service_center=service_center
        )
        
        return JsonResponse({
            'success': True,
            'suggestions': suggestions,
            'count': len(suggestions)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
def api_validate_stock_allocation(request):
    """Validate if stock can be allocated for requested materials"""
    try:
        data = json.loads(request.body)
        materials = data.get('materials', [])
        tenant_id = getattr(request, 'tenant_id', None)
        
        validation_result = {
            'success': True,
            'items': [],
            'total_available': 0,
            'total_missing': 0,
            'requires_purchase': False
        }
        
        for material_data in materials:
            item_id = material_data.get('item_id')
            required_qty = float(material_data.get('quantity', 0))
            
            try:
                item = Item.objects.get(id=item_id, tenant_id=tenant_id)
                
                # Check batch availability in user's accessible warehouses
                from inventory.services import InventoryAllocationService
                accessible_warehouses = InventoryAllocationService.get_user_accessible_warehouses(request.user)
                
                batch_filter = {
                    'item': item,
                    'status': 'active',
                    'current_quantity__gt': 0,
                    'tenant_id': tenant_id
                }
                
                # Filter by accessible warehouses if user context is provided
                if accessible_warehouses.exists():
                    from warehouse.models import ItemLocation
                    # Get item locations in accessible warehouses
                    accessible_item_locations = ItemLocation.objects.filter(
                        item=item,
                        location__in=accessible_warehouses,
                        quantity__gt=0
                    )
                    
                    if accessible_item_locations.exists():
                        warehouse_batch_ids = []
                        for item_loc in accessible_item_locations:
                            # Get batches in this warehouse location
                            location_batches = ItemBatch.objects.filter(
                                item=item,
                                warehouse_location=item_loc.location.id,
                                status='active',
                                current_quantity__gt=0
                            )
                            warehouse_batch_ids.extend(location_batches.values_list('id', flat=True))
                        
                        if warehouse_batch_ids:
                            batch_filter['id__in'] = warehouse_batch_ids
                        else:
                            # No batches in accessible warehouses
                            batch_filter['id__in'] = []
                
                available_batches = ItemBatch.objects.filter(**batch_filter).order_by('expiry_date', 'received_date')
                
                total_available = sum(batch.available_quantity for batch in available_batches)
                
                item_result = {
                    'item_id': str(item.id),
                    'item_name': item.name,
                    'item_sku': item.sku,
                    'required_quantity': required_qty,
                    'available_quantity': float(total_available),
                    'status': 'available' if total_available >= required_qty else 'shortage',
                    'shortage_amount': max(0, required_qty - total_available),
                    'batches': []
                }
                
                # Add batch details
                for batch in available_batches:
                    item_result['batches'].append({
                        'batch_id': str(batch.id),
                        'batch_number': batch.batch_number,
                        'available_quantity': float(batch.available_quantity),
                        'expiry_date': batch.expiry_date.isoformat() if batch.expiry_date else None,
                        'received_date': batch.received_date.isoformat() if batch.received_date else None
                    })
                
                if total_available >= required_qty:
                    validation_result['total_available'] += 1
                else:
                    validation_result['total_missing'] += 1
                    validation_result['requires_purchase'] = True
                    validation_result['success'] = False
                
                validation_result['items'].append(item_result)
                
            except Item.DoesNotExist:
                validation_result['items'].append({
                    'item_id': item_id,
                    'error': 'Item not found'
                })
                validation_result['success'] = False
        
        return JsonResponse(validation_result)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def api_get_alternative_parts(request):
    """Get alternative parts for a given item"""
    try:
        item_id = request.GET.get('item_id')
        
        if not item_id:
            return JsonResponse({
                'success': False,
                'error': 'Item ID is required'
            }, status=400)
        
        alternatives = SmartSuggestionService.get_alternative_parts(item_id)
        
        return JsonResponse({
            'success': True,
            'alternatives': alternatives,
            'count': len(alternatives)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def api_get_real_time_stock_info(request):
    """Get real-time stock information for an item"""
    try:
        item_id = request.GET.get('item_id')
        tenant_id = getattr(request, 'tenant_id', None)
        
        if not item_id:
            return JsonResponse({
                'success': False,
                'error': 'Item ID is required'
            }, status=400)
        
        try:
            item = Item.objects.get(id=item_id, tenant_id=tenant_id)
            
            # Get batch information
            batches = ItemBatch.objects.filter(
                item=item,
                status='active',
                tenant_id=tenant_id
            ).order_by('expiry_date', 'received_date')
            
            batch_info = []
            total_available = 0
            total_reserved = 0
            
            for batch in batches:
                batch_data = {
                    'batch_id': str(batch.id),
                    'batch_number': batch.batch_number,
                    'current_quantity': float(batch.current_quantity),
                    'reserved_quantity': float(batch.reserved_quantity),
                    'available_quantity': float(batch.available_quantity),
                    'purchase_price': float(batch.purchase_price) if batch.purchase_price else None,
                    'selling_price': float(batch.selling_price) if batch.selling_price else None,
                    'received_date': batch.received_date.isoformat() if batch.received_date else None,
                    'expiry_date': batch.expiry_date.isoformat() if batch.expiry_date else None,
                    'days_until_expiry': batch.days_until_expiry if hasattr(batch, 'days_until_expiry') else None,
                    'status': batch.status
                }
                batch_info.append(batch_data)
                total_available += batch.available_quantity
                total_reserved += batch.reserved_quantity
            
            return JsonResponse({
                'success': True,
                'item': {
                    'id': str(item.id),
                    'name': item.name,
                    'sku': item.sku,
                    'total_quantity': float(item.quantity),
                    'total_available': float(total_available),
                    'total_reserved': float(total_reserved),
                    'unit_price': float(item.unit_price) if item.unit_price else None,
                    'reorder_level': float(item.reorder_level) if item.reorder_level else None,
                    'maximum_level': float(item.maximum_level) if item.maximum_level else None,
                    'classification': item.classification.name if item.classification else None
                },
                'batches': batch_info,
                'batch_count': len(batch_info)
            })
            
        except Item.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Item not found'
            }, status=404)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
def api_simulate_work_order_allocation(request):
    """Simulate work order allocation without actually creating the work order"""
    try:
        data = json.loads(request.body)
        materials = data.get('materials', [])
        tenant_id = getattr(request, 'tenant_id', None)
        
        # Create a temporary work order for simulation
        class TempWorkOrder:
            def __init__(self, tenant_id):
                self.tenant_id = tenant_id
                self.materials = TempMaterialManager(materials, tenant_id)
        
        class TempMaterialManager:
            def __init__(self, materials_data, tenant_id):
                self.materials_data = materials_data
                self.tenant_id = tenant_id
            
            def all(self):
                temp_materials = []
                for mat_data in self.materials_data:
                    temp_mat = TempMaterial(mat_data, self.tenant_id)
                    temp_materials.append(temp_mat)
                return temp_materials
        
        class TempMaterial:
            def __init__(self, data, tenant_id):
                self.item = Item.objects.get(id=data['item_id'], tenant_id=tenant_id)
                self.quantity = float(data['quantity'])
        
        temp_work_order = TempWorkOrder(tenant_id)
        
        # Run allocation simulation
        allocation_result = InventoryAllocationService.allocate_stock_for_work_order(temp_work_order)
        
        # Convert result for JSON response
        json_result = {
            'success': allocation_result['success'],
            'total_allocated': allocation_result['total_allocated'],
            'requires_purchase': allocation_result['requires_purchase'],
            'allocated_items': [],
            'missing_items': []
        }
        
        for allocated in allocation_result['allocated_items']:
            json_result['allocated_items'].append({
                'item_name': allocated['item'].name,
                'item_sku': allocated['item'].sku,
                'required_quantity': float(allocated['required_quantity']),
                'allocated_quantity': float(allocated['allocated_quantity']),
                'batches': allocated['batches']
            })
        
        for missing in allocation_result['missing_items']:
            json_result['missing_items'].append({
                'item_name': missing['item'].name,
                'item_sku': missing['item'].sku,
                'required_quantity': float(missing['required_quantity']),
                'available_quantity': float(missing['available_quantity']),
                'shortage': float(missing['shortage'])
            })
        
        return JsonResponse(json_result)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def api_get_work_order_progress(request, work_order_id):
    """Get detailed progress information for a work order"""
    try:
        tenant_id = getattr(request, 'tenant_id', None)
        work_order = WorkOrder.objects.get(id=work_order_id, tenant_id=tenant_id)
        
        # Calculate progress metrics
        total_operations = work_order.operations.count()
        completed_operations = work_order.operations.filter(is_completed=True).count()
        
        total_materials = work_order.materials.count()
        consumed_materials = work_order.materials.filter(is_consumed=True).count()
        
        operation_progress = (completed_operations / total_operations * 100) if total_operations > 0 else 0
        material_progress = (consumed_materials / total_materials * 100) if total_materials > 0 else 0
        overall_progress = (operation_progress + material_progress) / 2
        
        # Get material status details
        material_details = []
        for material in work_order.materials.all():
            material_details.append({
                'id': str(material.id),
                'item_name': material.item.name,
                'item_sku': material.item.sku,
                'quantity': float(material.quantity),
                'unit': material.unit_of_measure,
                'is_consumed': material.is_consumed,
                'notes': material.notes
            })
        
        # Get operation status details
        operation_details = []
        for operation in work_order.operations.all():
            operation_details.append({
                'id': str(operation.id),
                'name': operation.name,
                'description': operation.description,
                'duration_minutes': operation.duration_minutes,
                'is_completed': operation.is_completed,
                'completed_at': operation.completed_at.isoformat() if operation.completed_at else None
            })
        
        return JsonResponse({
            'success': True,
            'work_order': {
                'id': str(work_order.id),
                'work_order_number': work_order.work_order_number,
                'status': work_order.status,
                'priority': work_order.priority,
                'created_at': work_order.created_at.isoformat(),
                'customer_name': work_order.customer.get_full_name() if work_order.customer else None,
                'vehicle_info': f"{work_order.vehicle.make.name} {work_order.vehicle.model.name}" if work_order.vehicle else None
            },
            'progress': {
                'overall_progress': round(overall_progress, 2),
                'operation_progress': round(operation_progress, 2),
                'material_progress': round(material_progress, 2),
                'operations_completed': completed_operations,
                'total_operations': total_operations,
                'materials_consumed': consumed_materials,
                'total_materials': total_materials
            },
            'materials': material_details,
            'operations': operation_details
        })
        
    except WorkOrder.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Work order not found'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500) 