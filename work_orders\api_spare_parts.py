from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.db.models import Q
import json
import traceback
import uuid

from inventory.models import Item, OperationCompatibility
from work_orders.models import WorkOrderType
from warehouse.models import ItemLocation, Location
from setup.models import Vehicle, ServiceCenter

@login_required
def api_get_spare_parts(request):
    """API endpoint to fetch spare parts based on operation and vehicle model.
    Includes warehouse availability information, pricing information, and transfer options.
    
    GET parameters:
        operation_id: ID of the operation type (UUID or name)
        vehicle_id: ID of the vehicle (optional)
        service_center_id: ID of the service center (optional)
    """
    tenant_id = getattr(request, 'tenant_id', None)
    
    try:
        # Get parameters
        operation_id = request.GET.get('operation_id')
        vehicle_id = request.GET.get('vehicle_id')
        service_center_id = request.GET.get('service_center_id')
        
        # Debug logging
        print(f"API: get_spare_parts called with: operation_id={operation_id}, vehicle_id={vehicle_id}, service_center_id={service_center_id}")
        
        if not operation_id:
            return JsonResponse({
                'success': False,
                'error': 'Operation ID is required'
            })
        
        # 1. Get operation type information
        operation_type = None
        try:
            # Try by ID first
            try:
                operation_type_obj = WorkOrderType.objects.get(pk=operation_id)
                operation_type = {
                    'id': str(operation_type_obj.id),
                    'name': operation_type_obj.name,
                    'description': operation_type_obj.description
                }
            except (WorkOrderType.DoesNotExist, ValueError, TypeError):
                # Then try by name
                operation_type_obj = WorkOrderType.objects.filter(name=operation_id).first()
                if operation_type_obj:
                    operation_type = {
                        'id': str(operation_type_obj.id),
                        'name': operation_type_obj.name,
                        'description': operation_type_obj.description
                    }
                else:
                    # Use the operation_id as a fallback
                    operation_type = {
                        'id': operation_id,
                        'name': operation_id,
                        'description': ''
                    }
        except Exception as e:
            print(f"Error getting operation type: {str(e)}")
            # Create a fallback operation type
            operation_type = {
                'id': operation_id,
                'name': operation_id,
                'description': ''
            }
        
        # 2. Get vehicle information if provided
        vehicle = None
        vehicle_make = None
        vehicle_model = None
        if vehicle_id:
            try:
                # Safely convert vehicle_id to UUID
                vehicle_uuid = uuid.UUID(vehicle_id)
                
                # Use direct SQL to avoid UUID issues
                from django.db import connection
                from django.conf import settings
                db_engine = settings.DATABASES['default']['ENGINE']
                
                with connection.cursor() as cursor:
                    if 'postgresql' in db_engine:
                        query = "SELECT make, model FROM setup_vehicle WHERE id::text = %s"
                    else:
                        query = "SELECT make, model FROM setup_vehicle WHERE id = %s"
                    
                    if tenant_id:
                        query += " AND tenant_id = %s"
                        if vehicle_uuid: # Only execute if vehicle_uuid is not None
                            cursor.execute(query, [str(vehicle_uuid), tenant_id])
                    else:
                        if vehicle_uuid: # Only execute if vehicle_uuid is not None
                            cursor.execute(query, [str(vehicle_uuid)])
                    
                    row = cursor.fetchone()
                    if row:
                        vehicle_make, vehicle_model = row
                        print(f"Found vehicle: {vehicle_make} {vehicle_model}")
            except ValueError:
                print(f"Invalid UUID for vehicle_id: {vehicle_id}")
                # vehicle_uuid remains None, so we don't try to query with an invalid UUID
            except Exception as e:
                print(f"Error getting vehicle: {str(e)}")
        
        # 3. Get service center and warehouse information
        service_center = None
        warehouses = []
        if service_center_id:
            try:
                service_center = ServiceCenter.objects.get(pk=service_center_id)
                print(f"Found service center: {service_center.name}")
                
                # Get warehouses for this service center
                from warehouse.models import Warehouse
                warehouses = Warehouse.objects.filter(
                    Q(service_center=service_center) |
                    Q(is_central=True)
                ).distinct()
                
                print(f"Found {warehouses.count()} warehouses for service center")
            except ServiceCenter.DoesNotExist:
                print(f"Service center with ID {service_center_id} not found")
        
        # 4. Get compatible parts - try multiple approaches
        parts_list = []
        
        # Try approach 1: Get parts by operation type ID
        if isinstance(operation_type, dict) and 'id' in operation_type:
            try:
                # Use the UUID or name from the operation type
                op_id = operation_type['id']
                
                # Query for compatible parts
                parts_query = Item.objects.filter(
                    operation_compatibilities__operation_type_id=op_id,
                    is_active=True
                )
                
                if tenant_id:
                    parts_query = parts_query.filter(tenant_id=tenant_id)
                
                # Filter by vehicle make/model if available
                if vehicle_make or vehicle_model:
                    vehicle_filter = Q()
                    
                    if vehicle_make:
                        vehicle_filter |= Q(vehicle_compatibilities__make__exact=vehicle_make)
                    
                    if vehicle_model:
                        vehicle_filter |= Q(vehicle_compatibilities__model__exact=vehicle_model)
                    
                    # Apply the filter
                    parts_query = parts_query.filter(vehicle_filter)
                
                # Get distinct parts
                parts_query = parts_query.distinct()
                parts_list = list(parts_query)
                
                print(f"Found {len(parts_list)} parts compatible with operation {operation_id}")
            except Exception as e:
                print(f"Error getting parts by operation ID: {str(e)}")
        
        # If no parts found, try approach 2: Direct SQL
        if not parts_list:
            try:
                from django.db import connection
                
                # Construct SQL to get compatible parts
                query = """
                    SELECT DISTINCT i.id, i.name, i.sku, i.unit_price, i.description
                    FROM inventory_item i
                    JOIN inventory_operationcompatibility oc ON i.id = oc.item_id
                    WHERE i.is_active = TRUE
                """
                
                params = []
                
                # Add operation type filter
                if isinstance(operation_type, dict) and 'id' in operation_type:
                    if 'postgresql' in settings.DATABASES['default']['ENGINE']:
                        query += " AND (oc.operation_type_id::text = %s OR oc.operation_type_id IN (SELECT id FROM work_orders_workordertype WHERE name = %s))"
                    else:
                        query += " AND (oc.operation_type_id = %s OR oc.operation_type_id IN (SELECT id FROM work_orders_workordertype WHERE name = %s))"
                    
                    params.extend([operation_type['id'], operation_type['name']])
                
                # Add tenant filter
                if tenant_id:
                    query += " AND i.tenant_id = %s"
                    params.append(tenant_id)
                
                # Add vehicle filter
                if vehicle_make or vehicle_model:
                    query += """ AND i.id IN (
                        SELECT item_id FROM inventory_vehiclecompatibility
                        WHERE 1=1
                    """
                    
                    if vehicle_make:
                        query += " AND LOWER(make) = LOWER(%s)"
                        params.append(vehicle_make)
                    
                    if vehicle_model:
                        query += " AND LOWER(model) = LOWER(%s)"
                        params.append(vehicle_model)
                    
                    query += ")"
                
                # Execute query
                with connection.cursor() as cursor:
                    cursor.execute(query, params)
                    rows = cursor.fetchall()
                
                # Import the Item model only when needed
                from inventory.models import Item
                
                # Process results
                for row in rows:
                    try:
                        item = Item.objects.get(pk=row[0])
                        parts_list.append(item)
                    except Exception as inner_e:
                        print(f"Error getting item {row[0]}: {str(inner_e)}")
                
                print(f"Found {len(parts_list)} parts with SQL query")
            except Exception as e:
                print(f"Error with SQL query: {str(e)}")
        
        # If still no parts, try approach 3: Search by similar operation name
        if not parts_list and isinstance(operation_type, dict) and 'name' in operation_type:
            try:
                operation_name = operation_type['name']
                search_terms = operation_name.split()
                
                if search_terms:
                    # Build a query looking for items with matching terms in name/description
                    search_query = None
                    for term in search_terms:
                        if len(term) > 3:  # Only use terms with at least 4 characters
                            if search_query is None:
                                search_query = Q(name__icontains=term) | Q(description__icontains=term)
                            else:
                                search_query |= Q(name__icontains=term) | Q(description__icontains=term)
                    
                    if search_query:
                        items_query = Item.objects.filter(search_query, is_active=True)
                        if tenant_id:
                            items_query = items_query.filter(tenant_id=tenant_id)
                        
                        # Apply vehicle filtering if needed
                        if vehicle_make:
                            items_query = items_query.filter(vehicle_compatibilities__make__exact=vehicle_make)
                        
                        if vehicle_model:
                            items_query = items_query.filter(vehicle_compatibilities__model__exact=vehicle_model)
                        
                        parts_list = list(items_query.distinct())
                        print(f"Found {len(parts_list)} parts via name/description search")
            except Exception as e:
                print(f"Error in text search: {str(e)}")
        
        # 5. Format the parts data with warehouse information
        parts_data = []
        
        for part in parts_list:
            # Default values
            recommended_quantity = 1
            part_price = part.unit_price if part.unit_price else 0
            is_special_price = False
            
            # Get quantity from operation compatibility if available
            try:
                if isinstance(operation_type, dict) and 'id' in operation_type:
                    op_compat = part.operation_compatibilities.filter(
                        Q(operation_type_id=operation_type['id']) |
                        Q(operation_type__name=operation_type['name'])
                    ).first()
                    
                    if op_compat and hasattr(op_compat, 'recommended_quantity') and op_compat.recommended_quantity:
                        recommended_quantity = float(op_compat.recommended_quantity)
            except Exception as e:
                print(f"Error getting recommended quantity: {str(e)}")
            
            # Check warehouse availability
            warehouse_availability = []
            total_available = 0
            
            if warehouses:
                for warehouse in warehouses:
                    try:
                        # Get locations in this warehouse
                        locations = Location.objects.filter(warehouse=warehouse)
                        
                        # Get item stock in these locations
                        stock_entries = ItemLocation.objects.filter(
                            item=part,
                            location__in=locations,
                            quantity__gt=0
                        )
                        
                        warehouse_qty = sum(entry.quantity for entry in stock_entries)
                        
                        if warehouse_qty > 0:
                            warehouse_availability.append({
                                'warehouse_id': str(warehouse.id),
                                'warehouse_name': warehouse.name,
                                'quantity': float(warehouse_qty),
                                'is_local': warehouse.service_center_id == service_center_id if service_center_id else False
                            })
                            
                            total_available += warehouse_qty
                    except Exception as e:
                        print(f"Error checking warehouse {warehouse.name}: {str(e)}")
            
            # Add part data
            parts_data.append({
                'id': str(part.id),
                'name': part.name,
                'sku': part.sku,
                'standard_price': float(part.unit_price) if part.unit_price else 0,
                'price': float(part_price),
                'price_source': 'standard',
                'is_special_price': is_special_price,
                'unit_of_measurement': part.unit_of_measurement.symbol if part.unit_of_measurement else '',
                'recommended_quantity': float(recommended_quantity),
                'total_available': float(total_available),
                'warehouse_availability': warehouse_availability,
                'is_required': False,
                'is_low_stock': total_available < recommended_quantity,
                'needs_transfer': total_available > 0 and all(not entry.get('is_local', False) for entry in warehouse_availability),
                'currency': 'جنيه'  # Egyptian Pound
            })
        
        # Return the results
        return JsonResponse({
            'success': True,
            'operation': operation_type,
            'parts': parts_data
        })
        
    except Exception as e:
        # Log the error
        error_traceback = traceback.format_exc()
        print(f"ERROR in api_get_spare_parts: {str(e)}")
        print(f"Request parameters: operation_id={request.GET.get('operation_id')}, vehicle_id={request.GET.get('vehicle_id')}, service_center_id={request.GET.get('service_center_id')}")
        print(f"Traceback: {error_traceback}")
        
        return JsonResponse({
            'success': False,
            'error': str(e),
            'message': "An error occurred while getting spare parts. Please try again or contact support if the problem persists."
        }) 