from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import (
    WorkOrderType, MaintenanceSchedule, ScheduleOperation, OperationPart,
    BillOfMaterials, BOMItem, WorkOrder, WorkOrderOperation, WorkOrderMaterial
)
from setup.models import ServiceCenter, Vehicle, Customer
from inventory.models import Item

class WorkOrderTypeForm(forms.ModelForm):
    """Form for creating and editing work order types"""
    
    class Meta:
        model = WorkOrderType
        fields = ['name', 'description', 'color_code', 'is_active']
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].widget.attrs.update({
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'placeholder': _('Enter work order type name...')
        })
        self.fields['description'].widget.attrs.update({
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'rows': 3,
            'placeholder': _('Enter description...')
        })
        self.fields['color_code'].widget.attrs.update({
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'placeholder': _('e.g., #FF5733')
        })

class MaintenanceScheduleForm(forms.ModelForm):
    """Form for creating and editing maintenance schedules"""
    
    class Meta:
        model = MaintenanceSchedule
        fields = [
            'name', 'description', 'interval_type', 'mileage_interval', 
            'time_interval_months', 'vehicle_make', 'vehicle_model', 
            'year_from', 'year_to', 'is_active'
        ]
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Base styling for all fields
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        self.fields['name'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter schedule name...')
        })
        self.fields['description'].widget.attrs.update({
            'class': base_class,
            'rows': 3,
            'placeholder': _('Enter description...')
        })
        self.fields['interval_type'].widget.attrs.update({
            'class': base_class
        })
        self.fields['mileage_interval'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter mileage interval...')
        })
        self.fields['time_interval_months'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter time interval in months...')
        })
        self.fields['vehicle_make'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter vehicle make...')
        })
        self.fields['vehicle_model'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter vehicle model...')
        })
        self.fields['year_from'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter start year...')
        })
        self.fields['year_to'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter end year...')
        })

class ScheduleOperationForm(forms.ModelForm):
    """Form for creating and editing schedule operations"""
    
    class Meta:
        model = ScheduleOperation
        fields = [
            'maintenance_schedule', 'name', 'description', 'duration_minutes',
            'sequence', 'is_required', 'operation_type'
        ]
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            self.fields['maintenance_schedule'].queryset = MaintenanceSchedule.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['operation_type'].queryset = WorkOrderType.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        self.fields['maintenance_schedule'].widget.attrs.update({'class': base_class})
        self.fields['name'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter operation name...')
        })
        self.fields['description'].widget.attrs.update({
            'class': base_class,
            'rows': 3,
            'placeholder': _('Enter description...')
        })
        self.fields['duration_minutes'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter duration in minutes...')
        })
        self.fields['sequence'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter sequence number...')
        })
        self.fields['operation_type'].widget.attrs.update({'class': base_class})

class OperationPartForm(forms.ModelForm):
    """Form for creating and editing operation parts"""
    
    class Meta:
        model = OperationPart
        fields = ['schedule_operation', 'item', 'quantity', 'is_required', 'notes']
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            self.fields['schedule_operation'].queryset = ScheduleOperation.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['item'].queryset = Item.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        self.fields['schedule_operation'].widget.attrs.update({'class': base_class})
        self.fields['item'].widget.attrs.update({'class': base_class})
        self.fields['quantity'].widget.attrs.update({
            'class': base_class,
            'step': '0.00001',
            'placeholder': _('Enter quantity...')
        })
        self.fields['notes'].widget.attrs.update({
            'class': base_class,
            'rows': 2,
            'placeholder': _('Enter notes...')
        })

class BillOfMaterialsForm(forms.ModelForm):
    """Form for creating and editing bills of materials"""
    
    class Meta:
        model = BillOfMaterials
        fields = ['name', 'description', 'finished_item', 'version', 'is_active', 'notes']
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            self.fields['finished_item'].queryset = Item.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        self.fields['name'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter BOM name...')
        })
        self.fields['description'].widget.attrs.update({
            'class': base_class,
            'rows': 3,
            'placeholder': _('Enter description...')
        })
        self.fields['finished_item'].widget.attrs.update({'class': base_class})
        self.fields['version'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter version...')
        })
        self.fields['notes'].widget.attrs.update({
            'class': base_class,
            'rows': 3,
            'placeholder': _('Enter notes...')
        })

class BOMItemForm(forms.ModelForm):
    """Form for creating and editing BOM items"""
    
    class Meta:
        model = BOMItem
        fields = [
            'bom', 'item', 'quantity', 'unit_of_measure', 
            'is_optional', 'sequence', 'notes'
        ]
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            self.fields['bom'].queryset = BillOfMaterials.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['item'].queryset = Item.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        self.fields['bom'].widget.attrs.update({'class': base_class})
        self.fields['item'].widget.attrs.update({'class': base_class})
        self.fields['quantity'].widget.attrs.update({
            'class': base_class,
            'step': '0.00001',
            'placeholder': _('Enter quantity...')
        })
        self.fields['unit_of_measure'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter unit of measure...')
        })
        self.fields['sequence'].widget.attrs.update({
            'class': base_class,
            'placeholder': _('Enter sequence...')
        })
        self.fields['notes'].widget.attrs.update({
            'class': base_class,
            'rows': 2,
            'placeholder': _('Enter notes...')
        })

class WorkOrderForm(forms.ModelForm):
    """Form for creating and editing work orders"""
    
    class Meta:
        model = WorkOrder
        fields = [
            'work_order_number', 'work_order_type', 'bill_of_materials', 
            'description', 'priority', 'status', 'operation_category',
            'maintenance_schedule', 'service_center', 'vehicle', 
            'current_odometer', 'fuel_level', 'planned_start_date',
            'planned_end_date', 'customer', 'customer_name', 
            'customer_phone', 'customer_email', 'service_item_serial',
            'warranty_status', 'estimated_cost', 'notes'
        ]
        widgets = {
            'planned_start_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'planned_end_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'description': forms.Textarea(attrs={'rows': 4}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            from core.middleware import get_current_tenant_id
            tenant_id = get_current_tenant_id()
            
            # Filter related objects by tenant
            self.fields['work_order_type'].queryset = WorkOrderType.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['bill_of_materials'].queryset = BillOfMaterials.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['maintenance_schedule'].queryset = MaintenanceSchedule.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['service_center'].queryset = ServiceCenter.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['vehicle'].queryset = Vehicle.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['customer'].queryset = Customer.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields
        for field_name, field in self.fields.items():
            if field_name not in ['warranty_status']:  # Skip checkbox fields
                field.widget.attrs.update({'class': base_class})
        
        # Add specific placeholders
        self.fields['work_order_number'].widget.attrs.update({
            'placeholder': _('Enter work order number...')
        })
        self.fields['description'].widget.attrs.update({
            'placeholder': _('Enter work order description...')
        })
        self.fields['current_odometer'].widget.attrs.update({
            'placeholder': _('Enter current odometer reading...')
        })
        self.fields['fuel_level'].widget.attrs.update({
            'placeholder': _('Enter fuel level percentage...')
        })
        self.fields['customer_name'].widget.attrs.update({
            'placeholder': _('Enter customer name...')
        })
        self.fields['customer_phone'].widget.attrs.update({
            'placeholder': _('Enter customer phone...')
        })
        self.fields['customer_email'].widget.attrs.update({
            'placeholder': _('Enter customer email...')
        })
        self.fields['service_item_serial'].widget.attrs.update({
            'placeholder': _('Enter service item serial...')
        })
        self.fields['estimated_cost'].widget.attrs.update({
            'placeholder': _('Enter estimated cost...')
        })
        self.fields['notes'].widget.attrs.update({
            'placeholder': _('Enter additional notes...')
        })

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate date range
        planned_start = cleaned_data.get('planned_start_date')
        planned_end = cleaned_data.get('planned_end_date')
        
        if planned_start and planned_end and planned_start >= planned_end:
            raise ValidationError(_('Planned end date must be after planned start date.'))
        
        # Validate fuel level
        fuel_level = cleaned_data.get('fuel_level')
        if fuel_level is not None and (fuel_level < 0 or fuel_level > 100):
            raise ValidationError(_('Fuel level must be between 0 and 100 percent.'))
        
        return cleaned_data

class WorkOrderOperationForm(forms.ModelForm):
    """Form for creating and editing work order operations"""
    
    class Meta:
        model = WorkOrderOperation
        fields = [
            'work_order', 'sequence', 'name', 'description', 
            'duration_minutes', 'is_completed', 'notes'
        ]
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            self.fields['work_order'].queryset = WorkOrder.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['is_completed']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['name'].widget.attrs.update({
            'placeholder': _('Enter operation name...')
        })
        self.fields['description'].widget.attrs.update({
            'rows': 3,
            'placeholder': _('Enter operation description...')
        })
        self.fields['duration_minutes'].widget.attrs.update({
            'placeholder': _('Enter duration in minutes...')
        })
        self.fields['sequence'].widget.attrs.update({
            'placeholder': _('Enter sequence number...')
        })
        self.fields['notes'].widget.attrs.update({
            'rows': 3,
            'placeholder': _('Enter operation notes...')
        })

class WorkOrderMaterialForm(forms.ModelForm):
    """Form for creating and editing work order materials"""
    
    class Meta:
        model = WorkOrderMaterial
        fields = [
            'work_order', 'item', 'quantity', 'unit_of_measure',
            'is_consumed', 'notes'
        ]
        
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            self.fields['work_order'].queryset = WorkOrder.objects.filter(
                tenant_id=tenant_id
            )
            self.fields['item'].queryset = Item.objects.filter(
                tenant_id=tenant_id
            )
        
        base_class = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        
        # Apply styling to all fields except checkboxes
        for field_name, field in self.fields.items():
            if field_name not in ['is_consumed']:
                field.widget.attrs.update({'class': base_class})
        
        self.fields['quantity'].widget.attrs.update({
            'step': '0.00001',
            'placeholder': _('Enter quantity...')
        })
        self.fields['unit_of_measure'].widget.attrs.update({
            'placeholder': _('Enter unit of measure...')
        })
        self.fields['notes'].widget.attrs.update({
            'rows': 2,
            'placeholder': _('Enter material notes...')
        })

class WorkOrderReportForm(forms.Form):
    """Form for work order reports"""
    
    REPORT_TYPE_CHOICES = [
        ('status', _('Status Report')),
        ('completion', _('Completion Report')),
        ('cost', _('Cost Analysis')),
        ('efficiency', _('Efficiency Report')),
    ]
    
    report_type = forms.ChoiceField(
        label=_("Report Type"),
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    date_from = forms.DateField(
        label=_("Date From"),
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    date_to = forms.DateField(
        label=_("Date To"),
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    work_order_type = forms.ModelChoiceField(
        label=_("Work Order Type"),
        queryset=WorkOrderType.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    status = forms.ChoiceField(
        label=_("Status"),
        choices=[('', _('All'))] + WorkOrder.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            self.fields['work_order_type'].queryset = WorkOrderType.objects.filter(
                tenant_id=tenant_id
            )

class WorkOrderSearchForm(forms.Form):
    """Form for searching work orders"""
    
    search = forms.CharField(
        label=_("Search"),
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'placeholder': _('Search by work order number, description, customer...')
        })
    )
    
    status = forms.ChoiceField(
        label=_("Status"),
        choices=[('', _('All'))] + WorkOrder.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    priority = forms.ChoiceField(
        label=_("Priority"),
        choices=[('', _('All'))] + WorkOrder.PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    work_order_type = forms.ModelChoiceField(
        label=_("Work Order Type"),
        queryset=WorkOrderType.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if user:
            self.fields['work_order_type'].queryset = WorkOrderType.objects.filter(
                tenant_id=tenant_id
            )

class WorkOrderStatusChangeForm(forms.Form):
    """Form for changing work order status with custom popups for each status"""
    status = forms.ChoiceField(
        label=_("Status"),
        choices=WorkOrder.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5'
        })
    )
    notes = forms.CharField(
        label=_("Notes"),
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5',
            'rows': 3,
            'placeholder': _('Add notes about this status change...')
        })
    )
    
    def __init__(self, *args, **kwargs):
        work_order = kwargs.pop('work_order', None)
        super().__init__(*args, **kwargs)
        
        if work_order:
            self.fields['status'].initial = work_order.status
            
            # Generate status-specific help text
            status_help = {
                'planned': _('Work order is scheduled and ready to start.'),
                'in_progress': _('Work has started on this work order.'),
                'on_hold': _('Work is temporarily paused.'),
                'completed': _('All work has been completed.'),
                'cancelled': _('Work order has been cancelled.')
            }
            
            # Set help text for each status option
            status_choices = []
            for value, label in WorkOrder.STATUS_CHOICES:
                help_text = status_help.get(value, '')
                # Format the choice with help text for the template
                status_choices.append((value, {'label': label, 'help': help_text}))
            
            self.status_choices_with_help = status_choices 