from django.core.management.base import BaseCommand
from django.utils import timezone
from work_orders.utils import check_and_auto_complete_work_orders
from core.utils import get_all_tenant_ids


class Command(BaseCommand):
    help = 'Auto-complete work orders that have all operations and materials finished and generate invoices'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            help='Specific tenant ID to process (if not provided, processes all tenants)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually making changes',
        )
    
    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id')
        dry_run = options.get('dry_run', False)
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        start_time = timezone.now()
        self.stdout.write(f'Starting auto-completion process at {start_time}')
        
        # Get tenant IDs to process
        if tenant_id:
            tenant_ids = [tenant_id]
            self.stdout.write(f'Processing specific tenant: {tenant_id}')
        else:
            try:
                tenant_ids = get_all_tenant_ids()
                self.stdout.write(f'Processing all tenants: {len(tenant_ids)} found')
            except Exception as e:
                # Fallback: try to get tenant IDs from work orders
                from work_orders.models import WorkOrder
                tenant_ids = list(WorkOrder.objects.values_list('tenant_id', flat=True).distinct())
                self.stdout.write(f'Using fallback method: {len(tenant_ids)} tenants found')
        
        total_stats = {
            'tenants_processed': 0,
            'total_checked': 0,
            'total_completed': 0,
            'total_invoices_created': 0,
            'total_sales_orders_created': 0,
            'total_errors': 0
        }
        
        # Process each tenant
        for tid in tenant_ids:
            try:
                self.stdout.write(f'\n--- Processing Tenant: {tid} ---')
                
                if not dry_run:
                    results = check_and_auto_complete_work_orders(tenant_id=tid)
                else:
                    # For dry run, just check without completing
                    from work_orders.models import WorkOrder
                    from work_orders.utils import check_work_order_completion
                    
                    work_orders = WorkOrder.objects.filter(
                        tenant_id=tid,
                        status='in_progress'
                    )
                    
                    results = {
                        'checked': work_orders.count(),
                        'completed': 0,
                        'invoices_created': 0,
                        'sales_orders_created': 0,
                        'errors': [],
                        'details': []
                    }
                    
                    for wo in work_orders:
                        completion_check = check_work_order_completion(wo)
                        if completion_check['can_complete']:
                            results['completed'] += 1
                            results['invoices_created'] += 1  # Would create invoice
                            results['sales_orders_created'] += 1  # Would create sales order
                        
                        results['details'].append({
                            'work_order_number': wo.work_order_number,
                            'completed': completion_check['can_complete'],
                            'invoice_created': completion_check['can_complete'],
                            'sales_order_created': completion_check['can_complete'],
                            'message': 'Would be completed' if completion_check['can_complete'] else 'Not ready for completion'
                        })
                
                # Update total stats
                total_stats['tenants_processed'] += 1
                total_stats['total_checked'] += results['checked']
                total_stats['total_completed'] += results['completed']
                total_stats['total_invoices_created'] += results['invoices_created']
                total_stats['total_sales_orders_created'] += results.get('sales_orders_created', 0)
                total_stats['total_errors'] += len(results['errors'])
                
                # Display results for this tenant
                self.stdout.write(f'  Work Orders Checked: {results["checked"]}')
                self.stdout.write(
                    self.style.SUCCESS(f'  Work Orders Completed: {results["completed"]}')
                )
                self.stdout.write(
                    self.style.SUCCESS(f'  Invoices Created: {results["invoices_created"]}')
                )
                self.stdout.write(
                    self.style.SUCCESS(f'  Sales Orders Created: {results.get("sales_orders_created", 0)}')
                )
                
                if results['errors']:
                    self.stdout.write(
                        self.style.ERROR(f'  Errors: {len(results["errors"])}')
                    )
                    for error in results['errors']:
                        self.stdout.write(
                            self.style.ERROR(
                                f'    - {error["work_order_number"]}: {error["error"]}'
                            )
                        )
                
                # Show details if verbose
                if options.get('verbosity', 1) >= 2:
                    self.stdout.write('  Details:')
                    for detail in results['details']:
                        status_style = self.style.SUCCESS if detail['completed'] else self.style.WARNING
                        self.stdout.write(
                            status_style(
                                f'    - {detail["work_order_number"]}: {detail["message"]}'
                            )
                        )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing tenant {tid}: {str(e)}')
                )
                total_stats['total_errors'] += 1
        
        # Final summary
        end_time = timezone.now()
        duration = end_time - start_time
        
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('AUTO-COMPLETION SUMMARY'))
        self.stdout.write('='*60)
        self.stdout.write(f'Started: {start_time}')
        self.stdout.write(f'Finished: {end_time}')
        self.stdout.write(f'Duration: {duration}')
        self.stdout.write(f'Tenants Processed: {total_stats["tenants_processed"]}')
        self.stdout.write(f'Work Orders Checked: {total_stats["total_checked"]}')
        self.stdout.write(
            self.style.SUCCESS(f'Work Orders Completed: {total_stats["total_completed"]}')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Invoices Created: {total_stats["total_invoices_created"]}')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Sales Orders Created: {total_stats["total_sales_orders_created"]}')
        )
        
        if total_stats['total_errors'] > 0:
            self.stdout.write(
                self.style.ERROR(f'Total Errors: {total_stats["total_errors"]}')
            )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN COMPLETED - No actual changes were made')
            )
        else:
            self.stdout.write(self.style.SUCCESS('Auto-completion process completed successfully!')) 