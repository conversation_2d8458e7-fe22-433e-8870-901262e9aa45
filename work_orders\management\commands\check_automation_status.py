from django.core.management.base import BaseCommand
from django.conf import settings
from work_orders.models import WorkOrder
from sales.models import SalesOrder
from billing.models import Invoice
from inventory.models import Item


class Command(BaseCommand):
    help = 'Check work order automation status and configuration'

    def handle(self, *args, **options):
        self.stdout.write('='*60)
        self.stdout.write(self.style.SUCCESS('WORK ORDER AUTOMATION STATUS'))
        self.stdout.write('='*60)
        
        # Configuration Status
        self.stdout.write('\n📋 CONFIGURATION:')
        self.stdout.write('-'*30)
        
        wo_auto = getattr(settings, 'WORK_ORDER_AUTO_CREATE_SALES_ORDER', True)
        so_auto = getattr(settings, 'SALES_ORDER_AUTO_CREATE_INVOICE', True)
        
        if wo_auto:
            self.stdout.write('✅ Work Order → Sales Order: ENABLED')
        else:
            self.stdout.write('❌ Work Order → Sales Order: DISABLED')
            
        if so_auto:
            self.stdout.write('✅ Sales Order → Invoice: ENABLED')
        else:
            self.stdout.write('❌ Sales Order → Invoice: DISABLED')
        
        # Statistics
        self.stdout.write('\n📊 DATABASE STATISTICS:')
        self.stdout.write('-'*30)
        
        # Work Orders
        total_wo = WorkOrder.objects.count()
        completed_wo = WorkOrder.objects.filter(status='completed').count()
        wo_with_so = WorkOrder.objects.filter(sales_orders__isnull=False).distinct().count()
        wo_without_so = WorkOrder.objects.filter(status='completed').exclude(sales_orders__isnull=False).count()
        wo_no_customer = WorkOrder.objects.filter(status='completed', customer__isnull=True).count()
        wo_no_service_center = WorkOrder.objects.filter(status='completed', service_center__isnull=True).count()
        
        self.stdout.write(f'Total Work Orders: {total_wo}')
        self.stdout.write(f'Completed Work Orders: {completed_wo}')
        self.stdout.write(f'Work Orders with Sales Orders: {wo_with_so}')
        self.stdout.write(f'Completed Work Orders without Sales Orders: {wo_without_so}')
        if wo_no_customer > 0:
            self.stdout.write(f'⚠️  Completed Work Orders without Customer: {wo_no_customer}')
        if wo_no_service_center > 0:
            self.stdout.write(f'⚠️  Completed Work Orders without Service Center: {wo_no_service_center}')
        
        # Sales Orders  
        total_so = SalesOrder.objects.count()
        so_from_wo = SalesOrder.objects.filter(work_order__isnull=False).count()
        so_with_invoice = SalesOrder.objects.filter(invoices__isnull=False).distinct().count()
        
        self.stdout.write(f'Total Sales Orders: {total_so}')
        self.stdout.write(f'Sales Orders from Work Orders: {so_from_wo}')
        self.stdout.write(f'Sales Orders with Invoices: {so_with_invoice}')
        
        # Invoices
        total_invoices = Invoice.objects.count()
        invoices_from_so = Invoice.objects.filter(sales_order__isnull=False).count()
        invoices_from_wo = Invoice.objects.filter(work_order__isnull=False).count()
        
        self.stdout.write(f'Total Invoices: {total_invoices}')
        self.stdout.write(f'Invoices from Sales Orders: {invoices_from_so}')
        self.stdout.write(f'Invoices from Work Orders: {invoices_from_wo}')
        
        # Labor Item Check
        self.stdout.write('\n🔧 LABOR CONFIGURATION:')
        self.stdout.write('-'*30)
        
        try:
            labor_item = Item.objects.get(name="Labor - General Service")
            self.stdout.write(f'✅ Labor Item exists: {labor_item.name}')
            self.stdout.write(f'   Unit Price: ${labor_item.unit_price or "Not set"}')
            self.stdout.write(f'   Unit: {labor_item.unit_of_measurement}')
        except Item.DoesNotExist:
            self.stdout.write('⚠️  Labor Item not found - will be created automatically when needed')
        except Item.MultipleObjectsReturned:
            self.stdout.write('⚠️  Multiple Labor Items found - may need cleanup')
        
        # Recommendations
        self.stdout.write('\n💡 RECOMMENDATIONS:')
        self.stdout.write('-'*30)
        
        if wo_without_so > 0:
            self.stdout.write(f'• Run migration command for {wo_without_so} completed work orders')
            self.stdout.write('  python manage.py create_sales_orders_from_completed_work_orders --dry-run')
            
        if wo_no_customer > 0 or wo_no_service_center > 0:
            self.stdout.write('• Fix work orders missing customer or service center before processing')
            if wo_no_customer > 0:
                self.stdout.write(f'  {wo_no_customer} work orders need customer assignment')
            if wo_no_service_center > 0:
                self.stdout.write(f'  {wo_no_service_center} work orders need service center assignment')
        
        if not wo_auto and not so_auto:
            self.stdout.write('• Both automations are disabled - consider enabling in .env file')
        elif not wo_auto:
            self.stdout.write('• Work Order automation disabled - sales orders won\'t be auto-created')
        elif not so_auto:
            self.stdout.write('• Invoice automation disabled - invoices won\'t be auto-created')
        
        if total_wo == 0:
            self.stdout.write('• No work orders found - create some test data to try the automation')
        
        # Test Candidates
        if wo_without_so > 0:
            self.stdout.write('\n🎯 PROCESSING CANDIDATES:')
            self.stdout.write('-'*30)
            
            candidates = WorkOrder.objects.filter(
                status='completed',
                customer__isnull=False,
                service_center__isnull=False
            ).exclude(
                sales_orders__isnull=False
            ).select_related('customer', 'service_center')[:5]
            
            if candidates:
                self.stdout.write('Sample work orders ready for processing:')
                for wo in candidates:
                    customer_name = wo.customer.full_name if wo.customer else 'No Customer'
                    service_center_name = wo.service_center.name if wo.service_center else 'No Service Center'
                    self.stdout.write(f'  • {wo.work_order_number} - {customer_name} @ {service_center_name}')
                
                if wo_without_so > 5:
                    self.stdout.write(f'  ... and {wo_without_so - 5} more')
            else:
                self.stdout.write('No eligible work orders found (completed work orders need both customer and service center)')
        
        self.stdout.write('\n' + '='*60)
        self.stdout.write('Use --help on management commands for more options')
        self.stdout.write('='*60) 