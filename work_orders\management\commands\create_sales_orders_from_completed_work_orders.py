import logging
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from work_orders.services import WorkOrderToSalesService
from work_orders.models import WorkOrder

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create sales orders and invoices for existing completed work orders'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without making any changes',
        )
        parser.add_argument(
            '--work-order-number',
            type=str,
            help='Process only specific work order by number',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=100,
            help='Limit number of work orders to process (default: 100)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force processing even if sales order already exists',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        work_order_number = options['work_order_number']
        limit = options['limit']
        force = options['force']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Running in DRY-RUN mode - no changes will be made')
            )
        
        try:
            if work_order_number:
                # Process specific work order
                self.process_specific_work_order(work_order_number, dry_run, force)
            else:
                # Process all eligible work orders
                self.process_all_work_orders(dry_run, limit, force)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Command failed: {str(e)}')
            )
            raise CommandError(f'Command failed: {str(e)}')

    def process_specific_work_order(self, work_order_number, dry_run=False, force=False):
        """Process a specific work order by number"""
        try:
            work_order = WorkOrder.objects.get(work_order_number=work_order_number)
        except WorkOrder.DoesNotExist:
            raise CommandError(f'Work order {work_order_number} not found')
        
        self.stdout.write(f'Processing work order: {work_order.work_order_number}')
        
        # Check if work order is completed
        if work_order.status != 'completed':
            self.stdout.write(
                self.style.WARNING(f'Work order {work_order.work_order_number} is not completed (status: {work_order.status})')
            )
            return
        
        # Check if sales order already exists
        if work_order.sales_orders.exists() and not force:
            existing_so = work_order.sales_orders.first()
            self.stdout.write(
                self.style.WARNING(f'Sales order {existing_so.order_number} already exists for work order {work_order.work_order_number}')
            )
            return
        
        if not work_order.customer:
            self.stdout.write(
                self.style.ERROR(f'Work order {work_order.work_order_number} has no customer assigned')
            )
            return
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'DRY-RUN: Would process work order {work_order.work_order_number}')
            )
            return
        
        # Process the work order
        with transaction.atomic():
            result = WorkOrderToSalesService.process_completed_work_order(work_order)
            
            if result['success']:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✓ Created sales order {result["sales_order"].order_number} '
                        f'and invoice {result["invoice"].invoice_number} '
                        f'for work order {work_order.work_order_number}'
                    )
                )
            else:
                error_msg = '; '.join(result['errors'])
                self.stdout.write(
                    self.style.ERROR(f'✗ Failed to process work order {work_order.work_order_number}: {error_msg}')
                )

    def process_all_work_orders(self, dry_run=False, limit=100, force=False):
        """Process all eligible completed work orders"""
        
        # Find completed work orders
        queryset = WorkOrder.objects.filter(status='completed').select_related(
            'customer', 'service_center', 'vehicle', 'assigned_technician'
        )
        
        if not force:
            # Exclude work orders that already have sales orders
            queryset = queryset.exclude(sales_orders__isnull=False)
        
        # Apply limit
        if limit:
            queryset = queryset[:limit]
        
        total_count = queryset.count()
        self.stdout.write(f'Found {total_count} completed work orders to process')
        
        if total_count == 0:
            self.stdout.write(self.style.SUCCESS('No work orders to process'))
            return
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'DRY-RUN: Would process {total_count} work orders')
            )
            for wo in queryset:
                customer_name = wo.customer.full_name if wo.customer else 'No Customer'
                self.stdout.write(f'  - {wo.work_order_number} ({customer_name})')
            return
        
        # Process work orders
        results = {
            'processed': 0,
            'sales_orders_created': 0,
            'invoices_created': 0,
            'errors': []
        }
        
        for work_order in queryset:
            try:
                # Check if customer exists
                if not work_order.customer:
                    self.stdout.write(
                        self.style.WARNING(f'Skipping work order {work_order.work_order_number}: No customer assigned')
                    )
                    continue
                
                with transaction.atomic():
                    result = WorkOrderToSalesService.process_completed_work_order(work_order)
                    results['processed'] += 1
                    
                    if result['success']:
                        results['sales_orders_created'] += 1
                        results['invoices_created'] += 1
                        
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'✓ {work_order.work_order_number} → '
                                f'SO-{result["sales_order"].order_number} → '
                                f'INV-{result["invoice"].invoice_number}'
                            )
                        )
                    else:
                        error_msg = '; '.join(result['errors'])
                        results['errors'].append(f'{work_order.work_order_number}: {error_msg}')
                        self.stdout.write(
                            self.style.ERROR(f'✗ {work_order.work_order_number}: {error_msg}')
                        )
                        
            except Exception as e:
                error_msg = f'{work_order.work_order_number}: {str(e)}'
                results['errors'].append(error_msg)
                self.stdout.write(
                    self.style.ERROR(f'✗ Exception processing {work_order.work_order_number}: {str(e)}')
                )
        
        # Print summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('PROCESSING SUMMARY'))
        self.stdout.write('='*50)
        self.stdout.write(f'Work orders processed: {results["processed"]}')
        self.stdout.write(f'Sales orders created: {results["sales_orders_created"]}')
        self.stdout.write(f'Invoices created: {results["invoices_created"]}')
        self.stdout.write(f'Errors: {len(results["errors"])}')
        
        if results['errors']:
            self.stdout.write('\nERRORS:')
            for error in results['errors']:
                self.stdout.write(f'  - {error}')
        
        if results['sales_orders_created'] > 0:
            self.stdout.write(
                self.style.SUCCESS(f'\n✓ Successfully processed {results["sales_orders_created"]} work orders!')
            ) 