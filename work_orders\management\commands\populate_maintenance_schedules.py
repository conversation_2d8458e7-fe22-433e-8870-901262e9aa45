import random
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError

from setup.models import VehicleMake, VehicleModel, Vehicle
from work_orders.models import MaintenanceSchedule, ScheduleOperation, WorkOrderType
from core.middleware import get_current_tenant_id


class Command(BaseCommand):
    help = 'Populate maintenance schedules for all available car models in the database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            help='Specific tenant ID to populate schedules for (default: current tenant or 1)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing schedules',
        )
        parser.add_argument(
            '--generic-only',
            action='store_true',
            help='Create only generic schedules (not specific to makes/models)',
        )
        parser.add_argument(
            '--specific-only',
            action='store_true',
            help='Create only specific schedules for each make/model combination',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting maintenance schedule population...')
        )
        
        # Get tenant ID
        self.tenant_id = options.get('tenant_id')
        if not self.tenant_id:
            try:
                self.tenant_id = get_current_tenant_id()
            except:
                self.tenant_id = 1
                
        self.stdout.write(f'Using tenant ID: {self.tenant_id}')
        
        # Get options
        self.force = options.get('force', False)
        self.generic_only = options.get('generic_only', False)
        self.specific_only = options.get('specific_only', False)
        
        try:
            with transaction.atomic():
                # Get available work order types
                self.work_order_types = list(WorkOrderType.objects.filter(
                    tenant_id=self.tenant_id,
                    is_active=True
                ))
                
                if not self.work_order_types:
                    self.stdout.write(
                        self.style.WARNING('No work order types found. Creating basic types...')
                    )
                    self._create_basic_work_order_types()
                
                total_created = 0
                
                if not self.specific_only:
                    # Create generic maintenance schedules
                    generic_count = self._create_generic_schedules()
                    total_created += generic_count
                    
                if not self.generic_only:
                    # Create specific schedules for each make/model
                    specific_count = self._create_specific_schedules()
                    total_created += specific_count
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully created {total_created} maintenance schedules!'
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating maintenance schedules: {str(e)}')
            )
            raise

    def _create_basic_work_order_types(self):
        """Create basic work order types if none exist"""
        basic_types = [
            {
                'name': 'تغيير زيت',
                'description': 'خدمة تغيير زيت المحرك والفلتر',
                'color_code': '#4CAF50'
            },
            {
                'name': 'صيانة فرامل',
                'description': 'فحص وصيانة نظام الفرامل',
                'color_code': '#2196F3'
            },
            {
                'name': 'صيانة إطارات',
                'description': 'فحص وصيانة الإطارات',
                'color_code': '#FF9800'
            },
            {
                'name': 'صيانة عامة',
                'description': 'صيانة وفحص عام للمركبة',
                'color_code': '#9C27B0'
            },
            {
                'name': 'إصلاح كهربائي',
                'description': 'إصلاح الأنظمة الكهربائية',
                'color_code': '#F44336'
            }
        ]
        
        for type_data in basic_types:
            work_order_type, created = WorkOrderType.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=type_data['name'],
                defaults={
                    'description': type_data['description'],
                    'color_code': type_data['color_code'],
                    'is_active': True
                }
            )
            if created:
                self.work_order_types.append(work_order_type)
                self.stdout.write(f'  ✓ Created work order type: {work_order_type.name}')

    def _create_generic_schedules(self):
        """Create generic maintenance schedules that work for all vehicles"""
        self.stdout.write('Creating generic maintenance schedules...')
        
        generic_schedules = [
            {
                'name': 'الصيانة الدورية 5,000 كم',
                'description': 'صيانة دورية كل 5,000 كيلومتر - تغيير زيت وفحص أساسي',
                'interval_type': 'mileage',
                'mileage_interval': 5000,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'تغيير فلتر الزيت', 'duration': 15, 'sequence': 20},
                    {'name': 'فحص مستوى السوائل', 'duration': 10, 'sequence': 30},
                    {'name': 'فحص الإطارات', 'duration': 15, 'sequence': 40},
                ]
            },
            {
                'name': 'الصيانة الدورية 10,000 كم',
                'description': 'صيانة دورية كل 10,000 كيلومتر - صيانة متوسطة',
                'interval_type': 'mileage',
                'mileage_interval': 10000,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'تغيير فلتر الزيت', 'duration': 15, 'sequence': 20},
                    {'name': 'تغيير فلتر الهواء', 'duration': 20, 'sequence': 30},
                    {'name': 'فحص نظام الفرامل', 'duration': 45, 'sequence': 40},
                    {'name': 'فحص البطارية', 'duration': 15, 'sequence': 50},
                    {'name': 'فحص الإطارات وضبط الهواء', 'duration': 20, 'sequence': 60},
                ]
            },
            {
                'name': 'الصيانة الدورية 20,000 كم',
                'description': 'صيانة دورية كل 20,000 كيلومتر - صيانة شاملة',
                'interval_type': 'mileage',
                'mileage_interval': 20000,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'تغيير فلتر الزيت', 'duration': 15, 'sequence': 20},
                    {'name': 'تغيير فلتر الهواء', 'duration': 20, 'sequence': 30},
                    {'name': 'تغيير فلتر الوقود', 'duration': 25, 'sequence': 40},
                    {'name': 'فحص وصيانة الفرامل', 'duration': 60, 'sequence': 50},
                    {'name': 'فحص نظام التعليق', 'duration': 45, 'sequence': 60},
                    {'name': 'فحص نظام التبريد', 'duration': 30, 'sequence': 70},
                    {'name': 'فحص النظام الكهربائي', 'duration': 40, 'sequence': 80},
                ]
            },
            {
                'name': 'الصيانة الدورية 40,000 كم',
                'description': 'صيانة دورية كل 40,000 كيلومتر - صيانة كبرى',
                'interval_type': 'mileage',
                'mileage_interval': 40000,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'تغيير فلتر الزيت', 'duration': 15, 'sequence': 20},
                    {'name': 'تغيير فلتر الهواء', 'duration': 20, 'sequence': 30},
                    {'name': 'تغيير فلتر الوقود', 'duration': 25, 'sequence': 40},
                    {'name': 'تغيير شمعات الإشعال', 'duration': 45, 'sequence': 50},
                    {'name': 'تغيير سير المحرك', 'duration': 60, 'sequence': 60},
                    {'name': 'صيانة نظام الفرامل', 'duration': 90, 'sequence': 70},
                    {'name': 'صيانة نظام التعليق', 'duration': 120, 'sequence': 80},
                    {'name': 'فحص وصيانة نظام التبريد', 'duration': 60, 'sequence': 90},
                    {'name': 'فحص شامل للمحرك', 'duration': 90, 'sequence': 100},
                ]
            },
            {
                'name': 'الصيانة الدورية 80,000 كم',
                'description': 'صيانة دورية كل 80,000 كيلومتر - صيانة شاملة متقدمة',
                'interval_type': 'mileage',
                'mileage_interval': 80000,
                'operations': [
                    {'name': 'صيانة شاملة للمحرك', 'duration': 180, 'sequence': 10},
                    {'name': 'تغيير زيت ناقل الحركة', 'duration': 90, 'sequence': 20},
                    {'name': 'صيانة نظام الفرامل الكاملة', 'duration': 150, 'sequence': 30},
                    {'name': 'استبدال ممتصات الصدمات', 'duration': 120, 'sequence': 40},
                    {'name': 'صيانة نظام التبريد الشاملة', 'duration': 90, 'sequence': 50},
                    {'name': 'فحص وضبط المحرك', 'duration': 120, 'sequence': 60},
                ]
            },
            # Time-based schedules
            {
                'name': 'الصيانة الدورية الشهرية',
                'description': 'فحص شهري للمركبة',
                'interval_type': 'time',
                'time_interval_months': 1,
                'operations': [
                    {'name': 'فحص مستوى الزيت', 'duration': 5, 'sequence': 10},
                    {'name': 'فحص مستوى السوائل', 'duration': 10, 'sequence': 20},
                    {'name': 'فحص الإطارات', 'duration': 15, 'sequence': 30},
                    {'name': 'فحص الإضاءة', 'duration': 10, 'sequence': 40},
                ]
            },
            {
                'name': 'الصيانة الدورية نصف السنوية',
                'description': 'صيانة دورية كل 6 أشهر',
                'interval_type': 'time',
                'time_interval_months': 6,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'فحص شامل للمركبة', 'duration': 90, 'sequence': 20},
                    {'name': 'فحص نظام الفرامل', 'duration': 45, 'sequence': 30},
                    {'name': 'فحص نظام التعليق', 'duration': 30, 'sequence': 40},
                    {'name': 'فحص النظام الكهربائي', 'duration': 40, 'sequence': 50},
                ]
            },
            {
                'name': 'الصيانة الدورية السنوية',
                'description': 'صيانة دورية كل 12 شهر - فحص سنوي شامل',
                'interval_type': 'time',
                'time_interval_months': 12,
                'operations': [
                    {'name': 'فحص شامل للمركبة', 'duration': 120, 'sequence': 10},
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 20},
                    {'name': 'تغيير جميع الفلاتر', 'duration': 60, 'sequence': 30},
                    {'name': 'صيانة نظام الفرامل', 'duration': 90, 'sequence': 40},
                    {'name': 'صيانة نظام التعليق', 'duration': 60, 'sequence': 50},
                    {'name': 'فحص وصيانة نظام التبريد', 'duration': 45, 'sequence': 60},
                    {'name': 'فحص النظام الكهربائي', 'duration': 60, 'sequence': 70},
                    {'name': 'فحص نظام العادم', 'duration': 30, 'sequence': 80},
                ]
            },
            # Combined schedules
            {
                'name': 'صيانة مزدوجة 15,000 كم / 6 أشهر',
                'description': 'صيانة كل 15,000 كم أو 6 أشهر أيهما أقرب',
                'interval_type': 'both',
                'mileage_interval': 15000,
                'time_interval_months': 6,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'تغيير فلتر الزيت', 'duration': 15, 'sequence': 20},
                    {'name': 'فحص نظام الفرامل', 'duration': 45, 'sequence': 30},
                    {'name': 'فحص الإطارات', 'duration': 20, 'sequence': 40},
                    {'name': 'فحص السوائل', 'duration': 15, 'sequence': 50},
                ]
            }
        ]
        
        created_count = 0
        for schedule_data in generic_schedules:
            created = self._create_schedule(schedule_data, '', '')
            if created:
                created_count += 1
                
        self.stdout.write(f'  ✓ Created {created_count} generic schedules')
        return created_count

    def _create_specific_schedules(self):
        """Create specific maintenance schedules for each make/model combination"""
        self.stdout.write('Creating specific maintenance schedules for each make/model...')
        
        # Get all vehicle makes and models
        vehicle_makes = VehicleMake.objects.filter(
            tenant_id=self.tenant_id,
            is_active=True
        ).prefetch_related('models')
        
        if not vehicle_makes.exists():
            self.stdout.write(
                self.style.WARNING('No vehicle makes found in database.')
            )
            return 0
            
        created_count = 0
        
        for make in vehicle_makes:
            models = make.models.filter(is_active=True)
            
            if not models.exists():
                # Create generic schedules for the make only
                created_count += self._create_make_specific_schedules(make.name, '')
            else:
                # Create schedules for each model
                for model in models:
                    created_count += self._create_make_specific_schedules(make.name, model.name)
                    
        self.stdout.write(f'  ✓ Created {created_count} specific schedules')
        return created_count

    def _create_make_specific_schedules(self, make_name, model_name):
        """Create maintenance schedules for a specific make/model"""
        model_display = f"{make_name} {model_name}".strip()
        
        # Define schedules based on vehicle class/type
        specific_schedules = [
            {
                'name': f'صيانة 5,000 كم - {model_display}',
                'description': f'صيانة دورية مخصصة لـ {model_display} كل 5,000 كم',
                'interval_type': 'mileage',
                'mileage_interval': 5000,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'تغيير فلتر الزيت', 'duration': 15, 'sequence': 20},
                    {'name': 'فحص مستوى السوائل', 'duration': 10, 'sequence': 30},
                ]
            },
            {
                'name': f'صيانة 10,000 كم - {model_display}',
                'description': f'صيانة دورية مخصصة لـ {model_display} كل 10,000 كم',
                'interval_type': 'mileage',
                'mileage_interval': 10000,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'تغيير فلتر الزيت', 'duration': 15, 'sequence': 20},
                    {'name': 'تغيير فلتر الهواء', 'duration': 20, 'sequence': 30},
                    {'name': 'فحص نظام الفرامل', 'duration': 45, 'sequence': 40},
                    {'name': 'فحص الإطارات', 'duration': 20, 'sequence': 50},
                ]
            },
            {
                'name': f'صيانة 30,000 كم - {model_display}',
                'description': f'صيانة كبرى مخصصة لـ {model_display} كل 30,000 كم',
                'interval_type': 'mileage',
                'mileage_interval': 30000,
                'operations': [
                    {'name': 'تغيير زيت المحرك', 'duration': 30, 'sequence': 10},
                    {'name': 'تغيير جميع الفلاتر', 'duration': 45, 'sequence': 20},
                    {'name': 'تغيير شمعات الإشعال', 'duration': 60, 'sequence': 30},
                    {'name': 'صيانة نظام الفرامل', 'duration': 90, 'sequence': 40},
                    {'name': 'فحص نظام التعليق', 'duration': 45, 'sequence': 50},
                    {'name': 'فحص نظام التبريد', 'duration': 30, 'sequence': 60},
                ]
            }
        ]
        
        # Add luxury car specific schedules for premium brands
        if any(brand in make_name.lower() for brand in ['bmw', 'مرسيدس', 'mercedes', 'audi', 'أودي', 'lexus', 'لكزس']):
            specific_schedules.append({
                'name': f'صيانة متميزة - {model_display}',
                'description': f'صيانة متميزة للسيارات الفاخرة - {model_display}',
                'interval_type': 'both',
                'mileage_interval': 10000,
                'time_interval_months': 6,
                'operations': [
                    {'name': 'فحص تشخيصي بالكمبيوتر', 'duration': 45, 'sequence': 10},
                    {'name': 'تغيير زيت محرك متميز', 'duration': 45, 'sequence': 20},
                    {'name': 'فحص أنظمة الأمان', 'duration': 60, 'sequence': 30},
                    {'name': 'فحص أنظمة الراحة', 'duration': 30, 'sequence': 40},
                    {'name': 'تنظيف وتلميع', 'duration': 90, 'sequence': 50},
                ]
            })
            
        created_count = 0
        for schedule_data in specific_schedules:
            created = self._create_schedule(schedule_data, make_name, model_name)
            if created:
                created_count += 1
                
        return created_count

    def _create_schedule(self, schedule_data, make_name, model_name):
        """Create a single maintenance schedule with its operations"""
        try:
            # Check if schedule already exists
            existing = MaintenanceSchedule.objects.filter(
                tenant_id=self.tenant_id,
                name=schedule_data['name'],
                vehicle_make=make_name,
                vehicle_model=model_name
            ).first()
            
            if existing and not self.force:
                self.stdout.write(f'    - Schedule already exists: {schedule_data["name"]}')
                return False
                
            if existing and self.force:
                # Delete existing schedule and its operations
                existing.delete()
                self.stdout.write(f'    - Deleted existing schedule: {schedule_data["name"]}')
            
            # Create the maintenance schedule
            schedule = MaintenanceSchedule.objects.create(
                tenant_id=self.tenant_id,
                name=schedule_data['name'],
                description=schedule_data['description'],
                interval_type=schedule_data['interval_type'],
                mileage_interval=schedule_data.get('mileage_interval'),
                time_interval_months=schedule_data.get('time_interval_months'),
                vehicle_make=make_name,
                vehicle_model=model_name,
                year_from=2010,  # Default year range
                year_to=2030,
                is_active=True
            )
            
            # Create operations for this schedule
            operations_created = 0
            for operation_data in schedule_data.get('operations', []):
                operation_type = random.choice(self.work_order_types) if self.work_order_types else None
                
                ScheduleOperation.objects.create(
                    tenant_id=self.tenant_id,
                    maintenance_schedule=schedule,
                    name=operation_data['name'],
                    description=f"عملية {operation_data['name']} ضمن {schedule.name}",
                    duration_minutes=operation_data['duration'],
                    sequence=operation_data['sequence'],
                    is_required=True,
                    operation_type=operation_type
                )
                operations_created += 1
                
            self.stdout.write(
                f'    ✓ Created: {schedule.name} ({operations_created} operations)'
            )
            return True
            
        except ValidationError as e:
            self.stdout.write(
                self.style.ERROR(f'    ✗ Validation error for {schedule_data["name"]}: {str(e)}')
            )
            return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'    ✗ Error creating {schedule_data["name"]}: {str(e)}')
            )
            return False 