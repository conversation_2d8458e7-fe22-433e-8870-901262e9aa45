from django.core.management.base import BaseCommand
from django.db import transaction
from work_orders.models import WorkOrderType
from core.utils import get_current_tenant_id


class Command(BaseCommand):
    help = 'Populate WorkOrderType entries with default pricing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            help='Tenant ID to populate data for',
        )

    def handle(self, *args, **options):
        tenant_id = options.get('tenant_id') or get_current_tenant_id()
        
        # Default operation types with pricing
        operation_types = [
            {'name': 'تغيير زيت المحرك', 'description': 'تغيير زيت المحرك وفلتر الزيت', 'price': 150.00},
            {'name': 'تغيير فلتر الزيت', 'description': 'استبدال فلتر الزيت', 'price': 45.00},
            {'name': 'فحص نظام الفرامل', 'description': 'فحص شامل لنظام الفرامل', 'price': 200.00},
            {'name': 'فحص الإطارات', 'description': 'فحص حالة الإطارات والضغط', 'price': 100.00},
            {'name': 'فحص مستوى السوائل', 'description': 'فحص مستوى السوائل المختلفة', 'price': 75.00},
            {'name': 'فحص فلتر الهواء', 'description': 'فحص وتنظيف فلتر الهواء', 'price': 85.00},
            {'name': 'تغيير زيت', 'description': 'تغيير زيت المحرك', 'price': 150.00},
            {'name': 'فحص فرامل', 'description': 'فحص نظام الفرامل', 'price': 200.00},
            {'name': 'صيانة دورية', 'description': 'صيانة دورية شاملة', 'price': 500.00},
            {'name': 'تصليح كهرباء', 'description': 'إصلاح الأعطال الكهربائية', 'price': 350.00},
            {'name': 'تصليح ميكانيكا', 'description': 'إصلاح الأعطال الميكانيكية', 'price': 600.00},
            {'name': 'تغيير فلتر هواء', 'description': 'استبدال فلتر الهواء', 'price': 100.00},
            {'name': 'ضبط محاذاة العجلات', 'description': 'ضبط زوايا العجلات', 'price': 250.00},
            {'name': 'تغيير بطارية', 'description': 'استبدال بطارية السيارة', 'price': 300.00},
            {'name': 'تغيير إطارات', 'description': 'استبدال الإطارات', 'price': 400.00},
            {'name': 'فحص شامل', 'description': 'فحص شامل للسيارة', 'price': 350.00},
        ]

        try:
            with transaction.atomic():
                created_count = 0
                updated_count = 0
                
                for op_data in operation_types:
                    operation_type, created = WorkOrderType.objects.get_or_create(
                        name=op_data['name'],
                        tenant_id=tenant_id,
                        defaults={
                            'description': op_data['description'],
                            'price': op_data['price'],
                            'is_active': True,
                        }
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'Created operation type: {operation_type.name} - {operation_type.price} EGP')
                        )
                    else:
                        # Update price if it's None or 0
                        if not operation_type.price or operation_type.price == 0:
                            operation_type.price = op_data['price']
                            operation_type.description = op_data['description']
                            operation_type.save()
                            updated_count += 1
                            self.stdout.write(
                                self.style.WARNING(f'Updated operation type: {operation_type.name} - {operation_type.price} EGP')
                            )

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully populated operation types. Created: {created_count}, Updated: {updated_count}'
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error populating operation types: {str(e)}')
            )
            raise 