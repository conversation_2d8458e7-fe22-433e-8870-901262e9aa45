# Generated by Django 4.2.20 on 2025-05-07 10:23

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BillOfMaterials',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('version', models.Char<PERSON>ield(default='1.0', max_length=50, verbose_name='Version')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('finished_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_finished_items', to='inventory.item', verbose_name='Finished Item')),
            ],
            options={
                'verbose_name': 'Bill of Materials',
                'verbose_name_plural': 'Bills of Materials',
                'ordering': ['name'],
                'unique_together': {('tenant_id', 'name', 'version')},
            },
        ),
        migrations.CreateModel(
            name='WorkOrder',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('work_order_number', models.CharField(max_length=50, unique=True, verbose_name='Work Order #')),
                ('description', models.TextField(verbose_name='Description')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20, verbose_name='Priority')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('planned', 'Planned'), ('in_progress', 'In Progress'), ('on_hold', 'On Hold'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='Status')),
                ('planned_start_date', models.DateTimeField(blank=True, null=True, verbose_name='Planned Start Date')),
                ('planned_end_date', models.DateTimeField(blank=True, null=True, verbose_name='Planned End Date')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='Actual Start Date')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='Actual End Date')),
                ('customer_name', models.CharField(blank=True, max_length=255, verbose_name='Customer Name')),
                ('customer_phone', models.CharField(blank=True, max_length=50, verbose_name='Customer Phone')),
                ('customer_email', models.CharField(blank=True, max_length=100, verbose_name='Customer Email')),
                ('service_item_serial', models.CharField(blank=True, max_length=100, verbose_name='Item Serial #')),
                ('warranty_status', models.BooleanField(default=False, verbose_name='Under Warranty')),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Estimated Cost')),
                ('actual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Actual Cost')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('bill_of_materials', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='work_orders', to='work_orders.billofmaterials', verbose_name='Bill of Materials')),
            ],
            options={
                'verbose_name': 'Work Order',
                'verbose_name_plural': 'Work Orders',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WorkOrderType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('color_code', models.CharField(blank=True, help_text='For UI display', max_length=20, verbose_name='Color Code')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Work Order Type',
                'verbose_name_plural': 'Work Order Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='WorkOrderOperation',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('sequence', models.PositiveIntegerField(default=10, verbose_name='Sequence')),
                ('name', models.CharField(max_length=100, verbose_name='Operation Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('duration_minutes', models.PositiveIntegerField(default=0, verbose_name='Duration (minutes)')),
                ('is_completed', models.BooleanField(default=False, verbose_name='Is Completed')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('work_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='work_orders.workorder', verbose_name='Work Order')),
            ],
            options={
                'verbose_name': 'Work Order Operation',
                'verbose_name_plural': 'Work Order Operations',
                'ordering': ['work_order', 'sequence'],
            },
        ),
        migrations.CreateModel(
            name='WorkOrderMaterial',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=5, max_digits=15, verbose_name='Quantity')),
                ('unit_of_measure', models.CharField(max_length=50, verbose_name='Unit of Measure')),
                ('is_consumed', models.BooleanField(default=False, verbose_name='Is Consumed')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='work_order_usages', to='inventory.item', verbose_name='Item')),
                ('work_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='materials', to='work_orders.workorder', verbose_name='Work Order')),
            ],
            options={
                'verbose_name': 'Work Order Material',
                'verbose_name_plural': 'Work Order Materials',
                'ordering': ['work_order', 'item__name'],
            },
        ),
        migrations.AddField(
            model_name='workorder',
            name='work_order_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='work_orders', to='work_orders.workordertype', verbose_name='Work Order Type'),
        ),
        migrations.CreateModel(
            name='BOMItem',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=5, max_digits=15, verbose_name='Quantity')),
                ('unit_of_measure', models.CharField(max_length=50, verbose_name='Unit of Measure')),
                ('is_optional', models.BooleanField(default=False, verbose_name='Is Optional')),
                ('sequence', models.PositiveIntegerField(default=10, verbose_name='Sequence')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='work_orders.billofmaterials', verbose_name='Bill of Materials')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_component_items', to='inventory.item', verbose_name='Component Item')),
            ],
            options={
                'verbose_name': 'BOM Item',
                'verbose_name_plural': 'BOM Items',
                'ordering': ['sequence'],
            },
        ),
    ]
