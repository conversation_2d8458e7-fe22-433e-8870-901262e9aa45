# Generated by Django 4.2.20 on 2025-05-08 09:57

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_item_category_item_item_type_vehiclecompatibility_and_more'),
        ('work_orders', '0002_workorder_service_center_workorder_vehicle'),
    ]

    operations = [
        migrations.CreateModel(
            name='MaintenanceSchedule',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('interval_type', models.CharField(choices=[('mileage', 'Mileage'), ('time', 'Time'), ('both', 'Both')], default='mileage', max_length=20, verbose_name='Interval Type')),
                ('mileage_interval', models.PositiveIntegerField(blank=True, null=True, verbose_name='Mileage Interval (km)')),
                ('time_interval_months', models.PositiveIntegerField(blank=True, null=True, verbose_name='Time Interval (months)')),
                ('vehicle_make', models.CharField(blank=True, max_length=100, verbose_name='Vehicle Make')),
                ('vehicle_model', models.CharField(blank=True, max_length=100, verbose_name='Vehicle Model')),
                ('year_from', models.PositiveIntegerField(blank=True, null=True, verbose_name='Year From')),
                ('year_to', models.PositiveIntegerField(blank=True, null=True, verbose_name='Year To')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Maintenance Schedule',
                'verbose_name_plural': 'Maintenance Schedules',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='workorder',
            name='current_odometer',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='Current Odometer Reading (km)'),
        ),
        migrations.AddField(
            model_name='workorder',
            name='fuel_level',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Fuel Level (%)'),
        ),
        migrations.AddField(
            model_name='workorder',
            name='operation_category',
            field=models.CharField(choices=[('custom', 'Custom'), ('scheduled', 'Scheduled Maintenance')], default='custom', max_length=20, verbose_name='Operation Category'),
        ),
        migrations.CreateModel(
            name='ScheduleOperation',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Operation Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('duration_minutes', models.PositiveIntegerField(default=0, verbose_name='Duration (minutes)')),
                ('sequence', models.PositiveIntegerField(default=10, verbose_name='Sequence')),
                ('is_required', models.BooleanField(default=True, verbose_name='Required')),
                ('maintenance_schedule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='work_orders.maintenanceschedule', verbose_name='Maintenance Schedule')),
                ('operation_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='schedule_operations', to='work_orders.workordertype', verbose_name='Operation Type')),
            ],
            options={
                'verbose_name': 'Schedule Operation',
                'verbose_name_plural': 'Schedule Operations',
                'ordering': ['maintenance_schedule', 'sequence'],
            },
        ),
        migrations.AddField(
            model_name='workorder',
            name='maintenance_schedule',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='work_orders', to='work_orders.maintenanceschedule', verbose_name='Maintenance Schedule'),
        ),
        migrations.CreateModel(
            name='OperationPart',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=5, default=1.0, max_digits=15, verbose_name='Quantity')),
                ('is_required', models.BooleanField(default=True, verbose_name='Required')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedule_parts', to='inventory.item', verbose_name='Item')),
                ('schedule_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parts', to='work_orders.scheduleoperation', verbose_name='Schedule Operation')),
            ],
            options={
                'verbose_name': 'Operation Part',
                'verbose_name_plural': 'Operation Parts',
                'unique_together': {('tenant_id', 'schedule_operation', 'item')},
            },
        ),
    ]
