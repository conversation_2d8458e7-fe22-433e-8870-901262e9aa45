# Generated by Django 4.2.20 on 2025-06-06 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("work_orders", "0004_workorder_customer"),
    ]

    operations = [
        migrations.AlterField(
            model_name="billofmaterials",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="bomitem",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="maintenanceschedule",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="operationpart",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="scheduleoperation",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="workorder",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="workordermaterial",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="workorderoperation",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
        migrations.AlterField(
            model_name="workordertype",
            name="tenant_id",
            field=models.UUIDField(
                db_index=True, editable=False, verbose_name="Tenant ID"
            ),
        ),
    ]
