# Generated by Django 4.2.20 on 2025-06-08 13:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("work_orders", "0006_alter_workorder_work_order_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="billofmaterials",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="bomitem",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="maintenanceschedule",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="operationpart",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="scheduleoperation",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="workorder",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="workordermaterial",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="workorderoperation",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.AlterField(
            model_name="workordertype",
            name="tenant_id",
            field=models.UUIDField(
                blank=True,
                db_index=True,
                editable=False,
                null=True,
                verbose_name="Tenant ID",
            ),
        ),
        migrations.CreateModel(
            name="WorkOrderHistory",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("planned", "Planned"),
                            ("in_progress", "In Progress"),
                            ("on_hold", "On Hold"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "changed_fields",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Changed Fields"
                    ),
                ),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="work_order_changes",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Changed By",
                    ),
                ),
                (
                    "work_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="history_entries",
                        to="work_orders.workorder",
                        verbose_name="Work Order",
                    ),
                ),
            ],
            options={
                "verbose_name": "Work Order History",
                "verbose_name_plural": "Work Order History",
                "ordering": ["-created_at"],
            },
        ),
    ]
