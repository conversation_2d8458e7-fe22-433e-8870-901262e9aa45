# Generated by Django 4.2.20 on 2025-06-08 16:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("work_orders", "0007_alter_billofmaterials_tenant_id_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="workorderhistory",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "Work Order History",
                "verbose_name_plural": "Work Order History Entries",
            },
        ),
        migrations.RemoveField(
            model_name="workorderhistory",
            name="changed_by",
        ),
        migrations.RemoveField(
            model_name="workorderhistory",
            name="changed_fields",
        ),
        migrations.RemoveField(
            model_name="workorderhistory",
            name="status",
        ),
        migrations.AddField(
            model_name="workorderhistory",
            name="action_type",
            field=models.CharField(
                choices=[
                    ("status_change", "Status Change"),
                    ("comment", "Comment Added"),
                    ("material_added", "Material Added"),
                    ("material_consumed", "Material Consumed"),
                    ("operation_added", "Operation Added"),
                    ("operation_completed", "Operation Completed"),
                    ("customer_update", "Customer Information Updated"),
                    ("vehicle_update", "Vehicle Information Updated"),
                    ("schedule_update", "Schedule Updated"),
                    ("assignment", "Work Order Assigned"),
                    ("cost_update", "Cost Updated"),
                    ("other", "Other"),
                ],
                default="other",
                max_length=50,
                verbose_name="Action Type",
            ),
        ),
        migrations.AddField(
            model_name="workorderhistory",
            name="attributes",
            field=models.JSONField(
                blank=True, default=dict, verbose_name="Custom Attributes"
            ),
        ),
        migrations.AddField(
            model_name="workorderhistory",
            name="description",
            field=models.TextField(
                default="Work order updated", verbose_name="Description"
            ),
        ),
        migrations.AddField(
            model_name="workorderhistory",
            name="new_value",
            field=models.TextField(blank=True, verbose_name="New Value"),
        ),
        migrations.AddField(
            model_name="workorderhistory",
            name="previous_value",
            field=models.TextField(blank=True, verbose_name="Previous Value"),
        ),
        migrations.AddField(
            model_name="workorderhistory",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="work_order_activities",
                to=settings.AUTH_USER_MODEL,
                verbose_name="User",
            ),
        ),
    ]
