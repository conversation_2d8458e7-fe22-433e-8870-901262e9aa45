# Generated by Django 4.2.20 on 2025-07-03 08:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("warehouse", "0010_warehousetimer_warehousetimerlog_and_more"),
        ("inventory", "0027_add_batch_tracking"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("work_orders", "0010_alter_workorder_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="WorkOrderTransferRequest",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "transfer_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="Transfer Number"
                    ),
                ),
                (
                    "requested_quantity",
                    models.DecimalField(
                        decimal_places=5,
                        max_digits=15,
                        verbose_name="Requested Quantity",
                    ),
                ),
                (
                    "transferred_quantity",
                    models.DecimalField(
                        decimal_places=5,
                        default=0,
                        max_digits=15,
                        verbose_name="Transferred Quantity",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("in_transit", "In Transit"),
                            ("completed", "Completed"),
                            ("rejected", "Rejected"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "approved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Approved At"
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Completed At"
                    ),
                ),
                (
                    "estimated_delivery_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Estimated Delivery Date"
                    ),
                ),
                (
                    "actual_delivery_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Actual Delivery Date"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "rejection_reason",
                    models.TextField(blank=True, verbose_name="Rejection Reason"),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_work_order_transfers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Approved By",
                    ),
                ),
                (
                    "destination_warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="incoming_work_order_transfers",
                        to="warehouse.location",
                        verbose_name="Destination Warehouse",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="work_order_transfers",
                        to="inventory.item",
                        verbose_name="Item",
                    ),
                ),
                (
                    "requested_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="requested_work_order_transfers",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Requested By",
                    ),
                ),
                (
                    "source_warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outgoing_work_order_transfers",
                        to="warehouse.location",
                        verbose_name="Source Warehouse",
                    ),
                ),
                (
                    "work_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transfer_requests",
                        to="work_orders.workorder",
                        verbose_name="Work Order",
                    ),
                ),
            ],
            options={
                "verbose_name": "Work Order Transfer Request",
                "verbose_name_plural": "Work Order Transfer Requests",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="WorkOrderTemplate",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=200, verbose_name="Template Name"),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("maintenance", "Maintenance"),
                            ("repair", "Repair"),
                            ("inspection", "Inspection"),
                            ("assembly", "Assembly"),
                            ("custom", "Custom"),
                        ],
                        default="custom",
                        max_length=20,
                        verbose_name="Template Type",
                    ),
                ),
                (
                    "compatible_makes",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Compatible Makes"
                    ),
                ),
                (
                    "compatible_models",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Compatible Models"
                    ),
                ),
                (
                    "year_from",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Year From"
                    ),
                ),
                (
                    "year_to",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="Year To"
                    ),
                ),
                (
                    "default_priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                        verbose_name="Default Priority",
                    ),
                ),
                (
                    "estimated_duration_hours",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=8,
                        null=True,
                        verbose_name="Estimated Duration (hours)",
                    ),
                ),
                (
                    "estimated_cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=15,
                        null=True,
                        verbose_name="Estimated Cost",
                    ),
                ),
                (
                    "operations_template",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of operation dictionaries",
                        verbose_name="Operations Template",
                    ),
                ),
                (
                    "materials_template",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of material dictionaries",
                        verbose_name="Materials Template",
                    ),
                ),
                (
                    "quality_checks_template",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of quality check dictionaries",
                        verbose_name="Quality Checks Template",
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(default=0, verbose_name="Usage Count"),
                ),
                (
                    "last_used_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Used At"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Is Active"),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=False,
                        help_text="Available to all users in tenant",
                        verbose_name="Is Public",
                    ),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="Tags"),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Category"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_templates",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Work Order Template",
                "verbose_name_plural": "Work Order Templates",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="WorkOrderQualityCheck",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "check_type",
                    models.CharField(
                        choices=[
                            ("pre_work", "Pre-Work Inspection"),
                            ("in_progress", "In-Progress Check"),
                            ("final", "Final Inspection"),
                            ("customer_approval", "Customer Approval"),
                        ],
                        max_length=30,
                        verbose_name="Check Type",
                    ),
                ),
                (
                    "check_name",
                    models.CharField(max_length=200, verbose_name="Check Name"),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Description"),
                ),
                (
                    "result",
                    models.CharField(
                        choices=[
                            ("pass", "Pass"),
                            ("fail", "Fail"),
                            ("conditional", "Conditional Pass"),
                            ("pending", "Pending"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Result",
                    ),
                ),
                (
                    "score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="Score (%)",
                    ),
                ),
                (
                    "checked_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Checked At"
                    ),
                ),
                (
                    "due_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Due Date"
                    ),
                ),
                ("findings", models.TextField(blank=True, verbose_name="Findings")),
                (
                    "recommendations",
                    models.TextField(blank=True, verbose_name="Recommendations"),
                ),
                (
                    "corrective_actions",
                    models.TextField(blank=True, verbose_name="Corrective Actions"),
                ),
                (
                    "is_required",
                    models.BooleanField(default=True, verbose_name="Required"),
                ),
                (
                    "is_blocking",
                    models.BooleanField(
                        default=False,
                        help_text="Prevents work order completion if failed",
                        verbose_name="Blocking",
                    ),
                ),
                (
                    "images",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Quality Check Images"
                    ),
                ),
                (
                    "attachments",
                    models.JSONField(
                        blank=True, default=list, verbose_name="Attachments"
                    ),
                ),
                (
                    "inspector",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="quality_inspections",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Inspector",
                    ),
                ),
                (
                    "work_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quality_checks",
                        to="work_orders.workorder",
                        verbose_name="Work Order",
                    ),
                ),
            ],
            options={
                "verbose_name": "Work Order Quality Check",
                "verbose_name_plural": "Work Order Quality Checks",
                "ordering": ["work_order", "check_type", "check_name"],
            },
        ),
        migrations.CreateModel(
            name="WorkOrderAllocation",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "allocated_quantity",
                    models.DecimalField(
                        decimal_places=5,
                        max_digits=15,
                        verbose_name="Allocated Quantity",
                    ),
                ),
                (
                    "consumed_quantity",
                    models.DecimalField(
                        decimal_places=5,
                        default=0,
                        max_digits=15,
                        verbose_name="Consumed Quantity",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("reserved", "Reserved"),
                            ("allocated", "Allocated"),
                            ("consumed", "Consumed"),
                            ("released", "Released"),
                            ("expired", "Expired"),
                        ],
                        default="reserved",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "allocated_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Allocated At"
                    ),
                ),
                (
                    "consumed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Consumed At"
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Expires At"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "allocated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="stock_allocations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Allocated By",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="work_order_allocations",
                        to="inventory.item",
                        verbose_name="Item",
                    ),
                ),
                (
                    "item_batch",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="work_order_allocations",
                        to="inventory.itembatch",
                        verbose_name="Item Batch",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="work_order_allocations",
                        to="warehouse.location",
                        verbose_name="Warehouse",
                    ),
                ),
                (
                    "work_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="allocations",
                        to="work_orders.workorder",
                        verbose_name="Work Order",
                    ),
                ),
                (
                    "work_order_material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="allocations",
                        to="work_orders.workordermaterial",
                        verbose_name="Work Order Material",
                    ),
                ),
            ],
            options={
                "verbose_name": "Work Order Allocation",
                "verbose_name_plural": "Work Order Allocations",
                "ordering": ["-allocated_at"],
            },
        ),
        migrations.CreateModel(
            name="WorkOrderActionLog",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        null=True,
                        verbose_name="Tenant ID",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("creation", "Creation"),
                            ("status", "Status Change"),
                            ("assignment", "Assignment"),
                            ("material", "Material Management"),
                            ("operation", "Operation Management"),
                            ("quality", "Quality Control"),
                            ("transfer", "Transfer Request"),
                            ("allocation", "Stock Allocation"),
                            ("notification", "Notification"),
                            ("customer", "Customer Interaction"),
                            ("system", "System Action"),
                            ("other", "Other"),
                        ],
                        default="other",
                        max_length=20,
                        verbose_name="Category",
                    ),
                ),
                ("action", models.CharField(max_length=100, verbose_name="Action")),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "previous_data",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="Previous Data"
                    ),
                ),
                (
                    "new_data",
                    models.JSONField(blank=True, default=dict, verbose_name="New Data"),
                ),
                (
                    "metadata",
                    models.JSONField(blank=True, default=dict, verbose_name="Metadata"),
                ),
                (
                    "source_ip",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="Source IP"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="User Agent")),
                (
                    "api_endpoint",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="API Endpoint"
                    ),
                ),
                (
                    "related_material",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="action_logs",
                        to="work_orders.workordermaterial",
                        verbose_name="Related Material",
                    ),
                ),
                (
                    "related_operation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="action_logs",
                        to="work_orders.workorderoperation",
                        verbose_name="Related Operation",
                    ),
                ),
                (
                    "related_transfer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="action_logs",
                        to="work_orders.workordertransferrequest",
                        verbose_name="Related Transfer",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="work_order_actions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
                (
                    "work_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="action_logs",
                        to="work_orders.workorder",
                        verbose_name="Work Order",
                    ),
                ),
            ],
            options={
                "verbose_name": "Work Order Action Log",
                "verbose_name_plural": "Work Order Action Logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="workordertransferrequest",
            index=models.Index(
                fields=["work_order", "status"], name="work_orders_work_or_10a92c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workordertransferrequest",
            index=models.Index(
                fields=["status", "priority"], name="work_orders_status_46f1a7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workordertemplate",
            index=models.Index(
                fields=["template_type", "is_active"],
                name="work_orders_templat_8b9a24_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="workordertemplate",
            index=models.Index(
                fields=["is_public", "is_active"], name="work_orders_is_publ_f59812_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workordertemplate",
            index=models.Index(
                fields=["usage_count"], name="work_orders_usage_c_9ddd10_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workorderqualitycheck",
            index=models.Index(
                fields=["work_order", "check_type"],
                name="work_orders_work_or_e35e68_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="workorderqualitycheck",
            index=models.Index(
                fields=["result", "is_blocking"], name="work_orders_result_144da6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workorderallocation",
            index=models.Index(
                fields=["work_order", "status"], name="work_orders_work_or_6f66ac_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workorderallocation",
            index=models.Index(
                fields=["item", "warehouse", "status"],
                name="work_orders_item_id_b6edac_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="workorderallocation",
            index=models.Index(
                fields=["status", "expires_at"], name="work_orders_status_1829b9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workorderactionlog",
            index=models.Index(
                fields=["work_order", "category"], name="work_orders_work_or_b46d5f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workorderactionlog",
            index=models.Index(
                fields=["user", "created_at"], name="work_orders_user_id_3d8eae_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workorderactionlog",
            index=models.Index(
                fields=["category", "action"], name="work_orders_categor_4f256f_idx"
            ),
        ),
    ]
