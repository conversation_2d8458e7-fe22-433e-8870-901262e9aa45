from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantModel
from core.querysets import BaseQuerySet
from setup.models import ServiceCenter, Vehicle, Customer
from django.core.exceptions import ValidationError
from django.conf import settings

class WorkOrderType(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Type of work order (e.g., repair, maintenance, assembly)"""
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    color_code = models.CharField(_("Color Code"), max_length=20, blank=True, help_text=_("For UI display"))
    price = models.DecimalField(_("Base Price"), max_digits=15, decimal_places=2, null=True, blank=True, help_text=_("Base price for this operation type"))
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order Type")
        verbose_name_plural = _("Work Order Types")
        ordering = ['name']

    def __str__(self):
        return self.name

class MaintenanceSchedule(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Standard maintenance schedule templates (e.g., 10,000km service)"""
    INTERVAL_TYPE_CHOICES = [
        ('mileage', _('Mileage')),
        ('time', _('Time')),
        ('both', _('Both')),
    ]
    
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    interval_type = models.CharField(_("Interval Type"), max_length=20, choices=INTERVAL_TYPE_CHOICES, default='mileage')
    mileage_interval = models.PositiveIntegerField(_("Mileage Interval (km)"), null=True, blank=True)
    time_interval_months = models.PositiveIntegerField(_("Time Interval (months)"), null=True, blank=True)
    vehicle_make = models.CharField(_("Vehicle Make"), max_length=100, blank=True)
    vehicle_model = models.CharField(_("Vehicle Model"), max_length=100, blank=True)
    year_from = models.PositiveIntegerField(_("Year From"), null=True, blank=True)
    year_to = models.PositiveIntegerField(_("Year To"), null=True, blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Maintenance Schedule")
        verbose_name_plural = _("Maintenance Schedules")
        ordering = ['name']
        
    def __str__(self):
        return self.name
        
    def clean(self):
        """Validate interval settings"""
        from django.core.exceptions import ValidationError
        
        if self.interval_type in ['mileage', 'both'] and not self.mileage_interval:
            raise ValidationError(_("Mileage interval is required for this interval type"))
            
        if self.interval_type in ['time', 'both'] and not self.time_interval_months:
            raise ValidationError(_("Time interval is required for this interval type"))
            
        super().clean()

    def suggest_compatible_parts(self, vehicle=None):
        """
        Suggest compatible parts for this maintenance schedule, 
        optionally filtered by specific vehicle
        
        Args:
            vehicle: Optional Vehicle instance to filter compatible parts
            
        Returns:
            List of compatible VehicleModelPart instances
        """
        from inventory.models import VehicleModelPart
        
        # Base query for parts linked to this schedule
        parts_query = VehicleModelPart.objects.filter(
            maintenance_schedule=self
        )
        
        # If vehicle is provided, filter by its details
        if vehicle:
            parts_query = parts_query.filter(
                make__exact=vehicle.make,
                model__exact=vehicle.model
            )
            
            # Filter by year if available
            if vehicle.year:
                parts_query = parts_query.filter(
                    year_from__lte=vehicle.year
                ).filter(
                    models.Q(year_to__isnull=True) |
                    models.Q(year_to__gte=vehicle.year)
                )
            
            # Filter by engine type if available
            if vehicle.engine_type:
                parts_query = parts_query.filter(
                    models.Q(engine_type='') |
                    models.Q(engine_type__exact=vehicle.engine_type)
                )
                
        return parts_query.select_related('item')
        
    def suggest_compatible_operations(self, vehicle=None):
        """
        Suggest compatible operations for this maintenance schedule,
        optionally filtered by specific vehicle
        
        Args:
            vehicle: Optional Vehicle instance to filter compatible operations
            
        Returns:
            List of compatible OperationCompatibility instances
        """
        from inventory.models import OperationCompatibility
        
        # Base query for operations linked to this schedule
        ops_query = OperationCompatibility.objects.filter(
            maintenance_schedule=self
        )
        
        # If vehicle is provided, filter by its details
        if vehicle:
            ops_query = ops_query.filter(
                models.Q(vehicle_make='') | 
                models.Q(vehicle_make__exact=vehicle.make)
            ).filter(
                models.Q(vehicle_model='') | 
                models.Q(vehicle_model__exact=vehicle.model)
            )
            
            # Filter by year if available
            if vehicle.year:
                ops_query = ops_query.filter(
                    models.Q(year_from__isnull=True) |
                    models.Q(year_from__lte=vehicle.year)
                ).filter(
                    models.Q(year_to__isnull=True) |
                    models.Q(year_to__gte=vehicle.year)
                )
            
            # Filter by engine type if available
            if vehicle.engine_type:
                ops_query = ops_query.filter(
                    models.Q(engine_type='') |
                    models.Q(engine_type__exact=vehicle.engine_type)
                )
                
        return ops_query.select_related('item', 'operation_type')

class ScheduleOperation(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Operations included in a maintenance schedule"""
    maintenance_schedule = models.ForeignKey(
        MaintenanceSchedule,
        on_delete=models.CASCADE,
        related_name="operations",
        verbose_name=_("Maintenance Schedule")
    )
    name = models.CharField(_("Operation Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    duration_minutes = models.PositiveIntegerField(_("Duration (minutes)"), default=0)
    sequence = models.PositiveIntegerField(_("Sequence"), default=10)
    is_required = models.BooleanField(_("Required"), default=True)
    operation_type = models.ForeignKey(
        WorkOrderType,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="schedule_operations",
        verbose_name=_("Operation Type")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Schedule Operation")
        verbose_name_plural = _("Schedule Operations")
        ordering = ['maintenance_schedule', 'sequence']
        
    def __str__(self):
        return f"{self.name} ({self.maintenance_schedule.name})"

class OperationPart(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Parts associated with schedule operations"""
    schedule_operation = models.ForeignKey(
        ScheduleOperation,
        on_delete=models.CASCADE,
        related_name="parts",
        verbose_name=_("Schedule Operation")
    )
    item = models.ForeignKey(
        'inventory.Item',
        on_delete=models.CASCADE,
        related_name="schedule_parts",
        verbose_name=_("Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=15, decimal_places=5, default=1.0)
    is_required = models.BooleanField(_("Required"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Operation Part")
        verbose_name_plural = _("Operation Parts")
        unique_together = [['tenant_id', 'schedule_operation', 'item']]
        
    def __str__(self):
        return f"{self.item.name} for {self.schedule_operation.name}"

class BillOfMaterials(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Bill of Materials for assemblies and manufacturing processes"""
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    finished_item = models.ForeignKey(
        'inventory.Item', 
        on_delete=models.CASCADE, 
        related_name="bom_finished_items",
        verbose_name=_("Finished Item")
    )
    version = models.CharField(_("Version"), max_length=50, default="1.0")
    is_active = models.BooleanField(_("Is Active"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Bill of Materials")
        verbose_name_plural = _("Bills of Materials")
        ordering = ['name']
        unique_together = [['tenant_id', 'name', 'version']]

    def __str__(self):
        return f"{self.name} (v{self.version})"

class BOMItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Individual item within a Bill of Materials"""
    bom = models.ForeignKey(
        BillOfMaterials, 
        on_delete=models.CASCADE, 
        related_name="items",
        verbose_name=_("Bill of Materials")
    )
    item = models.ForeignKey(
        'inventory.Item',
        on_delete=models.CASCADE, 
        related_name="bom_component_items",
        verbose_name=_("Component Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=15, decimal_places=5)
    unit_of_measure = models.CharField(_("Unit of Measure"), max_length=50)
    is_optional = models.BooleanField(_("Is Optional"), default=False)
    sequence = models.PositiveIntegerField(_("Sequence"), default=10)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("BOM Item")
        verbose_name_plural = _("BOM Items")
        ordering = ['sequence']

    def __str__(self):
        return f"{self.item.name} ({self.quantity} {self.unit_of_measure})"

class WorkOrder(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Work Order for repairs, maintenance or manufacturing"""
    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('critical', _('Critical')),
    ]
    
    STATUS_CHOICES = [
        ('planned', _('Reception')),
        ('in_progress', _('In Progress')),
        ('on_hold', _('On Hold')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]
    
    WORK_ORDER_TYPES = [
        ('custom', _('Custom')),
        ('scheduled', _('Scheduled Maintenance')),
    ]
    
    work_order_number = models.CharField(_("Work Order #"), max_length=50, unique=True)
    work_order_type = models.ForeignKey(
        WorkOrderType, 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="work_orders",
        verbose_name=_("Work Order Type")
    )
    bill_of_materials = models.ForeignKey(
        BillOfMaterials, 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="work_orders",
        verbose_name=_("Bill of Materials")
    )
    description = models.TextField(_("Description"))
    priority = models.CharField(_("Priority"), max_length=20, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='planned')
    operation_category = models.CharField(_("Operation Category"), max_length=20, choices=WORK_ORDER_TYPES, default='custom')
    maintenance_schedule = models.ForeignKey(
        MaintenanceSchedule,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="work_orders",
        verbose_name=_("Maintenance Schedule")
    )
    
    # Service Center and Vehicle Information
    service_center = models.ForeignKey(
        ServiceCenter,
        on_delete=models.PROTECT,
        related_name="work_orders",
        verbose_name=_("Service Center"),
        null=True, blank=True
    )
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.SET_NULL,
        related_name="work_orders",
        verbose_name=_("Vehicle"),
        null=True, blank=True
    )
    current_odometer = models.PositiveIntegerField(_("Current Odometer Reading (km)"), null=True, blank=True)
    fuel_level = models.DecimalField(_("Fuel Level (%)"), max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Scheduling
    planned_start_date = models.DateTimeField(_("Planned Start Date"), null=True, blank=True)
    planned_end_date = models.DateTimeField(_("Planned End Date"), null=True, blank=True)
    actual_start_date = models.DateTimeField(_("Actual Start Date"), null=True, blank=True)
    actual_end_date = models.DateTimeField(_("Actual End Date"), null=True, blank=True)
    
    # Customer Information
    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name="work_orders",
        verbose_name=_("Customer"),
        null=True, blank=True
    )
    # Legacy customer fields (to be migrated)
    customer_name = models.CharField(_("Customer Name"), max_length=255, blank=True)
    customer_phone = models.CharField(_("Customer Phone"), max_length=50, blank=True)
    customer_email = models.CharField(_("Customer Email"), max_length=100, blank=True)
    
    # Technician Assignment
    assigned_technician = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="assigned_work_orders",
        verbose_name=_("Assigned Technician"),
        help_text=_("Technician assigned to this work order")
    )
    
    # Service Information
    service_item_serial = models.CharField(_("Item Serial #"), max_length=100, blank=True)
    warranty_status = models.BooleanField(_("Under Warranty"), default=False)
    
    # Financial
    estimated_cost = models.DecimalField(_("Estimated Cost"), max_digits=15, decimal_places=2, null=True, blank=True)
    actual_cost = models.DecimalField(_("Actual Cost"), max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Other fields
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order")
        verbose_name_plural = _("Work Orders")
        ordering = ['-created_at']
        
    def __str__(self):
        return self.work_order_number
        
    def clean(self):
        """
        Validate that a vehicle doesn't have active work orders
        """
        if self.vehicle and not self.pk:  # Only for new work orders
            active_statuses = ['draft', 'planned', 'in_progress', 'on_hold']
            if WorkOrder.objects.filter(vehicle=self.vehicle, status__in=active_statuses).exists():
                raise ValidationError(_("This vehicle already has an active work order. Please complete or cancel it before creating a new one."))
        
        super().clean()
        
    def apply_maintenance_schedule(self):
        """Add operations and parts from the maintenance schedule to this work order"""
        if not self.maintenance_schedule:
            return
            
        # Create operations from the schedule
        for schedule_op in self.maintenance_schedule.operations.all():
            # Create the work order operation
            work_order_op = WorkOrderOperation.objects.create(
                tenant_id=self.tenant_id,
                work_order=self,
                sequence=schedule_op.sequence,
                name=schedule_op.name,
                description=schedule_op.description,
                duration_minutes=schedule_op.duration_minutes
            )
            
            # Add materials from the operation parts
            for part in schedule_op.parts.all():
                WorkOrderMaterial.objects.create(
                    tenant_id=self.tenant_id,
                    work_order=self,
                    item=part.item,
                    quantity=part.quantity,
                    unit_of_measure=part.item.unit_of_measurement.symbol if part.item.unit_of_measurement else '',
                    notes=part.notes
                )
    
    def auto_assign_customer_from_vehicle(self):
        """
        Automatically assign customer from vehicle if available
        """
        if not self.customer and self.vehicle and self.vehicle.owner:
            self.customer = self.vehicle.owner
            if not self.customer_name:
                self.customer_name = self.customer.full_name
            if not self.customer_phone:
                self.customer_phone = self.customer.phone
            if not self.customer_email:
                self.customer_email = self.customer.email
                
    def suggest_compatible_parts(self):
        """
        Suggest compatible parts based on the assigned vehicle and operation type
        
        Returns:
            List of compatible VehicleModelPart instances
        """
        from inventory.models import VehicleModelPart, OperationCompatibility
        
        if not self.vehicle:
            return []
            
        # 1. Check if we have a maintenance schedule
        if self.maintenance_schedule:
            return self.maintenance_schedule.suggest_compatible_parts(self.vehicle)
            
        # 2. Otherwise, find parts compatible with this vehicle and operation type
        vehicle = self.vehicle
        
        # Query for parts compatible with this vehicle
        parts_query = VehicleModelPart.objects.filter(
            make__exact=vehicle.make,
            model__exact=vehicle.model
        )
        
        # Filter by year if available
        if vehicle.year:
            parts_query = parts_query.filter(
                year_from__lte=vehicle.year
            ).filter(
                models.Q(year_to__isnull=True) |
                models.Q(year_to__gte=vehicle.year)
            )
        
        # Filter by engine type if available
        if hasattr(vehicle, 'engine_type') and vehicle.engine_type:
            parts_query = parts_query.filter(
                models.Q(engine_type='') |
                models.Q(engine_type__exact=vehicle.engine_type)
            )
            
        # 3. Also consider operation type compatibility
        if self.work_order_type:
            # Find compatible items for this operation type
            op_compat_items = OperationCompatibility.objects.filter(
                operation_type=self.work_order_type
            ).values_list('item_id', flat=True)
            
            # Union with vehicle-specific parts
            parts_query = parts_query.filter(
                models.Q(item_id__in=op_compat_items)
            )
            
        return parts_query.select_related('item').order_by('item__name')

class WorkOrderOperation(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Individual operation/step within a work order"""
    work_order = models.ForeignKey(
        WorkOrder, 
        on_delete=models.CASCADE, 
        related_name="operations",
        verbose_name=_("Work Order")
    )
    sequence = models.PositiveIntegerField(_("Sequence"), default=10)
    name = models.CharField(_("Operation Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    duration_minutes = models.PositiveIntegerField(_("Duration (minutes)"), default=0)
    is_completed = models.BooleanField(_("Is Completed"), default=False)
    completed_at = models.DateTimeField(_("Completed At"), null=True, blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order Operation")
        verbose_name_plural = _("Work Order Operations")
        ordering = ['work_order', 'sequence']

    def __str__(self):
        return f"{self.name} ({self.work_order.work_order_number})"

class WorkOrderMaterial(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Materials used in a work order"""
    work_order = models.ForeignKey(
        WorkOrder, 
        on_delete=models.CASCADE, 
        related_name="materials",
        verbose_name=_("Work Order")
    )
    item = models.ForeignKey(
        'inventory.Item',
        on_delete=models.CASCADE, 
        related_name="work_order_usages",
        verbose_name=_("Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=15, decimal_places=5)
    unit_of_measure = models.CharField(_("Unit of Measure"), max_length=50)
    is_consumed = models.BooleanField(_("Is Consumed"), default=False)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order Material")
        verbose_name_plural = _("Work Order Materials")
        ordering = ['work_order', 'item__name']

    def __str__(self):
        return f"{self.item.name} - {self.quantity} {self.unit_of_measure}"

class WorkOrderHistory(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """History of work order changes and activities"""
    ACTION_TYPES = [
        ('status_change', _('Status Change')),
        ('comment', _('Comment Added')),
        ('material_added', _('Material Added')),
        ('material_consumed', _('Material Consumed')),
        ('operation_added', _('Operation Added')),
        ('operation_completed', _('Operation Completed')),
        ('customer_update', _('Customer Information Updated')),
        ('vehicle_update', _('Vehicle Information Updated')),
        ('schedule_update', _('Schedule Updated')),
        ('assignment', _('Work Order Assigned')),
        ('cost_update', _('Cost Updated')),
        ('other', _('Other')),
    ]
    
    work_order = models.ForeignKey(
        WorkOrder, 
        on_delete=models.CASCADE, 
        related_name="history_entries",
        verbose_name=_("Work Order")
    )
    action_type = models.CharField(_("Action Type"), max_length=50, choices=ACTION_TYPES, default='other')
    description = models.TextField(_("Description"), default=_("Work order updated"))
    previous_value = models.TextField(_("Previous Value"), blank=True)
    new_value = models.TextField(_("New Value"), blank=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="work_order_activities",
        verbose_name=_("User")
    )
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order History")
        verbose_name_plural = _("Work Order History Entries")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.work_order.work_order_number} - {self.action_type} at {self.created_at.strftime('%Y-%m-%d %H:%M')}"

class WorkOrderTransferRequest(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Transfer requests for parts between warehouses for work orders"""
    TRANSFER_STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('in_transit', _('In Transit')),
        ('completed', _('Completed')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    ]
    
    work_order = models.ForeignKey(
        WorkOrder,
        on_delete=models.CASCADE,
        related_name="transfer_requests",
        verbose_name=_("Work Order")
    )
    transfer_number = models.CharField(_("Transfer Number"), max_length=50, unique=True)
    item = models.ForeignKey(
        'inventory.Item',
        on_delete=models.CASCADE,
        related_name="work_order_transfers",
        verbose_name=_("Item")
    )
    source_warehouse = models.ForeignKey(
        'warehouse.Location',
        on_delete=models.CASCADE,
        related_name="outgoing_work_order_transfers",
        verbose_name=_("Source Warehouse")
    )
    destination_warehouse = models.ForeignKey(
        'warehouse.Location',
        on_delete=models.CASCADE,
        related_name="incoming_work_order_transfers",
        verbose_name=_("Destination Warehouse")
    )
    requested_quantity = models.DecimalField(_("Requested Quantity"), max_digits=15, decimal_places=5)
    transferred_quantity = models.DecimalField(_("Transferred Quantity"), max_digits=15, decimal_places=5, default=0)
    status = models.CharField(_("Status"), max_length=20, choices=TRANSFER_STATUS_CHOICES, default='pending')
    priority = models.CharField(_("Priority"), max_length=20, choices=WorkOrder.PRIORITY_CHOICES, default='medium')
    
    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="requested_work_order_transfers",
        verbose_name=_("Requested By")
    )
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="approved_work_order_transfers",
        verbose_name=_("Approved By")
    )
    approved_at = models.DateTimeField(_("Approved At"), null=True, blank=True)
    completed_at = models.DateTimeField(_("Completed At"), null=True, blank=True)
    
    estimated_delivery_date = models.DateTimeField(_("Estimated Delivery Date"), null=True, blank=True)
    actual_delivery_date = models.DateTimeField(_("Actual Delivery Date"), null=True, blank=True)
    
    notes = models.TextField(_("Notes"), blank=True)
    rejection_reason = models.TextField(_("Rejection Reason"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Work Order Transfer Request")
        verbose_name_plural = _("Work Order Transfer Requests")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['work_order', 'status']),
            models.Index(fields=['status', 'priority']),
        ]
    
    def __str__(self):
        return f"{self.transfer_number} - {self.item.name} ({self.status})"

class WorkOrderAllocation(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Stock allocation records for work order materials"""
    ALLOCATION_STATUS_CHOICES = [
        ('reserved', _('Reserved')),
        ('allocated', _('Allocated')),
        ('consumed', _('Consumed')),
        ('released', _('Released')),
        ('expired', _('Expired')),
    ]
    
    work_order = models.ForeignKey(
        WorkOrder,
        on_delete=models.CASCADE,
        related_name="allocations",
        verbose_name=_("Work Order")
    )
    work_order_material = models.ForeignKey(
        WorkOrderMaterial,
        on_delete=models.CASCADE,
        related_name="allocations",
        verbose_name=_("Work Order Material")
    )
    item = models.ForeignKey(
        'inventory.Item',
        on_delete=models.CASCADE,
        related_name="work_order_allocations",
        verbose_name=_("Item")
    )
    warehouse = models.ForeignKey(
        'warehouse.Location',
        on_delete=models.CASCADE,
        related_name="work_order_allocations",
        verbose_name=_("Warehouse")
    )
    item_batch = models.ForeignKey(
        'inventory.ItemBatch',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="work_order_allocations",
        verbose_name=_("Item Batch")
    )
    
    allocated_quantity = models.DecimalField(_("Allocated Quantity"), max_digits=15, decimal_places=5)
    consumed_quantity = models.DecimalField(_("Consumed Quantity"), max_digits=15, decimal_places=5, default=0)
    status = models.CharField(_("Status"), max_length=20, choices=ALLOCATION_STATUS_CHOICES, default='reserved')
    
    allocated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="stock_allocations",
        verbose_name=_("Allocated By")
    )
    allocated_at = models.DateTimeField(_("Allocated At"), auto_now_add=True)
    consumed_at = models.DateTimeField(_("Consumed At"), null=True, blank=True)
    expires_at = models.DateTimeField(_("Expires At"), null=True, blank=True)
    
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Work Order Allocation")
        verbose_name_plural = _("Work Order Allocations")
        ordering = ['-allocated_at']
        indexes = [
            models.Index(fields=['work_order', 'status']),
            models.Index(fields=['item', 'warehouse', 'status']),
            models.Index(fields=['status', 'expires_at']),
        ]
    
    def __str__(self):
        return f"{self.item.name} - {self.allocated_quantity} ({self.status})"

class WorkOrderQualityCheck(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Quality control checks for work orders"""
    CHECK_TYPE_CHOICES = [
        ('pre_work', _('Pre-Work Inspection')),
        ('in_progress', _('In-Progress Check')),
        ('final', _('Final Inspection')),
        ('customer_approval', _('Customer Approval')),
    ]
    
    RESULT_CHOICES = [
        ('pass', _('Pass')),
        ('fail', _('Fail')),
        ('conditional', _('Conditional Pass')),
        ('pending', _('Pending')),
    ]
    
    work_order = models.ForeignKey(
        WorkOrder,
        on_delete=models.CASCADE,
        related_name="quality_checks",
        verbose_name=_("Work Order")
    )
    check_type = models.CharField(_("Check Type"), max_length=30, choices=CHECK_TYPE_CHOICES)
    check_name = models.CharField(_("Check Name"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    
    inspector = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="quality_inspections",
        verbose_name=_("Inspector")
    )
    
    result = models.CharField(_("Result"), max_length=20, choices=RESULT_CHOICES, default='pending')
    score = models.DecimalField(_("Score (%)"), max_digits=5, decimal_places=2, null=True, blank=True)
    
    checked_at = models.DateTimeField(_("Checked At"), null=True, blank=True)
    due_date = models.DateTimeField(_("Due Date"), null=True, blank=True)
    
    findings = models.TextField(_("Findings"), blank=True)
    recommendations = models.TextField(_("Recommendations"), blank=True)
    corrective_actions = models.TextField(_("Corrective Actions"), blank=True)
    
    is_required = models.BooleanField(_("Required"), default=True)
    is_blocking = models.BooleanField(_("Blocking"), default=False, help_text=_("Prevents work order completion if failed"))
    
    images = models.JSONField(_("Quality Check Images"), default=list, blank=True)
    attachments = models.JSONField(_("Attachments"), default=list, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Work Order Quality Check")
        verbose_name_plural = _("Work Order Quality Checks")
        ordering = ['work_order', 'check_type', 'check_name']
        indexes = [
            models.Index(fields=['work_order', 'check_type']),
            models.Index(fields=['result', 'is_blocking']),
        ]
    
    def __str__(self):
        return f"{self.work_order.work_order_number} - {self.check_name} ({self.result})"

class WorkOrderActionLog(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Detailed action logging for work orders with enhanced tracking"""
    ACTION_CATEGORIES = [
        ('creation', _('Creation')),
        ('status', _('Status Change')),
        ('assignment', _('Assignment')),
        ('material', _('Material Management')),
        ('operation', _('Operation Management')),
        ('quality', _('Quality Control')),
        ('transfer', _('Transfer Request')),
        ('allocation', _('Stock Allocation')),
        ('notification', _('Notification')),
        ('customer', _('Customer Interaction')),
        ('system', _('System Action')),
        ('other', _('Other')),
    ]
    
    work_order = models.ForeignKey(
        WorkOrder,
        on_delete=models.CASCADE,
        related_name="action_logs",
        verbose_name=_("Work Order")
    )
    category = models.CharField(_("Category"), max_length=20, choices=ACTION_CATEGORIES, default='other')
    action = models.CharField(_("Action"), max_length=100)
    description = models.TextField(_("Description"))
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="work_order_actions",
        verbose_name=_("User")
    )
    
    # Related objects for context
    related_material = models.ForeignKey(
        WorkOrderMaterial,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="action_logs",
        verbose_name=_("Related Material")
    )
    related_operation = models.ForeignKey(
        WorkOrderOperation,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="action_logs",
        verbose_name=_("Related Operation")
    )
    related_transfer = models.ForeignKey(
        WorkOrderTransferRequest,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="action_logs",
        verbose_name=_("Related Transfer")
    )
    
    # Data tracking
    previous_data = models.JSONField(_("Previous Data"), default=dict, blank=True)
    new_data = models.JSONField(_("New Data"), default=dict, blank=True)
    metadata = models.JSONField(_("Metadata"), default=dict, blank=True)
    
    # Source tracking
    source_ip = models.GenericIPAddressField(_("Source IP"), null=True, blank=True)
    user_agent = models.TextField(_("User Agent"), blank=True)
    api_endpoint = models.CharField(_("API Endpoint"), max_length=200, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Work Order Action Log")
        verbose_name_plural = _("Work Order Action Logs")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['work_order', 'category']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['category', 'action']),
        ]
    
    def __str__(self):
        return f"{self.work_order.work_order_number} - {self.action} by {self.user or 'System'}"

class WorkOrderTemplate(TimeStampedModel, UUIDPrimaryKeyModel, TenantModel):
    """Templates for standardized work orders"""
    TEMPLATE_TYPE_CHOICES = [
        ('maintenance', _('Maintenance')),
        ('repair', _('Repair')),
        ('inspection', _('Inspection')),
        ('assembly', _('Assembly')),
        ('custom', _('Custom')),
    ]
    
    name = models.CharField(_("Template Name"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    template_type = models.CharField(_("Template Type"), max_length=20, choices=TEMPLATE_TYPE_CHOICES, default='custom')
    
    # Vehicle compatibility
    compatible_makes = models.JSONField(_("Compatible Makes"), default=list, blank=True)
    compatible_models = models.JSONField(_("Compatible Models"), default=list, blank=True)
    year_from = models.PositiveIntegerField(_("Year From"), null=True, blank=True)
    year_to = models.PositiveIntegerField(_("Year To"), null=True, blank=True)
    
    # Template configuration
    default_priority = models.CharField(_("Default Priority"), max_length=20, choices=WorkOrder.PRIORITY_CHOICES, default='medium')
    estimated_duration_hours = models.DecimalField(_("Estimated Duration (hours)"), max_digits=8, decimal_places=2, null=True, blank=True)
    estimated_cost = models.DecimalField(_("Estimated Cost"), max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Template content
    operations_template = models.JSONField(_("Operations Template"), default=list, blank=True, help_text=_("List of operation dictionaries"))
    materials_template = models.JSONField(_("Materials Template"), default=list, blank=True, help_text=_("List of material dictionaries"))
    quality_checks_template = models.JSONField(_("Quality Checks Template"), default=list, blank=True, help_text=_("List of quality check dictionaries"))
    
    # Usage tracking
    usage_count = models.PositiveIntegerField(_("Usage Count"), default=0)
    last_used_at = models.DateTimeField(_("Last Used At"), null=True, blank=True)
    
    # Template metadata
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="created_templates",
        verbose_name=_("Created By")
    )
    is_active = models.BooleanField(_("Is Active"), default=True)
    is_public = models.BooleanField(_("Is Public"), default=False, help_text=_("Available to all users in tenant"))
    
    tags = models.JSONField(_("Tags"), default=list, blank=True)
    category = models.CharField(_("Category"), max_length=100, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Work Order Template")
        verbose_name_plural = _("Work Order Templates")
        ordering = ['name']
        indexes = [
            models.Index(fields=['template_type', 'is_active']),
            models.Index(fields=['is_public', 'is_active']),
            models.Index(fields=['usage_count']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.template_type})"
    
    def apply_to_work_order(self, work_order):
        """Apply this template to a work order"""
        from django.utils import timezone
        
        # Apply operations
        for i, op_data in enumerate(self.operations_template):
            WorkOrderOperation.objects.create(
                tenant_id=work_order.tenant_id,
                work_order=work_order,
                sequence=(i + 1) * 10,
                name=op_data.get('name', ''),
                description=op_data.get('description', ''),
                duration_minutes=op_data.get('duration_minutes', 0)
            )
        
        # Apply materials (requires item lookup)
        from inventory.models import Item
        for mat_data in enumerate(self.materials_template):
            try:
                item = Item.objects.get(
                    tenant_id=work_order.tenant_id,
                    sku=mat_data.get('sku', '')
                )
                WorkOrderMaterial.objects.create(
                    tenant_id=work_order.tenant_id,
                    work_order=work_order,
                    item=item,
                    quantity=mat_data.get('quantity', 1),
                    unit_of_measure=mat_data.get('unit_of_measure', ''),
                    notes=mat_data.get('notes', '')
                )
            except Item.DoesNotExist:
                continue
        
        # Apply quality checks
        for qc_data in enumerate(self.quality_checks_template):
            WorkOrderQualityCheck.objects.create(
                tenant_id=work_order.tenant_id,
                work_order=work_order,
                check_type=qc_data.get('check_type', 'final'),
                check_name=qc_data.get('check_name', ''),
                description=qc_data.get('description', ''),
                is_required=qc_data.get('is_required', True),
                is_blocking=qc_data.get('is_blocking', False)
            )
        
        # Update template usage
        self.usage_count += 1
        self.last_used_at = timezone.now()
        self.save(update_fields=['usage_count', 'last_used_at'])
        
        return True
