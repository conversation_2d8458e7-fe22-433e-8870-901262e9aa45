import logging
from django.db import transaction
from django.utils import timezone
from decimal import Decimal
from typing import Dict, List, Optional
from django.utils.translation import gettext_lazy as _

# Import the new pricing and inventory services
from inventory.services import DynamicPricingEngine, InventoryIntegrationService

logger = logging.getLogger(__name__)


class WorkOrderToSalesService:
    """Service for converting work orders to sales orders and invoices"""
    
    @staticmethod
    def process_completed_work_order(work_order):
        """
        Main entry point for processing completed work orders.
        Creates sales order and invoice from completed work order.
        
        Args:
            work_order: WorkOrder instance
            
        Returns:
            dict: {
                'success': bool,
                'sales_order': SalesOrder instance or None,
                'invoice': Invoice instance or None,
                'errors': list of error messages
            }
        """
        result = {
            'success': False,
            'sales_order': None,
            'invoice': None,
            'errors': []
        }
        
        try:
            # Validate work order
            if not work_order.customer:
                result['errors'].append('Work order must have a customer assigned')
                return result
                
            if work_order.status != 'completed':
                result['errors'].append('Work order must be completed')
                return result
            
            with transaction.atomic():
                # Create sales order
                sales_order_result = WorkOrderToSalesService.create_sales_order_from_work_order(work_order)
                
                if not sales_order_result['success']:
                    result['errors'].extend(sales_order_result['errors'])
                    return result
                
                result['sales_order'] = sales_order_result['sales_order']
                
                # Create invoice from sales order
                invoice_result = WorkOrderToSalesService.create_invoice_from_sales_order(sales_order_result['sales_order'])
                
                if not invoice_result['success']:
                    result['errors'].extend(invoice_result['errors'])
                    return result
                
                result['invoice'] = invoice_result['invoice']
                result['success'] = True
                
                logger.info(f"Successfully processed work order {work_order.work_order_number}: "
                          f"SO-{result['sales_order'].order_number}, "
                          f"INV-{result['invoice'].invoice_number}")
                
        except Exception as e:
            logger.error(f"Error processing work order {work_order.work_order_number}: {str(e)}")
            result['errors'].append(str(e))
            
        return result
    
    @staticmethod
    def create_sales_order_from_work_order(work_order):
        """
        Create a sales order from a completed work order.
        
        Args:
            work_order: WorkOrder instance
            
        Returns:
            dict: {
                'success': bool,
                'sales_order': SalesOrder instance or None,
                'errors': list of error messages
            }
        """
        from sales.models import SalesOrder, SalesOrderItem
        from inventory.models import Item
        
        result = {
            'success': False,
            'sales_order': None,
            'errors': []
        }
        
        try:
            # Check if sales order already exists
            existing_sales_order = SalesOrder.objects.filter(
                tenant_id=work_order.tenant_id,
                work_order=work_order
            ).first()
            
            if existing_sales_order:
                result['success'] = True
                result['sales_order'] = existing_sales_order
                return result
            
            with transaction.atomic():
                # Generate sales order number
                order_number = WorkOrderToSalesService._generate_sales_order_number(work_order.tenant_id)
                
                # Determine service type
                service_type = WorkOrderToSalesService._determine_service_type(work_order)
                
                # Create sales order
                sales_order = SalesOrder.objects.create(
                    tenant_id=work_order.tenant_id,
                    order_number=order_number,
                    customer=work_order.customer,
                    order_date=timezone.now().date(),
                    status='delivered',  # Mark as delivered since work is completed
                    shipping_address=getattr(work_order.customer, 'address', '') or '',
                    notes=f'Auto-generated from Work Order: {work_order.work_order_number}',
                    total_amount=Decimal('0.00'),
                    work_order=work_order,
                    work_order_number=work_order.work_order_number,
                    service_center=work_order.service_center,
                    vehicle=work_order.vehicle,
                    service_type=service_type,
                    service_completion_date=work_order.actual_end_date or timezone.now(),
                    technician=work_order.assigned_technician,
                    vehicle_odometer=work_order.current_odometer,
                    vehicle_condition_notes=work_order.notes,
                    labor_cost=Decimal('0.00'),
                    parts_cost=Decimal('0.00')
                )
                
                # Add operations as labor items
                labor_cost = WorkOrderToSalesService._add_labor_items(work_order, sales_order)
                
                # Add materials as parts items
                parts_cost = WorkOrderToSalesService._add_parts_items(work_order, sales_order)
                
                # Update totals
                sales_order.labor_cost = labor_cost
                sales_order.parts_cost = parts_cost
                sales_order.update_total_amount()
                
                result['success'] = True
                result['sales_order'] = sales_order
                
        except Exception as e:
            logger.error(f"Error creating sales order for work order {work_order.work_order_number}: {str(e)}")
            result['errors'].append(str(e))
            
        return result
    
    @staticmethod
    def create_invoice_from_sales_order(sales_order):
        """
        Create an invoice from a sales order.
        
        Args:
            sales_order: SalesOrder instance
            
        Returns:
            dict: {
                'success': bool,
                'invoice': Invoice instance or None,
                'errors': list of error messages
            }
        """
        from billing.models import Invoice, InvoiceItem
        from datetime import date, timedelta
        
        result = {
            'success': False,
            'invoice': None,
            'errors': []
        }
        
        try:
            # Check if invoice already exists
            from billing.models import Invoice
            existing_invoice = Invoice.objects.filter(
                tenant_id=sales_order.tenant_id,
                sales_order=sales_order
            ).first()
            
            if existing_invoice:
                result['success'] = True
                result['invoice'] = existing_invoice
                return result
            
            with transaction.atomic():
                # Generate invoice number
                invoice_number = WorkOrderToSalesService._generate_invoice_number(sales_order.tenant_id)
                
                # Create invoice
                invoice = Invoice.objects.create(
                    tenant_id=sales_order.tenant_id,
                    invoice_number=invoice_number,
                    sales_order=sales_order,
                    work_order=sales_order.work_order,
                    customer=sales_order.customer,
                    service_center=sales_order.service_center,
                    invoice_date=date.today(),
                    due_date=date.today() + timedelta(days=30),
                    status='issued',
                    subtotal=sales_order.total_amount,
                    tax_percentage=Decimal('14.0'),  # Default VAT
                    total_amount=sales_order.total_amount * Decimal('1.14'),
                    amount_due=sales_order.total_amount * Decimal('1.14'),
                    notes=f"Generated from Sales Order: {sales_order.order_number}"
                )
                
                # Create invoice items from sales order items
                for so_item in sales_order.items.all():
                    InvoiceItem.objects.create(
                        tenant_id=sales_order.tenant_id,
                        invoice=invoice,
                        item_type='part' if so_item.item_type == 'parts' else 'labor',
                        description=so_item.item.name if so_item.item else so_item.operation_description,
                        quantity=so_item.quantity,
                        unit_price=so_item.unit_price,
                        discount_amount=so_item.discount,
                        tax_percentage=Decimal('14.0'),
                        part_id=so_item.item.sku if so_item.item and hasattr(so_item.item, 'sku') else '',
                        work_order_material_id=so_item.work_order_material_id,
                        work_order_operation_id=so_item.work_order_operation_id
                    )
                
                result['success'] = True
                result['invoice'] = invoice
                
        except Exception as e:
            logger.error(f"Error creating invoice from sales order {sales_order.order_number}: {str(e)}")
            result['errors'].append(str(e))
            
        return result
    
    @staticmethod
    def _generate_sales_order_number(tenant_id):
        """Generate unique sales order number"""
        from sales.models import SalesOrder
        
        last_order = SalesOrder.objects.filter(
            tenant_id=tenant_id
        ).order_by('-created_at').first()
        
        if last_order and last_order.order_number:
            try:
                last_number = int(last_order.order_number.split('-')[-1])
                return f"SO-{last_number + 1:06d}"
            except (ValueError, IndexError):
                pass
        
        return f"SO-{timezone.now().strftime('%Y%m%d')}001"
    
    @staticmethod
    def _generate_invoice_number(tenant_id):
        """Generate unique invoice number"""
        from billing.models import Invoice
        
        last_invoice = Invoice.objects.filter(
            tenant_id=tenant_id
        ).order_by('-created_at').first()
        
        if last_invoice and last_invoice.invoice_number:
            try:
                last_number = int(last_invoice.invoice_number.split('-')[-1])
                return f"INV-{last_number + 1:06d}"
            except (ValueError, IndexError):
                pass
        
        return f"INV-{timezone.now().strftime('%Y%m%d')}001"
    
    @staticmethod
    def _determine_service_type(work_order):
        """Determine service type from work order"""
        if work_order.operation_category == 'scheduled':
            return 'maintenance'
        elif work_order.work_order_type:
            name = work_order.work_order_type.name.lower()
            if 'maintenance' in name:
                return 'maintenance'
            elif 'repair' in name:
                return 'repair'
            elif 'inspection' in name:
                return 'inspection'
        return 'custom'
    
    @staticmethod
    def _add_labor_items(work_order, sales_order):
        """Add labor items to sales order from work order operations"""
        from sales.models import SalesOrderItem
        from inventory.models import Item
        
        total_labor_cost = Decimal('0.00')
        
        for operation in work_order.operations.all():
            if operation.is_completed:
                # Create or get labor item
                labor_item, created = Item.objects.get_or_create(
                    tenant_id=work_order.tenant_id,
                    sku=f'LABOR-{operation.name.upper().replace(" ", "-")}',
                    defaults={
                        'name': f'Labor: {operation.name}',
                        'description': operation.description or f'Labor service for {operation.name}',
                        'category': 'service',
                        'unit_price': Decimal('5.00'),  # 5 EGP per minute
                        'quantity': Decimal('0.00'),
                        'min_stock_level': Decimal('0.00')
                    }
                )
                
                # Create sales order item
                SalesOrderItem.objects.create(
                    tenant_id=work_order.tenant_id,
                    sales_order=sales_order,
                    item=labor_item,
                    quantity=Decimal(str(operation.duration_minutes)),
                    unit_price=Decimal('5.00'),
                    discount=Decimal('0.00'),
                    item_type='labor',
                    work_order_operation_id=operation.id,
                    operation_duration=operation.duration_minutes,
                    operation_description=f'{operation.name}: {operation.description or ""}'
                )
                
                total_labor_cost += Decimal(str(operation.duration_minutes)) * Decimal('5.00')
        
        return total_labor_cost
    
    @staticmethod
    def _add_parts_items(work_order, sales_order):
        """Add parts items to sales order from work order materials"""
        from sales.models import SalesOrderItem
        
        total_parts_cost = Decimal('0.00')
        
        for material in work_order.materials.all():
            if material.is_consumed and material.item.unit_price:
                SalesOrderItem.objects.create(
                    tenant_id=work_order.tenant_id,
                    sales_order=sales_order,
                    item=material.item,
                    quantity=material.quantity,
                    unit_price=material.item.unit_price,
                    discount=Decimal('0.00'),
                    item_type='parts',
                    work_order_material_id=material.id,
                    operation_description=f'Material: {material.item.name} - {material.notes or ""}'
                )
                
                total_parts_cost += material.quantity * material.item.unit_price
        
        return total_parts_cost


class WorkOrderAllocationService:
    """Service for managing work order stock allocation and parts requests"""
    
    @staticmethod
    def allocate_stock_for_work_order(work_order, user=None):
        """
        Allocate stock for work order materials with real-time inventory validation.
        
        Args:
            work_order: WorkOrder instance
            user: User performing allocation
            
        Returns:
            dict: Allocation result with stock availability details
        """
        try:
            # Prepare parts list for inventory validation
            parts_list = []
            for material in work_order.materials.all():
                parts_list.append({
                    'item_id': str(material.item.id),
                    'quantity': float(material.quantity)
                })
            
            if not parts_list:
                return {
                    'success': True,
                    'message': 'No materials to allocate',
                    'availability_report': None
                }
            
            # Use new inventory integration service
            inventory_service = InventoryIntegrationService(work_order.tenant_id)
            availability_report = inventory_service.validate_stock_availability(
                parts_list, 
                warehouse_location=getattr(work_order.service_center, 'primary_warehouse', None)
            )
            
            result = {
                'success': availability_report['all_available'],
                'availability_report': availability_report,
                'missing_items': availability_report['missing_items'],
                'low_stock_items': availability_report['low_stock_items']
            }
            
            if availability_report['all_available']:
                result['message'] = 'All materials are available for allocation'
                
                # Create actual allocations (if WorkOrderAllocation model is available)
                try:
                    from work_orders.models import WorkOrderAllocation
                    allocations_created = []
                    
                    for material in work_order.materials.all():
                        # Create allocation record
                        allocation = WorkOrderAllocation.objects.create(
                            tenant_id=work_order.tenant_id,
                            work_order=work_order,
                            work_order_material=material,
                            item=material.item,
                            warehouse=getattr(work_order.service_center, 'primary_warehouse', None),
                            allocated_quantity=material.quantity,
                            status='reserved',
                            allocated_by=user
                        )
                        allocations_created.append(allocation.id)
                    
                    result['allocations_created'] = allocations_created
                    
                except ImportError:
                    # WorkOrderAllocation model not available yet
                    logger.info("WorkOrderAllocation model not available - skipping allocation creation")
                    result['message'] += ' (Allocation records not created - model not available)'
                
            else:
                # Auto-create parts requests for missing items
                parts_request_result = inventory_service.create_parts_request(
                    work_order, 
                    availability_report['missing_items'],
                    priority='high' if work_order.priority == 'critical' else work_order.priority
                )
                
                result['parts_requests'] = parts_request_result
                result['message'] = f"Missing {len(availability_report['missing_items'])} items. Parts requests created: {parts_request_result['successful_requests']}"
            
            return result
            
        except Exception as e:
            logger.error(f"Error allocating stock for work order {work_order.work_order_number}: {str(e)}")
            return {
                'success': False,
                'message': f'Error during stock allocation: {str(e)}',
                'availability_report': None
            }
    
    @staticmethod
    def request_part_transfer(work_order, item, source_warehouse, destination_warehouse, quantity, user):
        """
        Create part transfer request with enhanced inventory integration.
        
        Args:
            work_order: WorkOrder instance
            item: Item to transfer
            source_warehouse: Source warehouse
            destination_warehouse: Destination warehouse
            quantity: Quantity to transfer
            user: User requesting transfer
            
        Returns:
            dict: Transfer request result
        """
        try:
            from work_orders.models import WorkOrderTransferRequest
            import uuid
            
            # Generate transfer number
            transfer_number = f"WO-{work_order.work_order_number}-{uuid.uuid4().hex[:8].upper()}"
            
            # Create transfer request
            transfer_request = WorkOrderTransferRequest.objects.create(
                tenant_id=work_order.tenant_id,
                work_order=work_order,
                transfer_number=transfer_number,
                item=item,
                source_warehouse=source_warehouse,
                destination_warehouse=destination_warehouse,
                requested_quantity=quantity,
                status='pending',
                priority=work_order.priority,
                requested_by=user,
                notes=f"Transfer request for work order {work_order.work_order_number}"
            )
            
            # Send notification using the notification service
            try:
                from notifications.services import NotificationService
                notification_service = NotificationService(work_order.tenant_id)
                notification_service.notify_warehouse_staff_parts_request(transfer_request, work_order)
            except Exception as e:
                logger.warning(f"Failed to send transfer request notification: {str(e)}")
            
            return {
                'success': True,
                'transfer_request_id': str(transfer_request.id),
                'transfer_number': transfer_number,
                'message': 'Transfer request created successfully'
            }
            
        except Exception as e:
            logger.error(f"Error creating transfer request: {str(e)}")
            return {
                'success': False,
                'transfer_request_id': None,
                'message': f'Error creating transfer request: {str(e)}'
            }


class WorkOrderWorkflowService:
    """Service for managing work order workflows and status transitions"""
    
    @staticmethod
    def can_transition_to_status(work_order, new_status, user=None):
        """
        Check if work order can transition to new status with enhanced inventory validation.
        
        Args:
            work_order: WorkOrder instance
            new_status: Target status
            user: User attempting transition
            
        Returns:
            dict: {
                'can_transition': bool,
                'reason': str (if cannot transition),
                'inventory_report': dict (if relevant)
            }
        """
        current_status = work_order.status
        
        # Define allowed transitions
        allowed_transitions = {
            'planned': ['in_progress', 'cancelled'],
            'in_progress': ['completed', 'on_hold', 'cancelled'],
            'on_hold': ['in_progress', 'cancelled'],
            'completed': [],  # Cannot transition from completed
            'cancelled': []   # Cannot transition from cancelled
        }
        
        if new_status not in allowed_transitions.get(current_status, []):
            return {
                'can_transition': False,
                'reason': f'Cannot transition from {current_status} to {new_status}',
                'inventory_report': None
            }
        
        # Additional checks for specific transitions
        if new_status == 'in_progress':
            # Check if technician is assigned
            if not work_order.assigned_technician:
                return {
                    'can_transition': False,
                    'reason': 'Technician must be assigned before starting work',
                    'inventory_report': None
                }
            
            # Enhanced inventory validation using new service
            allocation_result = WorkOrderAllocationService.allocate_stock_for_work_order(work_order, user)
            if not allocation_result['success']:
                return {
                    'can_transition': False,
                    'reason': 'Required materials are not available in stock',
                    'inventory_report': allocation_result['availability_report']
                }
        
        elif new_status == 'completed':
            # Check if all operations are completed
            incomplete_operations = work_order.operations.filter(is_completed=False)
            if incomplete_operations.exists():
                return {
                    'can_transition': False,
                    'reason': 'All operations must be completed',
                    'inventory_report': None
                }
            
            # Check if all materials are consumed
            unconsumed_materials = work_order.materials.filter(is_consumed=False)
            if unconsumed_materials.exists():
                return {
                    'can_transition': False,
                    'reason': 'All materials must be consumed',
                    'inventory_report': None
                }
        
        return {
            'can_transition': True, 
            'reason': None,
            'inventory_report': None
        }
    
    @staticmethod
    def auto_complete_work_order_if_ready(work_order):
        """
        Automatically complete work order if all conditions are met with enhanced cost calculation.
        
        Args:
            work_order: WorkOrder instance
            
        Returns:
            dict: Auto-completion result with pricing details
        """
        from work_orders.utils import check_work_order_completion
        
        completion_check = check_work_order_completion(work_order)
        
        if completion_check['can_complete']:
            # Calculate final costs using dynamic pricing
            pricing_result = WorkOrderPricingService.calculate_work_order_estimate(work_order)
            
            work_order.status = 'completed'
            work_order.actual_end_date = timezone.now()
            work_order.actual_cost = pricing_result.get('total_cost', work_order.estimated_cost)
            work_order.save()
            
            # Process completed work order
            sales_result = WorkOrderToSalesService.process_completed_work_order(work_order)
            
            return {
                'success': True,
                'auto_completed': True,
                'pricing_result': pricing_result,
                'sales_order_created': sales_result['success'],
                'sales_result': sales_result
            }
        
        return {
            'success': False,
            'auto_completed': False,
            'reason': 'Work order completion requirements not met',
            'details': completion_check
        }


class WorkOrderPricingService:
    """Enhanced service for handling work order pricing using dynamic pricing engine"""
    
    @staticmethod
    def calculate_work_order_estimate(work_order, pricing_strategy='standard', force_recalculate=False):
        """
        Calculate estimated cost for work order using dynamic pricing engine.
        
        Args:
            work_order: WorkOrder instance
            pricing_strategy: Pricing strategy to use
            force_recalculate: Force recalculation even if pricing exists
            
        Returns:
            dict: Enhanced cost breakdown with dynamic pricing
        """
        try:
            # Check if we already have recent pricing (unless forced)
            if not force_recalculate and work_order.estimated_cost and work_order.estimated_cost > 0:
                # Return existing pricing with basic breakdown
                return WorkOrderPricingService._get_basic_cost_breakdown(work_order)
            
            # Prepare parts list for dynamic pricing
            parts_list = []
            for material in work_order.materials.all():
                parts_list.append({
                    'item_id': str(material.item.id),
                    'quantity': float(material.quantity)
                })
            
            # Use dynamic pricing engine
            pricing_engine = DynamicPricingEngine(work_order.tenant_id)
            
            # Get work order type ID
            operation_type_id = str(work_order.work_order_type.id) if work_order.work_order_type else None
            
            if operation_type_id:
                # Calculate with dynamic pricing
                pricing_result = pricing_engine.calculate_service_price(
                    operation_type_id=operation_type_id,
                    vehicle=work_order.vehicle,
                    service_center=work_order.service_center,
                    customer=work_order.customer,
                    parts_list=parts_list,
                    pricing_strategy=pricing_strategy,
                    target_date=timezone.now().date(),
                    context={
                        'work_order_priority': work_order.priority,
                        'maintenance_schedule': str(work_order.maintenance_schedule.id) if work_order.maintenance_schedule else None
                    }
                )
                
                if pricing_result['success']:
                    final_calc = pricing_result['final_calculation']
                    
                    return {
                        'success': True,
                        'method': 'dynamic_pricing',
                        'labor_cost': Decimal(str(final_calc.get('labor_cost', 0))),
                        'parts_cost': Decimal(str(final_calc.get('parts_cost', 0))),
                        'total_cost': Decimal(str(final_calc.get('total_price', 0))),
                        'base_service_price': Decimal(str(final_calc.get('base_service_price', 0))),
                        'seasonal_adjustments': final_calc.get('seasonal_adjustment', 0),
                        'rules_applied': pricing_result.get('rules_applied', []),
                        'pricing_strategy': pricing_strategy,
                        'calculation_log': pricing_result.get('calculation_log', []),
                        'parts_breakdown': pricing_result.get('parts_breakdown', []),
                        'raw_pricing_result': pricing_result
                    }
                else:
                    logger.warning(f"Dynamic pricing failed for work order {work_order.work_order_number}: {pricing_result.get('error')}")
            
            # Fallback to legacy pricing
            return WorkOrderPricingService._calculate_legacy_pricing(work_order)
            
        except Exception as e:
            logger.error(f"Error calculating work order estimate for {work_order.work_order_number}: {str(e)}")
            # Fallback to legacy pricing on error
            return WorkOrderPricingService._calculate_legacy_pricing(work_order)
    
    @staticmethod
    def _calculate_legacy_pricing(work_order):
        """Legacy pricing calculation as fallback"""
        labor_cost = Decimal('0.00')
        parts_cost = Decimal('0.00')
        
        # Calculate labor costs
        for operation in work_order.operations.all():
            # Default rate: 5 EGP per minute
            operation_cost = Decimal(str(operation.duration_minutes)) * Decimal('5.00')
            labor_cost += operation_cost
        
        # Calculate parts costs
        parts_breakdown = []
        for material in work_order.materials.all():
            if material.item.unit_price:
                material_cost = material.quantity * material.item.unit_price
                parts_cost += material_cost
                
                parts_breakdown.append({
                    'item_id': str(material.item.id),
                    'item_name': material.item.name,
                    'item_sku': material.item.sku,
                    'quantity': float(material.quantity),
                    'unit_price': float(material.item.unit_price),
                    'total_price': float(material_cost)
                })
        
        total_cost = labor_cost + parts_cost
        
        return {
            'success': True,
            'method': 'legacy_pricing',
            'labor_cost': labor_cost,
            'parts_cost': parts_cost,
            'total_cost': total_cost,
            'base_service_price': labor_cost,
            'pricing_strategy': 'legacy',
            'parts_breakdown': parts_breakdown,
            'operations_breakdown': [
                {
                    'name': op.name,
                    'duration': op.duration_minutes,
                    'cost': float(Decimal(str(op.duration_minutes)) * Decimal('5.00'))
                }
                for op in work_order.operations.all()
            ]
        }
    
    @staticmethod
    def _get_basic_cost_breakdown(work_order):
        """Get basic cost breakdown for existing pricing"""
        return {
            'success': True,
            'method': 'existing_pricing',
            'labor_cost': work_order.estimated_cost or Decimal('0.00'),
            'parts_cost': Decimal('0.00'),  # Not tracked separately in basic mode
            'total_cost': work_order.estimated_cost or Decimal('0.00'),
            'base_service_price': work_order.estimated_cost or Decimal('0.00'),
            'pricing_strategy': 'existing'
        }
    
    @staticmethod
    def update_work_order_costs(work_order, pricing_strategy='standard'):
        """
        Update work order estimated and actual costs using dynamic pricing.
        
        Args:
            work_order: WorkOrder instance
            pricing_strategy: Pricing strategy to use
            
        Returns:
            dict: Updated cost breakdown
        """
        cost_breakdown = WorkOrderPricingService.calculate_work_order_estimate(
            work_order, pricing_strategy, force_recalculate=True
        )
        
        if cost_breakdown['success']:
            work_order.estimated_cost = cost_breakdown['total_cost']
            
            # Update actual cost if work order is completed
            if work_order.status == 'completed':
                work_order.actual_cost = cost_breakdown['total_cost']
            
            work_order.save(update_fields=['estimated_cost', 'actual_cost'])
        
        return cost_breakdown
    
    @staticmethod
    def apply_pricing_strategy(work_order, strategy='standard'):
        """
        Apply specific pricing strategy to work order.
        
        Args:
            work_order: WorkOrder instance
            strategy: Pricing strategy ('standard', 'premium', 'economy', 'express', etc.)
            
        Returns:
            dict: Pricing result with strategy application
        """
        return WorkOrderPricingService.calculate_work_order_estimate(
            work_order, pricing_strategy=strategy, force_recalculate=True
        )
    
    @staticmethod
    def get_pricing_comparison(work_order):
        """
        Get pricing comparison across different strategies.
        
        Args:
            work_order: WorkOrder instance
            
        Returns:
            dict: Pricing comparison for all strategies
        """
        strategies = ['standard', 'premium', 'economy', 'express', 'warranty']
        comparison = {}
        
        for strategy in strategies:
            try:
                pricing_result = WorkOrderPricingService.calculate_work_order_estimate(
                    work_order, pricing_strategy=strategy, force_recalculate=True
                )
                comparison[strategy] = {
                    'total_cost': pricing_result.get('total_cost', 0),
                    'labor_cost': pricing_result.get('labor_cost', 0),
                    'parts_cost': pricing_result.get('parts_cost', 0),
                    'success': pricing_result.get('success', False)
                }
            except Exception as e:
                comparison[strategy] = {
                    'error': str(e),
                    'success': False
                }
        
        return {
            'comparison': comparison,
            'current_estimate': work_order.estimated_cost,
            'recommended_strategy': 'standard'  # Could be enhanced with business logic
        } 