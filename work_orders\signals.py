import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from .models import WorkOrder, WorkOrderMaterial, WorkOrderOperation, WorkOrderHistory
from .services import WorkOrderToSalesService
from notifications.services import NotificationService
from user_roles.models import UserRole
from django.urls import reverse
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=WorkOrder)
def work_order_pre_save(sender, instance, **kwargs):
    """Signal to handle pre-save actions for work orders"""
    # Check if this is an existing instance
    if instance.pk:
        try:
            # Get the original instance from database
            original = WorkOrder.objects.get(pk=instance.pk)
            
            # Store original status for history tracking
            instance._original_status = original.status
            instance._original_customer = original.customer
            instance._original_vehicle = original.vehicle
            instance._original_estimated_cost = original.estimated_cost
            instance._original_actual_cost = original.actual_cost
            instance._original_assigned_technician = original.assigned_technician
            
        except WorkOrder.DoesNotExist:
            # If it's a new instance, initialize attributes
            instance._original_status = None
            instance._original_customer = None
            instance._original_vehicle = None
            instance._original_estimated_cost = None
            instance._original_actual_cost = None
            instance._original_assigned_technician = None
    else:
        # If it's a new instance, initialize attributes
        instance._original_status = None
        instance._original_customer = None
        instance._original_vehicle = None
        instance._original_estimated_cost = None
        instance._original_actual_cost = None
        instance._original_assigned_technician = None

    # Auto-assign customer from vehicle if not set
    if not instance.customer and instance.vehicle and instance.vehicle.owner:
        instance.customer = instance.vehicle.owner

@receiver(post_save, sender=WorkOrder)
def work_order_post_save(sender, instance, created, **kwargs):
    """Signal to handle post-save actions for work orders"""
    from inventory.models import Item
    
    # Create history entry for new work order
    if created:
        # Create history entry for work order creation
        WorkOrderHistory.objects.create(
            work_order=instance,
            action_type='status_change',
            description='Work order created',
            new_value=instance.status,
            attributes={
                'status': instance.status,
                'priority': instance.priority,
                'is_creation': True
            }
        )
        
        # Notify about new work order creation
        try:
            _notify_work_order_created(instance)
        except Exception as e:
            logger.error(f"Error in work order creation notification for {instance.id}: {e}")
        return
    
    # Track status changes in history and notifications
    if hasattr(instance, '_original_status') and instance._original_status != instance.status:
        WorkOrderHistory.objects.create(
            work_order=instance,
            action_type='status_change',
            description=f'Status changed from {instance._original_status} to {instance.status}',
            previous_value=instance._original_status,
            new_value=instance.status,
            attributes={
                'status': instance.status
            }
        )
        
        # Send comprehensive status change notifications
        try:
            _notify_work_order_status_change(instance, instance._original_status, instance.status)
        except Exception as e:
            logger.error(f"Error in work order status change notification for {instance.id}: {e}")
        
        # Update timestamps based on status
        if instance.status == 'in_progress' and not instance.actual_start_date:
            instance.actual_start_date = timezone.now()
            instance.save(update_fields=['actual_start_date'])
        elif instance.status == 'completed' and not instance.actual_end_date:
            instance.actual_end_date = timezone.now()
            instance.save(update_fields=['actual_end_date'])
            
        # Auto-create sales order and invoice when work order is completed
        if instance.status == 'completed' and instance._original_status != 'completed':
            # Check if auto-creation is enabled
            from django.conf import settings
            if not getattr(settings, 'WORK_ORDER_AUTO_CREATE_SALES_ORDER', True):
                logger.info(f"Auto-creation disabled for work order {instance.work_order_number}")
                return
                
            try:
                logger.info(f"Work order {instance.work_order_number} completed, creating sales order and invoice...")
                result = WorkOrderToSalesService.process_completed_work_order(instance)
                
                if result['success']:
                    # Create history entry for sales order creation
                    WorkOrderHistory.objects.create(
                        work_order=instance,
                        action_type='other',
                        description=f'Sales order and invoice auto-created',
                        new_value=f"Sales Order: {result['sales_order'].order_number}, Invoice: {result['invoice'].invoice_number}",
                        attributes={
                            'sales_order_id': str(result['sales_order'].id),
                            'sales_order_number': result['sales_order'].order_number,
                            'invoice_id': str(result['invoice'].id),
                            'invoice_number': result['invoice'].invoice_number,
                            'auto_generated': True
                        }
                    )
                    logger.info(f"Successfully created sales order {result['sales_order'].order_number} and invoice {result['invoice'].invoice_number} for work order {instance.work_order_number}")
                else:
                    # Log errors but don't fail the work order completion
                    error_msg = '; '.join(result['errors'])
                    logger.error(f"Failed to auto-create sales order/invoice for work order {instance.work_order_number}: {error_msg}")
                    
                    # Create history entry for failure
                    WorkOrderHistory.objects.create(
                        work_order=instance,
                        action_type='other',
                        description=f'Failed to auto-create sales order and invoice: {error_msg}',
                        attributes={
                            'auto_generation_failed': True,
                            'errors': result['errors']
                        }
                    )
                    
            except Exception as e:
                logger.error(f"Unexpected error auto-creating sales order/invoice for work order {instance.work_order_number}: {str(e)}")
                
                # Create history entry for exception
                WorkOrderHistory.objects.create(
                    work_order=instance,
                    action_type='other',
                    description=f'Exception during auto-creation: {str(e)}',
                    attributes={
                        'auto_generation_exception': True,
                        'exception': str(e)
                    }
                )
            
    # Track technician assignment changes
    if hasattr(instance, '_original_assigned_technician') and instance._original_assigned_technician != instance.assigned_technician:
        try:
            _notify_technician_assignment_change(instance, instance._original_assigned_technician, instance.assigned_technician)
        except Exception as e:
            logger.error(f"Error in technician assignment notification for {instance.id}: {e}")
    
    # Track customer changes
    if hasattr(instance, '_original_customer') and instance._original_customer != instance.customer:
        previous_customer = instance._original_customer.full_name if instance._original_customer else 'None'
        new_customer = instance.customer.full_name if instance.customer else 'None'
        
        WorkOrderHistory.objects.create(
            work_order=instance,
            action_type='customer_update',
            description=f'Customer updated from {previous_customer} to {new_customer}',
            previous_value=previous_customer,
            new_value=new_customer
        )
        
    # Track vehicle changes
    if hasattr(instance, '_original_vehicle') and instance._original_vehicle != instance.vehicle:
        previous_vehicle = f"{instance._original_vehicle.make} {instance._original_vehicle.model}" if instance._original_vehicle else 'None'
        new_vehicle = f"{instance.vehicle.make} {instance.vehicle.model}" if instance.vehicle else 'None'
        
        WorkOrderHistory.objects.create(
            work_order=instance,
            action_type='vehicle_update',
            description=f'Vehicle updated from {previous_vehicle} to {new_vehicle}',
            previous_value=previous_vehicle,
            new_value=new_vehicle
        )
        
    # Track cost changes
    if (hasattr(instance, '_original_estimated_cost') and instance._original_estimated_cost != instance.estimated_cost) or \
       (hasattr(instance, '_original_actual_cost') and instance._original_actual_cost != instance.actual_cost):
        
        previous_cost = f"Estimated: {instance._original_estimated_cost}, Actual: {instance._original_actual_cost}"
        new_cost = f"Estimated: {instance.estimated_cost}, Actual: {instance.actual_cost}"
        
        WorkOrderHistory.objects.create(
            work_order=instance,
            action_type='cost_update',
            description=f'Cost updated',
            previous_value=previous_cost,
            new_value=new_cost
        )

@receiver(post_save, sender=WorkOrderMaterial)
def work_order_material_added(sender, instance, created, **kwargs):
    """Signal to handle post-save actions for work order materials"""
    if created:
        # Create history entry for material addition
        WorkOrderHistory.objects.create(
            work_order=instance.work_order,
            action_type='material_added',
            description=f'Material added: {instance.item.name}',
            new_value=f"{instance.quantity} {instance.unit_of_measure}",
            attributes={
                'material_id': str(instance.id),
                'item_id': str(instance.item.id),
                'item_name': instance.item.name,
                'quantity': float(instance.quantity),
                'unit': instance.unit_of_measure
            }
        )
        
        # Notify about material addition
        _notify_material_added_to_work_order(instance)
        
    elif hasattr(instance, '_changed_consumption') and instance._changed_consumption:
        # Create history entry for material consumption
        WorkOrderHistory.objects.create(
            work_order=instance.work_order,
            action_type='material_consumed',
            description=f'Material consumed: {instance.item.name}',
            new_value=f"{instance.quantity} {instance.unit_of_measure}",
            attributes={
                'material_id': str(instance.id),
                'item_id': str(instance.item.id),
                'item_name': instance.item.name,
                'quantity': float(instance.quantity),
                'unit': instance.unit_of_measure
            }
        )

@receiver(post_save, sender=WorkOrderOperation)
def work_order_operation_updated(sender, instance, created, **kwargs):
    """Signal to handle post-save actions for work order operations"""
    if created:
        # Create history entry for operation addition
        WorkOrderHistory.objects.create(
            work_order=instance.work_order,
            action_type='operation_added',
            description=f'Operation added: {instance.name}',
            new_value=f"Duration: {instance.duration_minutes} minutes",
            attributes={
                'operation_id': str(instance.id),
                'name': instance.name,
                'duration': instance.duration_minutes
            }
        )
    elif hasattr(instance, '_changed_completion') and instance._changed_completion:
        # Create history entry for operation completion
        WorkOrderHistory.objects.create(
            work_order=instance.work_order,
            action_type='operation_completed',
            description=f'Operation completed: {instance.name}',
            new_value=f"Completed at: {instance.completed_at.strftime('%Y-%m-%d %H:%M')}",
            attributes={
                'operation_id': str(instance.id),
                'name': instance.name,
                'duration': instance.duration_minutes,
                'completed_at': instance.completed_at.isoformat() if instance.completed_at else None
            }
        ) 

# ==================== NOTIFICATION HELPER FUNCTIONS ====================

def _notify_work_order_created(work_order):
    """Notify about new work order creation"""
    try:
        # Notify service advisors and managers
        staff = _get_users_by_roles([
            'service_advisor', 'service_center_manager', 'medium_center_manager'
        ], work_order.tenant_id)
        
        for staff_member in staff:
            NotificationService.create_notification(
                recipient=staff_member,
                notification_type_code='work_order_created',
                title=str(_("New Work Order Created - {wo_number}")).format(
                    wo_number=getattr(work_order, 'work_order_number', work_order.id)
                ),
                message=str(_("Work order for {customer} requires assignment and scheduling")).format(
                    customer=work_order.customer.full_name if work_order.customer else "Unknown Customer"
                ),
                priority='medium',
                action_required=True,
                action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                action_text=str(_("Assign Technician")),
                related_object_type='work_order',
                related_object_id=str(work_order.id),
                tenant_id=work_order.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_work_order_created: {e}")


def _notify_work_order_status_change(work_order, old_status, new_status):
    """Comprehensive notifications for work order status changes"""
    try:
        if new_status == 'approved':
            # Notify technicians for assignment
            technicians = _get_users_by_roles([
                'technician', 'senior_technician', 'lead_technician'
            ], work_order.tenant_id)
            
            for technician in technicians:
                NotificationService.create_action_item(
                    assigned_to=technician,
                    action_type='work_order_assignment',
                    title=str(_("Work Order Available for Assignment")).format(),
                    description=str(_("Work order {wo_number} approved and ready for assignment")).format(
                        wo_number=getattr(work_order, 'work_order_number', work_order.id)
                    ),
                    priority='medium',
                    action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                    related_object_type='work_order',
                    related_object_id=str(work_order.id),
                    metadata={
                        'wo_number': str(getattr(work_order, 'work_order_number', work_order.id)),
                        'customer': work_order.customer.full_name if work_order.customer else '',
                        'vehicle': f"{work_order.vehicle.make} {work_order.vehicle.model}" if work_order.vehicle else '',
                        'actions': [
                            {'type': 'accept', 'label': str(_('Accept Assignment')), 'class': 'bg-green-600 text-white'},
                            {'type': 'decline', 'label': str(_('Decline')), 'class': 'bg-red-600 text-white'},
                            {'type': 'need_info', 'label': str(_('Need More Info')), 'class': 'bg-blue-600 text-white'}
                        ]
                    },
                    tenant_id=work_order.tenant_id
                )
                
        elif new_status == 'in_progress':
            # Notify managers about work starting
            managers = _get_users_by_roles([
                'service_center_manager', 'service_advisor'
            ], work_order.tenant_id)
            
            for manager in managers:
                NotificationService.create_notification(
                    recipient=manager,
                    notification_type_code='work_order_started',
                    title=str(_("Work Order Started")).format(),
                    message=str(_("Work order {wo_number} is now in progress")).format(
                        wo_number=getattr(work_order, 'work_order_number', work_order.id)
                    ),
                    priority='low',
                    action_required=False,
                    action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                    action_text=str(_("Monitor Progress")),
                    related_object_type='work_order',
                    related_object_id=str(work_order.id),
                    tenant_id=work_order.tenant_id,
                    metadata={
                        'old_status': old_status,
                        'new_status': new_status
                    }
                )
                
            # Notify warehouse staff about parts request if materials exist
            materials = WorkOrderMaterial.objects.filter(work_order=work_order)
            if materials.exists():
                warehouse_staff = _get_users_by_roles([
                    'warehouse_manager', 'warehouse_staff', 'inventory_manager'
                ], work_order.tenant_id)
                
                for staff_member in warehouse_staff:
                    NotificationService.create_action_item(
                        assigned_to=staff_member,
                        action_type='parts_request',
                        title=str(_("Parts Request for Work Order")).format(),
                        description=str(_("Work order {wo_number} started - Please prepare required parts")).format(
                            wo_number=getattr(work_order, 'work_order_number', work_order.id)
                        ),
                        priority='high',
                        action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                        related_object_type='work_order',
                        related_object_id=str(work_order.id),
                        metadata={
                            'wo_number': str(getattr(work_order, 'work_order_number', work_order.id)),
                            'customer': work_order.customer.full_name if work_order.customer else '',
                            'vehicle': f"{work_order.vehicle.make} {work_order.vehicle.model}" if work_order.vehicle else '',
                            'technician': work_order.assigned_technician.get_full_name() if work_order.assigned_technician else '',
                            'materials_count': materials.count(),
                            'actions': [
                                {'type': 'prepare_parts', 'label': str(_('Prepare Parts')), 'class': 'bg-green-600 text-white'},
                                {'type': 'check_stock', 'label': str(_('Check Stock')), 'class': 'bg-blue-600 text-white'},
                                {'type': 'order_parts', 'label': str(_('Order Missing Parts')), 'class': 'bg-red-600 text-white'}
                            ]
                        },
                        tenant_id=work_order.tenant_id
                    )
                
        elif new_status == 'completed':
            # Notify quality control and cashier
            qc_and_billing = _get_users_by_roles([
                'quality_control', 'cashier', 'service_advisor'
            ], work_order.tenant_id)
            
            for staff_member in qc_and_billing:
                if 'quality_control' in [role.code for role in staff_member.user_roles.filter(is_active=True).values_list('role__code', flat=True)]:
                    # QC inspection action
                    NotificationService.create_action_item(
                        assigned_to=staff_member,
                        action_type='quality_inspection',
                        title=str(_("Quality Inspection Required")).format(),
                        description=str(_("Work order {wo_number} completed. Please perform quality inspection")).format(
                            wo_number=getattr(work_order, 'work_order_number', work_order.id)
                        ),
                        priority='high',
                        action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                        related_object_type='work_order',
                        related_object_id=str(work_order.id),
                        metadata={
                            'wo_number': str(getattr(work_order, 'work_order_number', work_order.id)),
                            'technician': work_order.assigned_technician.get_full_name() if work_order.assigned_technician else '',
                            'actions': [
                                {'type': 'approve_qc', 'label': str(_('Approve Quality')), 'class': 'bg-green-600 text-white'},
                                {'type': 'reject_qc', 'label': str(_('Reject - Rework')), 'class': 'bg-red-600 text-white'},
                                {'type': 'partial_qc', 'label': str(_('Partial Approval')), 'class': 'bg-yellow-600 text-white'}
                            ]
                        },
                        tenant_id=work_order.tenant_id
                    )
                    
                elif 'cashier' in [role.code for role in staff_member.user_roles.filter(is_active=True).values_list('role__code', flat=True)]:
                    # Invoice generation action
                    NotificationService.create_action_item(
                        assigned_to=staff_member,
                        action_type='generate_invoice',
                        title=str(_("Generate Invoice for Completed Work")).format(),
                        description=str(_("Work order {wo_number} completed. Please generate customer invoice")).format(
                            wo_number=getattr(work_order, 'work_order_number', work_order.id)
                        ),
                        priority='high',
                        action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                        related_object_type='work_order',
                        related_object_id=str(work_order.id),
                        metadata={
                            'wo_number': str(getattr(work_order, 'work_order_number', work_order.id)),
                            'customer': work_order.customer.full_name if work_order.customer else '',
                            'estimated_cost': float(work_order.estimated_cost) if work_order.estimated_cost else 0,
                            'actions': [
                                {'type': 'generate_invoice', 'label': str(_('Generate Invoice')), 'class': 'bg-green-600 text-white'},
                                {'type': 'review_costs', 'label': str(_('Review Costs First')), 'class': 'bg-blue-600 text-white'},
                                {'type': 'contact_customer', 'label': str(_('Contact Customer')), 'class': 'bg-yellow-600 text-white'}
                            ]
                        },
                        tenant_id=work_order.tenant_id
                    )
                    
        elif new_status == 'on_hold':
            # Notify about work order being put on hold
            managers = _get_users_by_roles([
                'service_center_manager', 'service_advisor'
            ], work_order.tenant_id)
            
            for manager in managers:
                NotificationService.create_action_item(
                    assigned_to=manager,
                    action_type='resolve_work_order_hold',
                    title=str(_("Work Order On Hold - Resolution Required")).format(),
                    description=str(_("Work order {wo_number} is on hold. Please resolve issues")).format(
                        wo_number=getattr(work_order, 'work_order_number', work_order.id)
                    ),
                    priority='high',
                    action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                    related_object_type='work_order',
                    related_object_id=str(work_order.id),
                    metadata={
                        'wo_number': str(getattr(work_order, 'work_order_number', work_order.id)),
                        'hold_reason': getattr(work_order, 'hold_reason', 'Not specified'),
                        'actions': [
                            {'type': 'resolve_hold', 'label': str(_('Resolve and Resume')), 'class': 'bg-green-600 text-white'},
                            {'type': 'escalate', 'label': str(_('Escalate Issue')), 'class': 'bg-red-600 text-white'},
                            {'type': 'contact_customer', 'label': str(_('Contact Customer')), 'class': 'bg-blue-600 text-white'},
                            {'type': 'order_parts', 'label': str(_('Order Missing Parts')), 'class': 'bg-purple-600 text-white'}
                        ]
                    },
                    tenant_id=work_order.tenant_id
                )
                
    except Exception as e:
        logger.error(f"Error in _notify_work_order_status_change: {e}")


def _notify_technician_assignment_change(work_order, old_technician, new_technician):
    """Notify about technician assignment changes"""
    try:
        # Notify the new technician
        if new_technician:
            NotificationService.create_notification(
                recipient=new_technician,
                notification_type_code='work_order_assigned',
                title=str(_("Work Order Assigned to You")).format(),
                message=str(_("Work order {wo_number} has been assigned to you")).format(
                    wo_number=getattr(work_order, 'work_order_number', work_order.id)
                ),
                priority='high',
                action_required=True,
                action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                action_text=str(_("Start Work")),
                related_object_type='work_order',
                related_object_id=str(work_order.id),
                tenant_id=work_order.tenant_id
            )
            
        # Notify the old technician if unassigned
        if old_technician and not new_technician:
            NotificationService.create_notification(
                recipient=old_technician,
                notification_type_code='work_order_unassigned',
                title=str(_("Work Order Unassigned")).format(),
                message=str(_("Work order {wo_number} has been unassigned from you")).format(
                    wo_number=getattr(work_order, 'work_order_number', work_order.id)
                ),
                priority='medium',
                action_required=False,
                action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order.pk}),
                action_text=str(_("View Details")),
                related_object_type='work_order',
                related_object_id=str(work_order.id),
                tenant_id=work_order.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_technician_assignment_change: {e}")


def _notify_material_added_to_work_order(work_order_material):
    """Notify about materials added to work orders"""
    try:
        item = work_order_material.item
        
        # Check if material needs approval (high value or critical)
        material_value = work_order_material.quantity * (item.cost_price if hasattr(item, 'cost_price') and item.cost_price else 0)
        is_critical = getattr(item, 'is_critical', False)
        
        if material_value > 500 or is_critical:
            # Notify parts manager for approval
            parts_managers = _get_users_by_roles([
                'parts_manager', 'inventory_manager', 'service_center_manager'
            ], work_order_material.tenant_id)
            
            for manager in parts_managers:
                NotificationService.create_action_item(
                    assigned_to=manager,
                    action_type='approve_material_request',
                    title=str(_("Material Request Approval Required")).format(),
                    description=str(_("High-value/critical material requested for work order {wo_number}")).format(
                        wo_number=getattr(work_order_material.work_order, 'work_order_number', work_order_material.work_order.id)
                    ),
                    priority='high' if is_critical else 'medium',
                    action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order_material.work_order.pk}),
                    related_object_type='work_order_material',
                    related_object_id=str(work_order_material.id),
                    metadata={
                        'material_name': item.name,
                        'quantity': float(work_order_material.quantity),
                        'estimated_value': float(material_value),
                        'is_critical': is_critical,
                        'wo_number': str(getattr(work_order_material.work_order, 'work_order_number', work_order_material.work_order.id)),
                        'actions': [
                            {'type': 'approve', 'label': str(_('Approve Material')), 'class': 'bg-green-600 text-white'},
                            {'type': 'deny', 'label': str(_('Reject Request')), 'class': 'bg-red-600 text-white'},
                            {'type': 'alternative', 'label': str(_('Suggest Alternative')), 'class': 'bg-blue-600 text-white'},
                            {'type': 'reduce_qty', 'label': str(_('Reduce Quantity')), 'class': 'bg-yellow-600 text-white'}
                        ]
                    },
                    tenant_id=work_order_material.tenant_id
                )
                
        # Notify warehouse to prepare materials
        warehouse_staff = _get_users_by_roles([
            'parts_clerk', 'warehouse_staff'
        ], work_order_material.tenant_id)
        
        for staff_member in warehouse_staff:
            NotificationService.create_notification(
                recipient=staff_member,
                notification_type_code='material_request',
                title=str(_("Material Request - Prepare for Work Order")).format(),
                message=str(_("Prepare {quantity} units of {item_name} for work order")).format(
                    quantity=work_order_material.quantity,
                    item_name=item.name
                ),
                priority='medium',
                action_required=True,
                action_url=reverse('work_orders:work_order_detail', kwargs={'pk': work_order_material.work_order.pk}),
                action_text=str(_("Prepare Material")),
                related_object_type='work_order_material',
                related_object_id=str(work_order_material.id),
                tenant_id=work_order_material.tenant_id
            )
            
    except Exception as e:
        logger.error(f"Error in _notify_material_added_to_work_order: {e}")


def _get_users_by_roles(role_codes, tenant_id):
    """Helper function to get users by role codes"""
    try:
        user_roles = UserRole.objects.filter(
            role__code__in=role_codes,
            tenant_id=tenant_id,
            is_active=True
        ).select_related('user', 'role')
        
        return [ur.user for ur in user_roles if ur.user.is_active]
    except Exception as e:
        logger.error(f"Error getting users by roles: {e}")
        return [] 