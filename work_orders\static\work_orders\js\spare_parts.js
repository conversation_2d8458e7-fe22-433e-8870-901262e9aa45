// Spare Parts functionality for Work Order form

document.addEventListener('DOMContentLoaded', function() {
    // Initialize spare parts functionality
    initSpareParts();
});

// Function to initialize spare parts functionality
function initSpareParts() {
    const partsStep = document.getElementById('step-spare-parts');
    if (!partsStep) return;
    
    // Elements
    const sparePartsLoading = document.querySelector('.spare-parts-loading');
    const sparePartsContainer = document.querySelector('.spare-parts-container');
    const sparePartsList = document.getElementById('spare-parts-list');
    const noPartsMessage = document.getElementById('no-parts-message');
    const selectedOperationDisplay = document.getElementById('selected-operation-display');
    const selectedVehicleDisplay = document.getElementById('selected-vehicle-display');
    
    // Listen for step navigation
    const formSteps = document.querySelectorAll('.form-step');
    const timelineItems = document.querySelectorAll('.timeline-item');
    const nextBtn = document.getElementById('next-step');
    const prevBtn = document.getElementById('prev-step');
    
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            // Check if we're moving to the spare parts step
            const currentStep = Array.from(formSteps).findIndex(step => 
                step.classList.contains('active'));
            
            if (currentStep === 2) { // Operations step (0-indexed)
                // We're moving to spare parts step
                setTimeout(loadCompatibleParts, 500);
            }
        });
    }
    
    // Initialize parts selection
    function loadCompatibleParts() {
        // Get current operation and vehicle
        const operationIds = getSelectedOperationIds();
        const vehicleId = document.getElementById('selected_vehicle_id').value;
        const serviceCenterId = document.getElementById('selected_service_center_id')?.value || '';
        
        if (operationIds.length === 0 || !vehicleId) {
            showSparePartsError('Please select at least one operation and a vehicle first');
            return;
        }
        
        // Show loading
        if (sparePartsLoading) sparePartsLoading.style.display = 'block';
        if (sparePartsContainer) sparePartsContainer.style.display = 'none';
        
        // Update operations display
        updateOperationsDisplay();
        
        // Set selected vehicle display
        if (selectedVehicleDisplay) {
            selectedVehicleDisplay.textContent = getSelectedVehicleDescription() || 'Unknown Vehicle';
        }
        
        // For each operation, fetch compatible parts
        let allParts = [];
        let completedRequests = 0;
        
        operationIds.forEach(operationId => {
            // Call API
            fetch(`/work-orders/api/get-spare-parts/?operation_id=${operationId}&vehicle_id=${vehicleId}&service_center_id=${serviceCenterId}`)
                .then(response => response.json())
                .then(data => {
                    completedRequests++;
                    
                    // Add parts to collection, avoiding duplicates
                    if (data.parts && data.parts.length > 0) {
                        data.parts.forEach(part => {
                            if (!allParts.some(p => p.id === part.id)) {
                                allParts.push(part);
                            }
                        });
                    }
                    
                    // If all requests are complete, render parts
                    if (completedRequests === operationIds.length) {
                        // Hide loading
                        if (sparePartsLoading) sparePartsLoading.style.display = 'none';
                        if (sparePartsContainer) sparePartsContainer.style.display = 'block';
                        
                        // Populate parts
                        renderSpareParts(allParts);
                    }
                })
                .catch(error => {
                    console.error('Error fetching spare parts:', error);
                    completedRequests++;
                    
                    // If all requests are complete, render parts
                    if (completedRequests === operationIds.length) {
                        // Hide loading
                        if (sparePartsLoading) sparePartsLoading.style.display = 'none';
                        if (sparePartsContainer) sparePartsContainer.style.display = 'block';
                        
                        // Populate parts
                        renderSpareParts(allParts);
                    }
                });
        });
    }
    
    // Function to update operations display
    function updateOperationsDisplay() {
        // Get selected operations
        const operationNames = getSelectedOperationNames();
        
        // Update the operations display in the spare parts step
        const operationsList = document.getElementById('parts-operations-list');
        if (operationsList) {
            operationsList.innerHTML = '';
            
            operationNames.forEach(name => {
                const li = document.createElement('li');
                li.textContent = name;
                operationsList.appendChild(li);
            });
        }
        
        // Update the selected operation display
        if (selectedOperationDisplay) {
            if (operationNames.length === 1) {
                selectedOperationDisplay.textContent = operationNames[0];
            } else {
                selectedOperationDisplay.textContent = `${operationNames.length} operations selected`;
            }
        }
    }
    
    // Function to render spare parts
    function renderSpareParts(parts) {
        // Clear existing parts
        if (sparePartsList) sparePartsList.innerHTML = '';
        
        if (parts.length === 0) {
            if (noPartsMessage) noPartsMessage.style.display = 'block';
            return;
        }
        
        if (noPartsMessage) noPartsMessage.style.display = 'none';
        
        // Template
        const template = document.getElementById('part-row-template');
        if (!template) return;
        
        // Add each part
        parts.forEach(part => {
            const row = document.importNode(template.content, true).querySelector('tr');
            
            // Set part ID
            row.dataset.partId = part.id;
            
            // Set part details
            row.querySelector('.part-name').textContent = part.name;
            row.querySelector('.part-sku').textContent = part.sku;
            row.querySelector('.part-unit').textContent = part.unit_of_measurement;
            row.querySelector('.part-price').textContent = `${part.unit_price} SAR`;
            
            // Set checkbox value
            const checkbox = row.querySelector('.part-select');
            if (checkbox) checkbox.value = part.id;
            
            // Set availability info
            const availabilityCell = row.querySelector('.part-availability');
            if (availabilityCell) {
                if (part.availability.available_qty > 0) {
                    availabilityCell.innerHTML = `<span class="text-green-600">${part.availability.available_qty} available</span>`;
                    
                    // Show other locations if any
                    if (part.availability.other_locations && part.availability.other_locations.length > 0) {
                        const otherLocations = part.availability.other_locations
                            .map(loc => `${loc.name}: ${loc.quantity}`)
                            .join('<br>');
                        
                        availabilityCell.innerHTML += `<br><span class="text-xs text-gray-500">Other locations:<br>${otherLocations}</span>`;
                    }
                } else {
                    availabilityCell.innerHTML = '<span class="text-red-600">Not available</span>';
                    
                    // Show other locations with stock
                    if (part.availability.other_locations && part.availability.other_locations.length > 0) {
                        const otherLocations = part.availability.other_locations
                            .filter(loc => loc.quantity > 0)
                            .map(loc => `${loc.name}: ${loc.quantity}`)
                            .join('<br>');
                        
                        if (otherLocations) {
                            availabilityCell.innerHTML += `<br><span class="text-xs text-blue-500">Available at:<br>${otherLocations}</span>`;
                            
                            // Show transfer button
                            const transferBtn = row.querySelector('.request-transfer-btn');
                            if (transferBtn) {
                                transferBtn.style.display = 'inline-block';
                                
                                // Add click handler for transfer button
                                transferBtn.addEventListener('click', function() {
                                    showTransferModal(part);
                                });
                            }
                        }
                    }
                }
            }
            
            // Add event listeners
            const addBtn = row.querySelector('.add-part-btn');
            const checkbox = row.querySelector('.part-select');
            const quantityInput = row.querySelector('.part-quantity');
            
            // Add button click
            if (addBtn) {
                addBtn.addEventListener('click', function() {
                    addPartToWorkOrder(part, parseInt(quantityInput.value) || 1);
                });
            }
            
            // Checkbox change
            if (checkbox) {
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        if (quantityInput) quantityInput.disabled = false;
                        if (addBtn) addBtn.disabled = false;
                    } else {
                        if (quantityInput) quantityInput.disabled = true;
                        if (addBtn) addBtn.disabled = true;
                    }
                });
            }
            
            // Add to the list
            if (sparePartsList) sparePartsList.appendChild(row);
        });
    }
    
    // Function to show spare parts error
    function showSparePartsError(message) {
        if (sparePartsLoading) sparePartsLoading.style.display = 'none';
        if (sparePartsContainer) sparePartsContainer.style.display = 'block';
        if (noPartsMessage) {
            noPartsMessage.style.display = 'block';
            noPartsMessage.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        }
    }
    
    // Function to add part to work order
    function addPartToWorkOrder(part, quantity) {
        console.log('Adding part to work order:', part, 'Quantity:', quantity);
        
        // Here you would typically add the part to a list or to the form data
        // For this example, we'll just show a notification
        showNotification(`Added ${quantity} ${part.name} to work order`, 'success');
        
        // Disable the row after adding
        const row = document.querySelector(`tr[data-part-id="${part.id}"]`);
        if (row) {
            row.classList.add('opacity-50');
            const addBtn = row.querySelector('.add-part-btn');
            if (addBtn) addBtn.disabled = true;
            
            const checkbox = row.querySelector('.part-select');
            if (checkbox) checkbox.disabled = true;
            
            const quantityInput = row.querySelector('.part-quantity');
            if (quantityInput) quantityInput.disabled = true;
        }
    }
    
    // Function to show transfer modal
    function showTransferModal(part) {
        const modal = document.getElementById('transferRequestModal');
        if (!modal) return;
        
        // Set part info
        const partNameElement = document.getElementById('transfer-part-name');
        if (partNameElement) {
            partNameElement.textContent = `${part.name} (${part.sku})`;
        }
        
        // Set current warehouse
        const currentWarehouse = part.availability.current_location ? 
            part.availability.current_location.name : 'Not available at current location';
        
        const warehouseElement = document.getElementById('transfer-current-warehouse');
        if (warehouseElement) {
            warehouseElement.textContent = currentWarehouse;
        }
        
        // Populate source locations
        const sourceSelect = document.getElementById('transfer-source-location');
        if (sourceSelect) {
            sourceSelect.innerHTML = '<option value="">Select location</option>';
            
            if (part.availability.other_locations && part.availability.other_locations.length > 0) {
                part.availability.other_locations.forEach(loc => {
                    if (loc.quantity > 0) {
                        const option = document.createElement('option');
                        option.value = loc.id;
                        option.textContent = `${loc.name} (${loc.quantity} available)`;
                        sourceSelect.appendChild(option);
                    }
                });
            }
            
            // Set max quantity based on selected location
            sourceSelect.addEventListener('change', function() {
                const selectedLocationId = this.value;
                if (!selectedLocationId) return;
                
                const selectedLocation = part.availability.other_locations.find(loc => loc.id === selectedLocationId);
                if (selectedLocation) {
                    const quantityInput = document.getElementById('transfer-quantity');
                    if (quantityInput) {
                        quantityInput.max = selectedLocation.quantity;
                        quantityInput.value = Math.min(1, selectedLocation.quantity);
                    }
                }
            });
        }
        
        // Handle submit button
        const submitBtn = document.getElementById('submit-transfer-request');
        if (submitBtn) {
            submitBtn.onclick = function() {
                const sourceLocationId = sourceSelect ? sourceSelect.value : '';
                const quantity = document.getElementById('transfer-quantity') ? 
                    document.getElementById('transfer-quantity').value : '';
                const notes = document.getElementById('transfer-notes') ? 
                    document.getElementById('transfer-notes').value : '';
                
                if (!sourceLocationId || !quantity) {
                    alert('Please select a source location and enter a quantity');
                    return;
                }
                
                // Here you would make an API call to create a transfer request
                console.log('Transfer request:', {
                    partId: part.id,
                    sourceLocationId,
                    quantity,
                    notes
                });
                
                // Show success notification
                showNotification('Transfer request created successfully', 'success');
                
                // Close modal (using Bootstrap JS)
                if (typeof bootstrap !== 'undefined') {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    } else {
                        // Fallback if Bootstrap JS is not available
                        modal.style.display = 'none';
                    }
                } else {
                    // Simple fallback
                    modal.style.display = 'none';
                }
            };
        }
        
        // Show modal (using Bootstrap JS)
        if (typeof bootstrap !== 'undefined') {
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        } else {
            // Simple fallback
            modal.style.display = 'block';
        }
    }
    
    // Helper function to show notification
    function showNotification(message, type = 'info') {
        // Check if we have a showNotification function in the parent scope
        if (typeof window.showNotification === 'function') {
            window.showNotification(message, type);
            return;
        }
        
        // Create notification element if it doesn't exist
        let notificationEl = document.getElementById('notification');
        if (!notificationEl) {
            notificationEl = document.createElement('div');
            notificationEl.id = 'notification';
            notificationEl.className = 'fixed bottom-4 right-4 z-[99999] p-4 rounded-lg shadow-lg transform transition-all duration-300 opacity-0 translate-y-8';
            document.body.appendChild(notificationEl);
        }
        
        // Set notification type
        let bgColor = 'bg-blue-500';
        switch (type) {
            case 'success':
                bgColor = 'bg-green-500';
                break;
            case 'error':
                bgColor = 'bg-red-500';
                break;
            case 'warning':
                bgColor = 'bg-yellow-500';
                break;
        }
        
        // Set content and styles
        notificationEl.className = `fixed bottom-4 right-4 z-[99999] p-4 rounded-lg shadow-lg transform transition-all duration-300 opacity-0 translate-y-8 text-white ${bgColor}`;
        notificationEl.innerHTML = message;
        
        // Show notification
        setTimeout(() => {
            notificationEl.classList.remove('opacity-0', 'translate-y-8');
        }, 10);
        
        // Hide after 5 seconds
        setTimeout(() => {
            notificationEl.classList.add('opacity-0', 'translate-y-8');
            setTimeout(() => {
                notificationEl.remove();
            }, 300);
        }, 5000);
    }
    
    // Helper functions to get selected operations and vehicle
    function getSelectedOperationIds() {
        // This depends on your operations step implementation
        const ids = [];
        
        // Try different selectors based on your form structure
        // First try operation items with selected class
        document.querySelectorAll('.operation-item.selected').forEach(item => {
            const id = item.dataset.id;
            if (id) ids.push(id);
        });
        
        // Try checkboxes
        document.querySelectorAll('.operation-id-input:checked').forEach(input => {
            const id = input.value;
            if (id) ids.push(id);
        });
        
        // Try other possible selectors
        document.querySelectorAll('[name="operation_ids[]"]:checked, [name="scheduled_operation_ids[]"]:checked, [name="custom_operation_ids[]"]:checked').forEach(input => {
            const id = input.value;
            if (id) ids.push(id);
        });
        
        return ids;
    }
    
    function getSelectedOperationNames() {
        const names = [];
        
        // Try different selectors based on your form structure
        // First try operation items with selected class
        document.querySelectorAll('.operation-item.selected').forEach(item => {
            const nameElement = item.querySelector('.operation-name');
            if (nameElement) names.push(nameElement.textContent);
        });
        
        // Try checkboxes
        document.querySelectorAll('.operation-id-input:checked').forEach(input => {
            const item = input.closest('.operation-item');
            if (item) {
                const nameElement = item.querySelector('.operation-name');
                if (nameElement) names.push(nameElement.textContent);
            }
        });
        
        // Try operation description selects
        document.querySelectorAll('[id^="operation_description_"]').forEach(select => {
            const selectedOption = select.options[select.selectedIndex];
            if (selectedOption) names.push(selectedOption.textContent);
        });
        
        return names.length > 0 ? names : ['Selected Operation'];
    }
    
    function getSelectedVehicleDescription() {
        // Get vehicle make, model, year from form
        const vehicleId = document.getElementById('selected_vehicle_id').value;
        if (!vehicleId) return null;
        
        const vehicleDetails = document.getElementById('selected-vehicle-details')?.querySelector('span')?.textContent;
        return vehicleDetails || 'Selected Vehicle';
    }
} 