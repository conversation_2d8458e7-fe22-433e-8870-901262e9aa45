/**
 * Vehicle Form Dynamic Handling
 * Handles dynamic data loading and interactions for the vehicle form
 */

document.addEventListener('DOMContentLoaded', function() {
    initVehicleMakeSelect();
    setupModelDependency();
    setupFormSubmission();
});

/**
 * Initialize the vehicle make select with data from the server
 */
function initVehicleMakeSelect() {
    const makeSelect = document.getElementById('vehicle_make');
    
    if (!makeSelect) return;
    
    // Convert to select2 if available
    if (typeof $.fn.select2 !== 'undefined') {
        $(makeSelect).select2({
            placeholder: gettext('اختر الشركة المصنعة'),
            allowClear: true,
            ajax: {
                url: '/work_orders/api/select/vehicle-makes/',
                dataType: 'json',
                delay: 250,
                cache: true,
                processResults: function(data) {
                    return {
                        results: data.results
                    };
                }
            }
        });
        
        // When a make is selected, update the model dropdown
        $(makeSelect).on('change', function() {
            const modelSelect = document.getElementById('vehicle_model');
            if (modelSelect) {
                // Clear the model select
                if (typeof $.fn.select2 !== 'undefined') {
                    $(modelSelect).val(null).trigger('change');
                } else {
                    modelSelect.innerHTML = '';
                    const placeholderOption = document.createElement('option');
                    placeholderOption.value = '';
                    placeholderOption.text = gettext('اختر الموديل');
                    modelSelect.appendChild(placeholderOption);
                }
            }
        });
    } else {
        // Fallback for non-select2 environments
        fetch('/work_orders/api/select/vehicle-makes/')
            .then(response => response.json())
            .then(data => {
                // Clear existing options
                makeSelect.innerHTML = '';
                
                // Add placeholder option
                const placeholderOption = document.createElement('option');
                placeholderOption.value = '';
                placeholderOption.text = gettext('اختر الشركة المصنعة');
                makeSelect.appendChild(placeholderOption);
                
                // Add options from API
                data.results.forEach(make => {
                    const option = document.createElement('option');
                    option.value = make.id;
                    option.text = make.text;
                    option.dataset.country = make.country;
                    makeSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading vehicle makes:', error);
            });
            
        // Add change event listener
        makeSelect.addEventListener('change', function() {
            const modelSelect = document.getElementById('vehicle_model');
            if (modelSelect) {
                // Clear the model select
                modelSelect.innerHTML = '';
                const placeholderOption = document.createElement('option');
                placeholderOption.value = '';
                placeholderOption.text = gettext('اختر الموديل');
                modelSelect.appendChild(placeholderOption);
            }
        });
    }
}

/**
 * Setup the dependency between make and model selects
 */
function setupModelDependency() {
    const modelSelect = document.getElementById('vehicle_model');
    
    if (!modelSelect) return;
    
    if (typeof $.fn.select2 !== 'undefined') {
        $(modelSelect).select2({
            placeholder: gettext('اختر الموديل'),
            allowClear: true,
            ajax: {
                url: '/work_orders/api/select/vehicle-models/',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    const makeSelect = document.getElementById('vehicle_make');
                    const yearInput = document.getElementById('vehicle_year');
                    
                    return {
                        make_id: makeSelect ? makeSelect.value : null,
                        year: yearInput ? yearInput.value : null,
                        term: params.term || ''
                    };
                },
                cache: true,
                processResults: function(data) {
                    return {
                        results: data.results
                    };
                }
            }
        });
    } else {
        // Update models when make changes
        const makeSelect = document.getElementById('vehicle_make');
        if (makeSelect) {
            makeSelect.addEventListener('change', function() {
                updateVehicleModels(this.value);
            });
        }
        
        // Also update when year changes
        const yearInput = document.getElementById('vehicle_year');
        if (yearInput) {
            yearInput.addEventListener('change', function() {
                const makeSelect = document.getElementById('vehicle_make');
                if (makeSelect && makeSelect.value) {
                    updateVehicleModels(makeSelect.value);
                }
            });
        }
    }
}

/**
 * Update vehicle models based on selected make
 * @param {string} makeId - The selected make ID
 */
function updateVehicleModels(makeId) {
    if (!makeId) return;
    
    const modelSelect = document.getElementById('vehicle_model');
    if (!modelSelect) return;
    
    const yearInput = document.getElementById('vehicle_year');
    const year = yearInput ? yearInput.value : null;
    
    // Build URL with parameters
    let url = `/work_orders/api/select/vehicle-models/?make_id=${makeId}`;
    if (year) {
        url += `&year=${year}`;
    }
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            // Clear existing options
            modelSelect.innerHTML = '';
            
            // Add placeholder option
            const placeholderOption = document.createElement('option');
            placeholderOption.value = '';
            placeholderOption.text = gettext('اختر الموديل');
            modelSelect.appendChild(placeholderOption);
            
            // Add options from API
            data.results.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.text = model.text;
                option.dataset.makeId = model.make_id;
                option.dataset.makeName = model.make_name;
                option.dataset.vehicleClass = model.vehicle_class;
                modelSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading vehicle models:', error);
        });
}

/**
 * Setup the form submission handling
 */
function setupFormSubmission() {
    const form = document.getElementById('vehicle_form');
    
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const makeSelect = document.getElementById('vehicle_make');
        const modelSelect = document.getElementById('vehicle_model');
        const makeInput = document.getElementById('vehicle_make_text');
        const modelInput = document.getElementById('vehicle_model_text');
        
        // Get the text values for display
        if (makeSelect && makeInput) {
            const selectedOption = makeSelect.options[makeSelect.selectedIndex];
            if (selectedOption) {
                makeInput.value = selectedOption.text;
            }
        }
        
        if (modelSelect && modelInput) {
            const selectedOption = modelSelect.options[modelSelect.selectedIndex];
            if (selectedOption) {
                modelInput.value = selectedOption.text;
            }
        }
        
        // Build the data to send
        const formData = {
            make: makeInput ? makeInput.value : '',
            model: modelInput ? modelInput.value : '',
            year: document.getElementById('vehicle_year')?.value,
            license_plate: document.getElementById('vehicle_license_plate')?.value,
            vin: document.getElementById('vehicle_vin')?.value,
            color: document.getElementById('vehicle_color')?.value,
            owner_id: document.getElementById('vehicle_owner_id')?.value,
            standard_make_id: makeSelect?.value,
            standard_model_id: modelSelect?.value,
            notes: document.getElementById('vehicle_notes')?.value
        };
        
        // Send to server
        fetch('/work_orders/api/create-vehicle/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Make sure the vehicle data is complete before passing to callback
                const vehicleData = data.vehicle;
                
                // If there's a callback function registered, call it
                if (window.vehicleCreatedCallback && typeof window.vehicleCreatedCallback === 'function') {
                    window.vehicleCreatedCallback(vehicleData);
                }
                
                // Close the modal if present
                const modal = document.getElementById('addVehicleModal');
                if (modal && typeof bootstrap !== 'undefined') {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                }
                
                // Show success message
                showNotification(gettext('تم إنشاء المركبة بنجاح'), 'success');
                
                // Reset the form
                form.reset();
            } else {
                // Show error message
                showNotification(data.error || gettext('حدث خطأ أثناء إنشاء المركبة'), 'error');
            }
        })
        .catch(error => {
            console.error('Error creating vehicle:', error);
            showNotification(gettext('حدث خطأ أثناء إنشاء المركبة'), 'error');
        });
    });
}

/**
 * Helper function to get CSRF cookie
 * @param {string} name - Cookie name
 * @returns {string} Cookie value
 */
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

/**
 * Show a notification message
 * @param {string} message - Message to display
 * @param {string} type - Notification type (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    // Check if toastr is available
    if (typeof toastr !== 'undefined') {
        toastr[type](message);
        return;
    }
    
    // Simple fallback notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type}`;
    notification.innerHTML = message;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '99999';
    notification.style.padding = '10px 20px';
    notification.style.borderRadius = '4px';
    notification.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 5000);
} 