{% load i18n %}

<div id="step-customer-content" class="form-step-content space-y-6">
    <div class="flex justify-between items-center mb-2">
        <h2 class="text-xl font-bold text-gray-700 flex items-center">
        <i class="fas fa-user-circle mr-2 text-blue-500"></i>
        {% trans "معلومات العميل" %}
    </h2>
        <a href="{% url 'work_orders:work_order_list' %}" class="flex items-center text-blue-600 hover:text-blue-800">
            <i class="fas fa-arrow-left ml-2"></i>
            {% trans "العودة إلى القائمة" %}
        </a>
    </div>
    
    <!-- Customer Search -->
    <div class="mb-4">
        <label for="customer_search_term" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
            <i class="fas fa-search text-blue-500 mr-2"></i>
            {% trans "مصطلح البحث" %}
        </label>
        <div class="flex space-x-2 rtl:space-x-reverse">
            <input type="text" id="customer_search_term" name="customer_search_term" 
                   class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm min-h-[44px]" 
                   placeholder="{% trans 'ادخل...' %}">
            <select id="customer_search_criteria" name="customer_search_criteria" 
                    class="form-select mt-1 block rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm min-h-[44px]">
                <option value="all" selected>{% trans "الكل" %}</option>
                <option value="name">{% trans "الاسم" %}</option>
                <option value="phone">{% trans "رقم الهاتف" %}</option>
                <option value="id_number">{% trans "رقم الهوية" %}</option>
            </select>
        </div>
        <div id="customer-search-results" class="mt-2 border border-gray-200 rounded-md max-h-60 overflow-y-auto"></div>
    </div>

    <!-- Selected Customer Info -->
    <div id="selected-customer-info" class="mb-4 p-4 border border-green-200 rounded-md bg-green-50" style="display: none;">
        <h3 class="text-lg font-bold mb-2 text-green-800 flex items-center">
            <i class="fas fa-user-check mr-2"></i>
            {% trans "العميل المختار" %}
        </h3>
        <p id="selected-customer-name" class="text-green-700 flex items-center">
            <i class="fas fa-id-card mr-2"></i>
            <span></span>
        </p>
        <p id="selected-customer-phone" class="text-green-700 flex items-center">
            <i class="fas fa-phone mr-2"></i>
            <span></span>
        </p>
    </div>

    <!-- New Customer Form Fields -->
    <div id="new-customer-fields" class="space-y-4 p-4 border border-gray-200 rounded-md" style="display: none;">
        <h3 class="text-lg font-bold mb-3 text-gray-700 flex items-center">
            <i class="fas fa-user-plus mr-2 text-blue-500"></i>
            {% trans "إضافة عميل جديد" %}
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="new_customer_first_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    {% trans "الاسم الأول" %} <span class="text-red-500 mr-1">*</span>
                </label>
                <input type="text" id="new_customer_first_name" name="new_customer_first_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'الاسم الأول' %}" required>
            </div>
            <div>
                <label for="new_customer_second_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    {% trans "الاسم الثاني" %}
                </label>
                <input type="text" id="new_customer_second_name" name="new_customer_second_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'الاسم الثاني (اختياري)' %}">
            </div>
            <div>
                <label for="new_customer_third_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    {% trans "الاسم الثالث" %}
                </label>
                <input type="text" id="new_customer_third_name" name="new_customer_third_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'الاسم الثالث (اختياري)' %}">
            </div>
            <div>
                <label for="new_customer_last_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    {% trans "الاسم الأخير" %} <span class="text-red-500 mr-1">*</span>
                </label>
                <input type="text" id="new_customer_last_name" name="new_customer_last_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'الاسم الأخير' %}" required>
            </div>
            <div>
                <label for="new_customer_phone" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-phone text-blue-500 mr-2"></i>
                    {% trans "رقم الهاتف" %} <span class="text-red-500 mr-1">*</span>
                </label>
                <input type="text" id="new_customer_phone" name="new_customer_phone" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'رقم الهاتف الأساسي' %}" dir="ltr">
            </div>
            <div>
                <label for="new_customer_alternative_phone" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-mobile-alt text-blue-500 mr-2"></i>
                    {% trans "رقم هاتف بديل (اختياري)" %}
                </label>
                <input type="text" id="new_customer_alternative_phone" name="new_customer_alternative_phone" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'رقم هاتف إضافي' %}" dir="ltr">
            </div>
            <div>
                <label for="new_customer_email" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-envelope text-blue-500 mr-2"></i>
                    {% trans "البريد الإلكتروني (اختياري)" %}
                </label>
                <input type="email" id="new_customer_email" name="new_customer_email" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'البريد الإلكتروني' %}" dir="ltr">
            </div>
            <div>
                <label for="new_customer_id_type" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-id-card text-blue-500 mr-2"></i>
                    {% trans "نوع إثبات الشخصية" %}
                </label>
                <select id="new_customer_id_type" name="new_customer_id_type" 
                        class="form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
                    <option value="">{% trans "اختر النوع" %}</option>
                    <option value="national_id">{% trans "بطاقة شخصية" %}</option>
                    <option value="passport">{% trans "جواز سفر" %}</option>
                    <option value="residence_permit">{% trans "إقامة" %}</option>
                    <option value="driving_license">{% trans "رخصة قيادة" %}</option>
                    <option value="other">{% trans "آخر" %}</option>
                </select>
            </div>
            <div>
                <label for="new_customer_id_number" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-fingerprint text-blue-500 mr-2"></i>
                    {% trans "رقم إثبات الشخصية" %}
                </label>
                <input type="text" id="new_customer_id_number" name="new_customer_id_number" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
             <div>
                <label for="new_customer_gender" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-venus-mars text-blue-500 mr-2"></i>
                    {% trans "النوع" %}
                </label>
                <select id="new_customer_gender" name="new_customer_gender" 
                        class="form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
                    <option value="">{% trans "اختر النوع" %}</option>
                    <option value="male">{% trans "ذكر" %}</option>
                    <option value="female">{% trans "أنثى" %}</option>
                    <option value="other">{% trans "آخر" %}</option>
                </select>
            </div>
             <div>
                <label for="new_customer_nationality" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-flag text-blue-500 mr-2"></i>
                    {% trans "الجنسية" %}
                </label>
                <select id="new_customer_nationality" name="new_customer_nationality" 
                        class="form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
                    <option value="Egyptian" selected>{% trans "مصري" %}</option>
                    <option value="Saudi">{% trans "سعودي" %}</option>
                    <option value="UAE">{% trans "إماراتي" %}</option>
                    <option value="Kuwaiti">{% trans "كويتي" %}</option>
                    <option value="Qatari">{% trans "قطري" %}</option>
                    <option value="Bahraini">{% trans "بحريني" %}</option>
                    <option value="Omani">{% trans "عماني" %}</option>
                    <option value="Jordanian">{% trans "أردني" %}</option>
                    <option value="Lebanese">{% trans "لبناني" %}</option>
                    <option value="Syrian">{% trans "سوري" %}</option>
                    <option value="Palestinian">{% trans "فلسطيني" %}</option>
                    <option value="Iraqi">{% trans "عراقي" %}</option>
                    <option value="Moroccan">{% trans "مغربي" %}</option>
                    <option value="Tunisian">{% trans "تونسي" %}</option>
                    <option value="Algerian">{% trans "جزائري" %}</option>
                    <option value="Libyan">{% trans "ليبي" %}</option>
                    <option value="Sudanese">{% trans "سوداني" %}</option>
                    <option value="Yemeni">{% trans "يمني" %}</option>
                    <option value="Other">{% trans "آخر" %}</option>
                </select>
            </div>
             <div>
                <label for="new_customer_type" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-users text-blue-500 mr-2"></i>
                    {% trans "نوع العميل" %}             
                </label>
                <select id="new_customer_type" name="new_customer_type" 
                        class="form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
                    <option value="individual">{% trans "فرد" %}</option>
                    <option value="corporate">{% trans "شركة" %}</option>
                </select>
            </div>
            <div id="company_fields" style="display: none;">
                <label for="new_customer_company_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-building text-blue-500 mr-2"></i>
                    {% trans "اسم الشركة" %}
                </label>
                <input type="text" id="new_customer_company_name" name="new_customer_company_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
            <div id="commercial_registration_field" style="display: none;">
                <label for="new_customer_commercial_registration" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-certificate text-blue-500 mr-2"></i>
                    {% trans "السجل التجاري" %} <span class="text-red-500 mr-1">*</span>
                    <span class="text-xs text-gray-500 mr-2">({% trans "مطلوب للشركات في مصر" %})</span>
                </label>
                <input type="text" id="new_customer_commercial_registration" name="new_customer_commercial_registration" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'أدخل رقم السجل التجاري' %}" dir="ltr">
            </div>
        </div>

        <div>
            <label for="new_customer_dob" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                <i class="fas fa-calendar-alt text-blue-500 mr-2"></i>
                {% trans "تاريخ الميلاد" %}
            </label>
            <input type="date" id="new_customer_dob" name="new_customer_dob" 
                   class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
        </div>

        <div>
            <label for="new_customer_address" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                {% trans "العنوان التفصيلي" %}
            </label>
            <textarea id="new_customer_address" name="new_customer_address" rows="3" 
                      class="form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                      placeholder="{% trans 'الشارع، رقم المبنى، الحي' %}"></textarea>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label for="new_customer_city" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-city text-blue-500 mr-2"></i>
                    {% trans "المدينة" %}
                </label>
                <input type="text" id="new_customer_city" name="new_customer_city" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'المدينة' %}">
            </div>
            <div>
                <label for="new_customer_state" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-map text-blue-500 mr-2"></i>
                    {% trans "المحافظة/الولاية" %}
                </label>
                <input type="text" id="new_customer_state" name="new_customer_state" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'المحافظة أو الولاية' %}">
            </div>
            <div>
                <label for="new_customer_postal_code" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-mail-bulk text-blue-500 mr-2"></i>
                    {% trans "الرمز البريدي" %}
                </label>
                <input type="text" id="new_customer_postal_code" name="new_customer_postal_code" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]" 
                       placeholder="{% trans 'الرمز البريدي' %}" dir="ltr">
            </div>
            <div>
                <label for="new_customer_country" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-globe text-blue-500 mr-2"></i>
                    {% trans "البلد" %}
                </label>
                <select id="new_customer_country" name="new_customer_country" 
                        class="form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
                    <option value="Egypt" selected>{% trans "مصر" %}</option>
                    <option value="Saudi Arabia">{% trans "السعودية" %}</option>
                    <option value="UAE">{% trans "الإمارات العربية المتحدة" %}</option>
                    <option value="Kuwait">{% trans "الكويت" %}</option>
                    <option value="Qatar">{% trans "قطر" %}</option>
                    <option value="Bahrain">{% trans "البحرين" %}</option>
                    <option value="Oman">{% trans "عمان" %}</option>
                    <option value="Jordan">{% trans "الأردن" %}</option>
                    <option value="Lebanon">{% trans "لبنان" %}</option>
                    <option value="Syria">{% trans "سوريا" %}</option>
                    <option value="Palestine">{% trans "فلسطين" %}</option>
                    <option value="Iraq">{% trans "العراق" %}</option>
                    <option value="Morocco">{% trans "المغرب" %}</option>
                    <option value="Tunisia">{% trans "تونس" %}</option>
                    <option value="Algeria">{% trans "الجزائر" %}</option>
                    <option value="Libya">{% trans "ليبيا" %}</option>
                    <option value="Sudan">{% trans "السودان" %}</option>
                    <option value="Yemen">{% trans "اليمن" %}</option>
                    <option value="Other">{% trans "دولة أخرى" %}</option>
                </select>
            </div>
        </div>

        <!-- Save and Select Button -->
        <div class="mt-4">
            <button type="button" id="save_and_select_customer" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors touch-feedback flex items-center justify-center">
                <i class="fas fa-save mr-2"></i>
                {% trans "حفظ واختيار" %}
            </button>
        </div>
    </div>
    
    <input type="hidden" id="selected_customer_id" name="selected_customer_id">
</div> 

<!-- Validation Error Container -->
<div id="customer-form-step-errors" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-700 hidden"></div>

<script>
    // Clear form data on page refresh
    document.addEventListener('DOMContentLoaded', function() {
        // Check if this is a page refresh (not a first load)
        if (performance.navigation.type === 1) {
            console.log('Customer form page was refreshed. Clearing session data...');
            
            // Clear form data from sessionStorage
            sessionStorage.removeItem('currentStep');
            sessionStorage.removeItem('customer');
            sessionStorage.removeItem('ui');
            
            // Also clear any values in the form
            document.getElementById('new_customer_first_name')?.value = '';
            document.getElementById('new_customer_second_name')?.value = '';
            document.getElementById('new_customer_third_name')?.value = '';
            document.getElementById('new_customer_last_name')?.value = '';
            document.getElementById('new_customer_phone')?.value = '';
            document.getElementById('new_customer_alternative_phone')?.value = '';
            document.getElementById('new_customer_email')?.value = '';
            document.getElementById('new_customer_id_type')?.value = '';
            document.getElementById('new_customer_id_number')?.value = '';
            document.getElementById('new_customer_gender')?.value = '';
            document.getElementById('new_customer_nationality')?.value = 'Egyptian';
            document.getElementById('new_customer_dob')?.value = '';
            document.getElementById('new_customer_address')?.value = '';
            document.getElementById('new_customer_city')?.value = '';
            document.getElementById('new_customer_state')?.value = '';
            document.getElementById('new_customer_postal_code')?.value = '';
            document.getElementById('new_customer_country')?.value = 'Egypt';
            document.getElementById('new_customer_type')?.value = 'individual';
            document.getElementById('new_customer_company_name')?.value = '';
            document.getElementById('new_customer_commercial_registration')?.value = '';
            document.getElementById('selected_customer_id')?.value = '';
            
            // If customer info box is shown, hide it
            const customerInfoBox = document.getElementById('selected-customer-info');
            if (customerInfoBox) {
                customerInfoBox.style.display = 'none';
            }
        }
        
        // Initialize customer type handling
        const customerTypeSelect = document.getElementById('new_customer_type');
        const companyFields = document.getElementById('company_fields');
        const commercialRegField = document.getElementById('commercial_registration_field');
        
        if (customerTypeSelect) {
            customerTypeSelect.addEventListener('change', function() {
                const isCompany = this.value === 'corporate';
                
                // Show/hide company-specific fields
                if (companyFields) {
                    companyFields.style.display = isCompany ? 'block' : 'none';
                }
                if (commercialRegField) {
                    commercialRegField.style.display = isCompany ? 'block' : 'none';
                }
                
                // Set required attribute for commercial registration
                const commercialRegInput = document.getElementById('new_customer_commercial_registration');
                if (commercialRegInput) {
                    commercialRegInput.required = isCompany;
                }
            });
            
            // Trigger initial state
            customerTypeSelect.dispatchEvent(new Event('change'));
        }
        
        // Initialize Arabic input validation for name fields
        initializeArabicValidation();
        
        // Initialize Egyptian ID validation
        initializeEgyptianIDValidation();
        
        // Initialize phone validation
        initializePhoneValidation();
        
        // Initialize alternative phone validation
        initializeAlternativePhoneValidation();
        
        // Initialize postal code validation
        initializePostalCodeValidation();
        
        // Initialize date of birth validation
        initializeDateOfBirthValidation();
        
        // Initialize commercial registration validation
        initializeCommercialRegistrationValidation();
    });
    
    // Arabic text validation
    function isArabicText(text) {
        if (!text) return true;
        // Arabic Unicode ranges and allowed characters
        const arabicPattern = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\u0980-\u09FF\s\-\.]*$/;
        return arabicPattern.test(text.trim());
    }
    
    function initializeArabicValidation() {
        const nameFields = [
            'new_customer_first_name',
            'new_customer_second_name', 
            'new_customer_third_name',
            'new_customer_last_name',
            'new_customer_company_name',
            'new_customer_city',
            'new_customer_state'
        ];
        
        nameFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', function() {
                    validateArabicInput(this);
                });
                
                field.addEventListener('paste', function(e) {
                    setTimeout(() => validateArabicInput(this), 100);
                });
            }
        });
    }
    
    function validateArabicInput(field) {
        const value = field.value;
        const isValid = isArabicText(value);
        
        // Remove existing validation styling
        field.classList.remove('border-red-500', 'border-green-500');
        
        // Find or create error message element
        let errorElement = field.parentNode.querySelector('.arabic-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'arabic-error text-red-500 text-xs mt-1';
            field.parentNode.appendChild(errorElement);
        }
        
        if (value && !isValid) {
            field.classList.add('border-red-500');
            errorElement.textContent = '{% trans "يجب أن يحتوي الاسم على حروف عربية فقط" %}';
            errorElement.style.display = 'block';
        } else {
            field.classList.add('border-green-500');
            errorElement.style.display = 'none';
        }
    }
    
    function initializeEgyptianIDValidation() {
        const idField = document.getElementById('new_customer_id_number');
        const idTypeField = document.getElementById('new_customer_id_type');
        
        if (idField && idTypeField) {
            function validateID() {
                const idNumber = idField.value.trim();
                const idType = idTypeField.value;
                
                if (idType === 'national_id' && idNumber) {
                    validateEgyptianNationalID(idField, idNumber);
                } else {
                    clearValidationState(idField);
                }
            }
            
            idField.addEventListener('input', validateID);
            idTypeField.addEventListener('change', validateID);
        }
    }
    
    function validateEgyptianNationalID(field, idNumber) {
        // Remove spaces and dashes
        const cleanId = idNumber.replace(/[\s\-]/g, '');
        
        let isValid = true;
        let errorMessage = '';
        
        // Check if it's exactly 14 digits
        if (!/^\d{14}$/.test(cleanId)) {
            isValid = false;
            errorMessage = '{% trans "الرقم القومي المصري يجب أن يكون 14 رقماً" %}';
        } else {
            // Extract components
            const century = parseInt(cleanId[0]);
            const year = parseInt(cleanId.substring(1, 3));
            const month = parseInt(cleanId.substring(3, 5));
            const day = parseInt(cleanId.substring(5, 7));
            const governorate = parseInt(cleanId.substring(7, 9));
            
            // Validate century (2 for 1900s, 3 for 2000s)
            if (century !== 2 && century !== 3) {
                isValid = false;
                errorMessage = '{% trans "الرقم القومي غير صحيح - رقم القرن يجب أن يكون 2 أو 3" %}';
            }
            // Validate month
            else if (month < 1 || month > 12) {
                isValid = false;
                errorMessage = '{% trans "الرقم القومي غير صحيح - الشهر غير صالح" %}';
            }
            // Validate day
            else if (day < 1 || day > 31) {
                isValid = false;
                errorMessage = '{% trans "الرقم القومي غير صحيح - اليوم غير صالح" %}';
            }
            // Validate governorate code (01-35 for Egyptian governorates)
            else if (governorate < 1 || governorate > 35) {
                isValid = false;
                errorMessage = '{% trans "الرقم القومي غير صحيح - كود المحافظة غير صالح" %}';
            }
        }
        
        showValidationResult(field, isValid, errorMessage);
    }
    
    function initializePhoneValidation() {
        const phoneField = document.getElementById('new_customer_phone');
        
        if (phoneField) {
            phoneField.addEventListener('input', function() {
                validateEgyptianPhone(this);
            });
        }
    }
    
    function validateEgyptianPhone(field) {
        const phone = field.value.trim();
        
        if (!phone) {
            clearValidationState(field);
            return;
        }
        
        // Remove spaces, hyphens, and parentheses
        const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
        
        // Egyptian phone patterns
        const patterns = [
            /^01[0-9]{9}$/,           // Mobile: 01xxxxxxxxx
            /^\+2001[0-9]{9}$/,       // Mobile with country code: +2001xxxxxxxxx
            /^0[2-9][0-9]{7,8}$/,     // Landline: 0xxxxxxxx or 0xxxxxxxxx
            /^\+20[2-9][0-9]{7,8}$/,  // Landline with country code: +20xxxxxxxx
        ];
        
        const isValid = patterns.some(pattern => pattern.test(cleanPhone));
        const errorMessage = isValid ? '' : '{% trans "رقم الهاتف المصري غير صحيح" %}';
        
        showValidationResult(field, isValid, errorMessage);
    }
    
    function initializeAlternativePhoneValidation() {
        const altPhoneField = document.getElementById('new_customer_alternative_phone');
        
        if (altPhoneField) {
            altPhoneField.addEventListener('input', function() {
                validateEgyptianPhone(this);
            });
        }
    }
    
    function initializePostalCodeValidation() {
        const postalField = document.getElementById('new_customer_postal_code');
        
        if (postalField) {
            postalField.addEventListener('input', function() {
                validatePostalCode(this);
            });
        }
    }
    
    function validatePostalCode(field) {
        const postalCode = field.value.trim();
        
        if (!postalCode) {
            clearValidationState(field);
            return;
        }
        
        // Egyptian postal codes are typically 5 digits
        const isValid = /^\d{5}$/.test(postalCode);
        const errorMessage = isValid ? '' : '{% trans "الرمز البريدي يجب أن يكون 5 أرقام" %}';
        
        showValidationResult(field, isValid, errorMessage);
    }
    
    function initializeDateOfBirthValidation() {
        const dobField = document.getElementById('new_customer_dob');
        
        if (dobField) {
            dobField.addEventListener('change', function() {
                validateDateOfBirth(this);
            });
        }
    }
    
    function validateDateOfBirth(field) {
        const dobValue = field.value;
        
        if (!dobValue) {
            clearValidationState(field);
            return;
        }
        
        const dob = new Date(dobValue);
        const today = new Date();
        const age = today.getFullYear() - dob.getFullYear();
        const monthDiff = today.getMonth() - dob.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
            age--;
        }
        
        let isValid = true;
        let errorMessage = '';
        
        if (dob > today) {
            isValid = false;
            errorMessage = '{% trans "تاريخ الميلاد لا يمكن أن يكون في المستقبل" %}';
        } else if (age < 16) {
            isValid = false;
            errorMessage = '{% trans "العمر يجب أن يكون 16 سنة على الأقل" %}';
        } else if (age > 120) {
            isValid = false;
            errorMessage = '{% trans "العمر غير صحيح" %}';
        }
        
        showValidationResult(field, isValid, errorMessage);
    }
    
    function initializeCommercialRegistrationValidation() {
        const regField = document.getElementById('new_customer_commercial_registration');
        
        if (regField) {
            regField.addEventListener('input', function() {
                validateCommercialRegistration(this);
            });
        }
    }
    
    function validateCommercialRegistration(field) {
        const regNumber = field.value.trim();
        
        if (!regNumber) {
            clearValidationState(field);
            return;
        }
        
        // Remove spaces and dashes
        const cleanReg = regNumber.replace(/[\s\-]/g, '');
        
        // Egyptian commercial registration is typically 7-10 digits
        const isValid = /^\d{7,10}$/.test(cleanReg);
        const errorMessage = isValid ? '' : '{% trans "رقم السجل التجاري يجب أن يكون من 7 إلى 10 أرقام" %}';
        
        showValidationResult(field, isValid, errorMessage);
    }
    
    function showValidationResult(field, isValid, errorMessage) {
        // Remove existing validation styling
        field.classList.remove('border-red-500', 'border-green-500');
        
        // Find or create error message element
        let errorElement = field.parentNode.querySelector('.validation-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'validation-error text-red-500 text-xs mt-1';
            field.parentNode.appendChild(errorElement);
        }
        
        if (isValid) {
            field.classList.add('border-green-500');
            errorElement.style.display = 'none';
        } else {
            field.classList.add('border-red-500');
            errorElement.textContent = errorMessage;
            errorElement.style.display = 'block';
        }
    }
    
    function clearValidationState(field) {
        field.classList.remove('border-red-500', 'border-green-500');
        const errorElement = field.parentNode.querySelector('.validation-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }
    
    // Enhanced customer form validation function
    function validateCustomerForm() {
        let isValid = true;
        const errors = [];
        
        // Required fields validation
        const requiredFields = [
            { id: 'new_customer_first_name', name: '{% trans "الاسم الأول" %}' },
            { id: 'new_customer_last_name', name: '{% trans "الاسم الأخير" %}' },
            { id: 'new_customer_phone', name: '{% trans "رقم الهاتف" %}' }
        ];
        
        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (!element || !element.value.trim()) {
                isValid = false;
                errors.push(`${field.name} {% trans "مطلوب" %}`);
                if (element) {
                    element.classList.add('border-red-500');
                }
            }
        });
        
        // Check customer type and commercial registration
        const customerType = document.getElementById('new_customer_type')?.value;
        const commercialReg = document.getElementById('new_customer_commercial_registration')?.value;
        
        if (customerType === 'corporate' && !commercialReg?.trim()) {
            isValid = false;
            errors.push('{% trans "السجل التجاري مطلوب للشركات في مصر" %}');
            const regField = document.getElementById('new_customer_commercial_registration');
            if (regField) {
                regField.classList.add('border-red-500');
            }
        }
        
        // Check for validation errors from other validators
        const fieldsWithErrors = document.querySelectorAll('.border-red-500');
        if (fieldsWithErrors.length > 0) {
            isValid = false;
            errors.push('{% trans "يرجى تصحيح الأخطاء المشار إليها" %}');
        }
        
        // Display errors
        const errorContainer = document.getElementById('customer-form-step-errors');
        if (errorContainer) {
            if (errors.length > 0) {
                errorContainer.innerHTML = '<ul class="list-disc list-inside">' + 
                    errors.map(error => `<li>${error}</li>`).join('') + '</ul>';
                errorContainer.classList.remove('hidden');
            } else {
                errorContainer.classList.add('hidden');
            }
        }
        
        return isValid;
    }

    // Form field validation indicators
    const validationIndicators = {};
    
    // Add validation indicators to required fields
    document.addEventListener('DOMContentLoaded', function() {
        // Required fields with their validation rules
        const requiredFields = [
            {id: 'new_customer_first_name', rule: /^.+$/},
            {id: 'new_customer_last_name', rule: /^.+$/},
            {id: 'new_customer_phone', rule: /^[\d\s\+\-]{8,}$/}, // Phone number with min 8 digits
            {id: 'new_customer_alternative_phone', rule: /^$|^[\d\s\+\-]{8,}$/}, // Alternative phone or empty
            {id: 'new_customer_email', rule: /^$|^[^\s@]+@[^\s@]+\.[^\s@]+$/}, // Email or empty
            {id: 'new_customer_id_number', rule: null, dependency: 'new_customer_id_type'}, // Required only if ID type is selected
            {id: 'new_customer_postal_code', rule: /^$|^\d{5}$/}, // Postal code or empty
            {id: 'new_customer_dob', rule: /^$|^\d{4}-\d{2}-\d{2}$/} // Date of birth or empty
        ];
        
        // Create validation indicators for each field
        requiredFields.forEach(field => {
            const inputElement = document.getElementById(field.id);
            if (!inputElement) return;
            
            // Create validation indicator
            const indicator = document.createElement('div');
            indicator.className = 'validation-indicator absolute right-2 top-1/2 transform -translate-y-1/2';
            indicator.innerHTML = ''; // Initially empty
            validationIndicators[field.id] = indicator;
            
            // Position the indicator inside the input's parent
            const inputContainer = inputElement.parentElement;
            if (inputContainer) {
                inputContainer.style.position = 'relative';
                inputContainer.appendChild(indicator);
            }
            
            // Add real-time validation
            inputElement.addEventListener('input', function() {
                validateField(field.id, field.rule, field.dependency);
            });
            
            // Initial validation state
            validateField(field.id, field.rule, field.dependency);
        });
        
        // If we have a dependency field, add listener to it
        const idTypeField = document.getElementById('new_customer_id_type');
        if (idTypeField) {
            idTypeField.addEventListener('change', function() {
                validateField('new_customer_id_number', null, 'new_customer_id_type');
            });
        }
    });
    
    // Validate individual field
    function validateField(fieldId, rule, dependencyFieldId) {
        const field = document.getElementById(fieldId);
        const indicator = validationIndicators[fieldId];
        if (!field || !indicator) return;
        
        let isValid = true;
        let isEmpty = !field.value.trim();
        
        // Check if field is dependent on another field
        if (dependencyFieldId) {
            const dependencyField = document.getElementById(dependencyFieldId);
            if (dependencyField && dependencyField.value && isEmpty) {
                isValid = false;
            } else if (!dependencyField || !dependencyField.value) {
                // If dependency field is empty or doesn't exist, this field is optional
                isValid = true;
            } else if (rule) {
                // If dependency is satisfied and we have a rule, check against it
                isValid = rule.test(field.value);
            }
        } else if (rule) {
            // Regular rule-based validation
            isValid = rule.test(field.value);
        }
        
        // Update indicator
        if (isEmpty && !dependencyFieldId) {
            // Empty field - neutral state
            indicator.innerHTML = '<i class="fas fa-asterisk text-gray-400 text-xs"></i>';
            field.classList.remove('border-red-500', 'border-green-500');
            field.classList.add('border-gray-300');
        } else if (isValid) {
            // Valid input
            indicator.innerHTML = '<i class="fas fa-check text-green-500"></i>';
            field.classList.remove('border-red-500', 'border-gray-300');
            field.classList.add('border-green-500');
        } else {
            // Invalid input
            indicator.innerHTML = '<i class="fas fa-times text-red-500"></i>';
            field.classList.remove('border-green-500', 'border-gray-300');
            field.classList.add('border-red-500');
        }
        
        return isValid;
    }
    
    // Add validation to save button
    document.addEventListener('DOMContentLoaded', function() {
        const saveButton = document.getElementById('save_and_select_customer');
        if (saveButton) {
            saveButton.addEventListener('click', function(e) {
                if (!validateCustomerForm()) {
                    e.preventDefault();
                    return false;
                }
                
                // If validation passes, continue with original click handler
            });
        }
    });
</script> 