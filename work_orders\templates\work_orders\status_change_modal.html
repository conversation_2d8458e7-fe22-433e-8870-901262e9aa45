{% load i18n %}

<div id="status-change-modal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full flex items-center justify-center">
    <div class="relative w-full max-w-md max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                <h3 class="text-xl font-medium text-gray-900">
                    {% trans "Change Work Order Status" %}
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="status-change-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">{% trans "Close modal" %}</span>
                </button>
            </div>
            
            <!-- Modal body -->
            <div class="p-4 md:p-5">
                <form id="status-change-form" method="post" action="{% url 'work_orders:change_work_order_status' work_order.id %}">
                    {% csrf_token %}
                    
                    <div class="mb-5">
                        <label for="status-select" class="block mb-2 text-sm font-medium text-gray-900">
                            {% trans "Status" %}
                        </label>
                        <select id="status-select" name="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                            {% for value, choice_data in status_form.status_choices_with_help %}
                                <option value="{{ value }}" {% if work_order.status == value %}selected{% endif %} 
                                        data-help="{{ choice_data.help }}">
                                    {{ choice_data.label }}
                                </option>
                            {% endfor %}
                        </select>
                        <p id="status-help-text" class="mt-1 text-sm text-gray-500"></p>
                    </div>
                    
                    <div class="mb-5">
                        <label for="status-notes" class="block mb-2 text-sm font-medium text-gray-900">
                            {% trans "Notes" %}
                        </label>
                        <textarea id="status-notes" name="notes" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500" placeholder="{% trans 'Add notes about this status change...' %}"></textarea>
                    </div>
                    
                    <!-- Status-specific content will be shown here -->
                    <div id="status-specific-content" class="mb-5 hidden">
                        <!-- Content for 'completed' status -->
                        <div id="completed-status-content" class="hidden">
                            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50">
                                <p>{% trans "Marking as completed will:" %}</p>
                                <ul class="mt-1.5 list-disc list-inside">
                                    <li>{% trans "Set actual end date to now" %}</li>
                                    <li>{% trans "Mark all operations as completed" %}</li>
                                    <li>{% trans "Mark all materials as consumed" %}</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Content for 'in_progress' status -->
                        <div id="in_progress-status-content" class="hidden">
                            <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50">
                                <p>{% trans "Marking as in progress will:" %}</p>
                                <ul class="mt-1.5 list-disc list-inside">
                                    <li>{% trans "Set actual start date to now if not already set" %}</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Content for 'on_hold' status -->
                        <div id="on_hold-status-content" class="hidden">
                            <div class="p-4 mb-4 text-sm text-yellow-800 rounded-lg bg-yellow-50">
                                <p>{% trans "Marking as on hold will pause work on this order." %}</p>
                                <p>{% trans "Please provide a reason in the notes." %}</p>
                            </div>
                        </div>
                        
                        <!-- Content for 'cancelled' status -->
                        <div id="cancelled-status-content" class="hidden">
                            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50">
                                <p>{% trans "Marking as cancelled will permanently stop this work order." %}</p>
                                <p>{% trans "Please provide a reason in the notes." %}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="button" class="me-2 py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100" data-modal-hide="status-change-modal">
                            {% trans "Cancel" %}
                        </button>
                        <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                            {% trans "Change Status" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const statusSelect = document.getElementById('status-select');
        const statusHelpText = document.getElementById('status-help-text');
        const statusSpecificContent = document.getElementById('status-specific-content');
        
        // Function to update help text and status-specific content
        function updateStatusHelp() {
            const selectedOption = statusSelect.options[statusSelect.selectedIndex];
            const helpText = selectedOption.getAttribute('data-help');
            statusHelpText.textContent = helpText;
            
            // Hide all status-specific content
            document.querySelectorAll('[id$="-status-content"]').forEach(el => {
                el.classList.add('hidden');
            });
            
            // Show status-specific content if it exists
            const statusValue = statusSelect.value;
            const statusContent = document.getElementById(`${statusValue}-status-content`);
            
            if (statusContent) {
                statusContent.classList.remove('hidden');
                statusSpecificContent.classList.remove('hidden');
            } else {
                statusSpecificContent.classList.add('hidden');
            }
        }
        
        // Update on page load
        updateStatusHelp();
        
        // Update when selection changes
        statusSelect.addEventListener('change', updateStatusHelp);
        
        // Submit form with AJAX
        document.getElementById('status-change-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            
            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide modal
                    document.querySelector('[data-modal-hide="status-change-modal"]').click();
                    
                    // Show success message
                    const toast = document.createElement('div');
                    toast.innerHTML = `
                        <div id="toast-success" class="fixed top-5 right-5 flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow" role="alert">
                            <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg">
                                <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                                </svg>
                                <span class="sr-only">Success icon</span>
                            </div>
                            <div class="ms-3 text-sm font-normal">${data.message}</div>
                            <button type="button" class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8" data-dismiss-target="#toast-success" aria-label="Close">
                                <span class="sr-only">Close</span>
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                </svg>
                            </button>
                        </div>
                    `;
                    document.body.appendChild(toast.firstElementChild);
                    
                    // Remove toast after 3 seconds
                    setTimeout(() => {
                        const toastElement = document.getElementById('toast-success');
                        if (toastElement) {
                            toastElement.remove();
                        }
                    }, 3000);
                    
                    // Reload the page to refresh content
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    // Show error message
                    if (data.error) {
                        alert(data.error);
                    } else if (data.errors) {
                        alert(Object.values(data.errors).join('\n'));
                    } else {
                        alert('An error occurred. Please try again.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        });
    });
</script>