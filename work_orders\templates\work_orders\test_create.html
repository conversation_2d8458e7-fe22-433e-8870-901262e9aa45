{% extends "base.html" %}
{% load i18n %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">{% trans "Test Work Order Creation" %}</h1>
    
    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <form id="test-form" method="post" class="space-y-4">
            {% csrf_token %}
            
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="customer_id">
                    {% trans "Customer" %}
                </label>
                <select id="customer_id" name="customer_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">{% trans "Select Customer" %}</option>
                    {% for customer in customers %}
                    <option value="{{ customer.id }}">{{ customer.get_full_name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="vehicle_id">
                    {% trans "Vehicle" %}
                </label>
                <select id="vehicle_id" name="vehicle_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <option value="">{% trans "Select Vehicle" %}</option>
                    {% for vehicle in vehicles %}
                    <option value="{{ vehicle.id }}">{{ vehicle.license_plate }} - {{ vehicle.make }} {{ vehicle.model }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="description">
                    {% trans "Description" %}
                </label>
                <textarea id="description" name="description" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" rows="3"></textarea>
            </div>
            
            <div class="flex items-center justify-between">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    {% trans "Create Work Order" %}
                </button>
            </div>
        </form>
    </div>
    
    <div id="result" class="mt-4 p-4 hidden">
        <div id="success-message" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded hidden"></div>
        <div id="error-message" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded hidden"></div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('test-form');
    const resultDiv = document.getElementById('result');
    const successDiv = document.getElementById('success-message');
    const errorDiv = document.getElementById('error-message');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Clear previous results
        resultDiv.classList.add('hidden');
        successDiv.classList.add('hidden');
        errorDiv.classList.add('hidden');
        
        // Get form data
        const formData = new FormData(form);
        
        // Send AJAX request
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            // Show the result div
            resultDiv.classList.remove('hidden');
            
            if (data.success) {
                // Show success message
                successDiv.textContent = data.message + ' (ID: ' + data.work_order_id + ')';
                successDiv.classList.remove('hidden');
                
                // Clear the form
                form.reset();
            } else {
                // Show error message
                errorDiv.textContent = data.message;
                errorDiv.classList.remove('hidden');
            }
        })
        .catch(error => {
            // Show the result div
            resultDiv.classList.remove('hidden');
            
            // Show error message
            errorDiv.textContent = 'An error occurred: ' + error.message;
            errorDiv.classList.remove('hidden');
        });
    });
});
</script>
{% endblock %}