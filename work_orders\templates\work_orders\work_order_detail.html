{% extends 'core/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6 rtl:text-right ltr:text-left">
    <!-- Header Section -->
    <div class="bg-white shadow rounded-lg mb-6 p-6">
        <div class="flex justify-between items-center mb-4">
            <h1 class="text-2xl font-bold flex items-center gap-2 rtl:flex-row-reverse">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {% trans "Work Order" %}: {{ work_order.work_order_number }}
            </h1>
            <div class="flex space-x-2 rtl:space-x-reverse">
                <!-- Status Badge -->
                <span class="px-3 py-1.5 rounded-full text-sm font-semibold flex items-center gap-1
                    {% if work_order.status == 'draft' %}bg-gray-200 text-gray-800
                    {% elif work_order.status == 'planned' %}bg-blue-200 text-blue-800
                    {% elif work_order.status == 'in_progress' %}bg-yellow-200 text-yellow-800
                    {% elif work_order.status == 'on_hold' %}bg-orange-200 text-orange-800
                    {% elif work_order.status == 'completed' %}bg-green-200 text-green-800
                    {% elif work_order.status == 'cancelled' %}bg-red-200 text-red-800
                    {% endif %}">
                    
                    {% if work_order.status == 'draft' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    {% elif work_order.status == 'planned' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    {% elif work_order.status == 'in_progress' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    {% elif work_order.status == 'on_hold' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    {% elif work_order.status == 'completed' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    {% elif work_order.status == 'cancelled' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    {% endif %}
                    
                            {{ work_order.get_status_display }}
                        </span>
                
                <!-- Priority Badge -->
                <span class="px-3 py-1.5 rounded-full text-sm font-semibold flex items-center gap-1
                    {% if work_order.priority == 'low' %}bg-gray-200 text-gray-800
                    {% elif work_order.priority == 'medium' %}bg-blue-200 text-blue-800
                    {% elif work_order.priority == 'high' %}bg-orange-200 text-orange-800
                    {% elif work_order.priority == 'critical' %}bg-red-200 text-red-800
                            {% endif %}">
                    
                    {% if work_order.priority == 'low' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    {% elif work_order.priority == 'medium' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4" />
                        </svg>
                    {% elif work_order.priority == 'high' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                    {% elif work_order.priority == 'critical' %}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    {% endif %}
                    
                            {{ work_order.get_priority_display }}
                        </span>
                    </div>
        </div>
        
        <!-- Customer and Vehicle Info - Enhanced -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition duration-200">
                <h3 class="font-medium text-lg mb-3 flex items-center gap-2 border-b pb-2 text-blue-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    {% trans "Customer Information" %}
                </h3>
                <div class="space-y-2">
                    {% if work_order.customer %}
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Name" %}:</span>
                                <p class="font-medium">{{ work_order.customer.get_full_name }}</p>
                            </div>
                        </div>
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Phone" %}:</span>
                                <p class="font-medium">{{ work_order.customer.phone }}</p>
                    </div>
                    </div>
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Email" %}:</span>
                                <p class="font-medium">{{ work_order.customer.email|default:"غير محدد" }}</p>
                    </div>
                </div>
                    {% else %}
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Name" %}:</span>
                                <p class="font-medium">{{ work_order.customer_name|default:"غير محدد" }}</p>
            </div>
                    </div>
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Phone" %}:</span>
                                <p class="font-medium">{{ work_order.customer_phone|default:"غير محدد" }}</p>
                    </div>
                    </div>
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Email" %}:</span>
                                <p class="font-medium">{{ work_order.customer_email|default:"غير محدد" }}</p>
                    </div>
                    </div>
                    {% endif %}

                    <!-- Quick actions button -->
                    <div class="mt-4 flex justify-end">
                        <button class="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 border border-blue-200 rounded-md px-2 py-1 hover:bg-blue-50 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                            </svg>
                            {% trans "Actions" %}
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition duration-200">
                <h3 class="font-medium text-lg mb-3 flex items-center gap-2 border-b pb-2 text-blue-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {% trans "Vehicle Information" %}
                </h3>
                <div class="space-y-2">
                    {% if work_order.vehicle %}
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Make/Model" %}:</span>
                                <p class="font-medium">{{ work_order.vehicle.make }} {{ work_order.vehicle.model }}</p>
                            </div>
                        </div>
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "License Plate" %}:</span>
                                <p class="font-medium">{{ work_order.vehicle.license_plate|default:"غير محدد" }}</p>
        </div>
                        </div>
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "VIN" %}:</span>
                                <p class="font-medium">{{ work_order.vehicle.vin|default:"غير محدد" }}</p>
                            </div>
                        </div>
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Year" %}:</span>
                                <p class="font-medium">{{ work_order.vehicle.year|default:"غير محدد" }}</p>
                    </div>
                </div>
                    {% else %}
                        <p class="text-gray-500">{% trans "No vehicle information" %}</p>
                    {% endif %}
                    
                    {% if work_order.current_odometer %}
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Current Odometer" %}:</span>
                                <p class="font-medium">{{ work_order.current_odometer }} كم</p>
                            </div>
                        </div>
                    {% endif %}
                    
                    {% if work_order.fuel_level %}
                        <div class="flex items-start gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                            </svg>
                            <div class="flex-1">
                                <span class="text-sm text-gray-500">{% trans "Fuel Level" %}:</span>
                                <p class="font-medium">{{ work_order.fuel_level }}%</p>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Quick actions button -->
                    <div class="mt-4 flex justify-end">
                        <button class="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 border border-blue-200 rounded-md px-2 py-1 hover:bg-blue-50 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            {% trans "View History" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Description -->
        <div class="mb-6">
            <h3 class="font-medium text-lg mb-2 flex items-center gap-2 text-blue-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                {% trans "Description" %}
            </h3>
            <div class="border rounded-lg p-4 bg-gray-50 shadow-inner">
                <p>{{ work_order.description|default:"لا يوجد وصف"|linebreaks }}</p>
            </div>
        </div>
        
        <!-- Notes -->
        {% if work_order.notes %}
        <div class="mb-6">
            <h3 class="font-medium text-lg mb-2 flex items-center gap-2 text-blue-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                {% trans "Notes" %}
            </h3>
            <div class="border rounded-lg p-4 bg-gray-50 shadow-inner">
                <p>{{ work_order.notes|linebreaks }}</p>
            </div>
        </div>
        {% endif %}
        
        <!-- Action Buttons - Enhanced with better styling -->
        <div class="flex flex-wrap justify-end gap-3 mt-4">
            <button id="change-status-btn" class="btn-primary flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {% trans "Change Status" %}
            </button>
            <a href="{% url 'work_orders:work_order_update' work_order.pk %}" class="btn-secondary flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                {% trans "Edit Work Order" %}
            </a>
            <button id="print-wo-btn" class="btn-outline flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                </svg>
                {% trans "Print" %}
            </button>
            <button id="take-payment-btn" class="btn-success flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                {% trans "Take Payment" %}
            </button>
        </div>
    </div>
    
    <!-- Operations -->
    <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
        <div class="p-4 border-b flex justify-between items-center">
            <h2 class="text-xl font-bold flex items-center gap-2 rtl:flex-row-reverse">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {% trans "Operations" %}
            </h2>
            <a href="#" id="add-operation-btn" class="btn-primary flex items-center gap-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {% trans "Add Operation" %}
            </a>
        </div>
            <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Seq" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Name" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Description" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Duration" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                        </tr>
                    </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                        {% for operation in work_order.operations.all %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ operation.sequence_number }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {{ operation.name }}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-800">
                            {{ operation.description|default:"لا يوجد وصف" }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            {% if operation.duration %}{{ operation.duration }} {% trans "min" %}{% else %}-{% endif %}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if operation.status == 'completed' %}bg-green-100 text-green-800
                                {% elif operation.status == 'in_progress' %}bg-yellow-100 text-yellow-800
                                {% elif operation.status == 'pending' %}bg-orange-100 text-orange-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ operation.get_status_display }}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            <div class="flex items-center space-x-1 rtl:space-x-reverse">
                                {% if operation.status != 'completed' %}
                                <button data-operation-id="{{ operation.id }}" class="complete-operation-btn p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors" title="{% trans 'Mark as Complete' %}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </button>
                                {% endif %}
                                <button data-operation-id="{{ operation.id }}" class="edit-operation-btn p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors" title="{% trans 'Edit Operation' %}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </button>
                                <button data-operation-id="{{ operation.id }}" class="delete-operation-btn p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors" title="{% trans 'Delete Operation' %}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                            </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-4 py-4 text-center text-sm text-gray-500">
                            <div class="flex flex-col items-center py-5">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p class="text-gray-500 text-lg mb-1">{% trans "No operations yet" %}</p>
                                <p class="text-gray-400 mb-3">{% trans "Add your first operation to get started" %}</p>
                                <button id="add-first-operation-btn" class="btn-primary flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    {% trans "Add First Operation" %}
                                </button>
                            </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
    </div>
    
    <!-- Materials -->
    <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
        <div class="p-4 border-b flex justify-between items-center">
            <h2 class="text-xl font-bold flex items-center gap-2 rtl:flex-row-reverse">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                {% trans "Materials" %}
            </h2>
            <a href="#" id="add-material-btn" class="btn-primary flex items-center gap-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {% trans "Add Material" %}
            </a>
        </div>
            <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Item" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Quantity" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Notes" %}
                        </th>
                        <th scope="col" class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                        </tr>
                    </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                        {% for material in work_order.materials.all %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                            <div class="flex items-center">
                                <div class="h-10 w-10 flex-shrink-0">
                                    <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4 rtl:mr-4 rtl:ml-0">
                                    <div class="font-medium text-gray-900">{{ material.item.name }}</div>
                                    {% if material.item.sku %}
                                        <div class="text-sm text-gray-500">{{ material.item.sku }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            <div class="flex items-center">
                                <span class="text-lg font-semibold text-gray-900">
                                    {% if material.quantity == material.quantity|floatformat:0|add:0 %}
                                        {{ material.quantity|floatformat:0 }}
                                    {% else %}
                                        {{ material.quantity|floatformat:2 }}
                                    {% endif %}
                                </span>
                                <span class="ml-2 text-sm text-gray-500">{{ material.unit_of_measure }}</span>
                            </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if material.is_consumed %}bg-green-100 text-green-800
                                {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                <div class="flex items-center gap-1">
                                    {% if material.is_consumed %}
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        {% trans "Consumed" %}
                                    {% else %}
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        {% trans "Available" %}
                                    {% endif %}
                                </div>
                            </span>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-800 max-w-xs truncate">
                            {{ material.notes|default:"-" }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-800">
                            <div class="flex items-center space-x-1 rtl:space-x-reverse">
                                {% if not material.is_consumed %}
                                <button data-material-id="{{ material.id }}" class="consume-material-btn p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors" title="{% trans 'Mark as Consumed' %}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </button>
                                {% endif %}
                                <button data-material-id="{{ material.id }}" class="edit-material-btn p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors" title="{% trans 'Edit Material' %}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </button>
                                <button data-material-id="{{ material.id }}" class="delete-material-btn p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors" title="{% trans 'Delete Material' %}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-4 py-4 text-center text-sm text-gray-500">
                            <div class="flex flex-col items-center py-5">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                </svg>
                                <p class="text-gray-500 text-lg mb-1">{% trans "No materials yet" %}</p>
                                <p class="text-gray-400 mb-3">{% trans "Add materials required for this work order" %}</p>
                                <button id="add-first-material-btn" class="btn-primary flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    {% trans "Add First Material" %}
                                </button>
                            </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
    </div>

    <!-- Attachments Section -->
    <div class="bg-white shadow rounded-lg mb-6 p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold flex items-center gap-2 rtl:flex-row-reverse">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
                {% trans "Attachments" %}
            </h2>
            <button id="add-attachment-btn" class="btn-primary flex items-center gap-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {% trans "Add Attachment" %}
            </button>
        </div>

        {% if work_order.attachments.all %}
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {% for attachment in work_order.attachments.all %}
                    <div class="border rounded-lg overflow-hidden bg-white hover:shadow-md transition duration-200">
                        <div class="h-32 bg-gray-100 flex items-center justify-center">
                            {% if attachment.file_type == 'image' %}
                                <img src="{{ attachment.file.url }}" alt="{{ attachment.title }}" class="h-full w-full object-cover">
                            {% else %}
                                <div class="p-4 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                            {% endif %}
                        </div>
                        <div class="p-3">
                            <h3 class="font-medium text-sm truncate">{{ attachment.title }}</h3>
                            <p class="text-xs text-gray-500">{{ attachment.file_size|filesizeformat }}</p>
                            <div class="flex justify-between mt-2">
                                <a href="{{ attachment.file.url }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                    {% trans "View" %}
                                </a>
                                <button data-attachment-id="{{ attachment.id }}" class="delete-attachment-btn text-red-600 hover:text-red-800 text-xs flex items-center gap-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    {% trans "Delete" %}
                                </button>
                </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="flex flex-col items-center py-8 bg-gray-50 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
                <p class="text-gray-500 text-lg mb-1">{% trans "No attachments yet" %}</p>
                <p class="text-gray-400 mb-3">{% trans "Add photos, documents or other files" %}</p>
                <button id="add-first-attachment-btn" class="btn-primary flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    {% trans "Upload Files" %}
                </button>
        </div>
        {% endif %}
    </div>

    <!-- Status Change Modal -->
    <div id="status-change-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-xl font-bold mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {% trans "Change Work Order Status" %}
            </h3>
            <form id="status-change-form" method="post" action="{% url 'work_orders:change_work_order_status' work_order.pk %}">
                {% csrf_token %}
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "New Status" %}</label>
                    <select name="status" class="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="draft">{% trans "Draft" %}</option>
                        <option value="planned">{% trans "Planned" %}</option>
                        <option value="in_progress">{% trans "In Progress" %}</option>
                        <option value="on_hold">{% trans "On Hold" %}</option>
                        <option value="completed">{% trans "Completed" %}</option>
                        <option value="cancelled">{% trans "Cancelled" %}</option>
                    </select>
                </div>
                <div class="flex justify-end gap-2">
                    <button type="button" id="close-status-modal" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition">
                        {% trans "Cancel" %}
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                        {% trans "Save Changes" %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Material Modal -->
    <div id="edit-material-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-xl font-bold mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                {% trans "Edit Material" %}
            </h3>
            <form id="edit-material-form">
                {% csrf_token %}
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "Item" %}</label>
                    <input type="text" id="edit-material-item" class="form-input block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100" readonly>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "Quantity" %}</label>
                    <input type="number" id="edit-material-quantity" step="0.01" min="0" class="form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "Unit" %}</label>
                    <input type="text" id="edit-material-unit" class="form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "Notes" %}</label>
                    <textarea id="edit-material-notes" rows="3" class="form-textarea block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                <div class="flex justify-end gap-2">
                    <button type="button" id="close-edit-material-modal" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition">
                        {% trans "Cancel" %}
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                        {% trans "Save Changes" %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Material Modal -->
    <div id="delete-material-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-xl font-bold mb-4 flex items-center gap-2 text-red-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {% trans "Delete Material" %}
            </h3>
            <p class="text-gray-600 mb-6">
                {% trans "Are you sure you want to delete this material? This action cannot be undone." %}
            </p>
            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <div class="flex items-center">
                    <div class="h-10 w-10 flex-shrink-0">
                        <div class="h-10 w-10 rounded-lg bg-red-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="font-medium text-gray-900" id="delete-material-name"></div>
                        <div class="text-sm text-gray-500" id="delete-material-quantity"></div>
                    </div>
                </div>
            </div>
            <div class="flex justify-end gap-2">
                <button type="button" id="close-delete-material-modal" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition">
                    {% trans "Cancel" %}
                </button>
                <button type="button" id="confirm-delete-material" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition">
                    {% trans "Delete" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Operation Modal -->
    <div id="edit-operation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-xl font-bold mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {% trans "Edit Operation" %}
            </h3>
            <form id="edit-operation-form">
                {% csrf_token %}
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "Operation Name" %}</label>
                    <input type="text" id="edit-operation-name" class="form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "Duration (minutes)" %}</label>
                    <input type="number" id="edit-operation-duration" min="0" class="form-input block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "Description" %}</label>
                    <textarea id="edit-operation-description" rows="3" class="form-textarea block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">{% trans "Notes" %}</label>
                    <textarea id="edit-operation-notes" rows="3" class="form-textarea block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                <div class="flex justify-end gap-2">
                    <button type="button" id="close-edit-operation-modal" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition">
                        {% trans "Cancel" %}
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
                        {% trans "Save Changes" %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Operation Modal -->
    <div id="delete-operation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 class="text-xl font-bold mb-4 flex items-center gap-2 text-red-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {% trans "Delete Operation" %}
            </h3>
            <p class="text-gray-600 mb-6">
                {% trans "Are you sure you want to delete this operation? This action cannot be undone." %}
            </p>
            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <div class="flex items-center">
                    <div class="h-10 w-10 flex-shrink-0">
                        <div class="h-10 w-10 rounded-lg bg-red-100 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="font-medium text-gray-900" id="delete-operation-name"></div>
                        <div class="text-sm text-gray-500" id="delete-operation-duration"></div>
                    </div>
                </div>
            </div>
            <div class="flex justify-end gap-2">
                <button type="button" id="close-delete-operation-modal" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition">
                    {% trans "Cancel" %}
                </button>
                <button type="button" id="confirm-delete-operation" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition">
                    {% trans "Delete" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status change modal functionality
    const statusChangeBtn = document.getElementById('change-status-btn');
    const statusChangeModal = document.getElementById('status-change-modal');
    const closeStatusModal = document.getElementById('close-status-modal');
    
    if (statusChangeBtn) {
        statusChangeBtn.addEventListener('click', function() {
            statusChangeModal.classList.remove('hidden');
        });
    }
    
    if (closeStatusModal) {
        closeStatusModal.addEventListener('click', function() {
            statusChangeModal.classList.add('hidden');
        });
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target === statusChangeModal) {
            statusChangeModal.classList.add('hidden');
        }
    });
    
    // Operation action buttons
    const completeOperationBtns = document.querySelectorAll('.complete-operation-btn');
    completeOperationBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const operationId = this.dataset.operationId;
            completeOperation(operationId);
        });
    });
    
    // Material action buttons
    const consumeMaterialBtns = document.querySelectorAll('.consume-material-btn');
    consumeMaterialBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const materialId = this.dataset.materialId;
            consumeMaterial(materialId);
        });
    });
    
    // Add operation button
    const addOperationBtn = document.getElementById('add-operation-btn');
    const addFirstOperationBtn = document.getElementById('add-first-operation-btn');
    
    if (addOperationBtn) {
        addOperationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddOperationForm();
        });
    }
    
    if (addFirstOperationBtn) {
        addFirstOperationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddOperationForm();
        });
    }
    
    // Add material button
    const addMaterialBtn = document.getElementById('add-material-btn');
    const addFirstMaterialBtn = document.getElementById('add-first-material-btn');
    
    if (addMaterialBtn) {
        addMaterialBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddMaterialForm();
        });
    }
    
    if (addFirstMaterialBtn) {
        addFirstMaterialBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddMaterialForm();
        });
    }
    
    // Add attachment button
    const addAttachmentBtn = document.getElementById('add-attachment-btn');
    const addFirstAttachmentBtn = document.getElementById('add-first-attachment-btn');
    
    if (addAttachmentBtn) {
        addAttachmentBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddAttachmentForm();
        });
    }
    
    if (addFirstAttachmentBtn) {
        addFirstAttachmentBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddAttachmentForm();
        });
    }
    
    // Print work order
    const printWoBtn = document.getElementById('print-wo-btn');
    if (printWoBtn) {
        printWoBtn.addEventListener('click', function() {
            window.print();
        });
    }
    
    // Take payment button
    const takePaymentBtn = document.getElementById('take-payment-btn');
    if (takePaymentBtn) {
        takePaymentBtn.addEventListener('click', function() {
            window.location.href = "{% url 'work_orders:work_order_payment' work_order.pk %}";
        });
    }
    
    // API functions
    function completeOperation(operationId) {
        fetch(`{% url 'work_orders:api_root' %}operation/${operationId}/complete/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                alert('Error completing operation');
            }
        });
    }
    
    function consumeMaterial(materialId) {
        fetch(`{% url 'work_orders:api_root' %}material/${materialId}/consume/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                alert('Error marking material as consumed');
            }
        });
    }
    
    function showAddOperationForm() {
        // Redirect to the add operation page
        window.location.href = "{% url 'work_orders:add_operation' work_order.pk %}";
    }
    
    function showAddMaterialForm() {
        // Redirect to the add material page
        window.location.href = "{% url 'work_orders:add_material' work_order.pk %}";
    }
    
    function showAddAttachmentForm() {
        // Redirect to the add attachment page
        window.location.href = "{% url 'work_orders:add_attachment' work_order.pk %}";
    }
    
    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Material edit and delete functionality
    let currentMaterialId = null;
    let currentOperationId = null;

    // Edit Material Modal
    const editMaterialModal = document.getElementById('edit-material-modal');
    const editMaterialForm = document.getElementById('edit-material-form');
    const closeEditMaterialModal = document.getElementById('close-edit-material-modal');

    // Delete Material Modal
    const deleteMaterialModal = document.getElementById('delete-material-modal');
    const closeDeleteMaterialModal = document.getElementById('close-delete-material-modal');
    const confirmDeleteMaterial = document.getElementById('confirm-delete-material');

    // Edit Operation Modal
    const editOperationModal = document.getElementById('edit-operation-modal');
    const editOperationForm = document.getElementById('edit-operation-form');
    const closeEditOperationModal = document.getElementById('close-edit-operation-modal');

    // Delete Operation Modal
    const deleteOperationModal = document.getElementById('delete-operation-modal');
    const closeDeleteOperationModal = document.getElementById('close-delete-operation-modal');
    const confirmDeleteOperation = document.getElementById('confirm-delete-operation');

    // Material Edit Button Event Listeners
    document.querySelectorAll('.edit-material-btn').forEach(button => {
        button.addEventListener('click', async function() {
            currentMaterialId = this.dataset.materialId;
            try {
                const response = await fetch(`/ar/work-orders/api/materials/${currentMaterialId}/`, {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                });
                
                if (response.ok) {
                    const material = await response.json();
                    document.getElementById('edit-material-item').value = material.item_name;
                    document.getElementById('edit-material-quantity').value = material.quantity;
                    document.getElementById('edit-material-unit').value = material.unit_of_measure;
                    document.getElementById('edit-material-notes').value = material.notes || '';
                    editMaterialModal.classList.remove('hidden');
                } else {
                    alert('Error loading material data');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error loading material data');
            }
        });
    });

    // Material Delete Button Event Listeners
    document.querySelectorAll('.delete-material-btn').forEach(button => {
        button.addEventListener('click', async function() {
            currentMaterialId = this.dataset.materialId;
            try {
                const response = await fetch(`/ar/work-orders/api/materials/${currentMaterialId}/`, {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                });
                
                if (response.ok) {
                    const material = await response.json();
                    document.getElementById('delete-material-name').textContent = material.item_name;
                    document.getElementById('delete-material-quantity').textContent = `${material.quantity} ${material.unit_of_measure}`;
                    deleteMaterialModal.classList.remove('hidden');
                } else {
                    alert('Error loading material data');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error loading material data');
            }
        });
    });

    // Operation Edit Button Event Listeners
    document.querySelectorAll('.edit-operation-btn').forEach(button => {
        button.addEventListener('click', async function() {
            currentOperationId = this.dataset.operationId;
            try {
                const response = await fetch(`/ar/work-orders/api/operations/${currentOperationId}/`, {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                });
                
                if (response.ok) {
                    const operation = await response.json();
                    document.getElementById('edit-operation-name').value = operation.name;
                    document.getElementById('edit-operation-duration').value = operation.duration;
                    document.getElementById('edit-operation-description').value = operation.description || '';
                    document.getElementById('edit-operation-notes').value = operation.notes || '';
                    editOperationModal.classList.remove('hidden');
                } else {
                    alert('Error loading operation data');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error loading operation data');
            }
        });
    });

    // Operation Delete Button Event Listeners
    document.querySelectorAll('.delete-operation-btn').forEach(button => {
        button.addEventListener('click', async function() {
            currentOperationId = this.dataset.operationId;
            try {
                const response = await fetch(`/ar/work-orders/api/operations/${currentOperationId}/`, {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                });
                
                if (response.ok) {
                    const operation = await response.json();
                    document.getElementById('delete-operation-name').textContent = operation.name;
                    document.getElementById('delete-operation-duration').textContent = `${operation.duration} minutes`;
                    deleteOperationModal.classList.remove('hidden');
                } else {
                    alert('Error loading operation data');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error loading operation data');
            }
        });
    });

    // Close modal event listeners
    closeEditMaterialModal.addEventListener('click', () => {
        editMaterialModal.classList.add('hidden');
    });

    closeDeleteMaterialModal.addEventListener('click', () => {
        deleteMaterialModal.classList.add('hidden');
    });

    closeEditOperationModal.addEventListener('click', () => {
        editOperationModal.classList.add('hidden');
    });

    closeDeleteOperationModal.addEventListener('click', () => {
        deleteOperationModal.classList.add('hidden');
    });

    // Close modals when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target === editMaterialModal) {
            editMaterialModal.classList.add('hidden');
        }
        if (e.target === deleteMaterialModal) {
            deleteMaterialModal.classList.add('hidden');
        }
        if (e.target === editOperationModal) {
            editOperationModal.classList.add('hidden');
        }
        if (e.target === deleteOperationModal) {
            deleteOperationModal.classList.add('hidden');
        }
    });

    // Edit Material Form Submit
    editMaterialForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = {
            quantity: document.getElementById('edit-material-quantity').value,
            unit_of_measure: document.getElementById('edit-material-unit').value,
            notes: document.getElementById('edit-material-notes').value
        };

        try {
            const response = await fetch(`/ar/work-orders/api/materials/${currentMaterialId}/`, {
                method: 'PUT',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                editMaterialModal.classList.add('hidden');
                location.reload(); // Refresh to show updated data
            } else {
                const error = await response.json();
                alert('Error updating material: ' + (error.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error updating material');
        }
    });

    // Edit Operation Form Submit
    editOperationForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = {
            name: document.getElementById('edit-operation-name').value,
            duration: document.getElementById('edit-operation-duration').value,
            description: document.getElementById('edit-operation-description').value,
            notes: document.getElementById('edit-operation-notes').value
        };

        try {
            const response = await fetch(`/ar/work-orders/api/operations/${currentOperationId}/`, {
                method: 'PUT',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                editOperationModal.classList.add('hidden');
                location.reload(); // Refresh to show updated data
            } else {
                const error = await response.json();
                alert('Error updating operation: ' + (error.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error updating operation');
        }
    });

    // Confirm Delete Material
    confirmDeleteMaterial.addEventListener('click', async function() {
        try {
            const response = await fetch(`/ar/work-orders/api/materials/${currentMaterialId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                deleteMaterialModal.classList.add('hidden');
                location.reload(); // Refresh to show updated data
            } else {
                const error = await response.json();
                alert('Error deleting material: ' + (error.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error deleting material');
        }
    });

    // Confirm Delete Operation
    confirmDeleteOperation.addEventListener('click', async function() {
        try {
            const response = await fetch(`/ar/work-orders/api/operations/${currentOperationId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                deleteOperationModal.classList.add('hidden');
                location.reload(); // Refresh to show updated data
            } else {
                const error = await response.json();
                alert('Error deleting operation: ' + (error.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Error deleting operation');
        }
    });

    // Mark Material as Consumed
    document.querySelectorAll('.consume-material-btn').forEach(button => {
        button.addEventListener('click', async function() {
            const materialId = this.dataset.materialId;
            try {
                const response = await fetch(`/ar/work-orders/api/materials/${materialId}/consume/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    location.reload(); // Refresh to show updated status
                } else {
                    const error = await response.json();
                    alert('Error marking material as consumed: ' + (error.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error marking material as consumed');
            }
        });
    });
});
</script>
{% endblock %}