{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<style>
    /* Enhanced Tab Styling */
    .tab-animation {
        transition: all 0.3s ease;
        position: relative;
    }
    
    .tab-animation:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Active tab enhanced styling */
    .tab-animation[aria-selected="true"] {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        border-bottom: 3px solid #2563eb;
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
        transform: translateY(-1px);
    }
    
    .tab-animation[aria-selected="true"]:before {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #2563eb, #3b82f6);
        border-radius: 2px 2px 0 0;
    }
    
    .tab-animation[aria-selected="true"] span {
        font-weight: 900;
        color: #1d4ed8;
    }
    
    .tab-animation[aria-selected="true"] svg {
        color: #1d4ed8;
        filter: drop-shadow(0 1px 2px rgba(29, 78, 216, 0.3));
    }
</style>
{% endblock %}

{% block title %}{{ title }}{% endblock %}

{% block extra_head %}
<style>
    /* Enhanced RTL Support Styles */
    .rtl {
        direction: rtl;
        text-align: right;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }
    
    /* Improve Arabic text rendering with enhanced typography */
    h1, h2, h3, h4, h5, h6, p, span, button, a, label, time {
        font-family: 'Tahoma', 'Arial', sans-serif;
    }
    
    /* Enhanced typography for better visibility */
    .text-enhanced {
        font-weight: 700 !important;
        font-size: 1.1em !important;
    }
    
    .value-text {
        font-weight: 600 !important;
        font-size: 1.05em !important;
    }
    
    /* Color coding for different value types */
    .work-order-number { color: #1e40af !important; font-weight: 800 !important; }
    .customer-name { color: #059669 !important; font-weight: 700 !important; }
    .vehicle-info { color: #92400e !important; font-weight: 700 !important; }
    .technician-name { color: #7c3aed !important; font-weight: 700 !important; }
    .date-info { color: #1f2937 !important; font-weight: 600 !important; }
    
    /* Enhanced button styling */
    .btn-enhanced {
        font-weight: 600 !important;
        transition: all 0.2s ease !important;
    }
    
    .btn-enhanced:hover:not(.cursor-not-allowed) {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    }
    
    .cursor-not-allowed:hover {
        transform: none !important;
        box-shadow: none !important;
    }
    
    /* Modal improvements */
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.5);
        z-index: 9998;
    }
    
    .modal-content {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 9999 !important;
        max-height: 90vh;
        overflow-y: auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
    
    /* Modal overlay */
    .modal-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        z-index: 9998 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    .modal-overlay[style*="display: none"] {
        display: none !important;
    }
    
    /* Modal container */
    .modal-container {
        background: white !important;
        border-radius: 8px !important;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
        max-height: 90vh !important;
        overflow-y: auto !important;
        margin: 20px !important;
        max-width: 600px !important;
        width: 90% !important;
        z-index: 9999 !important;
        position: relative !important;
    }
    
    /* Fix flex containers for RTL */
    .rtl .flex:not(.flex-col):not([class*="items-center"]) {
        flex-direction: row-reverse;
    }
    
    /* Fix for icon margins */
    .rtl svg.mr-1, 
    .rtl svg.mr-2,
    .rtl svg.mr-3,
    .rtl svg.mr-4 {
        margin-right: 0 !important;
    }
    
    .rtl svg.mr-1 { margin-left: 0.25rem !important; }
    .rtl svg.mr-2 { margin-left: 0.5rem !important; }
    .rtl svg.mr-3 { margin-left: 0.75rem !important; }
    .rtl svg.mr-4 { margin-left: 1rem !important; }
    
    /* Fix spacing classes */
    .rtl .ml-1 { margin-left: 0; margin-right: 0.25rem !important; }
    .rtl .ml-2 { margin-left: 0; margin-right: 0.5rem !important; }
    .rtl .ml-3 { margin-left: 0; margin-right: 0.75rem !important; }
    .rtl .ml-4 { margin-left: 0; margin-right: 1rem !important; }
    
    .rtl .mr-1 { margin-right: 0; margin-left: 0.25rem !important; }
    .rtl .mr-2 { margin-right: 0; margin-left: 0.5rem !important; }
    .rtl .mr-3 { margin-right: 0; margin-left: 0.75rem !important; }
    .rtl .mr-4 { margin-right: 0; margin-left: 1rem !important; }
    
    .rtl .pr-1 { padding-right: 0; padding-left: 0.25rem !important; }
    .rtl .pr-2 { padding-right: 0; padding-left: 0.5rem !important; }
    .rtl .pr-3 { padding-right: 0; padding-left: 0.75rem !important; }
    .rtl .pr-4 { padding-right: 0; padding-left: 1rem !important; }
    
    .rtl .pl-1 { padding-left: 0; padding-right: 0.25rem !important; }
    .rtl .pl-2 { padding-left: 0; padding-right: 0.5rem !important; }
    .rtl .pl-3 { padding-left: 0; padding-right: 0.75rem !important; }
    .rtl .pl-4 { padding-left: 0; padding-right: 1rem !important; }
    
    /* Fix text alignment */
    .rtl .text-left { text-align: right !important; }
    .rtl .text-right { text-align: left !important; }
    
    /* Fix borders */
    .rtl .border-l { border-left: 0 !important; border-right: 1px solid #e5e7eb !important; }
    .rtl .border-r { border-right: 0 !important; border-left: 1px solid #e5e7eb !important; }
    
    .rtl .border-l-2 { border-left: 0 !important; border-right: 2px solid #e5e7eb !important; }
    .rtl .border-r-2 { border-right: 0 !important; border-left: 2px solid #e5e7eb !important; }
    
    .rtl .border-l-4 { border-left: 0 !important; border-right: 4px solid #e5e7eb !important; }
    .rtl .border-r-4 { border-right: 0 !important; border-left: 4px solid #e5e7eb !important; }
    
    /* Timeline specific styles */
    .rtl .timeline-rtl {
        flex-direction: row-reverse !important;
    }
    
    /* Fix for timeline in RTL */
    .rtl ol.flex-row {
        flex-direction: row-reverse !important;
    }
    
    /* Fix for tabs */
    .rtl ul[data-tabs-toggle] {
        flex-direction: row-reverse !important;
    }
    
    /* Fix for drawer positioning */
    .rtl .right-0 {
        right: auto !important;
        left: 0 !important;
    }
    
    .rtl .left-0 {
        left: auto !important;
        right: 0 !important;
    }
    
    .rtl .translate-x-full {
        transform: translateX(-100%) !important;
    }
    
    /* Fix dropdown positioning */
    .rtl .dropdown-menu {
        left: auto !important;
        right: 0 !important;
    }
    
    /* Status modal specific overrides - no dimming */
    #status-change-modal {
        background: none !important;
    }
    
    #status-change-modal.modal-backdrop,
    #status-change-modal .modal-backdrop,
    #status-change-modal.modal-overlay,
    #status-change-modal .modal-overlay {
        display: none !important;
        background: none !important;
    }
</style>
{% endblock %}

{% block content %}
{% language 'ar' %}
<!-- Main Container with Always Visible Action Panel -->
<div class="container-fluid w-full px-1 md:px-2">
    <!-- Work Order Header Card with compact timeline and side panel -->
    <div class="flex rtl:flex-row-reverse flex-wrap">
        <!-- Main Content Area -->
        <div class="w-full pr-0 lg:pr-2 rtl:pr-0 rtl:pl-2">
            <div class="bg-white shadow-sm rounded-lg p-2 md:p-3 mb-2">
                <!-- Back Button and Change Status Row -->
                <div class="flex justify-between items-center mb-0">
                    <a href="{% url 'work_orders:work_order_list' %}" class="btn-enhanced inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500 shadow">
                        <svg class="w-3 h-3 mr-1 rtl:ml-1 rtl:mr-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        <span class="value-text">{% trans "عودة" %}</span>
                    </a>
                    
                    <button type="button" id="status-change-btn-top" class="btn-enhanced text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-bold rounded-lg text-sm px-4 py-2 flex items-center shadow-lg">
                        <svg class="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span class="value-text">{% trans "تغيير الحالة" %}</span>
                    </button>
                </div>
                
                <!-- Improved Timeline UI in Arabic with RTL Support - Moved Up More -->
                <div class="relative mx-auto max-w-5xl mb-1 py-0">
                    <!-- Timeline Track -->
                    <div class="absolute top-[24px] left-0 right-0 h-[3px] bg-gray-200"></div>
                    
                    <!-- Timeline Steps with RTL Support and On Hold -->
                    <div class="flex flex-row-reverse items-center justify-between">
                        <!-- Created/Started Step -->
                        <div class="flex flex-col items-center text-center z-10 px-1">
                            <div class="flex items-center justify-center w-[48px] h-[48px] rounded-full bg-white p-1 border-2 border-blue-600">
                                <div class="flex items-center justify-center w-full h-full rounded-full bg-blue-600">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-2">
                                <h3 class="font-bold text-gray-900 text-sm">{% trans "تم الإنشاء" %}</h3>
                                <time class="text-sm font-bold text-blue-600 date-info" dir="ltr">{{ work_order.created_at|date:"Y-m-d" }}</time>
                            </div>
                        </div>
                        
                        <!-- Planned Step -->
                        <div class="flex flex-col items-center text-center z-10 px-1">
                            <div class="flex items-center justify-center w-[48px] h-[48px] rounded-full bg-white p-1 border-2 {% if work_order.status == 'planned' %}border-indigo-600{% else %}border-gray-300{% endif %}">
                                <div class="flex items-center justify-center w-full h-full rounded-full {% if work_order.status == 'planned' %}bg-indigo-600{% else %}bg-gray-100{% endif %}">
                                    <svg class="w-6 h-6 {% if work_order.status == 'planned' %}text-white{% else %}text-gray-400{% endif %}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-2">
                                <h3 class="font-bold text-gray-900 text-sm">{% trans "استقبال" %}</h3>
                                <time class="text-sm font-bold {% if work_order.status == 'planned' %}text-indigo-600{% else %}text-gray-500{% endif %} date-info" dir="ltr">{{ work_order.planned_start_date|date:"Y-m-d"|default:"-" }}</time>
                            </div>
                        </div>
                        
                        <!-- In Progress Step -->
                        <div class="flex flex-col items-center text-center z-10 px-1">
                            <div class="flex items-center justify-center w-[48px] h-[48px] rounded-full bg-white p-1 border-2 {% if work_order.status == 'in_progress' %}border-orange-600{% else %}border-gray-300{% endif %}">
                                <div class="flex items-center justify-center w-full h-full rounded-full {% if work_order.status == 'in_progress' %}bg-orange-600{% else %}bg-gray-100{% endif %}">
                                    <svg class="w-6 h-6 {% if work_order.status == 'in_progress' %}text-white{% else %}text-gray-400{% endif %}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-2">
                                <h3 class="font-bold text-gray-900 text-sm">{% trans "قيد التنفيذ" %}</h3>
                                <time class="text-sm font-bold {% if work_order.status == 'in_progress' %}text-orange-600{% else %}text-gray-500{% endif %} date-info" dir="ltr">{{ work_order.actual_start_date|date:"Y-m-d"|default:"-" }}</time>
                            </div>
                        </div>
                        
                        <!-- On Hold Step -->
                        <div class="flex flex-col items-center text-center z-10 px-1">
                            <div class="flex items-center justify-center w-[48px] h-[48px] rounded-full bg-white p-1 border-2 {% if work_order.status == 'on_hold' %}border-yellow-600{% else %}border-gray-300{% endif %}">
                                <div class="flex items-center justify-center w-full h-full rounded-full {% if work_order.status == 'on_hold' %}bg-yellow-600{% else %}bg-gray-100{% endif %}">
                                    <svg class="w-6 h-6 {% if work_order.status == 'on_hold' %}text-white{% else %}text-gray-400{% endif %}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-2">
                                <h3 class="font-bold text-gray-900 text-sm">{% trans "معلق" %}</h3>
                                <time class="text-sm font-bold {% if work_order.status == 'on_hold' %}text-yellow-600{% else %}text-gray-500{% endif %} date-info" dir="ltr">-</time>
                            </div>
                        </div>
                        
                        <!-- Completed Step -->
                        <div class="flex flex-col items-center text-center z-10 px-1">
                            <div class="flex items-center justify-center w-[48px] h-[48px] rounded-full bg-white p-1 border-2 {% if work_order.status == 'completed' %}border-green-600{% else %}border-gray-300{% endif %}">
                                <div class="flex items-center justify-center w-full h-full rounded-full {% if work_order.status == 'completed' %}bg-green-600{% else %}bg-gray-100{% endif %}">
                                    <svg class="w-6 h-6 {% if work_order.status == 'completed' %}text-white{% else %}text-gray-400{% endif %}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-2">
                                <h3 class="font-bold text-gray-900 text-sm">{% trans "مكتمل" %}</h3>
                                <time class="text-sm font-bold {% if work_order.status == 'completed' %}text-green-600{% else %}text-gray-500{% endif %} date-info" dir="ltr">{{ work_order.actual_end_date|date:"Y-m-d"|default:"-" }}</time>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Compact Work Order Header with integrated tabs -->
                <div class="bg-white rounded-lg p-3 mb-3 border border-gray-200 shadow-sm">
                    <!-- Top row: ID and badges -->
                    <div class="flex justify-between items-center mb-2">
                        <div class="flex items-center space-x-3 rtl:space-x-reverse">
                            <h1 class="text-lg md:text-xl work-order-number">{{ work_order.work_order_number }}</h1>
                            {% if work_order.service_center %}
                            <span class="px-2 py-1 text-xs font-bold rounded bg-blue-100 text-blue-800 border border-blue-300 inline-flex items-center">
                                <svg class="inline-block w-3 h-3 mr-1 rtl:ml-1 rtl:mr-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span class="value-text">{{ work_order.service_center.name }}</span>
                            </span>
                            {% endif %}
                        </div>
                        
                        <div class="flex space-x-2 rtl:space-x-reverse items-center">
                            <!-- Compact Status badge -->
                            <span class="px-2 py-1 text-xs font-bold rounded-full rtl border inline-flex items-center
                        {% if work_order.status == 'draft' %}bg-gray-100 text-gray-800 border-gray-300
                        {% elif work_order.status == 'planned' %}bg-indigo-100 text-indigo-800 border-indigo-300
                        {% elif work_order.status == 'in_progress' %}bg-orange-100 text-orange-800 border-orange-300
                        {% elif work_order.status == 'on_hold' %}bg-yellow-100 text-yellow-800 border-yellow-300
                        {% elif work_order.status == 'completed' %}bg-green-100 text-green-800 border-green-300
                        {% elif work_order.status == 'cancelled' %}bg-red-100 text-red-800 border-red-300
                                {% endif %}" dir="rtl">
                                                                  <span class="value-text">
                                      {% if work_order.status == 'planned' %}استقبال
                                    {% elif work_order.status == 'in_progress' %}قيد التنفيذ
                                    {% elif work_order.status == 'on_hold' %}معلق
                                    {% elif work_order.status == 'completed' %}مكتمل
                                    {% elif work_order.status == 'cancelled' %}ملغي
                                    {% else %}{{ work_order.get_status_display }}{% endif %}
                                </span>
                            </span>
                            
                            <!-- Compact Priority badge -->
                            <span class="px-2 py-1 text-xs font-bold rounded-full rtl border inline-flex items-center
                        {% if work_order.priority == 'low' %}bg-gray-100 text-gray-800 border-gray-300
                        {% elif work_order.priority == 'medium' %}bg-blue-100 text-blue-800 border-blue-300
                        {% elif work_order.priority == 'high' %}bg-orange-100 text-orange-800 border-orange-300
                        {% elif work_order.priority == 'critical' %}bg-red-100 text-red-800 border-red-300
                                {% endif %}" dir="rtl">
                                <span class="value-text">
                                    {% if work_order.priority == 'low' %}منخفضة
                                    {% elif work_order.priority == 'medium' %}متوسطة
                                    {% elif work_order.priority == 'high' %}عالية
                                    {% elif work_order.priority == 'critical' %}حرجة
                                    {% else %}{{ work_order.get_priority_display }}{% endif %}
                                </span>
                            </span>
                        </div>
                    </div>
                    
                    <!-- Bold Tabbed navigation with vertical separators -->
                    <div class="border-t border-gray-300 pt-1 mt-1">
                        <ul class="flex text-sm font-extrabold text-center justify-center divide-x divide-gray-300 rtl:divide-x-reverse" id="mainTabs" data-tabs-toggle="#mainTabContent" role="tablist">
                        <li class="flex-1" role="presentation">
                            <button class="inline-block p-2 w-full text-blue-600 font-extrabold hover:text-blue-800 transition-colors tab-animation" id="info-tab" data-tabs-target="#info" type="button" role="tab" aria-controls="info" aria-selected="true">
                                <svg class="w-4 h-4 mx-auto mb-1" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="font-black">{% trans "معلومات" %}</span>
                            </button>
                        </li>
                        <li class="flex-1" role="presentation">
                            <button class="inline-block p-2 w-full text-green-600 font-extrabold hover:text-green-800 transition-colors tab-animation" id="operations-tab" data-tabs-target="#operations" type="button" role="tab" aria-controls="operations" aria-selected="false">
                                <svg class="w-4 h-4 mx-auto mb-1" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span class="font-black">{% trans "الصيانه" %}</span>
                            </button>
                        </li>
                        <li class="flex-1" role="presentation">
                            <button class="inline-block p-2 w-full text-orange-600 font-extrabold hover:text-orange-800 transition-colors tab-animation" id="materials-tab" data-tabs-target="#materials" type="button" role="tab" aria-controls="materials" aria-selected="false">
                                <svg class="w-4 h-4 mx-auto mb-1" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                <span class="font-black">{% trans "القطع" %}</span>
                            </button>
                        </li>
                        <li class="flex-1" role="presentation">
                            <button class="inline-block p-2 w-full text-purple-600 font-extrabold hover:text-purple-800 transition-colors tab-animation" id="notes-tab" data-tabs-target="#notes" type="button" role="tab" aria-controls="notes" aria-selected="false">
                                <svg class="w-4 h-4 mx-auto mb-1" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span class="font-black">{% trans "الملاحظات" %}</span>
                            </button>
                        </li>
                        <li class="flex-1" role="presentation">
                            <button class="inline-block p-2 w-full text-red-600 font-extrabold hover:text-red-800 transition-colors tab-animation" id="actions-tab" data-tabs-target="#actions" type="button" role="tab" aria-controls="actions" aria-selected="false">
                                <svg class="w-4 h-4 mx-auto mb-1" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                <span class="font-black">{% trans "الإجراءات" %}</span>
                            </button>
                        </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Tab Content -->
                <div id="mainTabContent">
                    <!-- Info Tab -->
                    <div class="hidden p-2 rounded-lg" id="info" role="tabpanel" aria-labelledby="info-tab">
                        <!-- Info Grid - Three columns on larger screens with better spacing -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Column 1: Customer & Vehicle Section -->
                            <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 hover:shadow-xl transition-shadow duration-200">
                                <!-- Customer Info -->
            <div class="mb-4">
                                    <h3 class="flex items-center text-sm font-bold text-gray-900 mb-2 bg-gray-50 p-2 rounded-md border-r-4 border-blue-500">
                                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        {% trans "العميل" %}
                                    </h3>
                                    
                                    <div class="bg-gray-50 p-3 rounded-md border-l-4 border-green-500">
                {% if work_order.customer %}
                                            <p class="text-lg customer-name">{{ work_order.customer.full_name }}</p>
                                            <div class="mt-2 flex flex-col space-y-2 text-sm text-gray-700">
                    {% if work_order.customer.phone %}
                    <div class="flex items-center">
                                                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                                                        <span class="font-medium">{{ work_order.customer.phone }}</span>
                    </div>
                    {% endif %}
                    
                    {% if work_order.customer.email %}
                    <div class="flex items-center">
                                                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                                                        <span class="font-medium">{{ work_order.customer.email }}</span>
                    </div>
                    {% endif %}
                </div>
                {% elif work_order.customer_name %}
                                            <!-- Non-model customer info -->
                                            <p class="text-base font-semibold text-gray-900">{{ work_order.customer_name }}</p>
                <div class="mt-1 flex flex-col space-y-1 text-sm text-gray-600">
                    {% if work_order.customer_phone %}
                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span>{{ work_order.customer_phone }}</span>
                    </div>
                    {% endif %}
                    
                    {% if work_order.customer_email %}
                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <span>{{ work_order.customer_email }}</span>
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <p class="text-sm text-gray-500">{% trans "No customer information" %}</p>
                {% endif %}
                                    </div>
            </div>
            
                                <!-- Vehicle Info -->
            <div>
                                    <h3 class="flex items-center text-sm font-bold text-gray-900 mb-2 bg-gray-50 p-2 rounded-md border-r-4 border-blue-500">
                                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        {% trans "المركبة" %}
                                    </h3>
                                    
                                    <div class="bg-gray-50 p-3 rounded-md border-l-4 border-amber-600">
                {% if work_order.vehicle %}
                                            <p class="text-lg vehicle-info">{{ work_order.vehicle.make }} {{ work_order.vehicle.model }} {% if work_order.vehicle.year %}({{ work_order.vehicle.year }}){% endif %}</p>
                                            <div class="mt-2 flex flex-col space-y-2 text-sm text-gray-700">
                    {% if work_order.vehicle.license_plate %}
                    <div class="flex items-center">
                                                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"></path>
                        </svg>
                                                        <span class="font-medium">{{ work_order.vehicle.license_plate }}</span>
                    </div>
                    {% endif %}
                    
                    {% if work_order.vehicle.vin %}
                    <div class="flex items-center">
                                                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                        </svg>
                                                        <span class="font-medium">{% trans "رقم الهيكل" %}: {{ work_order.vehicle.vin }}</span>
                    </div>
                    {% endif %}
                    
                    {% if work_order.current_odometer %}
                    <div class="flex items-center">
                                                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                                                        <span class="font-medium">{% trans "عداد المسافة" %}: {{ work_order.current_odometer }} كم</span>
                    </div>
                    {% endif %}
                </div>
                {% elif work_order.service_item_serial %}
                                            <p class="text-base font-semibold text-gray-900">{% trans "الرقم التسلسلي" %}: {{ work_order.service_item_serial }}</p>
                {% else %}
                <p class="text-sm text-gray-500">{% trans "No vehicle information" %}</p>
                {% endif %}
            </div>
        </div>
            </div>
            
                            <!-- Column 2: Type and Description -->
                            <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 hover:shadow-xl transition-shadow duration-200">
                                <!-- Work Order Type -->
            <div class="mb-4">
                                    <h3 class="flex items-center text-sm font-bold text-gray-900 mb-2 bg-gray-50 p-2 rounded-md border-r-4 border-purple-500">
                                        <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                        </svg>
                                        {% trans "النوع" %}
                                    </h3>
                                    <div class="bg-gray-50 p-3 rounded-md border-l-4 border-purple-500">
                                        {% if work_order.work_order_type %}
                                            <p class="text-lg technician-name">{{ work_order.work_order_type.name }}</p>
                                        {% elif work_order.operation_category %}
                                            <p class="text-lg technician-name">{{ work_order.get_operation_category_display }}</p>
                                        {% else %}
                                            <p class="text-sm text-gray-500">{% trans "No work order type specified" %}</p>
                                        {% endif %}
                </div>
            </div>
            
                                <!-- Description -->
            <div>
                                    <h3 class="flex items-center text-sm font-bold text-gray-900 mb-2 bg-gray-50 p-2 rounded-md border-r-4 border-purple-500">
                                        <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                                        </svg>
                                        {% trans "الوصف" %}
                                    </h3>
                                    <div class="bg-gray-50 p-3 rounded-md border-l-4 border-purple-500">
                                        <p class="text-base value-text text-gray-800">{{ work_order.description }}</p>
                </div>
            </div>
        </div>
        
                            <!-- Column 3: Schedule -->
                            <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 hover:shadow-xl transition-shadow duration-200">
                                <h3 class="flex items-center text-sm font-bold text-gray-900 mb-2 bg-gray-50 p-2 rounded-md border-r-4 border-green-500">
                                    <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                                    {% trans "الجدول الزمني" %}
                                </h3>
                                <div class="bg-gray-50 p-3 rounded-md border-l-4 border-green-500">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="p-3 border-b border-gray-200 bg-white rounded-md">
                                            <p class="text-sm font-bold text-gray-600 mb-2">{% trans "بداية مخططة" %}</p>
                                            <p class="date-info text-lg">{{ work_order.planned_start_date|date:"Y-m-d H:i"|default:"-" }}</p>
                    </div>
                                        <div class="p-3 border-b border-gray-200 bg-white rounded-md">
                                            <p class="text-sm font-bold text-gray-600 mb-2">{% trans "نهاية مخططة" %}</p>
                                            <p class="date-info text-lg">{{ work_order.planned_end_date|date:"Y-m-d H:i"|default:"-" }}</p>
                    </div>
                                        <div class="p-3 border-b border-gray-200 bg-white rounded-md">
                                            <p class="text-sm font-bold text-gray-600 mb-2">{% trans "البداية الفعلية" %}</p>
                                            <p class="date-info text-lg">{{ work_order.actual_start_date|date:"Y-m-d H:i"|default:"-" }}</p>
                </div>
                                        <div class="p-3 bg-white rounded-md">
                                            <p class="text-sm font-bold text-gray-600 mb-2">{% trans "النهاية الفعلية" %}</p>
                                            <p class="date-info text-lg">{{ work_order.actual_end_date|date:"Y-m-d H:i"|default:"-" }}</p>
            </div>
                                    </div>
            </div>
        </div>
    </div>
</div>

                    <!-- Operations Tab -->
                    <div class="hidden p-2 rounded-lg" id="operations" role="tabpanel" aria-labelledby="operations-tab">
                        <!-- Operations Card -->
                        <div class="bg-white rounded-lg border p-4">
                            <!-- Info message -->
                            <div class="bg-blue-50 rounded-lg p-2 mb-4 flex items-start text-blue-800 text-sm">
                                <svg class="w-5 h-5 mr-2 shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "الصيانه هي عمليات فردية يجب إجراؤها كجزء من أمر العمل هذا." %}</span>
    </div>
    
                            <!-- Operations List -->
    <div class="overflow-x-auto">
        <table class="w-full text-sm text-left text-gray-500 border-collapse">
            <thead class="text-sm text-gray-800 uppercase bg-gradient-to-r from-gray-50 to-gray-100 border-b-2 border-gray-200">
                <tr class="divide-x divide-gray-300">
                    <th scope="col" class="px-4 py-4 font-bold text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <span class="font-black">{% trans "الاسم" %}</span>
                        </div>
                    </th>
                    <th scope="col" class="px-4 py-4 font-bold text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-black">{% trans "المدة" %}</span>
                        </div>
                    </th>
                    <th scope="col" class="px-4 py-4 font-bold text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-black">{% trans "الحالة" %}</span>
                        </div>
                    </th>
                    <th scope="col" class="px-4 py-4 font-bold text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                            </svg>
                            <span class="font-black">{% trans "الإجراءات" %}</span>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for operation in operations %}
                <tr class="hover:bg-blue-50 transition-colors duration-200 divide-x divide-gray-200">
                    <td class="px-4 py-4 text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span class="font-bold text-gray-900">{{ operation.name }}</span>
                        </div>
                    </td>
                    <td class="px-4 py-4 text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-bold text-gray-900">{{ operation.duration_minutes }} {% trans "دقيقة" %}</span>
                        </div>
                    </td>
                    <td class="px-4 py-4 text-center">
                        <span class="px-3 py-2 text-sm font-bold rounded-full border-2 inline-flex items-center
                            {% if operation.is_completed %}bg-green-100 text-green-800 border-green-300{% else %}bg-blue-100 text-blue-800 border-blue-300{% endif %}">
                            {% if operation.is_completed %}
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {% trans "مكتمل" %}
                                {% if operation.completed_at %}
                                <span class="text-xs text-gray-500 ml-1">
                                    {{ operation.completed_at|date:"Y-m-d" }}
                                </span>
                                {% endif %}
                            {% else %}
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {% trans "قيد الانتظار" %}
                            {% endif %}
                        </span>
                    </td>
                    <td class="px-4 py-4 text-center">
                        <div class="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                            {% if work_order.status in 'planned,in_progress,on_hold' %}
                                {% if not operation.is_completed %}
                                <button type="button" 
                                        class="text-green-700 hover:text-white border-2 border-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-bold rounded-lg text-xs p-2 transition-all duration-200"
                                        data-operation-id="{{ operation.id }}" 
                                        data-complete-url="{% url 'work_orders:api_complete_operation' operation.id %}"
                                        title="{% trans 'إكمال العملية' %}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </button>
                                {% endif %}
                                <button type="button" 
                                        class="text-blue-700 hover:text-white border-2 border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-bold rounded-lg text-xs p-2 transition-all duration-200"
                                        data-operation-id="{{ operation.id }}"
                                        title="{% trans 'تعديل العملية' %}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button type="button" 
                                        class="text-red-700 hover:text-white border-2 border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-bold rounded-lg text-xs p-2 transition-all duration-200"
                                        data-operation-id="{{ operation.id }}"
                                        title="{% trans 'حذف العملية' %}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            {% else %}
                                <span class="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded">{% trans "غير متاح" %}</span>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr class="border-b">
                    <td colspan="4" class="px-4 py-3 text-center text-gray-500">
                                                {% trans "لا توجد مهام" %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
</div>

                            <!-- Add operation button - Centered and larger for mobile -->
                            <div class="mt-4 flex justify-center">
                                {% if work_order.status in 'planned,in_progress,on_hold' %}
                                <button id="add-operation-btn-large" type="button" class="btn-enhanced text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-bold rounded-lg text-base px-6 py-3 flex items-center shadow-lg">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <span class="value-text">{% trans "إضافة صيانه" %}</span>
                                </button>
                                {% else %}
                                <div class="btn-enhanced text-gray-400 bg-gray-100 cursor-not-allowed font-bold rounded-lg text-base px-6 py-3 flex items-center shadow-lg" title="{% trans 'غير متاح في هذه الحالة' %}">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <span class="value-text">{% trans "إضافة صيانه" %}</span>
                                </div>
                                {% endif %}
                            </div>
        </div>
    </div>
    
                    <!-- Materials Tab -->
                    <div class="hidden p-2 rounded-lg" id="materials" role="tabpanel" aria-labelledby="materials-tab">
                        <!-- Materials Card -->
                        <div class="bg-white rounded-lg border p-4">
                            <!-- Info message -->
                            <div class="bg-blue-50 rounded-lg p-2 mb-4 flex items-start text-blue-800 text-sm">
                                <svg class="w-5 h-5 mr-2 shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "القطع والمواد المطلوبة لأمر العمل هذا." %}</span>
                            </div>
                            
                            <!-- Materials List -->
    <div class="overflow-x-auto">
        <table class="w-full text-sm text-left text-gray-500 border-collapse">
            <thead class="text-sm text-gray-800 uppercase bg-gradient-to-r from-gray-50 to-gray-100 border-b-2 border-gray-200">
                <tr class="divide-x divide-gray-300">
                    <th scope="col" class="px-4 py-4 font-bold text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            <span class="font-black">{% trans "العنصر" %}</span>
                        </div>
                    </th>
                    <th scope="col" class="px-4 py-4 font-bold text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                            <span class="font-black">{% trans "الكمية" %}</span>
                        </div>
                    </th>
                    <th scope="col" class="px-4 py-4 font-bold text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-black">{% trans "الحالة" %}</span>
                        </div>
                    </th>
                    <th scope="col" class="px-4 py-4 font-bold text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                            </svg>
                            <span class="font-black">{% trans "الإجراءات" %}</span>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for material in materials %}
                <tr class="hover:bg-orange-50 transition-colors duration-200 divide-x divide-gray-200">
                    <td class="px-4 py-4 text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                            <span class="font-bold text-gray-900">{{ material.item.name }}</span>
                        </div>
                    </td>
                    <td class="px-4 py-4 text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                            <span class="font-bold text-gray-900">
                                {% if material.quantity == material.quantity|floatformat:0|add:"0" %}
                                    {{ material.quantity|floatformat:0 }}
                                {% else %}
                                    {{ material.quantity|floatformat:2 }}
                                {% endif %}
                                {{ material.unit_of_measure }}
                            </span>
                        </div>
                    </td>
                    <td class="px-4 py-4 text-center">
                        <span class="px-3 py-2 text-sm font-bold rounded-full border-2 inline-flex items-center
                            {% if material.is_consumed %}bg-green-100 text-green-800 border-green-300{% else %}bg-blue-100 text-blue-800 border-blue-300{% endif %}">
                            {% if material.is_consumed %}
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {% trans "مستهلك" %}
                                {% if material.consumed_at %}
                                <span class="text-xs text-gray-500 ml-1">
                                    {{ material.consumed_at|date:"Y-m-d" }}
                                </span>
                                {% endif %}
                            {% else %}
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {% trans "قيد الانتظار" %}
                            {% endif %}
                        </span>
                    </td>
                    <td class="px-4 py-4 text-center">
                        <div class="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                            {% if work_order.status in 'planned,in_progress,on_hold' %}
                                {% if not material.is_consumed and material.is_available %}
                                <button type="button" 
                                        class="text-green-700 hover:text-white border-2 border-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-bold rounded-lg text-xs p-2 transition-all duration-200"
                                        data-consume-url="{% url 'work_orders:api_consume_material' material.id %}"
                                        title="{% trans 'استهلاك المادة' %}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </button>
                                {% endif %}
                                <button type="button" 
                                        class="text-blue-700 hover:text-white border-2 border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-bold rounded-lg text-xs p-2 transition-all duration-200"
                                        data-material-id="{{ material.id }}"
                                        title="{% trans 'تعديل المادة' %}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button type="button" 
                                        class="text-red-700 hover:text-white border-2 border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-bold rounded-lg text-xs p-2 transition-all duration-200"
                                        data-material-id="{{ material.id }}"
                                        title="{% trans 'حذف المادة' %}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            {% else %}
                                <span class="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded">{% trans "غير متاح" %}</span>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr class="border-b">
                    <td colspan="4" class="px-4 py-3 text-center text-gray-500">
                                                {% trans "لا توجد مواد" %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
</div>

                            <!-- Action buttons -->
                            <div class="mt-4 flex flex-wrap justify-center gap-3">
                                {% if work_order.status in 'planned,in_progress,on_hold' %}
                                <button id="add-material-btn-large" type="button" class="btn-enhanced text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-bold rounded-lg text-base px-6 py-3 flex items-center shadow-lg">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <span class="value-text">{% trans "إضافة مواد" %}</span>
                                </button>
                                
                                <button id="request-parts-btn-large" type="button" class="btn-enhanced text-gray-900 bg-white border-2 border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-bold rounded-lg text-base px-6 py-3 flex items-center shadow-lg">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 4H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-2m-4-1v8m0 0l-4-4m4 4l4-4"></path>
                                    </svg>
                                    <span class="value-text">{% trans "طلب قطع" %}</span>
                                </button>
                                {% else %}
                                <div class="btn-enhanced text-gray-400 bg-gray-100 cursor-not-allowed font-bold rounded-lg text-base px-6 py-3 flex items-center shadow-lg" title="{% trans 'غير متاح في هذه الحالة' %}">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <span class="value-text">{% trans "إضافة مواد" %}</span>
                                </div>
                                
                                <div class="btn-enhanced text-gray-400 bg-gray-100 cursor-not-allowed font-bold rounded-lg text-base px-6 py-3 flex items-center shadow-lg" title="{% trans 'غير متاح في هذه الحالة' %}">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 4H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-2m-4-1v8m0 0l-4-4m4 4l4-4"></path>
                                    </svg>
                                    <span class="value-text">{% trans "طلب قطع" %}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
    </div>
    
                    <!-- Notes Tab -->
                    <div class="hidden p-2 rounded-lg" id="notes" role="tabpanel" aria-labelledby="notes-tab">
                        <!-- Notes Card -->
                        <div class="bg-white rounded-lg border p-4">
                            <!-- Notes list -->
                            <div class="space-y-3">
                                {% for note in notes %}
                                <div class="bg-gray-50 p-3 rounded-lg border-l-4 border-blue-500">
                                    <div class="flex justify-between items-start">
                                        <p class="text-sm font-medium text-gray-900">{{ note.content }}</p>
                                        <div class="flex flex-col items-end">
                                            <span class="text-xs text-gray-500">{{ note.created_at|date:"Y-m-d H:i" }}</span>
                                            <span class="text-xs font-medium text-gray-700">{{ note.created_by.get_full_name }}</span>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="text-center text-gray-500 py-4">
                                    {% trans "لا توجد ملاحظات" %}
                                </div>
                                {% endfor %}
                            </div>
                            
                            <!-- Add note form -->
                            <div class="mt-4">
                                <form method="post" action="{% url 'work_orders:work_order_update' work_order.id %}">
                {% csrf_token %}
                                    <div class="mb-3">
                                        <label for="note-content" class="block mb-2 text-sm font-medium text-gray-900">{% trans "أضف ملاحظة جديدة" %}</label>
                                        <textarea id="note-content" name="notes" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" required></textarea>
                </div>
                                    <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5">
                                        {% trans "إرسال" %}
                </button>
            </form>
                            </div>
                        </div>
        </div>
        
                    <!-- Actions Tab -->
                    <div class="hidden p-2 rounded-lg" id="actions" role="tabpanel" aria-labelledby="actions-tab">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Quick Actions Card -->
                            <div class="bg-white rounded-lg border p-4">
                                <h4 class="font-bold text-gray-900 mb-3">{% trans "إجراءات سريعة" %}</h4>
                                
                                <div class="space-y-3">
                                    <!-- Status Change Button - Always available -->
                                    <button type="button" id="status-change-btn-actions" class="btn-enhanced text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-bold rounded-lg text-base px-6 py-3 w-full flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        <span class="value-text">{% trans "تغيير الحالة" %}</span>
                                    </button>
                                    
                                    <!-- Add Operation Button - Only for planned, in_progress, on_hold -->
                                    {% if work_order.status in 'planned,in_progress,on_hold' %}
                                    <button id="add-operation-btn-action" type="button" class="btn-enhanced text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-bold rounded-lg text-base px-6 py-3 w-full flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        <span class="value-text">{% trans "إضافة عملية" %}</span>
                                    </button>
                                    {% else %}
                                    <div class="btn-enhanced text-gray-400 bg-gray-100 cursor-not-allowed font-bold rounded-lg text-base px-6 py-3 w-full flex items-center justify-center shadow-lg" title="{% trans 'غير متاح في هذه الحالة' %}">
                                        <svg class="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        <span class="value-text">{% trans "إضافة عملية" %}</span>
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Add Material Button - Only for planned, in_progress, on_hold -->
                                    {% if work_order.status in 'planned,in_progress,on_hold' %}
                                    <button id="add-material-btn-action" type="button" class="btn-enhanced text-white bg-purple-700 hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 font-bold rounded-lg text-base px-6 py-3 w-full flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        <span class="value-text">{% trans "إضافة مواد" %}</span>
                                    </button>
                                    {% else %}
                                    <div class="btn-enhanced text-gray-400 bg-gray-100 cursor-not-allowed font-bold rounded-lg text-base px-6 py-3 w-full flex items-center justify-center shadow-lg" title="{% trans 'غير متاح في هذه الحالة' %}">
                                        <svg class="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        <span class="value-text">{% trans "إضافة مواد" %}</span>
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Request Parts Button - Only for planned, in_progress, on_hold -->
                                    {% if work_order.status in 'planned,in_progress,on_hold' %}
                                    <button id="request-parts-btn-action" type="button" class="btn-enhanced text-gray-900 bg-white border-2 border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-bold rounded-lg text-base px-6 py-3 w-full flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 4H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-2m-4-1v8m0 0l-4-4m4 4l4-4"></path>
                                        </svg>
                                        <span class="value-text">{% trans "طلب قطع" %}</span>
                                    </button>
                                    {% else %}
                                    <div class="btn-enhanced text-gray-400 bg-gray-100 cursor-not-allowed font-bold rounded-lg text-base px-6 py-3 w-full flex items-center justify-center shadow-lg" title="{% trans 'غير متاح في هذه الحالة' %}">
                                        <svg class="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 4H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-2m-4-1v8m0 0l-4-4m4 4l4-4"></path>
                                        </svg>
                                        <span class="value-text">{% trans "طلب قطع" %}</span>
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Print Button - Always available -->
                                    <button type="button" onclick="window.print()" class="btn-enhanced text-gray-900 bg-white border-2 border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-bold rounded-lg text-base px-6 py-3 w-full flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                        </svg>
                                        <span class="value-text">{% trans "طباعة أمر العمل" %}</span>
                                    </button>
                                    
                                    
                                </div>
                                

                            </div>
                            
                            <!-- Enhanced History Timeline -->
                            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
                                <!-- Header with enhanced styling -->
                                <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <h4 class="flex items-center text-lg font-bold text-gray-900">
                                            <svg class="w-6 h-6 mr-3 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            {% trans "سجل الإجراءات" %}
                                        </h4>
                                        <span class="px-3 py-1 text-sm font-semibold text-indigo-800 bg-indigo-100 rounded-full">
                                            {{ history.count|default:"0" }} {% trans "إجراء" %}
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- Enhanced timeline content -->
                                <div class="p-6">
                                    <div class="relative" id="history">
                                        <!-- Timeline line -->
                                        <div class="absolute right-6 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-gray-200 to-gray-100"></div>
                                        
                                        {% for entry in history %}
                                        <div class="relative flex items-start space-x-3 rtl:space-x-reverse mb-6 last:mb-0">
                                            <!-- Enhanced timeline dot with action-specific colors -->
                                            <div class="relative">
                                                <div class="flex items-center justify-center w-10 h-10 rounded-full shadow-md
                                                    {% if entry.action_type == 'created' %}bg-green-500
                                                    {% elif entry.action_type == 'status_changed' %}bg-blue-500
                                                    {% elif entry.action_type == 'operation_added' %}bg-purple-500
                                                    {% elif entry.action_type == 'material_added' %}bg-orange-500
                                                    {% elif entry.action_type == 'operation_completed' %}bg-emerald-500
                                                    {% elif entry.action_type == 'note_added' %}bg-yellow-500
                                                    {% else %}bg-gray-500{% endif %}">
                                                    
                                                    {% if entry.action_type == 'created' %}
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                    </svg>
                                                    {% elif entry.action_type == 'status_changed' %}
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                    </svg>
                                                    {% elif entry.action_type == 'operation_added' %}
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    {% elif entry.action_type == 'material_added' %}
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                                    </svg>
                                                    {% elif entry.action_type == 'operation_completed' %}
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    {% elif entry.action_type == 'note_added' %}
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                    </svg>
                                                    {% else %}
                                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    {% endif %}
                                                </div>
                                                
                                                <!-- Pulse animation for recent entries -->
                                                {% if forloop.first %}
                                                <div class="absolute inset-0 rounded-full animate-ping bg-blue-400 opacity-20"></div>
                                                {% endif %}
                                            </div>
                                            
                                            <!-- Enhanced entry card -->
                                            <div class="flex-1 min-w-0 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 p-4">
                                                <!-- Header row with action type and timestamp -->
                                                <div class="flex items-center justify-between mb-2">
                                                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold
                                                            {% if entry.action_type == 'created' %}bg-green-100 text-green-800
                                                            {% elif entry.action_type == 'status_changed' %}bg-blue-100 text-blue-800
                                                            {% elif entry.action_type == 'operation_added' %}bg-purple-100 text-purple-800
                                                            {% elif entry.action_type == 'material_added' %}bg-orange-100 text-orange-800
                                                            {% elif entry.action_type == 'operation_completed' %}bg-emerald-100 text-emerald-800
                                                            {% elif entry.action_type == 'note_added' %}bg-yellow-100 text-yellow-800
                                                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                                                            {{ entry.get_action_type_display }}
                                                        </span>
                                                        
                                                        {% if forloop.first %}
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                                            {% trans "جديد" %}
                                                        </span>
                                                        {% endif %}
                                                    </div>
                                                    
                                                    <div class="flex items-center text-sm text-gray-500 space-x-1 rtl:space-x-reverse">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        <span dir="ltr">{{ entry.created_at|date:"Y-m-d H:i" }}</span>
                                                    </div>
                                                </div>
                                                
                                                <!-- Description -->
                                                <p class="text-sm text-gray-700 mb-3 leading-relaxed">{{ entry.description }}</p>
                                                
                                                <!-- User info with enhanced styling -->
                                                <div class="flex items-center justify-between pt-2 border-t border-gray-100">
                                                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                                        <div class="flex items-center justify-center w-6 h-6 rounded-full bg-gray-200">
                                                            <svg class="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                            </svg>
                                                        </div>
                                                        <span class="text-xs font-medium text-gray-600">
                                                            {% trans "بواسطة" %}: 
                                                            {% if entry.user %}
                                                                <span class="text-gray-900 font-semibold">{{ entry.user.get_full_name|default:entry.user.username }}</span>
                                                            {% else %}
                                                                <span class="text-indigo-600 font-semibold">{% trans "النظام" %}</span>
                                                            {% endif %}
                                                        </span>
                                                    </div>
                                                    
                                                    <!-- Time ago indicator -->
                                                    <span class="text-xs text-gray-400">
                                                        {{ entry.created_at|timesince }} {% trans "مضت" %}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        {% empty %}
                                        <!-- Enhanced empty state -->
                                        <div class="text-center py-12">
                                            <div class="flex justify-center mb-4">
                                                <div class="flex items-center justify-center w-16 h-16 rounded-full bg-gray-100">
                                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <h3 class="text-sm font-medium text-gray-900 mb-1">{% trans "لا يوجد سجل إجراءات" %}</h3>
                                            <p class="text-sm text-gray-500">{% trans "سيتم عرض الإجراءات والتحديثات هنا" %}</p>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    
                                    <!-- Enhanced load more section -->
                                    {% if history.count > 5 %}
                                    <div class="mt-8 pt-6 border-t border-gray-200">
                                        <div class="text-center">
                                            <button id="load-more-history" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                                </svg>
                                                {% trans "تحميل المزيد من السجل" %}
                                            </button>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Operation Modal -->
<div id="add-operation-modal" style="display: none; position: fixed; top: 80px; left: calc(50% - 300px); z-index: 9999; width: 600px;">
    <div style="background: white; border-radius: 8px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2); max-height: 85vh; overflow-y: auto; width: 100%; position: relative; border: 2px solid #e5e7eb;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 2px solid #e5e7eb; background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div style="display: flex; align-items: center; gap: 12px;">
                <div style="background: rgba(255,255,255,0.2); padding: 8px; border-radius: 8px;">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                        <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 style="font-size: 20px; font-weight: 700; margin: 0; color: white; font-family: 'Tahoma', Arial, sans-serif;">{% trans "إضافة صيانه جديدة" %}</h3>
            </div>
            <button type="button" onclick="hideAddOperationModal()" style="background: rgba(255,255,255,0.2); border: none; cursor: pointer; padding: 8px; border-radius: 6px; transition: background 0.2s;">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <path d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div style="padding: 24px;">
            <form id="operation-form">
                {% csrf_token %}
                <input type="hidden" name="work_order" value="{{ work_order.id }}">
                
                <!-- Operation Source Selection -->
                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {% trans "مصدر الصيانه" %}
                    </label>
                    <div style="display: flex; gap: 16px; margin-bottom: 12px;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="operation_source" value="existing" checked style="margin-left: 8px;">
                            <span>{% trans "من العمليات الموجودة" %}</span>
                        </label>
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="operation_source" value="custom" style="margin-left: 8px;">
                            <span>{% trans "إنشاء جديد" %}</span>
                        </label>
                    </div>
                </div>
                
                <!-- Existing Operations Dropdown -->
                <div id="existing-operation-section" style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2">
                            <path d="M9 11H3m3 8H3m7-8v8a2 2 0 01-2 2H5a2 2 0 01-2-2v-3a2 2 0 012-2h6a2 2 0 012 2zm7-6V9a2 2 0 012-2h2a2 2 0 012 2v2m-6 0a2 2 0 012-2h2a2 2 0 012 2m-6 0h6"></path>
                        </svg>
                        {% trans "اختر صيانه موجودة" %}
                    </label>
                    <select id="existing-operation-select" name="existing_operation" style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; background-color: white; font-size: 15px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        <option value="">{% trans "جاري التحميل..." %}</option>
                    </select>
                </div>
                
                <!-- Custom Operation Fields -->
                <div id="custom-operation-section" style="display: none;">
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500;">{% trans "اسم الصيانه" %}</label>
                        <input type="text" name="name" style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; background-color: #f9fafb;">
                </div>
                
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500;">{% trans "الوصف" %}</label>
                    <textarea name="description" rows="3" style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; background-color: #f9fafb;"></textarea>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500;">{% trans "المدة (بالدقائق)" %}</label>
                        <input type="number" name="duration_minutes" value="60" min="1" style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; background-color: #f9fafb;">
                    </div>
                </div>
                
                <!-- Common Fields -->
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500;">{% trans "الترتيب" %}</label>
                    <input type="number" name="sequence" value="10" min="1" required style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; background-color: #f9fafb;">
                </div>
                
                <!-- Operation Details Display (for existing operations) -->
                <div id="operation-details" style="display: none; margin-bottom: 16px; padding: 12px; background-color: #f8fafc; border-radius: 6px; border: 1px solid #e2e8f0;">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: #374151;">{% trans "تفاصيل الصيانه" %}</h4>
                    <div id="operation-details-content"></div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px;">
                    <button type="button" onclick="hideAddOperationModal()" style="display: flex; align-items: center; gap: 8px; background-color: #6b7280; color: white; font-size: 15px; font-weight: 600; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; transition: background-color 0.2s; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="m15 9-6 6"></path>
                            <path d="m9 9 6 6"></path>
                        </svg>
                        {% trans "إلغاء" %}
                    </button>
                    <button type="button" onclick="submitOperationForm()" style="display: flex; align-items: center; gap: 8px; background-color: #10b981; color: white; font-size: 15px; font-weight: 600; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; transition: background-color 0.2s; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        {% trans "إضافة الصيانه" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Material Modal -->
<div id="add-material-modal" style="display: none; position: fixed; top: 80px; left: calc(50% - 250px); z-index: 9999; width: 500px;">
    <div style="background: white; border-radius: 8px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2); max-height: 85vh; overflow-y: auto; width: 100%; position: relative; border: 2px solid #e5e7eb;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; border-bottom: 2px solid #e5e7eb; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div style="display: flex; align-items: center; gap: 10px;">
                <div style="background: rgba(255,255,255,0.2); padding: 6px; border-radius: 6px;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                        <path d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 style="font-size: 18px; font-weight: 700; margin: 0; color: white; font-family: 'Tahoma', Arial, sans-serif;">{% trans "إضافة مواد جديدة" %}</h3>
            </div>
            <button type="button" onclick="hideAddMaterialModal()" style="background: rgba(255,255,255,0.2); border: none; cursor: pointer; padding: 6px; border-radius: 4px; transition: background 0.2s;">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <path d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div style="padding: 18px;">
            <form id="material-form">
                {% csrf_token %}
                <input type="hidden" name="work_order" value="{{ work_order.id }}">
                
                <div style="margin-bottom: 16px;">
                    <label style="display: flex; align-items: center; gap: 6px; margin-bottom: 8px; font-size: 14px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2">
                            <path d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        {% trans "العنصر" %}
                    </label>
                    <select name="item" required style="width: 100%; padding: 10px 12px; border: 2px solid #e5e7eb; border-radius: 6px; background-color: white; font-size: 14px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s;">
                        <option value="">{% trans "اختر عنصر..." %}</option>
                        {% for item in available_items %}
                        <option value="{{ item.id }}">{{ item.name }} ({{ item.sku }})</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px;">
                    <div>
                        <label style="display: flex; align-items: center; gap: 6px; margin-bottom: 8px; font-size: 14px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2">
                                <path d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                            {% trans "الكمية" %}
                        </label>
                        <input type="number" name="quantity" value="1" step="0.01" min="0.01" required style="width: 100%; padding: 10px 12px; border: 2px solid #e5e7eb; border-radius: 6px; background-color: white; font-size: 14px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s;">
                    </div>
                    
                    <div>
                        <label style="display: flex; align-items: center; gap: 6px; margin-bottom: 8px; font-size: 14px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% trans "الوحدة" %}
                        </label>
                        <input type="text" name="unit_of_measure" value="ea" required style="width: 100%; padding: 10px 12px; border: 2px solid #e5e7eb; border-radius: 6px; background-color: white; font-size: 14px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s;">
                    </div>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <label style="display: flex; align-items: center; gap: 6px; margin-bottom: 8px; font-size: 14px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2">
                            <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                        </svg>
                        {% trans "ملاحظات" %}
                    </label>
                    <textarea name="notes" rows="2" style="width: 100%; padding: 10px 12px; border: 2px solid #e5e7eb; border-radius: 6px; background-color: white; font-size: 14px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s; resize: vertical;" placeholder="{% trans 'ملاحظات اختيارية...' %}"></textarea>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                    <button type="button" onclick="hideAddMaterialModal()" style="display: flex; align-items: center; gap: 6px; background-color: #6b7280; color: white; font-size: 14px; font-weight: 600; padding: 10px 16px; border: none; border-radius: 6px; cursor: pointer; transition: background-color 0.2s; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m15 9-6 6"></path>
                            <path d="m9 9 6 6"></path>
                        </svg>
                        {% trans "إلغاء" %}
                    </button>
                    <button type="button" onclick="submitMaterialForm()" style="display: flex; align-items: center; gap: 6px; background-color: #f59e0b; color: white; font-size: 14px; font-weight: 600; padding: 10px 16px; border: none; border-radius: 6px; cursor: pointer; transition: background-color 0.2s; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        {% trans "إضافة" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Request Parts Modal -->
<div id="request-parts-modal" style="display: none; position: fixed; top: 80px; left: calc(50% - 300px); z-index: 9999; width: 600px;">
    <div style="background: white; border-radius: 8px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2); max-height: 85vh; overflow-y: auto; width: 100%; position: relative; border: 2px solid #e5e7eb;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 2px solid #e5e7eb; background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
            <div style="display: flex; align-items: center; gap: 12px;">
                <div style="background: rgba(255,255,255,0.2); padding: 8px; border-radius: 8px;">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                        <path d="M8 4H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-2m-4-1v8m0 0l-4-4m4 4l4-4"></path>
                    </svg>
                </div>
                <h3 style="font-size: 20px; font-weight: 700; margin: 0; color: white; font-family: 'Tahoma', Arial, sans-serif;">{% trans "طلب قطع غيار" %}</h3>
            </div>
            <button type="button" onclick="hideRequestPartsModal()" style="background: rgba(255,255,255,0.2); border: none; cursor: pointer; padding: 8px; border-radius: 6px; transition: background 0.2s;">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <path d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div style="padding: 24px;">
            <form id="request-parts-form">
                {% csrf_token %}
                <input type="hidden" name="work_order" value="{{ work_order.id }}">
                
                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2">
                            <path d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                        </svg>
                        {% trans "المستودع المطلوب" %}
                    </label>
                    <select name="warehouse" required style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; background-color: white; font-size: 15px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        <option value="">{% trans "اختر مستودع..." %}</option>
                        {% for warehouse in available_warehouses %}
                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2">
                            <path d="M16 4h2a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2h2"></path>
                            <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                        </svg>
                        {% trans "نوع الطلب" %}
                    </label>
                    <select name="request_type" required style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; background-color: white; font-size: 15px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        <option value="transfer">🔄 {% trans "نقل من مستودع آخر" %}</option>
                        <option value="purchase">🛒 {% trans "طلب شراء جديد" %}</option>
                    </select>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                        </svg>
                        {% trans "الأولوية" %}
                    </label>
                    <select name="priority" required style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; background-color: white; font-size: 15px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        <option value="low">🟢 {% trans "منخفضة" %}</option>
                        <option value="medium" selected>🟡 {% trans "متوسطة" %}</option>
                        <option value="high">🟠 {% trans "عالية" %}</option>
                        <option value="urgent">🔴 {% trans "عاجل" %}</option>
                    </select>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2">
                            <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                        {% trans "تفاصيل الطلب" %}
                    </label>
                    <textarea name="details" rows="4" required style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; background-color: white; font-size: 15px; font-family: 'Tahoma', Arial, sans-serif; transition: border-color 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.05); resize: vertical;" placeholder="{% trans 'اذكر القطع المطلوبة والكميات...' %}"></textarea>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px;">
                    <button type="button" onclick="hideRequestPartsModal()" style="display: flex; align-items: center; gap: 8px; background-color: #6b7280; color: white; font-size: 15px; font-weight: 600; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; transition: background-color 0.2s; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="m15 9-6 6"></path>
                            <path d="m9 9 6 6"></path>
                        </svg>
                        {% trans "إلغاء" %}
                    </button>
                    <button type="button" onclick="submitRequestPartsForm()" style="display: flex; align-items: center; gap: 8px; background-color: #8b5cf6; color: white; font-size: 15px; font-weight: 600; padding: 12px 20px; border: none; border-radius: 8px; cursor: pointer; transition: background-color 0.2s; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        {% trans "إرسال الطلب" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Status Change Modal -->
<div id="status-change-modal" style="display: none; position: fixed; top: 100px; left: calc(50% - 250px); z-index: 9999; width: 500px;">
    <div style="background: white; border-radius: 8px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2); max-height: 80vh; overflow-y: auto; width: 100%; position: relative; border: 2px solid #e5e7eb;">
        <!-- Header -->
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 2px solid #e5e7eb; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div style="display: flex; align-items: center; gap: 12px;">
                <div style="background: rgba(255,255,255,0.2); padding: 8px; border-radius: 8px;">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                        <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </div>
                <h3 style="font-size: 20px; font-weight: 700; margin: 0; color: white; font-family: 'Tahoma', Arial, sans-serif;">{% trans "تغيير حالة أمر العمل" %}</h3>
            </div>
            <button type="button" id="close-status-modal" style="background: rgba(255,255,255,0.2); border: none; cursor: pointer; padding: 8px; border-radius: 6px; transition: background 0.2s;">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <path d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Body -->
        <div style="padding: 24px;">
            <!-- Current Status Display -->
            <div style="background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); padding: 16px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #d1d5db;">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#374151" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3"></path>
                        <path d="M12 17h.01"></path>
                    </svg>
                    <span style="font-size: 14px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">{% trans "الحالة الحالية" %}</span>
                </div>
                <div style="text-align: center; padding: 8px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
                    {% if work_order.status == 'draft' %}
                        <span style="color: #6b7280; font-weight: 600; font-family: 'Tahoma', Arial, sans-serif;">📝 {% trans "مسودة" %}</span>
                    {% elif work_order.status == 'planned' %}
                        <span style="color: #3b82f6; font-weight: 600; font-family: 'Tahoma', Arial, sans-serif;">📥 {% trans "استقبال" %}</span>
                    {% elif work_order.status == 'in_progress' %}
                        <span style="color: #f59e0b; font-weight: 600; font-family: 'Tahoma', Arial, sans-serif;">🔧 {% trans "تحت التشغيل" %}</span>
                    {% elif work_order.status == 'on_hold' %}
                        <span style="color: #f59e0b; font-weight: 600; font-family: 'Tahoma', Arial, sans-serif;">⏸️ {% trans "قيد الانتظار" %}</span>
                    {% elif work_order.status == 'completed' %}
                        <span style="color: #10b981; font-weight: 600; font-family: 'Tahoma', Arial, sans-serif;">✅ {% trans "اكتمال" %}</span>
                    {% elif work_order.status == 'cancelled' %}
                        <span style="color: #ef4444; font-weight: 600; font-family: 'Tahoma', Arial, sans-serif;">❌ {% trans "إلغاء الطلب" %}</span>
                    {% endif %}
                </div>
            </div>

            <!-- Status Flow Based on Current Status -->
            {% if work_order.status == 'draft' %}
                <!-- From Draft: Can go to Planned or Cancelled -->
                <div style="margin-bottom: 20px;">
                    <h4 style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#667eea" stroke-width="2">
                            <path d="M9 11l3 3L22 4"></path>
                            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
                        </svg>
                        {% trans "اختر الإجراء التالي" %}
                    </h4>
                    <div style="display: grid; gap: 12px;">
                        <button type="button" onclick="changeStatus('planned')" style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 2px solid #3b82f6; border-radius: 8px; background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); cursor: pointer; transition: all 0.2s; text-align: right;">
                            <div style="background: #3b82f6; padding: 8px; border-radius: 6px; margin-left: auto;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M14.7 6.3a1 1 0 000 1.4l1.6 1.6a1 1 0 001.4 0l3.77-3.77a6 6 0 01-7.94 7.94l-6.91 6.91a2.12 2.12 0 01-3-3l6.91-6.91a6 6 0 017.94-7.94l-3.76 3.76z"></path>
                                </svg>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 16px; font-weight: 700; color: #1e40af; font-family: 'Tahoma', Arial, sans-serif;">📥 {% trans "استقبال الطلب" %}</div>
                                <div style="font-size: 13px; color: #3730a3; font-family: 'Tahoma', Arial, sans-serif;">{% trans "بدء معالجة أمر العمل" %}</div>
                            </div>
                        </button>
                        <button type="button" onclick="changeStatus('cancelled')" style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 2px solid #ef4444; border-radius: 8px; background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%); cursor: pointer; transition: all 0.2s; text-align: right;">
                            <div style="background: #ef4444; padding: 8px; border-radius: 6px; margin-left: auto;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M15 9l-6 6"></path>
                                    <path d="M9 9l6 6"></path>
                                </svg>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 16px; font-weight: 700; color: #dc2626; font-family: 'Tahoma', Arial, sans-serif;">❌ {% trans "إلغاء الطلب" %}</div>
                                <div style="font-size: 13px; color: #b91c1c; font-family: 'Tahoma', Arial, sans-serif;">{% trans "إلغاء أمر العمل نهائياً" %}</div>
                            </div>
                        </button>
                    </div>
                </div>
            {% elif work_order.status == 'planned' %}
                <!-- From Planned: Can go to In Progress or Cancelled -->
                <div style="margin-bottom: 20px;">
                    <h4 style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#667eea" stroke-width="2">
                            <path d="M9 11l3 3L22 4"></path>
                            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
                        </svg>
                        {% trans "اختر الإجراء التالي" %}
                    </h4>
                    <div style="display: grid; gap: 12px;">
                        <button type="button" onclick="changeStatus('in_progress')" style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 2px solid #f59e0b; border-radius: 8px; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); cursor: pointer; transition: all 0.2s; text-align: right;">
                            <div style="background: #f59e0b; padding: 8px; border-radius: 6px; margin-left: auto;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M12 1v6m0 6v6"></path>
                                    <path d="m5.64 5.64 4.24 4.24m4.24 4.24 4.24 4.24"></path>
                                    <path d="m18.36 5.64-4.24 4.24M9.88 14.12l-4.24 4.24"></path>
                                </svg>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 16px; font-weight: 700; color: #d97706; font-family: 'Tahoma', Arial, sans-serif;">🔧 {% trans "بدء التشغيل" %}</div>
                                <div style="font-size: 13px; color: #b45309; font-family: 'Tahoma', Arial, sans-serif;">{% trans "البدء في تنفيذ العمليات" %}</div>
                            </div>
                        </button>
                        <button type="button" onclick="changeStatus('cancelled')" style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 2px solid #ef4444; border-radius: 8px; background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%); cursor: pointer; transition: all 0.2s; text-align: right;">
                            <div style="background: #ef4444; padding: 8px; border-radius: 6px; margin-left: auto;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M15 9l-6 6"></path>
                                    <path d="M9 9l6 6"></path>
                                </svg>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 16px; font-weight: 700; color: #dc2626; font-family: 'Tahoma', Arial, sans-serif;">❌ {% trans "إلغاء الطلب" %}</div>
                                <div style="font-size: 13px; color: #b91c1c; font-family: 'Tahoma', Arial, sans-serif;">{% trans "إلغاء أمر العمل نهائياً" %}</div>
                            </div>
                        </button>
                    </div>
                </div>
            {% elif work_order.status == 'in_progress' %}
                <!-- From In Progress: Can go to Completed or On Hold -->
                <div style="margin-bottom: 20px;">
                    <h4 style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#667eea" stroke-width="2">
                            <path d="M9 11l3 3L22 4"></path>
                            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
                        </svg>
                        {% trans "اختر الإجراء التالي" %}
                    </h4>
                    <div style="display: grid; gap: 12px;">
                        <button type="button" onclick="changeStatus('completed')" style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 2px solid #10b981; border-radius: 8px; background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%); cursor: pointer; transition: all 0.2s; text-align: right;">
                            <div style="background: #10b981; padding: 8px; border-radius: 6px; margin-left: auto;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M22 11.08V12a10 10 0 11-5.93-9.14"></path>
                                    <path d="M9 11l3 3L22 4"></path>
                                </svg>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 16px; font-weight: 700; color: #047857; font-family: 'Tahoma', Arial, sans-serif;">✅ {% trans "إكمال العمل" %}</div>
                                <div style="font-size: 13px; color: #065f46; font-family: 'Tahoma', Arial, sans-serif;">{% trans "إنهاء جميع العمليات بنجاح" %}</div>
                            </div>
                        </button>
                        <button type="button" onclick="changeStatus('on_hold')" style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 2px solid #f59e0b; border-radius: 8px; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); cursor: pointer; transition: all 0.2s; text-align: right;">
                            <div style="background: #f59e0b; padding: 8px; border-radius: 6px; margin-left: auto;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M12 6v6l4 2"></path>
                                </svg>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 16px; font-weight: 700; color: #d97706; font-family: 'Tahoma', Arial, sans-serif;">⏸️ {% trans "قيد الانتظار" %}</div>
                                <div style="font-size: 13px; color: #b45309; font-family: 'Tahoma', Arial, sans-serif;">{% trans "انتظار قطع أو موافقة" %}</div>
                            </div>
                        </button>
                    </div>
                </div>
            {% elif work_order.status == 'on_hold' %}
                <!-- From On Hold: Can go back to In Progress or Complete -->
                <div style="margin-bottom: 20px;">
                    <h4 style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px; font-size: 16px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#667eea" stroke-width="2">
                            <path d="M9 11l3 3L22 4"></path>
                            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
                        </svg>
                        {% trans "اختر الإجراء التالي" %}
                    </h4>
                    <div style="display: grid; gap: 12px;">
                        <button type="button" onclick="changeStatus('in_progress')" style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 2px solid #f59e0b; border-radius: 8px; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); cursor: pointer; transition: all 0.2s; text-align: right;">
                            <div style="background: #f59e0b; padding: 8px; border-radius: 6px; margin-left: auto;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                </svg>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 16px; font-weight: 700; color: #d97706; font-family: 'Tahoma', Arial, sans-serif;">🔧 {% trans "متابعة التشغيل" %}</div>
                                <div style="font-size: 13px; color: #b45309; font-family: 'Tahoma', Arial, sans-serif;">{% trans "العودة لحالة التشغيل" %}</div>
                            </div>
                        </button>
                        <button type="button" onclick="changeStatus('completed')" style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 2px solid #10b981; border-radius: 8px; background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%); cursor: pointer; transition: all 0.2s; text-align: right;">
                            <div style="background: #10b981; padding: 8px; border-radius: 6px; margin-left: auto;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M22 11.08V12a10 10 0 11-5.93-9.14"></path>
                                    <path d="M9 11l3 3L22 4"></path>
                                </svg>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-size: 16px; font-weight: 700; color: #047857; font-family: 'Tahoma', Arial, sans-serif;">✅ {% trans "إكمال العمل" %}</div>
                                <div style="font-size: 13px; color: #065f46; font-family: 'Tahoma', Arial, sans-serif;">{% trans "إنهاء العمل نهائياً" %}</div>
                            </div>
                        </button>
                    </div>
                </div>
            {% else %}
                <!-- For completed or cancelled status - show completion notice -->
                <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 8px; border: 1px solid #d1d5db;">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#6b7280" stroke-width="2" style="margin: 0 auto 12px;">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p style="font-size: 16px; font-weight: 600; color: #374151; margin: 0; font-family: 'Tahoma', Arial, sans-serif;">
                        {% trans "لا توجد إجراءات متاحة" %}
                    </p>
                    <p style="font-size: 14px; color: #6b7280; margin: 8px 0 0; font-family: 'Tahoma', Arial, sans-serif;">
                        {% trans "تم إنهاء أمر العمل" %}
                    </p>
                </div>
            {% endif %}

            <!-- Confirmation Modal for Status Change -->
            <div id="status-confirm-modal" style="display: none; position: fixed; top: 120px; left: calc(50% - 200px); z-index: 10000; width: 400px; background: white; border-radius: 8px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); border: 2px solid #e5e7eb;">
                <div style="padding: 20px; text-align: center;">
                    <div style="background: #3b82f6; padding: 12px; border-radius: 50%; width: 48px; height: 48px; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 style="font-size: 18px; font-weight: 700; color: #1f2937; margin-bottom: 8px; font-family: 'Tahoma', Arial, sans-serif;" id="confirm-title">{% trans "تأكيد التعليق" %}</h3>
                    <p style="font-size: 14px; color: #6b7280; margin-bottom: 20px; font-family: 'Tahoma', Arial, sans-serif;" id="confirm-message">{% trans "هل أنت متأكد من تعليق الطلب؟" %}</p>
                    
                    <!-- Technician Selection (only shown for in_progress status) -->
                    <div id="technician-selection" style="display: none; margin-bottom: 16px; text-align: right;">
                        <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 14px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            {% trans "اختر الفني المسؤول" %}
                        </label>
                        <select id="selected-technician" style="width: 100%; padding: 10px 12px; border: 2px solid #e5e7eb; border-radius: 6px; background-color: white; font-size: 14px; font-family: 'Tahoma', Arial, sans-serif; direction: rtl;">
                            <option value="">{% trans "اختر فني..." %}</option>
                            <!-- Technicians will be loaded dynamically -->
                        </select>
                    </div>
                    
                    <!-- Reason Selection (shown for certain statuses) -->
                    <div id="reason-selection" style="display: none; margin-bottom: 16px; text-align: right;">
                        <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 14px; font-weight: 600; color: #374151; font-family: 'Tahoma', Arial, sans-serif;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3b82f6" stroke-width="2">
                                <path d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% trans "سبب التغيير" %}
                        </label>
                        <select id="status-change-reason" style="width: 100%; padding: 10px 12px; border: 2px solid #e5e7eb; border-radius: 6px; background-color: white; font-size: 14px; font-family: 'Tahoma', Arial, sans-serif; direction: rtl;">
                            <option value="">{% trans "اختر السبب..." %}</option>
                            <!-- Reasons will be loaded dynamically based on status -->
                        </select>
                    </div>
                    
                    <div style="margin-bottom: 16px;">
                        <textarea id="status-change-notes" rows="3" style="width: 100%; padding: 10px; border: 2px solid #e5e7eb; border-radius: 6px; font-size: 14px; font-family: 'Tahoma', Arial, sans-serif; resize: vertical; direction: rtl;" placeholder="{% trans 'في انتظار قطعة غيار' %}"></textarea>
                    </div>
                    
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button type="button" onclick="cancelStatusChange()" style="background-color: #6b7280; color: white; font-size: 14px; font-weight: 600; padding: 10px 16px; border: none; border-radius: 6px; cursor: pointer; font-family: 'Tahoma', Arial, sans-serif;">
                            ❌ {% trans "إلغاء" %}
                        </button>
                        <button type="button" onclick="confirmStatusChange()" style="background-color: #3b82f6; color: white; font-size: 14px; font-weight: 600; padding: 10px 16px; border: none; border-radius: 6px; cursor: pointer; font-family: 'Tahoma', Arial, sans-serif;">
                            ✅ {% trans "تأكيد" %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Hidden form for status submission -->
            <form id="status-change-form" method="post" action="{% url 'work_orders:change_work_order_status' work_order.id %}" style="display: none;">
                {% csrf_token %}
                <input type="hidden" id="new-status" name="new_status" value="">
                <input type="hidden" id="status-notes" name="notes" value="">
                <input type="hidden" id="assigned-technician" name="assigned_technician" value="">
            </form>
        </div>
    </div>
</div>

<!-- JavaScript for the page -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.6.5/flowbite.min.js"></script>
<script>
    // Global variables for better state management
    let activeModals = new Set();
    let formSubmitting = false;
    
    document.addEventListener('DOMContentLoaded', function() {
        // Always set RTL direction for Arabic language 
        document.documentElement.dir = 'rtl';
        document.body.classList.add('rtl');
        document.documentElement.lang = 'ar';
        
        // Force Arabic labels to display properly
        document.querySelectorAll('.text-xs.font-bold, h3, label, .text-sm').forEach(el => {
            el.style.fontFamily = "Arial, sans-serif";
            el.dir = "rtl";
        });
        
        // Apply RTL styling to all elements
        const allElements = document.querySelectorAll('*');
        allElements.forEach(el => {
            // Swap margin and padding directions
            if (el.classList.contains('mr-1')) { el.classList.replace('mr-1', 'ml-1'); }
            if (el.classList.contains('mr-2')) { el.classList.replace('mr-2', 'ml-2'); }
            if (el.classList.contains('mr-3')) { el.classList.replace('mr-3', 'ml-3'); }
            if (el.classList.contains('mr-4')) { el.classList.replace('mr-4', 'ml-4'); }
            
            if (el.classList.contains('ml-1')) { el.classList.replace('ml-1', 'mr-1'); }
            if (el.classList.contains('ml-2')) { el.classList.replace('ml-2', 'mr-2'); }
            if (el.classList.contains('ml-3')) { el.classList.replace('ml-3', 'mr-3'); }
            if (el.classList.contains('ml-4')) { el.classList.replace('ml-4', 'mr-4'); }
            
            // Fix text alignment
            if (el.classList.contains('text-left')) { el.classList.replace('text-left', 'text-right'); }
            if (el.classList.contains('text-right')) { el.classList.replace('text-right', 'text-left'); }
        });
        
        // Initialize Flowbite components (but exclude modal functionality)
        if (typeof initFlowbite === 'function') {
            // Don't initialize Flowbite modals as we handle them manually
            const originalModal = window.Modal;
            window.Modal = undefined;
            initFlowbite();
            window.Modal = originalModal;
        }
        
        // Initialize tabs with better state management
        initializeTabs();
        
        // Setup collapsible sections
        setupCollapsibleSections();
        
        // Initialize modal system
        initializeModals();
        
        // Setup action buttons
        setupActionButtons();
        
        // Setup API action buttons  
        setupApiButtons();
        
        // Apply RTL specific adjustments
        applyRTLAdjustments();
        
        // Setup mobile navigation
        setupMobileNavigation();
        
        // Setup dropdown functionality
        setupDropdownFunctionality();
    });
    
    // ===========================================
    // UTILITY FUNCTIONS
    // ===========================================
    
    function showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification-toast');
        existingNotifications.forEach(n => n.remove());
        
        const notification = document.createElement('div');
        notification.className = `notification-toast fixed top-4 right-4 z-[99999] p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;
        
        // Set colors based on type
        const colors = {
            'success': 'bg-green-500 text-white',
            'error': 'bg-red-500 text-white', 
            'warning': 'bg-yellow-500 text-black',
            'info': 'bg-blue-500 text-white'
        };
        
        notification.className += ` ${colors[type] || colors.info}`;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span class="font-medium">${message}</span>
                <button type="button" class="ml-2 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
    
    function validateRequired(form, fieldName, errorMessage) {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (!field || !field.value.trim()) {
            showFieldError(field, errorMessage);
            return false;
        }
        clearFieldError(field);
        return true;
    }
    
    function showFieldError(field, message) {
        clearFieldError(field);
        if (!field) return;
        
        field.classList.add('border-red-500', 'bg-red-50');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-red-600 text-sm mt-1';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }
    
    function clearFieldError(field) {
        if (!field) return;
        
        field.classList.remove('border-red-500', 'bg-red-50');
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }
    
    function clearAllErrors(form) {
        form.querySelectorAll('.field-error').forEach(error => error.remove());
        form.querySelectorAll('.border-red-500').forEach(field => {
            field.classList.remove('border-red-500', 'bg-red-50');
        });
    }
    

    
    // ===========================================
    // TAB MANAGEMENT
    // ===========================================
    
    function initializeTabs() {
        const tabElements = [
            { id: 'info-tab', targetId: 'info' },
            { id: 'operations-tab', targetId: 'operations' },
            { id: 'materials-tab', targetId: 'materials' },
            { id: 'notes-tab', targetId: 'notes' },
            { id: 'actions-tab', targetId: 'actions' }
        ];
        
        // Get active tab from localStorage or default to first tab
        const activeTabId = localStorage.getItem('activeWorkOrderTab') || 'info-tab';
        
        // Set active tab based on localStorage or default
        tabElements.forEach(tab => {
            const triggerEl = document.getElementById(tab.id);
            const targetEl = document.getElementById(tab.targetId);
            
            if (!triggerEl || !targetEl) return;
            
            if (tab.id === activeTabId) {
                // Active tab styling
                triggerEl.classList.add('text-blue-600', 'border-blue-600', 'bg-blue-50', 'border-b-2', 'shadow-md');
                triggerEl.classList.remove('border-transparent', 'bg-transparent');
                triggerEl.setAttribute('aria-selected', 'true');
                targetEl.classList.remove('hidden');
            } else {
                // Inactive tab styling
                triggerEl.classList.remove('text-blue-600', 'border-blue-600', 'bg-blue-50', 'border-b-2', 'shadow-md');
                triggerEl.classList.add('border-transparent', 'bg-transparent');
                triggerEl.setAttribute('aria-selected', 'false');
                targetEl.classList.add('hidden');
            }
            
            // Add click event listener
            triggerEl.addEventListener('click', () => {
                // Hide all tabs
                tabElements.forEach(t => {
                    const tTrigger = document.getElementById(t.id);
                    const tTarget = document.getElementById(t.targetId);
                    if (tTrigger && tTarget) {
                        tTrigger.classList.remove('text-blue-600', 'border-blue-600', 'bg-blue-50', 'border-b-2', 'shadow-md');
                        tTrigger.classList.add('border-transparent', 'bg-transparent');
                        tTrigger.setAttribute('aria-selected', 'false');
                        tTarget.classList.add('hidden');
                    }
                });
                
                // Show active tab
                triggerEl.classList.add('text-blue-600', 'border-blue-600', 'bg-blue-50', 'border-b-2', 'shadow-md');
                triggerEl.classList.remove('border-transparent', 'bg-transparent');
                triggerEl.setAttribute('aria-selected', 'true');
                targetEl.classList.remove('hidden');
                
                // Save active tab to localStorage
                localStorage.setItem('activeWorkOrderTab', tab.id);
            });
        });
        
        // Ensure RTL for tab text
        document.querySelectorAll('#mainTabs button span').forEach(span => {
            span.dir = "rtl";
            span.style.fontFamily = "'Tahoma', 'Arial', sans-serif";
        });
    }
    
    // ===========================================
    // COLLAPSIBLE SECTIONS
    // ===========================================
    
    function setupCollapsibleSections() {
        const collapsibleSections = [
            { toggle: 'schedule-toggle', content: 'schedule-content', arrow: 'schedule-arrow' },
            { toggle: 'description-toggle', content: 'description-content', arrow: 'description-arrow' },
            { toggle: 'service-center-toggle', content: 'service-center-content', arrow: 'service-center-arrow' },
            { toggle: 'work-order-type-toggle', content: 'work-order-type-content', arrow: 'work-order-type-arrow' }
        ];
        
         function setupCollapsible(toggleId, contentId, arrowId) {
             const toggle = document.getElementById(toggleId);
             const content = document.getElementById(contentId);
             const arrow = document.getElementById(arrowId);
             
             if (toggle && content && arrow) {
                 // Check if content is empty or just has placeholders
                 const contentText = content.textContent.trim();
                 const hasEmptyValues = contentText.includes('--') || contentText.includes('N/A') || contentText === '';
                 const isEmpty = hasEmptyValues || content.querySelector('p')?.textContent.trim() === '';
                 
                 // Start collapsed if empty
                 let isCollapsed = isEmpty;
                 
                 // Set initial state
                 if (isCollapsed) {
                     content.style.maxHeight = '0';
                     content.style.padding = '0';
                     content.style.opacity = '0';
                     arrow.classList.add('rotate-180');
                 } else {
                     content.style.maxHeight = content.scrollHeight + 'px';
                     content.style.padding = '0.5rem';
                     content.style.opacity = '1';
                 }
                 
                 toggle.addEventListener('click', function() {
                     isCollapsed = !isCollapsed;
                     
                     if (isCollapsed) {
                         content.style.maxHeight = '0';
                         content.style.padding = '0';
                         content.style.opacity = '0';
                         arrow.classList.add('rotate-180');
                     } else {
                         content.style.maxHeight = content.scrollHeight + 'px';
                         content.style.padding = '0.5rem';
                         content.style.opacity = '1';
                         arrow.classList.remove('rotate-180');
                     }
                 });
             }
         }
        
        collapsibleSections.forEach(section => {
            setupCollapsible(section.toggle, section.content, section.arrow);
        });
    }
    
    // ===========================================
    // MODAL MANAGEMENT
    // ===========================================
    
    function initializeModals() {
        // Status change modal
        const statusChangeModal = document.getElementById('status-change-modal');
        const statusToggleBtns = [
            document.getElementById('status-change-btn-top'),
            document.getElementById('status-change-btn-actions')
        ].filter(Boolean); // Remove null elements
        const closeStatusModalBtn = document.getElementById('close-status-modal');
        
        function showStatusModal() {
            if (statusChangeModal) {
                statusChangeModal.style.display = 'block';
                activeModals.add('status-change-modal');
            }
        }
        
        function hideStatusModal() {
            if (statusChangeModal) {
                statusChangeModal.style.display = 'none';
                activeModals.delete('status-change-modal');
            
                // Clear any form errors
                const form = statusChangeModal.querySelector('form');
                if (form) clearAllErrors(form);
            }
        }
        
        // Event listeners for ALL status modal buttons
        statusToggleBtns.forEach(btn => {
            if (btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Status button clicked:', this);
                    showStatusModal();
                });
            }
        });
        
        if (closeStatusModalBtn) {
            closeStatusModalBtn.addEventListener('click', function(e) {
                e.preventDefault();
                hideStatusModal();
            });
        }
        
        // Cancel button in status modal
        const cancelStatusBtn = document.getElementById('cancel-status-change');
        if (cancelStatusBtn) {
            cancelStatusBtn.addEventListener('click', function(e) {
                e.preventDefault();
                hideStatusModal();
            });
        }
        
        // Click outside functionality removed since there's no overlay
        
        // Setup status form submission
        const statusForm = document.getElementById('status-change-form');
        if (statusForm) {
            statusForm.addEventListener('submit', function(e) {
                e.preventDefault();
                submitStatusForm();
            });
        }
        
        // Click outside functionality removed since there are no overlays

        // Global escape key handler
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && activeModals.size > 0) {
                // Close the most recently opened modal
                const lastModal = Array.from(activeModals).pop();
                if (lastModal === 'status-change-modal') hideStatusModal();
                else if (lastModal === 'add-operation-modal') hideAddOperationModal();
                else if (lastModal === 'add-material-modal') hideAddMaterialModal();
                else if (lastModal === 'request-parts-modal') hideRequestPartsModal();
            }
        });
    }
    
    // ===========================================
    // MODAL SHOW/HIDE FUNCTIONS
    // ===========================================
    
    function showAddOperationModal() {
        console.log('showAddOperationModal called');
        const modal = document.getElementById('add-operation-modal');
        if (modal) {
            console.log('Modal found, displaying...');
            modal.style.display = 'block';
            activeModals.add('add-operation-modal');
            
            // Clear previous form data and errors
            const form = modal.querySelector('#operation-form');
            if (form) {
                form.reset();
                clearAllErrors(form);
                
                // Reset to default state
                const existingRadio = document.querySelector('input[name="operation_source"][value="existing"]');
                if (existingRadio) {
                    existingRadio.checked = true;
                    toggleOperationSource();
                }
                
                // Load existing operations
                loadExistingOperations();
            }
        } else {
            console.error('Add operation modal not found');
        }
    }
    
    function hideAddOperationModal() {
        const modal = document.getElementById('add-operation-modal');
        if (modal) {
            modal.style.display = 'none';
            activeModals.delete('add-operation-modal');
        }
    }
    
    function showAddMaterialModal() {
        const modal = document.getElementById('add-material-modal');
        if (modal) {
            modal.style.display = 'block';
            activeModals.add('add-material-modal');
            
            // Clear previous form data and errors
            const form = modal.querySelector('#material-form');
            if (form) {
                form.reset();
                clearAllErrors(form);
            }
        }
    }
    
    function hideAddMaterialModal() {
        const modal = document.getElementById('add-material-modal');
        if (modal) {
            modal.style.display = 'none';
            activeModals.delete('add-material-modal');
        }
    }
    
    function showRequestPartsModal() {
        const modal = document.getElementById('request-parts-modal');
        if (modal) {
            modal.style.display = 'block';
            activeModals.add('request-parts-modal');
            
            // Clear previous form data and errors
            const form = modal.querySelector('#request-parts-form');
            if (form) {
                form.reset();
                clearAllErrors(form);
            }
        }
    }
    
    function hideRequestPartsModal() {
        const modal = document.getElementById('request-parts-modal');
        if (modal) {
            modal.style.display = 'none';
            activeModals.delete('request-parts-modal');
        }
    }
    
    // ===========================================
    // FORM VALIDATION FUNCTIONS
    // ===========================================
    
    function validateOperationForm() {
        const form = document.getElementById('operation-form');
        if (!form) return false;
        
        clearAllErrors(form);
        let isValid = true;
        
        const sourceRadios = form.querySelectorAll('input[name="operation_source"]');
        let selectedSource = 'existing';
        sourceRadios.forEach(radio => {
            if (radio.checked) {
                selectedSource = radio.value;
            }
        });
        
        if (selectedSource === 'existing') {
            // Validate existing operation selection
            const existingSelect = form.querySelector('#existing-operation-select');
            if (!existingSelect || !existingSelect.value) {
                showFieldError(existingSelect, '{% trans "يجب اختيار صيانه من القائمة" %}');
                isValid = false;
            }
        } else {
            // Validate custom operation fields
            if (!validateRequired(form, 'name', '{% trans "اسم الصيانه مطلوب" %}')) {
                isValid = false;
            }
            
            // Validate duration
            const durationField = form.querySelector('[name="duration_minutes"]');
            if (durationField && durationField.value) {
                const duration = parseInt(durationField.value);
                if (isNaN(duration) || duration <= 0) {
                    showFieldError(durationField, '{% trans "المدة يجب أن تكون رقم أكبر من الصفر" %}');
                    isValid = false;
                }
            }
        }
        
        // Validate sequence (common for both)
        const sequenceField = form.querySelector('[name="sequence"]');
        if (sequenceField && sequenceField.value) {
            const sequence = parseInt(sequenceField.value);
            if (isNaN(sequence) || sequence <= 0) {
                showFieldError(sequenceField, '{% trans "الترتيب يجب أن يكون رقم أكبر من الصفر" %}');
                isValid = false;
            }
        }
        
        return isValid;
    }
    
    function validateMaterialForm() {
        const form = document.getElementById('material-form');
        if (!form) return false;
        
        clearAllErrors(form);
        let isValid = true;
        
        // Validate item selection
        if (!validateRequired(form, 'item', '{% trans "يجب اختيار عنصر" %}')) {
            isValid = false;
        }
        
        // Validate quantity
        const quantityField = form.querySelector('[name="quantity"]');
        if (quantityField) {
            const quantity = parseFloat(quantityField.value);
            if (isNaN(quantity) || quantity <= 0) {
                showFieldError(quantityField, '{% trans "الكمية يجب أن تكون رقم أكبر من الصفر" %}');
                isValid = false;
            }
        }
        
        // Validate unit of measure
        if (!validateRequired(form, 'unit_of_measure', '{% trans "وحدة القياس مطلوبة" %}')) {
            isValid = false;
        }
        
        return isValid;
    }
    
    function validateRequestPartsForm() {
        const form = document.getElementById('request-parts-form');
        if (!form) return false;
        
        clearAllErrors(form);
        let isValid = true;
        
        // Validate warehouse
        if (!validateRequired(form, 'warehouse', '{% trans "يجب اختيار مستودع" %}')) {
            isValid = false;
        }
        
        // Validate details
        if (!validateRequired(form, 'details', '{% trans "تفاصيل الطلب مطلوبة" %}')) {
            isValid = false;
        }
        
        return isValid;
    }
    
    // ===========================================
    // FORM SUBMISSION FUNCTIONS
    // ===========================================
    
    function submitStatusForm() {
        if (formSubmitting) return;
        
        const form = document.getElementById('status-change-form');
        if (!form) return;
        
        // Basic validation
        const statusField = form.querySelector('[name="status"]');
        if (!statusField || !statusField.value) {
            showNotification('{% trans "يجب اختيار حالة جديدة" %}', 'error');
            return;
        }
        
        formSubmitting = true;
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {% trans "جاري الحفظ..." %}
        `;
        
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('{% trans "تم تحديث حالة أمر العمل بنجاح" %}', 'success');
                hideStatusModal();
                setTimeout(() => window.location.reload(), 1000);
            } else {
                handleFormErrors(form, data);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{% trans "حدث خطأ أثناء تحديث الحالة" %}', 'error');
        })
        .finally(() => {
            formSubmitting = false;
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    }

    // ===========================================
    // NEW STATUS FLOW FUNCTIONS
    // ===========================================

    let pendingStatusChange = null;
    
    function changeStatus(newStatus) {
        pendingStatusChange = newStatus;
        const confirmModal = document.getElementById('status-confirm-modal');
        const confirmMessage = document.getElementById('confirm-message');
        const confirmTitle = document.getElementById('confirm-title');
        const technicianSelection = document.getElementById('technician-selection');
        const reasonSelection = document.getElementById('reason-selection');
        
        // Update confirmation message and title based on status
        let message = '';
        let title = '';
        switch(newStatus) {
            case 'planned':
                message = '{% trans "هل أنت متأكد من استقبال الطلب؟" %}';
                title = '{% trans "تأكيد الاستقبال" %}';
                break;
            case 'in_progress':
                message = '{% trans "هل أنت متأكد من بدء التشغيل؟" %}';
                title = '{% trans "تأكيد بدء التشغيل" %}';
                break;
            case 'on_hold':
                message = '{% trans "هل أنت متأكد من تعليق الطلب؟" %}';
                title = '{% trans "تأكيد التعليق" %}';
                break;
            case 'completed':
                message = '{% trans "هل أنت متأكد من إكمال العمل؟" %}';
                title = '{% trans "تأكيد الإكمال" %}';
                break;
            case 'cancelled':
                message = '{% trans "هل أنت متأكد من إلغاء الطلب؟" %}';
                title = '{% trans "تأكيد الإلغاء" %}';
                break;
            default:
                message = '{% trans "هل أنت متأكد من تغيير الحالة؟" %}';
                title = '{% trans "تأكيد التغيير" %}';
        }
        
        confirmMessage.textContent = message;
        confirmTitle.textContent = title;
        
        // Show/hide technician selection based on status
        if (newStatus === 'in_progress') {
            technicianSelection.style.display = 'block';
            loadTechnicians();
        } else {
            technicianSelection.style.display = 'none';
        }
        
        // Show/hide reason selection and populate reasons based on status
        if (newStatus === 'on_hold' || newStatus === 'cancelled') {
            reasonSelection.style.display = 'block';
            loadReasons(newStatus);
        } else {
            reasonSelection.style.display = 'none';
        }
        
        // Clear previous notes, technician and reason
        const notesField = document.getElementById('status-change-notes');
        const techField = document.getElementById('selected-technician');
        const reasonField = document.getElementById('status-change-reason');
        notesField.value = '';
        if (techField) techField.value = '';
        if (reasonField) reasonField.value = '';
        
        // Set placeholder based on status
        if (newStatus === 'in_progress') {
            notesField.placeholder = '{% trans "ملاحظات بدء التشغيل" %}';
        } else if (newStatus === 'on_hold') {
            notesField.placeholder = '{% trans "تفاصيل إضافية حول التعليق" %}';
        } else if (newStatus === 'cancelled') {
            notesField.placeholder = '{% trans "تفاصيل إضافية حول الإلغاء" %}';
        } else if (newStatus === 'completed') {
            notesField.placeholder = '{% trans "ملاحظات الإكمال" %}';
        } else {
            notesField.placeholder = '{% trans "ملاحظات إضافية (اختيارية)" %}';
        }
        
        confirmModal.style.display = 'block';
    }
    
    function loadTechnicians() {
        const techSelect = document.getElementById('selected-technician');
        if (!techSelect) return;
        
        // Clear existing options except the first one
        techSelect.innerHTML = '<option value="">{% trans "اختر فني..." %}</option>';
        
        fetch("{% url 'work_orders:api_technicians' %}", {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.technicians) {
                data.technicians.forEach(tech => {
                    const option = document.createElement('option');
                    option.value = tech.id;
                    option.textContent = tech.name;
                    techSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading technicians:', error);
        });
    }
    
    function loadReasons(status) {
        const reasonSelect = document.getElementById('status-change-reason');
        if (!reasonSelect) return;
        
        // Clear existing options
        reasonSelect.innerHTML = '<option value="">{% trans "اختر السبب..." %}</option>';
        
        // Define reasons based on status
        let reasons = [];
        
        if (status === 'on_hold') {
            reasons = [
                { value: 'waiting_parts', text: '{% trans "في انتظار قطع الغيار" %}' },
                { value: 'waiting_customer', text: '{% trans "في انتظار موافقة العميل" %}' },
                { value: 'waiting_authorization', text: '{% trans "في انتظار التصريح" %}' },
                { value: 'technical_issue', text: '{% trans "مشكلة تقنية" %}' },
                { value: 'waiting_diagnosis', text: '{% trans "في انتظار التشخيص" %}' },
                { value: 'external_service', text: '{% trans "في انتظار خدمة خارجية" %}' },
                { value: 'other', text: '{% trans "أخرى" %}' }
            ];
        } else if (status === 'cancelled') {
            reasons = [
                { value: 'customer_request', text: '{% trans "طلب العميل" %}' },
                { value: 'parts_unavailable', text: '{% trans "قطع الغيار غير متوفرة" %}' },
                { value: 'cost_too_high', text: '{% trans "التكلفة عالية جداً" %}' },
                { value: 'technical_impossible', text: '{% trans "غير ممكن تقنياً" %}' },
                { value: 'duplicate_order', text: '{% trans "طلب مكرر" %}' },
                { value: 'customer_no_response', text: '{% trans "عدم رد العميل" %}' },
                { value: 'warranty_void', text: '{% trans "انتهاء الضمان" %}' },
                { value: 'other', text: '{% trans "أخرى" %}' }
            ];
        }
        
        // Populate the dropdown
        reasons.forEach(reason => {
            const option = document.createElement('option');
            option.value = reason.value;
            option.textContent = reason.text;
            reasonSelect.appendChild(option);
        });
        
        // Add change listener to update notes placeholder when reason is selected
        reasonSelect.addEventListener('change', function() {
            const notesField = document.getElementById('status-change-notes');
            if (this.value === 'other') {
                notesField.placeholder = status === 'on_hold' 
                    ? '{% trans "يرجى تحديد سبب التعليق..." %}' 
                    : '{% trans "يرجى تحديد سبب الإلغاء..." %}';
            } else if (this.value) {
                notesField.placeholder = '{% trans "تفاصيل إضافية (اختيارية)" %}';
            }
        });
    }
    
    function cancelStatusChange() {
        const confirmModal = document.getElementById('status-confirm-modal');
        confirmModal.style.display = 'none';
        pendingStatusChange = null;
    }
    
    function confirmStatusChange() {
        if (!pendingStatusChange) return;
        
        const notesField = document.getElementById('status-change-notes');
        const notes = notesField.value.trim();
        
        // Check technician selection for in_progress status
        if (pendingStatusChange === 'in_progress') {
            const techField = document.getElementById('selected-technician');
            const selectedTechnician = techField ? techField.value : '';
            
            if (!selectedTechnician) {
                showNotification('{% trans "يجب اختيار فني عند بدء التشغيل" %}', 'error');
                return;
            }
            
            // Set technician field
            document.getElementById('assigned-technician').value = selectedTechnician;
        }
        
        // Check reason selection for on_hold and cancelled statuses
        if (pendingStatusChange === 'on_hold' || pendingStatusChange === 'cancelled') {
            const reasonField = document.getElementById('status-change-reason');
            const selectedReason = reasonField ? reasonField.value : '';
            
            if (!selectedReason) {
                const statusName = pendingStatusChange === 'on_hold' ? '{% trans "التعليق" %}' : '{% trans "الإلغاء" %}';
                showNotification(`{% trans "يجب اختيار سبب" %} ${statusName}`, 'error');
                return;
            }
            
            // If "Other" is selected, notes become required
            if (selectedReason === 'other' && !notes) {
                const statusName = pendingStatusChange === 'on_hold' ? '{% trans "التعليق" %}' : '{% trans "الإلغاء" %}';
                showNotification(`{% trans "يرجى تحديد سبب" %} ${statusName} {% trans "في الملاحظات" %}`, 'error');
                return;
            }
        }
        
        // Prepare notes with reason if selected
        let finalNotes = notes;
        const reasonField = document.getElementById('status-change-reason');
        if (reasonField && reasonField.value && reasonField.value !== 'other') {
            const reasonText = reasonField.options[reasonField.selectedIndex].text;
            finalNotes = reasonText + (notes ? '\n' + notes : '');
        }
        
        // Set the hidden form fields
        document.getElementById('new-status').value = pendingStatusChange;
        document.getElementById('status-notes').value = finalNotes;
        
        // Hide confirmation modal
        const confirmModal = document.getElementById('status-confirm-modal');
        confirmModal.style.display = 'none';
        
        // Submit the form
        const statusForm = document.getElementById('status-change-form');
        
        formSubmitting = true;
        
        const formData = new FormData(statusForm);
        
        fetch(statusForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('{% trans "تم تحديث حالة أمر العمل بنجاح" %}', 'success');
                const statusModal = document.getElementById('status-change-modal');
                if (statusModal) {
                    statusModal.style.display = 'none';
                    activeModals.delete('status-change-modal');
                }
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showNotification(data.message || '{% trans "حدث خطأ أثناء تحديث الحالة" %}', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{% trans "حدث خطأ أثناء تحديث الحالة" %}', 'error');
        })
        .finally(() => {
            formSubmitting = false;
            pendingStatusChange = null;
        });
    }
    
    function submitOperationForm() {
        if (formSubmitting || !validateOperationForm()) return;
        
        const form = document.getElementById('operation-form');
        const formData = new FormData();
        
        // Get operation source
        const sourceRadios = form.querySelectorAll('input[name="operation_source"]');
        let selectedSource = 'existing';
        sourceRadios.forEach(radio => {
            if (radio.checked) {
                selectedSource = radio.value;
            }
        });
        
        // Add common fields
        formData.append('work_order', '{{ work_order.id }}');
        formData.append('sequence', form.querySelector('[name="sequence"]').value);
        formData.append('operation_source', selectedSource);
        
        if (selectedSource === 'existing') {
            // Handle existing operation
            const existingSelect = form.querySelector('#existing-operation-select');
            const selectedOption = existingSelect.options[existingSelect.selectedIndex];
            
            if (selectedOption && selectedOption.dataset.operationData) {
                const operationData = JSON.parse(selectedOption.dataset.operationData);
                formData.append('existing_operation_id', selectedOption.value);
                formData.append('name', operationData.name);
                formData.append('description', operationData.description || '');
                formData.append('duration_minutes', operationData.duration_minutes || 60);
                formData.append('operation_type', selectedOption.dataset.operationType);
            }
        } else {
            // Handle custom operation
            formData.append('name', form.querySelector('[name="name"]').value);
            formData.append('description', form.querySelector('[name="description"]').value || '');
            formData.append('duration_minutes', form.querySelector('[name="duration_minutes"]').value || 60);
        }
        
        formSubmitting = true;
        showLoadingState('operation');
        
        fetch("{% url 'work_orders:api_create_operation' %}", {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                showNotification('{% trans "تم إضافة الصيانه بنجاح" %}', 'success');
                hideAddOperationModal();
                setTimeout(() => window.location.reload(), 1000);
            } else {
                handleFormErrors(form, data);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
            showNotification('{% trans "حدث خطأ أثناء إضافة الصيانه" %}', 'error');
        })
        .finally(() => {
            formSubmitting = false;
            hideLoadingState('operation');
        });
    }
    
    function submitMaterialForm() {
        if (formSubmitting || !validateMaterialForm()) return;
        
        const form = document.getElementById('material-form');
        const formData = new FormData(form);
        
        // Ensure work_order_id is set
        formData.set('work_order', '{{ work_order.id }}');
        
        formSubmitting = true;
        showLoadingState('material');
        
        fetch("{% url 'work_orders:add_material' work_order.id %}", {
                    method: 'POST',
            body: formData,
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                showNotification('{% trans "تم إضافة المواد بنجاح" %}', 'success');
                hideAddMaterialModal();
                setTimeout(() => window.location.reload(), 1000);
                    } else {
                handleFormErrors(form, data);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
            showNotification('{% trans "حدث خطأ أثناء إضافة المواد" %}', 'error');
        })
        .finally(() => {
            formSubmitting = false;
            hideLoadingState('material');
        });
    }
    
    function submitRequestPartsForm() {
        if (formSubmitting || !validateRequestPartsForm()) return;
        
        const form = document.getElementById('request-parts-form');
        const formData = new FormData(form);
        
        formSubmitting = true;
        showLoadingState('request-parts');
        
        fetch("{% url 'work_orders:api_request_parts' %}", {
                    method: 'POST',
            body: formData,
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                showNotification('{% trans "تم إرسال طلب القطع بنجاح" %}', 'success');
                hideRequestPartsModal();
                setTimeout(() => window.location.reload(), 1000);
                    } else {
                handleFormErrors(form, data);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
            showNotification('{% trans "حدث خطأ أثناء إرسال الطلب" %}', 'error');
        })
        .finally(() => {
            formSubmitting = false;
            hideLoadingState('request-parts');
        });
    }
    
    // ===========================================
    // HELPER FUNCTIONS
    // ===========================================
    
    function showLoadingState(formType) {
        const buttons = {
            'operation': document.querySelector('#add-operation-modal button[onclick="submitOperationForm()"]'),
            'material': document.querySelector('#add-material-modal button[onclick="submitMaterialForm()"]'),
            'request-parts': document.querySelector('#request-parts-modal button[onclick="submitRequestPartsForm()"]')
        };
        
        const button = buttons[formType];
        if (button) {
            button.disabled = true;
            button.dataset.originalText = button.innerHTML;
            button.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {% trans "جاري المعالجة..." %}
            `;
        }
    }
    
    function hideLoadingState(formType) {
        const buttons = {
            'operation': document.querySelector('#add-operation-modal button[onclick="submitOperationForm()"]'),
            'material': document.querySelector('#add-material-modal button[onclick="submitMaterialForm()"]'),
            'request-parts': document.querySelector('#request-parts-modal button[onclick="submitRequestPartsForm()"]')
        };
        
        const button = buttons[formType];
        if (button && button.dataset.originalText) {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText;
            delete button.dataset.originalText;
        }
    }
    
    function handleFormErrors(form, data) {
        if (data.errors) {
            // Show field-specific errors
            Object.entries(data.errors).forEach(([field, errors]) => {
                const fieldElement = form.querySelector(`[name="${field}"]`);
                if (fieldElement) {
                    showFieldError(fieldElement, Array.isArray(errors) ? errors.join(', ') : errors);
                }
            });
            showNotification('{% trans "يرجى تصحيح الأخطاء المذكورة" %}', 'error');
        } else {
            showNotification(data.error || '{% trans "حدث خطأ غير متوقع" %}', 'error');
        }
    }
    
    // ===========================================
    // ACTION BUTTON SETUP
    // ===========================================
    
    function setupActionButtons() {
        console.log('Setting up action buttons...');
        
        // Add Operation buttons (multiple buttons with same functionality)
        const addOperationBtns = document.querySelectorAll('[id*="add-operation-btn"]');
        console.log('Found', addOperationBtns.length, 'add operation buttons');
        addOperationBtns.forEach((btn, index) => {
            console.log('Adding click listener to operation button', index, btn.id);
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Operation button clicked:', btn.id);
                showAddOperationModal();
            });
        });
        
        // Add Material buttons (multiple buttons with same functionality)
        const addMaterialBtns = document.querySelectorAll('[id*="add-material-btn"]');
        console.log('Found', addMaterialBtns.length, 'add material buttons');
        addMaterialBtns.forEach((btn, index) => {
            console.log('Adding click listener to material button', index, btn.id);
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Material button clicked:', btn.id);
                showAddMaterialModal();
            });
        });
        
        // Request Parts buttons (multiple buttons with same functionality)
        const requestPartsBtns = document.querySelectorAll('[id*="request-parts-btn"]');
        console.log('Found', requestPartsBtns.length, 'request parts buttons');
        requestPartsBtns.forEach((btn, index) => {
            console.log('Adding click listener to request parts button', index, btn.id);
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Request parts button clicked:', btn.id);
                showRequestPartsModal();
            });
        });
        
        // Print button
        const printBtns = document.querySelectorAll('[onclick="window.print()"]');
        console.log('Found', printBtns.length, 'print buttons');
        printBtns.forEach((btn, index) => {
            console.log('Print button', index, 'is working with onclick');
        });
        
        // Edit work order buttons (links)
        const editBtns = document.querySelectorAll('a[href*="work_order_update"]');
        console.log('Found', editBtns.length, 'edit work order buttons');
        editBtns.forEach((btn, index) => {
            console.log('Edit button', index, 'href:', btn.href);
        });
        
        // Status change buttons
        const statusBtns = [
            document.getElementById('status-change-btn-top'),
            document.getElementById('status-change-btn-actions')
        ].filter(Boolean);
        console.log('Found', statusBtns.length, 'status change buttons');
        statusBtns.forEach((btn, index) => {
            console.log('Status change button', index, 'is set up in initializeModals() - both top and Actions tab buttons');
        });
        
        // Operation source radio button listeners
        document.addEventListener('change', function(e) {
            if (e.target.name === 'operation_source') {
                toggleOperationSource();
            }
        });
        
        // Existing operation dropdown listener
        document.addEventListener('change', function(e) {
            if (e.target.id === 'existing-operation-select') {
                const selectedOption = e.target.options[e.target.selectedIndex];
                if (selectedOption && selectedOption.dataset.operationData) {
                    const operationData = JSON.parse(selectedOption.dataset.operationData);
                    showOperationDetails(operationData);
                } else {
                    showOperationDetails(null);
                }
            }
        });
    }
    
    function setupApiButtons() {
        // Operation completion buttons
        document.querySelectorAll('[data-complete-url]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('data-complete-url');
                const operationName = this.closest('tr')?.querySelector('td:first-child')?.textContent?.trim() || 'العملية';
                
                if (confirm(`{% trans "هل أنت متأكد من إكمال" %} ${operationName}؟`)) {
                    performApiAction(url, `{% trans "تم إكمال العملية بنجاح" %}`, '{% trans "حدث خطأ أثناء إكمال العملية" %}');
                }
            });
        });
        
        // Material consumption buttons
        document.querySelectorAll('[data-consume-url]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('data-consume-url');
                const materialName = this.closest('tr')?.querySelector('td:first-child')?.textContent?.trim() || 'المادة';
                
                if (confirm(`{% trans "هل أنت متأكد من استهلاك" %} ${materialName}؟`)) {
                    performApiAction(url, `{% trans "تم تسجيل استهلاك المادة بنجاح" %}`, '{% trans "حدث خطأ أثناء تسجيل الاستهلاك" %}');
                }
            });
        });
        
        // Load more history button
        const loadMoreHistoryBtn = document.getElementById('load-more-history');
        if (loadMoreHistoryBtn) {
            loadMoreHistoryBtn.addEventListener('click', function() {
                fetch("{% url 'work_orders:api_get_work_order_history' work_order.id %}?limit=50")
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const historyContainer = document.getElementById('history');
                            // Implementation for updating history would go here
                            showNotification('{% trans "تم تحميل المزيد من السجل" %}', 'success');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('{% trans "حدث خطأ أثناء تحميل السجل" %}', 'error');
                    });
            });
        }
    }
    
    function performApiAction(url, successMessage, errorMessage) {
        fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                showNotification(successMessage, 'success');
                setTimeout(() => window.location.reload(), 1000);
                } else {
                showNotification(data.error || errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            showNotification(errorMessage, 'error');
        });
    }
    
    // ===========================================
    // RTL AND MOBILE SETUP
    // ===========================================
    
    function applyRTLAdjustments() {
        if (document.dir === 'rtl') {
            // Fix border alignment for RTL
            const borderElements = document.querySelectorAll('.border-l-4');
            borderElements.forEach(el => {
                el.classList.remove('border-l-4');
                el.classList.add('border-r-4');
            });
            
            // Fix text alignment in tables
            const tableHeaders = document.querySelectorAll('th.text-left');
            tableHeaders.forEach(th => {
                th.classList.remove('text-left');
                th.classList.add('text-right');
            });
            
            const tableCells = document.querySelectorAll('td.text-left');
            tableCells.forEach(td => {
                td.classList.remove('text-left');
                td.classList.add('text-right');
            });
        }
        
        // Force RTL on all elements
        document.querySelectorAll('*').forEach(el => {
            if (el.style) {
                el.style.textAlign = el.style.textAlign === 'left' ? 'right' : el.style.textAlign;
            }
        });
    }
    
    function setupMobileNavigation() {
        // Mobile navigation toggle
        const mobileToggleBtn = document.getElementById('mobile-actions-toggle');
        const actionsPanel = document.querySelector('.lg\\:w-1\\/4');
        
        if (mobileToggleBtn && actionsPanel) {
            // Initially hide on mobile
            if (window.innerWidth < 1024) {
                actionsPanel.classList.add('hidden');
            }
            
            mobileToggleBtn.addEventListener('click', function() {
                actionsPanel.classList.toggle('hidden');
            });
            
            // Hide panel when clicking outside on mobile
            document.addEventListener('click', function(e) {
                if (window.innerWidth < 1024 && 
                    !actionsPanel.contains(e.target) && 
                    e.target !== mobileToggleBtn) {
                    actionsPanel.classList.add('hidden');
                }
            });
        }
    }
    
    function setupDropdownFunctionality() {
        // Dropdown functionality removed
        console.log('Dropdown functionality has been removed');
    }
    
    // Make functions globally available for inline onclick handlers
    window.submitOperationForm = submitOperationForm;
    window.submitMaterialForm = submitMaterialForm;
    window.submitRequestPartsForm = submitRequestPartsForm;
    window.hideAddOperationModal = hideAddOperationModal;
    window.hideAddMaterialModal = hideAddMaterialModal;
    window.hideRequestPartsModal = hideRequestPartsModal;
    window.toggleOperationSource = toggleOperationSource;
    window.loadExistingOperations = loadExistingOperations;
    window.showOperationDetails = showOperationDetails;
    
    // Test function to verify all buttons are working
    window.testAllButtons = function() {
        console.log('=== TESTING ALL BUTTONS ===');
        
        // Test status change buttons
        const statusBtns = [
            document.getElementById('status-change-btn-top'),
            document.getElementById('status-change-btn-actions')
        ].filter(Boolean);
        console.log('Status change buttons:', statusBtns.length, 'found');
        
        // Test add operation buttons
        const operationBtns = document.querySelectorAll('[id*="add-operation-btn"]');
        console.log('Add operation buttons:', operationBtns.length, 'found');
        
        // Test add material buttons
        const materialBtns = document.querySelectorAll('[id*="add-material-btn"]');
        console.log('Add material buttons:', materialBtns.length, 'found');
        
        // Test request parts buttons
        const partsBtns = document.querySelectorAll('[id*="request-parts-btn"]');
        console.log('Request parts buttons:', partsBtns.length, 'found');
        
        // Test print button
        const printBtn = document.querySelector('[onclick="window.print()"]');
        console.log('Print button:', printBtn ? 'FOUND' : 'NOT FOUND');
        
        // Edit work order button has been removed
        
        // Dropdown button has been removed
        
        // Test modals
        const operationModal = document.getElementById('add-operation-modal');
        const materialModal = document.getElementById('add-material-modal');
        const partsModal = document.getElementById('request-parts-modal');
        const statusModal = document.getElementById('status-change-modal');
        
        console.log('Operation modal:', operationModal ? 'FOUND' : 'NOT FOUND');
        console.log('Material modal:', materialModal ? 'FOUND' : 'NOT FOUND');
        console.log('Parts modal:', partsModal ? 'FOUND' : 'NOT FOUND');
        console.log('Status modal:', statusModal ? 'FOUND' : 'NOT FOUND');
        
        console.log('=== TEST COMPLETE ===');
        
        return {
            statusBtns: statusBtns.length,
            operationBtns: operationBtns.length,
            materialBtns: materialBtns.length,
            partsBtns: partsBtns.length,
            printBtn: !!printBtn,
            modals: {
                operation: !!operationModal,
                material: !!materialModal,
                parts: !!partsModal,
                status: !!statusModal
            }
        };
    };
    
    // Quick test functions for each modal
    window.testOperationModal = function() {
        console.log('Testing operation modal...');
        const modal = document.getElementById('add-operation-modal');
        if (modal) {
            modal.style.display = 'block';
            activeModals.add('add-operation-modal');
        }
    };
    
    window.testMaterialModal = function() {
        console.log('Testing material modal...');
        const modal = document.getElementById('add-material-modal');
        if (modal) {
            modal.style.display = 'block';
            activeModals.add('add-material-modal');
        }
    };
    
    window.testPartsModal = function() {
        console.log('Testing parts modal...');
        const modal = document.getElementById('request-parts-modal');
        if (modal) {
            modal.style.display = 'block';
            activeModals.add('request-parts-modal');
        }
    };
    
    window.testStatusModal = function() {
        console.log('Testing status modal...');
        const statusChangeModal = document.getElementById('status-change-modal');
        if (statusChangeModal) {
            statusChangeModal.style.display = 'block';
            activeModals.add('status-change-modal');
        }
    };
    
    // ===========================================
    // OPERATION MODAL SPECIFIC FUNCTIONS
    // ===========================================
    
    function toggleOperationSource() {
        const existingSection = document.getElementById('existing-operation-section');
        const customSection = document.getElementById('custom-operation-section');
        const operationDetails = document.getElementById('operation-details');
        const sourceRadios = document.querySelectorAll('input[name="operation_source"]');
        
        let selectedSource = 'existing';
        sourceRadios.forEach(radio => {
            if (radio.checked) {
                selectedSource = radio.value;
            }
        });
        
        if (selectedSource === 'existing') {
            existingSection.style.display = 'block';
            customSection.style.display = 'none';
            operationDetails.style.display = 'none';
        } else {
            existingSection.style.display = 'none';
            customSection.style.display = 'block';
            operationDetails.style.display = 'none';
        }
    }
    
    function loadExistingOperations() {
        const dropdown = document.getElementById('existing-operation-select');
        if (!dropdown) return;
        
        // Clear existing options except the first one
        dropdown.innerHTML = '<option value="">جاري التحميل...</option>';
        
        console.log('Loading existing operations...');
        
        // Get CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        
        fetch("{% url 'work_orders:api_get_existing_operations' %}", {
            method: 'GET',
                headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('API Response status:', response.status);
            if (response.status === 302 || response.status === 401) {
                throw new Error('Authentication required');
            }
            return response.json();
        })
            .then(data => {
            console.log('API Response data:', data);
            
            // Clear loading option
            dropdown.innerHTML = '<option value="">اختر عملية موجودة...</option>';
            
                if (data.success) {
                let hasOptions = false;
                
                // Add schedule operations group
                if (data.schedule_operations && data.schedule_operations.length > 0) {
                    const scheduleGroup = document.createElement('optgroup');
                    scheduleGroup.label = 'عمليات الصيانة المجدولة';
                    
                    data.schedule_operations.forEach(op => {
                        const option = document.createElement('option');
                        option.value = `schedule_${op.id}`;
                        option.textContent = `${op.name} (${op.maintenance_schedule_name || 'جدول الصيانة'})`;
                        option.dataset.operationType = 'schedule';
                        option.dataset.operationData = JSON.stringify(op);
                        scheduleGroup.appendChild(option);
                    });
                    
                    dropdown.appendChild(scheduleGroup);
                    hasOptions = true;
                }
                
                // Add work order operations group
                if (data.work_order_operations && data.work_order_operations.length > 0) {
                    const woGroup = document.createElement('optgroup');
                    woGroup.label = 'عمليات من أوامر عمل سابقة';
                    
                    data.work_order_operations.forEach(op => {
                        const option = document.createElement('option');
                        option.value = `workorder_${op.id}`;
                        option.textContent = op.name;
                        option.dataset.operationType = 'workorder';
                        option.dataset.operationData = JSON.stringify(op);
                        woGroup.appendChild(option);
                    });
                    
                    dropdown.appendChild(woGroup);
                    hasOptions = true;
                }
                
                if (!hasOptions) {
                    dropdown.innerHTML = '<option value="">لا توجد عمليات متاحة</option>';
                }
                
                console.log(`Loaded ${data.schedule_operations?.length || 0} schedule operations and ${data.work_order_operations?.length || 0} work order operations`);
                
                if (data.debug_info) {
                    console.log('Debug info:', data.debug_info);
                }
                
                } else {
                console.error('API Error:', data.error);
                dropdown.innerHTML = '<option value="">خطأ في تحميل العمليات</option>';
                showNotification('خطأ في تحميل العمليات الموجودة: ' + (data.error || 'خطأ غير معروف'), 'error');
                }
            })
            .catch(error => {
            console.error('Fetch Error:', error);
            dropdown.innerHTML = '<option value="">خطأ في الاتصال</option>';
            if (error.message === 'Authentication required') {
                showNotification('يرجى تسجيل الدخول أولاً', 'error');
            } else {
                showNotification('خطأ في الاتصال بالخادم', 'error');
            }
        });
    }
    
    function showOperationDetails(operationData) {
        const detailsDiv = document.getElementById('operation-details');
        const contentDiv = document.getElementById('operation-details-content');
        
        if (!operationData) {
            detailsDiv.style.display = 'none';
            return;
        }
        
        let detailsHtml = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 13px;">
                <div>
                    <span style="font-weight: 600; color: #6b7280;">{% trans "الاسم" %}:</span>
                    <span style="color: #374151;">${operationData.name}</span>
                </div>
                <div>
                    <span style="font-weight: 600; color: #6b7280;">{% trans "المدة" %}:</span>
                    <span style="color: #374151;">${operationData.duration_minutes} {% trans "دقيقة" %}</span>
                </div>
        `;
        
        if (operationData.description) {
            detailsHtml += `
                <div style="grid-column: 1 / -1;">
                    <span style="font-weight: 600; color: #6b7280;">{% trans "الوصف" %}:</span>
                    <span style="color: #374151;">${operationData.description}</span>
                </div>
            `;
        }
        
        if (operationData.maintenance_schedule_name) {
            detailsHtml += `
                <div style="grid-column: 1 / -1;">
                    <span style="font-weight: 600; color: #6b7280;">{% trans "جدول الصيانة" %}:</span>
                    <span style="color: #374151;">${operationData.maintenance_schedule_name}</span>
                </div>
            `;
        }
        
        detailsHtml += '</div>';
        contentDiv.innerHTML = detailsHtml;
        detailsDiv.style.display = 'block';
    }
    
    // Auto-run test when page loads
    setTimeout(function() {
        console.log('=== AUTO MODAL TEST ===');
        const operationModal = document.getElementById('add-operation-modal');
        const materialModal = document.getElementById('add-material-modal');
        const partsModal = document.getElementById('request-parts-modal');
        const statusModal = document.getElementById('status-change-modal');
        
        console.log('Operation modal found:', !!operationModal);
        console.log('Material modal found:', !!materialModal);
        console.log('Parts modal found:', !!partsModal);
        console.log('Status modal found:', !!statusModal);
        
        if (operationModal) {
            console.log('Operation modal current display:', operationModal.style.display);
        }
        
        // Test button event listeners
        const operationBtns = document.querySelectorAll('[id*="add-operation-btn"]');
        const materialBtns = document.querySelectorAll('[id*="add-material-btn"]');
        const partsBtns = document.querySelectorAll('[id*="request-parts-btn"]');
        
        console.log('Found buttons:', {
            operation: operationBtns.length,
            material: materialBtns.length,
            parts: partsBtns.length
        });
        
        console.log('=== END AUTO TEST ===');
    }, 1000);

</script>

<!-- Edit Material Modal -->
<div id="edit-material-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-xl font-bold mb-4 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            {% trans "تعديل المادة" %}
        </h3>
        <form id="edit-material-form">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "الكمية" %}</label>
                <input type="number" name="quantity" step="0.01" min="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "الوحدة" %}</label>
                <input type="text" name="unit_of_measure" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "ملاحظات" %}</label>
                <textarea name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>
            <div class="flex justify-end gap-3">
                <button type="button" id="close-edit-material-modal" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">{% trans "إلغاء" %}</button>
                <button type="submit" class="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700">{% trans "حفظ" %}</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Material Modal -->
<div id="delete-material-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-xl font-bold mb-4 flex items-center gap-2 text-red-600">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            {% trans "حذف المادة" %}
        </h3>
        <p class="text-gray-700 mb-6">{% trans "هل أنت متأكد من حذف هذه المادة؟ لا يمكن التراجع عن هذا الإجراء." %}</p>
        <div id="delete-material-preview" class="bg-gray-50 p-3 rounded mb-4 text-sm"></div>
        <div class="flex justify-end gap-3">
            <button type="button" id="close-delete-material-modal" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">{% trans "إلغاء" %}</button>
            <button type="button" id="confirm-delete-material" class="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700">{% trans "حذف" %}</button>
        </div>
    </div>
</div>

<!-- Edit Operation Modal -->
<div id="edit-operation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-xl font-bold mb-4 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            {% trans "تعديل العملية" %}
        </h3>
        <form id="edit-operation-form">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "اسم العملية" %}</label>
                <input type="text" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "المدة (بالدقائق)" %}</label>
                <input type="number" name="duration_minutes" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "الوصف" %}</label>
                <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "ملاحظات" %}</label>
                <textarea name="notes" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>
            <div class="flex justify-end gap-3">
                <button type="button" id="close-edit-operation-modal" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">{% trans "إلغاء" %}</button>
                <button type="submit" class="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700">{% trans "حفظ" %}</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Operation Modal -->
<div id="delete-operation-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-xl font-bold mb-4 flex items-center gap-2 text-red-600">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            {% trans "حذف العملية" %}
        </h3>
        <p class="text-gray-700 mb-6">{% trans "هل أنت متأكد من حذف هذه العملية؟ لا يمكن التراجع عن هذا الإجراء." %}</p>
        <div id="delete-operation-preview" class="bg-gray-50 p-3 rounded mb-4 text-sm"></div>
        <div class="flex justify-end gap-3">
            <button type="button" id="close-delete-operation-modal" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300">{% trans "إلغاء" %}</button>
            <button type="button" id="confirm-delete-operation" class="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700">{% trans "حذف" %}</button>
        </div>
    </div>
</div>

<script>
    // Material edit and delete functionality
    let currentMaterialId = null;
    let currentOperationId = null;

    // Edit Material Modal
    const editMaterialModal = document.getElementById('edit-material-modal');
    const editMaterialForm = document.getElementById('edit-material-form');
    const closeEditMaterialModal = document.getElementById('close-edit-material-modal');
    
    console.log('Modal elements found:');
    console.log('editMaterialModal:', !!editMaterialModal);
    console.log('editMaterialForm:', !!editMaterialForm);
    console.log('closeEditMaterialModal:', !!closeEditMaterialModal);
    
    // Check if materials and operations tables exist
    const materialsTable = document.querySelector('#materials table');
    const operationsTable = document.querySelector('#operations table');
    console.log('Tables found:');
    console.log('materialsTable:', !!materialsTable);
    console.log('operationsTable:', !!operationsTable);
    
    if (materialsTable) {
        const materialRows = materialsTable.querySelectorAll('tbody tr');
        console.log('Material rows found:', materialRows.length);
    }
    if (operationsTable) {
        const operationRows = operationsTable.querySelectorAll('tbody tr');
        console.log('Operation rows found:', operationRows.length);
    }

    // Delete Material Modal
    const deleteMaterialModal = document.getElementById('delete-material-modal');
    const deleteEditMaterialModal = document.getElementById('close-delete-material-modal');
    const confirmDeleteMaterial = document.getElementById('confirm-delete-material');

    // Edit Operation Modal
    const editOperationModal = document.getElementById('edit-operation-modal');
    const editOperationForm = document.getElementById('edit-operation-form');
    const closeEditOperationModal = document.getElementById('close-edit-operation-modal');

    // Delete Operation Modal
    const deleteOperationModal = document.getElementById('delete-operation-modal');
    const closeDeleteOperationModal = document.getElementById('close-delete-operation-modal');
    const confirmDeleteOperation = document.getElementById('confirm-delete-operation');

    // Add event listeners for material edit buttons
    document.addEventListener('click', function(e) {
        console.log('Document click detected:', e.target);
        
        // Edit material button
        if (e.target.closest('[title="تعديل المادة"]')) {
            console.log('Edit material button clicked');
            e.preventDefault();
            const button = e.target.closest('button');
            const row = button.closest('tr');
            
            if (!row || !editMaterialForm || !editMaterialModal) {
                console.error('Missing elements for material edit');
                return;
            }
            
            const materialNameElement = row.querySelector('td:first-child span');
            const quantityElement = row.querySelector('td:nth-child(2) span');
            
            if (!materialNameElement || !quantityElement) {
                console.error('Cannot find material name or quantity elements');
                return;
            }
            
            const materialName = materialNameElement.textContent.trim();
            const quantityText = quantityElement.textContent.trim();
            const quantity = quantityText.split(' ')[0].trim(); // Remove newlines from quantity
            const unit = quantityText.split(' ').slice(1).join(' ').trim();
            
            // Get material ID from data attribute
            currentMaterialId = button.dataset.materialId;
            
            // Fill form
            const quantityInput = editMaterialForm.querySelector('[name="quantity"]');
            const unitInput = editMaterialForm.querySelector('[name="unit_of_measure"]');
            const notesInput = editMaterialForm.querySelector('[name="notes"]');
            
            if (quantityInput) quantityInput.value = quantity;
            if (unitInput) unitInput.value = unit;
            if (notesInput) notesInput.value = '';
            
            editMaterialModal.classList.remove('hidden');
        }

        // Delete material button
        if (e.target.closest('[title="حذف المادة"]')) {
            console.log('Delete material button clicked');
            e.preventDefault();
            const button = e.target.closest('button');
            const row = button.closest('tr');
            
            if (!row || !deleteMaterialModal) {
                console.error('Missing elements for material delete');
                return;
            }
            
            const materialNameElement = row.querySelector('td:first-child span');
            const quantityElement = row.querySelector('td:nth-child(2) span');
            
            if (!materialNameElement || !quantityElement) {
                console.error('Cannot find material name or quantity elements');
                return;
            }
            
            const materialName = materialNameElement.textContent.trim();
            const quantityText = quantityElement.textContent.trim();
            
            currentMaterialId = button.dataset.materialId;
            
            const previewElement = document.getElementById('delete-material-preview');
            if (previewElement) {
                previewElement.innerHTML = 
                    `<strong>${materialName}</strong><br><small>${quantityText}</small>`;
            }
            
            deleteMaterialModal.classList.remove('hidden');
        }

        // Edit operation button
        if (e.target.closest('[title="تعديل العملية"]')) {
            console.log('Edit operation button clicked');
            e.preventDefault();
            const button = e.target.closest('button');
            const row = button.closest('tr');
            
            if (!row || !editOperationForm || !editOperationModal) {
                console.error('Missing elements for operation edit');
                return;
            }
            
            const operationNameElement = row.querySelector('td:nth-child(1) span');
            const durationElement = row.querySelector('td:nth-child(2) span');
            
            if (!operationNameElement || !durationElement) {
                console.error('Cannot find operation name or duration elements');
                return;
            }
            
            const operationName = operationNameElement.textContent.trim();
            const durationText = durationElement.textContent.trim();
            const duration = durationText.split(' ')[0].trim(); // Remove any newlines
            
            currentOperationId = button.dataset.operationId;
            
            // Fill form
            const nameInput = editOperationForm.querySelector('[name="name"]');
            const durationInput = editOperationForm.querySelector('[name="duration_minutes"]');
            const descriptionInput = editOperationForm.querySelector('[name="description"]');
            const notesInput = editOperationForm.querySelector('[name="notes"]');
            
            if (nameInput) nameInput.value = operationName;
            if (durationInput) durationInput.value = duration;
            if (descriptionInput) descriptionInput.value = '';
            if (notesInput) notesInput.value = '';
            
            editOperationModal.classList.remove('hidden');
        }

        // Delete operation button
        if (e.target.closest('[title="حذف العملية"]')) {
            console.log('Delete operation button clicked');
            e.preventDefault();
            const button = e.target.closest('button');
            const row = button.closest('tr');
            
            if (!row || !deleteOperationModal) {
                console.error('Missing elements for operation delete');
                return;
            }
            
            const operationNameElement = row.querySelector('td:nth-child(1) span');
            const durationElement = row.querySelector('td:nth-child(2) span');
            
            if (!operationNameElement || !durationElement) {
                console.error('Cannot find operation name or duration elements');
                return;
            }
            
            const operationName = operationNameElement.textContent.trim();
            const durationText = durationElement.textContent.trim();
            
            currentOperationId = button.dataset.operationId;
            
            const previewElement = document.getElementById('delete-operation-preview');
            if (previewElement) {
                previewElement.innerHTML = 
                    `<strong>${operationName}</strong><br><small>${durationText}</small>`;
            }
            
            deleteOperationModal.classList.remove('hidden');
        }
    });

    // Close modal handlers
    if (closeEditMaterialModal) {
        closeEditMaterialModal.addEventListener('click', () => {
            editMaterialModal.classList.add('hidden');
        });
    }

    if (deleteEditMaterialModal) {
        deleteEditMaterialModal.addEventListener('click', () => {
            deleteMaterialModal.classList.add('hidden');
        });
    }

    if (closeEditOperationModal) {
        closeEditOperationModal.addEventListener('click', () => {
            editOperationModal.classList.add('hidden');
        });
    }

    if (closeDeleteOperationModal) {
        closeDeleteOperationModal.addEventListener('click', () => {
            deleteOperationModal.classList.add('hidden');
        });
    }

    // Form submission handlers
    if (editMaterialForm) {
        editMaterialForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!currentMaterialId) {
                showNotification('خطأ: لم يتم تحديد المادة', 'error');
                return;
            }
            
            const quantityInput = this.querySelector('[name="quantity"]');
            const unitInput = this.querySelector('[name="unit_of_measure"]');
            const notesInput = this.querySelector('[name="notes"]');
            
            if (!quantityInput || !unitInput) {
                showNotification('خطأ: بيانات النموذج غير مكتملة', 'error');
                return;
            }
            
            const formData = {
                quantity: quantityInput.value,
                unit_of_measure: unitInput.value,
                notes: notesInput ? notesInput.value : ''
            };
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (!csrfToken) {
                showNotification('خطأ: رمز الأمان غير موجود', 'error');
                return;
            }
            
            // Make API call to update material
            fetch(`/ar/work-orders/api/materials/${currentMaterialId}/`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken.value
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('تم تحديث المادة بنجاح', 'success');
                    editMaterialModal.classList.add('hidden');
                    location.reload(); // Refresh to show updates
                } else {
                    showNotification('خطأ في تحديث المادة: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('خطأ في الاتصال', 'error');
            });
        });
    }

    if (editOperationForm) {
        editOperationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!currentOperationId) {
                showNotification('خطأ: لم يتم تحديد العملية', 'error');
                return;
            }
            
            const nameInput = this.querySelector('[name="name"]');
            const durationInput = this.querySelector('[name="duration_minutes"]');
            const descriptionInput = this.querySelector('[name="description"]');
            const notesInput = this.querySelector('[name="notes"]');
            
            if (!nameInput || !durationInput) {
                showNotification('خطأ: بيانات النموذج غير مكتملة', 'error');
                return;
            }
            
            const formData = {
                name: nameInput.value,
                duration_minutes: durationInput.value,
                description: descriptionInput ? descriptionInput.value : '',
                notes: notesInput ? notesInput.value : ''
            };
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (!csrfToken) {
                showNotification('خطأ: رمز الأمان غير موجود', 'error');
                return;
            }
            
            // Make API call to update operation
            fetch(`/ar/work-orders/api/operations/${currentOperationId}/`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken.value
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('تم تحديث العملية بنجاح', 'success');
                    editOperationModal.classList.add('hidden');
                    location.reload(); // Refresh to show updates
                } else {
                    showNotification('خطأ في تحديث العملية: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('خطأ في الاتصال', 'error');
            });
        });
    }

    // Delete confirmation handlers
    if (confirmDeleteMaterial) {
        confirmDeleteMaterial.addEventListener('click', function() {
            if (!currentMaterialId) {
                showNotification('خطأ: لم يتم تحديد المادة', 'error');
                return;
            }
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (!csrfToken) {
                showNotification('خطأ: رمز الأمان غير موجود', 'error');
                return;
            }
            
            fetch(`/ar/work-orders/api/materials/${currentMaterialId}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': csrfToken.value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('تم حذف المادة بنجاح', 'success');
                    deleteMaterialModal.classList.add('hidden');
                    
                    // Find and remove the material row with animation
                    const materialRow = document.querySelector(`button[data-material-id="${currentMaterialId}"]`)?.closest('tr');
                    if (materialRow) {
                        // Add fade out animation
                        materialRow.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                        materialRow.style.opacity = '0';
                        materialRow.style.transform = 'translateX(-20px)';
                        
                        // Remove the row after animation completes
                        setTimeout(() => {
                            materialRow.remove();
                            
                            // Check if materials table is empty and show empty state
                            const materialsTable = document.querySelector('#materials tbody');
                            if (materialsTable && materialsTable.children.length === 0) {
                                materialsTable.innerHTML = '<tr><td colspan="4" class="text-center text-gray-500 py-8">لا توجد مواد مطلوبة</td></tr>';
                            }
                        }, 300);
                    }
                } else {
                    showNotification('خطأ في حذف المادة: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('خطأ في الاتصال', 'error');
            });
        });
    }

    if (confirmDeleteOperation) {
        confirmDeleteOperation.addEventListener('click', function() {
            if (!currentOperationId) {
                showNotification('خطأ: لم يتم تحديد العملية', 'error');
                return;
            }
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (!csrfToken) {
                showNotification('خطأ: رمز الأمان غير موجود', 'error');
                return;
            }
            
            fetch(`/ar/work-orders/api/operations/${currentOperationId}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': csrfToken.value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('تم حذف العملية بنجاح', 'success');
                    deleteOperationModal.classList.add('hidden');
                    
                    // Find and remove the operation row with animation
                    const operationRow = document.querySelector(`button[data-operation-id="${currentOperationId}"]`)?.closest('tr');
                    if (operationRow) {
                        // Add fade out animation
                        operationRow.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                        operationRow.style.opacity = '0';
                        operationRow.style.transform = 'translateX(-20px)';
                        
                        // Remove the row after animation completes
                        setTimeout(() => {
                            operationRow.remove();
                            
                            // Check if operations table is empty and show empty state
                            const operationsTable = document.querySelector('#operations tbody');
                            if (operationsTable && operationsTable.children.length === 0) {
                                operationsTable.innerHTML = '<tr><td colspan="5" class="text-center text-gray-500 py-8">لا توجد عمليات مطلوبة</td></tr>';
                            }
                        }, 300);
                    }
                } else {
                    showNotification('خطأ في حذف العملية: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('خطأ في الاتصال', 'error');
            });
        });
    }

    // Close modals when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target === editMaterialModal) {
            editMaterialModal.classList.add('hidden');
        }
        if (e.target === deleteMaterialModal) {
            deleteMaterialModal.classList.add('hidden');
        }
        if (e.target === editOperationModal) {
            editOperationModal.classList.add('hidden');
        }
        if (e.target === deleteOperationModal) {
            deleteOperationModal.classList.add('hidden');
        }
    });
</script>

{% endlanguage %}
{% endblock %}