{% load i18n %}

<div class="relative py-4">
    <div class="absolute inset-0 flex items-center" aria-hidden="true">
        <div class="w-0.5 h-full bg-gray-200 mx-auto"></div>
    </div>
    
    <div class="relative z-10 space-y-6">
        {% for entry in history_entries %}
        <div class="flex items-center">
            <!-- Timeline dot with status color -->
            <div class="relative flex h-8 w-8 flex-none items-center justify-center">
                {% if entry.action_type == 'status_change' %}
                    {% if entry.new_value == 'completed' %}
                        <div class="h-6 w-6 rounded-full bg-green-500"></div>
                    {% elif entry.new_value == 'in_progress' %}
                        <div class="h-6 w-6 rounded-full bg-blue-500"></div>
                    {% elif entry.new_value == 'on_hold' %}
                        <div class="h-6 w-6 rounded-full bg-yellow-500"></div>
                    {% elif entry.new_value == 'cancelled' %}
                        <div class="h-6 w-6 rounded-full bg-red-500"></div>
                    {% elif entry.new_value == 'planned' %}
                        <div class="h-6 w-6 rounded-full bg-indigo-500"></div>
                    {% else %}
                        <div class="h-6 w-6 rounded-full bg-gray-500"></div>
                    {% endif %}
                {% elif entry.action_type == 'material_added' or entry.action_type == 'material_consumed' %}
                    <div class="h-6 w-6 rounded-full bg-purple-500"></div>
                {% elif entry.action_type == 'operation_added' or entry.action_type == 'operation_completed' %}
                    <div class="h-6 w-6 rounded-full bg-orange-500"></div>
                {% else %}
                    <div class="h-6 w-6 rounded-full bg-gray-500"></div>
                {% endif %}
            </div>
            
            <!-- Timeline content -->
            <div class="flex-auto rounded-md border border-gray-200 p-3 shadow-sm">
                <div class="flex justify-between gap-x-4">
                    <div class="py-0.5 text-xs leading-5 text-gray-500">
                        <span>{{ entry.created_at|date:"Y-m-d H:i" }}</span>
                    </div>
                    <div class="text-xs font-medium leading-5">
                        {% if entry.user %}
                            {% trans "By" %}: {{ entry.user }}
                        {% else %}
                            {% trans "System" %}
                        {% endif %}
                    </div>
                </div>
                <p class="text-sm font-semibold leading-6 text-gray-900">{{ entry.description }}</p>
                
                {% if entry.notes %}
                <div class="mt-2 text-sm text-gray-700">
                    <p>{{ entry.notes }}</p>
                </div>
                {% endif %}
                
                {% if entry.previous_value or entry.new_value %}
                <div class="mt-2 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                    {% if entry.previous_value %}
                    <div>
                        <span class="font-medium text-gray-700">{% trans "From" %}:</span> 
                        <span>{{ entry.previous_value }}</span>
                    </div>
                    {% endif %}
                    
                    {% if entry.previous_value and entry.new_value %}
                    <svg class="h-1.5 w-1.5 fill-current" viewBox="0 0 6 6" aria-hidden="true">
                        <circle cx="3" cy="3" r="3" />
                    </svg>
                    {% endif %}
                    
                    {% if entry.new_value %}
                    <div>
                        <span class="font-medium text-gray-700">{% trans "To" %}:</span> 
                        <span>{{ entry.new_value }}</span>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% empty %}
        <div class="flex items-center">
            <div class="relative flex h-8 w-8 flex-none items-center justify-center">
                <div class="h-6 w-6 rounded-full bg-gray-300"></div>
            </div>
            <div class="flex-auto rounded-md border border-gray-200 p-3 shadow-sm">
                <p class="text-sm text-gray-500">{% trans "No history entries available" %}</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>