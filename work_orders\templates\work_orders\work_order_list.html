{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "أوامر العمل" %} | {% trans "نظام Aftersails" %}{% endblock %}

{% block extra_css %}
<style>
    /* Base styles for status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-draft { background-color: #f3f4f6; color: #374151; }
    .status-planned { background-color: #dbeafe; color: #1e40af; }
    .status-in_progress { background-color: #fef3c7; color: #d97706; }
    .status-on_hold { background-color: #fee2e2; color: #dc2626; }
    .status-completed { background-color: #d1fae5; color: #065f46; }
    .status-cancelled { background-color: #f3e8ff; color: #7c3aed; }

    .action-btn {
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .btn-primary { background-color: #3b82f6; color: white; }
    .btn-success { background-color: #10b981; color: white; }
    .btn-warning { background-color: #f59e0b; color: white; }
    .btn-danger { background-color: #ef4444; color: white; }

    .table-row:hover {
        background-color: #f9fafb;
    }

    /* Priority badges */
    .priority-low { color: #6b7280; }
    .priority-medium { color: #3b82f6; }
    .priority-high { color: #f59e0b; }
    .priority-critical { color: #ef4444; }

    /* RTL adjustments */
    html[dir="rtl"] .action-btn i {
        margin-left: 0.25rem;
        margin-right: 0;
    }

    /* Table styling with RTL support */
    .work-orders-table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        direction: rtl;
        font-family: 'Tahoma', 'Arial', sans-serif;
    }

    .work-orders-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 600;
        text-align: right;
        padding: 0.75rem 0.875rem;
        border-bottom: 1px solid #d1d5db;
        border-left: 1px solid #d1d5db;
        position: relative;
        color: #374151;
        font-size: 0.875rem;
    }

    .work-orders-table th:first-child {
        border-left: none;
    }

    .work-orders-table th .header-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        direction: rtl;
    }

    .work-orders-table th .header-icon {
        width: 1rem;
        height: 1rem;
        color: #6b7280;
        flex-shrink: 0;
    }

    .work-orders-table th span {
        font-weight: 600;
        color: #374151;
    }

    .work-orders-table td {
        padding: 1rem 0.875rem;
        border-bottom: 1px solid #e5e7eb;
        border-left: 1px solid #e5e7eb;
        vertical-align: middle;
        text-align: right;
        direction: rtl;
        font-weight: 600;
        color: #111827;
        font-size: 1rem;
        line-height: 1.6;
    }

    .work-orders-table td:first-child {
        border-left: none;
    }

    .work-orders-table tbody tr:hover {
        background-color: #f9fafb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .work-orders-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Enhanced text styling for better readability */
    .work-orders-table .work-order-number {
        font-weight: 800;
        color: #1e40af;
        font-size: 1.125rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .work-orders-table .customer-name {
        font-weight: 700;
        color: #047857;
        font-size: 1.0625rem;
    }

    .work-orders-table .vehicle-info {
        font-weight: 700;
        color: #7c2d12;
        font-size: 1rem;
    }

    .work-orders-table .vehicle-info small {
        font-weight: 600;
        color: #6b7280;
        display: block;
        margin-top: 0.25rem;
        font-size: 0.875rem;
    }

    .work-orders-table .technician-name {
        font-weight: 700;
        color: #7c3aed;
        font-size: 1rem;
    }

    .work-orders-table .date-info {
        font-weight: 700;
        color: #374151;
        font-size: 1rem;
    }

    .work-orders-table .date-info small {
        font-weight: 600;
        color: #6b7280;
        display: block;
        margin-top: 0.25rem;
        font-size: 0.875rem;
    }

    .work-orders-table .unassigned,
    .work-orders-table .not-specified {
        font-weight: 600;
        color: #9ca3af;
        font-style: italic;
    }

    /* Status filter tabs */
    .status-tabs {
        background: white;
        border-radius: 0.75rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 1.5rem;
        overflow: hidden;
        display: flex;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .status-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 0.75rem;
        border: none;
        background: transparent;
        cursor: pointer;
        font-weight: 600;
        font-size: 0.75rem;
        transition: all 0.3s ease;
        border-left: 1px solid #e5e7eb;
        color: #6b7280;
        text-decoration: none;
        min-width: 80px;
        flex: 1;
        gap: 0.25rem;
        min-height: 60px;
    }

    .status-tab:first-child {
        border-left: none;
    }

    .status-tab:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .status-tab .tab-icon {
        width: 1.25rem;
        height: 1.25rem;
        margin-bottom: 0.125rem;
    }

    .status-tab .tab-label {
        font-weight: 600;
        text-align: center;
        font-size: 0.75rem;
    }

    .status-tab .tab-count {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 9999px;
        padding: 0.125rem 0.375rem;
        font-size: 0.625rem;
        font-weight: 700;
        min-width: 1.25rem;
        text-align: center;
        line-height: 1;
    }

    /* All status - Blue */
    .status-tab[data-status=""] {
        color: #1e40af;
    }
    .status-tab[data-status=""]:hover {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e3a8a;
    }
    .status-tab[data-status=""].active {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    /* Draft - Gray */
    .status-tab[data-status="draft"] {
        color: #374151;
    }
    .status-tab[data-status="draft"]:hover {
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        color: #111827;
    }
    .status-tab[data-status="draft"].active {
        background: linear-gradient(135deg, #6b7280 0%, #374151 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
    }

    /* Planned - Indigo */
    .status-tab[data-status="planned"] {
        color: #4338ca;
    }
    .status-tab[data-status="planned"]:hover {
        background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        color: #3730a3;
    }
    .status-tab[data-status="planned"].active {
        background: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
    }

    /* In Progress - Orange */
    .status-tab[data-status="in_progress"] {
        color: #ea580c;
    }
    .status-tab[data-status="in_progress"]:hover {
        background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
        color: #c2410c;
    }
    .status-tab[data-status="in_progress"].active {
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
    }

    /* On Hold - Yellow */
    .status-tab[data-status="on_hold"] {
        color: #d97706;
    }
    .status-tab[data-status="on_hold"]:hover {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        color: #b45309;
    }
    .status-tab[data-status="on_hold"].active {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    }

    /* Completed - Green */
    .status-tab[data-status="completed"] {
        color: #059669;
    }
    .status-tab[data-status="completed"]:hover {
        background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        color: #047857;
    }
    .status-tab[data-status="completed"].active {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    /* Cancelled - Red */
    .status-tab[data-status="cancelled"] {
        color: #dc2626;
    }
    .status-tab[data-status="cancelled"]:hover {
        background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
        color: #b91c1c;
    }
    .status-tab[data-status="cancelled"].active {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    }

    /* Complete RTL Dropdown Fix */
    .filter-select {
        appearance: none !important;
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: left 12px center !important;
        background-size: 16px !important;
        padding-right: 20px !important;
        padding-left: 45px !important;
        text-align: right !important;
        direction: rtl !important;
        text-align-last: right !important;
        box-sizing: border-box !important;
    }

    /* Force remove all browser default arrows */
    .filter-select::-webkit-dropdown-arrow {
        display: none !important;
    }
    
    .filter-select::-moz-appearance {
        display: none !important;
    }

    /* Remove default arrow from select */
    .filter-select::-ms-expand {
        display: none;
    }

    /* Date input RTL styling */
    input[type="date"][dir="rtl"] {
        text-align: right !important;
        direction: rtl !important;
        padding-right: 16px !important;
        padding-left: 16px !important;
    }

    /* Search input RTL styling */
    input[type="text"][dir="rtl"] {
        text-align: right !important;
        direction: rtl !important;
        padding-right: 16px !important;
        padding-left: 16px !important;
    }

    /* Override any conflicting styles */
    .filter-select option {
        text-align: right !important;
        direction: rtl !important;
        padding-right: 8px !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6" dir="rtl">
        <!-- Filters and Actions -->
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
        <div class="flex flex-wrap items-center gap-2">
            <!-- Status Filter -->
            <div class="flex-shrink-0">
                <select id="statusFilter" class="filter-select py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
                    <option value="">{% trans "جميع الحالات" %}</option>
                    <option value="planned">{% trans "استقبال" %}</option>
                    <option value="in_progress">{% trans "قيد التنفيذ" %}</option>
                    <option value="on_hold">{% trans "معلق" %}</option>
                    <option value="completed">{% trans "مكتمل" %}</option>
                    <option value="cancelled">{% trans "ملغي" %}</option>
                </select>
            </div>

            <!-- Priority Filter -->
            <div class="flex-shrink-0">
                <select id="priorityFilter" class="filter-select py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
                    <option value="">{% trans "جميع الأولويات" %}</option>
                    <option value="low">{% trans "منخفضة" %}</option>
                    <option value="medium">{% trans "متوسطة" %}</option>
                    <option value="high">{% trans "عالية" %}</option>
                    <option value="critical">{% trans "حرجة" %}</option>
                </select>
            </div>

            <!-- Technician Filter -->
            <div class="flex-shrink-0">
                <select id="technicianFilter" class="filter-select py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
                    <option value="">{% trans "جميع الفنيين" %}</option>
                    <!-- Will be populated by JavaScript -->
                </select>
            </div>

            <!-- Start Date Filter -->
            <div class="flex-shrink-0">
                <input type="date" id="startDateFilter" 
                       placeholder="{% trans 'تاريخ البداية' %}"
                       class="py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
            </div>

            <!-- End Date Filter -->
            <div class="flex-shrink-0">
                <input type="date" id="endDateFilter" 
                       placeholder="{% trans 'تاريخ النهاية' %}"
                       class="py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
            </div>

            <!-- Search Filter -->
            <div class="flex-shrink-0">
                <input type="text" id="searchFilter" placeholder="{% trans 'رقم أمر العمل أو العميل...' %}" 
                       class="py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]" dir="rtl">
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-2 flex-shrink-0">
                <a href="{% url 'work_orders:work_order_create' %}" 
                   class="inline-flex items-center justify-center w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm transition-colors duration-200"
                   title="{% trans 'أمر عمل جديد' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </a>
                <button onclick="refreshData()" 
                        class="inline-flex items-center justify-center w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg shadow-sm transition-colors duration-200"
                        title="{% trans 'تحديث' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Status Filter Tabs -->
    <div class="status-tabs" dir="rtl">
        <button class="status-tab active" data-status="" onclick="filterByStatus('')">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <span class="tab-label">{% trans "الكل" %}</span>
            <span class="tab-count" id="count-all">{{ object_list.count }}</span>
        </button>
        
        <button class="status-tab" data-status="planned" onclick="filterByStatus('planned')">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
                            <span class="tab-label">{% trans "استقبال" %}</span>
            <span class="tab-count" id="count-planned">0</span>
        </button>
        <button class="status-tab" data-status="in_progress" onclick="filterByStatus('in_progress')">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="tab-label">{% trans "قيد التنفيذ" %}</span>
            <span class="tab-count" id="count-in_progress">0</span>
        </button>
        <button class="status-tab" data-status="on_hold" onclick="filterByStatus('on_hold')">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="tab-label">{% trans "معلق" %}</span>
            <span class="tab-count" id="count-on_hold">0</span>
        </button>
        <button class="status-tab" data-status="completed" onclick="filterByStatus('completed')">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="tab-label">{% trans "مكتمل" %}</span>
            <span class="tab-count" id="count-completed">0</span>
        </button>
        <button class="status-tab" data-status="cancelled" onclick="filterByStatus('cancelled')">
            <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="tab-label">{% trans "ملغي" %}</span>
            <span class="tab-count" id="count-cancelled">0</span>
        </button>
    </div>

    <!-- Work Orders Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="work-orders-table w-full" id="workOrdersTable">
                <thead>
                    <tr>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>{% trans "رقم أمر العمل" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>{% trans "العميل" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span>{% trans "المركبة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{% trans "الحالة" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <span>{% trans "الأولوية" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span>{% trans "الفني المسؤول" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <span>{% trans "تاريخ الإنشاء" %}</span>
                            </div>
                        </th>
                        <th class="text-right">
                            <div class="header-content">
                                <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                                <span>{% trans "الإجراءات" %}</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
        {% for work_order in object_list %}
                    <tr class="table-row" data-status="{{ work_order.status }}" data-priority="{{ work_order.priority }}" data-technician="{{ work_order.assigned_technician.id|default:'' }}">
                        <td>
                            <span class="work-order-number">{{ work_order.work_order_number }}</span>
                        </td>
                        <td>
                            <span class="customer-name">
                                {{ work_order.customer_name|default:work_order.customer.full_name|default:"غير محدد" }}
                            </span>
                        </td>
                        <td>
                            {% if work_order.vehicle %}
                                <div class="vehicle-info">
                                    {{ work_order.vehicle.make.name }} {{ work_order.vehicle.model.name }}
                                    <small>{{ work_order.vehicle.license_plate }}</small>
                </div>
                            {% else %}
                                <span class="not-specified">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                                  <span class="status-badge status-{{ work_order.status }}">
                      {% if work_order.status == 'planned' %}{% trans "استقبال" %}
                    {% elif work_order.status == 'in_progress' %}{% trans "قيد التنفيذ" %}
                    {% elif work_order.status == 'on_hold' %}{% trans "معلق" %}
                    {% elif work_order.status == 'completed' %}{% trans "مكتمل" %}
                    {% elif work_order.status == 'cancelled' %}{% trans "ملغي" %}
                    {% endif %}
                </span>
                        </td>
                        <td>
                            <span class="priority-{{ work_order.priority }} font-bold">
                                {% if work_order.priority == 'low' %}{% trans "منخفضة" %}
                                {% elif work_order.priority == 'medium' %}{% trans "متوسطة" %}
                                {% elif work_order.priority == 'high' %}{% trans "عالية" %}
                                {% elif work_order.priority == 'critical' %}{% trans "حرجة" %}
                        {% endif %}
                    </span>
                        </td>
                        <td>
                            {% if work_order.assigned_technician %}
                                <span class="technician-name">{{ work_order.assigned_technician.get_full_name|default:work_order.assigned_technician.username }}</span>
                            {% else %}
                                <span class="unassigned">غير مُعيَّن</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="date-info">
                                {{ work_order.created_at|date:"Y/m/d" }}
                                <small>{{ work_order.created_at|date:"H:i" }}</small>
                </div>
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                {% if work_order.status == 'completed' %}
                                    <!-- Preview only for completed work orders -->
                                    <a href="{% url 'work_orders:work_order_detail' work_order.pk %}" class="action-btn btn-primary text-xs">
                                        <i class="fas fa-eye ml-1"></i>
                                        {% trans "عرض" %}
                                    </a>
                                {% else %}
                                    <!-- Full actions for non-completed work orders -->
                                    <a href="{% url 'work_orders:work_order_detail' work_order.pk %}" class="action-btn btn-primary text-xs">
                                        <i class="fas fa-edit ml-1"></i>
                                        {% trans "تعديل" %}
                                    </a>
                    
                                    {% if work_order.status != 'cancelled' and work_order.status != 'planned' %}
                                    <button class="status-change-btn action-btn btn-warning text-xs" 
                                            data-work-order-id="{{ work_order.pk }}" 
                                            data-current-status="{{ work_order.status }}">
                                        <i class="fas fa-exchange-alt ml-1"></i>
                                        {% trans "تغيير الحالة" %}
                                    </button>
                                    {% endif %}
                    
                                    {% if not work_order.assigned_technician and work_order.status != 'planned' %}
                                    <button class="assign-technician-btn action-btn btn-success text-xs" 
                                            data-work-order-id="{{ work_order.pk }}">
                                        <i class="fas fa-user-plus ml-1"></i>
                                        {% trans "تعيين فني" %}
                                    </button>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </td>
                    </tr>
        {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-12">
            <i class="fas fa-clipboard-list text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">{% trans "لا توجد أوامر عمل" %}</h3>
            <p class="text-gray-500 mb-4">{% trans "لم يتم العثور على أوامر عمل مطابقة للمعايير المحددة" %}</p>
            <a href="{% url 'work_orders:work_order_create' %}" class="action-btn btn-primary">
                <i class="fas fa-plus ml-2"></i>
                {% trans "إنشاء أمر عمل جديد" %}
            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="flex justify-center mt-8">
        <nav class="flex items-center space-x-2 space-x-reverse">
            {% if page_obj.has_previous %}
            <a href="?page=1" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                {% trans "الأولى" %}
            </a>
            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                {% trans "السابقة" %}
            </a>
            {% endif %}
            
            <span class="px-3 py-2 text-sm font-medium text-gray-700 bg-blue-50 border border-blue-300 rounded-md">
                {% trans "صفحة" %} {{ page_obj.number }} {% trans "من" %} {{ page_obj.paginator.num_pages }}
            </span>
            
            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                {% trans "التالية" %}
            </a>
            <a href="?page={{ page_obj.paginator.num_pages }}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                {% trans "الأخيرة" %}
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}
</div>

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/work_order_status.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load technicians for filter
    loadTechniciansForFilter();
    
    // Setup filters
    setupFilters();
    
    // Calculate status counts
    calculateStatusCounts();
});

function loadTechniciansForFilter() {
    fetch('{% url "work_orders:api_technicians" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('technicianFilter');
                data.technicians.forEach(tech => {
                    const option = document.createElement('option');
                    option.value = tech.id;
                    option.textContent = tech.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading technicians:', error));
}

function setupFilters() {
    const statusFilter = document.getElementById('statusFilter');
    const priorityFilter = document.getElementById('priorityFilter');
    const technicianFilter = document.getElementById('technicianFilter');
    const startDateFilter = document.getElementById('startDateFilter');
    const endDateFilter = document.getElementById('endDateFilter');
    const searchFilter = document.getElementById('searchFilter');
    
    [statusFilter, priorityFilter, technicianFilter, startDateFilter, endDateFilter, searchFilter].forEach(filter => {
        filter.addEventListener('change', applyFilters);
        filter.addEventListener('input', applyFilters);
    });
}

function applyFilters() {
    const statusFilter = document.getElementById('statusFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    const technicianFilter = document.getElementById('technicianFilter').value;
    const startDateFilter = document.getElementById('startDateFilter').value;
    const endDateFilter = document.getElementById('endDateFilter').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
    
    const rows = document.querySelectorAll('.table-row');
    
    rows.forEach(row => {
        let show = true;
        
        // Status filter
        if (statusFilter && row.dataset.status !== statusFilter) {
            show = false;
        }
        
        // Priority filter
        if (priorityFilter && row.dataset.priority !== priorityFilter) {
            show = false;
        }
        
        // Technician filter
        if (technicianFilter && row.dataset.technician !== technicianFilter) {
            show = false;
        }
        
        // Date filters
        if (startDateFilter || endDateFilter) {
            const rowDate = row.dataset.createdDate; // Assuming this data attribute exists
            if (rowDate) {
                const workOrderDate = new Date(rowDate);
                
                if (startDateFilter) {
                    const startDate = new Date(startDateFilter);
                    if (workOrderDate < startDate) {
                        show = false;
                    }
                }
                
                if (endDateFilter) {
                    const endDate = new Date(endDateFilter);
                    endDate.setHours(23, 59, 59, 999); // Include the entire end date
                    if (workOrderDate > endDate) {
                        show = false;
                    }
                }
            }
        }
        
        // Search filter
        if (searchFilter) {
            const rowText = row.textContent.toLowerCase();
            if (!rowText.includes(searchFilter)) {
                show = false;
            }
        }
        
        row.style.display = show ? 'table-row' : 'none';
    });
}

function refreshData() {
    window.location.reload();
}

function calculateStatusCounts() {
    const rows = document.querySelectorAll('.table-row');
    const counts = {
        'draft': 0,
        'planned': 0,
        'in_progress': 0,
        'on_hold': 0,
        'completed': 0,
        'cancelled': 0
    };
    
    rows.forEach(row => {
        const status = row.dataset.status;
        if (counts.hasOwnProperty(status)) {
            counts[status]++;
        }
    });
    
    // Update count displays
    Object.keys(counts).forEach(status => {
        const countElement = document.getElementById(`count-${status}`);
        if (countElement) {
            countElement.textContent = counts[status];
        }
    });
    
    // Update total count
    const totalCountElement = document.getElementById('count-all');
    if (totalCountElement) {
        totalCountElement.textContent = rows.length;
    }
}

function filterByStatus(status) {
    // Update active tab
    document.querySelectorAll('.status-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-status="${status}"]`).classList.add('active');
    
    // Clear other filters
    document.getElementById('statusFilter').value = status;
    document.getElementById('priorityFilter').value = '';
    document.getElementById('technicianFilter').value = '';
    document.getElementById('startDateFilter').value = '';
    document.getElementById('endDateFilter').value = '';
    document.getElementById('searchFilter').value = '';
    
    // Apply filter
    applyFilters();
}
</script>
{% endblock %} 