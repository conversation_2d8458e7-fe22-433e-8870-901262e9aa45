from django import template
from django.template.base import TokenType
from importlib import util

register = template.Library()

@register.filter
def filter_is_installed(value):
    """
    Check if a template tag library is installed.
    Usage: {% if "crispy_forms_tags"|filter_is_installed %}
    """
    try:
        # Check if the module is installed
        if value == "crispy_forms_tags":
            return util.find_spec("crispy_forms") is not None
        return False
    except:
        return False 