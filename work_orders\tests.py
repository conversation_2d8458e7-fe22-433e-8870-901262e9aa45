import json
from decimal import Decimal
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock

from .models import (
    WorkOrder, WorkOrderMaterial, WorkOrderOperation, 
    WorkOrderTransferRequest, WorkOrderAllocation, 
    WorkOrderQualityCheck, WorkOrderTemplate
)
from .services import (
    WorkOrderToSalesService, WorkOrderAllocationService,
    WorkOrderWorkflowService, WorkOrderPricingService
)
from inventory.models import Item
from warehouse.models import Location, ItemLocation
from sales.models import SalesOrder, SalesOrderItem
from billing.models import Invoice
from setup.models import Customer, Vehicle, ServiceCenter, ServiceCenterType, Company, Franchise

User = get_user_model()


class WorkOrderTestBase(TestCase):
    """Base test class with common setup for work order tests"""
    
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test tenant data
        self.tenant_id = 1
        
        # Create test customer
        self.customer = Customer.objects.create(
            tenant_id=self.tenant_id,
            first_name='John',
            last_name='Doe',
            phone='************',
            email='<EMAIL>'
        )
        
        # Create test vehicle
        self.vehicle = Vehicle.objects.create(
            tenant_id=self.tenant_id,
            make='Toyota',
            model='Camry',
            year=2020,
            license_plate='ABC123',
            owner=self.customer
        )
        
        # Create test service center dependencies
        self.franchise = Franchise.objects.create(
            tenant_id=self.tenant_id,
            name='Test Franchise',
            code='TF001'
        )
        
        self.company = Company.objects.create(
            tenant_id=self.tenant_id,
            franchise=self.franchise,
            name='Test Company',
            code='TC001'
        )
        
        self.center_type = ServiceCenterType.objects.create(
            name='Standard Center',
            max_capacity=20
        )
        
        # Create test service center
        self.service_center = ServiceCenter.objects.create(
            tenant_id=self.tenant_id,
            company=self.company,
            name='Test Service Center',
            code='SC001',
            center_type=self.center_type,
            phone='555-0123',
            email='<EMAIL>'
        )
        
        # Create test warehouse
        self.warehouse = Location.objects.create(
            tenant_id=self.tenant_id,
            name='Main Warehouse',
            code='MAIN',
            address='123 Main St'
        )
        
        # Create test items
        self.item1 = Item.objects.create(
            tenant_id=self.tenant_id,
            name='Oil Filter',
            sku='OF001',
            unit_price=Decimal('25.00'),
            quantity=100
        )
        
        self.item2 = Item.objects.create(
            tenant_id=self.tenant_id,
            name='Air Filter',
            sku='AF001',
            unit_price=Decimal('15.00'),
            quantity=50
        )
        
        # Create warehouse stock
        ItemLocation.objects.create(
            tenant_id=self.tenant_id,
            item=self.item1,
            location=self.warehouse,
            quantity=50
        )
        
        ItemLocation.objects.create(
            tenant_id=self.tenant_id,
            item=self.item2,
            location=self.warehouse,
            quantity=25
        )
        
        # Create test work order
        self.work_order = WorkOrder.objects.create(
            tenant_id=self.tenant_id,
            work_order_number='WO-001',
            description='Test work order',
            customer=self.customer,
            vehicle=self.vehicle,
            service_center=self.service_center,
            status='planned',
            priority='medium'
        )
        
        # Add materials to work order
        self.wo_material1 = WorkOrderMaterial.objects.create(
            tenant_id=self.tenant_id,
            work_order=self.work_order,
            item=self.item1,
            quantity=2,
            unit_of_measure='pcs'
        )
        
        self.wo_material2 = WorkOrderMaterial.objects.create(
            tenant_id=self.tenant_id,
            work_order=self.work_order,
            item=self.item2,
            quantity=1,
            unit_of_measure='pcs'
        )
        
        # Add operations to work order
        self.wo_operation1 = WorkOrderOperation.objects.create(
            tenant_id=self.tenant_id,
            work_order=self.work_order,
            name='Replace Oil Filter',
            description='Remove old filter and install new one',
            duration_minutes=30,
            sequence=10
        )
        
        self.wo_operation2 = WorkOrderOperation.objects.create(
            tenant_id=self.tenant_id,
            work_order=self.work_order,
            name='Replace Air Filter',
            description='Replace air filter',
            duration_minutes=15,
            sequence=20
        )


class WorkOrderToSalesServiceTests(WorkOrderTestBase):
    """Tests for WorkOrderToSalesService"""
    
    def test_process_completed_work_order_success(self):
        """Test successful processing of completed work order"""
        # Mark work order as completed
        self.work_order.status = 'completed'
        self.work_order.save()
        
        # Mark materials as consumed
        self.wo_material1.is_consumed = True
        self.wo_material1.save()
        self.wo_material2.is_consumed = True
        self.wo_material2.save()
        
        # Mark operations as completed
        self.wo_operation1.is_completed = True
        self.wo_operation1.save()
        self.wo_operation2.is_completed = True
        self.wo_operation2.save()
        
        # Process work order
        result = WorkOrderToSalesService.process_completed_work_order(self.work_order)
        
        # Assertions
        self.assertTrue(result['success'])
        self.assertIsNotNone(result['sales_order'])
        self.assertIsNotNone(result['invoice'])
        self.assertEqual(len(result['errors']), 0)
        
        # Check sales order creation
        sales_order = result['sales_order']
        self.assertEqual(sales_order.customer, self.customer)
        self.assertEqual(sales_order.work_order, self.work_order)
        self.assertEqual(sales_order.status, 'delivered')
        
        # Check sales order items
        so_items = sales_order.items.all()
        self.assertEqual(so_items.count(), 4)  # 2 materials + 2 operations
        
        # Check invoice creation
        invoice = result['invoice']
        self.assertEqual(invoice.customer, self.customer)
        self.assertEqual(invoice.sales_order, sales_order)
        self.assertEqual(invoice.work_order, self.work_order)
    
    def test_process_work_order_without_customer(self):
        """Test processing work order without customer fails"""
        self.work_order.customer = None
        self.work_order.status = 'completed'
        self.work_order.save()
        
        result = WorkOrderToSalesService.process_completed_work_order(self.work_order)
        
        self.assertFalse(result['success'])
        self.assertIn('customer', result['errors'][0].lower())
    
    def test_process_work_order_not_completed(self):
        """Test processing work order that's not completed fails"""
        result = WorkOrderToSalesService.process_completed_work_order(self.work_order)
        
        self.assertFalse(result['success'])
        self.assertIn('completed', result['errors'][0].lower())
    
    def test_prevent_duplicate_sales_order(self):
        """Test that duplicate sales orders are not created"""
        # Mark work order as completed
        self.work_order.status = 'completed'
        self.work_order.save()
        
        # Process first time
        result1 = WorkOrderToSalesService.process_completed_work_order(self.work_order)
        self.assertTrue(result1['success'])
        
        # Process second time
        result2 = WorkOrderToSalesService.process_completed_work_order(self.work_order)
        self.assertTrue(result2['success'])
        
        # Should return same sales order
        self.assertEqual(result1['sales_order'].id, result2['sales_order'].id)


class WorkOrderWorkflowServiceTests(WorkOrderTestBase):
    """Tests for WorkOrderWorkflowService"""
    
    def test_can_transition_to_in_progress_with_technician(self):
        """Test work order can transition to in_progress when technician assigned"""
        self.work_order.assigned_technician = self.user
        self.work_order.save()
        
        result = WorkOrderWorkflowService.can_transition_to_status(
            self.work_order, 'in_progress', self.user
        )
        
        self.assertTrue(result['can_transition'])
        self.assertIsNone(result['reason'])
    
    def test_cannot_transition_to_in_progress_without_technician(self):
        """Test work order cannot transition to in_progress without technician"""
        result = WorkOrderWorkflowService.can_transition_to_status(
            self.work_order, 'in_progress', self.user
        )
        
        self.assertFalse(result['can_transition'])
        self.assertIn('technician', result['reason'].lower())
    
    def test_cannot_transition_from_completed(self):
        """Test work order cannot transition from completed status"""
        self.work_order.status = 'completed'
        self.work_order.save()
        
        result = WorkOrderWorkflowService.can_transition_to_status(
            self.work_order, 'in_progress', self.user
        )
        
        self.assertFalse(result['can_transition'])
        self.assertIn('cannot transition', result['reason'].lower())


class WorkOrderPricingServiceTests(WorkOrderTestBase):
    """Tests for WorkOrderPricingService"""
    
    def test_calculate_work_order_estimate(self):
        """Test calculation of work order cost estimate"""
        result = WorkOrderPricingService.calculate_work_order_estimate(self.work_order)
        
        # Expected costs
        # Labor: (30 + 15) minutes * 5 EGP/minute = 225 EGP
        # Parts: (2 * 25) + (1 * 15) = 65 EGP
        # Total: 290 EGP
        
        self.assertEqual(result['labor_cost'], Decimal('225.00'))
        self.assertEqual(result['parts_cost'], Decimal('65.00'))
        self.assertEqual(result['total_cost'], Decimal('290.00'))
        
        # Check breakdown
        self.assertEqual(len(result['breakdown']['operations']), 2)
        self.assertEqual(len(result['breakdown']['materials']), 2)


class WorkOrderAPITests(WorkOrderTestBase):
    """Tests for Work Order API endpoints"""
    
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.force_login(self.user)
    
    def test_api_add_material_to_work_order(self):
        """Test adding material to work order via API"""
        url = reverse('work_orders:api_add_material_to_work_order')
        data = {
            'work_order_id': str(self.work_order.id),
            'item_id': str(self.item1.id),
            'quantity': 3,
            'unit_of_measure': 'pcs',
            'notes': 'Test material',
            'check_availability': True
        }
        
        response = self.client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        
        self.assertTrue(response_data['success'])
        self.assertIn('material_id', response_data)
        self.assertIn('availability', response_data)
        
        # Check that material was created/updated
        materials = WorkOrderMaterial.objects.filter(
            work_order=self.work_order,
            item=self.item1
        )
        self.assertEqual(materials.count(), 1)
        # Should have 2 (existing) + 3 (new) = 5 total
        self.assertEqual(materials.first().quantity, 5)
