from django.urls import path
from django.http import JsonResponse
from . import views
from .api_spare_parts import api_get_spare_parts
from . import api_enhanced

app_name = 'work_orders'

urlpatterns = [
    # Work Order CRUD
    path('', views.WorkOrderListView.as_view(), name='work_order_list'),
    path('create/', views.WorkOrderCreateView.as_view(), name='work_order_create'),
    path('<uuid:pk>/', views.WorkOrderDetailView.as_view(), name='work_order_detail'),
    path('<uuid:pk>/update/', views.WorkOrderUpdateView.as_view(), name='work_order_update'),
    
    # Work Order Materials (existing view)
    path('<uuid:work_order_id>/materials/add/', views.WorkOrderMaterialCreateView.as_view(), name='add_material'),
    
    # Maintenance Schedules (existing views)
    path('<uuid:work_order_id>/select-schedule/', views.MaintenanceScheduleSelectionView.as_view(), name='select_maintenance_schedule'),
    path('<uuid:work_order_id>/apply-schedule/<uuid:schedule_id>/', views.apply_maintenance_schedule, name='apply_maintenance_schedule'),
    
    # API endpoints for status management
    path('api/technicians/', views.api_get_technicians, name='api_technicians'),
    path('<uuid:work_order_id>/change-status/', views.change_work_order_status, name='change_work_order_status'),
    path('<uuid:work_order_id>/assign-technician/', views.assign_technician, name='assign_technician'),
    
    # Test endpoints
    path('test-create/', views.test_create_work_order, name='test_create_work_order'),
    
    # API endpoints for work order form
    path('api/filter-items/', views.api_filter_items, name='api_filter_items'),
    path('api/search-customers/', views.api_search_customers, name='api_search_customers'),
    path('api/search-vehicles/', views.api_search_vehicles, name='api_search_vehicles'),
    path('api/create-customer/', views.api_create_customer, name='api_create_customer'),
    path('api/create-vehicle/', views.api_create_vehicle, name='api_create_vehicle'),
    path('api/vehicle-makes/', views.api_get_vehicle_makes, name='api_vehicle_makes'),
    path('api/vehicle-models/', views.api_get_vehicle_models, name='api_vehicle_models'),
    path('api/maintenance-schedules/', views.api_get_maintenance_schedules, name='api_maintenance_schedules'),
    path('api/operations-for-vehicle/', views.api_get_operations_for_vehicle, name='api_operations_for_vehicle'),
    path('api/operation-descriptions/', views.api_get_operation_descriptions, name='api_operation_descriptions'),
    
    # Work order detail and operation management API endpoints
    path('api/get-work-order-history/<uuid:pk>/', views.api_get_work_order_history, name='api_get_work_order_history'),
    path('api/get-vehicle-history/<uuid:vehicle_id>/', views.api_get_vehicle_history, name='api_get_vehicle_history'),
    path('api/create-operation/', views.api_create_operation, name='api_create_operation'),
    path('api/get-existing-operations/', views.api_get_existing_operations, name='api_get_existing_operations'),
    path('api/request-parts/', views.api_request_parts, name='api_request_parts'),
    path('api/consume-material/<uuid:material_id>/', views.api_consume_material, name='api_consume_material'),
    path('api/spare-parts-for-operation/', views.api_spare_parts_for_operation, name='api_spare_parts_for_operation'),
    
    # Auto-completion endpoints
    path('api/auto-complete-work-orders/', views.api_auto_complete_work_orders, name='api_auto_complete_work_orders'),
    path('api/check-completion-status/<uuid:work_order_id>/', views.api_check_work_order_completion_status, name='api_check_completion_status'),
    
    # API endpoints matching the template's concatenated URL structure
    path('api/root/operation/<uuid:operation_id>/complete/', views.api_complete_operation, name='api_complete_operation_via_root'),
    path('api/root/material/<uuid:material_id>/consume/', views.api_consume_material, name='api_consume_material_via_root'),
    path('api/operation/<uuid:operation_id>/complete/', views.api_complete_operation, name='api_complete_operation_alt'),
    path('api/material/<uuid:material_id>/consume/', views.api_consume_material, name='api_consume_material_alt'),
    
    # API root endpoint (empty response for template compatibility)
    path('api/root/', views.api_root_handler, name='api_root'),
    
    # Missing API endpoints
    path('api/request-part-transfer/', views.api_request_part_transfer, name='api_request_part_transfer'),
    path('api/add-material/', views.api_add_material_to_work_order, name='api_add_material_to_work_order'),
    path('api/complete-operation/<uuid:operation_id>/', views.api_complete_operation, name='api_complete_operation'),
    path('api/spare-parts/', views.api_spare_parts_for_operation, name='api_spare_parts_for_operation'),
    path('api/spare-parts-enhanced/', api_get_spare_parts, name='api_get_spare_parts'),
    
    # Enhanced workflow API endpoints
    path('api/smart-suggestions/', api_enhanced.api_get_smart_part_suggestions, name='api_smart_suggestions'),
    path('api/validate-stock/', api_enhanced.api_validate_stock_allocation, name='api_validate_stock'),
    path('api/alternative-parts/', api_enhanced.api_get_alternative_parts, name='api_alternative_parts'),
    path('api/real-time-stock/', api_enhanced.api_get_real_time_stock_info, name='api_real_time_stock'),
    path('api/simulate-allocation/', api_enhanced.api_simulate_work_order_allocation, name='api_simulate_allocation'),
    path('api/work-order-progress/<uuid:work_order_id>/', api_enhanced.api_get_work_order_progress, name='api_work_order_progress'),
    
    # Materials and Operations CRUD API endpoints  
    path('api/materials/<uuid:material_id>/', views.api_get_material, name='api_get_material'),
    path('api/operations/<uuid:operation_id>/', views.api_get_operation, name='api_get_operation'),
    path('api/materials/<uuid:material_id>/update/', views.api_update_material, name='api_update_material'),
    path('api/operations/<uuid:operation_id>/update/', views.api_update_operation, name='api_update_operation'),
    path('api/materials/<uuid:material_id>/delete/', views.api_delete_material, name='api_delete_material'),
    path('api/operations/<uuid:operation_id>/delete/', views.api_delete_operation, name='api_delete_operation'),
    path('api/materials/<uuid:material_id>/consume/', views.api_consume_material, name='api_consume_material_specific'),
] 