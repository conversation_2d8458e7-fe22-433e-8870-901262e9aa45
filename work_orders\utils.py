from django.utils import timezone
from decimal import Decimal
from django.db import transaction


def check_work_order_completion(work_order):
    """
    Check if a work order should be automatically completed based on operations and materials status.
    
    Args:
        work_order: WorkOrder instance
        
    Returns:
        dict: {
            'can_complete': bool,
            'operations_completed': bool,
            'materials_consumed': bool,
            'missing_operations': list,
            'missing_materials': list
        }
    """
    # Check if work order is in a valid status to be completed
    if work_order.status not in ['in_progress']:
        return {
            'can_complete': False,
            'reason': f'Work order status is {work_order.status}, expected in_progress',
            'operations_completed': False,
            'materials_consumed': False,
            'missing_operations': [],
            'missing_materials': []
        }
    
    # Check operations completion
    all_operations = work_order.operations.all()
    completed_operations = all_operations.filter(is_completed=True)
    missing_operations = list(all_operations.filter(is_completed=False).values_list('name', flat=True))
    
    operations_completed = len(missing_operations) == 0 if all_operations.exists() else True
    
    # Check materials consumption
    all_materials = work_order.materials.all()
    consumed_materials = all_materials.filter(is_consumed=True)
    missing_materials = list(all_materials.filter(is_consumed=False).values_list('item__name', flat=True))
    
    materials_consumed = len(missing_materials) == 0 if all_materials.exists() else True
    
    # Work order can be completed if all operations are done AND all materials are consumed
    can_complete = operations_completed and materials_consumed
    
    return {
        'can_complete': can_complete,
        'operations_completed': operations_completed,
        'materials_consumed': materials_consumed,
        'missing_operations': missing_operations,
        'missing_materials': missing_materials,
        'total_operations': all_operations.count(),
        'completed_operations_count': completed_operations.count(),
        'total_materials': all_materials.count(),
        'consumed_materials_count': consumed_materials.count()
    }


def auto_complete_work_order(work_order, user=None):
    """
    Automatically complete a work order and generate invoice and sales order if conditions are met.
    
    Args:
        work_order: WorkOrder instance
        user: User who triggered the completion (optional)
        
    Returns:
        dict: {
            'completed': bool,
            'invoice_created': bool,
            'sales_order_created': bool,
            'invoice': Invoice instance or None,
            'sales_order': SalesOrder instance or None,
            'message': str
        }
    """
    from work_orders.models import WorkOrderHistory
    from billing.models import Invoice, InvoiceItem
    from sales.models import SalesOrder, SalesOrderItem
    from decimal import Decimal
    
    # Check if work order can be completed
    completion_check = check_work_order_completion(work_order)
    
    if not completion_check['can_complete']:
        return {
            'completed': False,
            'invoice_created': False,
            'sales_order_created': False,
            'invoice': None,
            'sales_order': None,
            'message': f"Cannot complete work order: {completion_check.get('reason', 'Requirements not met')}",
            'details': completion_check
        }
    
    try:
        with transaction.atomic():
            # Update work order status
            old_status = work_order.status
            work_order.status = 'completed'
            work_order.actual_end_date = timezone.now()
            
            # Calculate actual cost from operations and materials
            total_operations_cost = Decimal('0.00')
            total_materials_cost = Decimal('0.00')
            
            # Calculate operations cost (duration * hourly rate)
            for operation in work_order.operations.all():
                # Assume 5 EGP per minute as default rate
                operation_cost = Decimal(str(operation.duration_minutes)) * Decimal('5.00')
                total_operations_cost += operation_cost
            
            # Calculate materials cost
            for material in work_order.materials.all():
                if material.item.unit_price:
                    material_cost = material.quantity * material.item.unit_price
                    total_materials_cost += material_cost
            
            work_order.actual_cost = total_operations_cost + total_materials_cost
            work_order.save()
            
            # Create history entry
            WorkOrderHistory.objects.create(
                tenant_id=work_order.tenant_id,
                work_order=work_order,
                action_type='status_change',
                description='Work order automatically completed - all operations and materials finished',
                previous_value=old_status,
                new_value='completed',
                user=user,
                notes='Auto-completed when all operations and materials were consumed'
            )
            
            # Generate invoice
            invoice_result = generate_invoice_for_work_order(work_order)
            
            # Generate sales order
            sales_order_result = generate_sales_order_for_work_order(work_order)
            
            return {
                'completed': True,
                'invoice_created': invoice_result['created'],
                'sales_order_created': sales_order_result['created'],
                'invoice': invoice_result.get('invoice'),
                'sales_order': sales_order_result.get('sales_order'),
                'message': f'Work order completed successfully. Invoice {"created" if invoice_result["created"] else "already exists"}. Sales order {"created" if sales_order_result["created"] else "already exists"}.',
                'details': completion_check
            }
            
    except Exception as e:
        return {
            'completed': False,
            'invoice_created': False,
            'sales_order_created': False,
            'invoice': None,
            'sales_order': None,
            'message': f'Error completing work order: {str(e)}',
            'details': completion_check
        }


def generate_sales_order_for_work_order(work_order):
    """
    Generate a sales order for a completed work order with all operations and materials.
    
    Args:
        work_order: WorkOrder instance
        
    Returns:
        dict: {
            'created': bool,
            'sales_order': SalesOrder instance or None,
            'message': str
        }
    """
    from sales.models import SalesOrder, SalesOrderItem
    from inventory.models import Item
    
    # Check if sales order already exists for this work order
    try:
        # First check by direct work order reference
        existing_sales_order = SalesOrder.objects.filter(
            tenant_id=work_order.tenant_id,
            work_order=work_order
        ).first()
        
        # Fallback: check by work order number in notes
        if not existing_sales_order:
            existing_sales_order = SalesOrder.objects.filter(
                tenant_id=work_order.tenant_id,
                work_order_number=work_order.work_order_number
            ).first()
        
        if existing_sales_order:
            return {
                'created': False,
                'sales_order': existing_sales_order,
                'message': f'Sales order {existing_sales_order.order_number} already exists for this work order'
            }
    except Exception as e:
        pass
    
    # Ensure work order has required data
    if not work_order.customer:
        return {
            'created': False,
            'sales_order': None,
            'message': 'Cannot create sales order: Work order must have a customer'
        }
    
    try:
        with transaction.atomic():
            # Generate sales order number
            last_order = SalesOrder.objects.filter(
                tenant_id=work_order.tenant_id
            ).order_by('-created_at').first()
            
            if last_order and last_order.order_number:
                try:
                    last_number = int(last_order.order_number.split('-')[-1])
                    order_number = f"SO-{last_number + 1:06d}"
                except (ValueError, IndexError):
                    order_number = f"SO-{timezone.now().strftime('%Y%m%d')}001"
            else:
                order_number = f"SO-{timezone.now().strftime('%Y%m%d')}001"
            
            # Determine service type based on work order
            service_type = 'custom'
            if work_order.operation_category == 'scheduled':
                service_type = 'maintenance'
            elif work_order.work_order_type:
                if 'maintenance' in work_order.work_order_type.name.lower():
                    service_type = 'maintenance'
                elif 'repair' in work_order.work_order_type.name.lower():
                    service_type = 'repair'
                elif 'inspection' in work_order.work_order_type.name.lower():
                    service_type = 'inspection'
            
            # Create sales order with complete work order data
            sales_order = SalesOrder.objects.create(
                tenant_id=work_order.tenant_id,
                order_number=order_number,
                customer=work_order.customer,
                order_date=timezone.now().date(),
                status='delivered',  # Mark as delivered since work is completed
                shipping_address=getattr(work_order.customer, 'address', '') or '',
                notes=f'Auto-generated from completed Work Order: {work_order.work_order_number}. '
                      f'Service performed at {work_order.service_center.name if work_order.service_center else "Service Center"}. '
                      f'Vehicle: {work_order.vehicle.license_plate if work_order.vehicle else "N/A"}. '
                      f'Description: {work_order.description}',
                total_amount=Decimal('0.00'),  # Will be calculated after adding items
                
                # Work Order Integration Fields
                work_order=work_order,
                work_order_number=work_order.work_order_number,
                service_center=work_order.service_center,
                vehicle=work_order.vehicle,
                service_type=service_type,
                service_completion_date=work_order.actual_end_date or timezone.now(),
                technician=work_order.assigned_technician,
                vehicle_odometer=work_order.current_odometer,
                vehicle_condition_notes=work_order.notes,
                labor_cost=Decimal('0.00'),  # Will be calculated after adding items
                parts_cost=Decimal('0.00')   # Will be calculated after adding items
            )
            
            # Add operation items as services to sales order
            for operation in work_order.operations.all():
                if operation.is_completed:
                    # Create a labor service item for the operation
                    operation_cost = Decimal(str(operation.duration_minutes)) * Decimal('5.00')  # 5 EGP per minute
                    
                    # Try to find or create a service item for labor
                    labor_item, created = Item.objects.get_or_create(
                        tenant_id=work_order.tenant_id,
                        sku=f'LABOR-{operation.name.upper().replace(" ", "-")}',
                        defaults={
                            'name': f'Labor: {operation.name}',
                            'description': operation.description or f'Labor service for {operation.name}',
                            'category': 'service',
                            'unit_price': Decimal('5.00'),  # 5 EGP per minute
                            'unit_of_measurement_id': None,
                            'quantity': Decimal('0.00'),  # Service items don't have stock
                            'min_stock_level': Decimal('0.00'),
                            'max_stock_level': Decimal('0.00'),
                            'is_active': True
                        }
                    )
                    
                    SalesOrderItem.objects.create(
                        tenant_id=work_order.tenant_id,
                        sales_order=sales_order,
                        item=labor_item,
                        quantity=Decimal(str(operation.duration_minutes)),
                        unit_price=Decimal('5.00'),
                        discount=Decimal('0.00'),
                        item_type='labor',
                        work_order_operation_id=operation.id,
                        operation_duration=operation.duration_minutes,
                        operation_description=f'{operation.name}: {operation.description or ""}'
                    )
            
            # Add material items to sales order
            for material in work_order.materials.all():
                if material.is_consumed and material.item.unit_price:
                    SalesOrderItem.objects.create(
                        tenant_id=work_order.tenant_id,
                        sales_order=sales_order,
                        item=material.item,
                        quantity=material.quantity,
                        unit_price=material.item.unit_price,
                        discount=Decimal('0.00'),
                        item_type='parts',
                        work_order_material_id=material.id,
                        operation_description=f'Material: {material.item.name} - {material.notes or ""}'
                    )
            
            # Update total amount
            sales_order.update_total_amount()
            
            return {
                'created': True,
                'sales_order': sales_order,
                'message': f'Sales order {sales_order.order_number} created successfully'
            }
            
    except Exception as e:
        return {
            'created': False,
            'sales_order': None,
            'message': f'Error creating sales order: {str(e)}'
        }


def generate_invoice_for_work_order(work_order):
    """
    Generate an invoice for a completed work order.
    
    Args:
        work_order: WorkOrder instance
        
    Returns:
        dict: {
            'created': bool,
            'invoice': Invoice instance or None,
            'message': str
        }
    """
    from billing.models import Invoice, InvoiceItem
    from datetime import timedelta
    
    # Check if invoice already exists
    try:
        existing_invoice = Invoice.objects.get(work_order=work_order)
        return {
            'created': False,
            'invoice': existing_invoice,
            'message': f'Invoice {existing_invoice.invoice_number} already exists for this work order'
        }
    except Invoice.DoesNotExist:
        pass
    
    # Ensure work order has required data
    if not work_order.customer:
        return {
            'created': False,
            'invoice': None,
            'message': 'Cannot create invoice: Work order must have a customer'
        }
    
    if not work_order.service_center:
        return {
            'created': False,
            'invoice': None,
            'message': 'Cannot create invoice: Work order must have a service center'
        }
    
    try:
        with transaction.atomic():
            # Create invoice
            invoice = Invoice.objects.create(
                tenant_id=work_order.tenant_id,
                work_order=work_order,
                customer=work_order.customer,
                service_center=work_order.service_center,
                invoice_date=timezone.now().date(),
                due_date=timezone.now().date() + timedelta(days=30),
                status='draft',
                tax_method='exclusive',
                tax_percentage=Decimal('14.00'),  # Egyptian VAT
                notes=f'Auto-generated invoice for work order {work_order.work_order_number}'
            )
            
            # Add operation items to invoice
            for operation in work_order.operations.all():
                operation_cost = Decimal(str(operation.duration_minutes)) * Decimal('5.00')  # 5 EGP per minute
                
                InvoiceItem.objects.create(
                    tenant_id=work_order.tenant_id,
                    invoice=invoice,
                    item_type='labor',
                    description=f'{operation.name} - {operation.description}',
                    quantity=Decimal(str(operation.duration_minutes)),
                    unit_price=Decimal('5.00'),
                    work_order_operation_id=operation.id,
                    tax_percentage=invoice.tax_percentage
                )
            
            # Add material items to invoice
            for material in work_order.materials.all():
                if material.item.unit_price:
                    InvoiceItem.objects.create(
                        tenant_id=work_order.tenant_id,
                        invoice=invoice,
                        item_type='part',
                        description=f'{material.item.name} - {material.item.description or ""}',
                        quantity=material.quantity,
                        unit_price=material.item.unit_price,
                        part_id=material.item.sku or str(material.item.id),
                        work_order_material_id=material.id,
                        tax_percentage=invoice.tax_percentage
                    )
            
            # Calculate totals
            invoice.calculate_totals()
            invoice.status = 'issued'  # Change to issued after calculation
            invoice.save()
            
            return {
                'created': True,
                'invoice': invoice,
                'message': f'Invoice {invoice.invoice_number} created successfully'
            }
            
    except Exception as e:
        return {
            'created': False,
            'invoice': None,
            'message': f'Error creating invoice: {str(e)}'
        }


def check_and_auto_complete_work_orders(tenant_id=None):
    """
    Check all in-progress work orders and auto-complete those that are ready.
    
    Args:
        tenant_id: Specific tenant ID to check (optional)
        
    Returns:
        dict: Summary of completed work orders and created invoices/sales orders
    """
    from work_orders.models import WorkOrder
    
    # Get all in-progress work orders
    queryset = WorkOrder.objects.filter(status='in_progress')
    if tenant_id:
        queryset = queryset.filter(tenant_id=tenant_id)
    
    results = {
        'checked': 0,
        'completed': 0,
        'invoices_created': 0,
        'sales_orders_created': 0,
        'errors': [],
        'details': []
    }
    
    for work_order in queryset:
        results['checked'] += 1
        
        completion_result = auto_complete_work_order(work_order)
        
        if completion_result['completed']:
            results['completed'] += 1
            
            if completion_result['invoice_created']:
                results['invoices_created'] += 1
                
            if completion_result['sales_order_created']:
                results['sales_orders_created'] += 1
        
        results['details'].append({
            'work_order_number': work_order.work_order_number,
            'completed': completion_result['completed'],
            'invoice_created': completion_result['invoice_created'],
            'sales_order_created': completion_result['sales_order_created'],
            'message': completion_result['message']
        })
        
        if not completion_result['completed'] and 'Error' in completion_result['message']:
            results['errors'].append({
                'work_order_number': work_order.work_order_number,
                'error': completion_result['message']
            })
    
    return results 