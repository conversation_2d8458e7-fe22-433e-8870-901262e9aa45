from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, FormView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from django.utils import timezone
from django.core.exceptions import ValidationError
import json
from django import forms
from django.utils.http import urlencode
import uuid
from django.views.decorators.http import require_POST, require_GET
from django.contrib.auth.models import User
from django.conf import settings
import logging

# This comment is added to force a file reload.
logger = logging.getLogger(__name__)

from .models import (
    WorkOrder, WorkOrderType, BillOfMaterials, WorkOrderOperation, 
    WorkOrderMaterial, MaintenanceSchedule, ScheduleOperation, WorkOrderHistory
)
from .forms import WorkOrderStatusChangeForm
from inventory.models import Item
from setup.models import ServiceCenter, Vehicle, Customer, Franchise, Company, VehicleMake, VehicleModel
from user_roles.models import UserRole, Role
from core.middleware import get_current_tenant_id

class WorkOrderListView(LoginRequiredMixin, ListView):
    model = WorkOrder
    template_name = 'work_orders/work_order_list.html'
    context_object_name = 'work_orders'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by status if provided in GET parameters and apply entity filters"""
        queryset = super().get_queryset()

        # Filter by status if provided
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Get entity filter parameters
        service_center_id = self.request.GET.get('service_center_id')
        company_id = self.request.GET.get('company_id')
        franchise_id = self.request.GET.get('franchise_id')

        # Apply explicit entity filters first
        if service_center_id:
            queryset = queryset.filter(service_center_id=service_center_id)
        elif company_id:
            queryset = queryset.filter(service_center__company_id=company_id)
        elif franchise_id:
            queryset = queryset.filter(service_center__company__franchise_id=franchise_id)

        # If no explicit entity filters are provided, filter based on user role scope
        if not (service_center_id or company_id or franchise_id):
            # Only apply role-based filtering if not a superuser
            if not self.request.user.is_superuser and hasattr(self.request.user, 'user_roles'):
                user_roles = self.request.user.user_roles.filter(is_active=True)
                primary_role = None

                for user_role in user_roles:
                    if user_role.is_primary:
                        primary_role = user_role
                        break

                if not primary_role and user_roles.exists():
                    primary_role = user_roles.first()

                if primary_role:
                    if primary_role.service_center:
                        queryset = queryset.filter(service_center_id=primary_role.service_center.id)
                    elif primary_role.company:
                        queryset = queryset.filter(service_center__company_id=primary_role.company.id)
                    elif primary_role.franchise:
                        queryset = queryset.filter(service_center__company__franchise_id=primary_role.franchise.id)

        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Work Orders')
        context['active_status'] = self.request.GET.get('status', '')
        context['statuses'] = WorkOrder.STATUS_CHOICES

        # Get entity filter parameters from GET
        franchise_id_param = self.request.GET.get('franchise_id')
        company_id_param = self.request.GET.get('company_id')
        service_center_id_param = self.request.GET.get('service_center_id')

        # Start with all active entities, ordered by name
        all_franchises = Franchise.objects.filter(is_active=True).order_by('name')
        all_companies = Company.objects.filter(is_active=True).order_by('name')
        all_service_centers = ServiceCenter.objects.filter(is_active=True).order_by('name')

        # Apply user role scope if no explicit filters are set
        # This determines the initial scope of entities the user is allowed to see
        user_scoped_franchises = all_franchises
        user_scoped_companies = all_companies
        user_scoped_service_centers = all_service_centers

        user = self.request.user
        if not user.is_superuser and hasattr(user, 'user_roles'):
            user_roles = user.user_roles.filter(is_active=True)
            primary_role = None

            for user_role in user_roles:
                if user_role.is_primary:
                    primary_role = user_role
                    break

            if not primary_role and user_roles.exists():
                primary_role = user_roles.first()

            if primary_role:
                if primary_role.service_center:
                    user_scoped_service_centers = all_service_centers.filter(id=primary_role.service_center.id)
                    user_scoped_companies = all_companies.filter(id=primary_role.service_center.company_id)
                    if primary_role.service_center.company and primary_role.service_center.company.franchise:
                        user_scoped_franchises = all_franchises.filter(id=primary_role.service_center.company.franchise_id)
                    else:
                        user_scoped_franchises = Franchise.objects.none()
                elif primary_role.company:
                    user_scoped_companies = all_companies.filter(id=primary_role.company.id)
                    user_scoped_service_centers = all_service_centers.filter(company_id=primary_role.company.id)
                    if primary_role.company.franchise:
                        user_scoped_franchises = all_franchises.filter(id=primary_role.company.franchise_id)
                    else:
                        user_scoped_franchises = Franchise.objects.none()
                elif primary_role.franchise:
                    user_scoped_franchises = all_franchises.filter(id=primary_role.franchise.id)
                    user_scoped_companies = all_companies.filter(franchise_id=primary_role.franchise.id)
                    user_scoped_service_centers = all_service_centers.filter(company__franchise_id=primary_role.franchise.id)

        # Now, apply explicit GET filters on top of the user scoped entities
        # This determines the lists shown in the dropdowns
        filtered_franchises = user_scoped_franchises
        filtered_companies = user_scoped_companies
        filtered_service_centers = user_scoped_service_centers

        selected_franchise = None
        if franchise_id_param:
            try:
                # Get the selected franchise, ensuring it's within the user's scope
                selected_franchise = user_scoped_franchises.get(id=franchise_id_param)
                # Filter dependent lists based on the selected franchise
                filtered_companies = user_scoped_companies.filter(franchise=selected_franchise)
                filtered_service_centers = user_scoped_service_centers.filter(company__franchise=selected_franchise)
            except (Franchise.DoesNotExist, ValueError):
                # If the selected franchise is invalid or outside user scope, clear dependent lists
                filtered_companies = Company.objects.none()
                filtered_service_centers = ServiceCenter.objects.none()
                selected_franchise = None

        selected_company = None
        # Only apply company filter if a valid franchise was selected (or if no franchise filter was attempted)
        if company_id_param and (selected_franchise is not None or not franchise_id_param):
             try:
                 # Get the selected company, ensuring it's within the current filtered companies list
                 selected_company = filtered_companies.get(id=company_id_param)
                 # Filter dependent service centers based on the selected company
                 filtered_service_centers = filtered_service_centers.filter(company=selected_company)
             except (Company.DoesNotExist, ValueError):
                 # If the selected company is invalid or outside the current scope, clear dependent list
                 filtered_service_centers = ServiceCenter.objects.none()
                 selected_company = None
        elif selected_franchise is None and franchise_id_param: # Invalid franchise selected, no companies shown
             filtered_companies = Company.objects.none()
             filtered_service_centers = ServiceCenter.objects.none()

        selected_service_center = None
        # Only apply service center filter if a valid company was selected (or if no company filter was attempted)
        if service_center_id_param and (selected_company is not None or not company_id_param):
            try:
                # Get the selected service center, ensuring it's within the current filtered service centers list
                selected_service_center = filtered_service_centers.get(id=service_center_id_param)
                # No further lists to filter
            except (ServiceCenter.DoesNotExist, ValueError):
                selected_service_center = None
        elif selected_company is None and company_id_param: # Invalid company selected, no service centers shown
             filtered_service_centers = ServiceCenter.objects.none()

        # Add the filtered entity lists and selected entities to the context
        context['franchises'] = filtered_franchises
        context['companies'] = filtered_companies
        context['service_centers'] = filtered_service_centers
        context['selected_franchise'] = selected_franchise
        context['selected_company'] = selected_company
        context['selected_service_center'] = selected_service_center

        # --- Debugging: Print filtered entity lists ---
        print(f"DEBUG: WorkOrderListView - Franchises count: {filtered_franchises.count()}")
        print(f"DEBUG: WorkOrderListView - Companies count: {filtered_companies.count()}")
        print(f"DEBUG: WorkOrderListView - Service Centers count: {filtered_service_centers.count()}")
        
        # Print scope information
        print(f"DEBUG: User is superuser: {self.request.user.is_superuser}")
        if not self.request.user.is_superuser and hasattr(self.request.user, 'user_roles'):
            primary_role = None
            user_roles = self.request.user.user_roles.filter(is_active=True)
            for user_role in user_roles:
                if user_role.is_primary:
                    primary_role = user_role
                    break
            if not primary_role and user_roles.exists():
                primary_role = user_roles.first()
                
            if primary_role:
                print(f"DEBUG: Primary role - franchise: {primary_role.franchise}")
                print(f"DEBUG: Primary role - company: {primary_role.company}")
                print(f"DEBUG: Primary role - service center: {primary_role.service_center}")
                
        if selected_franchise:
             print(f"DEBUG: WorkOrderListView - Selected Franchise: {selected_franchise.name} ({selected_franchise.id})")
        if selected_company:
             print(f"DEBUG: WorkOrderListView - Selected Company: {selected_company.name} ({selected_company.id})")
        if selected_service_center:
             print(f"DEBUG: WorkOrderListView - Selected Service Center: {selected_service_center.name} ({selected_service_center.id})")
        # --- End Debugging ---

        # Filter the base queryset for status counts based on the effective filters
        base_queryset = super().get_queryset() # Start with the queryset filtered by get_queryset (which handles status and basic entity filter)

        # The get_queryset method should now correctly filter the main list of work orders
        # based on the effective entity filters (GET or role-based).
        # So, the base_queryset here should already be filtered correctly.

        # Count work orders by status with applied entity filters (which are now reflected in base_queryset)
        status_counts = {}
        for status_tuple in WorkOrder.STATUS_CHOICES:
            status_code = status_tuple[0]
            status_counts[status_code] = base_queryset.filter(status=status_code).count()

        context['status_counts'] = status_counts

        # Preserve query parameters for pagination and other links
        # Make sure to preserve the effective entity filter IDs for pagination links
        preserved_params = {k: v for k, v in self.request.GET.items() 
                            if k not in ['franchise_id', 'company_id', 'service_center_id']}
        if selected_franchise:
             preserved_params['franchise_id'] = selected_franchise.id
        if selected_company:
             preserved_params['company_id'] = selected_company.id
        if selected_service_center:
             preserved_params['service_center_id'] = selected_service_center.id

        context['preserved_query'] = urlencode(preserved_params)

        return context

class WorkOrderDetailView(LoginRequiredMixin, DetailView):
    model = WorkOrder
    template_name = 'work_orders/work_order_detail_compact.html'
    context_object_name = 'work_order'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        work_order = self.get_object()
        
        # Get operations and materials
        context['operations'] = work_order.operations.all()
        context['materials'] = work_order.materials.all()
        
        # Get history entries
        context['history_entries'] = work_order.history_entries.all().order_by('-created_at')[:10]
        context['history'] = context['history_entries']  # Alias for template
        context['notes'] = work_order.history_entries.filter(action_type='comment').order_by('-created_at')
        
        # Create status form with choices and help text
        status_form = WorkOrderStatusChangeForm(initial={'status': work_order.status})
        
        # Prepare status choices with help text
        status_choices_with_help = []
        for value, label in WorkOrder.STATUS_CHOICES:
            help_text = ""
            if value == 'draft':
                help_text = _("Draft work orders are not yet ready to be worked on.")
            elif value == 'planned':
                help_text = _("Planned work orders are scheduled but not started.")
            elif value == 'in_progress':
                help_text = _("Work has started on this order.")
            elif value == 'on_hold':
                help_text = _("This work order is temporarily paused.")
            elif value == 'completed':
                help_text = _("All work has been finished on this order.")
            elif value == 'cancelled':
                help_text = _("This work order has been cancelled and will not be completed.")
                
            status_choices_with_help.append((value, {'label': label, 'help': help_text}))
            
        status_form.status_choices_with_help = status_choices_with_help
        context['status_form'] = status_form
        
        # Add items and warehouses for the modals
        from inventory.models import Item
        try:
            from warehouse.models import Warehouse
            context['available_warehouses'] = Warehouse.objects.filter(is_active=True).order_by('name')
        except ImportError:
            context['available_warehouses'] = []
        
        # Get available items for material selection
        context['available_items'] = Item.objects.all().order_by('name')
        
        # Set page title
        context['title'] = _('Work Order') + f": {work_order.work_order_number}"
        
        return context

class WorkOrderCreateForm(forms.ModelForm):
    """Custom form for work order creation with operation type selection"""
    operation_category = forms.ChoiceField(
        choices=WorkOrder.WORK_ORDER_TYPES,
        widget=forms.RadioSelect(),
        initial='custom',
        label=_("Operation Type")
    )
    
    class Meta:
        model = WorkOrder
        fields = [
            'work_order_number', 'bill_of_materials', 
            'description', 'priority', 'status', 'operation_category',
            'planned_start_date', 'planned_end_date', 
            'customer', 'customer_name', 'customer_phone', 'customer_email',
            'service_center', 'vehicle', 'current_odometer', 'fuel_level', 
            'service_item_serial', 'warranty_status', 'estimated_cost', 'notes'
        ]
        widgets = {
            'planned_start_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'planned_end_date': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        self.fields['operation_category'].help_text = _("Choose 'Custom' for regular operations or 'Scheduled Maintenance' for predefined maintenance packages")
        
        # Make work_order_number required but with a default value 
        self.fields['work_order_number'].required = True
        if not self.initial.get('work_order_number'):
            prefix = "WO"
            timestamp = timezone.now().strftime('%Y%m%d%H%M')
            self.initial['work_order_number'] = f"{prefix}-{timestamp}"
            
        # Help text for customer selection
        self.fields['customer'].help_text = _("Select a customer or enter details below. If a vehicle with owner is selected, customer will be auto-assigned.")
        self.fields['vehicle'].help_text = _("Vehicles with active work orders are not available for selection.")
        
        # Filter out vehicles with active work orders
        active_vehicles = Vehicle.objects.filter(
            work_orders__status__in=['draft', 'planned', 'in_progress', 'on_hold']
        ).distinct().values_list('id', flat=True)
        
        self.fields['vehicle'].queryset = Vehicle.objects.exclude(id__in=active_vehicles)
    
    def clean(self):
        """Validate that the vehicle doesn't have active work orders"""
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        
        if vehicle:
            if vehicle.has_active_work_orders():
                raise ValidationError({
                    'vehicle': _("This vehicle already has an active work order. Please complete or cancel it before creating a new one.")
                })
        
        return cleaned_data

class CustomerSearchForm(forms.Form):
    """Form for searching customers"""
    search = forms.CharField(
        max_length=100, 
        required=False,
        widget=forms.TextInput(attrs={'placeholder': _('Search by name, ID, phone...')})
    )

class WorkOrderCreateView(LoginRequiredMixin, CreateView):
    model = WorkOrder
    template_name = 'work_orders/work_order_form.html'
    form_class = WorkOrderCreateForm
    
    def get_success_url(self):
        return reverse('work_orders:work_order_list')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the user to the form
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        
        # Filter service centers based on user role
        if not self.request.user.is_superuser and hasattr(self.request.user, 'user_roles'):
            user_roles = self.request.user.user_roles.filter(is_active=True)
            primary_role = None
            
            # Find primary role
            for user_role in user_roles:
                if user_role.is_primary:
                    primary_role = user_role
                    break
                    
            # If no primary role is found, use the first active role
            if not primary_role and user_roles.exists():
                primary_role = user_roles.first()
            
            # Filter service centers based on role scope
            if primary_role:
                if primary_role.service_center:
                    # Service center role only sees their service center
                    form.fields['service_center'].queryset = ServiceCenter.objects.filter(id=primary_role.service_center.id)
                    # Pre-select the service center if there's only one
                    if form.fields['service_center'].queryset.count() == 1:
                        form.fields['service_center'].initial = form.fields['service_center'].queryset.first()
                elif primary_role.company:
                    # Company role only sees service centers in their company
                    form.fields['service_center'].queryset = ServiceCenter.objects.filter(company_id=primary_role.company.id)
                elif primary_role.franchise:
                    # Franchise role only sees service centers in their franchise
                    form.fields['service_center'].queryset = ServiceCenter.objects.filter(company__franchise_id=primary_role.franchise.id)
        
        return form
    
    def get_context_data(self, **kwargs):
        # Ensure self.object is set for CreateView when form is invalid
        if not hasattr(self, 'object'):
            self.object = None
            
        context = super().get_context_data(**kwargs)
        context['title'] = _('Create Work Order')
        context['customer_search_form'] = CustomerSearchForm()
        
        # Clear session data related to work order form
        self.clear_work_order_form_session()
        
        return context
    
    def clear_work_order_form_session(self):
        """Clear session data related to the work order form"""
        session_keys = [
            'work_order_form_data',
            'selected_customer',
            'selected_vehicle',
            'selected_operations',
            'selected_parts',
            'current_step',
            'vehicle_photo_data'
        ]
        
        for key in session_keys:
            if key in self.request.session:
                del self.request.session[key]
                
        # Save the session
        self.request.session.modified = True
    
    def form_valid(self, form):
        work_order = form.save(commit=False)
        
        # Set tenant ID if using multi-tenancy
        if hasattr(self.request, 'tenant_id'):
            work_order.tenant_id = self.request.tenant_id
            
        work_order.save()
        
        # Auto-assign customer from vehicle if not set
        if not work_order.customer and work_order.vehicle and work_order.vehicle.owner:
            work_order.customer = work_order.vehicle.owner
            work_order.save()
        
        # Clear any session data related to the work order form
        self.clear_work_order_form_session()
        
        # Add a message to inform the user to clear client-side storage
        messages.success(self.request, _('Work order created successfully. Page data has been cleared.'))
        
        # If it's a scheduled maintenance work order, redirect to schedule selection
        if work_order.operation_category == 'scheduled':
            return redirect('work_orders:select_maintenance_schedule', work_order_id=work_order.id)
            
        return super().form_valid(form)

    def post(self, request, *args, **kwargs):
        try:
            # Debug logging to see what's being submitted
            print("==================== WORK ORDER FORM SUBMISSION ====================")
            print("POST data keys:", request.POST.keys())
            print("Customer ID:", request.POST.get('selected_customer_id'))
            print("Vehicle ID:", request.POST.get('selected_vehicle_id'))
            print("Service Description:", request.POST.get('service_request_description'))
            print("Operation Descriptions:", request.POST.getlist('operation_description[]'))
            print("Part IDs:", request.POST.getlist('part_id[]'))
            print("=================================================================")
            
            # Force redirect to test if we're reaching this point
            print("FORCE REDIRECT TEST - CHECKING POST PROCESSING")
            
            # Simplified approach: Just create the work order without operations or materials
            customer_id = request.POST.get('selected_customer_id')
            vehicle_id = request.POST.get('selected_vehicle_id')
            service_description = request.POST.get('service_request_description')
            
            # Get tenant_id from request if available
            tenant_id = get_current_tenant_id()
            
            # If tenant_id is None, try to get it from the first WorkOrder
            if tenant_id is None:
                # Try to find an existing work order to get tenant_id
                existing_work_order = WorkOrder.objects.first()
                if existing_work_order:
                    tenant_id = existing_work_order.tenant_id
                    print(f"Using tenant_id from existing work order: {tenant_id}")
                else:
                    # Try to get tenant_id from Customer
                    from setup.models import Customer
                    customer_obj = Customer.objects.first()
                    if customer_obj and hasattr(customer_obj, 'tenant_id'):
                        tenant_id = customer_obj.tenant_id
                        print(f"Using tenant_id from Customer: {tenant_id}")
                    else:
                        # Last resort - use a hardcoded UUID as tenant_id
                        import uuid
                        # Use the tenant_id we found from a Customer in the database
                        tenant_id = uuid.UUID('00000000-0000-0000-0000-000000000001')
                        print(f"Using hardcoded tenant_id: {tenant_id}")
            
            print(f"Final tenant_id: {tenant_id}")
            
            # Check if we have the required data
            if not customer_id or not vehicle_id or not service_description:
                print("MISSING ESSENTIAL DATA")
                form = self.get_form()
                form.add_error(None, _("Missing required data: Customer, Vehicle, and Service Description are all required."))
                return self.form_invalid(form)
            
            # Get the objects
            try:
                from setup.models import Customer, Vehicle
                customer = Customer.objects.get(id=customer_id)
                vehicle = Vehicle.objects.get(id=vehicle_id)
                print(f"Found customer: {customer}")
                print(f"Found vehicle: {vehicle}")
            except Exception as e:
                print(f"ERROR: {str(e)}")
                form = self.get_form()
                form.add_error(None, _("Customer or Vehicle not found."))
                return self.form_invalid(form)

            # Generate work order number
            prefix = "WO"
            timestamp = timezone.now().strftime('%Y%m%d%H%M')
            work_order_number = f"{prefix}-{timestamp}"
            
            # WorkOrderType is now optional
            work_order_type = None
            try:
                work_order_type = WorkOrderType.objects.first()
                print(f"Using work order type: {work_order_type}")
            except Exception as e:
                print(f"No work order type found or error: {str(e)}")
                # Continue without a work order type
            
            # Create the work order
            work_order = WorkOrder.objects.create(
                work_order_number=work_order_number,
                customer=customer,
                vehicle=vehicle,
                description=service_description,
                status='planned',
                priority='medium',
                work_order_type=work_order_type,  # This can now be None
                tenant_id=tenant_id  # Set tenant_id from request
            )
            
            print(f"SUCCESSFULLY CREATED WORK ORDER: {work_order}")
            
            # Now add operations and materials
            try:
                # Get operations data from the form
                operation_descriptions = []
                
                # First try array-style parameters
                for key in request.POST.keys():
                    if key.startswith('operation_description['):
                        operation_descriptions.append(request.POST.get(key))
                
                # If none found, try the older form style
                if not operation_descriptions:
                    operation_descriptions = request.POST.getlist('operation_description[]')
                
                # Create operations
                for i, description in enumerate(operation_descriptions):
                    if not description:
                        continue
                        
                    # Try to get estimated time and price
                    est_time_key = f'operation_estimated_time[{i}]'
                    price_key = f'operation_price[{i}]'
                    
                    estimated_time = request.POST.get(est_time_key, '')
                    price = request.POST.get(price_key, '')
                    
                    # Convert to minutes if provided (hours to minutes)
                    duration_minutes = 0
                    if estimated_time:
                        try:
                            duration_minutes = int(float(estimated_time) * 60)
                        except ValueError:
                            duration_minutes = 0
                    
                    # Create the operation
                    WorkOrderOperation.objects.create(
                        work_order=work_order,
                        name=description,
                        description=description,
                        duration_minutes=duration_minutes,
                        tenant_id=tenant_id  # Use the same tenant_id as the work order
                    )
                    print(f"Created operation: {description}")
                
                # Get materials data from the form
                part_ids = request.POST.getlist('part_id[]')
                part_quantities = request.POST.getlist('part_quantity[]')
                part_units = request.POST.getlist('part_unit[]')
                part_prices = request.POST.getlist('part_price[]')
                
                print(f"Part IDs: {part_ids}")
                print(f"Part Quantities: {part_quantities}")
                print(f"Part Units: {part_units}")
                print(f"Part Prices: {part_prices}")
                
                # Create materials
                for i, part_id in enumerate(part_ids):
                    if not part_id:
                        continue
                        
                    # Get corresponding quantity, unit and price (with safe defaults)
                    quantity = part_quantities[i] if i < len(part_quantities) else '1'
                    unit = part_units[i] if i < len(part_units) else ''
                    price = part_prices[i] if i < len(part_prices) else '0'
                    
                    # Convert quantity to decimal
                    try:
                        quantity = float(quantity)
                    except ValueError:
                        quantity = 1.0
                    
                    # Get the item
                    try:
                        from inventory.models import Item
                        item = Item.objects.get(id=part_id)
                        
                        # Create the material
                        material = WorkOrderMaterial.objects.create(
                            work_order=work_order,
                            item=item,
                            quantity=quantity,
                            unit_of_measure=unit or (item.unit_of_measurement.symbol if item.unit_of_measurement else ''),
                            tenant_id=tenant_id  # Use the same tenant_id as the work order
                        )
                        print(f"Created material: {item.name} (qty: {quantity}, unit: {unit})")
                    except Exception as e:
                        print(f"Error creating material for part_id {part_id}: {str(e)}")
                
            except Exception as e:
                print(f"Error creating operations/materials: {str(e)}")
                # Continue with the flow - at least the main Work Order was created

            messages.success(request, _('Work order created successfully.'))
            
            # Process work order with automation
            try:
                from inventory.services import WorkOrderWorkflowService
                workflow_result = WorkOrderWorkflowService.process_work_order_creation(work_order)
                
                # Add workflow messages
                for notification in workflow_result.get('notifications', []):
                    if notification['type'] == 'success':
                        messages.success(request, notification['message'])
                    elif notification['type'] == 'warning':
                        messages.warning(request, notification['message'])
                    elif notification['type'] == 'info':
                        messages.info(request, notification['message'])
                
                # Add purchase order info
                if workflow_result.get('purchase_orders'):
                    po_count = len(workflow_result['purchase_orders'])
                    messages.info(request, _(f'Created {po_count} purchase orders for missing items'))
                    
            except Exception as e:
                print(f"Error in workflow automation: {str(e)}")
                # Continue normally even if automation fails
            
            # Redirect to the work order detail page
            return redirect('work_orders:work_order_detail', pk=work_order.pk)
            
        except Exception as e:
            print("==================== GENERAL ERROR ====================")
            print(f"Error: {str(e)}")
            import traceback
            print(traceback.format_exc())
            print("======================================================")
            form = self.get_form()
            form.add_error(None, _(f"An unexpected error occurred: {str(e)}"))
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Override form_invalid to ensure self.object is set to None"""
        # Ensure self.object is set for CreateView when form is invalid
        if not hasattr(self, 'object'):
            self.object = None
        return super().form_invalid(form)

class WorkOrderUpdateView(LoginRequiredMixin, UpdateView):
    model = WorkOrder
    template_name = 'work_orders/work_order_form.html'
    fields = [
        'work_order_type', 'bill_of_materials', 'description', 
        'priority', 'status', 'planned_start_date', 'planned_end_date',
        'actual_start_date', 'actual_end_date', 
        'customer', 'customer_name', 'customer_phone', 'customer_email', 
        'service_center', 'vehicle', 'current_odometer', 'fuel_level', 
        'service_item_serial', 'warranty_status', 'estimated_cost', 
        'actual_cost', 'notes'
    ]
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        
        # Filter service centers based on user role
        if not self.request.user.is_superuser and hasattr(self.request.user, 'user_roles'):
            user_roles = self.request.user.user_roles.filter(is_active=True)
            primary_role = None
            
            # Find primary role
            for user_role in user_roles:
                if user_role.is_primary:
                    primary_role = user_role
                    break
                    
            # If no primary role is found, use the first active role
            if not primary_role and user_roles.exists():
                primary_role = user_roles.first()
            
            # Filter service centers based on role scope
            if primary_role:
                if primary_role.service_center:
                    # Service center role only sees their service center
                    form.fields['service_center'].queryset = ServiceCenter.objects.filter(id=primary_role.service_center.id)
                elif primary_role.company:
                    # Company role only sees service centers in their company
                    form.fields['service_center'].queryset = ServiceCenter.objects.filter(company_id=primary_role.company.id)
                elif primary_role.franchise:
                    # Franchise role only sees service centers in their franchise
                    form.fields['service_center'].queryset = ServiceCenter.objects.filter(company__franchise_id=primary_role.franchise.id)
        
        return form
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Update Work Order')
        context['customer_search_form'] = CustomerSearchForm()
        return context
    
    def get_success_url(self):
        return reverse_lazy('work_orders:work_order_detail', kwargs={'pk': self.object.pk})
    
    def form_valid(self, form):
        messages.success(self.request, _('Work order updated successfully'))
        return super().form_valid(form)

class MaintenanceScheduleSelectionView(LoginRequiredMixin, ListView):
    """View for selecting a maintenance schedule for a work order"""
    model = MaintenanceSchedule
    template_name = 'work_orders/maintenance_schedule_selection.html'
    context_object_name = 'schedules'
    
    def get_queryset(self):
        """Filter maintenance schedules based on vehicle make/model if available"""
        work_order = self.get_work_order()
        queryset = super().get_queryset().filter(is_active=True)
        
        # Apply role-based filtering to maintenance schedules
        if not self.request.user.is_superuser and hasattr(self.request.user, 'user_roles'):
            user_roles = self.request.user.user_roles.filter(is_active=True)
            primary_role = None
            
            # Find primary role
            for user_role in user_roles:
                if user_role.is_primary:
                    primary_role = user_role
                    break
                    
            # If no primary role is found, use the first active role
            if not primary_role and user_roles.exists():
                primary_role = user_roles.first()
            
            # Filter schedules based on role scope
            if primary_role:
                if primary_role.service_center:
                    # Service center role only sees their maintenance schedules or global ones
                    queryset = queryset.filter(
                        Q(service_center_id=primary_role.service_center.id) | 
                        Q(service_center__isnull=True)
                    )
                elif primary_role.company:
                    # Company role only sees their company's maintenance schedules or global ones
                    queryset = queryset.filter(
                        Q(service_center__company_id=primary_role.company.id) | 
                        Q(service_center__isnull=True)
                    )
                elif primary_role.franchise:
                    # Franchise role only sees their franchise's maintenance schedules or global ones
                    queryset = queryset.filter(
                        Q(service_center__company__franchise_id=primary_role.franchise.id) | 
                        Q(service_center__isnull=True)
                    )
        
        # Filter by vehicle information if available
        if work_order.vehicle:
            vehicle = work_order.vehicle
            
            # Filter by make
            queryset = queryset.filter(
                Q(vehicle_make__exact=vehicle.make) | Q(vehicle_make='')
            )
            
            # Filter by model if model is specified in the schedule
            model_filter = Q(vehicle_model__exact=vehicle.model) | Q(vehicle_model='')
            queryset = queryset.filter(model_filter)
            
            # Filter by year if vehicle year is available
            if vehicle.year:
                year_filter = (
                    (Q(year_from__lte=vehicle.year) | Q(year_from__isnull=True)) &
                    (Q(year_to__gte=vehicle.year) | Q(year_to__isnull=True))
                )
                queryset = queryset.filter(year_filter)
        
        return queryset
    
    def get_work_order(self):
        """Get the work order to attach a maintenance schedule to"""
        return get_object_or_404(WorkOrder, pk=self.kwargs['work_order_id'])
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        work_order = self.get_work_order()
        context['work_order'] = work_order
        context['title'] = _('Select Maintenance Schedule')
        
        # Get vehicle info for filtering
        if work_order.vehicle:
            context['vehicle'] = work_order.vehicle
        
        # Get odometer reading for interval recommendation
        if work_order.current_odometer and work_order.vehicle:
            # Get last service for this vehicle
            last_service = WorkOrder.objects.filter(
                vehicle=work_order.vehicle,
                status='completed',
                current_odometer__isnull=False
            ).order_by('-actual_end_date').first()
            
            if last_service:
                context['last_service'] = last_service
                context['mileage_since_last_service'] = work_order.current_odometer - last_service.current_odometer
        
        return context

def apply_maintenance_schedule(request, work_order_id, schedule_id):
    """Apply a maintenance schedule to a work order"""
    work_order = get_object_or_404(WorkOrder, pk=work_order_id)
    schedule = get_object_or_404(MaintenanceSchedule, pk=schedule_id)
    
    # Attach the schedule to the work order
    work_order.maintenance_schedule = schedule
    work_order.save()
    
    # Apply the schedule operations and parts
    work_order.apply_maintenance_schedule()
    
    messages.success(request, _('Maintenance schedule "{0}" applied successfully.').format(schedule.name))
    return redirect('work_orders:work_order_detail', pk=work_order.id)

class BillOfMaterialsListView(LoginRequiredMixin, ListView):
    model = BillOfMaterials
    template_name = 'work_orders/bom_list.html'
    context_object_name = 'bom_list'
    paginate_by = 20
    
    def get_queryset(self):
        """Apply role-based filtering to bill of materials"""
        queryset = super().get_queryset()
        
        # Only apply role-based filtering if not a superuser
        if not self.request.user.is_superuser and hasattr(self.request.user, 'user_roles'):
            user_roles = self.request.user.user_roles.filter(is_active=True)
            primary_role = None
            
            # Find primary role
            for user_role in user_roles:
                if user_role.is_primary:
                    primary_role = user_role
                    break
                    
            # If no primary role is found, use the first active role
            if not primary_role and user_roles.exists():
                primary_role = user_roles.first()
            
            # Filter queryset based on role scope
            if primary_role:
                if primary_role.service_center:
                    # Service center role only sees BOMs for their service center
                    queryset = queryset.filter(service_center_id=primary_role.service_center.id)
                elif primary_role.company:
                    # Company role only sees BOMs for their company's service centers
                    queryset = queryset.filter(service_center__company_id=primary_role.company.id)
                elif primary_role.franchise:
                    # Franchise role only sees BOMs for their franchise
                    queryset = queryset.filter(service_center__company__franchise_id=primary_role.franchise.id)
                    
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Bills of Materials')
        return context

class BillOfMaterialsDetailView(LoginRequiredMixin, DetailView):
    model = BillOfMaterials
    template_name = 'work_orders/bom_detail.html'
    context_object_name = 'bom'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = self.object.name
        context['components'] = self.object.items.all().order_by('sequence')
        return context

class WorkOrderMaterialCreateView(LoginRequiredMixin, CreateView):
    model = WorkOrderMaterial
    fields = ['item', 'quantity', 'unit_of_measure', 'notes']
    template_name = 'work_orders/material_form.html'
    
    def get_work_order(self):
        return get_object_or_404(WorkOrder, pk=self.kwargs['work_order_id'])
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        work_order = self.get_work_order()
        context['work_order'] = work_order
        
        # Get the vehicle if available (for service work orders)
        vehicle = None
        if hasattr(work_order, 'vehicle') and work_order.vehicle:
            vehicle = work_order.vehicle
        elif work_order.service_item_serial:
            # Try to find vehicle by serial number
            vehicle = Vehicle.objects.filter(
                tenant_id=getattr(self.request, 'tenant_id', None),
                vin=work_order.service_item_serial
            ).first()
        
        if vehicle:
            context['vehicle'] = vehicle
        
        # Get service center warehouses
        service_center = None
        if hasattr(work_order, 'service_center') and work_order.service_center:
            service_center = work_order.service_center
            context['service_center'] = service_center
            
            # Get available warehouses for this service center
            context['warehouses'] = service_center.get_available_warehouses()
        
        return context
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        work_order = self.get_work_order()
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Start with base queryset limited by tenant
        items_queryset = Item.objects.all()
        if tenant_id:
            items_queryset = items_queryset.filter(tenant_id=tenant_id)
            
        # Filter by vehicle compatibility if applicable
        vehicle = None
        if hasattr(work_order, 'vehicle') and work_order.vehicle:
            vehicle = work_order.vehicle
        elif work_order.service_item_serial:
            # Try to find vehicle by serial number
            vehicle = Vehicle.objects.filter(
                tenant_id=tenant_id,
                vin=work_order.service_item_serial
            ).first()
            
        if vehicle:
            items_queryset = items_queryset.compatible_with_vehicle(
                make=vehicle.make,
                model=vehicle.model,
                year=vehicle.year
            )
            
        # Filter by operation/work order type compatibility
        if work_order.work_order_type:
            operation_items = items_queryset.compatible_with_operation(
                operation_type_id=work_order.work_order_type.id
            )
            
            # We don't want to strictly limit to operation-compatible items,
            # but we want to prioritize them in the UI
            context = self.get_context_data()
            context['operation_compatible_items'] = operation_items
            
        # Filter by warehouse availability if service center is set
        service_center = None
        if hasattr(work_order, 'service_center') and work_order.service_center:
            service_center = work_order.service_center
            
            # Get warehouses for this service center
            warehouses = service_center.get_available_warehouses()
            
            if warehouses:
                # Get items that have stock in any of these warehouses
                warehouse_items = items_queryset.filter(
                    locations__location__in=warehouses,
                    locations__quantity__gt=0
                ).distinct()
                
                # We don't want to strictly limit to warehouse items,
                # but we want to prioritize them in the UI
                context = self.get_context_data()
                context['warehouse_items'] = warehouse_items
                
                # Also prepare stock info for display
                warehouse_stock = {}
                for item in warehouse_items:
                    warehouse_stock[str(item.id)] = [
                        {
                            'warehouse_id': str(loc.location.id),
                            'warehouse_name': loc.location.name,
                            'quantity': float(loc.quantity),
                        }
                        for loc in item.locations.filter(
                            location__in=warehouses,
                            quantity__gt=0
                        )
                    ]
                context['warehouse_stock'] = json.dumps(warehouse_stock)
        
        # Always use the full queryset, but with prioritized items marked
        form.fields['item'].queryset = items_queryset
        
        return form
    
    def form_valid(self, form):
        """Set the work order before saving"""
        form.instance.work_order = self.get_work_order()
        form.instance.tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Check if this is an AJAX request
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            material = form.save()
            return JsonResponse({
                'success': True,
                'message': 'Material added successfully',
                'material': {
                    'id': str(material.id),
                    'item_name': material.item.name,
                    'quantity': float(material.quantity),
                    'unit_of_measure': material.unit_of_measure
                }
            })
        
        return super().form_valid(form)
    
    def form_invalid(self, form):
        """Handle invalid forms for AJAX requests"""
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            errors = {}
            for field, error_list in form.errors.items():
                errors[field] = [str(error) for error in error_list]
            return JsonResponse({
                'success': False,
                'error': 'Form validation failed',
                'errors': errors
            })
        return super().form_invalid(form)
    
    def get_success_url(self):
        return reverse('work_orders:work_order_detail', args=[self.kwargs['work_order_id']])

def api_filter_items(request):
    """
    API endpoint to filter items based on vehicle, operation, and warehouse
    """
    vehicle_make = request.GET.get('make')
    vehicle_model = request.GET.get('model')
    vehicle_year = request.GET.get('year')
    operation_type_id = request.GET.get('operation_type')
    service_center_id = request.GET.get('service_center')
    
    tenant_id = get_current_tenant_id()
    
    # Start with all items for this tenant
    items = Item.objects.all()
    if tenant_id:
        items = items.filter(tenant_id=tenant_id)
    
    # Apply role-based filtering to available service centers
    service_centers = ServiceCenter.objects.all()
    
    if not request.user.is_superuser and hasattr(request.user, 'user_roles'):
        user_roles = request.user.user_roles.filter(is_active=True)
        primary_role = None
        
        # Find primary role
        for user_role in user_roles:
            if user_role.is_primary:
                primary_role = user_role
                break
                
        # If no primary role is found, use the first active role
        if not primary_role and user_roles.exists():
            primary_role = user_roles.first()
        
        # Filter based on role scope
        if primary_role:
            if primary_role.service_center:
                # Service center role only sees their service center
                service_centers = service_centers.filter(id=primary_role.service_center.id)
            elif primary_role.company:
                # Company role only sees service centers in their company
                service_centers = service_centers.filter(company_id=primary_role.company.id)
            elif primary_role.franchise:
                # Franchise role only sees service centers in their franchise
                service_centers = service_centers.filter(company__franchise_id=primary_role.franchise.id)
    
    # Make sure the requested service center is in the allowed list
    if service_center_id:
        try:
            service_center = service_centers.get(pk=service_center_id)
        except ServiceCenter.DoesNotExist:
            # If the requested service center isn't in the allowed list, don't use it
            service_center_id = None
            service_center = None
    
    # Filter by vehicle compatibility
    if vehicle_make:
        vehicle_filter = Q(vehicle_compatibilities__make__exact=vehicle_make)
        
        if vehicle_model:
            vehicle_filter &= Q(vehicle_compatibilities__model__exact=vehicle_model)
            
        if vehicle_year and vehicle_year.isdigit():
            year = int(vehicle_year)
            vehicle_filter &= (
                Q(vehicle_compatibilities__year_from__lte=year) & 
                (Q(vehicle_compatibilities__year_to__isnull=True) | Q(vehicle_compatibilities__year_to__gte=year))
            )
            
        items = items.filter(vehicle_filter).distinct()
    
    # Filter by operation type
    operation_items = []
    if operation_type_id:
        operation_items = list(items.compatible_with_operation(operation_type_id).values_list('id', flat=True))
    
    # Filter by warehouse
    warehouse_items = []
    warehouse_stock = {}
    
    if service_center_id:
        try:
            service_center = ServiceCenter.objects.get(pk=service_center_id)
            warehouses = service_center.get_available_warehouses()
            
            if warehouses:
                warehouse_items_qs = items.filter(
                    locations__location__in=warehouses,
                    locations__quantity__gt=0
                ).distinct()
                
                warehouse_items = list(warehouse_items_qs.values_list('id', flat=True))
                
                # Prepare stock info for display
                for item in warehouse_items_qs:
                    warehouse_stock[str(item.id)] = [
                        {
                            'warehouse_id': str(loc.location.id),
                            'warehouse_name': loc.location.name,
                            'quantity': float(loc.quantity),
                        }
                        for loc in item.locations.filter(
                            location__in=warehouses,
                            quantity__gt=0
                        )
                    ]
        except ServiceCenter.DoesNotExist:
            pass
    
    # Get basic item details for all filtered items
    item_list = items.values('id', 'name', 'sku', 'unit_price', 'quantity', 'category', 'item_type')
    
    response_data = {
        'items': list(item_list),
        'operation_items': operation_items,
        'warehouse_items': warehouse_items,
        'warehouse_stock': warehouse_stock
    }
    
    return JsonResponse(response_data)

@login_required
def api_get_maintenance_schedules(request):
    """
    FIXED API endpoint to get maintenance schedules based on vehicle data and mileage
    """
    try:
        # Get parameters from request
        vehicle_id_str = request.GET.get('vehicle_id', '').strip()
        make = request.GET.get('make')
        model = request.GET.get('model')
        year = request.GET.get('year')
        current_odometer = request.GET.get('odometer')
        last_service_odometer = request.GET.get('last_service_odometer')
        
        # Validate vehicle_id if provided
        vehicle = None
        if vehicle_id_str:
            try:
                vehicle_id = uuid.UUID(vehicle_id_str)
                from setup.models import Vehicle
                tenant_id = get_current_tenant_id()
                if tenant_id:
                    vehicle = Vehicle.objects.get(id=vehicle_id, tenant_id=tenant_id)
                else:
                    vehicle = Vehicle.objects.get(id=vehicle_id)
                
                # Use vehicle data if available
                if vehicle:
                    make = make or vehicle.make
                    model = model or vehicle.model
                    year = year or str(vehicle.year) if vehicle.year else None
                    
            except (ValueError, TypeError, Vehicle.DoesNotExist):
                # Continue with provided parameters if vehicle lookup fails
                pass
        
        tenant_id = get_current_tenant_id()
        
        # Start with all active schedules for this tenant
        schedules = MaintenanceSchedule.objects.filter(is_active=True)
        if tenant_id:
            schedules = schedules.filter(tenant_id=tenant_id)
        
        # Filter by vehicle make and model (MaintenanceSchedule has these fields)
        if make:
            schedules = schedules.filter(Q(vehicle_make__iexact=make) | Q(vehicle_make='') | Q(vehicle_make__isnull=True))
            
            if model:
                schedules = schedules.filter(Q(vehicle_model__iexact=model) | Q(vehicle_model='') | Q(vehicle_model__isnull=True))
        
        # Filter by year if provided
        if year and year.isdigit():
            year_val = int(year)
            year_filter = (
                (Q(year_from__lte=year_val) | Q(year_from__isnull=True)) &
                (Q(year_to__gte=year_val) | Q(year_to__isnull=True))
            )
            schedules = schedules.filter(year_filter)
        
        # Calculate recommended schedules based on mileage
        recommended = []
        if current_odometer and current_odometer.isdigit() and last_service_odometer and last_service_odometer.isdigit():
            current_odometer_val = int(current_odometer)
            last_service_odometer_val = int(last_service_odometer)
            mileage_since_service = current_odometer_val - last_service_odometer_val
            
            for schedule in schedules:
                if schedule.interval_type in ['mileage', 'both'] and schedule.mileage_interval:
                    if mileage_since_service >= schedule.mileage_interval:
                        recommended.append(str(schedule.id))
        
        # Return serialized schedules
        schedules_data = []
        for schedule in schedules:
            try:
                operations_count = schedule.operations.count() if hasattr(schedule, 'operations') else 0
            except:
                operations_count = 0
            
            schedules_data.append({
                'id': str(schedule.id),
                'name': schedule.name,
                'description': schedule.description or '',
                'interval_type': schedule.interval_type,
                'mileage_interval': schedule.mileage_interval,
                'time_interval_months': schedule.time_interval_months,
                'operations_count': operations_count,
                'is_recommended': str(schedule.id) in recommended
            })
        
        return JsonResponse({
            'schedules': schedules_data,
            'success': True,
            'count': len(schedules_data)
        })
        
    except Exception as e:
        logger.error(f"Error in api_get_maintenance_schedules: {str(e)}")
        return JsonResponse({
            'error': ['خطأ في تحميل جداول الصيانة'],
            'success': False,
            'details': str(e) if settings.DEBUG else 'Internal server error'
        }, status=500)

@login_required
def api_get_operations_for_vehicle(request):
    """
    FIXED VERSION: API endpoint that returns appropriate operations based on vehicle make, model, and odometer reading
    Used in the work order form, step 3 (Operations)
    
    CRITICAL FIX: Proper UUID validation and vehicle active work order checking
    """
    try:
        # Get parameters from request
        vehicle_id_str = request.GET.get('vehicle_id', '').strip()
        
        # CRITICAL FIX: Validate vehicle_id is not empty string
        if not vehicle_id_str:
            return JsonResponse({
                'error': ['معرف المركبة مطلوب'],
                'success': False
            }, status=400)
        
        # CRITICAL FIX: Proper UUID validation
        try:
            vehicle_id = uuid.UUID(vehicle_id_str)
        except (ValueError, TypeError):
            return JsonResponse({
                'error': ['معرف المركبة غير صحيح'],
                'success': False
            }, status=400)

        make = request.GET.get('make')
        model = request.GET.get('model')
        odometer = request.GET.get('odometer', 0)
        
        # CRITICAL FIX: Verify vehicle exists and belongs to tenant
        try:
            from setup.models import Vehicle
            # Get tenant_id from request context, not user
            tenant_id = get_current_tenant_id()
            if tenant_id:
                vehicle = Vehicle.objects.get(
                    id=vehicle_id, 
                    tenant_id=tenant_id
                )
            else:
                vehicle = Vehicle.objects.get(id=vehicle_id)
        except Vehicle.DoesNotExist:
            return JsonResponse({
                'error': ['المركبة غير موجودة'],
                'success': False
            }, status=404)
        
        # CRITICAL FIX: Check if vehicle has active work orders
        active_work_orders = WorkOrder.objects.filter(
            vehicle=vehicle,
            status__in=['planned', 'in_progress', 'on_hold']
        )
        if tenant_id:
            active_work_orders = active_work_orders.filter(tenant_id=tenant_id)
        
        if active_work_orders.exists():
            active_wo = active_work_orders.first()
            return JsonResponse({
                'error': [f'المركبة لديها أمر عمل نشط رقم {active_wo.work_order_number}'],
                'success': False,
                'active_work_order': {
                    'number': active_wo.work_order_number,
                    'status': active_wo.status
                }
            }, status=409)

        try:
            odometer = int(odometer)
        except (ValueError, TypeError):
            odometer = 0
        
        # Initialize result structures
        scheduled_operations = []
        custom_operations = []
        recommended_schedule = None
        
        # Set tenant ID if multi-tenancy is used
        tenant_id = get_current_tenant_id()
        
        # Import models
        from .models import MaintenanceSchedule, ScheduleOperation
        from django.db.models import Q
        
        # Log what we received for debugging
        print(f"API: operations_for_vehicle called with vehicle_id={vehicle_id}, make={make}, model={model}, odometer={odometer}")
        
        # Get vehicle make/model safely without relying on UUID parsing
        if vehicle_id:
            try:
                from django.db import connection
                
                # Use direct SQL to get vehicle info safely
                from django.conf import settings
                db_engine = settings.DATABASES['default']['ENGINE']
                
                with connection.cursor() as cursor:
                    if 'postgresql' in db_engine:
                        # PostgreSQL syntax
                        query = "SELECT make, model, last_service_date, last_service_odometer FROM setup_vehicle WHERE id::text = %s"
                    else:
                        # SQLite and others
                        query = "SELECT make, model, last_service_date, last_service_odometer FROM setup_vehicle WHERE id = %s"
                    
                    print(f"DEBUG (before SQL execute): query={query}, vehicle_id={vehicle_id}, tenant_id={tenant_id}")

                    # Only execute if vehicle_id is not None
                    if vehicle_id:
                        if tenant_id:
                            cursor.execute(query, [str(vehicle_id), tenant_id])
                        else:
                            cursor.execute(query, [str(vehicle_id)])
                        
                        row = cursor.fetchone()
                        if row:
                            vehicle_make, vehicle_model, last_service_date, last_service_odometer = row
                            print(f"Direct SQL found vehicle: {vehicle_make} {vehicle_model}")
                            make = vehicle_make
                            model = vehicle_model
                            
                            # Get last service odometer reading
                            try:
                                last_service_odometer = int(last_service_odometer) if last_service_odometer else 0
                            except (ValueError, TypeError):
                                last_service_odometer = 0
            except Exception as e:
                print(f"Error getting vehicle with SQL: {str(e)}")
                # Continue with any provided make/model
        
        print(f"DEBUG: vehicle_id before cursor.execute: {vehicle_id}")
        
        # Find maintenance schedules based on vehicle and odometer
        if make and odometer > 0:
            # Query for matching maintenance schedules
            schedules_query = MaintenanceSchedule.objects.filter(is_active=True)
            
            # Apply tenant filter if available
            if tenant_id:
                schedules_query = schedules_query.filter(tenant_id=tenant_id)
            
            # Filter by vehicle make and model
            schedules_query = schedules_query.filter(
                Q(vehicle_make__exact=make) | Q(vehicle_make='')
            )
            
            if model:
                schedules_query = schedules_query.filter(
                    Q(vehicle_model__exact=model) | Q(vehicle_model='')
                )
            
            # Get the schedules
            matching_schedules = list(schedules_query)
            mileage_since_last = odometer - last_service_odometer if 'last_service_odometer' in locals() else odometer
            
            # Find schedules that are recommended based on mileage
            recommended_schedules = []
            for schedule in matching_schedules:
                if schedule.interval_type in ['mileage', 'both'] and schedule.mileage_interval:
                    if mileage_since_last >= schedule.mileage_interval:
                        recommended_schedules.append(schedule)
            
            # If we have recommended schedules, use the first one
            if recommended_schedules:
                recommended_schedule = recommended_schedules[0]
                # Get operations for this schedule
                schedule_ops = ScheduleOperation.objects.filter(
                    maintenance_schedule=recommended_schedule
                ).order_by('sequence')
                
                for op in schedule_ops:
                    scheduled_operations.append({
                        'id': str(op.id),
                        'name': op.name,
                        'description': op.description,
                        'duration_hours': round(op.duration_minutes / 60, 2) if op.duration_minutes else 0,
                        'duration_minutes': op.duration_minutes,
                        'price': float(op.operation_type.price) if op.operation_type and hasattr(op.operation_type, 'price') and op.operation_type.price else 0,
                        'is_required': op.is_required,
                        'is_recommended': True,
                        'schedule_name': recommended_schedule.name
                    })
        
        # Get custom operations
        from inventory.models import OperationCompatibility
        
        # Get operations compatible with this make/model - avoid UUID validation errors
        try:
            if tenant_id:
                custom_ops_query = OperationCompatibility.objects.filter(tenant_id=tenant_id)
            else:
                custom_ops_query = OperationCompatibility.objects.all()
            
            # Filter by make/model using string matching instead of exact UUID matching to avoid validation errors
            if make:
                # Try to filter by make text representation instead of UUID
                custom_ops_query = custom_ops_query.filter(
                    Q(operation_description__icontains=make) | 
                    Q(operation_description__isnull=False)
                )
            
            if model:
                # Additional filtering by model if needed
                custom_ops_query = custom_ops_query.filter(
                    Q(operation_description__icontains=model) | 
                    Q(operation_description__isnull=False)
                                )
        except Exception as ops_error:
            print(f"Error in custom_ops_query: {str(ops_error)}")
            # Fallback to empty queryset
            custom_ops_query = OperationCompatibility.objects.none()
            
        # Get unique operation descriptions
        seen_descriptions = set()
        for op in custom_ops_query:
            if op.operation_description and op.operation_description not in seen_descriptions:
                seen_descriptions.add(op.operation_description)
                custom_operations.append({
                    'id': str(op.id),
                    'name': op.operation_description,
                    'description': op.operation_description,
                    'duration_hours': round(op.duration_minutes / 60, 2) if op.duration_minutes else 0,
                    'duration_minutes': op.duration_minutes,
                    'price': float(op.operation_type.price) if op.operation_type and hasattr(op.operation_type, 'price') and op.operation_type.price else 0,
                    'is_required': False,
                    'is_recommended': False
                })
        
        # If no operations are found, query database for operations with prices
        if not scheduled_operations and not custom_operations:
            try:
                # Query WorkOrderType model for operations with prices
                work_order_types = WorkOrderType.objects.filter(
                    tenant_id=tenant_id,
                    price__isnull=False,
                    price__gt=0
                ).order_by('name')
                
                print(f"Found {work_order_types.count()} WorkOrderType entries for operations API")
                
                for wot in work_order_types:
                    # Calculate duration based on operation type
                    duration_hours = 1.0  # Default
                    operation_name = wot.name.lower()
                    
                    if 'زيت' in operation_name and 'محرك' in operation_name:
                        duration_hours = 0.5
                    elif 'فلتر' in operation_name and 'زيت' in operation_name:
                        duration_hours = 0.25
                    elif 'فحص' in operation_name and ('فرامل' in operation_name or 'نظام' in operation_name):
                        duration_hours = 0.75
                    elif 'فحص' in operation_name and 'إطارات' in operation_name:
                        duration_hours = 0.33
                    elif 'فحص' in operation_name and ('سوائل' in operation_name or 'مستوى' in operation_name):
                        duration_hours = 0.25
                    elif 'فحص' in operation_name and ('فلتر' in operation_name or 'هواء' in operation_name):
                        duration_hours = 0.33
                    elif 'صيانة' in operation_name and 'دورية' in operation_name:
                        duration_hours = 2.0
                    elif 'تصليح' in operation_name and 'كهرباء' in operation_name:
                        duration_hours = 1.5
                    elif 'تصليح' in operation_name and 'ميكانيكا' in operation_name:
                        duration_hours = 2.5
                    elif 'ضبط' in operation_name and 'محاذاة' in operation_name:
                        duration_hours = 1.0
                    elif 'تغيير' in operation_name and 'بطارية' in operation_name:
                        duration_hours = 0.5
                    elif 'تغيير' in operation_name and 'إطارات' in operation_name:
                        duration_hours = 1.0
                    elif 'فحص' in operation_name and 'شامل' in operation_name:
                        duration_hours = 1.5
                    
                    custom_operations.append({
                        'id': str(wot.id),
                        'name': wot.name,
                        'description': wot.name,
                        'duration_hours': duration_hours,
                        'duration_minutes': int(duration_hours * 60),
                        'price': float(wot.price),
                        'is_required': False,
                        'is_recommended': False
                    })
                    
                print(f"Added {len(custom_operations)} operations from database")
                
            except Exception as db_error:
                print(f"Error querying WorkOrderType in operations API: {str(db_error)}")
                # Only add minimal fallback if database query fails
                fallback_operations = [
                    {'name': 'صيانة عامة', 'duration': 1.0, 'price': 250},
                    {'name': 'فحص شامل', 'duration': 1.5, 'price': 350}
                ]
                
                for op in fallback_operations:
                    custom_operations.append({
                        'id': op['name'],
                        'name': op['name'],
                        'description': op['name'],
                        'duration_hours': op['duration'],
                        'duration_minutes': int(op['duration'] * 60),
                        'price': op['price'],
                        'is_required': False,
                        'is_recommended': False
                    })
        
        # Debug logging
        print(f"DEBUG: Scheduled operations count: {len(scheduled_operations)}")
        for op in scheduled_operations:
            print(f"DEBUG: Scheduled operation - name: {op.get('name')}, price: {op.get('price')}")
        
        print(f"DEBUG: Custom operations count: {len(custom_operations)}")  
        for op in custom_operations:
            print(f"DEBUG: Custom operation - name: {op.get('name')}, price: {op.get('price')}")
        
        # Prepare the response
        response_data = {
            'success': True,
            'scheduled_operations': scheduled_operations,
            'custom_operations': custom_operations,
            'recommended_schedule': {
                'id': str(recommended_schedule.id),
                'name': recommended_schedule.name,
                'description': recommended_schedule.description,
                'mileage_interval': recommended_schedule.mileage_interval
            } if recommended_schedule else None,
            'mileage_since_last_service': mileage_since_last if 'mileage_since_last' in locals() else None
        }
        
        return JsonResponse(response_data)
        
    except Exception as e:
        # Log detailed error information
        import traceback
        error_traceback = traceback.format_exc()
        print(f"ERROR in api_get_operations_for_vehicle: {str(e)}")
        print(f"Request parameters: vehicle_id={request.GET.get('vehicle_id')}, make={request.GET.get('make')}, model={request.GET.get('model')}")
        print(f"Traceback: {error_traceback}")
        
        return JsonResponse({
            'success': False,
            'error': str(e),
            'message': "An error occurred while getting operations. Please try again or contact support if the problem persists."
        })

@login_required
def api_get_operation_descriptions(request):
    """
    API endpoint that returns operation descriptions from VehicleOperationCompatibility
    Used in the work order form to populate operation description dropdowns
    """
    try:
        # Get parameters
        vehicle_id = request.GET.get('vehicle_id')
        make = request.GET.get('make')
        model = request.GET.get('model')
        
        # Get tenant_id properly
        tenant_id = get_current_tenant_id()
        
        # Import models
        from inventory.models import VehicleOperationCompatibility, OperationCompatibility
        from setup.models import Vehicle
        
        # Log the request for debugging
        print(f"API: operation_descriptions called with vehicle_id={vehicle_id}, make={make}, model={model}")
        
        # Try to get vehicle make/model first thing, before any other operations
        if vehicle_id:
            try:
                # Use direct SQL to get vehicle info, avoiding ORM UUID issues
                from django.db import connection
                
                # Get vehicle ID as text to avoid UUID parsing
                vehicle_id_str_for_sql = str(vehicle_id) # Renamed to avoid confusion with original request param
                
                # Construct a safe SQL query that doesn't rely on UUID parsing
                # Different syntax based on database backend
                from django.conf import settings
                db_engine = settings.DATABASES['default']['ENGINE']
                
                with connection.cursor() as cursor:
                    if 'postgresql' in db_engine:
                        # PostgreSQL syntax
                        id_clause = "id::text = %s"
                    else:
                        # SQLite and others
                        id_clause = "id = %s"
                    
                    if tenant_id:
                        cursor.execute(
                            f"SELECT make, model FROM setup_vehicle WHERE {id_clause} AND tenant_id = %s", 
                            [vehicle_id_str_for_sql, tenant_id]
                        )
                    else:
                        cursor.execute(
                            f"SELECT make, model FROM setup_vehicle WHERE {id_clause}", 
                            [vehicle_id_str_for_sql]
                        )
                    
                    row = cursor.fetchone()
                    if row:
                        vehicle_make, vehicle_model = row
                        print(f"Direct SQL found vehicle for operation descriptions: {vehicle_make} {vehicle_model}")
                        # Set make and model if not already set
                        if not make:
                            make = vehicle_make
                        if not model:
                            model = vehicle_model
            except Exception as e:
                print(f"Direct SQL vehicle lookup failed in api_get_operation_descriptions: {str(e)}")
                # Continue with any make/model values we might already have
        
        # Filter VehicleOperationCompatibility by make/model if provided
        try:
            # Get all vehicle ops for this tenant and filter in Python
            all_vehicle_ops = list(VehicleOperationCompatibility.objects.filter(tenant_id=tenant_id))
            filtered_vehicle_ops = []
            
            # Python-side filtering to avoid ORM UUID issues
            for vop in all_vehicle_ops:
                if make and vop.vehicle_make and hasattr(vop.vehicle_make, 'name'):
                    try:
                        if vop.vehicle_make.name.lower() != make.lower():
                            continue
                    except AttributeError:
                        pass
                        
                if model and vop.vehicle_model and hasattr(vop.vehicle_model, 'name'):
                    try:
                        if vop.vehicle_model.name.lower() != model.lower():
                            continue
                    except AttributeError:
                        pass
                
                # This vehicle op passed all filters, add it
                filtered_vehicle_ops.append(vop)
            
            # Get operation compatibility IDs safely
            op_compat_ids = set()
            for vop in filtered_vehicle_ops:
                if hasattr(vop, 'operation_compatibility_id') and vop.operation_compatibility_id:
                    op_compat_ids.add(vop.operation_compatibility_id)
            
            # Filter operation compatibilities using Python instead of ORM
            all_op_compat = list(OperationCompatibility.objects.filter(tenant_id=tenant_id))
            filtered_query = []
            
            for op in all_op_compat:
                if op.id in op_compat_ids:
                    filtered_query.append(op)
                    
            # Get unique operation descriptions
            descriptions = set()
            for op in filtered_query:
                if op.operation_description and op.operation_description.strip():
                    descriptions.add(op.operation_description.strip())
        except Exception as e:
            print(f"Error processing operation compatibilities: {str(e)}")
            descriptions = set()
        
                    # Format the result
        operation_types = []
        for description in descriptions:
            # Look for a matching operation compatibility to get the duration and price
            duration_hours = None
            price = None
            for op in filtered_query:
                if op.operation_description == description:
                    if op.duration_minutes:
                        duration_hours = round(op.duration_minutes / 60, 2)
                    # Try to get price from operation type or use default pricing
                    if hasattr(op, 'operation_type') and op.operation_type and hasattr(op.operation_type, 'price') and op.operation_type.price:
                        price = float(op.operation_type.price)
                    else:
                        # Use default pricing based on operation name
                        default_prices = {
                            'تغيير زيت المحرك': 150,
                            'تغيير فلتر الزيت': 45,
                            'فحص نظام الفرامل': 200,
                            'فحص الإطارات': 100,
                            'فحص مستوى السوائل': 75,
                            'فحص فلتر الهواء': 85,
                            'صيانة دورية': 500,
                            'تصليح كهرباء': 350,
                            'تصليح ميكانيكا': 600,
                            'ضبط محاذاة العجلات': 250,
                            'تغيير بطارية': 300,
                            'تغيير إطارات': 400,
                            'فحص شامل': 350,
                            # Legacy short names for backward compatibility
                            'تغيير زيت': 150,
                            'فحص فرامل': 200
                        }
                        price = default_prices.get(description, 100)
                    break
                    
            operation_types.append({
                'id': description,
                'name': description,
                'duration_hours': duration_hours,
                'price': price
            })
        
        # Always add operations from WorkOrderType database to ensure maintenance operations are included
        try:
            # Query WorkOrderType model for actual operation types with prices
            work_order_types = WorkOrderType.objects.filter(
                tenant_id=tenant_id,
                price__isnull=False,
                price__gt=0
            ).order_by('name')
            
            print(f"Found {work_order_types.count()} WorkOrderType entries with prices")
            
            # Create a set to track operation names we've already added to avoid duplicates
            existing_names = {op['name'] for op in operation_types}
            
            for wot in work_order_types:
                # Skip if we already have this operation from vehicle compatibility
                if wot.name in existing_names:
                    continue
                    
                # Convert estimated duration from typical time (we don't have duration in WorkOrderType)
                # Use reasonable defaults based on operation type
                duration_hours = 1.0  # Default 1 hour
                
                # Set typical durations for common operations
                operation_name = wot.name.lower()
                if 'زيت' in operation_name and 'محرك' in operation_name:
                    duration_hours = 0.5  # Oil change: 30 minutes
                elif 'فلتر' in operation_name and 'زيت' in operation_name:
                    duration_hours = 0.25  # Oil filter: 15 minutes
                elif 'فحص' in operation_name and ('فرامل' in operation_name or 'نظام' in operation_name):
                    duration_hours = 0.75  # Brake inspection: 45 minutes
                elif 'فحص' in operation_name and 'إطارات' in operation_name:
                    duration_hours = 0.33  # Tire inspection: 20 minutes
                elif 'فحص' in operation_name and ('سوائل' in operation_name or 'مستوى' in operation_name):
                    duration_hours = 0.25  # Fluid check: 15 minutes
                elif 'فحص' in operation_name and ('فلتر' in operation_name or 'هواء' in operation_name):
                    duration_hours = 0.33  # Air filter check: 20 minutes
                elif 'صيانة' in operation_name and 'دورية' in operation_name:
                    duration_hours = 2.0  # Periodic maintenance: 2 hours
                elif 'تصليح' in operation_name and 'كهرباء' in operation_name:
                    duration_hours = 1.5  # Electrical repair: 1.5 hours
                elif 'تصليح' in operation_name and 'ميكانيكا' in operation_name:
                    duration_hours = 2.5  # Mechanical repair: 2.5 hours
                elif 'ضبط' in operation_name and 'محاذاة' in operation_name:
                    duration_hours = 1.0  # Wheel alignment: 1 hour
                elif 'تغيير' in operation_name and 'بطارية' in operation_name:
                    duration_hours = 0.5  # Battery replacement: 30 minutes
                elif 'تغيير' in operation_name and 'إطارات' in operation_name:
                    duration_hours = 1.0  # Tire replacement: 1 hour
                elif 'فحص' in operation_name and 'شامل' in operation_name:
                    duration_hours = 1.5  # Comprehensive inspection: 1.5 hours
                
                operation_types.append({
                    'id': str(wot.id),
                    'name': wot.name,
                    'duration_hours': duration_hours,
                    'price': float(wot.price)
                })
                
            print(f"Added {len([op for op in operation_types if op['name'] not in existing_names])} additional operations from database")
                
        except Exception as e:
            print(f"Error querying WorkOrderType: {str(e)}")
            # Fall back to basic defaults only if database query fails and we have no operations
            if not operation_types:
                basic_defaults = [
                    {'name': 'صيانة عامة', 'duration': 1.0, 'price': 250},
                    {'name': 'فحص شامل', 'duration': 1.5, 'price': 350}
                ]
                for op in basic_defaults:
                    operation_types.append({
                        'id': op['name'],
                        'name': op['name'],
                        'duration_hours': op['duration'],
                        'price': op['price']
                    })
        
        return JsonResponse({
            'success': True,
            'operation_types': operation_types
        })
        
    except Exception as e:
        # Log detailed error information
        import traceback
        error_traceback = traceback.format_exc()
        print(f"ERROR in api_get_operation_descriptions: {str(e)}")
        print(f"Request parameters: vehicle_id={request.GET.get('vehicle_id')}, make={request.GET.get('make')}, model={request.GET.get('model')}")
        print(f"Traceback: {error_traceback}")
        
        return JsonResponse({
            'success': False,
            'error': str(e),
            'message': "An error occurred while getting operation descriptions. Please try again or contact support if the problem persists."
        })

@login_required
def api_search_customers(request):
    term = request.GET.get('term', '')
    search_by = request.GET.get('search_by', 'all')
    
    if not term or len(term) < 2: # Require at least 2 characters for search
        return JsonResponse({'customers': []})

    try:
        query_filter = Q()
        if search_by == 'name':
            query_filter = (
                Q(first_name__icontains=term) |
                Q(second_name__icontains=term) |
                Q(third_name__icontains=term) |
                Q(last_name__icontains=term)
            )
        elif search_by == 'phone':
            query_filter = Q(phone__icontains=term)
        elif search_by == 'id_number':
            query_filter = Q(id_number__icontains=term)
        else: # Default to 'all' or if an unknown value is passed
            query_filter = (
                Q(first_name__icontains=term) |
                Q(second_name__icontains=term) |
                Q(third_name__icontains=term) |
                Q(last_name__icontains=term) |
                Q(phone__icontains=term) |
                Q(id_number__icontains=term)
            )

        customers = Customer.objects.filter(query_filter)[:10]
        
        customers_data = []
        for customer in customers:
            # Directly concatenate name fields instead of calling get_full_name()
            full_name = f"{customer.first_name or ''} {customer.second_name or ''} {customer.third_name or ''} {customer.last_name or ''}".strip()
            
            customer_data = {
                'id': customer.id,
                'full_name': full_name,
                'phone_number': customer.phone or '',
                'national_id_number': customer.id_number or '',  # Changed to match expected field in frontend
                'id_type': customer.id_type or '',
                'gender': customer.gender or '',  # Direct attribute access instead of get_gender_display()
            }
            customers_data.append(customer_data)
        
        return JsonResponse({'customers': customers_data})
    except Exception as e:
        # Log the error
        print(f"Error in api_search_customers: {str(e)}")
        # Return empty list with error message in case of any exception
        return JsonResponse({'customers': [], 'error': str(e)})

@login_required
def api_search_vehicles(request):
    """API endpoint to search for vehicles"""
    if request.method == 'GET':
        try:
            search_term = request.GET.get('term', '')
            customer_id = request.GET.get('customer_id', None)
            tenant_id = get_current_tenant_id()
            include_standard = request.GET.get('include_standard', 'true') == 'true'
            fetch_all = request.GET.get('fetch_all', 'false') == 'true'

            # If fetch_all is true, we don't require a search term when customer_id is provided
            if not search_term and not customer_id:
                return JsonResponse({'results': []})

            vehicles = Vehicle.objects.all()
            
            # Use select_related to optimize queries for related objects
            if include_standard:
                vehicles = vehicles.select_related('owner', 'standard_make', 'standard_model')
            else:
                vehicles = vehicles.select_related('owner')

            if tenant_id:
                vehicles = vehicles.filter(tenant_id=tenant_id)

            # Filter by customer if customer_id is provided
            if customer_id:
                vehicles = vehicles.filter(owner_id=customer_id)

            # Apply search term filter (if provided and not fetching all)
            if search_term and not (fetch_all and customer_id):
                vehicles = vehicles.filter(
                    Q(license_plate__icontains=search_term) |
                    Q(vin__icontains=search_term) |
                    Q(make__icontains=search_term) |
                    Q(model__icontains=search_term) |
                    Q(standard_make__name__icontains=search_term) |
                    Q(standard_model__name__icontains=search_term)
                )

            # Get vehicles with active work orders for flagging
            active_statuses = ['draft', 'planned', 'in_progress', 'on_hold']
            active_work_orders = WorkOrder.objects.filter(
                status__in=active_statuses,
                vehicle__isnull=False
            ).select_related('vehicle')
            
            # Create a mapping of vehicle_id to active work order details
            active_vehicle_work_orders = {}
            for wo in active_work_orders:
                active_vehicle_work_orders[str(wo.vehicle.id)] = {
                    'work_order_number': wo.work_order_number,
                    'status': wo.status,
                    'status_display': wo.get_status_display(),
                    'created_at': wo.created_at.strftime('%Y-%m-%d')
                }

            # Limit results (more vehicles when fetch_all is true)
            limit = 50 if fetch_all else 15
            vehicles = vehicles[:limit]

            # Format results - separate available and unavailable vehicles
            available_vehicles = []
            unavailable_vehicles = []
            
            for vehicle in vehicles:
                # Check if this vehicle has active work orders
                vehicle_id = str(vehicle.id)
                has_active_work_order = vehicle_id in active_vehicle_work_orders
                
                # Get standard make and model info if available
                standard_make_id = str(vehicle.standard_make.id) if vehicle.standard_make else None
                standard_make_name = vehicle.standard_make.name if vehicle.standard_make else None
                standard_model_id = str(vehicle.standard_model.id) if vehicle.standard_model else None
                standard_model_name = vehicle.standard_model.name if vehicle.standard_model else None
                
                # Create display text with availability indicator
                display_text = f"{vehicle.make} {vehicle.model} ({vehicle.license_plate or vehicle.vin or 'N/A'})"
                if has_active_work_order:
                    display_text += " - ❌ غير متاح"
                
                vehicle_data = {
                    'id': vehicle_id,
                    'text': display_text,
                    'make': vehicle.make,
                    'model': vehicle.model,
                    'year': vehicle.year,
                    'vin': vehicle.vin,
                    'license_plate': vehicle.license_plate,
                    'color': vehicle.color,
                    'owner_id': str(vehicle.owner.id) if vehicle.owner else None,
                    'owner_name': vehicle.owner.full_name if vehicle.owner else '',
                    'has_active_work_order': has_active_work_order,
                    'is_available': not has_active_work_order
                }
                
                # Add active work order details if present
                if has_active_work_order:
                    vehicle_data['active_work_order'] = active_vehicle_work_orders[vehicle_id]
                
                # Include standard make/model info if requested
                if include_standard:
                    vehicle_data.update({
                        'standard_make_id': standard_make_id,
                        'standard_make_name': standard_make_name,
                        'standard_model_id': standard_model_id,
                        'standard_model_name': standard_model_name
                    })
                
                # Separate available and unavailable vehicles
                if has_active_work_order:
                    unavailable_vehicles.append(vehicle_data)
                else:
                    available_vehicles.append(vehicle_data)
            
            # Combine results with available vehicles first
            results = available_vehicles + unavailable_vehicles

            return JsonResponse({'results': results})
        except Exception as e:
            import traceback
            print(f"Error in api_search_vehicles: {e}")
            traceback.print_exc()
            return JsonResponse({'error': str(e)}, status=500)
    return JsonResponse({'error': _('Invalid request method')}, status=405)

@login_required
def api_get_vehicle_makes(request):
    """API endpoint to get all vehicle makes for dropdown"""
    tenant_id = get_current_tenant_id()
    
    makes = VehicleMake.objects.filter(is_active=True)
    if tenant_id:
        makes = makes.filter(tenant_id=tenant_id)
    
    makes = makes.order_by('name')
    
    results = [{'id': str(make.id), 'name': make.name} for make in makes]
    return JsonResponse({'results': results})

@login_required
def api_get_vehicle_models(request):
    """API endpoint to get vehicle models for a specific make"""
    make_id = request.GET.get('make_id')
    tenant_id = get_current_tenant_id()
    
    if not make_id:
        return JsonResponse({'results': []})
    
    models = VehicleModel.objects.filter(make_id=make_id, is_active=True)
    if tenant_id:
        models = models.filter(tenant_id=tenant_id)
    
    models = models.order_by('name')
    
    results = [{'id': str(model.id), 'name': model.name, 'vehicle_class': model.vehicle_class} for model in models]
    return JsonResponse({'results': results})

@login_required
def api_create_vehicle(request):
    """API endpoint to create a new vehicle"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Try to get tenant ID from multiple possible sources
            tenant_id = get_current_tenant_id()
            
            # If tenant_id is not available on request, try to get it from the session or use a default value
            if not tenant_id:
                tenant_id = request.session.get('tenant_id', None)
                
            # As a last resort, try to get tenant_id from an existing vehicle or service center
            if not tenant_id:
                existing_vehicle = Vehicle.objects.first()
                if existing_vehicle:
                    tenant_id = existing_vehicle.tenant_id
                else:
                    # Try to get tenant from any existing service center
                    existing_service_center = ServiceCenter.objects.first()
                    if existing_service_center:
                        tenant_id = existing_service_center.tenant_id
                    else:
                        # If all else fails, use a default value (e.g., 1 for single-tenant setups)
                        tenant_id = 1
            
            # Get data from the request
            make = data.get('make')
            model = data.get('model') 
            year = data.get('year')
            license_plate = data.get('license_plate')
            vin = data.get('vin')
            color = data.get('color')
            owner_id = data.get('owner_id')
            service_center_id = data.get('service_center_id')
            notes = data.get('notes')
            
            # Standard make/model references
            standard_make_id = data.get('standard_make_id')
            standard_model_id = data.get('standard_model_id')

            # Basic validation (more comprehensive validation can be added)
            if not make or not model:
                return JsonResponse({'success': False, 'error': _('Missing required fields: Make, Model')}, status=400)

            # Create the customer instance if owner_id is provided
            owner = None
            if owner_id:
                try:
                    owner = Customer.objects.get(id=owner_id)
                except Customer.DoesNotExist:
                    return JsonResponse({'success': False, 'error': _('Specified owner does not exist')}, status=400)

            # Create the service_center instance if service_center_id is provided
            service_center = None
            if service_center_id:
                try:
                    service_center = ServiceCenter.objects.get(id=service_center_id)
                except ServiceCenter.DoesNotExist:
                    return JsonResponse({'success': False, 'error': _('Specified service center does not exist')}, status=400)
                    
            # Get the standard make and model instances if IDs are provided
            standard_make = None
            standard_model = None
            
            if standard_make_id:
                try:
                    standard_make = VehicleMake.objects.get(id=standard_make_id)
                except VehicleMake.DoesNotExist:
                    # Don't return an error, just don't link to a standard make
                    pass
                    
            if standard_model_id:
                try:
                    standard_model = VehicleModel.objects.get(id=standard_model_id)
                    # Ensure the model belongs to the selected make
                    if standard_make and standard_model.make != standard_make:
                        standard_model = None
                except VehicleModel.DoesNotExist:
                    # Don't return an error, just don't link to a standard model
                    pass

            # Create the new vehicle
            vehicle = Vehicle.objects.create(
                tenant_id=tenant_id,
                make=make,
                model=model,
                year=int(year) if year and year.isdigit() else None,
                license_plate=license_plate or '',
                vin=vin or '',
                color=color or '',
                owner=owner,
                service_center=service_center,
                standard_make=standard_make,
                standard_model=standard_model,
                notes=notes or ''
            )
            
            # Return success response with the new vehicle's ID and details
            vehicle_data = {
                'id': str(vehicle.id),
                'text': f"{vehicle.make} {vehicle.model} ({vehicle.license_plate or vehicle.vin or 'N/A'})",
                'make': vehicle.make,
                'model': vehicle.model,
                'year': vehicle.year,
                'vin': vehicle.vin,
                'license_plate': vehicle.license_plate,
                'color': vehicle.color,
                'owner_id': str(vehicle.owner.id) if vehicle.owner else None,
                'has_active_work_order': False,
                'standard_make_id': str(standard_make.id) if standard_make else None,
                'standard_model_id': str(standard_model.id) if standard_model else None
            }
            
            return JsonResponse({'success': True, 'vehicle': vehicle_data})

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': _('Invalid JSON')}, status=400)
        except Exception as e:
            # Log the error for debugging
            print(f"Error creating vehicle: {e}")
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

    return JsonResponse({'success': False, 'error': _('Invalid request method.')}, status=405) # Method Not Allowed

@login_required
def api_request_part_transfer(request):
    """API endpoint to request part transfer between warehouses"""
    if request.method == 'POST':
        try:
            import json
            from work_orders.services import WorkOrderAllocationService
            from inventory.models import Item
            from warehouse.models import Warehouse
            from work_orders.models import WorkOrder
            from django.utils.translation import gettext_lazy as _
            
            data = json.loads(request.body)
            
            # Get request parameters
            work_order_id = data.get('work_order_id')
            item_id = data.get('item_id')
            source_warehouse_id = data.get('source_warehouse_id')
            destination_warehouse_id = data.get('destination_warehouse_id')
            quantity = data.get('quantity')
            notes = data.get('notes', '')
            
            # Validate required fields
            if not all([work_order_id, item_id, source_warehouse_id, destination_warehouse_id, quantity]):
                return JsonResponse({
                    'success': False,
                    'error': _('Missing required fields: work_order_id, item_id, source_warehouse_id, destination_warehouse_id, quantity')
                })
            
            # Get objects
            try:
                work_order = WorkOrder.objects.get(id=work_order_id)
                item = Item.objects.get(id=item_id)
                source_warehouse = Warehouse.objects.get(id=source_warehouse_id)
                destination_warehouse = Warehouse.objects.get(id=destination_warehouse_id)
                quantity = float(quantity)
            except (WorkOrder.DoesNotExist, Item.DoesNotExist, Warehouse.DoesNotExist, ValueError) as e:
                return JsonResponse({
                    'success': False,
                    'error': f'Invalid data: {str(e)}'
                })
            
            # Check warehouse stock availability
            from warehouse.models import ItemLocation
            available_stock = ItemLocation.objects.filter(
                warehouse=source_warehouse,
                item=item
            ).aggregate(
                total=models.Sum('quantity')
            )['total'] or 0
            
            if available_stock < quantity:
                return JsonResponse({
                    'success': False,
                    'error': f'Insufficient stock in source warehouse. Available: {available_stock}, Requested: {quantity}'
                })
            
            # Request transfer using service
            result = WorkOrderAllocationService.request_part_transfer(
                work_order=work_order,
                item=item,
                source_warehouse=source_warehouse,
                destination_warehouse=destination_warehouse,
                quantity=quantity,
                user=request.user
            )
            
            if result['success']:
                # Create transfer record in warehouse app
                from warehouse.models import TransferOrder, TransferOrderItem
                
                # Generate transfer order number
                last_transfer = TransferOrder.objects.filter(
                    tenant_id=work_order.tenant_id
                ).order_by('-created_at').first()
                
                if last_transfer and last_transfer.transfer_number:
                    try:
                        last_number = int(last_transfer.transfer_number.split('-')[-1])
                        transfer_number = f"TR-{last_number + 1:06d}"
                    except (ValueError, IndexError):
                        transfer_number = f"TR-{timezone.now().strftime('%Y%m%d')}001"
                else:
                    transfer_number = f"TR-{timezone.now().strftime('%Y%m%d')}001"
                
                # Create transfer order
                transfer_order = TransferOrder.objects.create(
                    tenant_id=work_order.tenant_id,
                    transfer_number=transfer_number,
                    from_warehouse=source_warehouse,
                    to_warehouse=destination_warehouse,
                    requested_by=request.user,
                    status='pending',
                    work_order=work_order,
                    notes=f"Transfer for Work Order: {work_order.work_order_number}. {notes}".strip(),
                    priority='high' if work_order.priority == 'urgent' else 'normal'
                )
                
                # Create transfer item
                TransferOrderItem.objects.create(
                    tenant_id=work_order.tenant_id,
                    transfer_order=transfer_order,
                    item=item,
                    requested_quantity=quantity,
                    notes=notes
                )
                
                return JsonResponse({
                    'success': True,
                    'message': f'Transfer request created successfully: {transfer_number}',
                    'transfer_order_id': transfer_order.id,
                    'transfer_number': transfer_number
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': result.get('message', 'Transfer request failed')
                })
                
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': _('Invalid JSON data')
            })
        except Exception as e:
            logger.error(f"Error in part transfer request: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f'Transfer request failed: {str(e)}'
            })
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})

@login_required
def api_add_material_to_work_order(request):
    """API endpoint to add material to work order with availability checking"""
    if request.method == 'POST':
        try:
            import json
            from work_orders.models import WorkOrder, WorkOrderMaterial
            from inventory.models import Item
            from warehouse.models import ItemLocation
            from work_orders.services import WorkOrderAllocationService, WorkOrderPricingService
            from django.utils.translation import gettext_lazy as _
            from django.db import models
            
            data = json.loads(request.body)
            
            # Get request parameters
            work_order_id = data.get('work_order_id')
            item_id = data.get('item_id')
            quantity = data.get('quantity')
            unit_of_measure = data.get('unit_of_measure', '')
            notes = data.get('notes', '')
            check_availability = data.get('check_availability', True)
            
            # Validate required fields
            if not all([work_order_id, item_id, quantity]):
                return JsonResponse({
                    'success': False,
                    'error': _('Missing required fields: work_order_id, item_id, quantity')
                })
            
            # Get objects
            try:
                work_order = WorkOrder.objects.get(id=work_order_id)
                item = Item.objects.get(id=item_id)
                quantity = float(quantity)
                
                if quantity <= 0:
                    return JsonResponse({
                        'success': False,
                        'error': _('Quantity must be greater than 0')
                    })
                    
            except (WorkOrder.DoesNotExist, Item.DoesNotExist, ValueError) as e:
                return JsonResponse({
                    'success': False,
                    'error': f'Invalid data: {str(e)}'
                })
            
            # Check if material already exists for this work order
            existing_material = WorkOrderMaterial.objects.filter(
                work_order=work_order,
                item=item
            ).first()
            
            if existing_material:
                # Update existing material quantity
                existing_material.quantity += quantity
                if notes:
                    existing_material.notes = f"{existing_material.notes}; {notes}" if existing_material.notes else notes
                existing_material.save()
                
                return JsonResponse({
                    'success': True,
                    'message': f'Material quantity updated. New total: {existing_material.quantity}',
                    'material_id': existing_material.id,
                    'total_quantity': str(existing_material.quantity)
                })
            
            # Check availability if requested
            availability_info = {}
            if check_availability:
                # Check stock in service center warehouse
                service_center_stock = 0
                main_warehouse_stock = 0
                
                if work_order.service_center and work_order.service_center.primary_warehouse:
                    service_center_stock = ItemLocation.objects.filter(
                        warehouse=work_order.service_center.primary_warehouse,
                        item=item
                    ).aggregate(
                        total=models.Sum('quantity')
                    )['total'] or 0
                
                # Check stock in main warehouse
                main_warehouses = ItemLocation.objects.filter(
                    item=item
                ).exclude(
                    warehouse=work_order.service_center.primary_warehouse if work_order.service_center else None
                ).values('warehouse').distinct()
                
                for wh in main_warehouses:
                    wh_stock = ItemLocation.objects.filter(
                        warehouse_id=wh['warehouse'],
                        item=item
                    ).aggregate(
                        total=models.Sum('quantity')
                    )['total'] or 0
                    main_warehouse_stock += wh_stock
                
                total_available = service_center_stock + main_warehouse_stock
                
                availability_info = {
                    'service_center_stock': service_center_stock,
                    'main_warehouse_stock': main_warehouse_stock,
                    'total_available': total_available,
                    'requested_quantity': quantity,
                    'sufficient_stock': total_available >= quantity
                }
                
                # If insufficient stock, provide suggestions
                if not availability_info['sufficient_stock']:
                    shortage = quantity - total_available
                    availability_info['shortage'] = shortage
                    availability_info['suggestions'] = []
                    
                    if main_warehouse_stock > 0:
                        availability_info['suggestions'].append(
                            f"Request transfer of {min(main_warehouse_stock, shortage)} from main warehouse"
                        )
                    
                    if shortage > main_warehouse_stock:
                        availability_info['suggestions'].append(
                            f"Purchase additional {shortage - main_warehouse_stock} units"
                        )
            
            # Create the material record
            material = WorkOrderMaterial.objects.create(
                tenant_id=work_order.tenant_id,
                work_order=work_order,
                item=item,
                quantity=quantity,
                unit_of_measure=unit_of_measure,
                notes=notes,
                is_consumed=False
            )
            
            # Update work order cost estimates
            cost_breakdown = WorkOrderPricingService.update_work_order_costs(work_order)
            
            response_data = {
                'success': True,
                'message': f'Material "{item.name}" added successfully',
                'material_id': material.id,
                'material': {
                    'id': material.id,
                    'item_name': item.name,
                    'quantity': str(material.quantity),
                    'unit_price': str(item.unit_price or 0),
                    'total_cost': str(material.quantity * (item.unit_price or 0)),
                    'unit_of_measure': unit_of_measure,
                    'notes': notes
                },
                'work_order_updated_cost': str(cost_breakdown['total_cost'])
            }
            
            # Add availability info if checked
            if check_availability:
                response_data['availability'] = availability_info
            
            return JsonResponse(response_data)
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': _('Invalid JSON data')
            })
        except Exception as e:
            logger.error(f"Error adding material to work order: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f'Failed to add material: {str(e)}'
            })
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})

@login_required
def api_create_customer(request):
    """API endpoint to create a new customer"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Try to get tenant ID from multiple possible sources
            tenant_id = get_current_tenant_id()
            
            # If tenant_id is not available on request, try to get it from the session or use a default value
            if not tenant_id:
                tenant_id = request.session.get('tenant_id', None)
                
            # As a last resort, try to get tenant_id from an existing customer or service center
            if not tenant_id:
                # Try to get tenant from any existing customer
                existing_customer = Customer.objects.first()
                if existing_customer:
                    tenant_id = existing_customer.tenant_id
                else:
                    # Try to get tenant from any existing service center
                    existing_service_center = ServiceCenter.objects.first()
                    if existing_service_center:
                        tenant_id = existing_service_center.tenant_id
                    else:
                        # If all else fails, use a default value (e.g., 1 for single-tenant setups)
                        tenant_id = 1
            
            # Get data from the request
            first_name = data.get('first_name')
            second_name = data.get('second_name')
            third_name = data.get('third_name')
            last_name = data.get('last_name')
            phone_number = data.get('phone_number')
            alternative_phone = data.get('alternative_phone')
            email = data.get('email')
            gender = data.get('gender')
            date_of_birth = data.get('date_of_birth')
            customer_type = data.get('customer_type', 'individual')
            address = data.get('address')
            city = data.get('city')
            id_type = data.get('id_type')
            id_number = data.get('id_number')
            company_name = data.get('company_name')
            commercial_registration = data.get('commercial_registration')
            nationality = data.get('nationality', 'Egyptian')

            # Import validation functions
            from setup.utils import (
                validate_arabic_name, 
                validate_egyptian_national_id, 
                validate_egyptian_phone,
                validate_commercial_registration,
                is_egyptian_context
            )

            # Basic validation (more comprehensive validation can be added)
            if not first_name or not last_name or not phone_number:
                 return JsonResponse({'success': False, 'error': _('Missing required fields: First Name, Last Name, Phone Number')}, status=400)

            # Egyptian context validations
            if is_egyptian_context():
                try:
                    # Validate Arabic names
                    validate_arabic_name(first_name)
                    validate_arabic_name(last_name)
                    if second_name:
                        validate_arabic_name(second_name)
                    if third_name:
                        validate_arabic_name(third_name)
                    if company_name:
                        validate_arabic_name(company_name)
                    
                    # Validate phone numbers
                    validate_egyptian_phone(phone_number)
                    if alternative_phone:
                        validate_egyptian_phone(alternative_phone)
                    
                    # Validate Egyptian ID
                    if id_type == 'national_id' and id_number:
                        validate_egyptian_national_id(id_number)
                    
                    # Validate commercial registration for corporate customers
                    if customer_type == 'corporate':
                        if not commercial_registration:
                            return JsonResponse({
                                'success': False, 
                                'error': _('السجل التجاري مطلوب للشركات في مصر')
                            }, status=400)
                        validate_commercial_registration(commercial_registration)
                    
                except ValidationError as e:
                    return JsonResponse({
                        'success': False, 
                        'error': str(e.message)
                    }, status=400)

            # Check for duplicate ID number
            if id_number:
                existing_customer = Customer.objects.filter(id_number=id_number).first()
                if existing_customer:
                    return JsonResponse({
                        'success': False, 
                        'error': _('رقم الهوية مستخدم مسبقاً')
                    }, status=400)

            # Check for duplicate email
            if email:
                existing_customer = Customer.objects.filter(email=email).first()
                if existing_customer:
                    return JsonResponse({
                        'success': False, 
                        'error': _('البريد الإلكتروني مستخدم مسبقاً')
                    }, status=400)

            # Create the new customer
            customer = Customer.objects.create(
                tenant_id=tenant_id,
                first_name=first_name,
                second_name=second_name,
                third_name=third_name,
                last_name=last_name,
                phone=phone_number,
                alternative_phone=alternative_phone,
                email=email,
                gender=gender,
                date_of_birth=date_of_birth if date_of_birth else None, # Allow null date
                customer_type=customer_type,
                address=address,
                city=city,
                id_type=id_type,
                id_number=id_number,
                company_name=company_name,
                commercial_registration=commercial_registration,
                nationality=nationality
            )
            
            # Return success response with the new customer's ID
            return JsonResponse({'success': True, 'customer': {'id': str(customer.id)}})

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': _('Invalid JSON')}, status=400)
        except Exception as e:
            # Log the error for debugging
            print(f"Error creating customer: {e}")
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

    return JsonResponse({'success': False, 'error': _('Invalid request method.')}, status=405) # Method Not Allowed

@login_required
def api_spare_parts_for_operation(request):
    """
    API endpoint to fetch spare parts compatible with a specific operation type.
    Params:
        operation_id: ID of the operation type (can be UUID or name)
    Returns:
        JSON with spare parts data
    """
    try:
        # Get operation ID
        operation_id = request.GET.get('operation_id')
        if not operation_id:
            return JsonResponse({
                'success': False,
                'error': 'Operation ID is required'
            })
            
        # Clean the operation_id to avoid format issues
        operation_id = str(operation_id).strip()
        
        # Set tenant ID if multi-tenancy is used
        tenant_id = get_current_tenant_id()
        
        # Import models
        from inventory.models import Item, OperationCompatibility
        from work_orders.models import WorkOrderType
        
        # Try to get operation type - avoid UUID validation errors
        operation_type = None
        
        try:
            # First try by name since most operations come as names
            operation_type_obj = WorkOrderType.objects.filter(name=operation_id).first()
            if operation_type_obj:
                operation_type = {
                    'id': str(operation_type_obj.id),
                    'name': operation_type_obj.name,
                    'description': operation_type_obj.description,
                    'price': float(operation_type_obj.price) if hasattr(operation_type_obj, 'price') and operation_type_obj.price else 0
                }
            else:
                # Try by ID only if it looks like a UUID
                import re
                uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', re.IGNORECASE)
                if uuid_pattern.match(operation_id):
                    try:
                        operation_type_obj = WorkOrderType.objects.get(pk=operation_id)
                        operation_type = {
                            'id': str(operation_type_obj.id),
                            'name': operation_type_obj.name,
                            'description': operation_type_obj.description,
                            'price': float(operation_type_obj.price) if hasattr(operation_type_obj, 'price') and operation_type_obj.price else 0
                        }
                    except WorkOrderType.DoesNotExist:
                        pass
                
                # If still not found, create a temporary operation type
                if not operation_type:
                    operation_type = {
                        'id': operation_id,  # Use the name as ID
                        'name': operation_id,
                        'description': '',
                        'price': 0
                    }
        except Exception as e:
            print(f"Error getting operation type: {str(e)}")
            # Create a temporary operation type
            operation_type = {
                'id': operation_id,
                'name': operation_id,
                'description': '',
                'price': 0
            }
            
        if not operation_type:
            return JsonResponse({
                'success': False,
                'error': 'Operation type not found'
            })
        
        # Get compatible parts based on operation name or ID
        try:
            # Try to get parts by operation type ID first
            compatible_parts = []
            
            try:
                # Try to find parts by operation name first to avoid UUID issues
                parts_query = Item.objects.filter(
                    operation_compatibilities__operation_type__name=operation_id
                )
                
                # If no parts found and we have a valid UUID, try by ID
                if not parts_query.exists() and isinstance(operation_type, dict) and 'id' in operation_type:
                    import re
                    uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', re.IGNORECASE)
                    if uuid_pattern.match(operation_type['id']):
                        try:
                            parts_query = Item.objects.filter(
                                operation_compatibilities__operation_type__id=operation_type['id']
                            )
                        except Exception as uuid_error:
                            print(f"UUID query failed: {str(uuid_error)}")
                            # Fallback to empty queryset
                            parts_query = Item.objects.none()
                
                # Add tenant filtering if necessary
                if tenant_id:
                    parts_query = parts_query.filter(tenant_id=tenant_id)
                    
                # Ensure we get distinct results
                parts_query = parts_query.distinct()
                
                # Get the parts
                compatible_parts = list(parts_query)
                
            except Exception as e:
                print(f"Error querying parts by operation type: {str(e)}")
                # Continue with an empty list
            
            print(f"Found {len(compatible_parts)} compatible parts for operation {operation_id}")
            
            # If no parts found, look for parts with similar names or descriptions
            if not compatible_parts:
                try:
                    # Search for parts with names or descriptions that might match this operation
                    search_terms = operation_id.split()
                    if search_terms:
                        search_query = None
                        for term in search_terms:
                            if len(term) > 3:  # Only use terms with at least 4 characters
                                if search_query is None:
                                    search_query = Q(name__icontains=term) | Q(description__icontains=term)
                                else:
                                    search_query |= Q(name__icontains=term) | Q(description__icontains=term)
                        
                        if search_query:
                            items_query = Item.objects.filter(search_query)
                            if tenant_id:
                                items_query = items_query.filter(tenant_id=tenant_id)
                            compatible_parts = list(items_query.distinct())
                except Exception as e:
                    print(f"Error in fallback search: {str(e)}")
                    # Continue with whatever parts we found
            
            # Format the parts data
            spare_parts = []
            for part in compatible_parts:
                part_data = {
                    'id': str(part.id),
                    'name': part.name,
                    'sku': part.sku,
                    'recommended_quantity': 1,  # Default quantity
                    'unit_of_measurement': part.unit_of_measurement.symbol if part.unit_of_measurement else '',
                    'price': float(part.unit_price) if part.unit_price else 0,
                    'current_stock': float(part.current_stock) if hasattr(part, 'current_stock') and part.current_stock else 0,
                    'operation_id': operation_id,
                    'currency': 'جنيه'  # Set currency to Egyptian Pound
                }
                
                # Get recommended quantity if available
                try:
                    # Try by name first to avoid UUID validation errors
                    op_compat = part.operation_compatibilities.filter(operation_type__name=operation_id).first()
                    
                    # If not found and we have a UUID, try by ID
                    if not op_compat and isinstance(operation_type, dict) and 'id' in operation_type:
                        import re
                        uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', re.IGNORECASE)
                        if uuid_pattern.match(operation_type['id']):
                            try:
                                op_compat = part.operation_compatibilities.filter(operation_type_id=operation_type['id']).first()
                            except Exception as uuid_error:
                                print(f"UUID compatibility lookup failed: {str(uuid_error)}")
                        
                    if op_compat and hasattr(op_compat, 'recommended_quantity') and op_compat.recommended_quantity:
                        part_data['recommended_quantity'] = float(op_compat.recommended_quantity)
                except Exception as inner_e:
                    print(f"Error getting recommended quantity: {str(inner_e)}")
                
                # Always use Egyptian Pound as currency
                part_data['currency'] = 'جنيه'
                
                spare_parts.append(part_data)
                
            # If no parts were found using the ORM, try a direct SQL approach as fallback
            if not spare_parts:
                from django.db import connection
                
                # Construct a query to get compatible parts
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT i.id, i.name, i.sku, i.unit_price, i.quantity
                        FROM inventory_item i
                        JOIN inventory_operationcompatibility oc ON i.id = oc.item_id
                        WHERE oc.operation_type_id = %s
                        ORDER BY i.name
                    """, [operation_id])
                    
                    for row in cursor.fetchall():
                        part_data = {
                            'id': str(row[0]),
                            'name': row[1],
                            'sku': row[2],
                            'recommended_quantity': 1,
                            'unit_of_measurement': '',
                            'price': float(row[3]) if row[3] else 0,
                            'current_stock': float(row[4]) if row[4] else 0,
                            'operation_id': operation_id
                        }
                        spare_parts.append(part_data)
                
        except Exception as e:
            import traceback
            print(f"Error getting compatible parts: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            spare_parts = []
        
        return JsonResponse({
            'success': True,
            'operation': operation_type,
            'spare_parts': spare_parts
        })
    except Exception as e:
        import traceback
        print(f"ERROR in api_spare_parts_for_operation: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def test_create_work_order(request):
    """Simple test view to create a work order"""
    if request.method == 'POST':
        print("==================== TEST WORK ORDER CREATION ====================")
        
        # Get essential data from the form
        customer_id = request.POST.get('customer_id')
        vehicle_id = request.POST.get('vehicle_id')
        description = request.POST.get('description')
        
        # Get tenant_id from request if available
        tenant_id = get_current_tenant_id()
        
        # If tenant_id is None, try to get it from the first WorkOrder
        if tenant_id is None:
            # Try to find an existing work order to get tenant_id
            existing_work_order = WorkOrder.objects.first()
            if existing_work_order:
                tenant_id = existing_work_order.tenant_id
                print(f"Using tenant_id from existing work order: {tenant_id}")
            else:
                # Try to get tenant_id from Customer
                customer_obj = Customer.objects.first()
                if customer_obj and hasattr(customer_obj, 'tenant_id'):
                    tenant_id = customer_obj.tenant_id
                    print(f"Using tenant_id from Customer: {tenant_id}")
                else:
                    # Last resort - use a hardcoded UUID as tenant_id
                    import uuid
                    # Use the tenant_id we found from a Customer in the database
                    tenant_id = uuid.UUID('00000000-0000-0000-0000-000000000001')
                    print(f"Using hardcoded tenant_id: {tenant_id}")
        
        print(f"Final tenant_id: {tenant_id}")
        
        # Create response data
        response_data = {
            'success': False,
            'message': '',
            'work_order_id': None
        }
        
        # Validate required data
        if not customer_id or not vehicle_id or not description:
            response_data['message'] = 'Missing required data'
            return JsonResponse(response_data)
        
        try:
            # Get the required objects
            customer = Customer.objects.get(id=customer_id)
            vehicle = Vehicle.objects.get(id=vehicle_id)
            
            # Generate work order number
            prefix = "WO"
            timestamp = timezone.now().strftime('%Y%m%d%H%M')
            work_order_number = f"{prefix}-{timestamp}"
            
            # WorkOrderType is now optional
            work_order_type = None
            try:
                work_order_type = WorkOrderType.objects.first()
                print(f"Using work order type: {work_order_type}")
            except Exception as e:
                print(f"No work order type found or error: {str(e)}")
                # Continue without a work order type
            
            # Create the work order
            work_order = WorkOrder.objects.create(
                work_order_number=work_order_number,
                customer=customer,
                vehicle=vehicle,
                description=description,
                status='planned',
                priority='medium',
                work_order_type=work_order_type,  # This can now be None
                tenant_id=tenant_id  # Set tenant_id from request
            )
            
            print(f"Successfully created work order: {work_order}")
            
            # Return success
            response_data['success'] = True
            response_data['message'] = 'Work order created successfully'
            response_data['work_order_id'] = str(work_order.id)
            
            return JsonResponse(response_data)
            
        except Exception as e:
            import traceback
            print(f"Error creating work order: {str(e)}")
            print(traceback.format_exc())
            response_data['message'] = f'Error: {str(e)}'
            return JsonResponse(response_data)
    
    # GET request - show a simple form
    customers = Customer.objects.all()[:10]
    vehicles = Vehicle.objects.all()[:10]
    
    context = {
        'customers': customers,
        'vehicles': vehicles,
    }
    
    return render(request, 'work_orders/test_create.html', context)

@login_required
@require_POST
def change_work_order_status(request, pk):
    """API endpoint to change the status of a work order with custom notes"""
    try:
        work_order = get_object_or_404(WorkOrder, pk=pk)
        
        # Initialize form with POST data
        form = WorkOrderStatusChangeForm(request.POST, work_order=work_order)
        
        if form.is_valid():
            # Get the new status and notes from the form
            status = form.cleaned_data['status']
            notes = form.cleaned_data['notes']
            
            # Validate the status
            valid_statuses = [choice[0] for choice in WorkOrder.STATUS_CHOICES]
            if status not in valid_statuses:
                return JsonResponse({
                    'success': False,
                    'error': _('Invalid status value')
                })
                
            # Get the old status for history tracking
            old_status = work_order.status
                
            # Update the work order
            work_order.status = status
            
            # Update appropriate timestamps based on the new status
            if status == 'in_progress' and not work_order.actual_start_date:
                work_order.actual_start_date = timezone.now()
            elif status == 'completed' and not work_order.actual_end_date:
                work_order.actual_end_date = timezone.now()
                
            # Append notes to the work order if provided
            if notes:
                # Add the notes to the work order's notes field
                existing_notes = work_order.notes or ''
                work_order.notes = f"{existing_notes}\n\n{timezone.now().strftime('%Y-%m-%d %H:%M')} - {_('Status changed to')} {dict(WorkOrder.STATUS_CHOICES).get(status)}:\n{notes}"
            
            # Set the original status for history tracking
            work_order._original_status = old_status
                
            # Save the work order to trigger the post_save signal
            work_order.save()
            
            # Manually create a history entry for the status change with notes
            if notes:
                # Create or update the history entry with the notes
                history_entry = WorkOrderHistory.objects.filter(
                    work_order=work_order,
                    action_type='status_change',
                    created_at__gte=timezone.now() - timezone.timedelta(seconds=10)
                ).first()
                
                if history_entry:
                    # Update the existing entry with notes
                    history_entry.notes = notes
                    history_entry.save()
                else:
                    # Create a new history entry with notes
                    WorkOrderHistory.objects.create(
                        work_order=work_order,
                        action_type='status_change',
                        description=f'Status changed from {old_status} to {status}',
                        previous_value=old_status,
                        new_value=status,
                        notes=notes,
                        user=request.user,
                        attributes={
                            'status': status,
                            'has_notes': True
                        }
                    )
            
            # Handle specific status actions
            if status == 'completed':
                # Mark all operations as completed
                for operation in work_order.operations.filter(is_completed=False):
                    operation.is_completed = True
                    operation.completed_at = timezone.now()
                    operation._changed_completion = True
                    operation.save()
                
                # Mark all materials as consumed
                for material in work_order.materials.filter(is_consumed=False):
                    material.is_consumed = True
                    material._changed_consumption = True
                    material.save()
            
            # Return success response for AJAX or redirect for form submit
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'message': _('Work order status updated successfully'),
                    'new_status': status,
                    'status_display': dict(WorkOrder.STATUS_CHOICES).get(status)
                })
            else:
                messages.success(request, _('Work order status updated successfully'))
                return redirect('work_orders:work_order_detail', pk=work_order.pk)
        else:
            # Form validation error
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'errors': form.errors
                })
            else:
                messages.error(request, _('Please correct the errors below.'))
                return redirect('work_orders:work_order_detail', pk=work_order.pk)
            
    except Exception as e:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
        else:
            messages.error(request, str(e))
            return redirect('work_orders:work_order_detail', pk=pk)

@login_required
@require_GET
def api_get_operation(request, operation_id):
    """API endpoint to get operation data"""
    try:
        operation = get_object_or_404(WorkOrderOperation, pk=operation_id)
        
        # Return the operation data with the correct field names for JavaScript
        operation_data = {
            'id': str(operation.id),
            'name': operation.name,
            'description': operation.description or '',
            'duration': operation.duration_minutes,
            'notes': getattr(operation, 'notes', '') or '',
            'sequence': operation.sequence,
            'is_completed': operation.is_completed
        }
        
        return JsonResponse(operation_data)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def api_create_operation(request):
    """API endpoint to create a new operation"""
    try:
        # Get data from the request
        work_order_id = request.POST.get('work_order')
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        duration_minutes = request.POST.get('duration_minutes', 60)
        sequence = request.POST.get('sequence', 10)
        
        # Validate required fields
        if not work_order_id or not name:
            return JsonResponse({
                'success': False,
                'error': _('Work order ID and operation name are required')
            })
            
        # Get the work order
        work_order = get_object_or_404(WorkOrder, pk=work_order_id)
        
        # Create the operation
        operation = WorkOrderOperation.objects.create(
            work_order=work_order,
            name=name,
            description=description,
            duration_minutes=int(duration_minutes),
            sequence=int(sequence),
            tenant_id=get_current_tenant_id() or work_order.tenant_id
        )
        
        return JsonResponse({
            'success': True,
            'operation': {
                'id': str(operation.id),
                'name': operation.name
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def api_update_operation(request, operation_id):
    """API endpoint to update an operation"""
    try:
        # Check if the request method is allowed
        if request.method not in ['POST', 'PUT']:
            return JsonResponse({
                'success': False,
                'error': _('Method not allowed')
            }, status=405)
        
        # Get the operation
        operation = get_object_or_404(WorkOrderOperation, pk=operation_id)
        
        # Get data from the request
        if request.method == 'PUT':
            # Handle JSON data for PUT requests
            try:
                data = json.loads(request.body)
                name = data.get('name')
                description = data.get('description', '')
                duration = data.get('duration', 0)
                notes = data.get('notes', '')
            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'error': _('Invalid JSON data')
                }, status=400)
        else:
            # Handle form data for POST requests
            name = request.POST.get('operation_name')
            description = request.POST.get('operation_description', '')
            duration = request.POST.get('operation_duration', 0)
            notes = request.POST.get('notes', '')
        
        # Validate required fields
        if not name:
            return JsonResponse({
                'success': False,
                'error': _('Operation name is required')
            })
            
        # Update the operation
        operation.name = name
        operation.description = description
        operation.duration_minutes = int(duration) if duration else 0
        operation.notes = notes
        operation.save()
        
        return JsonResponse({
            'success': True,
            'message': _('Operation updated successfully')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def api_delete_operation(request, operation_id):
    """API endpoint to delete an operation"""
    try:
        # Check if the request method is allowed
        if request.method not in ['POST', 'DELETE']:
            return JsonResponse({
                'success': False,
                'error': _('Method not allowed')
            }, status=405)
        
        # Get the operation
        operation = get_object_or_404(WorkOrderOperation, pk=operation_id)
        
        # Delete the operation
        operation.delete()
        
        return JsonResponse({
            'success': True,
            'message': _('Operation deleted successfully')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def api_complete_operation(request, operation_id):
    """API endpoint to mark an operation as complete"""
    try:
        # Get the operation
        operation = get_object_or_404(WorkOrderOperation, pk=operation_id)
        
        # Update the operation
        operation.is_completed = True
        operation.completed_at = timezone.now()
        operation.save()
        
        # Check if work order can be auto-completed
        from work_orders.utils import auto_complete_work_order
        work_order = operation.work_order
        
        auto_completion_result = auto_complete_work_order(work_order, user=request.user)
        
        response_data = {
            'success': True,
            'message': _('Operation marked as complete')
        }
        
        # Add auto-completion info to response
        if auto_completion_result['completed']:
            response_data['work_order_completed'] = True
            response_data['invoice_created'] = auto_completion_result['invoice_created']
            response_data['sales_order_created'] = auto_completion_result.get('sales_order_created', False)
            response_data['completion_message'] = auto_completion_result['message']
            if auto_completion_result['invoice']:
                response_data['invoice_number'] = auto_completion_result['invoice'].invoice_number
            if auto_completion_result.get('sales_order'):
                response_data['sales_order_number'] = auto_completion_result['sales_order'].order_number
        else:
            response_data['work_order_completed'] = False
            response_data['completion_details'] = auto_completion_result.get('details', {})
        
        return JsonResponse(response_data)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_GET
def api_get_material(request, material_id):
    """API endpoint to get material data"""
    try:
        material = get_object_or_404(WorkOrderMaterial, pk=material_id)
        
        # Return the material data
        material_data = {
            'id': str(material.id),
            'item_id': str(material.item.id),
            'item_name': material.item.name,
            'quantity': float(material.quantity),
            'unit_of_measure': material.unit_of_measure,
            'notes': material.notes,
            'is_consumed': material.is_consumed
        }
        
        return JsonResponse({
            'success': True,
            'material': material_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def api_update_material(request, material_id):
    """API endpoint to update a material"""
    try:
        # Check if the request method is allowed
        if request.method not in ['POST', 'PUT']:
            return JsonResponse({
                'success': False,
                'error': _('Method not allowed')
            }, status=405)
        
        # Get the material
        material = get_object_or_404(WorkOrderMaterial, pk=material_id)
        
        # Get data from the request
        if request.method == 'PUT':
            # Handle JSON data for PUT requests
            try:
                data = json.loads(request.body)
                quantity = data.get('quantity')
                unit_of_measure = data.get('unit_of_measure')
                notes = data.get('notes', '')
            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'error': _('Invalid JSON data')
                }, status=400)
        else:
            # Handle form data for POST requests
            quantity = request.POST.get('quantity')
            unit_of_measure = request.POST.get('unit_of_measure')
            notes = request.POST.get('notes', '')
        
        # Validate required fields
        if not quantity or not unit_of_measure:
            return JsonResponse({
                'success': False,
                'error': _('Quantity and unit of measure are required')
            })
            
        # Update the material
        material.quantity = float(quantity)
        material.unit_of_measure = unit_of_measure
        material.notes = notes
        material.save()
        
        return JsonResponse({
            'success': True,
            'message': _('Material updated successfully')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def api_delete_material(request, material_id):
    """API endpoint to delete a material"""
    try:
        # Check if the request method is allowed
        if request.method not in ['POST', 'DELETE']:
            return JsonResponse({
                'success': False,
                'error': _('Method not allowed')
            }, status=405)
        
        # Get the material
        material = get_object_or_404(WorkOrderMaterial, pk=material_id)
        
        # Delete the material
        material.delete()
        
        return JsonResponse({
            'success': True,
            'message': _('Material deleted successfully')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def api_consume_material(request, material_id):
    """API endpoint to mark a material as consumed"""
    try:
        # Get the material
        material = get_object_or_404(WorkOrderMaterial, pk=material_id)
        
        # Update the material
        material.is_consumed = True
        material.save()
        
        # Check if work order can be auto-completed
        from work_orders.utils import auto_complete_work_order
        work_order = material.work_order
        
        auto_completion_result = auto_complete_work_order(work_order, user=request.user)
        
        response_data = {
            'success': True,
            'message': _('Material marked as consumed')
        }
        
        # Add auto-completion info to response
        if auto_completion_result['completed']:
            response_data['work_order_completed'] = True
            response_data['invoice_created'] = auto_completion_result['invoice_created']
            response_data['sales_order_created'] = auto_completion_result.get('sales_order_created', False)
            response_data['completion_message'] = auto_completion_result['message']
            if auto_completion_result['invoice']:
                response_data['invoice_number'] = auto_completion_result['invoice'].invoice_number
            if auto_completion_result.get('sales_order'):
                response_data['sales_order_number'] = auto_completion_result['sales_order'].order_number
        else:
            response_data['work_order_completed'] = False
            response_data['completion_details'] = auto_completion_result.get('details', {})
        
        return JsonResponse(response_data)
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def api_create_external_part(request):
    """API endpoint to create a new external part"""
    try:
        # Get data from the request
        work_order_id = request.POST.get('work_order_id')
        name = request.POST.get('name')
        supplier = request.POST.get('supplier')
        quantity = request.POST.get('quantity')
        unit = request.POST.get('unit')
        price = request.POST.get('price')
        notes = request.POST.get('notes', '')
        
        # Validate required fields
        if not work_order_id or not name or not supplier or not quantity or not unit or not price:
            return JsonResponse({
                'success': False,
                'error': _('All fields are required')
            })
            
        # Get the work order
        work_order = get_object_or_404(WorkOrder, pk=work_order_id)
        
        # Create a temporary item for the external part
        from inventory.models import Item
        
        # Check if an item with this name already exists
        item = Item.objects.filter(name=name).first()
        if not item:
            # Create a new item
            item = Item.objects.create(
                name=name,
                description=f"{_('External part from')} {supplier}",
                unit_price=float(price),
                tenant_id=get_current_tenant_id() or work_order.tenant_id,
                attributes={
                    'external': True,
                    'supplier': supplier
                }
            )
        
        # Create the material
        material = WorkOrderMaterial.objects.create(
            work_order=work_order,
            item=item,
            quantity=float(quantity),
            unit_of_measure=unit,
            notes=notes,
            tenant_id=get_current_tenant_id() or work_order.tenant_id,
            attributes={
                'external': True,
                'supplier': supplier,
                'price': float(price)
            }
        )
        
        return JsonResponse({
            'success': True,
            'material': {
                'id': str(material.id),
                'name': material.item.name
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def api_create_external_operation(request):
    """API endpoint to create a new external operation"""
    try:
        # Get data from the request
        work_order_id = request.POST.get('work_order_id')
        name = request.POST.get('name')
        provider = request.POST.get('provider')
        price = request.POST.get('price')
        duration = request.POST.get('duration', 0)
        notes = request.POST.get('notes', '')
        
        # Validate required fields
        if not work_order_id or not name or not provider or not price:
            return JsonResponse({
                'success': False,
                'error': _('All fields are required')
            })
            
        # Get the work order
        work_order = get_object_or_404(WorkOrder, pk=work_order_id)
        
        # Create the operation
        operation = WorkOrderOperation.objects.create(
            work_order=work_order,
            name=name,
            description=f"{_('External operation by')} {provider}: {name}",
            duration_minutes=int(duration),
            tenant_id=get_current_tenant_id() or work_order.tenant_id,
            attributes={
                'external': True,
                'provider': provider,
                'price': float(price)
            },
            notes=notes
        )
        
        return JsonResponse({
            'success': True,
            'operation': {
                'id': str(operation.id),
                'name': operation.name
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_GET
def api_get_available_parts(request):
    """API endpoint to get available parts for the parts request modal"""
    try:
        # Get all items that can be requested
        from inventory.models import Item
        
        # Filter by tenant if multi-tenancy is enabled
        tenant_id = get_current_tenant_id()
        if tenant_id:
            items = Item.objects.filter(tenant_id=tenant_id)
        else:
            items = Item.objects.all()
            
        # Filter out non-physical items
        items = items.filter(is_physical=True)
        
        # Return the items
        parts = []
        for item in items:
            parts.append({
                'id': str(item.id),
                'name': item.name,
                'sku': item.sku,
                'price': float(item.unit_price) if item.unit_price else 0,
                'unit': item.unit_of_measurement.symbol if hasattr(item, 'unit_of_measurement') and item.unit_of_measurement else ''
            })
        
        return JsonResponse({
            'success': True,
            'parts': parts
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_GET
def api_get_warehouses(request):
    """API endpoint to get warehouses for the parts request modal"""
    try:
        # Get all warehouses
        from inventory.models import Location
        
        # Filter by tenant if multi-tenancy is enabled
        tenant_id = get_current_tenant_id()
        if tenant_id:
            warehouses = Location.objects.filter(tenant_id=tenant_id, is_warehouse=True)
        else:
            warehouses = Location.objects.filter(is_warehouse=True)
            
        # Return the warehouses
        warehouse_list = []
        for warehouse in warehouses:
            warehouse_list.append({
                'id': str(warehouse.id),
                'name': warehouse.name
            })
        
        return JsonResponse({
            'success': True,
            'warehouses': warehouse_list
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def api_request_parts(request):
    """API endpoint to request parts from a warehouse"""
    try:
        # Get data from the request
        work_order_id = request.POST.get('work_order')
        warehouse_id = request.POST.get('warehouse')
        request_type = request.POST.get('request_type', 'transfer')
        priority = request.POST.get('priority', 'medium')
        details = request.POST.get('details', '')
        
        # Validate required fields
        if not work_order_id or not warehouse_id or not details:
            return JsonResponse({
                'success': False,
                'error': _('Work order, warehouse, and details are required')
            })
            
        # Get the work order
        work_order = get_object_or_404(WorkOrder, pk=work_order_id)
        
        # Get the warehouse if provided
        warehouse_name = "Unknown Warehouse"
        if warehouse_id:
            try:
                from warehouse.models import Warehouse
                warehouse = get_object_or_404(Warehouse, pk=warehouse_id)
                warehouse_name = warehouse.name
            except ImportError:
                # Fallback to inventory Location model
                try:
                    from inventory.models import Location
                    warehouse = get_object_or_404(Location, pk=warehouse_id)
                    warehouse_name = warehouse.name
                except:
                    pass
        
        # Create a work order history entry to track the request
        WorkOrderHistory.objects.create(
            work_order=work_order,
            action_type='other',
            description=f"{_('Parts request submitted')} - {request_type.title()}",
            notes=f"{_('Warehouse')}: {warehouse_name}\n{_('Priority')}: {priority}\n{_('Details')}: {details}",
            user=request.user,
            tenant_id=get_current_tenant_id() or work_order.tenant_id
        )
        
        # Add a note to the work order
        work_order.notes = (work_order.notes or '') + f"\n\n{timezone.now().strftime('%Y-%m-%d %H:%M')} - {_('Parts request submitted')}:\n{_('Type')}: {request_type.title()}\n{_('Warehouse')}: {warehouse_name}\n{_('Priority')}: {priority}\n{_('Details')}: {details}"
        work_order.save()
        
        return JsonResponse({
            'success': True,
            'message': _('Parts request submitted successfully')
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_GET
def api_get_work_order_history(request, pk):
    """API endpoint to get work order history entries"""
    try:
        work_order = get_object_or_404(WorkOrder, pk=pk)
        
        # Get history entries
        history_entries = work_order.history_entries.all().order_by('-created_at')
        
        # Optionally limit the number of entries
        limit = request.GET.get('limit')
        if limit and limit.isdigit():
            history_entries = history_entries[:int(limit)]
        
        # Format the result
        entries = []
        for entry in history_entries:
            # Format the timestamp
            timestamp = entry.created_at.strftime('%Y-%m-%d %H:%M')
            
            # Get the user name if available
            user_name = entry.user.get_full_name() if entry.user else _('System')
            if not user_name.strip() and entry.user:
                user_name = entry.user.username
            
            entries.append({
                'id': str(entry.id),
                'action_type': entry.action_type,
                'action_display': dict(WorkOrderHistory.ACTION_TYPES).get(entry.action_type, entry.action_type),
                'description': entry.description,
                'previous_value': entry.previous_value,
                'new_value': entry.new_value,
                'notes': entry.notes,
                'timestamp': timestamp,
                'user': user_name,
                'attributes': entry.attributes
            })
        
        return JsonResponse({
            'success': True,
            'history': entries
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_GET
def api_get_vehicle_history(request, vehicle_id):
    """API endpoint to get vehicle service history (all work orders for a vehicle)"""
    try:
        vehicle = get_object_or_404(Vehicle, pk=vehicle_id)
        tenant_id = get_current_tenant_id()
        
        # Check if vehicle belongs to current tenant
        if hasattr(vehicle, 'tenant_id') and vehicle.tenant_id != tenant_id:
            return JsonResponse({
                'success': False,
                'error': 'غير مصرح لك بعرض سجل هذه المركبة'
            })
        
        # Get work orders for this vehicle, excluding draft/cancelled orders
        work_orders = WorkOrder.objects.filter(
            vehicle=vehicle,
            tenant_id=tenant_id
        ).exclude(
            status__in=['draft', 'cancelled']
        ).order_by('-created_at')
        
        # Limit to last 10 work orders
        limit = int(request.GET.get('limit', 10))
        work_orders = work_orders[:limit]
        
        # Format the result
        history = []
        for work_order in work_orders:
            # Get operations for this work order
            operations = work_order.operations.all().values_list('name', flat=True)
            operations_list = list(operations) if operations else [work_order.description[:50] + '...' if len(work_order.description) > 50 else work_order.description]
            
            # Format date
            date_formatted = work_order.created_at.strftime('%Y-%m-%d')
            
            # Get status display with translation
            status_display = str(dict(WorkOrder.STATUS_CHOICES).get(work_order.status, work_order.status))
            
            history.append({
                'id': str(work_order.id),
                'work_order_number': work_order.work_order_number,
                'date': date_formatted,
                'status': work_order.status,
                'status_display': status_display,
                'operations': operations_list,
                'odometer': work_order.current_odometer,
                'description': work_order.description[:100] + '...' if len(work_order.description) > 100 else work_order.description,
                'service_center': work_order.service_center.name if work_order.service_center else '',
            })
        
        # Calculate maintenance summary
        maintenance_summary = {
            'last_service_date': None,
            'total_visits': len(history),
            'next_service_date': None,
            'last_service_odometer': 0
        }
        
        # Get last completed service
        completed_orders = [wo for wo in work_orders if wo.status == 'completed']
        if completed_orders:
            last_service = completed_orders[0]  # Already ordered by date desc
            maintenance_summary['last_service_date'] = last_service.actual_end_date or last_service.created_at
            maintenance_summary['last_service_odometer'] = last_service.current_odometer or 0
        
        # Calculate next service date (estimate based on average interval)
        if len(completed_orders) >= 2:
            # Calculate average interval between services
            intervals = []
            for i in range(len(completed_orders) - 1):
                current_date = completed_orders[i].actual_end_date or completed_orders[i].created_at
                next_date = completed_orders[i+1].actual_end_date or completed_orders[i+1].created_at
                interval_days = (current_date - next_date).days
                if interval_days > 0:
                    intervals.append(interval_days)
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                next_service = maintenance_summary['last_service_date'] + timezone.timedelta(days=avg_interval)
                maintenance_summary['next_service_date'] = next_service

        return JsonResponse({
            'success': True,
            'history': history,
            'vehicle_info': {
                'make': vehicle.make,
                'model': vehicle.model,
                'year': vehicle.year,
                'license_plate': vehicle.license_plate,
                'last_service_odometer': vehicle.last_service_odometer or 0,
            },
            'maintenance_summary': maintenance_summary
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def api_get_technicians(request):
    """API endpoint to get available technicians"""
    try:
        tenant_id = get_current_tenant_id()
        
        # Get technician roles
        technician_roles = Role.objects.filter(
            code__in=['technician', 'small_center_technician']
        )
        
        # Get users with technician roles
        technician_user_roles = UserRole.objects.filter(
            role__in=technician_roles,
            is_active=True,
            tenant_id=tenant_id
        ).select_related('user')
        
        # Filter by user's service center if not admin
        if not request.user.is_superuser and hasattr(request.user, 'user_roles'):
            user_roles = request.user.user_roles.filter(is_active=True)
            primary_role = user_roles.filter(is_primary=True).first()
            
            if primary_role and primary_role.service_center:
                # Filter technicians from the same service center
                technician_user_roles = technician_user_roles.filter(
                    service_center=primary_role.service_center
                )
        
        technicians = []
        for user_role in technician_user_roles:
            user = user_role.user
            technicians.append({
                'id': user.id,
                'name': f"{user.first_name} {user.last_name}".strip() or user.username,
                'username': user.username,
                'service_center': user_role.service_center.name if user_role.service_center else None
            })
        
        # Remove duplicates based on user ID
        seen_ids = set()
        unique_technicians = []
        for tech in technicians:
            if tech['id'] not in seen_ids:
                unique_technicians.append(tech)
                seen_ids.add(tech['id'])
        
        return JsonResponse({
            'success': True,
            'technicians': unique_technicians
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })

@login_required
@require_POST
def change_work_order_status(request, work_order_id):
    """API endpoint to change work order status"""
    try:
        work_order = get_object_or_404(WorkOrder, id=work_order_id)
        
        # Check permissions
        tenant_id = get_current_tenant_id()
        if work_order.tenant_id != tenant_id:
            return JsonResponse({
                'success': False,
                'message': 'غير مصرح لك بتعديل هذا أمر العمل'
            })
        
        new_status = request.POST.get('new_status')
        assigned_technician_id = request.POST.get('assigned_technician')
        notes = request.POST.get('notes', '')
        
        if not new_status:
            return JsonResponse({
                'success': False,
                'message': 'يجب تحديد الحالة الجديدة'
            })
        
        # Validate status transition
        valid_transitions = {
            'draft': ['planned', 'cancelled'],
            'planned': ['in_progress', 'on_hold', 'cancelled'],
            'in_progress': ['completed', 'on_hold', 'cancelled'],
            'on_hold': ['in_progress', 'cancelled'],
            'completed': [],
            'cancelled': []
        }
        
        current_status = work_order.status
        if new_status not in valid_transitions.get(current_status, []):
            return JsonResponse({
                'success': False,
                'message': f'لا يمكن تغيير الحالة من {current_status} إلى {new_status}'
            })
        
        # Store old status for history
        old_status = work_order.status
        
        # Update work order
        work_order.status = new_status
        
        # Assign technician if provided and status is in_progress
        if new_status == 'in_progress' and assigned_technician_id:
            try:
                technician = User.objects.get(id=assigned_technician_id)
                work_order.assigned_technician = technician
            except User.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': 'الفني المحدد غير موجود'
                })
        
        # Update timestamps
        if new_status == 'in_progress' and not work_order.actual_start_date:
            work_order.actual_start_date = timezone.now()
        elif new_status == 'completed' and not work_order.actual_end_date:
            work_order.actual_end_date = timezone.now()
        
        work_order.save()
        
        # Create history entry
        WorkOrderHistory.objects.create(
            tenant_id=tenant_id,
            work_order=work_order,
            action_type='status_change',
            description=f'تم تغيير الحالة من {old_status} إلى {new_status}',
            previous_value=old_status,
            new_value=new_status,
            user=request.user,
            notes=notes
        )
        
        # Send email notification if email service is available
        try:
            from notifications.email_service import send_work_order_status_email
            send_work_order_status_email(
                work_order=work_order,
                from_status=old_status,
                to_status=new_status,
                tenant_id=tenant_id
            )
        except ImportError:
            pass  # Email service not available
        
        return JsonResponse({
            'success': True,
            'message': 'تم تحديث حالة أمر العمل بنجاح',
            'new_status': new_status,
            'assigned_technician': work_order.assigned_technician.get_full_name() if work_order.assigned_technician else None
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })

@login_required
def api_root_handler(request):
    """
    API root handler - returns empty response for template compatibility
    The template concatenates paths to this URL, but Django doesn't work that way.
    The actual endpoints are handled by separate URL patterns.
    """
    from django.http import HttpResponse
    return HttpResponse("API Root", status=200)

@login_required
@require_POST
def assign_technician(request, work_order_id):
    """API endpoint to assign technician to work order"""
    try:
        work_order = get_object_or_404(WorkOrder, id=work_order_id)
        
        # Check permissions
        tenant_id = get_current_tenant_id()
        if work_order.tenant_id != tenant_id:
            return JsonResponse({
                'success': False,
                'message': 'غير مصرح لك بتعديل هذا أمر العمل'
            })
        
        technician_id = request.POST.get('assigned_technician')
        notes = request.POST.get('notes', '')
        
        if not technician_id:
            return JsonResponse({
                'success': False,
                'message': 'يجب تحديد فني'
            })
        
        try:
            technician = User.objects.get(id=technician_id)
            
            # Verify technician has appropriate role
            has_tech_role = UserRole.objects.filter(
                user=technician,
                role__code__in=['technician', 'small_center_technician'],
                is_active=True,
                tenant_id=tenant_id
            ).exists()
            
            if not has_tech_role:
                return JsonResponse({
                    'success': False,
                    'message': 'المستخدم المحدد ليس فني'
                })
            
        except User.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'الفني المحدد غير موجود'
            })
        
        # Store old technician for history
        old_technician = work_order.assigned_technician
        
        # Update work order
        work_order.assigned_technician = technician
        work_order.save()
        
        # Create history entry
        WorkOrderHistory.objects.create(
            tenant_id=tenant_id,
            work_order=work_order,
            action_type='assignment',
            description=f'تم تعيين الفني {technician.get_full_name()}',
            previous_value=old_technician.get_full_name() if old_technician else 'غير محدد',
            new_value=technician.get_full_name(),
            user=request.user,
            notes=notes
        )
        
        return JsonResponse({
            'success': True,
            'message': 'تم تعيين الفني بنجاح',
            'assigned_technician': technician.get_full_name()
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })

@login_required
def api_get_existing_operations(request):
    """API endpoint to get existing operations for dropdown"""
    try:
        from core.middleware import get_current_tenant_id
        tenant_id = get_current_tenant_id()
        
        # If tenant_id is None, try to get it from existing data
        if tenant_id is None:
            # Try to find an existing work order to get tenant_id
            existing_work_order = WorkOrder.objects.first()
            if existing_work_order:
                tenant_id = existing_work_order.tenant_id
            else:
                # Try to get tenant_id from Customer
                from setup.models import Customer
                customer_obj = Customer.objects.first()
                if customer_obj and hasattr(customer_obj, 'tenant_id'):
                    tenant_id = customer_obj.tenant_id
                else:
                    # Last resort - use a hardcoded UUID as tenant_id
                    import uuid
                    tenant_id = uuid.UUID('00000000-0000-0000-0000-000000000001')
        
        # Get all available tenant IDs for debugging
        all_schedule_tenant_ids = list(ScheduleOperation.objects.values_list('tenant_id', flat=True).distinct())
        all_wo_tenant_ids = list(WorkOrderOperation.objects.values_list('tenant_id', flat=True).distinct())
        
        # Get schedule operations - try with tenant_id first, then without if none found
        schedule_operations = []
        schedule_ops = ScheduleOperation.objects.filter(tenant_id=tenant_id).select_related('maintenance_schedule').order_by('name')[:50]
        
        # If no operations found with tenant filtering, try without tenant filtering
        if not schedule_ops.exists():
            schedule_ops = ScheduleOperation.objects.all().select_related('maintenance_schedule').order_by('name')[:50]
        
        for op in schedule_ops:
            schedule_operations.append({
                'id': str(op.id),
                'name': op.name,
                'description': op.description,
                'duration_minutes': op.duration_minutes,
                'sequence': op.sequence,
                'maintenance_schedule_name': op.maintenance_schedule.name if op.maintenance_schedule else '',
                'is_required': op.is_required
            })
        
        # Get unique work order operations (avoid duplicates by name)
        work_order_operations = []
        seen_names = set()
        
        wo_ops = WorkOrderOperation.objects.filter(tenant_id=tenant_id).exclude(
            name__in=[op['name'] for op in schedule_operations]  # Exclude names already in schedule operations
        ).order_by('name')[:50]
        
        # If no operations found with tenant filtering, try without tenant filtering
        if not wo_ops.exists():
            wo_ops = WorkOrderOperation.objects.all().exclude(
                name__in=[op['name'] for op in schedule_operations]
            ).order_by('name')[:50]
        
        for op in wo_ops:
            if op.name not in seen_names:
                work_order_operations.append({
                    'id': str(op.id),
                    'name': op.name,
                    'description': op.description,
                    'duration_minutes': op.duration_minutes,
                    'sequence': op.sequence
                })
                seen_names.add(op.name)
                
                # Limit to 30 unique names
                if len(work_order_operations) >= 30:
                    break
        
        return JsonResponse({
            'success': True,
            'schedule_operations': schedule_operations,
            'work_order_operations': work_order_operations,
            'debug_info': {
                'requested_tenant_id': str(tenant_id) if tenant_id else 'None',
                'schedule_ops_count': len(schedule_operations),
                'work_order_ops_count': len(work_order_operations),
                'all_schedule_tenant_ids': [str(tid) for tid in all_schedule_tenant_ids if tid],
                'all_wo_tenant_ids': [str(tid) for tid in all_wo_tenant_ids if tid],
                'total_schedule_ops_in_db': ScheduleOperation.objects.count(),
                'total_wo_ops_in_db': WorkOrderOperation.objects.count()
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def api_auto_complete_work_orders(request):
    """API endpoint to manually trigger auto-completion of ready work orders"""
    try:
        tenant_id = get_current_tenant_id()
        
        # Get work order ID if specified, otherwise process all ready ones
        work_order_id = request.POST.get('work_order_id')
        
        if work_order_id:
            # Process specific work order
            work_order = get_object_or_404(WorkOrder, pk=work_order_id, tenant_id=tenant_id)
            
            from work_orders.utils import auto_complete_work_order
            result = auto_complete_work_order(work_order, user=request.user)
            
            return JsonResponse({
                'success': True,
                'work_order_number': work_order.work_order_number,
                'completed': result['completed'],
                'invoice_created': result['invoice_created'],
                'sales_order_created': result.get('sales_order_created', False),
                'completion_message': result['message'],
                'invoice_number': result['invoice'].invoice_number if result.get('invoice') else None,
                'sales_order_number': result['sales_order'].order_number if result.get('sales_order') else None
            })
        else:
            # Process all ready work orders for this tenant
            from work_orders.utils import check_and_auto_complete_work_orders
            results = check_and_auto_complete_work_orders(tenant_id=tenant_id)
            
            return JsonResponse({
                'success': True,
                'summary': {
                    'checked': results['checked'],
                    'completed': results['completed'],
                    'invoices_created': results['invoices_created'],
                    'sales_orders_created': results['sales_orders_created'],
                    'errors_count': len(results['errors'])
                },
                'details': results['details'],
                'errors': results['errors'],
                'message': f"Processed {results['checked']} work orders. Completed {results['completed']}, created {results['invoices_created']} invoices and {results['sales_orders_created']} sales orders."
            })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'message': 'Error during auto-completion process'
        })


@login_required
def api_check_work_order_completion_status(request, work_order_id):
    """API endpoint to check if a work order is ready for completion"""
    try:
        tenant_id = get_current_tenant_id()
        work_order = get_object_or_404(WorkOrder, pk=work_order_id, tenant_id=tenant_id)
        
        from work_orders.utils import check_work_order_completion
        completion_check = check_work_order_completion(work_order)
        
        return JsonResponse({
            'success': True,
            'work_order_number': work_order.work_order_number,
            'status': work_order.status,
            'can_complete': completion_check['can_complete'],
            'operations_completed': completion_check['operations_completed'],
            'materials_consumed': completion_check['materials_consumed'],
            'missing_operations': completion_check['missing_operations'],
            'missing_materials': completion_check['missing_materials'],
            'progress': {
                'operations': f"{completion_check['completed_operations_count']}/{completion_check['total_operations']}",
                'materials': f"{completion_check['consumed_materials_count']}/{completion_check['total_materials']}"
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
